<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="1">
        <record id="ir_cron_sync_user_devices" model="ir.cron">
            <field name="name">获取机床列表</field>
            <field name="model_id" ref="model_roke_work_center"/>
            <field name="state">code</field>
            <field name="code">model.get_user_devices()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">months</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <field name="active" eval="False" />
        </record>
        <record id="ir_cron_sync_work_logs" model="ir.cron">
            <field name="name">获取机床加工记录</field>
            <field name="model_id" ref="model_roke_work_center"/>
            <field name="state">code</field>
            <field name="code">
# 默认取当前时间前十分钟的加工记录。
# 如果需要取其它时间的价格记录，请入参开始/结束时间（格式“年-月-日 时:分:秒”）；
# 如果需求取当前时间的前20分钟的加工记录，那么入参时间间隔time_interval（单位分钟）。
# 比如：model.get_work_logs(start_time="2024-01-01 0:00:00", end_time="2024-01-01 8:00:00") 或 model.get_work_logs(time_interval=20)
model.get_work_logs()
            </field>
            <field name="interval_number">10</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <field name="active" eval="False" />
        </record>
    </data>
</odoo>