# import requests
#
# import re
#
#
# def process_url(web_base_url):
#     # 替换 http:// 或 https://
#     web_base_url = re.sub(r'^https?://', '', web_base_url)
#     # 删除斜杠 /
#     web_base_url = web_base_url.replace("/", "")
#     return web_base_url
#
# # Uncaught (in promise) TypeError: self.keepRun is not a function
# # 测试
# web_base_url = "https://example.com/"
# processed_url = process_url(web_base_url)
#
#
# def upload_file(url, file_path, processed_url):
#     try:
#         files = {'file': open(file_path, 'rb')}
#         print({'file': open(file_path, 'rb')})
#         data = {'filePath': processed_url}
#         response = requests.post(url, files=files, data=data)
#         if response.status_code == 200:
#             return response.json()
#         else:
#             return None
#     except requests.exceptions.RequestException as e:
#         return e
#
#

# file_path = r"C:\Users\<USER>\Desktop\改BL320200-70S底盖(2).SLDPRT"
#
# data = upload_file(url, file_path, processed_url)
# if data:
#     print("获取到的数据：", data)
# else:
#     print("获取数据失败。")
# import json
# import requests
#
# url = "http://**************:8088/cad/uploadFileUrl"
# dict_ = {
#     'ossId': "1772107631008669698",
#     'url': "http://*************:9000/roke/localhost:8022/2024/03/25/274c6b3c80e34b059563874f5600ac20.DXF"
# }
#
# json_ = json.dumps(dict_, ensure_ascii=False)
# datas = requests.post(url, data=json_)
# content = json.loads(datas.content)
# print(content)
# dataaaa = content["data"]
# print(dataaaa)
