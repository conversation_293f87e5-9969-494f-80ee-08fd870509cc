# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import hashlib
import json
import time


class OpenApiHeader:
    def __init__(self, app_id, timestamp, app_sign):
        self.appID = app_id
        self.timestamp = timestamp
        self.appSign = app_sign

    def to_json(self):

        data = {
            "app-id": self.appID,
            "time-stamp": str(self.timestamp),
            "app-sign": self.appSign
        }
        return json.dumps(data)

    def to_dict(self):
        return {
            "app-id": self.appID,
            "time-stamp": self.timestamp,
            "app-sign": self.appSign
        }


class OpenApiHeaderHelper:
    def __init__(self, app_id, app_secret):
        self.app_id = app_id
        self.app_secret = app_secret

    def gen_json(self):
        openApiHeader = self._do_gen_header()
        return openApiHeader.to_json()

    def gen_map(self):
        openApiHeader = self._do_gen_header()
        return openApiHeader.to_dict()

    def _do_gen_header(self):
        timestamp = int(time.time() * 1000)
        appSign = self._do_md5_hash(self.app_id + self.app_secret + str(timestamp))
        return OpenApiHeader(self.app_id, timestamp, appSign)

    @staticmethod
    def _do_md5_hash(content):
        hash_object = hashlib.md5(content.encode())
        return hash_object.hexdigest()