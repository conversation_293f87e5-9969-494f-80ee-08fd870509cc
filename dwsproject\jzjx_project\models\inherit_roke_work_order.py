from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class InheritWorkOrder(models.Model):
    _inherit = "roke.work.order"

    document_ids = fields.Many2many(related="task_id.document_ids", string="作业指导")
    task_file_ids = fields.Many2many(related="task_id.file_ids")

    next_wo_id = fields.Many2one("roke.work.order", string="下道工单", store=True, compute="_compute_next_work_order")

    @api.depends("task_id")
    def _compute_next_work_order(self):
        for v in self:
            if v.type == "生产":
                sequence = min(v.mapped("sequence"))
                next_wos = v.task_id.work_order_ids.filtered(lambda wo: wo.sequence > sequence)
                next_wo = sorted(next_wos, key=lambda x: x['sequence'], reverse=False)
                if not next_wo:
                    continue
                v.next_wo_id = next_wo[0].id
            elif v.type == "返修":
                v.next_wo_id = v.repair_wr_id.work_order_id.id
            elif v.type == "补件":
                sequence = v.sequence
                next_wos = v.repair_wr_id.scrap_line_ids.scrap_work_order_ids.filtered(lambda wo: wo.sequence > sequence)
                next_wo = sorted(next_wos, key=lambda x: x['sequence'], reverse=False)
                if not next_wo:
                    v.next_wo_id = v.repair_wr_id.work_order_id.id
                else:
                    v.next_wo_id = next_wo[0].id

    def _get_production_multiple(self, previous):
        """
        获取当前生产倍数
        :param previous:
        :return:
        """
        judge = "切割" in self.process_id.name or "压铆" in self.process_id.name or "折弯" in self.process_id.name
        if not self.routing_line_id and not judge:
            # 无工艺明细直接返回
            return 1
        if "切割" in self.process_id.name and self.task_id:
            multiple = self.task_id.spare_parts_qty / self.task_id.plan_qty
        elif "压铆" in self.process_id.name and self.task_id:
            multiple = self.task_id.press_riveting_qty / self.task_id.plan_qty
        elif "折弯" in self.process_id.name and self.task_id:
            multiple = self.task_id.bending_qty / self.task_id.plan_qty
        else:
            multiple = self.routing_line_id.multiple
        if len(previous) > 1:
            # 取到最小数对应的前道工单
            rel_previous = self._multi_previous_get_min_finished(previous)
            rel_previous_judge = "切割" in rel_previous.process_id.name or "压铆" in rel_previous.process_id.name \
                                 or "折弯" in rel_previous.process_id.name
            if not rel_previous.routing_line_id.multiple and not rel_previous_judge:  # 无倍数直接返回
                return 1
            if "切割" in rel_previous.process_id.name:
                rel_previous_multiple = rel_previous.task_id.spare_parts_qty / rel_previous.task_id.plan_qty
            elif "压铆" in rel_previous.process_id.name:
                rel_previous_multiple = rel_previous.task_id.press_riveting_qty / rel_previous.task_id.plan_qty
            elif "折弯" in rel_previous.process_id.name:
                rel_previous_multiple = rel_previous.task_id.bending_qty / rel_previous.task_id.plan_qty
            else:
                rel_previous_multiple = rel_previous.routing_line_id.multiple
            return multiple / rel_previous_multiple
        if not previous.routing_line_id.multiple and not judge:  # 无倍数直接返回
            return 1
        elif not previous and judge:
            return 1

        if "切割" in previous.process_id.name and self.task_id and previous.task_id:
            previous_multiple = previous.task_id.spare_parts_qty / self.task_id.plan_qty
        elif "压铆" in previous.process_id.name and self.task_id and previous.task_id:
            previous_multiple = previous.task_id.press_riveting_qty / self.task_id.plan_qty
        elif "折弯" in previous.process_id.name and self.task_id and previous.task_id:
            previous_multiple = previous.task_id.bending_qty / self.task_id.plan_qty
        else:
            previous_multiple = previous.routing_line_id.multiple
        return (multiple / previous_multiple) if previous_multiple else 0
