# -*- coding: utf-8 -*-
"""
Description:
    激光切割机加工记录
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class RokeWorkLog(models.Model):
    _name = "roke.work.log"
    _description = "加工记录"
    _order = "id desc"
    _rec_name = "materialName"

    work_center_id = fields.Many2one("roke.work.center", string="工作中心", index=True)
    fileName = fields.Char(string="图纸文件路径")
    portionId = fields.Char(string="排版唯一标识", index=True)
    startTime = fields.Char(string="加工开始时间")
    endTime = fields.Char(string="加工结束时间")
    timeTaken = fields.Integer(string="实际切割用时")
    tmEstimate = fields.Float(string="预计用时")
    curveLength = fields.Float(string="切割长度")
    moveLength = fields.Float(string="空移长度")
    pierceCount = fields.Integer(string="穿孔数")
    endState = fields.Selection([("0", "已完成"), ("-1", "正在加工"), ("1", "暂停"), ("2", "停止"), ("4", "未知")], string="加工状态")
    material = fields.Char(string="材质", index=True)
    materialName = fields.Char(string="工艺文件名", index=True)
    portionName = fields.Char(string="排版名称", index=True)
    thickness = fields.Integer(string="厚度(mm)")
    plateSize = fields.Char(string="排版尺寸", index=True)
    selected = fields.Boolean(string="是否为选中加工")
    boundSize = fields.Char(string="图形边框尺寸")
    syncTime = fields.Datetime(string="数据同步时间")
    partAmount = fields.Integer(string="零件数量")
    workUuid = fields.Char(string="加工记录标识", index=True)
    taskUuid = fields.Char(string="任务标识")
    part_ids = fields.One2many("roke.work.log.part", "log_id", string="零件信息")

    @api.model
    def create(self, vals):
        res = super(RokeWorkLog, self).create(vals)
        # file_list = res.fileName.split("\\")
        # if not file_list:
        #     return res
        # file_name = file_list[-1].split("_")
        # if not file_name:
        #     return res
        # task_code = file_name[0]
        # task = self.env["roke.production.task"].sudo().search([("code", "=", task_code)])
        # if not task:
        #     return res
        # work_order = self.env["roke.work.order"].sudo().search([("process_id.name", "ilike", "切割")], limit=1)
        # if not work_order:
        #     return res
        # try:
        #     work_order.app_confirm(
        #         finish_qty=sum(res.part_ids.mapped("amount")),
        #         employee_id=self.env.user.employee_id.id,
        #         unqualified_qty=0,
        #         work_hours=round(res.timeTaken / 3600, 2),
        #         team_id=0,
        #     )
        # except Exception as e:
        #     _logger.info(f"{e}")
        return res


class RokeWorkLogPart(models.Model):
    _name = "roke.work.log.part"
    _description = "加工记录配件"

    log_id = fields.Many2one("roke.work.log", string="加工记录", required=True, ondelete="cascade")
    partId = fields.Char(string="零件唯一标识")
    name = fields.Char(string="零件名称")
    amount = fields.Integer(string="单种零件完成数")
