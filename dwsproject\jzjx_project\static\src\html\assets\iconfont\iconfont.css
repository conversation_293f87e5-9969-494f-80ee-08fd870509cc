@font-face {
  font-family: "iconfont"; /* Project id 4723859 */
  src: url('iconfont.woff2?t=1731743989340') format('woff2'),
       url('iconfont.woff?t=1731743989340') format('woff'),
       url('iconfont.ttf?t=1731743989340') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-BOM1:before {
  content: "\e707";
}

.icon-BOM:before {
  content: "\e622";
}

.icon-tuozhuai1:before {
  content: "\e611";
}

.icon-chanpin2:before {
  content: "\e61b";
}

.icon-chanpin1:before {
  content: "\e623";
}

.icon-zhankai:before {
  content: "\e60a";
}

.icon-zhedie:before {
  content: "\e62f";
}

.icon-wenjianjiazhankai:before {
  content: "\e602";
}

.icon-wenjianjiaguanbi:before {
  content: "\e74a";
}

