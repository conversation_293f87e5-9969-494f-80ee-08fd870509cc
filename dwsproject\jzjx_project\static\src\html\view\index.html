<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>工艺设置</title>
    <!-- 禁止界面缩放 -->
    <meta content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=0" name="viewport" />
    <!-- 引入本地文件--Vue、axios、element-ui的样式文件、element-ui的JS文件-->
    <link rel="stylesheet" href="/roke_workstation_api/static/html/routing/element-ui/index.css" />
    <link rel="stylesheet" href="/roke_workstation_api/static/html/routing/assets/iconfont/iconfont.css" />
    <script src="/roke_workstation_api/static/html/routing/js/vue.js"></script>
    <script src="/roke_workstation_api/static/html/routing/js/axios.min.js"></script>
    <script src="/roke_workstation_api/static/html/routing/js/Sortable.min.js"></script>
    <script src="/roke_workstation_api/static/html/routing/js/vuedraggable.umd.min.js"></script>
    <script src="/roke_workstation_api/static/html/routing/element-ui/index.js"></script>
    <script src="/roke_workstation_api/static/html/routing/assets/iconfont/iconfont.js"></script>
</head>

<body id="bodyId" style="display: none;">
    <div id="app" v-loading.body.fullscreen.lock="loading">
        <!-- 左侧树形区域 -->
        <div class="leftBox">
            <div class="searchBox">
                <el-input prefix-icon="el-icon-search" v-model="treeFilterValue" size="medium"
                    placeholder="请输入关键字"></el-input>
                <div class="unfoldBox" @click="unfoldHandel">
                    <el-tooltip content="全部折叠">
                        <i class="iconfont icon-zhedie"></i>
                    </el-tooltip>
                </div>
            </div>
            <div class="treeBox">
                <el-tree class="treeComBox" ref="treeRef" node-key="id" highlight-current :data="treeList"
                    :filter-node-method="treeFilterNode" :props="defaultProps" :load="loadNode" lazy
                    @node-click="handleNodeClick">
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span style="padding-right: 15px;" class="textBox">
                            <i v-if="node.expanded&&data.type=='category'" class="iconfont icon-wenjianjiazhankai"></i>
                            <i v-if="!node.expanded&&data.type=='category'" class="iconfont icon-wenjianjiaguanbi"></i>
                            <i v-if="node.expanded&&data.type=='product'" class="iconfont icon-chanpin2"></i>
                            <i v-if="!node.expanded&&data.type=='product'" class="iconfont icon-chanpin1"></i>
                            <i v-if="node.expanded&&data.type=='bom'" class="iconfont icon-BOM1"></i>
                            <i v-if="!node.expanded&&data.type=='bom'" class="iconfont icon-BOM"></i>
                            [[ node.label ]]
                            <span v-if="('product_active' in data)&&!data.product_active">(已归档)</span>
                            <span v-if="data.bom_type=='bomline'||data.bom_type=='bom+bomline'">
                                ([[data.quantity]])
                            </span>
                        </span>
                        <span class="iconBox">
                            <el-dropdown placement="bottom" @visible-change="menuVisibleChange($event,node,data)"
                                @command="menuCommandHandle($event,node,data)">
                                <i class="el-icon-more iconClass"></i>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item :command="item.value" v-for="(item,index) in menuList"
                                        :key="item.index">
                                        [[item.text]]
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </span>
                    </span>
                </el-tree>
            </div>
        </div>
        <!-- 右侧内容区域 -->
        <div class="rightBox">
            <div class="rightTopBox">
                <div class="infoBox">
                    <label>编号：</label>
                    <el-tooltip :content="infoHandle('编号')" :disabled="infoHandle('编号').length<9" placement="top">
                        <span>[[ infoHandle('编号')]]</span>
                    </el-tooltip>
                </div>
                <div class="infoBox">
                    <label>名称：</label>
                    <el-tooltip :content="infoHandle('名称')" :disabled="infoHandle('名称').length<9" placement="top">
                        <span>[[infoHandle('名称')]]</span>
                    </el-tooltip>
                </div>
                <div class="infoBox">
                    <label>类别：</label>
                    <el-tooltip :content="infoHandle('类别')" :disabled="infoHandle('类别').length<9" placement="top">
                        <span>[[infoHandle('类别')]]</span>
                    </el-tooltip>
                </div>
                <div class="infoBox">
                    <label>规格型号：</label>
                    <el-tooltip :content="infoHandle('规格型号')" :disabled="infoHandle('规格型号').length<9" placement="top">
                        <span>[[infoHandle('规格型号')]]</span>
                    </el-tooltip>
                </div>
                <div class="infoBox">
                    <label>数量：</label>
                    <el-tooltip :content="infoHandle('数量')" :disabled="infoHandle('数量').length<9" placement="top">
                        <span>[[infoHandle('数量')]]</span>
                    </el-tooltip>
                </div>
            </div>
            <div class="rightBottomBox">
                <div class="rightBottomLeftBox">
                    <div class="title">
                        <span>产品工序</span>
                        <el-button size="small" :disabled="!treeActiveData.type||treeActiveData.type=='category'"
                            type="primary" @click="() => addProcessDialogShow = true">
                            添加
                        </el-button>
                    </div>
                    <div class="processBox">
                        <draggable v-model="productProcessList" chosen-class="chosen" delay="0" group="people"
                            animation="100" force-fallback class="draggableAreaStyle" @end="processSortEndHandle">
                            <div class="processItemBox" :class="{processActive:item.show}"
                                v-for="(item,index) in productProcessList" :key="index" @click="selProcessHandle(item)">
                                <span>[[item.process_name]]</span>
                                <span>
                                    <i class="el-icon-delete" @click.stop="deleteProcessIconHandle(item,index)"></i>
                                    <i class="iconfont icon-tuozhuai1"></i>
                                </span>
                            </div>
                        </draggable>
                    </div>
                </div>
                <div class="rightBottomRightBox">
                    <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"
                        @select="menuSelectHandle">
                        <el-menu-item index="工艺参数">工艺参数</el-menu-item>
                        <el-menu-item index="工艺文件">工艺文件</el-menu-item>
                        <el-menu-item index="设备检查项">设备检查项</el-menu-item>
                        <el-menu-item index="不良类型">不良类型</el-menu-item>
                        <el-menu-item index="生产信息">生产信息</el-menu-item>
                    </el-menu>
                    <div class="menuContentArea">
                        <!-- 工艺参数 -->
                        <div v-if="activeIndex=='工艺参数'">
                            <el-table ref="tabelRef" :data="isEdit?standardsCopyList:standardsList"
                                :height="windowHeight" :header-cell-style="{backgroundColor:'#f5f7fa'}"
                                style="width: 100%">
                                <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                                <el-table-column label="参数名称" align="center">
                                    <template slot-scope="scope">
                                        <el-input v-if="isEdit" size="medium" v-model="scope.row.title"></el-input>
                                        <span v-else>[[scope.row.title]]</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="值" align="center">
                                    <template slot-scope="scope">
                                        <el-input v-if="isEdit" size="medium" v-model="scope.row.content"></el-input>
                                        <span v-else>[[scope.row.content]]</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="参数说明" align="center">
                                    <template slot-scope="scope">
                                        <el-input v-if="isEdit" size="medium" type="textarea" autosize
                                            v-model="scope.row.description">
                                        </el-input>
                                        <span v-else>[[scope.row.description]]</span>
                                    </template>
                                </el-table-column>
                                <el-table-column v-if="!isEdit" label="操作" align="center" width="80">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="deleteInfoHandle('工艺参数',scope.row)">
                                            <i class="el-icon-delete" style="font-size: 18px"></i>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="padding: 10px;">
                                <el-button type="primary" size="small" :disabled="!selProductProcessData||isEdit"
                                    @click="addInfoHandle('工艺参数')">
                                    添加
                                </el-button>
                                <el-button type="primary" size="small" @click="editInfoHandle('工艺参数')"
                                    :disabled="!selProductProcessData">
                                    [[isEdit?'取消编辑':'编辑']]
                                </el-button>
                                <el-button type="primary" :disabled="!isEdit" size="small"
                                    @click="saveInfoHandle('工艺参数')">保存</el-button>
                            </div>
                        </div>
                        <!-- 工艺文件 -->
                        <div v-if="activeIndex=='工艺文件'">
                            <el-table ref="tabelRef" :header-cell-style="{backgroundColor:'#f5f7fa'}"
                                style="width: 100%" :data="documentsList" :height="windowHeight">
                                <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                                <el-table-column label="工艺文件名称" align="center" show-overflow-tooltip>
                                    <template slot-scope="scope">
                                        <div style="display: flex; align-items: center; ">
                                            <el-image style="width: 1rem; height: 1rem;margin-right: 5px;"
                                                :src="baseURL+scope.row.thumbnail" fit="contain">
                                            </el-image>
                                            [[scope.row.name]]
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="上传人" align="center" width="80">
                                    <template slot-scope="scope">
                                        <div style="display: flex; align-items: center; ">
                                            [[scope.row.create_user]]
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="上传时间" align="center" width="160">
                                    <template slot-scope="scope">
                                        <div style="display: flex; align-items: center; ">
                                            [[dateTimeHandle(scope.row.create_date)]]
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="文件类型" align="center" width="100">
                                    <template slot-scope="scope">
                                        <div style="display: flex; align-items: center; ">
                                            [[
                                            getFileTypeHandle(scope.row.mimetype)!='其它文件'?getFileTypeHandle(scope.row.mimetype):
                                            getFileSuffixHandle(scope.row.name) ]]
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" align="center" width="100">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" style="margin-left: 10px;"
                                            @click="filePreviewHandle(scope.row)">
                                            <i class="el-icon-view" style="font-size: 18px"></i>
                                        </el-button>
                                        <el-button type="text" size="small" style="margin-left: 10px;"
                                            @click="deleteInfoHandle('工艺文件',scope.row)">
                                            <i class="el-icon-delete" style="font-size: 18px"></i>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="padding: 10px;">
                                <el-upload :auto-upload="false" action="#" :disabled="!this.selProductProcessData"
                                    :show-file-list="false" :on-change="uploadChange">
                                    <el-button type="primary" size="small" :disabled="!this.selProductProcessData">
                                        上传附件
                                    </el-button>
                                </el-upload>
                            </div>
                        </div>
                        <!-- 设备检查项 -->
                        <div v-if="activeIndex=='设备检查项'">
                            <el-table ref="tabelRef" :data="checksList" :header-cell-style="{backgroundColor:'#f5f7fa'}"
                                style="width: 100%" :height="windowHeight">
                                <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                                <el-table-column prop="name" label="名称" align="center"></el-table-column>
                                <el-table-column prop="input_type" label="类型" align="center">
                                    <template slot-scope="scope">
                                        [[typeHandle(scope.row.input_type)]]
                                    </template>
                                </el-table-column>
                                <el-table-column prop="description" label="检查内容" align="center"></el-table-column>
                                <el-table-column prop="standard_value" label="检查值" align="center">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.input_type=='float'||scope.row.input_type=='int'">
                                            [[scope.row.lower_value]] ~ [[scope.row.upper_value]]
                                        </span>
                                        <span v-else>
                                            [[scope.row.standard_value]]
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" align="center" width="100">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="editInfoHandle('设备检查项',scope.row)">
                                            <i class="el-icon-edit" style="font-size: 18px"></i>
                                        </el-button>
                                        <el-button type="text" size="small"
                                            @click="deleteInfoHandle('设备检查项',scope.row)">
                                            <i class="el-icon-delete" style="font-size: 18px"></i>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="padding: 10px;">
                                <el-button type="primary" size="small" :disabled="!selProductProcessData||isEdit"
                                    @click="addInfoHandle('设备检查项')">
                                    添加
                                </el-button>
                            </div>
                        </div>
                        <!-- 不良类型 -->
                        <div v-if="activeIndex=='不良类型'">
                            <el-table ref="tabelRef" :data="scrapReasonsList" :height="windowHeight"
                                :header-cell-style="{backgroundColor:'#f5f7fa'}" style="width: 100%">
                                <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
                                <el-table-column prop="name" label="产品不良类型" align="center"></el-table-column>
                                <el-table-column label="操作" align="center" width="80">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="deleteInfoHandle('不良类型',scope.row)">
                                            <i class="el-icon-delete" style="font-size: 18px"></i>
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="padding: 10px;">
                                <el-button type="primary" size="small" key="selected" :disabled="!selProductProcessData"
                                    @click="badnessTypeBtnHandle('selected')">
                                    选择不良类型
                                </el-button>
                                <el-button type="primary" size="small" key="create" :disabled="!selProductProcessData"
                                    @click="badnessTypeBtnHandle('create')">
                                    创建不良类型
                                </el-button>
                            </div>
                        </div>
                        <!-- 生产信息 -->
                        <div v-if="activeIndex=='生产信息'" style="width: 100%; padding: 0 15px;">
                            <el-form class="fromBox" :model="productionInfoData" label-width="6rem">
                                <el-form-item label="标准产能：" prop="standard_title" style="margin-top: 15px;">
                                    <el-input :disabled="!isEdit" v-model="productionInfoData.capacity"
                                        placeholder="请填写标准产能"></el-input>
                                </el-form-item>
                                <el-form-item label="FPY：" prop="standard_content" style="margin-top: 15px;">
                                    <el-input :disabled="!isEdit" v-model="productionInfoData.fpy"
                                        placeholder="请填写FPY"></el-input>
                                </el-form-item>
                            </el-form>
                            <div
                                style="display: flex; align-items: center; justify-content: space-evenly; margin-top: 15px;">
                                <el-button type="primary" :disabled="!(treeActiveData.type=='product')" size="small"
                                    @click="editInfoHandle('生产信息')">
                                    [[isEdit?'取消编辑':'编辑']]
                                </el-button>
                                <el-button type="primary" :disabled="!isEdit" size="small"
                                    @click="saveInfoHandle('生产信息')">
                                    保存
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 创建产品弹窗 -->
        <el-dialog :title="isNoBom?'创建并关联':'添加产品'" :visible.sync="createProductDialogShow" width="50%"
            @close="dialogCloseHandle('新增产品')" @open="createProductDialogOpen">
            <div class="fromBox">
                <div class="fromItemBox" v-if="isNoBom">
                    <label>选择类别：</label>
                    <el-select style="width: 100%;" v-model="createProductData.category_id" placeholder="请选择">
                        <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
                <div class="fromItemBox">
                    <label>产品名称：</label>
                    <el-input v-model="createProductData.product_name" placeholder="请输入"></el-input>
                </div>
                <div class="fromItemBox">
                    <label>规格型号：</label>
                    <el-input v-model="createProductData.specification" placeholder="请输入"></el-input>
                </div>
                <div class="fromItemBox" v-if="isNoBom">
                    <label>录入数量：</label>
                    <el-input v-model="createProductData.qty" placeholder="请输入"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('新增产品')">取消</el-button>
                <el-button size="small" type="primary" @click="dialogSaveHandle('新增产品')">保存</el-button>
            </span>
        </el-dialog>
        <!-- 修改产品弹窗 -->
        <el-dialog title="修改产品" :visible.sync="editProductDialogShow" width="50%" @close="dialogCloseHandle('编辑产品')"
            @open="editProductDialogOpen">
            <div class="fromBox">
                <div class="fromItemBox">
                    <label>选择类别：</label>
                    <el-select style="width: 100%;" v-model="editProductData.category_id" placeholder="请选择">
                        <el-option v-for="item in categoryList" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
                <div class="fromItemBox">
                    <label>产品名称：</label>
                    <el-input v-model="editProductData.product_name" placeholder="请输入"></el-input>
                </div>
                <div class="fromItemBox">
                    <label>规格型号：</label>
                    <el-input v-model="editProductData.specification" placeholder="请输入"></el-input>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('编辑产品')">取消</el-button>
                <el-button size="small" type="primary" @click="dialogSaveHandle('编辑产品')">保存</el-button>
            </span>
        </el-dialog>
        <!-- 创建BOM弹窗 -->
        <el-dialog title="创建BOM" :visible.sync="createBomDialogShow" destroy-on-close width="70%"
            @close="dialogCloseHandle('新增BOM')" @open="createBomDialogOpen">
            <el-input v-model="searchProductValue" placeholder="请输入产品名称" @change="searchProductChange"></el-input>
            <el-table ref="singleTable" border :data="productList" height="55vh" :row-key="(row)=>{return row.id}"
                highlight-current-row style="width: 100%; margin-top: 10px;" @current-change="handleCurrentChange">
                <el-table-column prop="category_name" label="类别" align="center"></el-table-column>
                <el-table-column prop="name" label="名称" align="center"></el-table-column>
                <el-table-column prop="specification" label="规格型号" align="center"></el-table-column>
                <el-table-column prop="qty" label="数量" align="center">
                    <template slot-scope="scope">
                        <el-input-number v-if="scope.row.id==currentRow?.id" style="width: 100%;"
                            v-model="scope.row.qty" :min="1">
                        </el-input-number>
                        <span v-else>[[scope.row.qty]]</span>
                    </template>
                </el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('新增BOM')">取消</el-button>
                <el-button size="small" type="primary" @click="createProductBtnHandle">新建产品</el-button>
                <el-button size="small" type="primary" :disabled="!this.currentRow" @click="dialogSaveHandle('新增BOM')">
                    选择关联
                </el-button>
            </span>
        </el-dialog>
        <!-- 添加产品工序弹窗 -->
        <el-dialog title="添加产品工序" :visible.sync="addProcessDialogShow" destroy-on-close width="60%"
            @close="dialogCloseHandle('添加产品工序')" @open="addProcessDialogOpen">
            <div style="margin-top: 15px; display: flex; align-items: center; justify-content: center;">
                <el-transfer v-model="multipleSelection" :data="processList" filterable filter-placeholder="请输入名称"
                    :titles="['工序列表','选中列表']" :props="{key:'id', label:'name',disabled:'disabled'}">
                </el-transfer>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('添加产品工序')">取消</el-button>
                <el-button size="small" type="primary" :disabled="multipleSelection.length<1"
                    @click="dialogSaveHandle('添加产品工序')">
                    确定
                </el-button>
            </span>
        </el-dialog>
        <!-- 添加工艺参数弹窗 -->
        <el-dialog title="添加工艺参数" :visible.sync="standardsDialogShow" destroy-on-close width="40%"
            @close="dialogCloseHandle('添加工艺参数')">
            <el-form class="fromBox" :model="standardsForm" :rules="standardsRules" ref="standardsFormRef"
                label-width="6rem">
                <el-form-item label="参数名称：" prop="standard_title" required>
                    <el-input v-model="standardsForm.standard_title" placeholder="请填写参数名称"></el-input>
                </el-form-item>
                <el-form-item label="值：" prop="standard_content" required>
                    <el-input v-model="standardsForm.standard_content" placeholder="请填写值"></el-input>
                </el-form-item>
                <el-form-item label="参数说明：" prop="standard_description">
                    <el-input type="textarea" :autosize="{ minRows: 2}" v-model="standardsForm.standard_description"
                        placeholder="请填写参数说明">
                    </el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('添加工艺参数')">取消</el-button>
                <el-button size="small" type="primary" @click="dialogSaveHandle('添加工艺参数')">确定</el-button>
            </span>
        </el-dialog>
        <!-- 添加设备检查项弹窗 -->
        <el-dialog :title="isEdit?'修改设备检查项':'添加设备检查项'" :visible.sync="checksDialogShow" destroy-on-close width="50%"
            @close="dialogCloseHandle('添加设备检查项')">
            <div style="max-height: 60vh; overflow-y: scroll; overflow-x: hidden;">
                <el-form class="fromBox" :model="checksForm" :rules="checksRules" ref="checksFormRef"
                    label-width="6rem">
                    <el-form-item label="类型：" prop="input_type" required>
                        <el-select v-model="checksForm.input_type" placeholder="请选择类型" style="width: 100%;"
                            @change="selectTyleChange">
                            <el-option v-for="item in equipmentInspectionTypeList" :key="item.value" :label="item.name"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="名称：" prop="name" required>
                        <el-input v-model="checksForm.name" placeholder="请填写名称"></el-input>
                    </el-form-item>
                    <el-form-item label="描述：" prop="description">
                        <el-input type="textarea" :autosize="{ minRows: 2}" v-model="checksForm.description"
                            placeholder="请填写描述">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="标准值：" prop="standard_value"
                        v-if="checksForm.input_type!='float'&&checksForm.input_type!='int'">
                        <el-input v-model="checksForm.standard_value" placeholder="请填写标准值"></el-input>
                    </el-form-item>
                    <el-form-item label="标准下限：" prop="lower_value"
                        v-if="checksForm.input_type=='float'||checksForm.input_type=='int'">
                        <el-input-number v-model="checksForm.lower_value" :precision="checksForm.input_type=='int'?0:2"
                            placeholder="请填写标准下限" :min="0"></el-input-number>
                    </el-form-item>
                    <el-form-item label="标准上限：" prop="upper_value"
                        v-if="checksForm.input_type=='float'||checksForm.input_type=='int'">
                        <el-input-number v-model="checksForm.upper_value" :precision="checksForm.input_type=='int'?0:2"
                            placeholder="请填写标准上限" :min="0"></el-input-number>
                    </el-form-item>
                    <el-form-item v-for="(item,index) in checksForm.select_item_ids" :key="index"
                        :label="`可选值${(index+1)}：`" prop="select_item_ids" v-if="checksForm.input_type=='select'">
                        <el-input v-model="item.value" placeholder="请填写可选值"></el-input>
                    </el-form-item>
                    <div style="display: flex; align-items: center; justify-content: center;"
                        v-if="checksForm.input_type=='select'">
                        <el-button size="small" type="primary" @click="()=>checksForm.select_item_ids.push({value:''})">
                            新增可选值
                        </el-button>
                    </div>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('添加设备检查项')">取消</el-button>
                <el-button size="small" type="primary" @click="dialogSaveHandle('添加设备检查项')">确定</el-button>
            </span>
        </el-dialog>
        <!-- 选择不良类型弹窗 -->
        <el-dialog title="选择不良类型" :visible.sync="selBadnessTypeDialogShow" destroy-on-close width="60%"
            @close="dialogCloseHandle('选择不良类型')" @open="selBadnessTypeDialogOpen">
            <div style="display: flex; align-items: center; justify-content: center;">
                <el-transfer v-model="selBadnessTypeData" :data="badnessTypeList" filterable filter-placeholder="请输入名称"
                    :titles="['工序列表','选中列表']" :props="{key:'id', label:'name',disabled:'disabled'}">
                </el-transfer>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button size="small" @click="dialogCloseHandle('选择不良类型')">取消</el-button>
                <el-button size="small" type="primary" @click="dialogSaveHandle('选择不良类型')">确定</el-button>
            </span>
        </el-dialog>
    </div>
</body>

<script>
    document.addEventListener("click", function () {
        // 发送消息给父页面
        window.parent.postMessage("hidePopover", "*");
    })
    Vue.component('vuedraggable', window.vuedraggable) // 注册拖拽组件
    const validatePass = (rule, value, callback) => {
        if (value.length < 1) {
            callback(new Error('当前字段为必填字段！'))
        } else {
            callback()
        }
    }
    let vue = new Vue({
        el: '#app',
        delimiters: ["[[", "]]"], // 替换原本vue的[[ key ]]取值方式(与odoo使用的jinja2冲突问题)
        components: {
            vuedraggable: window.vuedraggable, // 拖拽组件
        },
        data() {
            return {
                windowHeight: document.documentElement.clientHeight - 190, // 高度
                baseURL: '', // 基地址
                loading: false, // 全局加载效果
                treeFilterValue: '', // 树形搜索框的值
                defaultProps: { children: 'children', label: 'name', isLeaf: 'leaf' }, // 树形组件对应关系
                treeList: [],  // 树形组件数据
                treeNode: null, // 当前操作的树形组件节点
                treeData: null, // 当前操作的树形组件节点数据
                menuList: [], // 树形组件下拉菜单列表
                menuType: null, // 当前操作的树形组件菜单类型
                treeActiveData: {}, // 树形组件当前高亮选中的树形数据
                routingData: {}, // 产品工艺路线信息
                productProcessList: [], // 产品下工序列表
                createProductDialogShow: false, // 创建产品弹窗状态
                createProductData: {}, // 创建产品表单数据
                editProductDialogShow: false, // 修改产品弹窗状态
                categoryList: [], // 可用产品类别列表
                editProductData: {}, // 修改产品表单数据
                createBomDialogShow: false, // 创建BOM弹窗状态
                searchProductValue: '', // 搜索产品输入框的值
                productList: [], // 产品列表
                currentRow: null, // 选中的产品数据
                isNoBom: false, // 创建产品时是否为无类别创建产品
                addProcessDialogShow: false, // 添加产品工序弹窗状态
                searchProcessValue: '', // 搜索工序输入框的值
                processList: [], // 添加产品工序弹窗工序列表
                multipleSelection: [], // 添加产品工序弹窗选中工序列表
                activeIndex: "工艺参数", // 当前选中的导航菜单
                isEdit: false, // 是否为编辑状态
                standardsDialogShow: false, // 工艺参数添加弹窗状态
                standardsForm: {}, // 新建工艺参数表单数据
                // 添加工艺参数 必填效验
                standardsRules: {
                    standard_title: [{ required: true, trigger: 'blur', message: '请输入参数名称' }],
                    standard_content: [{ required: true, trigger: 'blur', message: '请输入值' }],
                },
                standardsCopyList: [], // 工艺参数编辑时的列表
                checksDialogShow: false, // 设备检查项添加弹窗状态
                checksForm: {
                    input_type: '',
                    name: '',
                    description: '',
                    standard_value: '',
                    select_item_ids: [],
                    lower_value: 0,
                    upper_value: 0
                }, // 新建检查项表单数据
                // 添加设备检查项 必填效验
                checksRules: {
                    input_type: [{ required: true, trigger: 'blur', message: '请选择类型' }],
                    name: [{ required: true, trigger: 'blur', message: '请输入名称' }],
                },
                // 设备检查项类型列表
                equipmentInspectionTypeList: [
                    { name: '选择', value: 'select' },
                    { name: '文本', value: 'text' },
                    { name: '小数', value: 'float' },
                    { name: '整数', value: 'int' },
                    { name: '布尔', value: 'boolean' },
                ],
                productionInfoData: {}, // 生产信息数据
                selBadnessTypeDialogShow: false, // 选择不良类型弹窗
                badnessTypeList: [], // 不良类型列表
                selBadnessTypeData: [], // 选中的不良类型列表
            }
        },
        computed: {
            // 选中的产品下的工序
            selProductProcessData() {
                return this.productProcessList.find(item => item.show)
            },
            // 工艺参数列表
            standardsList() {
                return this.selProductProcessData?.standards ? this.selProductProcessData.standards : []
            },
            // 工艺文件列表
            documentsList() {
                return this.selProductProcessData?.documents ? this.selProductProcessData.documents : []
            },
            // 设备检查项列表
            checksList() {
                return this.selProductProcessData?.checks ? this.selProductProcessData.checks : []
            },
            // 不良类型列表
            scrapReasonsList() {
                return this.selProductProcessData?.scrap_reasons ? this.selProductProcessData.scrap_reasons : []
            },
        },
        watch: {
            // 监听树形搜索框的变化，进行模糊搜索
            treeFilterValue(newVal) {
                this.$refs.treeRef.filter(newVal)
                // 递归折叠方法
                let expandNodeFn = (node) => {
                    node.expanded = newVal ? true : false
                    if (node.childNodes) {
                        node.childNodes.forEach(childNode => {
                            expandNodeFn(childNode)
                        })
                    }
                }
                this.$refs.treeRef.store.root.childNodes.forEach((item, index) => {
                    expandNodeFn(item)
                })
            },
            // 监听产品表格选中的数据,把其他未选中的数量重置为1
            currentRow: {
                handler(newVal) {
                    if (!newVal) return
                    this.productList.forEach(item => {
                        if (newVal.id != item.id) {
                            item.qty = 1
                        }
                    })
                },
                deep: true
            },
            // 监听菜单变化
            activeIndex(newVal) {
                if (newVal && newVal != '生产信息') {
                    // 解决切换时表格会闪的问题
                    this.$nextTick(() => {
                        this.$refs.tabelRef.doLayout()
                    })
                }
            }
        },
        mounted() {
            // 在 mounted 钩子函数时才显示内容,防止出现白屏问题
            setTimeout(() => {
                document.getElementById("bodyId").style.display = "block"
            }, 800)
            window.onresize = () => {
                this.windowHeight = document.documentElement.clientHeight - 190
            }
        },
        methods: {
            // 接口请求方法封装
            requestApi(url, config, errorMessage = "操作失败，请稍后重试", contentType = 'application/json') {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + url,
                        data: config,
                        headers: { 'Content-Type': contentType },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0 || result?.data?.result?.state == 'success' || result?.data?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.result?.state == 'error') {
                            reject(result.data.result.megs)
                        } else if (result?.data?.code == 0) {
                            reject(result.data.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject(errorMessage)
                    })
                })
            },
            // 获取物料信息列表
            getMaterialInfoListApi() {
                return new Promise((resolve, reject) => {
                    this.loading = true
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/tree",
                        data: {},
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            result.data.result.data.forEach(item => {
                                this.leafHandleFn(item)
                            })
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("物料信息获取失败!")
                    })
                })
            },
            // 获取Bom列表
            getBomsListApi(config = {}) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/boms",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            result.data.result.data.forEach(item => {
                                this.leafHandleFn(item)
                            })
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("BOM信息获取失败!")
                    })
                })
            },
            // 获取子Bom列表
            getSubBomsListApi(config = {}) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/sub_boms",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            result.data.result.data.forEach(item => {
                                this.leafHandleFn(item)
                            })
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("子BOM信息获取失败!")
                    })
                })
            },
            // 删除方法
            deleteApi(url, config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + url,
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("删除失败,请稍后重试！")
                    })
                })
            },
            // 新建产品类别方法
            createCategoryApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/create/category",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("创建类别失败！")
                    })
                })
            },
            // 修改产品类别方法
            editCategoryApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/update/category",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("创建类别失败！")
                    })
                })
            },
            // 在产品类别上添加产品
            categoryCreateProductApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/create/category/product",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("创建产品失败！")
                    })
                })
            },
            // 在产品上添加同级产品
            productCreateProductApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/create/product/product",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("创建产品失败！")
                    })
                })
            },
            // 获取可用产品类别
            getCategoryListApi() {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/search_read",
                        data: {
                            model: "roke.product.category",
                            domain: [["is_finished", "=", true]],
                            fields: ["id", "name"],
                            order: "id asc"
                        },
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取可用类别列表失败!")
                    })
                })
            },
            // 修改产品信息方法
            editProductInfoApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/update/product",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("修改产品信息失败!")
                    })
                })
            },
            // 获取产品列表(带搜索)
            getProductListApi() {
                return new Promise((resolve, reject) => {
                    let config = {
                        model: "roke.product",
                        fields: ["id", "code", "name", "specification", "category_id"],
                        order: "id asc",
                        domain: [["name", "ilike", this.searchProductValue]]
                    }
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/search_read",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            result.data.result.data.forEach(item => {
                                item.category_name = item.category_id ? item.category_id[1] : ''
                                item.category_id = item.category_id ? item.category_id[0] : ''
                                item.qty = 1
                            })
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取产品列表失败!")
                    })
                })
            },
            // 创建无BOM产品方法
            createNoBomProductApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/create/product",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("创建BOM信息失败!")
                    })
                })
            },
            // 创建BOM方法
            createBomApi(url, config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + url,
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.data.message)
                        }
                    }).catch((error) => {
                        reject("创建BOM信息失败!")
                    })
                })
            },
            // 修改BOM方法
            editBomApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/dws/pd/bom/update",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("修改BOM失败!")
                    })
                })
            },
            // 获取工艺路线信息
            getRoutingInfoApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/materiail/routing/get",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 获取产品下工序列表
            getProductProcessListApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/routing/process/get",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 删除工序方法
            deleteProcessApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/routing/process/unlink",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 获取工序列表(带搜索)
            getProcessListApi() {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/search_read",
                        data: {
                            fields: ["id", "name", "category_id"],
                            model: "roke.process",
                            domain: [["name", "ilike", this.searchProcessValue]]
                        },
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            result.data.result.data.forEach(item => {
                                item.category_name = item.category_id ? item.category_id[1] : ''
                                item.category_id = item.category_id ? item.category_id[0] : 0
                            })
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 保存工艺路线明细
            routingSaveProcessApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/routing/process/save",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 工艺路线信息列表排序保存方法
            routingInfoSortSaveApi(config) {
                return new Promise((resolve, reject) => {
                    axios({
                        method: "POST",
                        url: this.baseURL + "/roke/workstation/routing/process/sort",
                        data: config,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }).then((result) => {
                        if (result?.data?.result?.code == 0) {
                            resolve(result.data)
                        } else if (result?.data?.result?.code == 1) {
                            reject(result.data.result.message)
                        } else if (result?.data?.error) {
                            reject(result.data.error.message)
                        }
                    }).catch((error) => {
                        reject("获取工序列表失败！")
                    })
                })
            },
            // 错误处理方法
            errorHandle(message, isCloseLoading) {
                if (message) {
                    this.$message.error(message)
                }
                if (isCloseLoading) {
                    this.loading = false
                }
            },
            // 树形组件筛选方法
            treeFilterNode(value, data) {
                if (!value) return true
                return data.name.indexOf(value) !== -1
            },
            // 展开折叠icon点击事件
            unfoldHandel() {
                // 递归折叠方法
                let collapseNodeFn = (node) => {
                    node.expanded = false
                    if (node.childNodes) {
                        node.childNodes.forEach(childNode => {
                            collapseNodeFn(childNode)
                        })
                    }
                }
                this.$refs.treeRef.store.root.childNodes.forEach((item, index) => {
                    collapseNodeFn(item)
                })
            },
            // 递归处理没有子级不展示展开箭头方法
            leafHandleFn(data) {
                if (data.type == 'category') {
                    data['leaf'] = (data.children.length > 0) ? false : true
                } else if (data.type == 'product') {
                    data['leaf'] = (data?.has_bom) ? false : true
                } else if (data.type == 'bom') {
                    data['leaf'] = (data?.has_children) ? false : true
                }
                if (data?.children?.length > 0) {
                    data.children.forEach(item => {
                        this.leafHandleFn(item)
                    })
                }
            },
            // 树形组件展开事件
            async loadNode(node, resolve) {
                if (node.level === 0) {
                    // 刚开始渲染获取产品类别和产品
                    await this.getMaterialInfoListApi().then(data => {
                        this.loading = false
                        if (data.result.data.length > 0) {
                            return resolve(data.result.data)
                        } else {
                            return resolve([])
                        }
                    }).catch(error => {
                        this.errorHandle(error, true)
                        return resolve([])
                    })
                } else {
                    if (node?.data?.type == 'category') {
                        return resolve(node.data.children)
                    } else if (node?.data?.type == 'product') {
                        if (node?.data?.children?.length > 0) {
                            return resolve(node.data.children)
                        } else {
                            await this.getBomsListApi({ product_id: node.data.id }).then(data => {
                                return resolve(data.result.data)
                            }).catch(error => {
                                this.errorHandle(error)
                                return resolve([])
                            })
                        }
                    } else if (node?.data?.type == 'bom') {
                        if (node?.data?.children?.length > 0) {
                            return resolve(node.data.children)
                        } else {
                            if (node.data.query_id) {
                                await this.getSubBomsListApi({ bom_id: node.data.query_id }).then(data => {
                                    return resolve(data.result.data)
                                }).catch(error => {
                                    this.errorHandle(error)
                                    return resolve([])
                                })
                            } else {
                                return resolve([])
                            }
                        }
                    }
                }
            },
            // 点击树形item
            handleNodeClick(data, node) {
                this.isEdit = false
                this.treeActiveData = JSON.parse(JSON.stringify(data))
                if (data.type != "category") {
                    if (('product_active' in data) && !data.product_active) {
                        this.productProcessList = []
                        this.routingData = {}
                        return
                    }
                    this.loading = true
                    let config = {
                        product_id: data.type == 'product' ? data.id : data.product_id,
                        routing_state: "确认"
                    }
                    this.getRoutingInfoApi(config).then(res => {
                        if (res.result.data.length > 0) {
                            this.routingData = res.result.data[0]
                            let processConfig = {
                                routing_id: this.routingData.routing_id
                            }
                            this.getProductProcessListApi(processConfig).then(result => {
                                this.loading = false
                                result.result.data.forEach(item => {
                                    item['show'] = false
                                })
                                this.productProcessList = result.result.data
                            }).catch(error => {
                                this.errorHandle(error, true)
                            })
                        } else {
                            this.loading = false
                            this.routingData = {}
                            this.productProcessList = []
                        }
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                }
                if (data.type == "product") {
                    this.productionInfoData = {
                        product_id: data.id,
                        category_id: data.category.id,
                        product_name: data.name,
                        specification: data.specification,
                        capacity: data.capacity,
                        fpy: data.fpy
                    }
                } else {
                    this.productionInfoData = {}
                    this.routingData = {}
                    this.productProcessList = []
                }
            },
            // 菜单展示隐藏事件
            menuVisibleChange(flag, node, data) {
                if (flag) {
                    this.treeNode = node
                    this.treeData = data
                    this.menuList = this.getMenuListFn(data)
                } else {
                    this.menuList = []
                }
            },
            // 获取下拉菜单列表
            getMenuListFn(data) {
                if (data.type == "category") {
                    // 类别
                    if (data.parent_id == 0) {
                        // 顶级产品类别 -- 不能新增同级产品
                        return [
                            { text: '新增同级类别', value: 'createPeerCategory' },
                            { text: '新增下级类别', value: 'createJuniorCategory' },
                            { text: '新增下级产品', value: 'createJuniorProduct' },
                            { text: '删除', value: 'delete' },
                            { text: '修改', value: 'edit' }
                        ]
                    } else {
                        return [
                            { text: '新增同级类别', value: 'createPeerCategory' },
                            { text: '新增下级类别', value: 'createJuniorCategory' },
                            { text: '新增同级产品', value: 'createPeerProduct' },
                            { text: '新增下级产品', value: 'createJuniorProduct' },
                            { text: '删除', value: 'delete' },
                            { text: '修改', value: 'edit' }
                        ]
                    }
                } else if (data.type == "product") {
                    // 产品 -- 如果此产品下没有BOM，显示新增BOM，去创建BOM并关联到产品
                    if (data.has_bom) {
                        return [
                            { text: '新增同级产品', value: 'createPeerProduct' },
                            { text: '删除', value: 'delete' },
                            { text: '修改', value: 'edit' }
                        ]
                    } else {
                        return [
                            { text: '新增同级产品', value: 'createPeerProduct' },
                            { text: '新增BOM', value: 'createBom' },
                            { text: '删除', value: 'delete' },
                            { text: '修改', value: 'edit' }
                        ]
                    }
                } else if (data.type == 'bom') {
                    // bom
                    if (data.bom_type == "bom_root") {
                        // bom不能添加同级bom
                        return [
                            { text: '新增下级BOM', value: 'createJuniorBom' },
                            { text: '删除', value: 'delete' },
                        ]
                    } else {
                        // 子bom可以添加同级bom
                        return [
                            { text: '新增同级BOM', value: 'createPeerBom' },
                            { text: '新增下级BOM', value: 'createJuniorBom' },
                            { text: '删除', value: 'delete' },
                            { text: '修改', value: 'edit' }
                        ]
                    }
                } else {
                    return []
                }
            },
            // 点击菜单项事件
            menuCommandHandle(el, node, data) {
                // 保存当前点击的菜单类型 -- 弹窗操作的需要使用
                this.menuType = el
                if (el == 'createPeerCategory' || el == 'createJuniorCategory') {
                    // 新建类别 -- 同级类别、下级类别
                    this.$prompt('请输入类别名称', '添加类别', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPattern: /^(?!\s+).*(?<!\s)$/,
                        inputErrorMessage: '名称格式不正确',
                        beforeClose: (action, instance, done) => {
                            if (action === 'confirm') {
                                instance.confirmButtonLoading = true
                                instance.confirmButtonText = '保存中...'
                                let config = {
                                    category_name: instance.inputValue,
                                    operate_type: el == 'createPeerCategory' ? '同级' : '下级',
                                    category_id: data.type == 'category' ? data.category_id : data.id
                                }
                                this.createCategoryApi(config).then(result => {
                                    this.$message.success(result.result.message)
                                    instance.confirmButtonLoading = false
                                    instance.confirmButtonText = '确定'
                                    let newCategoryData = {
                                        children: [],
                                        id: `roke.product.category(${result.result.data},)`,
                                        category_id: result.result.data,
                                        name: instance.inputValue,
                                        parent_id: el == 'createPeerCategory' ? data.parent_id : data.id,
                                        type: "category",
                                        leaf: true
                                    }
                                    if (el == 'createPeerCategory') {
                                        // 新增同级类别 -- 向当前node后面添加新增的数据
                                        this.$refs.treeRef.insertAfter(newCategoryData, data)
                                    } else if (el == 'createJuniorCategory') {
                                        // 新增下级类别
                                        // 增加下级时如果刚开始为空,添加成功后会不显示展开icon,所以需要手动刷新
                                        node.isLeafByUser = false
                                        this.$refs.treeRef.append(newCategoryData, data)
                                    }
                                    done()
                                }).catch(error => {
                                    instance.confirmButtonLoading = false
                                    instance.confirmButtonText = '确定'
                                    this.errorHandle(error)
                                })
                            } else {
                                done()
                            }
                        }
                    }).then(({ value }) => { }).catch(() => { })
                } else if (el == 'createPeerProduct' || el == 'createJuniorProduct') {
                    // 新建产品 -- 打开创建产品弹窗
                    this.createProductDialogShow = true
                } else if (el == 'createBom' || el == 'createPeerBom' || el == 'createJuniorBom') {
                    // 新建Bom -- 新建Bom、新建同级Bom、新建下级Bom
                    this.createBomDialogShow = true
                } else if (el == 'delete') {
                    // 删除
                    let deleteFun = (url, config) => {
                        this.loading = true
                        this.deleteApi(url, config).then(result => {
                            this.loading = false
                            this.$message.success(result.result.message)
                            // 在删除之前获取到上级节点
                            let parentNode = this.$refs.treeRef.getNode(node.parent.data.id)
                            this.$refs.treeRef.remove(data)
                            if (data.bom_type == 'bom_root') {
                                // 删除BOM时如果该产品下没有BOM了，需要把产品的状态改成可以添加BOM
                                if (parentNode?.childNodes?.length == 0) {
                                    parentNode.data.has_bom = false
                                }
                            }
                        }).catch(error => {
                            this.errorHandle(error, true)
                        })
                    }
                    let hintText = '是否删除此信息？'
                    let config = {}
                    let url = ''
                    if (data.type == 'category') {
                        // 删除类别
                        url = "/roke/dws/pd/delete/category"
                        config = { category_id: data.category_id }
                        if (data?.children?.length > 0) {
                            hintText = '有关联下级信息，是否一起删除?'
                        }
                    } else if (data.type == 'product') {
                        // 删除产品
                        url = "/roke/dws/pd/delete/product/product"
                        config = { product_id: data.id }
                        if (data?.has_bom) {
                            hintText = '有关联下级信息，是否一起删除?'
                        }
                    } else if (data.type == 'bom') {
                        // 删除bom
                        url = "/roke/dws/pd/bom/unlink"
                        config = {
                            bom_type: data.bom_type
                        }
                        // bom_root 时传递 bom_id, 其他传递 bom_line_id
                        if (data.bom_type == 'bom_root') {
                            config['bom_id'] = data.bom_id
                        } else {
                            config['bom_line_id'] = data.bom_line_id
                        }
                        if (data?.has_children) {
                            hintText = '有关联下级信息，是否一起删除?'
                        }
                    }
                    this.$confirm(hintText, '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        deleteFun(url, config)
                    }).catch(() => { })
                } else if (el == 'edit') {
                    // 编辑
                    if (data.type == 'category') {
                        // 编辑类别
                        this.$prompt('请输入类别名称', '修改类别', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            inputValue: data.name,
                            inputPattern: /^(?!\s+).*(?<!\s)$/,
                            inputErrorMessage: '名称格式不正确',
                            beforeClose: (action, instance, done) => {
                                if (action === 'confirm') {
                                    instance.confirmButtonLoading = true
                                    instance.confirmButtonText = '保存中...'
                                    let config = {
                                        category_name: instance.inputValue,
                                        category_id: data.category_id
                                    }
                                    this.editCategoryApi(config).then(result => {
                                        this.$message.success(result.result.message)
                                        instance.confirmButtonLoading = false
                                        instance.confirmButtonText = '确定'
                                        node.data.name = instance.inputValue
                                        done()
                                    }).catch(error => {
                                        instance.confirmButtonLoading = false
                                        instance.confirmButtonText = '确定'
                                        this.errorHandle(error)
                                    })
                                } else {
                                    done()
                                }
                            }
                        }).then(({ value }) => { }).catch(() => { })
                    } else if (data.type == 'product') {
                        // 编辑产品
                        this.editProductDialogShow = true
                    } else if (data.type == 'bom') {
                        // 编辑BOM
                        this.$prompt('请输入数量', '修改BOM', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            inputValue: data.quantity,
                            inputType: "number",
                            inputPattern: /^[1-9][0-9]*$/,
                            inputErrorMessage: '数量必须为大于0的整数',
                            beforeClose: (action, instance, done) => {
                                if (action === 'confirm') {
                                    instance.confirmButtonLoading = true
                                    instance.confirmButtonText = '保存中...'
                                    let config = {
                                        bom_type: data.bom_type,
                                        quantity: +instance.inputValue,
                                        bom_line_id: data.bom_line_id
                                    }
                                    this.editBomApi(config).then(result => {
                                        this.$message.success(result.result.message)
                                        instance.confirmButtonLoading = false
                                        instance.confirmButtonText = '确定'
                                        node.data.quantity = instance.inputValue
                                        done()
                                    }).catch(error => {
                                        instance.confirmButtonLoading = false
                                        instance.confirmButtonText = '确定'
                                        this.errorHandle(error)
                                    })
                                } else {
                                    done()
                                }
                            }
                        }).then(({ value }) => { }).catch(() => { })
                    }
                }
            },
            // 处理字段展示方法
            infoHandle(field) {
                let infoHandle = (value) => {
                    return value ? value : '--'
                }
                if (this.treeActiveData?.type) {
                    if (field == '编号') {
                        return this.treeActiveData.type != 'category' ? infoHandle(this.treeActiveData.code) : '--'
                    } else if (field == '名称') {
                        return this.treeActiveData.type != 'category' ? infoHandle(this.treeActiveData.name) : '--'
                    } else if (field == '类别') {
                        return this.treeActiveData.type != 'category' ? infoHandle(this.treeActiveData?.category?.name) : '--'
                    } else if (field == '规格型号') {
                        return this.treeActiveData.type != 'category' ? infoHandle(this.treeActiveData.specification) : '--'
                    } else if (field == '数量') {
                        return this.treeActiveData.type == 'bom' ? this.treeActiveData.quantity.toString() : '--'
                    }
                } else {
                    return '--'
                }
            },
            // 工序列表删除icon点击事件
            deleteProcessIconHandle(data, index) {
                this.loading = true
                let config = {
                    line_id: data.line_id,
                    routing_id: this.routingData.routing_id
                }
                this.deleteProcessApi(config).then(result => {
                    this.productProcessList.splice(index, 1)
                    this.$message.success(result.result.message)
                    let processConfig = {
                        routing_id: this.routingData.routing_id
                    }
                    this.getProductProcessListApi(processConfig).then(result => {
                        this.loading = false
                        result.result.data.forEach(item => {
                            item['show'] = false
                        })
                        this.productProcessList = result.result.data
                    }).catch(error => {
                        this.loading = false
                    })
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 弹窗关闭事件
            dialogCloseHandle(type) {
                if (type == '新增产品') {
                    this.createProductDialogShow = false
                    this.createProductData = {}
                    if (this.isNoBom) {
                        this.isNoBom = false
                        this.searchProductValue = ''
                        this.currentRow = null
                        this.$refs.singleTable.clearSelection()
                    }
                } else if (type == '编辑产品') {
                    this.editProductDialogShow = false
                    this.categoryList = []
                    this.editProductData = {}
                } else if (type == '新增BOM') {
                    this.createBomDialogShow = false
                    this.createProductDialogShow = false
                    this.createProductData = {}
                    this.isNoBom = false
                    this.searchProductValue = ''
                    this.currentRow = null
                    this.$refs.singleTable.clearSelection()
                } else if (type == '添加产品工序') {
                    this.addProcessDialogShow = false
                    this.searchProcessValue = ''
                    this.multipleSelection = []
                } else if (type == '添加工艺参数') {
                    this.standardsDialogShow = false
                    this.standardsForm = {}
                } else if (type == '添加设备检查项') {
                    this.checksDialogShow = false
                    this.checksForm = {
                        input_type: '',
                        name: '',
                        description: '',
                        standard_value: '',
                        select_item_ids: [],
                        lower_value: 0,
                        upper_value: 0
                    }
                    this.isEdit = false
                } else if (type == '选择不良类型') {
                    this.selBadnessTypeDialogShow = false
                    this.badnessTypeList = []
                    this.selBadnessTypeData = []
                }
            },
            // 弹窗保存事件
            dialogSaveHandle(type) {
                if (type == '新增产品') {
                    // 新增无BOM产品 新增同级产品 新增下级产品
                    if (this.isNoBom) {
                        // 新增无BOM产品
                        this.loading = true
                        this.createNoBomProductApi(this.createProductData).then(result => {
                            this.createBomFn(result.result.data)
                        }).catch(error => {
                            this.errorHandle(error, true)
                        })
                    } else if (this.treeData.type == 'category') {
                        // 类别
                        let config = {
                            category_id: this.treeData.id,
                            operate_type: this.menuType == 'createPeerProduct' ? '同级' : '下级',
                            product_name: this.createProductData.product_name,
                            specification: this.createProductData.specification
                        }
                        this.categoryCreateProductApi(config).then(result => {
                            this.dialogCloseHandle(type)
                            result.result.data['leaf'] = true
                            this.$message.success(result.result.message)
                            if (this.menuType == 'createPeerProduct') {
                                // 新增同级产品 -- 向当前node后面添加新增的数据
                                this.$refs.treeRef.insertAfter(result.result.data, this.treeData)
                            } else if (this.menuType == 'createJuniorProduct') {
                                // 新增下级产品
                                // 增加下级时如果刚开始为空,添加成功后会不显示展开icon,所以需要手动刷新
                                this.treeNode.isLeafByUser = false
                                this.$refs.treeRef.append(result.result.data, this.treeData)
                            }
                        }).catch(error => {
                            this.errorHandle(error)
                        })
                    } else {
                        // 产品
                        let config = {
                            product_id: this.treeData.id,
                            product_name: this.createProductData.product_name,
                            specification: this.createProductData.specification
                        }
                        this.productCreateProductApi(config).then(result => {
                            this.dialogCloseHandle(type)
                            result.result.data['leaf'] = true
                            this.$message.success(result.result.message)
                            if (this.menuType == 'createPeerProduct') {
                                // 新增同级产品 -- 向当前node后面添加新增的数据
                                this.$refs.treeRef.insertAfter(result.result.data, this.treeData)
                            } else if (this.menuType == 'createJuniorProduct') {
                                // 新增下级产品
                                // 增加下级时如果刚开始为空,添加成功后会不显示展开icon,所以需要手动刷新
                                this.treeNode.isLeafByUser = false
                                this.$refs.treeRef.append(result.result.data, this.treeData)
                            }
                        }).catch(error => {
                            this.errorHandle(error)
                        })
                    }
                } else if (type == '编辑产品') {
                    this.loading = true
                    this.editProductInfoApi(this.editProductData).then(result => {
                        this.loading = false
                        this.$message.success(result.result.message)
                        if (this.editProductData.category_id == this.treeData.category.id) {
                            // 新类别id相同和原来的id相同,表示没有换上级,直接修改信息
                            this.treeNode.data.name = this.editProductData.product_name
                            this.treeNode.data.specification = this.editProductData.specification
                            this.treeActiveData = this.treeNode.data
                            if (this.treeNode.data.id == this.treeActiveData.id) {
                                this.productionInfoData.product_name = this.editProductData.product_name
                                this.productionInfoData.specification = this.editProductData.specification
                                this.productionInfoData.capacity = this.treeNode.data.capacity
                                this.productionInfoData.fpy = this.treeNode.data.fpy
                                this.isEdit = false
                            }
                            this.dialogCloseHandle(type)
                        } else {
                            // 新类别id相同和原来的id不相同,表示更换了上级,需要找到上级的位置去插入,并把当前位置的删除
                            // 自行处理修改后的数据,然后插入到指定位置
                            let newData = JSON.parse(JSON.stringify(this.treeData))
                            newData.id = this.editProductData.product_id
                            newData.name = this.editProductData.product_name
                            newData.specification = this.editProductData.specification
                            newData.category.id = this.editProductData.category_id
                            newData.leaf = newData.has_bom ? false : true
                            this.categoryList.forEach(item => {
                                if (item.id == this.editProductData.category_id) {
                                    newData.category.name = item.name
                                }
                            })
                            // 把当前位置的删除
                            this.$refs.treeRef.remove(this.treeNode)
                            // 通过树形组件的key值去获取要插入的位置的node
                            let newNode = this.$refs.treeRef.getNode(newData.category.id)
                            // 通过树形组件的node数据把数据插入到新的位置
                            this.$refs.treeRef.append(newData, newNode)
                            // 设置新node的展开折叠icon展示,防止新位置原本没有数据不显示展开折叠icon
                            newNode.isLeafByUser = false
                            this.dialogCloseHandle(type)
                            this.treeActiveData = {}
                            this.routingData = {}
                            this.productProcessList = []
                            this.productionInfoData = {}
                        }
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '新增BOM') {
                    this.createBomFn()
                } else if (type == '添加产品工序') {
                    let oldList = []
                    let newList = []
                    this.productProcessList.forEach(item => {
                        let obj = {
                            type: "update",
                            process_id: item.process_id,
                            line_id: item.line_id
                        }
                        oldList.push(obj)
                    })
                    this.multipleSelection.forEach(item => {
                        let obj = {
                            type: "add",
                            process_id: item
                        }
                        newList.push(obj)
                    })
                    let lines = [...oldList, ...newList]
                    lines.forEach((item, index) => {
                        item['sequence'] = index + 1
                    })
                    let config = {
                        product_id: this.treeActiveData.type == "product" ? this.treeActiveData.id : this.treeActiveData.product_id,
                        lines: lines
                    }
                    this.loading = true
                    this.routingSaveProcessApi(config).then(result => {
                        if (!this.routingData.routing_id) {
                            // 当前产品没有工艺路线在保存工序时后端会自动创建，前端需要重新拿一下当前产品的工艺路线信息
                            let routingConfig = {
                                product_id: this.treeActiveData.type == "product" ? this.treeActiveData.id : this.treeActiveData.product_id,
                                routing_state: "确认"
                            }
                            this.getRoutingInfoApi(routingConfig).then(res => {
                                if (res.result.data.length > 0) {
                                    this.routingData = res.result.data[0]
                                } else {
                                    this.routingData = {}
                                }
                            }).catch(err => { })
                        }
                        let processConfig = {
                            routing_id: result.result.data.routing_id
                        }
                        this.getProductProcessListApi(processConfig).then(res => {
                            res.result.data.forEach(item => {
                                item['show'] = false
                            })
                            this.productProcessList = res.result.data
                            this.$message.success(result.result.message)
                            this.loading = false
                            this.dialogCloseHandle('添加产品工序')
                        }).catch(err => {
                            this.errorHandle('添加成功，但数据刷新失败，请手动刷新当前页面！', true)
                        })
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '添加工艺参数') {
                    this.$refs.standardsFormRef.validate((valid) => {
                        if (valid) {
                            let config = {
                                routing_line_id: this.selProductProcessData.line_id,
                                standard_title: this.standardsForm.standard_title,
                                standard_content: this.standardsForm.standard_content,
                                standard_description: this.standardsForm.standard_description,
                            }
                            this.loading = true
                            this.requestApi("/roke/workstation/standard/add", config).then(result => {
                                this.loading = false
                                this.$message.success(result.result.message)
                                this.productProcessList.forEach(item => {
                                    if (item.show) {
                                        item.standards = result.result.data
                                    }
                                })
                                this.dialogCloseHandle('添加工艺参数')
                            }).catch(error => {
                                this.errorHandle(error, true)
                            })
                        } else {
                            return false
                        }
                    })
                } else if (type == '添加设备检查项') {
                    this.$refs.checksFormRef.validate((valid) => {
                        if (valid) {
                            let checkData = JSON.parse(JSON.stringify(this.checksForm))
                            let config = {}
                            let url = ""
                            if (this.isEdit) {
                                // 编辑
                                url = "/roke/dws/pd/checks/update"
                                config = {
                                    check_id: checkData.id,
                                    check_info: {
                                        input_type: checkData.input_type,
                                        name: checkData.name,
                                        sequence: checkData.sequence,
                                        description: checkData.description,
                                        standard_value: '',
                                        select_item_ids: [],
                                        lower_value: 0,
                                        upper_value: 0
                                    }
                                }
                                if (checkData.input_type == "select") {
                                    // 选择模式
                                    config.check_info.select_item_ids = checkData.select_item_ids
                                    config.check_info.standard_value = checkData.standard_value
                                } else if (checkData.input_type == "text" || checkData.input_type == "boolean") {
                                    // 文本模式和布尔模式
                                    config.check_info.standard_value = checkData.standard_value
                                } else if (checkData.input_type == "float" || checkData.input_type == "int") {
                                    // 小数模式和整数模式
                                    config.check_info.lower_value = checkData.lower_value
                                    config.check_info.upper_value = checkData.upper_value
                                }
                            } else {
                                // 添加
                                url = "/roke/workstation/checks/add"
                                let obj = {
                                    input_type: checkData.input_type,
                                    name: checkData.name,
                                    description: checkData.description,
                                    standard_value: '',
                                    select_item_ids: []
                                }
                                if (checkData.input_type == "select") {
                                    // 选择模式
                                    obj.select_item_ids = checkData.select_item_ids
                                    obj.standard_value = checkData.standard_value
                                } else if (checkData.input_type == "text" || checkData.input_type == "boolean") {
                                    // 文本模式和布尔模式
                                    obj.standard_value = checkData.standard_value
                                } else if (checkData.input_type == "float" || checkData.input_type == "int") {
                                    // 小数模式和整数模式
                                    obj.lower_value = checkData.lower_value
                                    obj.upper_value = checkData.upper_value
                                }
                                let list = []
                                checkData.select_item_ids.forEach(item => {
                                    list.push(item.value)
                                })
                                obj.select_item_ids = list
                                config = {
                                    routing_line_id: this.selProductProcessData.line_id,
                                    checks: [obj]
                                }
                            }
                            this.loading = true
                            this.requestApi(url, config).then(result => {
                                this.loading = false
                                this.$message.success(result.result.message)
                                if (this.isEdit) {
                                    // 编辑
                                    let newList = []
                                    this.productProcessList.forEach(item => {
                                        if (item.show) {
                                            newList = JSON.parse(JSON.stringify(item.checks))
                                        }
                                    })
                                    let newIndex = ''
                                    newList.forEach((items, indexs) => {
                                        if (items.id == checkData.id) {
                                            newIndex = indexs
                                        }
                                    })
                                    let newData = JSON.parse(JSON.stringify(config.check_info))
                                    newData['id'] = checkData.id
                                    newList[newIndex] = newData
                                    this.productProcessList.forEach(item => {
                                        if (item.show) {
                                            item.checks = JSON.parse(JSON.stringify(newList))
                                        }
                                    })
                                } else {
                                    // 新增
                                    this.productProcessList.forEach(item => {
                                        if (item.show) {
                                            item.checks = result.result.data
                                        }
                                    })
                                }
                                this.dialogCloseHandle('添加设备检查项')
                            }).catch(error => {
                                this.errorHandle(error, true)
                            })
                        } else {
                            return false
                        }
                    })
                } else if (type == '选择不良类型') {
                    let config = {
                        routing_line_id: this.selProductProcessData.line_id,
                        scrap_reason_ids: this.selBadnessTypeData
                    }
                    this.loading = true
                    this.requestApi('/roke/workstation/routing/scrap_reason/add', config).then(data => {
                        this.loading = false
                        this.$message.success(data.result.message)
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                item.scrap_reasons = data.result.data
                            }
                        })
                        this.dialogCloseHandle('选择不良类型')
                    }).catch(error => {

                    })
                }
            },
            // 创建BOM处理方法
            createBomFn(data = null) {
                let url = null
                let config = {}
                if (this.menuType == 'createBom') {
                    // 产品创建BOM
                    config = {
                        product_id: this.treeData.id,
                        bom_info: [{
                            product_id: data ? data.id : this.currentRow.id,
                            qty: data ? this.createProductData.qty : this.currentRow.qty
                        }]
                    }
                    url = "/roke/dws/pd/create/product/bom"
                } else if (this.menuType == 'createPeerBom') {
                    // 子BOM创建同级BOM
                    config = {
                        bom_line_id: this.treeData.bom_line_id,
                        product_id: data ? data.id : this.currentRow.id,
                        qty: data ? this.createProductData.qty : this.currentRow.qty,
                    }
                    url = "/roke/dws/pd/add/bomline"
                } else if (this.menuType == 'createJuniorBom') {
                    // 创建下级BOM
                    config = {
                        bom_type: this.treeData.bom_type,
                        bom_info: [{
                            product_id: data ? data.id : this.currentRow.id,
                            qty: data ? this.createProductData.qty : this.currentRow.qty,
                        }]
                    }
                    if (this.treeData.bom_type == 'bom_root') {
                        // BOM创建下级子BOM
                        config['bom_id'] = this.treeData.bom_id
                    } else {
                        // 子BOM创建下级BOM
                        config['bom_line_id'] = this.treeData.bom_line_id
                    }
                    url = "/roke/dws/pd/create/subordinate"
                }
                this.loading = true
                this.createBomApi(url, config).then(data => {
                    if (this.menuType == 'createBom') {
                        // 产品创建BOM -- 重新获取数据,然后添加到树形组件内
                        this.getBomsListApi({ product_id: this.treeData.id }).then(result => {
                            // 通过树形组件的key值去获取要插入的位置的node
                            let newNode = this.$refs.treeRef.getNode(this.treeData.id)
                            if (result?.result?.data?.length > 0) {
                                newNode.data.has_bom = true
                            }
                            result.result.data.forEach(item => {
                                // 通过树形组件的node数据把数据插入到新的位置
                                this.$refs.treeRef.append(item, newNode)
                            })
                            // 设置新node的展开折叠icon展示,防止新位置原本没有数据不显示展开折叠icon
                            newNode.isLeaf = false
                            this.$message.success('添加成功!')
                            this.loading = false
                            this.dialogCloseHandle('新增BOM')
                        }).catch(error => {
                            this.errorHandle('刷新失败!', true)
                        })
                    } else if (this.menuType == 'createPeerBom') {
                        // 子BOM创建同级BOM
                        let parentData = this.treeNode.parent.data
                        this.getSubBomsListApi({ bom_id: parentData.query_id }).then(result => {
                            // 通过树形组件的key值去获取要插入的位置的node
                            let newNode = this.$refs.treeRef.getNode(parentData.id)
                            // 通过树形组件的key数据覆盖掉原来的子级
                            this.$refs.treeRef.updateKeyChildren(newNode.data.id, result.result.data)
                            // 设置新node的展开折叠icon展示,防止新位置原本没有数据不显示展开折叠icon
                            newNode.isLeafByUser = false
                            this.$message.success('添加成功!')
                            this.loading = false
                            this.dialogCloseHandle('新增BOM')
                        }).catch(error => {
                            this.errorHandle('刷新失败!', true)
                        })
                    } else if (this.menuType == 'createJuniorBom') {
                        // 子BOM创建下级BOM、BOM创建下级子BOM -- 重新获取当前BOM下的所有数据,然后重新设置当前BOM的子级
                        this.getSubBomsListApi({
                            bom_id: this.treeData.bom_type != 'bomline' ? this.treeData.query_id : data.result.data.query_id
                        }).then(result => {
                            // 通过树形组件的key值去获取要插入的位置的node
                            let newNode = this.$refs.treeRef.getNode(this.treeData.id)
                            // 通过树形组件的key数据覆盖掉原来的子级
                            this.$refs.treeRef.updateKeyChildren(newNode.data.id, result.result.data)
                            // 设置新node的展开折叠icon展示,防止新位置原本没有数据不显示展开折叠icon
                            newNode.isLeafByUser = false
                            this.$message.success('添加成功!')
                            this.loading = false
                            this.dialogCloseHandle('新增BOM')
                        }).catch(error => {
                            this.errorHandle('刷新失败!', true)
                        })
                    }
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 创建产品弹窗打开事件
            createProductDialogOpen() {
                if (!this.isNoBom) return
                this.loading = true
                this.getCategoryListApi().then(result => {
                    this.loading = false
                    this.categoryList = result.result.data
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 修改产品弹窗打开事件
            editProductDialogOpen() {
                this.loading = true
                this.editProductData = {
                    category_id: this.treeData.category.id,
                    product_id: this.treeData.id,
                    product_name: this.treeData.name,
                    specification: this.treeData.specification
                }
                this.getCategoryListApi().then(result => {
                    this.loading = false
                    this.categoryList = result.result.data
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 创建BOM弹窗打开事件
            createBomDialogOpen() {
                this.loading = true
                this.getProductListApi().then(result => {
                    this.loading = false
                    this.productList = result.result.data
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 创建BOM弹窗产品搜索input框的change事件
            searchProductChange() {
                this.loading = true
                this.getProductListApi().then(result => {
                    this.loading = false
                    this.productList = result.result.data
                    if (this.currentRow) {
                        this.$refs.singleTable.setCurrentRow(this.currentRow)
                    }
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 创建BOM弹窗表格单选事件
            handleCurrentChange(val) {
                this.currentRow = val
            },
            // 创建BOM弹窗下方创建产品按钮点击事件
            createProductBtnHandle() {
                this.isNoBom = true
                this.createProductDialogShow = true
            },
            // 添加工序弹窗打开事件
            addProcessDialogOpen() {
                this.loading = true
                this.getProcessListApi().then(result => {
                    this.processList = result.result.data
                    this.loading = false
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 添加工序弹窗工序搜索input框的change事件
            searchProcessChange() {
                this.loading = true
                this.getProcessListApi().then(result => {
                    this.processList = result.result.data
                    this.loading = false
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 添加工序弹窗表格选中事件
            handleSelectionChange(val) {
                this.multipleSelection = val
            },
            // 选中工序事件
            selProcessHandle(item) {
                this.isEdit = false
                if (item.show) {
                    item.show = false
                } else {
                    this.productProcessList.forEach(items => {
                        items.show = false
                    })
                    item.show = true
                }
            },
            // 工序列表排序结束事件
            processSortEndHandle() {
                let line_ids = []
                this.productProcessList.forEach(item => {
                    line_ids.push(item.line_id)
                })
                let config = {
                    routing_id: this.routingData.routing_id,
                    line_ids: line_ids
                }
                this.loading = true
                this.routingInfoSortSaveApi(config).then(data => {
                    this.loading = false
                    this.$message.success(data.result.message)
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 菜单激活事件
            menuSelectHandle(index) {
                this.activeIndex = index
                this.isEdit = false
            },
            // 添加按钮点击事件
            addInfoHandle(type) {
                if (type == '工艺参数') {
                    this.standardsDialogShow = true
                } else if (type == '设备检查项') {
                    this.checksDialogShow = true
                }
            },
            // 内容信息区域表格删除事件
            deleteInfoHandle(type, row) {
                if (type == '工艺参数') {
                    this.loading = true
                    this.requestApi('/roke/workstation/standard/delete', { standard_id: row.id }).then(result => {
                        this.loading = false
                        this.$message.success(result.result.message)
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                let delIndex = item.standards.findIndex(it => it.id == row.id)
                                if (delIndex != -1) {
                                    item.standards.splice(delIndex, 1)
                                }
                            }
                        })
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '工艺文件') {
                    this.loading = true
                    let config = {
                        routing_line_id: this.selProductProcessData.line_id,
                        document_id: row.id
                    }
                    this.requestApi('/roke/workstation/guidance/unlink', config).then(result => {
                        this.loading = false
                        this.$message.success(result.result.message)
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                let delIndex = item.documents.findIndex(it => it.id == row.id)
                                if (delIndex != -1) {
                                    item.documents.splice(delIndex, 1)
                                }
                            }
                        })
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '设备检查项') {
                    this.loading = true
                    let config = {
                        routing_line_id: this.selProductProcessData.line_id,
                        check_id: row.id
                    }
                    this.requestApi('/roke/workstation/check/unlink', config).then(result => {
                        this.loading = false
                        this.$message.success(result.result.message)
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                let delIndex = item.checks.findIndex(it => it.id == row.id)
                                if (delIndex != -1) {
                                    item.checks.splice(delIndex, 1)
                                }
                            }
                        })
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '不良类型') {
                    this.loading = true
                    let config = {
                        routing_line_id: this.selProductProcessData.line_id,
                        scrap_reason_id: row.id
                    }
                    this.requestApi('/roke/workstation/routing/scrap_reason/unlink', config).then(result => {
                        this.loading = false
                        this.$message.success(result.result.message)
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                let delIndex = item.scrap_reasons.findIndex(it => it.id == row.id)
                                if (delIndex != -1) {
                                    item.scrap_reasons.splice(delIndex, 1)
                                }
                            }
                        })
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                }
            },
            // 内容信息区域编辑、取消编辑按钮点击事件
            editInfoHandle(type, data) {
                this.isEdit = !this.isEdit
                if (type == '工艺参数') {
                    if (this.isEdit) {
                        this.standardsCopyList = JSON.parse(JSON.stringify(this.standardsList))
                    }
                } else if (type == '设备检查项') {
                    this.checksForm = JSON.parse(JSON.stringify(data))
                    this.checksDialogShow = true
                } else if (type == '生产信息') {
                    if (!this.isEdit) {
                        this.productionInfoData.capacity = this.treeActiveData.capacity
                        this.productionInfoData.fpy = this.treeActiveData.fpy
                    }
                }
            },
            // 内容信息区域表格保存事件
            saveInfoHandle(type) {
                if (type == '工艺参数') {
                    let standard_info = []
                    this.standardsCopyList.forEach(item => {
                        let obj = JSON.parse(JSON.stringify(item))
                        delete obj.sequence
                        standard_info.push(obj)
                    })
                    this.loading = true
                    this.requestApi('/roke/dws/pd/standard/update', { standard_info }).then(result => {
                        this.loading = false
                        this.productProcessList.forEach(item => {
                            if (item.show) {
                                item.standards = JSON.parse(JSON.stringify(this.standardsCopyList))
                            }
                        })
                        this.$message.success(result.result.message)
                        this.isEdit = false
                        this.standardsCopyList = []
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                } else if (type == '生产信息') {
                    this.loading = true
                    this.editProductInfoApi(this.productionInfoData).then(result => {
                        let activeNode = this.$refs.treeRef.getNode(this.productionInfoData.product_id)
                        activeNode.data.capacity = this.productionInfoData.capacity
                        activeNode.data.fpy = this.productionInfoData.fpy
                        this.isEdit = false
                        this.loading = false
                        this.$message.success(result.result.message)
                    }).catch(error => {
                        this.errorHandle(error, true)
                    })
                }
            },
            // 上传附件按钮点击事件
            uploadChange(file) {
                let formData = new FormData()
                formData.append('files', file.raw) // 这就是文件流
                formData.append('routing_line_id', this.selProductProcessData.line_id) // 这就是文件流
                formData.append('document_name', file.name) // 这就是文件流
                this.loading = true
                this.requestApi('/roke/workstation/guidance/add', formData, '上传失败,稍后再试', 'multipart/form-data').then(result => {
                    this.productProcessList.forEach(item => {
                        if (item.show) {
                            item.documents = result.data
                        }
                    })
                    this.loading = false
                    this.$message.success(result.message)
                }).catch(error => {
                    this.errorHandle(error, true)
                })
            },
            // 不良类型标签页上方按钮点击事件
            badnessTypeBtnHandle(type) {
                if (type == 'selected') {
                    // 选择不良类型
                    this.selBadnessTypeDialogShow = true
                } else {
                    this.$prompt('请输入不良类型名称', '选择不良类型', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputValue: '',
                        inputValidator: (value) => {
                            if (!value || value.length == 0) {
                                return false
                            }
                        },
                        inputErrorMessage: '当前字段不可为空',
                        beforeClose: (action, instance, done) => {
                            if (action === 'confirm') {
                                instance.confirmButtonLoading = true
                                instance.confirmButtonText = '保存中...'
                                let config = {
                                    routing_line_id: this.selProductProcessData.line_id,
                                    scrap_reason_name: instance.inputValue
                                }
                                this.requestApi("/roke/workstation/scrap_reason/add", config).then(result => {
                                    this.productProcessList.forEach(item => {
                                        if (item.show) {
                                            item.scrap_reasons = result.result.data
                                        }
                                    })
                                    this.$message.success(result.result.message)
                                    instance.confirmButtonLoading = false
                                    instance.confirmButtonText = '确定'
                                    done()
                                }).catch(error => {
                                    instance.confirmButtonLoading = false
                                    instance.confirmButtonText = '确定'
                                    this.errorHandle(error)
                                })
                            } else {
                                done()
                            }
                        }
                    }).then(({ value }) => { }).catch(() => { })
                }
            },
            // 添加设备检查项弹窗选择类型下拉框change事件
            selectTyleChange(e) {
                this.checksForm.standard_value = ''
                this.checksForm.select_item_ids = []
                this.checksForm.lower_value = 0
                this.checksForm.upper_value = 0
            },
            // 选择不良类型弹窗打开事件
            selBadnessTypeDialogOpen() {
                let ids = []
                this.scrapReasonsList.forEach(item => {
                    ids.push(item.id)
                })
                let config = {
                    fields: ["id", "name"],
                    model: "roke.scrap.reason",
                    domain: [['id', "not in", ids]],
                }
                this.requestApi('/roke/workstation/search_read', config, '获取不良类型列表失败!').then(data => {
                    this.badnessTypeList = data.result.data
                }).catch(error => {
                    this.errorHandle(error)
                })
            },
            // 处理设备检查项类型展示问题
            typeHandle(type) {
                let name = ''
                this.equipmentInspectionTypeList.forEach(item => {
                    if (item.value == type) {
                        name = item.name
                    }
                })
                return name
            },
            // 获取工艺文件类型方法
            getFileTypeHandle(type, isCapital) {
                const mimeTypes = {
                    // 图片
                    'image/jpeg': 'jpeg',
                    'image/png': 'png',
                    'image/gif': 'gif',
                    'image/bmp': 'bmp',
                    'image/webp': 'webp',
                    'image/svg+xml': 'svg',
                    // 文档
                    'application/pdf': 'pdf',
                    'application/msword': 'doc',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
                    'application/vnd.ms-excel': 'xls',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
                    'application/vnd.ms-powerpoint': 'ppt',
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
                    'text/plain': 'txt',
                    'text/html': 'html',
                    // 音频
                    'audio/mpeg': 'mpeg',
                    'audio/wav': 'wav',
                    'audio/ogg': 'ogg',
                    'audio/mp4': 'mp4',
                    // 视频
                    'video/mp4': 'mp4',
                    'video/x-msvideo': 'avi',
                    'video/x-matroska': 'mkv',
                    'video/webm': 'webm',
                    // 压缩文件
                    'application/zip': 'zip',
                    'application/x-rar-compressed': 'rar',
                    'application/gzip': 'gz',
                    'application/x-7z-compressed': '7z',
                    // cad文件
                    'application/acad': 'dwg',
                    'application/dxf': 'dxf',
                    'application/acad': 'step',
                    'application/acad': 'sldprt',
                    'application/acad': 'sldasm',
                    // 其他
                    'application/octet-stream': '二进制',
                    'application/json': 'json',
                    'application/xml': 'xml',
                }
                if (isCapital) {
                    return ((mimeTypes[type] + ' 文件') || '其它文件').toUpperCase()
                } else {
                    return ((mimeTypes[type] + ' 文件') || '其它文件')
                }

            },
            // 获取文件名后缀
            getFileSuffixHandle(name, isCapital) {
                if (name) {
                    name.slice('.')[1]
                    if (isCapital) {
                        return ((name.slice('.')[1] + ' 文件') || '其它文件').toUpperCase()
                    } else {
                        return ((name.slice('.')[1] + ' 文件') || '其它文件')
                    }
                } else {
                    return '未知文件'
                }
            },
            // 文件预览方法
            filePreviewHandle(data) {
                // 调用oodo的预览方法
                window.parent.postMessage({ method: "XFilePreview", doc_id: data.id })
            },
            // 处理时间方法
            dateTimeHandle(dataTime) {
                if (dataTime) {
                    let indexOfColonBeforeSeconds = dataTime.lastIndexOf(':')
                    let result = dataTime.slice(0, indexOfColonBeforeSeconds)
                    return result
                } else {
                    return dataTime
                }

            },
        },
    })
</script>

<style lang="scss" scoped>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    .el-dialog {
        margin-top: 10vh !important;
    }

    .el-dialog__body {
        padding: 10px 20px;
    }

    #app {
        padding: 8px;
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;

        .leftBox {
            display: flex;
            flex-direction: column;
            width: 20%;
            height: 100%;
            align-items: center;
            background-color: #fff;
            box-shadow: 0px 0px 8px 4px rgba(0, 0, 0, 0.05);
            border-radius: 4px;
            border: 1px solid #CDCDCD;
            overflow: hidden;

            .searchBox {
                width: 100%;
                display: flex;
                align-items: center;
                padding: 10px;

                .unfoldBox {
                    margin-left: 8px;
                    cursor: pointer;
                }
            }

            .treeBox {
                flex: auto;
                height: 1px;
                overflow: auto;
                width: 100%;

                .treeComBox {
                    min-width: 100%;
                    display: inline-block;

                    .custom-tree-node {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        padding-right: 5px;
                        justify-content: space-between;

                        .textBox {
                            padding-right: 10px;
                        }

                        .iconBox {
                            padding-right: 10px;

                            .iconClass {
                                font-size: 18px;
                                padding-left: 5px;
                            }
                        }
                    }
                }
            }
        }

        .rightBox {
            display: flex;
            flex-direction: column;
            height: 100%;
            flex: 1;
            margin-left: 8px;

            .rightTopBox {
                padding: 10px;
                background-color: #fff;
                box-shadow: 0px 0px 8px 4px rgba(0, 0, 0, 0.05);
                border-radius: 4px;
                border: 1px solid #CDCDCD;
                overflow: hidden;
                display: flex;
                align-items: center;
                line-height: 30px;

                .infoBox {
                    display: flex;
                    align-items: center;
                    width: 20%;
                    margin-left: 8px;

                    label {
                        white-space: nowrap;
                    }

                    span {
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                }

                .infoBox:first-child {
                    margin-left: 0px;
                }
            }

            .rightBottomBox {
                flex: auto;
                display: flex;
                align-items: center;
                margin-top: 8px;

                .rightBottomLeftBox {
                    width: 23%;
                    height: 100%;
                    background-color: #fff;
                    box-shadow: 0px 0px 8px 4px rgba(0, 0, 0, 0.05);
                    border-radius: 4px;
                    border: 1px solid #CDCDCD;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;

                    .title {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        border-bottom: 1px solid rgba(151, 151, 151, 0.3);
                        padding: 10px;
                        height: 60px;
                        line-height: 60px;
                    }

                    .processBox {
                        flex: auto;
                        height: 1px;
                        overflow-y: scroll;

                        .processItemBox {
                            width: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding: 0 5px 0 15px;
                            border-bottom: 1px solid #D9D9D9;
                            cursor: pointer;

                            .el-icon-delete {
                                padding: 10px;
                                line-height: 30px;
                            }
                        }

                        .processItemBox:hover {
                            background-color: #f5f7fa;
                        }

                        .processActive {
                            background-color: #e7f1ff;
                            color: rgb(23, 118, 255);
                        }
                    }
                }

                .rightBottomRightBox {
                    margin-left: 8px;
                    flex: 1;
                    height: 100%;
                    background-color: #fff;
                    box-shadow: 0px 0px 8px 4px rgba(0, 0, 0, 0.05);
                    border-radius: 4px;
                    border: 1px solid #CDCDCD;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;

                    .menuContentArea {
                        flex: auto;
                        height: 1px;
                    }
                }
            }
        }

        ::v-deep .el-tree-node__content {
            width: 260px;
        }

        ::v-deep .el-tree-node__content:hover,
        .el-upload-list__item:hover {
            background-color: rgba(23, 118, 255, 0.1);
        }

        .menu {
            width: 15px;
            height: 15px;
        }

        ::v-deep .el-input--suffix {
            width: 100%;
        }

        ::v-deep .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
            background-color: #e7f1ff;
            color: #1776FF;
        }

        /* 点击时背景色 */
        ::v-deep .el-tree-node:focus>.el-tree-node__content {
            background-color: #e7f1ff;
            color: #1776FF;
        }
    }

    .fromBox {
        .el-form-item__content {
            display: flex;
            align-items: center;
        }

        label {
            white-space: nowrap;
        }

        .el-input-number {
            width: 100% !important;
        }
    }

    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    /* 滚动条背景色 */
    ::-webkit-scrollbar-track {
        background-color: #fff;
    }

    /* 滚动条滑块颜色 */
    ::-webkit-scrollbar-thumb {
        background-color: #cce5ff;
        border-radius: 10px;
    }

    /* 滚动条滑块颜色hover效果 */
    :hover::-webkit-scrollbar-thumb {
        background-color: #007bff;
    }

    .is-current>.el-tree-node__content {
        color: #007bff;
    }

    .el-transfer-panel {
        width: calc((60vw - 40px - 190px) / 2);
    }

    .el-transfer__buttons {
        width: 75.6px;
        padding: 0 10px;

        .el-button {
            margin-left: 0;
        }
    }
</style>

</html>