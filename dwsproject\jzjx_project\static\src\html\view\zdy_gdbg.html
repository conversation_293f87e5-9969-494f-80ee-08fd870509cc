<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>工单报工平台</title>
    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/index.css">
    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/main.css">
    <script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/vue.min.js"></script>
    <script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/index.js"></script>
    <script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/axios.min.js"></script>
</head>

<body>
<div id="app">
    <!-- 顶部导航区域 -->
    <div class="topNav">
        <el-row style="height: 100%;line-height: 4rem;">
            <el-col :span="8">
                <div style="padding-left: 2rem;color: #fff;font-weight: bold;">{{currentTime}}&emsp;{{currentSec}}
                </div>
            </el-col>
            <el-col :span="8">
                <div style="color: #fff;font-size: 2rem;font-weight: bold;text-align: center">{{topName}}</div>
            </el-col>
            <el-col :span="8">
                <div style="padding-right: 2rem;color: #fff;font-weight: bold;text-align: center;">
                    <span>当前用户：{{username}}</span>
                    <el-button class="exit" @click="exit"></el-button>
                </div>
            </el-col>
        </el-row>
    </div>
    <!-- 内容区域 -->
    <div class="mainCon" :style="{height:inHeight+'px'}">
        <!-- 内容左侧区域 -->
        <div class="rightCon">
            <!-- 顶部搜索框 -->
            <div class="search">
                <el-input v-model="search" placeholder="请录入编号" @keyup.enter.native="enterCode" ref="autofocus"
                          clearable @input="input_event"></el-input>
            </div>
            <!-- 工单报工表格内容 -->
            <div v-show="!checkFlag">
                <el-table class="gdbgTable" :height="inHeight-90" :data="record_list_data" :key="gd_random_key"
                          v-loading="gd_loading" border style="width: 100%; overflow-y: auto;">
                    <template v-for="(item, index) in record_top_title">
                        <el-table-column :label="item.field_description" :prop="item.field_name"></el-table-column>
                    </template>
                    <el-table-column fixed="right" label="操作" width="150">
                        <template slot-scope="scope" style="background-color: #fff;">
                            <el-button @click.native.prevent="handleCurrentChange(scope.row)" type="primary"
                                       v-if="scope.row.allow" :disabled="!startedFlag" class="tableOperateBtn">
                                报工 / 投料
                            </el-button>
                            <el-button @click.native.prevent="handleCurrentChange(scope.row)" type="primary"
                                       v-if="!scope.row.allow" class="tableOperateBtn">
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @current-change="handleCurrentChangee" :current-page="currentPage" :page-sizes="10"
                               layout="total, prev, pager, next" :total="total">
                </el-pagination>
            </div>
            <!-- 报工记录表格内容 -->
            <div v-show="checkFlag" class="checkList">
                <el-table class="bgjlTable" :height="inHeight-90" :data="jl_record_list_data" :key="gd_random_key"
                          v-loading="gd_loading" border style="width: 100%; overflow-y: auto;">
                    <template v-for="(item, index) in jl_record_top_title">
                        <el-table-column :label="item.field_description" :prop="item.field_name"></el-table-column>
                    </template>
                    <el-table-column fixed="right" label="操作" width="150">
                        <template slot-scope="scope" style="background-color: #fff;">
                            <el-button @click.native.prevent="withdraw(scope.row)" type="danger"
                                       class="tableOperateBtn">
                                撤销
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @current-change="handleSizeChangejl" :current-page="currentPagejl" :page-sizes="10"
                               layout="total, prev, pager, next" :total="total">
                </el-pagination>
            </div>
        </div>
        <!-- 工单报工右侧内容 -->
        <div v-show="!checkFlag" class="leftCon" style="overflow-y: scroll">
            <!-- 右侧上方按钮区域 -->
            <div class="btns">
                <el-button type="primary" class="controlBtn" @click="start" v-if="!startedFlag">开工</el-button>
                <el-button type="danger" class="controlBtn stopT" @click="stop" v-if="startedFlag">停工</el-button>
            </div>
            <!-- 右侧下方内容区域 -->
            <div class="filterCon">
                <el-collapse v-model="activeNames" @change="handleChange">
                    <!-- 筛选条件区域 -->
                    <el-collapse-item title="条件筛选" name="1">
                        <div v-for="item in record_filter_data" style="margin: 0.5rem auto">
                            <span style="display: inline-block;width: 100%">{{item.field_description}}</span>
                            <!-- 单选many2one -->
                            <el-select v-if="item.ttype === 'many2one'" style="width: 100%;" v-model="item.value"
                                       filterable placeholder="请选择" remote
                                       :remote-method="(value) => remoteMethod(value,item)" :loading="loading"
                                       v-loadmore="(value) => zdy_loadMore(value,item)" clearable
                                       @clear="(value) =>reset_vals(value,item)">
                                <el-option v-for="items in item.record_list" :key="items.id" :label="items.name"
                                           :title="items.name" :value="items.id"></el-option>
                            </el-select>

                            <!-- 右侧自定义筛选分类弹窗 -->
                            <!-- <el-button v-if="item.ttype === 'many2one'" type="primary" icon="el-icon-edit"
                                    circle @click="open_dialog(item)"></el-button>
                                <el-dialog v-if="item.ttype === 'many2one'" title="选择项" width="80%"
                                    :visible.sync="choose_zdy_list" :before-close="zdy_dialog_close">
                                    <div style="flex-wrap: wrap;width: 100%;">
                                        <div style="display: flex;padding: 15px;width:96%;flex-wrap: wrap;">
                                            <el-tabs v-model="zdy_activeName" @tab-click="zdy_handleClick"
                                                style="width: 100%">
                                                <el-tab-pane v-for="(itms,index) in zdy_cptablist" :label="itms"
                                                    :name="itms.index"></el-tab-pane>
                                            </el-tabs>
                                            <div class="category" v-show="zdy_child">
                                                <div class="list">
                                                    <div class="type_name" v-for="(itm,index) in zdy_child_category"
                                                        :key="" :style="itm.style"
                                                        @click="sel_zdy_category(itm,index)">
                                                        <span>{{itm.name}}</span>
                                                        <img src="/roke_mes_production/static/src/images/13a73c69-8345-43b4-a96b-42f26de58437.png"
                                                            v-if="itm.child_category.length>0" alt="">
                                                    </div>
                                                </div>
                                                <div class="button" v-if="zdy_child_category.length > 0">
                                                    <el-button type="primary" style="background: #02BA7E;"
                                                        @click="zdy_pre">上一页</el-button>
                                                    <el-button type="primary" style="background: #02BA7E;"
                                                        @click="zdy_next">下一页</el-button>
                                                </div>
                                                <div class="img" @click="close_zdy_type"
                                                    v-if="zdy_child_category.length > 0">
                                                    <span>收起</span>
                                                    <img src="/roke_mes_production/static/src/images/shouqi.png"
                                                        alt="">
                                                </div>
                                            </div>
                                            <div style="width:100%;display: flex;flex-wrap: wrap;justify-content: left;"
                                                v-loading="zdy_load_ing">
                                                <div v-for="(itemm,index) in zdy_List"
                                                    @click="choose_zdy_item(item,itemm)"
                                                    style="cursor:pointer;display: flex;min-width:13%;text-align:center;height: 40px;line-height: 40px;padding: 5px;margin: 8px;border: 1px solid #979797;overflow: hidden;text-overflow: ellipsis">
                                                    {{itemm.name}}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-dialog> -->

                            <!-- 多选 -->
                            <el-select v-if="item.ttype === 'many2many' || item.ttype === 'one2many'"
                                       style="width: 100%;" v-model="item.value" filterable placeholder="请选择" remote
                                       :remote-method="(value) => remoteMethod(value,item)" :loading="loading"
                                       v-loadmore="(value) => zdy_loadMore(value,item)" clearable multiple
                                       @clear="(value) =>reset_vals(value,item)">
                                <el-option v-for="items in item.record_list" :key="items.id" :label="items.name"
                                           :title="items.name" :value="items.id"></el-option>
                            </el-select>
                            <!-- 字符串 -->
                            <el-input v-if="item.ttype === 'char' || item.ttype === 'text'" type="text"
                                      v-model="item.value" placeholder="请录入" style="width: 100%;"></el-input>
                            <!-- 日期 -->
                            <el-date-picker v-if="item.ttype === 'date'" v-model="item.value" type="date"
                                            value-format='yyyy-MM-dd' style="width: 100%;"></el-date-picker>
                            <!-- 精确时间 -->
                            <el-date-picker v-if="item.ttype === 'datetime'" style="width: 100%;"
                                            v-model="item.value" type="datetime"
                                            value-format='yyyy-MM-dd HH:mm:ss'></el-date-picker>
                            <!-- 单选selection -->
                            <el-select v-if="item.ttype === 'selection'" style="width: 100%;" v-model="item.value"
                                       placeholder="请选择" clearable @clear="(value) =>reset_vals(value,item)">
                                <el-option v-for="items in item.select_vals" :key="items.value" :label="items.value"
                                           :title="items.value" :value="items.value"></el-option>
                            </el-select>
                            <!-- 整数 -->
                            <el-input-number v-if="item.ttype === 'integer'" style="width: 100%;"
                                             v-model="item.value" :min="0" :precision="0"></el-input-number>
                            <!-- 浮点 -->
                            <el-input-number v-if="item.ttype === 'float'" style="width: 100%;" v-model="item.value"
                                             :min="0" :precision="2"></el-input-number>
                        </div>
                        <!-- 搜索按钮 -->
<!--                        :disabled="!startedFlag"-->
                        <el-button type="primary" @click="searchFilter" class="searchBtn"
                                   >搜索
                        </el-button>
                    </el-collapse-item>

                    <!-- 更多功能区域 -->
                    <el-collapse-item title="更多功能" name="2">
                        <el-row>
                            <el-col :span="6" style="text-align: center">
                                <button class="bgjl" @click="baogongjilu"></button>
                                <p style="width: 100%;">报工记录</p>
                            </el-col>
                            <el-col :span="6" style="text-align: center">
                                <button class="mrsz" @click="morenshezhi"></button>
                                <p style="width: 100%;">默认设置</p>
                            </el-col>
                            <el-col :span="6" style="text-align: center">
                                <button class="drjx" @click="dangrijixiao"></button>
                                <p style="width: 100%;">当日绩效</p>
                            </el-col>
                            <el-col :span="6" style="text-align: center">
                                <button class="sybz" @click="shiyongbangzhu"></button>
                                <p style="width: 100%;">使用帮助</p>
                            </el-col>
                        </el-row>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </div>
        <!-- 报工记录右侧内容 -->
        <div v-show="checkFlag" class="leftCon" style="overflow-y: scroll">
            <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item title="条件筛选" name="1">
                    <div v-for="item in jl_record_filter_data" style="margin: 0.5rem auto">
                        <span style="display: inline-block;width: 100%">{{item.field_description}}</span>
                        <!-- 单选many2one -->
                        <el-select v-if="item.ttype === 'many2one'" v-model="item.value" style="width: 100%;"
                                   filterable placeholder="请选择" remote
                                   :remote-method="(value) => remoteMethod(value,item)"
                                   :loading="loading" v-loadmore="(value) => zdy_loadMore(value,item)" clearable>
                            <el-option v-for="items in item.record_list" :key="items.id" :label="items.name"
                                       :title="items.name" :value="items.id"></el-option>
                        </el-select>
                        <!-- <el-button v-if="item.ttype === 'many2one'" type="primary" icon="el-icon-edit" circle
                                @click="open_dialog(item)"></el-button> -->
                        <!-- 多选 -->
                        <el-select v-if="item.ttype === 'many2many' || item.ttype === 'one2many'"
                                   style="width: 100%;" v-model="item.value" filterable placeholder="请选择" remote
                                   :remote-method="(value) => remoteMethod(value,item)" :loading="loading"
                                   v-loadmore="(value) => zdy_loadMore(value,item)" clearable multiple>
                            <el-option v-for="items in item.record_list" :key="items.id" :label="items.name"
                                       :title="items.name" :value="items.id"></el-option>
                        </el-select>
                        <!-- 字符串 -->
                        <el-input v-if="item.ttype === 'char' || item.ttype === 'text'" type="text"
                                  style="width: 100%;" v-model="item.value" placeholder="请录入"></el-input>
                        <!-- 日期 -->
                        <el-date-picker v-if="item.ttype === 'date'" v-model="item.value" type="date"
                                        style="width: 100%;" value-format='yyyy-MM-dd'></el-date-picker>
                        <!-- 精确时间 -->
                        <el-date-picker v-if="item.ttype === 'datetime'" v-model="item.value" type="datetime"
                                        style="width: 100%;" value-format='yyyy-MM-dd HH:mm:ss'></el-date-picker>
                        <!-- 单选selection -->
                        <el-select v-if="item.ttype === 'selection'" v-model="item.value" placeholder="请选择"
                                   style="width: 100%;" clearable>
                            <el-option v-for="items in item.select_vals" :key="items.value" :label="items.value"
                                       :title="items.value" :value="items.value"></el-option>
                        </el-select>
                        <!-- 整数 -->
                        <el-input-number v-if="item.ttype === 'integer'" v-model="item.value" :min="0"
                                         style="width: 100%;" :precision="0"></el-input-number>
                        <!-- 浮点 -->
                        <el-input-number v-if="item.ttype === 'float'" v-model="item.value" :min="0"
                                         style="width: 100%;" :precision="2"></el-input-number>
                    </div>
                    <el-button type="primary" @click="searchFilter" class="searchBtn"
                               :disabled="!startedFlag">搜索
                    </el-button>
                </el-collapse-item>
                <el-collapse-item title="更多功能" name="2">
                    <el-row>
                        <el-col :span="6" style="text-align: center">
                            <button class="bgjl" @click="baogongjilu"></button>
                            <p style="width: 100%;">报工记录</p>
                        </el-col>
                        <el-col :span="6" style="text-align: center">
                            <button class="mrsz" @click="morenshezhi"></button>
                            <p style="width: 100%;">默认设置</p>
                        </el-col>
                        <el-col :span="6" style="text-align: center">
                            <button class="drjx" @click="dangrijixiao"></button>
                            <p style="width: 100%;">当日绩效</p>
                        </el-col>
                        <el-col :span="6" style="text-align: center">
                            <button class="sybz" @click="shiyongbangzhu"></button>
                            <p style="width: 100%;">使用帮助</p>
                        </el-col>
                    </el-row>
                </el-collapse-item>
                <div>
                    <el-button type="warning" class="noOrder" style="width: 100%;margin: 1rem auto;"
                               @click="go_back">
                        返回首页
                    </el-button>
                </div>
            </el-collapse>
        </div>
    </div>
    <!-- 点击报工弹窗 -->
    <el-dialog :title="titleName" width="80%" :visible.sync="gdxq" :before-close="clean_data">
        <div v-loading="gdbg_loading" style="width: 100%;display: flex;flex-wrap: wrap;">
            <div style="width: 100%;display: flex;flex-wrap: wrap;">
                <!-- 报工详情数据 -->
                <div style="display: flex;width: 100%;justify-content: space-around;">
                    <div style="padding: 0 10px;display: flex;flex-wrap: wrap;">
                        <!-- 可报工 -->
                        <div v-if="allow" v-for="(item,index) in xdy_title"
                             style="display: flex;width:25%;justify-content: flex-start; align-items: center;">
                            <p style="font-weight: bold;">{{item.field_description}}：</p>
                            <!-- 文本 -->
                            <span style="line-height:50px;color: #009688;"
                                  v-if="item.field_readonly">{{item.value}}</span>
                            <!-- 多选 -->
                            <el-select
                                    v-if="(item.ttype === 'one2many' || item.ttype === 'many2many') && !(item.field_readonly)"
                                    filterable clearable @clear="(value) =>reset_vals(value,item)" remote
                                    :remote-method="(value) => dialog_remoteMethod(value,item)" :loading="dia_loading"
                                    v-loadmore="(value) => zdy_loadMore(value,item)" v-model="item.value_id"
                                    placeholder="请选择" multiple @change="">
                                <el-option v-for="items in item.select_vals" :key="items.id" :label="items.name"
                                           :value="items.id"></el-option>
                            </el-select>
                            <!-- 单选 -->
                            <el-select
                                    v-if="(item.ttype === 'many2one' || item.ttype === 'selection') && !(item.field_readonly)"
                                    filterable clearable @clear="(value) =>reset_vals(value,item)" remote
                                    :remote-method="(value) => dialog_remoteMethod(value,item)" :loading="dia_loading"
                                    v-loadmore="(value) => zdy_loadMore(value,item)" v-model="item.value_id"
                                    placeholder="请选择" @change="">
                                <el-option v-for="items in item.select_vals" :key="items.id" :label="items.name"
                                           :value="items.id"></el-option>
                            </el-select>
                        </div>
                        <p style="font-weight: bold;" v-if="workType == '补件' || workType == '返修'">{{ workType }}备注：</p>
                            <!-- 文本 -->
                            <span style="line-height:50px;color: #009688;">{{ noteValue }}</span>
                        <!-- 仅查看 -->
                        <div v-if="!allow" v-for="(item,index) in xdy_title "
                             style="display: flex;width:25%;justify-content: flex-start; align-items: center;">
                            <p style="font-weight: bold;">{{item.field_description}}：</p>
                            <span style="line-height:50px" style="color: #009688;">
                                    {{item.value}}
                                </span>
                        </div>
                    </div>
                </div>
                <!-- 录入详情区域 -->
                <div class="rightCon" style="width: 100%;display: flex;padding-top: 0;">
                    <div class="rightCon" style="width: 66%;">
                        <!-- 可查看 -->
                        <div v-if="!allow">
                            <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"
                                     @select="handleSelect">
                                <el-menu-item index="5">作业指导</el-menu-item>
                                <el-menu-item index="10">作业规范</el-menu-item>
                                <el-menu-item index="6" v-if="!no_orderFlag">物料需求</el-menu-item>
                                <el-menu-item index="7" v-if="!no_orderFlag">投料记录</el-menu-item>
                            </el-menu>
                            <!-- 作业指导 -->
                            <div v-if="activeIndex === '5'" style="display: flex;">
                                <div style="display: flex;justify-content: left;flex-wrap: wrap;">
                                    <el-button v-for="(item,index) in file_list" @click="checkout_pdf(item)"
                                               style="margin: 10px;">{{item.name}}
                                    </el-button>
                                </div>
                            </div>
                            <!-- 作业规范 -->
                            <div v-if="activeIndex === '10' && !no_orderFlag">
                                <div>
                                    <div v-for="(item,index) in order_detail.standard_list"
                                         style="margin-top: 10px;border-bottom: 1px solid #ddd;padding:10px">
                                        <div>作业规范标题：{{item.title}}</div>
                                        <div>作业规范内容：{{item.name}}</div>
                                        <div style="display: flex;justify-content: left;margin-top: 10px;">
                                            <el-button v-for="o in item.image_url"
                                                       @click="checkout_pdf(o)">{{o.name}}
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 物料需求 -->
                            <div v-if="activeIndex === '6'">
                                <el-table :data="material_demand_list" class="lrxqTable">
                                    <el-table-column style="text-align: center;" label="序号" type="index">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="material" label="物料名称"></el-table-column>
                                    <el-table-column prop="demand_qty" label="需求数量"></el-table-column>
                                    <el-table-column prop="finished_qty" label="已投数量"></el-table-column>
                                    <el-table-column label="需求差额">
                                        <template slot-scope="scope">
                                            {{scope.row.difference_qty}}
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <!-- 投料记录 -->
                            <div v-if="activeIndex === '7'">
                                <el-table :data="material_record_list" class="lrxqTable">
                                    <el-table-column style="text-align: center;" label="序号" type="index">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="material" label="物料名称"></el-table-column>
                                    <el-table-column prop="qty" label="投料数量"></el-table-column>
                                    <el-table-column prop="lot_code" label="单件/批次号"></el-table-column>
                                    <el-table-column label="操作" width="100">
                                        <template slot-scope="scope">
                                            <el-button @click.native.prevent="deleteRow(scope.row)" type="text"
                                                       style="color: #FF4539;font-size: 16px;">撤回
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <!-- 可报工 -->
                        <div v-if="allow" style="margin-top: 1rem;">
                            <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal"
                                     @select="handleSelect">
                                <el-menu-item index="1" v-if="reportData_value.length > 1">多人报工</el-menu-item>
                                <el-menu-item index="2">采集项</el-menu-item>
                                <el-menu-item index="3">报废原因</el-menu-item>
                                <el-menu-item index="4">返修原因</el-menu-item>
                                <el-menu-item index="5">作业指导</el-menu-item>
                                <el-menu-item index="10">作业规范</el-menu-item>
                                <el-menu-item index="6" v-if="!no_orderFlag">物料需求</el-menu-item>
                                <el-menu-item index="7" v-if="!no_orderFlag">投料记录</el-menu-item>
                                <el-menu-item index="8" v-if="byshow">副产品</el-menu-item>
                                <el-menu-item index="9">投料</el-menu-item>
                            </el-menu>
                            <!--多人报工-->
                            <el-table class="lrxqTable" v-if="activeIndex === '1'" ref="manyTable1"
                                      :data="manyWokers" highlight-current-row style="width: 100%;height: 100%;"
                                      @selection-change="handleGoodsChange">
                                <el-table-column type="selection" width="50" :disabled="true"></el-table-column>
                                <el-table-column property="name" label="作业人员"></el-table-column>
                                <el-table-column label="权重">
                                    <template slot-scope="scope">
                                        <el-input-number :row="scope.row" v-model="scope.row.weight" :min="0"
                                                         :precision="2"
                                                         @change="(currentValue, oldValue) => changeWeight(currentValue, oldValue, scope.row.weight)">
                                        </el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="工资比例(百分比%)">
                                    <template slot-scope="scope">
                                        <!--<span v-model="scope.row.percent" v-if="allWeight === 0 && scope.row.weight !== undefined">{{fun(100/manyWokers.length)}}%</span>-->
                                        <span
                                                v-model="scope.row.percent">{{fun(100/allWeight*scope.row.weight)}}%</span>
                                        <!--<span v-model="scope.row.percent" v-if="scope.row.weight === undefined">{{fun(100/manyWokers.length)}}%</span>-->
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!--采集项-->
                            <el-table v-if="activeIndex === '2'" :data="collection_list" class="lrxqTable">
                                <el-table-column style="text-align: center;" label="序号" type="index">
                                    <template slot-scope="scope">
                                        <span>{{scope.$index+1}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column property="name" label="名称"></el-table-column>
                                <el-table-column style="text-align: center;" property="capture_item_name"
                                                 label="采集项">
                                    <template slot-scope="scope">
                                        <!-- 文字 -->
                                        <el-input :row="scope.row" v-model="scope.row.cj_input"
                                                  v-if="scope.row.data_type === '文本'" placeholder="请输入文字"
                                                  style="width: 60%;line-height: 47px;"></el-input>
                                        <!-- 下拉选择 -->
                                        <el-select :row="scope.row" v-model="scope.row.cj_select" placeholder="请选择"
                                                   v-if="scope.row.data_type === '单选'"
                                                   style="width: 60%;line-height: 47px;">
                                            <el-option v-for="item in scope.row.single_items" :key="item"
                                                       :label="item" :value="item">
                                            </el-option>
                                        </el-select>
                                        <!-- 数字小数 -->
                                        <el-input-number :row="scope.row" v-if="scope.row.data_type === '小数'"
                                                         v-model="scope.row.cj_number" :min="0" label="数量"
                                                         :precision="2"
                                                         style="width: 200px;"></el-input-number>
                                        <!-- 按钮增加计数 -->
                                        <el-input-number :row="scope.row" v-if="scope.row.data_type === '整数'"
                                                         v-model="scope.row.cj_input_num" :min="0" label="数量"
                                                         :precision="0"
                                                         style="width: 200px;"></el-input-number>
                                        <!-- 多选 -->
                                        <el-select :row="scope.row" v-if="scope.row.data_type === '多选'"
                                                   v-model="scope.row.cj_selection" multiple placeholder="请选择">
                                            <el-option v-for="item in scope.row.multiple_items" :key="item"
                                                       :label="item" :value="item"></el-option>
                                        </el-select>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!-- 报废原因 -->
                            <div style="display: flex;margin-top: 1rem; align-items: center;"
                                 v-if="activeIndex === '3'">
                                <p>选择报废原因：</p>
                                <el-select v-model="scrapReason" placeholder="请选择" filterable multiple
                                           @change="setreasons" v-loadmore="loadMore_bf" :filter-method="bfChange">
                                    <el-option v-for="item in scrap_reason" :key="item.index" :label="item.name"
                                               :value="item.id"></el-option>
                                </el-select>
                            </div>
                            <el-table class="lrxqTable" ref="manyTable2" :data="scrapReasons"
                                      v-if="activeIndex === '3'" highlight-current-row
                                      style="width: 100%;height: 100%;">
                                <el-table-column style="text-align: center;" label="序号" type="index">
                                    <template slot-scope="scope">
                                        <span>{{scope.$index+1}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column property="name" label="报废原因"></el-table-column>
                                <el-table-column label="数量">
                                    <template slot-scope="scope">
                                        <el-input-number :row="scope.row" v-model="scope.row.bf_num"
                                                         @change="bf_change" :min="0" label="数量"
                                                         :precision="2"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="选填备注">
                                    <template slot-scope="scope">
                                        <el-input :row="scope.row" v-model="scope.row.bf_reason"
                                                  label="选填备注"></el-input>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!-- 返修原因 -->
                            <div style="display: flex;margin-top: 1rem; align-items: center;"
                                 v-if="activeIndex === '4'">
                                <p>选择返修原因：</p>
                                <el-select v-model="repairReason" placeholder="请选择" filterable multiple
                                           @change="setfx" v-loadmore="loadMore_fx" :filter-method="fxChange">
                                    <el-option v-for="item in repair_reason" :key="item.index" :label="item.name"
                                               :value="item.id"></el-option>
                                </el-select>
                            </div>
                            <el-table class="lrxqTable" ref="manyTable3" :data="repair_reasons"
                                      v-if="activeIndex === '4'" highlight-current-row
                                      style="width: 100%;height: 100%;">
                                <el-table-column style="text-align: center;" label="序号" type="index">
                                    <template slot-scope="scope">
                                        <span>{{scope.$index+1}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column property="name" label="返修原因"></el-table-column>
                                <el-table-column label="数量">
                                    <template slot-scope="scope">
                                        <el-input-number :row="scope.row" v-model="scope.row.fx_num"
                                                         @change="fx_change" :min="0" label="数量"
                                                         :precision="2"></el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="选填备注">
                                    <template slot-scope="scope">
                                        <el-input :row="scope.row" v-model="scope.row.fx_reason"
                                                  label="选填备注"></el-input>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!-- 有工单，作业指导 -->
                            <div v-if="activeIndex === '5' && !no_orderFlag">
                                <div style="display: flex;justify-content: left;flex-wrap: wrap;">
                                    <el-button v-for="(item,index) in file_list" @click="checkout_pdf(item)"
                                               style="margin: 10px;">{{item.name}}
                                    </el-button>
                                </div>
                            </div>
                            <!-- 有工单，作业规范 -->
                            <div v-if="activeIndex === '10' && !no_orderFlag">
                                <div>
                                    <div v-for="(item,index) in order_detail.standard_list"
                                         style="margin-top: 10px;border-bottom: 1px solid #ddd;padding:10px">
                                        <div>作业规范标题：{{item.title}}</div>
                                        <div>作业规范内容：{{item.name}}</div>
                                        <div style="display: flex;justify-content: left;margin-top: 10px;">
                                            <el-button v-for="o in item.image_url"
                                                       @click="checkout_pdf(o)">{{o.name}}
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--物料需求-->
                            <div v-if="activeIndex === '6'">
                                <el-table :data="material_demand_list" class="lrxqTable">
                                    <el-table-column style="text-align: center;" label="序号" type="index">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="material" label="物料名称"></el-table-column>
                                    <el-table-column prop="demand_qty" label="需求数量"></el-table-column>
                                    <el-table-column prop="finished_qty" label="已投数量"></el-table-column>
                                    <el-table-column label="需求差额">
                                        <template slot-scope="scope">
                                            {{scope.row.difference_qty}}
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <!--投料记录-->
                            <div v-if="activeIndex === '7'">
                                <el-table :data="material_record_list" class="lrxqTable">
                                    <el-table-column style="text-align: center;" label="序号" type="index">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="material" label="物料名称"></el-table-column>
                                    <el-table-column prop="qty" label="投料数量"></el-table-column>
                                    <el-table-column prop="lot_code" label="单件/批次号"></el-table-column>
                                    <el-table-column label="操作" width="100">
                                        <template slot-scope="scope">
                                            <el-button @click.native.prevent="deleteRow(scope.row)" type="text"
                                                       style="color: #FF4539;font-size: 16px;">撤回
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <!--副产品-->
                            <div style="display: flex;margin-top: 1rem;" v-if="activeIndex === '8'">
                                <p>选择副产品：</p>
                                <el-select v-model="byProduct " placeholder="请选择" filterable multiple
                                           @change="setby">
                                    <el-option v-for="item in by_reasons" :key="item.index"
                                               :label="item.product_name" :value="item.product_id"></el-option>
                                </el-select>
                            </div>
                            <div>
                                <el-table class="lrxqTable" ref="manyTable3" :data="byProductList"
                                          v-if="activeIndex === '8'" highlight-current-row
                                          style="width: 100%;height: 100%;">
                                    <el-table-column style="text-align: center;" label="序号" type="index">
                                        <template slot-scope="scope">
                                            <span>{{scope.$index+1}}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column property="product_name" label="副产品"></el-table-column>
                                    <el-table-column>
                                        <template slot-scope="scope">
                                            <el-input-number :row="scope.row" v-model="scope.row.by_num" :min="0"
                                                             label="数量" :precision="2"></el-input-number>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <!-- 投料开始 -->
                            <div style="margin-top: 1rem;" v-if="activeIndex === '9'">
                                <div style="width: 100%;margin-top: 1rem;display: flex;">
                                    <div style="width: 100%;">
                                        <span>单件/批次号：</span>
                                        <el-input type="text" v-model="pch" style="width: 200px;"
                                                  @blur="getDetails"></el-input>
                                    </div>
                                    <div style="width: 100%;">
                                        <span>选&ensp;择&ensp;物&ensp;料：</span>
                                        <el-select placeholder="请选择" v-model="wl_id" style="width: 200px;"
                                                   @change="set_touliao_num" v-loadmore="loadMore" filterable
                                                   :filter-method="chanpinChange">
                                            <el-option v-for="item in chanpinList" :key="item.id" :label="item.name"
                                                       :value="item.id">
                                            </el-option>
                                        </el-select>
                                        <el-button type="primary" icon="el-icon-edit" circle
                                                   @click="open_check_cp"></el-button>
                                    </div>
                                </div>
                            </div>
                            <el-table class="lrxqTable" ref="manyTable2" :data="feedingList"
                                      v-if="activeIndex === '9'" :key="random_key" highlight-current-row
                                      style="width: 100%;height: 100%;">
                                <el-table-column style="text-align: center;" label="序号" type="index">
                                    <template slot-scope="scope">
                                        <span>{{scope.$index+1}}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column property="material_name" label="物料名称"></el-table-column>
                                <el-table-column label="数量">
                                    <template slot-scope="scope">
                                        <el-input-number :row="scope.row" v-model="scope.row.touliao_num" :min="1"
                                                         @change='toulCheck' :precision="2">
                                        </el-input-number>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作">
                                    <template slot-scope="scope">
                                        <el-button type="danger" icon="el-icon-delete" circle
                                                   @click="wlhandleClick(scope.row)"></el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <!-- 投料结束 -->
                        </div>
                    </div>
                    <div class="leftCon" style="width: 34%;" v-if="!allow">
                        <!-- <div>
                            <p>工作中心：{{read_workcenter}}</p>
                            <p>班&emsp;&emsp;组：{{read_team}}</p>
                            <p v-if="read_employee.length < 1">人&emsp;&emsp;员：</p>
                            <p v-if="read_employee.length >= 1">人&emsp;&emsp;员：<span
                                    v-for="item in read_employee">{{item.name}}/</span></p>
                        </div> -->
                        <div style="border-top: 1px solid #ddd;margin-top: 75px;" v-if="collection_list.length > 0">
                            <!-- 仅查看 -->
                            <p style="font-size: 18px;font-weight: bold;">采集项</p>
                            <div v-for="(item,index) in read_collection_list">
                                <p>{{item.name}} : {{item.result}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="leftCon" style="width: 34%;" v-if="allow">
                        <div>
                            <div style="display:flex;">
                                <p style="font-weight: bold;font-size: 16px;">工单计划：</p>
                                <p style="font-weight: bold;font-size: 16px;">{{plan_qty}}</p>
                                <p style="font-weight: bold;font-size: 16px;">&ensp;/&ensp;</p>
                                <p style="font-weight: bold;font-size: 16px;">本次可报：</p>
                                <p style="font-weight: bold;font-size: 16px;">{{allow_qty}}</p>
                            </div>
                            <div style="display:flex;">
                                <p>报&ensp;&ensp;工&ensp;时&ensp;间：</p>
                                <el-date-picker :picker-options="pickerOptionss" v-model="dateVal" type="datetime"
                                                placeholder="为空时默认当前时间"
                                                value-format='yyyy-MM-dd HH:mm:ss'></el-date-picker>
                            </div>
<!--                            <div style="display:flex;">-->
<!--                                <p>工&ensp;&ensp;&ensp;时&ensp;&ensp;&ensp;数：</p>-->
<!--                                <el-input-number v-model="bg_gss" :min="0" label="工时数" :precision="2">-->
<!--                                </el-input-number>-->
<!--                            </div>-->
                            <div style="display:flex;">
                                <p>合&ensp;&ensp;&ensp;格&ensp;&ensp;&ensp;数：</p>
                                <el-input-number v-model="bg_hgs" :min="0" label="合格数" @change="set_move_qty"
                                                 :precision="2"></el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{uom}}</span>
                            </div>
                            <div v-if="auxiliary_uom !== ''" style="display:flex;">
                                <p>合 格 辅 数 量：</p>
                                <el-input-number v-model="bg_hgfsl" :min="0" label="合格辅数量" :precision="2">
                                </el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{auxiliary_uom}}</span>
                            </div>
                            <div style="display:flex;">
                                <p>不&ensp;&ensp;合&ensp;格&ensp;数：</p>
                                <el-input-number v-model="bg_bhgs" :min="0" label="不合格数"
                                                 :precision="2"></el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{uom}}</span>
                            </div>
                            <div v-if="auxiliary_uom !== ''" style="display:flex;">
                                <p>不合格辅数量：</p>
                                <el-input-number v-model="bg_bhgfsl" :min="0" label="不合格辅数量" :precision="2">
                                </el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{auxiliary_uom}}</span>
                            </div>
                            <div style="display:flex;">
                                <p>报&ensp;&ensp;&ensp;废&ensp;&ensp;&ensp;数：</p>
                                <el-input-number v-model="bg_bfs" disabled :min="0" label="报废数" :precision="2">
                                </el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{uom}}</span>
                            </div>
                            <div style="display:flex;">
                                <p>返&ensp;&ensp;&ensp;修&ensp;&ensp;&ensp;数：</p>
                                <el-input-number v-model="bg_fxs" disabled :min="0" label="返修数" :precision="2">
                                </el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{uom}}</span>
                            </div>
                            <div style="display:flex;" v-if="custom_move">
                                <p>流&ensp;&ensp;&ensp;转&ensp;&ensp;&ensp;数：</p>
                                <el-input-number v-model="move_qty" :min="0" label="为空默认自动流转" :precision="2">
                                </el-input-number>
                                <span
                                        style="height: 47px;line-height: 47px;color: #11a6c4;font-weight: bold;padding-left: 3px;">
                                        {{uom}}</span>
                            </div>
                            <div style="display:flex;" v-if="custom_move">
                                <p>流&ensp;&ensp;&ensp;转&ensp;&ensp;&ensp;至：</p>
                                <el-select v-model="next_process_id" filterable placeholder="为空默认自动流转"
                                           style="width: 50%" v-loadmore='loadMore_Gongxu'
                                           :filter-method="gongxuChange">
                                    <el-option v-for="item in gongxuList" :key="item.id" :label="item.name"
                                               :value="item.id">
                                    </el-option>
                                </el-select>
                            </div>
                            <div style="display:flex;">
                                <p>备&ensp;&ensp;注&ensp;说&ensp;明：</p>
                                <el-input v-model="report_note" label="备注" style="width: 180px"></el-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer" style="width: 100%;text-align: right;">
                    <el-button type="primary" @click="downloadUrl" v-if="document_list.length > 0">下载文件</el-button>
                    <el-button type="primary" @click="reportSubmit" v-if="allow">报工</el-button>
                    <el-button @click="clean_data">关闭</el-button>
                </span>
        </div>
    </el-dialog>
    <!-- 当日绩效弹窗 -->
    <el-dialog title="当日工资" width="50%" :visible.sync="drjx">
        <div style="padding: 1rem;text-align: center;width: 100%;font-size: 16px;">
            截止到{{jx_msg.today}}，您的计件工资为
            <span style="color: #11a6c4;font-weight: bold;font-size: 18px;">
                    {{jx_msg.salary}}
                </span>
            元
            <div style="width: 100%;display: flex;flex-wrap:wrap;margin-top: 1rem;">
                <p style="width: 50%">报工数：{{jx_msg.finish_qty}}</p>
                <p style="width: 50%">报废数：{{jx_msg.scrap_qty}}</p>
                <p style="width: 50%">不合格数：{{jx_msg.unqualified_qty}}</p>
                <p style="width: 50%">返修数：{{jx_msg.repair_qty}}</p>
            </div>
            <el-button type="primary" @click="check_details">明细</el-button>
            <el-button @click="drjx = false;">关闭</el-button>
        </div>
    </el-dialog>
    <!-- 默认设置弹窗 -->
    <el-dialog title="提示" :visible.sync="morenshezhilog" width="50%" :before-close="handleClose">
        <div style="padding: 0 60px; width: 80%;">
            <div style="display: flex;">
                <p style="width: 100px;">工作中心：</p>
                <el-select style="flex: 1;" v-model="workCenter_value" clearable placeholder="请选择" filterable
                           @clear='empty' v-loadmore="loadMore_gzzx" :filter-method="gzzxChange">
                    <el-option v-for="item in Workcenter" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div style="display: flex;">
                <p style="width: 100px;">班&emsp;&emsp;组：</p>
                <el-select style="flex: 1;" v-model="Information_values" clearable placeholder="请选择" filterable
                           @clear='empty1' v-loadmore="loadMore_bz" :filter-method="bzChange">
                    <el-option v-for="item in Information" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div style="display: flex;">
                <p style="width: 100px;">工&emsp;&emsp;序：</p>
                <el-select style="flex: 1;" v-model="gongx_value" clearable placeholder="请选择" filterable
                           @clear='empty2' v-loadmore='loadMore_Gongxu' :filter-method="gongxuChange">
                    <el-option v-for="item in gongxuList" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
            <div style="display: flex;">
                <p style="width: 100px;">人&emsp;&emsp;员：</p>
                <el-select style="flex: 1;" v-model="reportData_value" clearable placeholder="请选择" filterable
                           multiple @change="setValues" @clear='empty3' v-loadmore="loadMore_ry"
                           :filter-method='ryChange'>
                    <el-option v-for="item in reportData" :key="item.index" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
                <el-button @click="morenshezhilog = false">取 消</el-button>
                <el-button type="primary" @click="morenshezhiClick">确 定</el-button>
            </span>
    </el-dialog>
    <!-- 产品分类弹窗 -->
    <el-dialog title="选择产品" width="80%" :visible.sync="choose_cp_list" :before-close="cp_dialog_close">
        <div style="flex-wrap: wrap;width: 100%;">
            <div style="display: flex;padding: 15px;width:96%;flex-wrap: wrap;">
                <el-tabs v-model="activeName" @tab-click="handleClick" style="width: 100%">
                    <el-tab-pane v-for="(item,index) in cptablist" :label="item.name"
                                 :name="item.index"></el-tab-pane>
                </el-tabs>
                <div class="category" v-show="cp_child">
                    <div class="list">
                        <div class="type_name" v-for="(item,index) in cp_child_category" :key="" :style="item.style"
                             @click="sel_cp_category(item,index)">
                            <span>{{item.name}}</span>
                            <img src="/roke_mes_production/static/src/images/13a73c69-8345-43b4-a96b-42f26de58437.png"
                                 v-if="item.child_category.length>0" alt="">
                        </div>
                    </div>
                    <div class="button">
                        <el-button type="primary" style="background: #02BA7E;" @click="cp_pre">上一页</el-button>
                        <el-button type="primary" style="background: #02BA7E;" @click="cp_next">下一页</el-button>
                    </div>
                    <div class="img" @click="close_cp_type">
                        <span>收起</span>
                        <img src="/roke_mes_production/static/src/images/shouqi.png" alt="">
                    </div>
                </div>
                <div style="width:100%;display: flex;flex-wrap: wrap;justify-content: left;" v-loading="cp_loading">
                    <div v-for="(item,index) in productsList" @click="choose_cp_item(item)"
                         style="cursor:pointer;display: flex;min-width:13%;text-align:center;height: 40px;line-height: 40px;padding: 5px;margin: 8px;border: 1px solid #979797;overflow: hidden;text-overflow: ellipsis">
                        {{item.name}}
                    </div>
                </div>
            </div>
        </div>
    </el-dialog>
</div>
</body>
<script>
    // 自定义指令下拉加载
    Vue.directive('loadmore', {
        bind(el, binding) {
            // 获取element-ui定义好的scroll盒子
            const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
            SELECTWRAP_DOM.addEventListener('scroll', function () {
                const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight;
                if (CONDITION) {
                    binding.value()
                }
            })
        }
    });
    var Main = {
        data() {
            return {
                dialog_key: false,
                dia_loading: false,
                loading: false,
                jl_record_filter_data: [],
                jl_filter_list: {}, // 报工记录筛选条件
                jl_record_list_data: [],
                jl_record_top_title: [],
                zdy_total_list: [],
                zdy_category_id: '',
                cp_category_id: '',
                tabindex: '',
                zdy_List: [],
                zdy_child_category: [],
                zdy_child: false,
                zdy_load_ing: false,
                zdy_cptablist: [],
                zdy_activeName: '',
                choose_zdy_list: false,
                xdy_title: [],
                gd_loading: false,
                gd_random_key: 0,
                filter_list: {'state': '未完工'}, // 工单报工筛选条件
                record_list_data: [],
                record_top_title: [],
                record_filter_data: [],
                random_key: 0,
                cp_child_category: [],
                cp_loading: false,
                cp_child: false,
                gdbg_loading: false,
                tableData: [],
                state2: '',
                topName: '',
                typeShow: '',
                child: false,
                nextchild: [],
                cp_nextchild: [],
                level: '',
                cp_level: '',
                ids: [],
                cp_ids: [],
                zdy_ids: [],
                zdy_cp_ids: [],
                category_id: '',
                child_category: [],
                cp_true: false,
                processList: [],
                activeName_gx: '0',
                tabsList: [],
                secrch_gx_id: '',
                processSels: [],
                processSel_flag: false,
                secrch_gx_val: '',
                processSel: [],
                choose_gx_list: false,
                secrch_cp_id: '',
                productSel_flag: false,
                productSel: [],
                load_ing: true,
                productsList: [],
                activeName: '0',
                secrch_cp_val: '',
                choose_cp_list: false,
                cptablist: [],
                file_list: [],
                uom: '',
                auxiliary_uom: '',
                bg_bhgfsl: 0,
                bg_hgfsl: 0,
                material_record_list: [],
                material_demand_list: [],
                demand_qty: 0,
                sys_qty: 0,
                allow_input_material: false,
                wl_id: '',
                material_id: '',
                material_name: '',
                pch: '',
                touliao_num: 1,
                next_process_id: '',
                move_qty: 0,
                custom_move: false,
                report_note: "",
                dateVal: '',
                byProduct: [],
                repairReason: [],
                repair_reasons: [],
                by_reasons: [],
                jx_msg: '',
                drjx: false,
                tl: false,
                gongzuozhongxin: '',
                banzu: '',
                renyuan: '',
                checkFlag: false,
                checkRecords: [],
                no_order_gx: '',
                no_order_gxList: [],
                no_order_cp: '',
                no_order_cpList: [],
                manyWokers: [],
                bg_fxs: 0,
                bg_bfs: 0,
                bg_bhgs: 0,
                bg_hgs: 0,
                bg_gss: 0,
                reportData_value: [],
                Information_value: '',
                Information_values: '',
                scrapReason: [],
                scrapReasons: [],
                titleName: '',
                repair_reason: [],
                byProductList: [],
                bf_checkList: [],
                fx_checkList: [],
                by_checkList: [],
                tl_checkList: [],
                scrap_reason: [],
                activeIndex: '2',
                allow: false,
                allow_qty: 0,
                plan_qty: 0,
                kehuList: [],
                kehu: '',
                gongxuList: [],
                gongxu: '',
                collection_list: [],
                cj_selection_options: [],
                cj_selection: [],
                cj_radio_option: [],
                cj_radio: '',
                cj_input_num: 0,
                cj_number: 0,
                cj_options: [],
                cj_select: '',
                cj_input: '',
                gdxq: false,
                task_type: '',
                order_detail: [],
                startedFlag: false,
                radio: '',
                currentPage: 1,
                pageSize: 10,
                currentPagejl: 1,
                pageSizejl: 10,
                inHeight: 0,
                pickerOptions: {
                    shortcuts: [{
                        text: '最近一周',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近一个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近三个月',
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                value2: [],
                dingdanhao: '',
                xiangmuhao: '',
                chanpin: '',
                chanpinList: [],
                tableDatas: [],
                search: '',
                activeNames: ['1', '2'],
                username: '',
                timer: null,
                currentTime: '',
                currentSec: '',
                procedureList: [],
                select_procedure: '',
                select_product: '',
                reportData: [],
                Information: [],
                Workcenter: [],
                workCenter_value: "",
                workCenter_value_id: '',
                Information_value_id: '',
                work_order_id: '',
                order_id: '',
                employee_allot_list: [],
                allWeight: 0,
                no_orderFlag: false,
                unqualified_qty: 0,
                morenshezhilog: false,
                defaultList: [],
                defaultgongxu: "",
                flag: false,
                mrNum: '',
                banBum: '',
                renyuanList: [],
                arr: [],
                gongxuNum: '',
                gongxu_List: [],
                gongx_value: '',
                specificationList: [],
                specification: false,
                byshow: false,
                dd: false,
                compareList: [],
                compareHeader: [],
                dialogObj: '',
                ddVisible: false,
                ddInput: '',
                ddInput1: '',
                ddInput2: '',
                ddorderId: '',
                ddId: '',
                ddkehu: '',
                ddshow: false,
                ddgongxu: '',
                ddchanpin: '',
                feedingList: [],
                total: '',
                totaljl: '',
                datations: '',
                chanpinpageNum: 1,
                chanpinpageSize: 20,
                gongxupageNum: 1,
                gongxupageSize: 20,
                renyuanpageNum: 1,
                renyuanpageSize: 20,
                banzupageNum: 1,
                banzupageSize: 20,
                gongzuozhongxinpageNum: 1,
                gongzuozhongxinpageSize: 20,
                gongzuozhongxinTotal: 0,
                kehupageNum: 1,
                kehupageSize: 20,
                scrapReasonpageNum: 1,
                scrapReasonpageSize: 20,
                repairReasonpageNum: 1,
                repairReasonpageSize: 20,
                listPriority: '',
                listState: '',
                datations_Kehu: '',
                datations_Gongxu: '',
                datations_bf: '',
                datations_fx: '',
                datations_gzzx: '',
                datations_bz: '',
                datations_ry: '',
                listChanpin: '',
                listGongxu: '',
                listKehu: '',
                listXiangmuhao: '',
                listDingdanhao: '',
                timeStart: '',
                timeEnd: '',
                listry: '',
                listbz: '',
                listgzzx: '',
                personnelText: '',
                reportHide: false,
                orderShow: '',
                codeShow: '',
                many_procedure: false,
                bcDatas: [],
                bcDatas_value: '',
                reportDataValue: '',
                dialogFormVisibleShow: false,
                userEdit: [],
                tableDataL: [],
                deleTian: '',
                zaizai: [],
                gongzuotian: '',
                banciTIAN: '',
                tianzaiduixiang: {},
                rowTIAN: {},
                TIANTIAN: '',
                arrayT: 1,
                read_workcenter: '',
                read_team: '',
                read_employee: '',
                zdy_datations: '',
                statu: null, // 顶部搜索框节流阀
                // 限制报工时间最大日期
                pickerOptionss: {
                    disabledDate(time) {
                        return time.getTime() > Date.now();
                    },
                },
                gdId: null, // 当前工单id
                work_center_show: false, // 判断工作中心是否必传标识
                document_list: [],
                default_process_id:null,
                noteValue: '',
                workType: ''
            }
        },
        created() {
            this.isLoginApi()
            this.gongxu_filter('初始化')

        },
        mounted() {
        },
        methods: {
            // 判断用户是否登录
            isLoginApi() {
                let that = this;
                axios.request({
                    url: "/roke/mes/check_login_state",
                    method: "get"
                }).then((res) => {
                    if (res.data.state === 'success') {
                        that.startedFlag = res.data.started;
                        that.username = res.data.user_name;
                        that.timer = setInterval(that.setCurrentTime, 1000);
                        that.getmorenList();
                        that.chanpin_filter();
                        that.kehu_filter();
                        // that.gongxu_filter();
                        that.get_category();
                        let screenHeight = window.innerHeight;
                        that.inHeight = screenHeight - 84;
                        // if(!that.wgdbgFlag){
                        //     that.$nextTick(() => {
                        //         that.$refs.autofocus.focus()
                        //     });
                        // }
                    } else {
                        that.$alert('请先登录账号！', '提示信息', {
                            confirmButtonText: '确定',
                            callback: action => {
                                window.location.replace('/roke_mes_production/static/html/login.html');
                            }
                        });
                    }
                });
            },
            // 获取页面全部数据
            getPageAllDataApi(default_process_id) {
                let that = this;
                axios.request({
                    url: "/roke/get_staging_order_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'model_index': 'roke.work.order',
                        'work_desk_index': 'gongdanbaogong',
                        'domain_dict': {'state': '未完工','process_id':default_process_id},
                        'code': '',
                        'search_type': '',
                        'page_no': 1,
                        'page_size': 10
                    }
                }).then((res) => {
                    if (res.data.result.state === 'success') {
                        that.total = res.data.result.total_number;
                        that.topName = res.data.result.top_title;
                        that.record_list_data = res.data.result.record_list_data;
                        that.record_top_title = res.data.result.record_top_title;
                        that.record_filter_data = res.data.result.record_filter_data;
                        that.record_filter_data.map((item, index) => {
                            item.chanpinpageNum = 1;
                            // 默认筛选条件未完工
                            if (item.field_index === 'state') {
                                item.value = '未完工'
                            }
                            // 获取下拉框数据
                            if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                                axios.request({
                                    url: "/roke/get_general_base_data",
                                    method: "post",
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    data: {
                                        "ttype": item.ttype,
                                        "page_no": 1,
                                        "page_size": 20,
                                        "model_index": item.relation,
                                        "domain_data": '',
                                        "selection_mode": ''
                                    }
                                }).then((res) => {
                                    if (res.data.result.state === 'success') {
                                        item.record_list = res.data.result.record_list;
                                    } else if (res.data.result.state === 'error') {
                                        that.$message({
                                            type: 'error',
                                            message: res.data.result.msgs
                                        });
                                    } else if (res.data.error) {
                                        that.$message({
                                            type: 'error',
                                            message: res.data.error.message
                                        });
                                    }
                                });
                            }
                        });
                    } else {
                        that.$message({
                            type: 'error',
                            message: '未查到该数据！'
                        });
                    }
                });
            },
            // 报废原因数量发生变化事件
            bf_change(e) {
                this.bg_bfs = 0
                this.scrap_reason.forEach(item => {
                    if (item.bf_num !== undefined && item.bf_num !== '') {
                        this.bg_bfs += item.bf_num
                    }
                })
                this.bg_bhgs = this.bg_bfs + this.bg_fxs
            },
            // 返修原因数量发生变化事件
            fx_change(e) {
                this.bg_fxs = 0
                this.repair_reasons.forEach(item => {
                    if (item.fx_num !== undefined && item.fx_num !== '') {
                        this.bg_fxs += item.fx_num
                    }
                })
                this.bg_bhgs = this.bg_bfs + this.bg_fxs
            },
            //自定义获取下拉框加载
            auto_loadmore(val) {
                // console.log(val);
                if (this.datations !== '到底啦') {
                    this.chanpin_filter((this.chanpinpageNum += 1))
                }
            },
            //自定义筛选下拉加载
            zdy_loadMore(val, item) {
                console.log(val,item);
                item.chanpinpageNum += 1;
                if (item.zdy_datations !== '到底啦') {
                    this.zdy_filter(item.chanpinpageNum, item)
                }
            },
            // 获取产品下拉框加载
            loadMore() {
                // 这里写入要触发的方法
                if (this.datations !== '到底啦') {
                    this.chanpin_filter((this.chanpinpageNum += 1))
                }
            },
            // 获取客户下拉框加载
            loadMore_Kehu() {
                if (this.datations_Kehu !== '到底啦') {
                    this.kehu_filter((this.kehupageNum += 1))
                }
            },
            // 获取工序下拉框加载
            loadMore_Gongxu() {
                if (this.datations_Gongxu !== '到底啦') {
                    // console.log(this.gongxupageNum += 1);
                    this.gongxu_filter((this.gongxupageNum += 1))
                }
            },
            loadMore_bf() {
                if (this.datations_bf !== '到底啦') {
                    this.getbfDatas((this.scrapReasonpageNum += 1))
                }
            },
            loadMore_fx() {
                if (this.datations_fx !== '到底啦') {
                    this.getfxDatas((this.repairReasonpageNum += 1))
                }
            },
            loadMore_gzzx() {
                if (this.datations_gzzx !== '到底啦') {
                    this.getgzzxDates((this.gongzuozhongxinpageNum += 1))
                }
            },
            loadMore_bz() {
                if (this.datations_bz != '到底啦') {
                    this.getbzDates((this.banzupageNum += 1))
                }
            },
            loadMore_ry() {
                if (this.datations_ry != '到底啦') {
                    this.getDatas((this.renyuanpageNum += 1))
                }
            },
            // 进入页面默认获取默认值
            getmorenList() {
                let that = this;
                axios.request({
                    url: "/roke/get_user_default_setting",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {}
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        // console.log();
                        that.defaultList = res.data.result
                        that.workCenter_value = res.data.result.work_center_id;
                        that.mrNum = res.data.result.work_center_id.id;
                        that.banBum = res.data.result.team_id.id;
                        console.log(res.data.result)
                        that.Information_value = res.data.result.team_id.name
                        that.Information_values = res.data.result.team_id.name
                        that.gongx_value = res.data.result.process_id.id
                        that.gongxuNum = res.data.result.process_id.id
                        that.reportData_value = []
                        that.renyuanList = []
                        for (let i = 0; i < res.data.result.employee_ids.length; i++) {
                            that.reportData_value.push(res.data.result.employee_ids[i].id)
                            that.renyuanList.push(res.data.result.employee_ids[i].id)
                        }
                        // 人员
                        // if(!that.plbgFlag)
                        that.getDatas()
                        // 报废原因
                        that.getbfDatas()
                        // 返修原因
                        that.getfxDates()
                        // 工作中心
                        // that.getgzzxDates()
                        // 班组
                        that.getbzDates()

                    }
                });
            },
            // 获取表格数据列表
            getList(page) {
                let that = this;
                that.gd_loading = true;
                // 处理筛选结果
                let list = {};
                that.record_filter_data.map(item => {
                    list[item.field_index] = item.value;
                    if(item.field_index == 'process_id'){
                      if(!item.value){
list[item.field_index] = that.default_process_id
                      }
                    }
                });
                that.filter_list = list;
                axios.request({
                    url: "/roke/get_staging_order_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'model_index': 'roke.work.order',
                        'work_desk_index': 'gongdanbaogong',
                        'domain_dict': that.filter_list,
                        'code': '',
                        'search_type': '',
                        'page_no': page,
                        'page_size': 10
                    }
                }).then((res) => {
                    if (res.data.result.state === 'success') {
                        if (res.data.result.total_number === 0) {
                            that.$message({
                                type: 'error',
                                message: '未查到对应数据！'
                            });
                        } else {
                            that.total = res.data.result.total_number;
                            that.topName = res.data.result.top_title;
                            that.record_list_data = res.data.result.record_list_data;
                            that.record_top_title = res.data.result.record_top_title;
                            that.gd_random_key = Math.random();
                        }
                        that.gd_loading = false;
                    } else {
                        that.$message({
                            type: 'error',
                            message: '未查到该数据！'
                        });
                        that.gd_loading = false;
                    }
                });
            },
            // 点击修改默认值按钮
            morenshezhi() {
                this.morenshezhilog = true
            },
            // 默认设置确定
            morenshezhiClick() {
                this.morenshezhilog = false;
                axios.request({
                    url: "/roke/set_user_default_setting",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        "process_id": this.gongx_value,
                        "work_center_id": this.workCenter_value,
                        "team_id": this.Information_values,
                        "employee_ids": this.reportData_value,
                    }
                }).then((res) => {
                    if (res.data.result.state === 'success') {
                        this.$message({
                            type: 'success',
                            message: res.data.result.msgs
                        });
                        // this.getmorenList();
                    }
                });
            },
            // 清除默认设置第一行
            empty() {
                // console.log(111)
                this.workCenter_value = ''
            },
            // 清除默认设置第二行
            empty1() {
                this.Information_value = ''
            },
            // 清除默认设置第三行
            empty2() {
                this.gongx_value = ''
            },
            // 清除默认设置第四行
            empty3() {
                this.reportData_value = ''
            },
            // 默认设置弹窗关闭
            handleClose(done) {
                done();
            },
            // 工单报工页码切换事件
            handleCurrentChangee(val) {
                this.currentPage = val;
                this.getList(this.currentPage)
            },
            // 报工记录页码切换事件
            handleSizeChangejl(val) {
                this.pageSizejl = val
                this.baogongjilu()
            },
            // 时间不足两位加0
            convertDate: function (value) {
                return value < 10 ? "0" + value : value;
            },
            // 时间计时器
            setCurrentTime: function () { //设置当前时间
                var date = new Date();
                var year = date.getFullYear(); //年
                var month = this.convertDate(date.getMonth() + 1); //月
                var day = this.convertDate(date.getDate()); //日
                var hours = this.convertDate(date.getHours()); //时
                var minutes = this.convertDate(date.getMinutes()); //分
                var seconds = this.convertDate(date.getSeconds()); //秒
                this.currentTime = year + "年" + month + "月" + day + "日";
                this.currentSec = hours + ":" + minutes + ":" + seconds;
            },
            // 条件筛选事件
            handleChange(val) {
                // console.log(val);
            },
            // 获取报废原因
            getbfDatas(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_scrap_reason_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'scrap_reason': '',
                        "page_no": val,
                        'page_size': that.scrapReasonpageSize
                    }
                }).then((res) => {
                    // console.log(res);
                    // that.scrap_reason = res.data.result.result;
                    if (val === 1 || val === undefined || val === '') {
                        that.scrap_reason = res.data.result.result
                    } else {
                        that.scrap_reason = [...that.scrap_reason, ...res.data.result.result];
                    }
                    if ((that.scrap_reason.length == res.data.result.total_number)) {
                        that.datations_bf = '到底啦'
                    }
                });
            },
            // 自定义搜索报废原因
            bfChange(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_scrap_reason_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'scrap_reason': val,
                    }
                }).then(function (res) {
                    that.scrap_reason = res.data.result.result;
                });
            },
            // 获取返修原因
            getfxDates(val) {
                let that = this
                axios.request({
                    url: "/roke/get_repair_reason_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'repair_reason': '',
                        "page_no": val,
                        'page_size': that.repairReasonpageSize

                    }
                }).then(function (res) {
                    // that.repair_reason = res.data.result.result;
                    if (val === 1 || val === undefined || val === '') {
                        that.repair_reason = res.data.result.result
                    } else {
                        that.repair_reason = [...that.repair_reason, ...res.data.result.result]
                    }
                    if ((that.repair_reason.length == res.data.result.total_number)) {
                        that.datations_fx = '到底啦'
                    }
                });
            },
            // 自定义搜索返修原因
            fxChange(val) {
                let that = this
                axios.request({
                    url: "/roke/get_repair_reason_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'repair_reason': val,
                    }
                }).then((res) => {
                    that.repair_reason = res.data.result.result
                });
            },
            // 获取工作中心
            getgzzxDates(val) {
                let that = this
                axios.request({
                    url: "/roke/get_work_center_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'wo_id': that.gdId,
                        'work_center': '',
                        "page_no": val,
                        'page_size': that.gongzuozhongxinpageSize
                    }
                }).then((res) => {
                    if (val === 1 || val === undefined || val === '') {
                        that.Workcenter = res.data.result.result
                    } else {
                        that.Workcenter = [...that.Workcenter, ...res.data.result.result];
                    }
                    if ((that.Workcenter.length == res.data.result.total_number)) {
                        that.datations_gzzx = '到底啦'
                    }
                });
            },
            // 自定义搜索工作中心
            gzzxChange(val, id) {
                let that = this
                axios.request({
                    url: "/roke/get_work_center_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'wo_id': id,
                        'work_center': val,
                    }
                }).then((res) => {
                    that.Workcenter = res.data.result.result
                });
            },
            // 获取班组
            getbzDates(val) {
                let that = this;
                if (that.plbgFlag) {
                    that.Information = [];
                }
                ;
                axios.request({
                    url: "/roke/get_team_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'team': '',
                        "page_no": val,
                        'page_size': that.banzupageSize
                    }
                }).then((res) => {
                    if (val === 1 || val === undefined || val === '') {
                        that.Information = res.data.result.result
                    } else {
                        that.Information = [...that.Information, ...res.data.result.result];
                    }
                    if ((that.Information.length === res.data.result.total_number)) {
                        that.datations_bz = '到底啦'
                    }
                });
            },
            // 自定义搜索班组
            bzChange(val) {
                let that = this
                axios.request({
                    url: "/roke/get_team_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'team': val,
                    }
                }).then((res) => {
                    that.Information = res.data.result.result
                });
            },
            // 获取班次
            bcData() {
                let that = this;
                axios.request({
                    url: "/roke/get_classes_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {}
                }).then(res => {
                    that.bcDatas = res.data.result.result;
                })
            },
            // 获取人员
            getDatas(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_employee_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'employee': '',
                        "page_no": val,
                        'page_size': that.renyuanpageSize
                    }
                }).then((res) => {
                    if (val === 1 || val === undefined || val === '') {
                        that.reportData = res.data.result.result
                    } else {
                        that.reportData = [...that.reportData, ...res.data.result.result];
                    }
                    if ((that.reportData.length === res.data.result.total_number)) {
                        that.datations_ry = '到底啦'
                    }
                    if (that.many_procedure === false) {
                        that.getArr(res.data.result.result)
                    }
                });
            },
            // 自定义搜索人员
            ryChange(val) {
                let that = this
                axios.request({
                    url: "/roke/get_employee_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'employee': val,
                        'team_id': that.personnelText
                    }
                }).then((res) => {
                    that.reportData = res.data.result.result
                });
            },
            // 自定义搜索追加人员
            ryChange_add(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_employee_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'employee': val,
                        'team_id': ''
                    }
                }).then((res) => {
                    that.reportData = [...that.reportData, ...res.data.result.result];
                });
            },
            getArr(val) {
                this.workCenter_value = this.mrNum
                this.reportData_value = this.renyuanList
                this.Information_value = this.banBum
                this.gongx_value = this.gongxuNum
                let list = [];
                for (var k = 0; k < this.renyuanList.length; k++) {
                    for (var i = 0; i < val.length; i++) {
                        val[i].weight = 1;
                        if (val[i].id === this.renyuanList[k]) {
                            list.push(this.reportData[i]);
                            this.Information_value = val[i].team_id;
                        }
                    }
                }
                this.manyWokers = list;
                this.allWeight = this.manyWokers.length;
                if (this.renyuanList.length === 1) {
                    this.manyWokers = [];
                }
            },
            // 报工当前行事件
            handleCurrentChange(val) {
                this.gdId = val.id;
                this.allow = val.allow;
                this.order_id = val.id;
                this.dialog_key = true;
                this.getUrl(val)
                this.showData(val);
                this.getbfDatas(1)
                this.getfxDates(1)
                // this.getgzzxDates(1)
            },
            getUrl(val) {
                let that = this
                axios.request({
                    url: "/roke/workstation/product_task/get_document_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'work_order_id': val.id
                    }
                }).then((res) => {
                    if (res.data.result.code === 0) {
                        that.document_list = res.data.result.data
                    } else {
                        that.$message({
                            type: 'warning',
                            message: res.data.result.message
                        });
                    }
                });
            },
            // 下载工艺文件
            downloadUrl() {
                this.document_list.forEach(item => {
                    let webulr = item.data + '&download=true'
                    fetch(webulr)
                        .then((res) => res.blob())
                        .then((blob) => {
                            const a = document.createElement("a");
                            const objUrl = URL.createObjectURL(blob);
                            a.download = item.name;
                            a.href = objUrl;
                            a.click();
                            window.URL.revokeObjectURL(objUrl);
                            a.remove();
                        })
                })
            },

            // 获取报工、查看弹窗数据
            showData(val) {
                console.log(val)
                let that = this;
                axios.request({
                                            url: "/roke/work_record/get_image_data",
                                            method: "post",
                                            headers: {
                                                'Content-Type': 'application/json'
                                            },
                                            data: {
                                                "work_order_id": val.id
                                            }
                                        }).then((res) => {
                                            if (res.data.result.code === 0) {
                                                this.noteValue = res.data.result.note;
                                                this.workType = res.data.result.type;
                                            }
                                        });
                // 判断是否可以报工
                if (that.allow) {
                    // 可报工
                    that.titleName = val.product_id + '—' + val.process_id + '(可报工)';
                    axios.request({
                        url: "/roke/check_staging_order_detail",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            'model_index': 'roke.work.order',
                            'work_desk_index': 'gongdanbaogong',
                            'order_id': that.order_id,
                            'file_type': ''
                        }
                    }).then((res) => {
                        that.workCenter_value = res.data.result.result.xdy_data.work_center_id;
                        that.material_record_list = res.data.result.result.xdy_data.material_record_list;
                        that.by_reasons = res.data.result.result.xdy_data.by_products;
                        that.byshow = res.data.result.result.xdy_data.allow_by_product;
                        that.material_demand_list = res.data.result.result.xdy_data.material_demand_list;
                        that.allow_input_material = res.data.result.result.xdy_data.allow_input_material;
                        that.specificationList = res.data.result.result.xdy_data.standard_list;
                        if (that.specificationList.length > 0) {
                            that.specification = true
                        }
                        ;
                        that.order_detail = res.data.result.result.xdy_data;
                        that.xdy_title = res.data.result.result.xdy_title;
                        that.xdy_title.map(item => {
                            item.chanpinpageNum = 1;
                            if ((item.ttype === 'many2one' || item.ttype === 'one2many' || item.ttype === 'many2many') && (item.field_name === 'employee_ids' || item.field_name === 'team_id')) {
                                axios.request({
                                    url: "/roke/get_staging_base_data",
                                    method: "post",
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    data: {
                                        'model_index': item.relation,
                                        'search_str': '',
                                        'category_id': '',
                                        'page_no': 1,
                                        'page_size': 20
                                    }
                                }).then((res) => {
                                    if (res.data.result.state === 'success') {
                                        item.select_vals = res.data.result.record_list;
                                        if (item.relation === 'roke.work.center') {
                                            item.value_id = res.data.result.record_list[0].id
                                        }
                                    } else if (res.data.result.state === 'error') {
                                        that.$message({
                                            type: 'warning',
                                            message: res.data.result.msgs
                                        });
                                    } else {
                                        that.$message({
                                            type: 'warning',
                                            message: '暂无数据！'
                                        });
                                    }
                                });
                            } else if ((item.ttype === 'many2one' || item.ttype === 'one2many' || item.ttype === 'many2many') && (item.field_name === 'work_center_id')) {
                                axios.request({
                                    url: "/roke/get_work_center_list",
                                    method: "post",
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    data: {
                                        'wo_id': that.gdId,
                                        // 'work_center': '',
                                        // "page_no": val,
                                        // 'page_size': that.gongzuozhongxinpageSize
                                    }
                                }).then((res) => {
                                    // console.log(res)
                                    if (res.data.result.state === 'success') {
                                        if (res.data.result.result.length < 1) {
                                            that.work_center_show = true
                                            item.select_vals = []
                                        } else {
                                            that.work_center_show = false
                                            item.select_vals = res.data.result.result;
                                            item.value_id = res.data.result.result[0].id
                                        }
                                    } else if (res.data.result.state === 'error') {
                                        that.$message({
                                            type: 'warning',
                                            message: res.data.result.msgs
                                        });
                                    } else {
                                        // that.$message({
                                        //     type: 'warning',
                                        //     message: '暂无数据！'
                                        // });
                                    }
                                });
                            }
                        });
                        that.file_list = res.data.result.result.xdy_data.file_url[0];
                        that.task_type = res.data.result.result.xdy_data.task_type;
                        that.gdxq = true;
                        that.bg_hgs = res.data.result.result.xdy_data.allow_qty;
                        that.bg_hgs = res.data.result.result.xdy_data.wait_qty;
                        that.custom_move = res.data.result.result.xdy_data.custom_move;
                        that.move_qty = res.data.result.result.xdy_data.move_qty;
                        that.allow = res.data.result.result.xdy_data.allow;
                        that.allow_qty = res.data.result.result.xdy_data.allow_qty;
                        that.plan_qty = res.data.result.result.xdy_data.plan_qty;
                        that.collection_list = res.data.result.result.xdy_data.collection_list;
                        if (that.collection_list.length > 0) {
                            that.collection_list.map((item) => {
                                if (item.data_type === '多选') {
                                    that.cj_selection_options = item.multiple_items;
                                } else if (item.data_type === '单选') {
                                    that.cj_options = item.single_items;
                                }
                            })
                        }
                        that.uom = res.data.result.result.xdy_data.uom;
                        that.auxiliary_uom = res.data.result.result.xdy_data.auxiliary_uom;
                        //投料默认显示投料需求内容
                        if (that.material_demand_list.length > 0) {
                            that.feedingList = that.material_demand_list.map(item => {
                                return {
                                    'material_id': item.material_id,
                                    "qty": item.difference_qty,
                                    "lot_code": item.pch,
                                    "material_name": item.material,
                                    "touliao_num": item.difference_qty
                                }
                            });
                        }
                        that.random_key = Math.random();
                    });
                } else {
                    // 可查看
                    let that = this;
                    that.titleName = val.product_id + '—' + val.process_id + '(仅查看)';
                    axios.request({
                        url: "/roke/check_staging_order_detail",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            'model_index': 'roke.work.order',
                            'work_desk_index': 'gongdanbaogong',
                            'order_id': that.order_id,
                            'file_type': ''
                        }
                    }).then((res) => {
                        // that.file_list = res.data.result.result.file_url;
                        that.read_employee = res.data.result.result.xdy_data.employee.map(item => {
                            return {
                                name: item.name
                            }
                        });
                        that.read_collection_list = res.data.result.result.xdy_data.collection_result_list.map(item => {
                            return {
                                id: item.id,
                                name: item.name,
                                result: item.result
                            }
                        });
                        that.read_collection_list.map(item => {
                            let type = Object.prototype.toString.call(item.result);
                            if (type === '[object Array]') {
                                item.result = item.result.join('，');
                            }
                        });
                        that.read_team = res.data.result.result.xdy_data.team.name;
                        that.read_workcenter = res.data.result.result.xdy_data.work_center.name;
                        that.order_detail = res.data.result.result.xdy_data;
                        that.xdy_title = res.data.result.result.xdy_title;
                        that.xdy_title.map(item => {
                            if ((item.ttype === 'many2one' || item.ttype === 'one2many' || item.ttype === 'many2many') && !(item.field_readonly)) {
                                axios.request({
                                    url: "/roke/get_staging_base_data",
                                    method: "post",
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    data: {
                                        'model_index': item.relation,
                                        'search_str': '',
                                        'category_id': '',
                                        'page_no': 1,
                                        'page_size': 20
                                    }
                                }).then((res) => {
                                    if (res.data.result.state === 'success') {
                                        item.select_vals = res.data.result.record_list;
                                    } else if (res.data.result.state === 'error') {
                                        that.$message({
                                            type: 'warning',
                                            message: res.data.result.msgs
                                        });
                                    } else {
                                        that.$message({
                                            type: 'warning',
                                            message: '暂无数据！'
                                        });
                                    }
                                });
                            }
                        });
                        that.file_list = res.data.result.result.xdy_data.file_url[0];
                        that.material_record_list = res.data.result.result.xdy_data.material_record_list;
                        that.material_demand_list = res.data.result.result.xdy_data.material_demand_list;
                        that.allow_input_material = res.data.result.result.xdy_data.allow_input_material;
                        that.by_reasons = res.data.result.result.xdy_data.by_products;
                        that.byshow = res.data.result.result.xdy_data.allow_by_product;
                        that.gdxq = true;
                        that.task_type = res.data.result.result.xdy_data.task_type;
                        that.allow = res.data.result.result.xdy_data.allow;
                        that.allow_qty = res.data.result.result.xdy_data.allow_qty;
                        that.plan_qty = res.data.result.result.xdy_data.plan_qty;
                        that.collection_list = res.data.result.result.xdy_data.collection_list;
                        if (that.collection_list.length > 0) {
                            that.collection_list.map((item) => {
                                if (item.data_type === '多选') {
                                    that.cj_selection_options = item.multiple_items;
                                } else if (item.data_type === '单选') {
                                    that.cj_options = item.single_items;
                                }
                            })
                        }
                    });
                }
            },
            // 可报工弹窗多人报工表格选择项发生变化时触发事件
            handleGoodsChange(val) {
                let list = [];
                val.map(item => {
                    list.push({'employee_id': item.id, 'weighted': item.weight});
                });
                this.employee_allot_list = list;
            },
            // 保留两位小数
            fun(val) {
                return val.toFixed(2)
            },
            // 权重计算
            changeWeight(newVal, oldVal, val) {
                if (newVal > oldVal) {
                    this.allWeight = this.allWeight + (newVal - oldVal);
                } else if (newVal < oldVal) {
                    this.allWeight = this.allWeight - (oldVal - newVal);
                }
            },
            //清除数据
            clean_data() {
                this.gdxq = false;
                this.activeIndex = '2';
                this.bf_checkList = [];
                this.fx_checkList = [];
                this.by_checkList = [];
                this.scrap_reason = [];
                this.repair_reason = [];
                this.bg_fxs = 0;
                this.bg_bfs = 0;
                this.bg_bhgs = 0;
                this.bg_hgs = 0;
                this.bg_gss = 0;
                this.no_order_cp = '';
                this.no_order_gx = '';
                this.order_detail = [];
                this.no_orderFlag = false;
                this.scrapReason = [];
                this.repairReason = [];
                this.scrapReasons = [];
                this.repair_reasons = [];
                this.by_reasons = [];
                this.byProduct = [];
                this.byProductList = [];
                this.ddInput = '';
                this.ddInput1 = '';
                this.ddInput2 = '';
                this.ddorderId = '';
                this.dd = false;
                this.ddId = '';
                this.compareHeader = [];
                this.compareList = [];
                this.ddVisible = false;
                this.ddkehu = '';
                this.feedingList = [];
                this.tl_checkList = [];
                this.pch = '';
                this.wl_id = '';
                this.order_id = '';
                this.collection_list = [];
                this.file_list = [];
                this.ddKehu1 = '';
                this.no_order_scdd = '';
                this.no_order_xsdd = '';
                this.dialog_key = false;
            },
            // 筛选事件
            searchFilter() {
                let that = this;
                if (that.checkFlag) {
                    // 报工记录
                    let list = {};
                    that.jl_record_filter_data.map(item => {
                        list[item.field_index] = item.value;
                    });
                    that.jl_filter_list = list;
                    that.currentPagejl = 1;
                    that.baogongjilu()
                } else {
                    // 工单报工
                    let list = {};
                    that.record_filter_data.map(item => {
                        list[item.field_index] = item.value;
                    });
                    that.filter_list = list;
                    that.currentPage = 1;
                    that.getList()
                }
            },
            // 顶部搜索框input事件
            input_event(e) {
                const that = this;
                if (e === '' || e === undefined) {
                    this.getList(1)
                } else {
                    if (that.statu !== null) {
                        clearTimeout(that.statu)
                    }
                    that.statu = setTimeout(() => {
                        that.enterCode(); // 调用写好的方法
                    }, 1000)
                }
            },
            // 顶部搜索框enter事件
            enterCode() {
                if (this.checkFlag) {
                    // 报工记录
                    if (this.jl_record_list_data.length > 0 && this.search.length > 0) {
                        let that = this;
                        axios.request({
                            url: "/roke/get_staging_order_list",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'model_index': 'roke.work.record',
                                'work_desk_index': 'baogongjilu',
                                'domain_dict': that.filter_list,
                                'code': that.search,
                                'search_type': '工单',
                                'page_no': 1,
                                'page_size': 10
                            }
                        }).then((res) => {
                            if (res.data.result.state === 'success') {
                                that.total = res.data.result.total_number;
                                that.topName = res.data.result.top_title;
                                that.jl_record_list_data = res.data.result.record_list_data;
                                that.jl_record_top_title = res.data.result.record_top_title;
                                that.jl_record_filter_data = res.data.result.record_filter_data;
                                that.jl_record_filter_data.map((item, index) => {
                                    item.chanpinpageNum = 1;
                                    if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                                        axios.request({
                                            url: "/roke/get_general_base_data",
                                            method: "post",
                                            headers: {
                                                'Content-Type': 'application/json'
                                            },
                                            data: {
                                                "ttype": item.ttype,
                                                "page_no": 1,
                                                "page_size": 20,
                                                "model_index": item.relation,
                                                "domain_data": '',
                                                "selection_mode": ''
                                            }
                                        }).then((res) => {
                                            if (res.data.result.state === 'success') {
                                                item.record_list = res.data.result.record_list;
                                            } else if (res.data.result.state === 'error') {
                                                that.$message({
                                                    type: 'error',
                                                    message: res.data.result.msgs
                                                });
                                            } else if (res.data.error) {
                                                that.$message({
                                                    type: 'error',
                                                    message: res.data.error.message
                                                });
                                            }
                                        });
                                    }
                                });
                                that.gd_random_key = Math.random();
                            } else {
                                that.$message({
                                    type: 'error',
                                    message: '未查到该数据！'
                                });
                                setTimeout(that.baogongjilu(), 1000);
                            }
                        });
                    } else {
                        this.baogongjilu();
                    }
                } else {
                    // 工单报工
                    if (this.record_list_data.length > 0 && this.search.length > 0) {
                        let that = this;
                        axios.request({
                            url: "/roke/get_staging_order_list",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'model_index': 'roke.work.order',
                                'work_desk_index': 'gongdanbaogong',
                                'domain_dict': {'state': '未完工'},
                                'code': that.search,
                                'search_type': '工单',
                                'page_no': 1,
                                'page_size': 10
                            }
                        }).then((res) => {
                            if (res.data.result.state === 'success') {
                                that.total = res.data.result.total_number;
                                that.topName = res.data.result.top_title;
                                that.record_list_data = res.data.result.record_list_data;
                                that.record_top_title = res.data.result.record_top_title;
                                that.record_filter_data = res.data.result.record_filter_data;
                                that.record_filter_data.map((item, index) => {
                                    item.chanpinpageNum = 1;
                                    if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                                        axios.request({
                                            url: "/roke/get_general_base_data",
                                            method: "post",
                                            headers: {
                                                'Content-Type': 'application/json'
                                            },
                                            data: {
                                                "ttype": item.ttype,
                                                "page_no": 1,
                                                "page_size": 20,
                                                "model_index": item.relation,
                                                "domain_data": '',
                                                "selection_mode": ''
                                            }
                                        }).then((res) => {
                                            if (res.data.result.state === 'success') {
                                                item.record_list = res.data.result.record_list;
                                            } else if (res.data.result.state === 'error') {
                                                that.$message({
                                                    type: 'error',
                                                    message: res.data.result.msgs
                                                });
                                            } else if (res.data.error) {
                                                that.$message({
                                                    type: 'error',
                                                    message: res.data.error.message
                                                });
                                            }
                                        });
                                    }
                                });
                                that.gd_random_key = Math.random();
                            } else {
                                that.$message({
                                    type: 'error',
                                    message: '未查到该数据！'
                                });
                                setTimeout(that.getList(), 1000);
                            }
                        });
                    } else {
                        this.getList();
                    }
                }
            },
            // 开工事件
            start() {
                let that = this;
                axios.request({
                    url: "/roke/mes/work_start",
                    method: "get",
                }).then((res) => {
                    if (res.data.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.msgs
                        });
                    } else {
                        // that.$message({
                        //     type: 'success',
                        //     message: res.data.msgs
                        // });
                        that.startedFlag = true;
                    }
                });
            },
            // 停工事件
            stop() {
                let that = this;
                axios.request({
                    url: "/roke/mes/work_stop",
                    method: "get",
                }).then((res) => {
                    if (res.data.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.msgs
                        });
                    } else {
                        // that.$message({
                        //     type: 'success',
                        //     message: res.data.msgs
                        // });
                        that.startedFlag = false;
                    }
                });
            },
            // 退出事件
            exit() {
                window.opener = null;
                window.open('', '_self');
                window.close();
            },
            // 报工事件
            reportSubmit() {
                let that = this;
                let collection_list = [];
                let rt = [];
                that.xdy_title.map(item => {
                    if (item.field_name === 'employee_ids' && item.value_id) {
                        item.value_id.map(items => {
                            for (var i = 0; i < item.select_vals.length; i++) {
                                if (item.select_vals[i].id === items) {
                                    rt.push(item.select_vals[i]);
                                }
                            }
                        });
                    }
                });
                that.reportData_value = rt;
                let allow_key = true;
                for (var i = 0; i < that.reportData_value.length; i++) {
                    if (typeof that.reportData_value[i] === 'string') {
                        for (var k = 0; k < that.reportData.length; k++) {
                            if (that.reportData_value[i] === that.reportData[k].name) {
                                that.reportData_value[i] = that.reportData[k].id;
                            }
                        }
                    }
                }
                // for (var t = 0; t < that.Workcenter.length; t++) {
                //     if (that.Workcenter[t].name === that.workCenter_value) {
                //         that.workCenter_value = that.Workcenter[t].id;
                //     }
                // }
                // console.log(that.xdy_title)
                that.xdy_title.forEach(item => {
                    if (item.field_name == 'work_center_id') {
                        that.workCenter_value = item.value_id
                    }
                })
                that.xdy_title.forEach(item => {
                    if (item.field_name == 'team_id') {
                        that.Information_value = item.value_id
                    }
                })
                // console.log(that.Information)
                // for (var q = 0; q < that.Information.length; q++) {
                //     if (that.Information[q].name === that.Information_value) {
                //         that.Information_value = that.Information[q].id;
                //     }
                // }
                that.collection_list.map((item) => {
                    if (item.data_type === '小数') {
                        collection_list.push({'item_id': item.id, 'result': item.cj_number})
                    } else if (item.data_type === '多选') {
                        collection_list.push({'item_id': item.id, 'result': item.cj_selection})
                    } else if (item.data_type === '单选') {
                        collection_list.push({'item_id': item.id, 'result': item.cj_select})
                    } else if (item.data_type === '整数') {
                        collection_list.push({'item_id': item.id, 'result': item.cj_input_num})
                    } else if (item.data_type === '文本') {
                        collection_list.push({'item_id': item.id, 'result': item.cj_input})
                    }
                });
                that.bf_checkList = that.scrap_reason.map((item) => {
                    return {
                        'reason_id': item.id,
                        'qty': item.bf_num,
                        'note': item.bf_reason
                    }
                });
                for (var d = 0; d < that.bf_checkList.length; d++) {
                    if (that.bf_checkList[d].qty === undefined) {
                        that.bf_checkList[d].qty = 0;
                    }
                }
                that.fx_checkList = that.repair_reason.map((item) => {
                    return {
                        'reason_id': item.id,
                        'qty': item.fx_num,
                        'note': item.fx_reason
                    }
                });
                for (var v = 0; v < that.fx_checkList.length; v++) {
                    if (that.fx_checkList[v].qty === undefined) {
                        that.fx_checkList[v].qty = 0;
                    }
                }
                that.by_checkList = that.by_reasons.map((item) => {
                    return {
                        'product_id': item.product_id,
                        'qty': item.by_num,
                    }
                });
                for (var e = 0; e < that.by_checkList.length; e++) {
                    if (that.by_checkList[e].qty === undefined) {
                        that.by_checkList[e].qty = 0;
                    }
                }
                that.tl_checkList = that.feedingList.map((item) => {
                    return {
                        'material_id': item.material_id,
                        'qty': item.touliao_num,
                        'lot_code': item.lot_code
                    }
                })
                for (var t = 0; t < that.tl_checkList.length; t++) {
                    if (that.tl_checkList[t].qty === undefined) {
                        return that.$message({
                            type: 'warning',
                            message: '请填写投料数！'
                        });
                    }
                }
                // 判断是否满足报工条件
                if (that.reportData_value.length < 1) {
                    // 判断是否选择人员
                    that.$message({
                        type: 'warning',
                        message: '请选择人员！'
                    });
                    allow_key = false;
                } else if (that.bg_bhgs !== that.bg_fxs + that.bg_bfs) {
                    // 判断不合格数是否等于报废数 + 返修数
                    that.$message({
                        type: 'error',
                        message: '报废数+返修数必须等于不合格数！',
                        duration: 2000
                    });
                    allow_key = false;
                } else if (that.bg_bhgs === 0 && that.bg_hgs === 0 && that.bg_gss === 0) {
                    // 判断不合格数、合格数和工时数是否同时为0
                    that.$message({
                        type: 'error',
                        message: '不合格数、合格数和工时数不可同时为0！',
                        duration: 2000
                    });
                    allow_key = false;
                } else if (that.workCenter_value === '' || that.workCenter_value === undefined && work_center_show) {
                    // 判断是否选择工作中心
                    that.$message({
                        type: 'warning',
                        message: '请选择工作中心！'
                    });
                    allow_key = false;
                } else if (that.material_demand_list.length > 0) {
                    // 判断是否选择投料信息
                    that.material_demand_list.map(item => {
                        if (item.must) {
                            if (that.feedingList.length < 1) {
                                that.$message({
                                    type: 'error',
                                    message: '请进行投料！',
                                    duration: 2000
                                });
                                allow_key = false;
                            } else {
                                let key = that.feedingList.find(items => items.material_id === item.material_id);
                                if (key === undefined) {
                                    that.$message({
                                        type: 'error',
                                        message: '请录入必填投料--' + item.material,
                                        duration: 2000
                                    });
                                    allow_key = false;
                                }
                            }
                        }
                    });
                }
                // this.bg_gss
                axios.request({
                        url: "/roke/mes/work_time/get",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {}
                    }).then((res) => {
                        if(res.data.result.code == 0){
                        that.bg_gss = res.data.result.work_hours
                            if (that.reportData_value.length === 1 && allow_key) {
                    // 单人报工
                    var ryList = [];
                    for (let i = 0; i < that.reportData_value.length; i++) {
                        let obj = {
                            employee_id: that.reportData_value[i].id,
                            weighted: 1
                        };
                        ryList.push(obj)
                    }
                    that.gdbg_loading = true;
                    axios.request({
                        url: "/roke/work_submit",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            "work_order_id": that.order_id,
                            "product_id": "",
                            "process_id": "",
                            "work_center_id": that.workCenter_value,
                            "team_id": that.Information_value,
                            "employee_list": ryList,
                            "finish_qty": that.bg_hgs,
                            "unqualified_qty": that.bg_bhgs,
                            "scrap_qty": that.bg_bfs,
                            "repair_qty": that.bg_fxs,
                            "work_hours": that.bg_gss,
                            "collection_list": collection_list,
                            "scrap_list": that.bf_checkList,
                            "repair_list": that.fx_checkList,
                            "report_time": that.dateVal,
                            "next_process_id": that.next_process_id,
                            "move_qty": that.move_qty,
                            "note": that.report_note,
                            "by_products": that.by_checkList,
                            "material_list": that.tl_checkList
                        }
                    }).then((res) => {
                        that.gdbg_loading = false;
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs,
                                duration: 5000
                            });
                        } else {
                            that.$message({
                                type: 'success',
                                message: res.data.result.msgs
                            });
                            that.search = '';
                            that.clean_data();
                            that.record_filter_data.map(item => {
                                if (item.field_description === '状态') {
                                    item.value = '未完工'
                                }
                            });
                            that.stop()
                            that.getList();
                        }
                    });
                } else if (that.reportData_value.length > 1 && allow_key) {
                    // 多人报工
                    var ryList = []
                    for (let i = 0; i < that.reportData_value.length; i++) {
                        let obj = {
                            employee_id: that.reportData_value[i].id,
                            weighted: 1
                        }
                        ryList.push(obj)
                    }
                    that.gdbg_loading = true;
                    axios.request({
                        url: "/roke/work_submit",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            "work_order_id": that.order_id,
                            "product_id": "",
                            "process_id": "",
                            "work_center_id": that.workCenter_value,
                            "team_id": that.Information_value,
                            "finish_qty": that.bg_hgs,
                            "unqualified_qty": that.bg_bhgs,
                            "scrap_qty": that.bg_bfs,
                            "repair_qty": that.bg_fxs,
                            "work_hours": that.bg_gss,
                            "employee_list": ryList,
                            "collection_list": collection_list,
                            "scrap_list": that.bf_checkList,
                            "repair_list": that.fx_checkList,
                            "report_time": that.dateVal,
                            "next_process_id": that.next_process_id,
                            "move_qty": that.move_qty,
                            "note": that.report_note,
                            "by_products": that.by_checkList,
                            "material_list": that.tl_checkList
                        }
                    }).then((res) => {
                        that.gdbg_loading = false;
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs,
                                duration: 5000
                            });
                        } else {
                            that.$message({
                                type: 'success',
                                message: res.data.result.msgs
                            });
                            that.search = ''
                            that.clean_data();
                            that.record_filter_data.map(item => {
                                if (item.field_description === '状态') {
                                    item.value = '未完工'
                                }
                            });
                            that.stop()
                            that.getList();
                        }
                    });
                }
                        }else {
                            that.$message({
                                type: 'error',
                                message: '获取工时失败，报工失败'
                            });
                        }
                    });
            },
            search_gongxu_filter(val) {
                // console.log(val);
                //获取工序
                // let that = this;
                // console.log(val);
                // axios.request({
                //     url: "/roke/get_process_list",
                //     method: "post",
                //     headers: {
                //         'Content-Type': 'application/json'
                //     },
                //     data: {
                //         'process': val,
                //
                //     }
                // }).then(function (res) {
                //     // console.log(res);
                //     if (res.data.result.state === 'error') {
                //         that.$message({
                //             type: 'error',
                //             message: res.data.result.msgs
                //         });
                //     } else {
                //         that.no_order_gxList = res.data.result.result;
                //     }
                // });
                let resultList = [];
                this.no_order_gxList.forEach((item) => {
                    if (item.name.indexOf(val) > -1) {
                        resultList.push(item);
                    }
                });
                this.filter_no_order_gxList = resultList;
                // console.log(this.filter_no_order_gxList);
                if (val === undefined || val === '' || val === null) {
                    this.filter_no_order_gxList = this.no_order_gxList;
                }
            },
            search_xsdd_filter(val) {
                let resultList = [];
                this.sale_order_lists.forEach((item) => {
                    if (item.name.indexOf(val) > -1) {
                        resultList.push(item);
                    }
                });
                this.filter_sale_order_lists = resultList;
                // console.log(this.filter_no_order_gxList);
                if (val === undefined || val === '' || val === null) {
                    this.filter_sale_order_lists = this.sale_order_lists;
                }
            },
            search_scdd_filter(val) {
                let resultList = [];
                this.production_order_order_lists.forEach((item) => {
                    if (item.name.indexOf(val) > -1) {
                        resultList.push(item);
                    }
                });
                this.filter_production_order_lists = resultList;
                // console.log(this.filter_no_order_gxList);
                if (val === undefined || val === '' || val === null) {
                    this.filter_production_order_lists = this.production_order_order_lists;
                }
            },
            gongxu_filter(val) {
                let that = this
                console.log(11111);
                let sta = {}
                if(val == '初始化'){
sta = {
                        'process': '',
                        'page_size': that.gongxupageSize,
                        "product_id": that.no_order_cp
                    }
                }else {
                    sta = {
                        'process': '',
                        "page_no": val,
                        'page_size': that.gongxupageSize,
                        "product_id": that.no_order_cp
                    }
                }
                //获取工序
                axios.request({
                    url: "/roke/get_process_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: sta
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.gongxuList = [...that.gongxuList, ...res.data.result.result];
                        if ((that.gongxuList.length === res.data.result.total_number)) {
                            that.datations_Gongxu = '到底啦'
                        }
                        that.no_order_gxList = [...that.no_order_gxList, ...res.data.result.result];
                        if ((that.no_order_gxList.length === res.data.result.total_number)) {
                            that.datations_Gongxu = '到底啦'
                        }
                        if(val == '初始化'){
                            that.gongxuList.forEach((item) => {
                              if (item.name.includes('切割')) {
                                  that.default_process_id = item.id
                                  that.gongxuList.length = 0
                                  return
                              }
                             });
                            that.getPageAllDataApi(that.default_process_id)
                        }
                    }
                });
            },
            // 自定义搜索工序
            gongxuChange(val) {
                //获取工序
                let that = this;
                axios.request({
                    url: "/roke/get_process_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'process': val,
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.gongxuList = res.data.result.result;
                        that.no_order_gxList = res.data.result.result;
                        that.processSels = res.data.result.result
                    }
                });
            },
            //获取筛选条件
            chanpin_filter(val1) {
                let that = this;
                //获取产品
                axios.request({
                    url: "/roke/get_product_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'product': '',
                        "page_no": val1,
                        "page_size": that.chanpinpageSize
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.chanpinList = [...that.chanpinList, ...res.data.result.result];
                        if ((that.chanpinList.length == res.data.result.total_number)) {
                            that.datations = '到底啦'
                        }
                        that.no_order_cpList = [...that.no_order_cpList, ...res.data.result.result];
                        // console.log(that.no_order_cpList);
                        if ((that.no_order_cpList.length == res.data.result.total_number)) {
                            that.datations = '到底啦'
                        }
                    }
                });
            },
            zdy_filter(num, val) {
                console.log(num,val);
                //滚动加载
                let that = this;
                if (!that.checkFlag) {
                    // that.record_filter_data.map((item, index) => {
                    //     if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                            // console.log(item);
                            axios.request({
                                url: "/roke/get_general_base_data",
                                method: "post",
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                data: {
                                    "ttype": val.ttype,
                                    "page_no": num,
                                    "page_size": 20,
                                    "model_index": val.relation,
                                    "domain_data": '',
                                    "selection_mode": ''
                                }
                            }).then((res) => {
                                if (res.data.result.state === 'success') {
                                    // if (item.record_list && res.data.result.record_list != []) {
                                    //     item.record_list = [...item.record_list, ...res.data.result.record_list];
                                    // }
                                    if (val.select_vals && res.data.result.record_list != []) {
                                        val.select_vals = [...val.select_vals, ...res.data.result.record_list];
                                    }

                                    // console.log(item);
                                    if (num === res.data.result.total_page) {
                                        val.zdy_datations = '到底啦';
                                    }
                                } else if (res.data.result.state === 'error') {
                                    that.$message({
                                        type: 'error',
                                        message: res.data.result.msgs
                                    });
                                } else if (res.data.error) {
                                    that.$message({
                                        type: 'error',
                                        message: res.data.error.message
                                    });
                                }
                            });
                        // }
                    // });
                } else {
                    that.jl_record_filter_data.map((item, index) => {
                        if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                            // console.log(item);
                            axios.request({
                                url: "/roke/get_general_base_data",
                                method: "post",
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                data: {
                                    "ttype": item.ttype,
                                    "page_no": num,
                                    "page_size": 20,
                                    "model_index": item.relation,
                                    "domain_data": '',
                                    "selection_mode": ''
                                }
                            }).then((res) => {
                                console.log(res);
                                if (res.data.result.state === 'success') {
                                    if (item.record_list && res.data.result.record_list != []) {
                                        item.record_list = [...item.record_list, ...res.data.result.record_list];
                                    }
                                    if (val.select_vals && res.data.result.record_list != []) {
                                        val.select_vals = [...val.select_vals, ...res.data.result.record_list];
                                    }
                                    // console.log(item);
                                    if (num === res.data.result.total_page) {
                                        val.zdy_datations = '到底啦';
                                    }
                                } else if (res.data.result.state === 'error') {
                                    that.$message({
                                        type: 'error',
                                        message: res.data.result.msgs
                                    });
                                } else if (res.data.error) {
                                    that.$message({
                                        type: 'error',
                                        message: res.data.error.message
                                    });
                                }
                            });
                        }
                    });
                }

            },
            // 自定义搜索产品
            chanpinChange(val) {
                // console.log(val);
                let that = this;
                //获取产品
                axios.request({
                    url: "/roke/get_product_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'product': val,
                    }
                }).then(function (res) {
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        // console.log(res);
                        that.chanpinList = res.data.result.result;
                        that.no_order_cpList = res.data.result.result;
                        that.productSel = res.data.result.result
                    }
                });
            },
            kehu_filter(val) {
                let that = this;
                //获取客户
                axios.request({
                    url: "/roke/get_customer_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'customer': '',
                        "page_no": val,
                        'page_size': that.kehupageSize
                    }
                }).then(function (res) {
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.kehuList = [...that.kehuList, ...res.data.result.result];
                        if ((that.kehuList.length == res.data.result.total_number)) {
                            that.datations_Kehu = '到底啦'
                        }
                    }
                });
            },
            // 自定义搜索客户
            kehuChange(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_customer_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'customer': val,
                    }
                }).then((res) => {
                    that.kehuList = res.data.result.result
                });
            },
            // 无工单报工选择产品接口
            noClick() {
                let that = this;
                // console.log(that.no_order_cp);
                axios.request({
                    url: "/roke/get_process_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        "product_id": that.no_order_cp
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.no_order_gxList = res.data.result.result;
                        that.filter_no_order_gxList = that.no_order_gxList;
                        that.no_order_gx = res.data.result.result[0].id;
                        that.no_order_gx_name = res.data.result.result[0].name;
                        if (that.activeName_gx === '0') {
                            that.processSel = that.no_order_gxList;
                        }
                        that.setSalaryRule()
                    }
                });
            },
            handleSelect(key, keyPath) {
                // console.log(key);
                this.activeIndex = key;
                if (key === '9') {

                }
            },
            setValues(val) {
                let that = this;
                if (that.reportData_value.length < 1 && that.reportData_value == '' && that.reportData_value == undefined) {
                    axios.request({
                        url: "/roke/get_employee_list",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {}
                    }).then(function (res) {
                        let arr = []
                        console.log(res);
                        that.reportData = [...arr, ...res.data.result.result]
                        if ((that.reportData.length == res.data.result.total_number)) {
                            that.datations_ry = '到底啦'
                        }
                    });
                } else {
                    let list = [];
                    for (var k = 0; k < val.length; k++) {
                        for (var i = 0; i < that.reportData.length; i++) {
                            that.reportData[i].weight = 1;
                            that.reportData[i].tableData = []
                            // console.log(this.reportData[i]);
                            if (that.reportData[i].id === val[k] || that.reportData[i].name === val[k]) {
                                list.push(that.reportData[i]);
                                that.Information_value = that.reportData[i].team_id;
                            }
                        }
                    }
                    that.manyWokers = list;
                    that.allWeight = that.manyWokers.length;
                    console.log();
                    // if (val.length === 1) {
                    //     that.manyWokers = [];
                    // }
                }
            },
            setreasons(val) {
                // console.log(val)
                let list = [];
                for (var k = 0; k < val.length; k++) {
                    for (var i = 0; i < this.scrap_reason.length; i++) {
                        if (this.scrap_reason[i].id === val[k] || this.scrap_reason[i].name === val[k]) {
                            list.push(this.scrap_reason[i]);
                        }
                    }
                }
                this.scrapReasons = list;
                if (val.length < 1) {
                    this.scrapReasons = [];
                }
            },
            setfx(val) {
                let list = [];
                for (var k = 0; k < val.length; k++) {
                    for (var i = 0; i < this.repair_reason.length; i++) {
                        if (this.repair_reason[i].id === val[k] || this.repair_reason[i].name === val[k]) {
                            list.push(this.repair_reason[i]);
                        }
                    }
                }
                this.repair_reasons = list;
                if (val.length < 1) {
                    this.repair_reasons = [];
                }
            },
            setby(val) {
                console.log(val)
                let list = [];
                for (var k = 0; k < val.length; k++) {
                    for (var i = 0; i < this.by_reasons.length; i++) {
                        if (this.by_reasons[i].product_id === val[k] || this.by_reasons[i].product_name === val[k]) {
                            list.push(this.by_reasons[i]);
                        }
                    }
                }
                this.byProductList = list;
                if (val.length < 1) {
                    this.byProductList = [];
                }
            },
            //无工单报工
            no_order() {
                this.no_orderFlag = true;
                this.gdxq = true;
                this.allow = true;
                this.plan_qty = 0;
                this.allow_qty = 0;
                this.move_qty = 0;
                this.titleName = '无工单报工';
                // // 人员
                // this.getDatas()
                // // 报废原因
                // this.getbfDatas()
                // // 返修原因
                // this.getfxDates()
                // // 工作中心
                // this.getgzzxDates()
                // // 班组
                // this.getbzDates()
                // console.log(this.scrap_reason)
                this.order_id = ''
            },
            //无工单设置计薪规则
            setSalaryRule() {
                let that = this;
                axios.request({
                    url: "/roke/process_get_salary_rule",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'process_id': that.no_order_gx
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        let list = {
                            'salary_rule': res.data.result.salary_rule,
                            'salary_type': res.data.result.salary_type
                        };
                        that.order_detail = list;
                        // 副产品
                        axios.request({
                            url: "/roke/process_by_product",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'process_id': that.no_order_gx
                            }
                        }).then(function (res) {
                            // console.log(res);
                            that.by_reasons = res.data.result.by_products;
                            that.byshow = res.data.result.allow_by_product
                        });
                        // console.log(that.order_detail);
                    }
                });

                //无工单选工序后获取采集项
                axios.request({
                    url: "/roke/no_order_get_process_info",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'process_id': that.no_order_gx,
                        'product_id': that.no_order_cp
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        // that.$message({
                        //     type: 'success',
                        //     message: res.data.result.msgs
                        // });
                        that.collection_list = res.data.result.result.collection_list;
                        that.file_list = res.data.result.result.file_url[0];
                        that.no_order_standard_list = res.data.result.result.standard_list;
                    }
                });
            },
            // 获取报工记录数据
            baogongjilu(page) {
                let that = this;
                that.checkFlag = true;
                axios.request({
                    url: "/roke/get_staging_order_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'model_index': 'roke.work.record',
                        'work_desk_index': 'baogongjilu',
                        'domain_dict': that.jl_filter_list,
                        'code': '',
                        'search_type': '',
                        'page_no': 1,
                        'page_size': 10
                    }
                }).then((res) => {
                    if (res.data.result.state === 'success') {
                        that.total = res.data.result.total_number;
                        that.topName = res.data.result.top_title;
                        that.jl_record_list_data = res.data.result.record_list_data;
                        that.jl_record_top_title = res.data.result.record_top_title;
                        that.jl_record_filter_data = res.data.result.record_filter_data;
                        that.jl_record_filter_data.map((item, index) => {
                            item.chanpinpageNum = 1;
                            if (item.ttype === 'many2one' || item.ttype === 'many2many' || item.ttype === 'one2many') {
                                axios.request({
                                    url: "/roke/get_general_base_data",
                                    method: "post",
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    data: {
                                        "ttype": item.ttype,
                                        "page_no": 1,
                                        "page_size": 20,
                                        "model_index": item.relation,
                                        "domain_data": '',
                                        "selection_mode": ''
                                    }
                                }).then((res) => {
                                    if (res.data.result.state === 'success') {
                                        item.record_list = res.data.result.record_list;
                                    } else if (res.data.result.state === 'error') {
                                        that.$message({
                                            type: 'error',
                                            message: res.data.result.msgs
                                        });
                                    } else if (res.data.error) {
                                        that.$message({
                                            type: 'error',
                                            message: res.data.error.message
                                        });
                                    }
                                });
                            }
                        });
                    } else {
                        that.$message({
                            type: 'error',
                            message: '未查到该数据！'
                        });
                    }
                });
            },
            go_back() {
                this.checkFlag = false;
                this.currentPage = 1;
                this.currentPagejl = 1
                this.search = '';
                this.getList();
            },
            //当日绩效
            dangrijixiao() {
                let that = this;
                axios.request({
                    url: "/roke/get_toady_totals",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {}
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        // that.$message({
                        //     type: 'success',
                        //     message: res.data.result.msgs
                        // });
                        that.jx_msg = res.data.result;
                        that.drjx = true;
                        // console.log(that.checkRecords);
                    }
                });
            },
            //查看详情
            check_details() {
                this.drjx = false;
                this.baogongjilu();
            },
            //使用帮助
            shiyongbangzhu() {
                window.open('https://xbg-doc.rokedata.com/');
            },
            //撤销报工
            withdraw(val) {
                let that = this;
                // console.log(val);
                axios.request({
                    url: "/roke/work_withdraw",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'work_record_id': val.id
                    }
                }).then(function (res) {
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        // that.$message({
                        //     type: 'success',
                        //     message: res.data.result.msgs
                        // });
                        that.baogongjilu();
                        // console.log(that.checkRecords);
                    }
                });
            },
            //合格数改变流转数
            set_move_qty(val) {
                this.move_qty = val;
            },
            //根据批次号获取投料信息
            getDetails() {
                let that = this;
                if (that.pch.length < 1) {
                    return
                } else {
                    axios.request({
                        url: "/roke/get_input_material_info",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            "work_order_id": that.order_id,
                            "lot_code": that.pch,
                            "process_id": that.no_order_gx
                        }
                    }).then(function (res) {
                        // console.log(res);
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs
                            });
                        } else {
                            that.touliao_num = res.data.result.default_qty;
                            that.demand_qty = res.data.result.demand_qty;
                            that.sys_qty = res.data.result.sys_qty;
                            that.chanpinList.map((item) => {
                                if (item.id === res.data.result.material_id) {
                                    that.wl_id = res.data.result.material_id;
                                }
                            })
                            if (res.data.result.material_id == '') {
                                that.$message({
                                    type: 'error',
                                    message: '当前批次号没有查询到物料'
                                });
                                return
                            } else {
                                that.feedingList.push({
                                    'material_id': res.data.result.material_id,
                                    "qty": that.touliao_num,
                                    "lot_code": that.pch,
                                    "material_name": res.data.result.material_name,
                                    "touliao_num": that.touliao_num
                                });
                                // console.log(that.feedingList)
                            }

                        }
                    });
                }
            },
            toulCheck(val) {
                this.touliao_num = val
                // console.log(this.feedingList)
            },
            //选择物料设置投料数量
            set_touliao_num(val) {
                let that = this;
                axios.request({
                    url: "/roke/get_input_material_info",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        "work_order_id": that.order_id,
                        "material_id": that.wl_id,
                        "process_id": that.no_order_gx
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.touliao_num = res.data.result.default_qty;
                        that.demand_qty = res.data.result.demand_qty;
                        that.sys_qty = res.data.result.sys_qty;
                        let selectedName = {};
                        selectedName = that.chanpinList.find((item) => {
                            return item.id === val;
                            //筛选出匹配数据，是对应数据的整个对象
                        });
                        let touname = ''
                        touname = selectedName.name
                        // console.log(selectedName)
                        that.feedingList.push({
                            'material_id': res.data.result.material_id,
                            "qty": that.touliao_num,
                            "lot_code": that.pch,
                            "material_name": touname,
                            "touliao_num": that.touliao_num
                        });
                        // console.log(that.feedingList)
                    }
                });
            },
            // 投料删除
            wlhandleClick(row) {
                // console.log(row);
                this.feedingList.some((item, i) => {
                    if (item.material_id === row.material_id) {
                        this.feedingList.splice(i, 1)
                    }
                })
            },
            //投料撤回
            deleteRow(val) {
                // console.log(val);
                let that = this;
                this.$confirm('确认撤销？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    axios.request({
                        url: "/roke/withdraw_material",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            "material_record_id": val.id,
                        }
                    }).then(function (res) {
                        // console.log(res);
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs
                            });
                        } else {
                            // that.$message({
                            //     type: 'success',
                            //     message: res.data.result.msgs
                            // });
                            that.material_record_list = that.material_record_list.filter((item) => {
                                return item.id !== val.id;
                            });
                        }
                    });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    });
                });
            },
            // 班组改变获取班组下人员
            personnelClick(val) {
                let that = this;
                this.reportDataValue = '';
                that.userEdit.team_id = val;
                that.personnelText = val;
                that.manyWokersTian = [];
                if (val === '') {
                    that.getbzDates();
                }
                if (that.Information_value.length < 1 && that.Information_value === '') {
                    axios.request({
                        url: "/roke/get_employee_list",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {}
                    }).then(function (res) {
                        let arr = [];
                        that.reportData = [...arr, ...res.data.result.result];
                        // console.log(that.reportData);
                        if ((that.reportData.length === res.data.result.total_number)) {
                            that.datations_ry = '到底啦'
                        }
                    });
                } else {
                    axios.request({
                        url: "/roke/get_employee_list",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            "team_id": val,
                        }
                    }).then(function (res) {
                        // console.log(res);
                        let arr = [];
                        that.reportData = [...arr, ...res.data.result.result];
                        // console.log(that.reportData);
                        if ((that.reportData.length === res.data.result.total_number)) {
                            that.datations_ry = '到底啦'
                        }
                        that.reportData_value = ''
                    });
                }
            },
            //工单报工和无工单报工查看作业指导pdf
            checkout_pdf(val) {
                //console.log(val);
                let key = val.name.substring(val.name.lastIndexOf(".")).toLowerCase();
                //console.log(key);
                if (key === '.dwg' || key === '.dxf') {
                    window.open('/roke/extra/preview?att_id=' + val.id);
                } else if (key === '.step' || key === '.sldprt' || key === '.sldasm') {
                    window.open('/roke/extra/preview/3d?att_id=' + val.id);
                } else {
                    window.open(val.data);
                }
            },
            //打开选择产品弹窗
            open_check_cp() {
                // console.log(this.no_order_cp_name);
                this.choose_cp_list = true;
                let that = this;
                axios.request({
                    url: "/roke/get_product_list",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'category_id': that.cptablist[0].id
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'success') {
                        let list = [];
                        res.data.result.result.map((item) => {
                            list.push({
                                id: item.id,
                                name: item.name,
                                style: ""
                            })
                        });
                        that.productsList = list;
                        that.load_ing = false;
                        that.check_cp_level();
                    } else {
                        that.$message({
                            type: 'warning',
                            message: '暂无数据！'
                        });
                        that.load_ing = false;
                    }

                });
            },
            //获取产品分类
            get_category() {
                let that = this;
                axios.request({
                    url: "/roke/get_product_category",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {}
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'success') {
                        res.data.result.result.map((item, index) => {
                            if (item.child_category.length > 0) {
                                that.cptablist[index] = {
                                    id: item.id,
                                    name: item.name,
                                    child_category: item.child_category,
                                    style: "",
                                    badge: {
                                        isDot: true
                                    }
                                }
                            } else {
                                that.cptablist[index] = {
                                    id: item.id,
                                    name: item.name,
                                    child_category: item.child_category,
                                    style: ""
                                }
                            }
                        });
                        that.cptablist.unshift({
                            id: "",
                            name: "订单产品",
                            child_category: [],
                            style: ""
                        });
                        // console.log(that.cptablist);
                    } else {
                        that.$message({
                            type: 'warning',
                            message: '暂无数据！'
                        });
                    }

                });
            },
            //选择产品类别
            handleClick(tab, event) {
                // console.log(tab.label);
                this.cptablist.map((item, index) => {
                    if (tab.label === item.name) {
                        let that = this;
                        axios.request({
                            url: "/roke/get_product_list",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'category_id': item.id
                            }
                        }).then(function (res) {
                            // console.log(res);
                            if (res.data.result.state === 'success') {
                                let list = [];
                                res.data.result.result.map((item) => {
                                    list.push({
                                        id: item.id,
                                        name: item.name,
                                        style: ""
                                    })
                                });
                                that.productsList = list;
                                that.check_cp_level();
                                // console.log(that.productsList);
                            } else {
                                that.$message({
                                    type: 'warning',
                                    message: '暂无数据！'
                                });
                            }

                        });
                    }
                })
            },
            //判断产品是否分级getDatas
            check_cp_level() {
                this.tabindex = this.activeName;
                this.index = this.activeName;
                if (this.tabindex !== 0) {
                    // console.log(this.cptablist[this.tabindex]);
                    this.cp_child_category = this.cptablist[this.tabindex].child_category;
                    this.cp_category_id = this.cptablist[this.tabindex].id;
                    // if(this.cp_child_category.length > 0){
                    this.cp_child_category.map(res => {
                        res.style = ""
                    });
                    // }
                    if (this.cp_child === false && this.cptablist[this.tabindex].child_category.length > 0) {
                        this.cp_child = true;
                        // console.log(this.child);
                        let list = this.cptablist;
                        this.cp_ids = [];
                        this.cp_ids.push(this.cptablist[this.tabindex].id);
                        this.cp_ids = Array.from(new Set(this.cp_ids))
                    } else if (this.cp_child === true) {
                        this.cp_child = false
                    } else if (this.cptablist[this.tabindex].child_category.length === 0) {
                        this.cp_child = false
                    }
                    this.handleClick(this.activeName)
                } else {
                    this.productsList = this.no_order_cpList;
                    this.cp_child = false;
                }
            },
            //选择产品
            choose_cp_item(val) {
                this.cp_loading = true;
                if (this.productsList.length) {
                    let len = Math.ceil(this.productsList.length / 20);
                    // console.log(len);
                    for (var i = 0; i < len; i++) {
                        this.loadMore();
                    }
                    let that = this;
                    setTimeout(function () {
                        that.productSel_flag = false;
                        that.secrch_cp_val = val.name;
                        that.wl_id = val.id;
                        that.choose_cp_list = false;
                        that.cp_true = true;
                        that.cp_loading = false;
                        that.set_touliao_num(val.id);
                    }, 1000);
                }
                ;
            },
            cp_pre() {
                let list = this.cptablist;
                let that = this;
                for (let i = 0; i <= this.cp_level - 1; i++) {
                    list.forEach(res => {
                        if (res.id === that.cp_ids[i]) {
                            list = res.child_category;
                            if (i === this.cp_level - 2) {
                                that.cp_child_category = res.child_category;
                                if (i !== 0) {
                                    that.cp_ids.splice(i)
                                }
                                // console.log(that.cp_ids);
                            }
                            // this.$forceUpdate()
                        }
                    })
                }
                this.cp_level--;
            },
            cp_next() {
                if (this.cp_ids.length > 1) {
                    this.cp_child_category = this.cp_nextchild
                }
                this.cp_child_category.map(res => {
                    res.style = ""
                })
            },
            close_cp_type() {
                this.cp_child = false;
            },
            //关闭产品弹窗
            cp_dialog_close() {
                this.secrch_cp_val = '';
                this.activeName = '0';
                if (this.no_order_cp === '') {
                    this.cp_true = false;
                }
                this.choose_cp_list = false;
                this.cp_child = false;
                this.choose_cp_list = false;
            },
            //自定义筛选单选弹窗
            open_dialog(val) {
                // console.log(val);
                let that = this;
                that.choose_zdy_list = true;
                that.zdy_load_ing = true;
                axios.request({
                    url: "/roke/get_staging_base_data",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'model_index': val.relation,
                        'search_str': '',
                        'category_id': '',
                        'page_no': '',
                        'page_size': ''
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.result.state === 'success') {
                        if (res.data.result.is_category) {
                            that.zdy_child = true;
                            that.zdy_cptablist = res.data.result.record_list.map(item => {
                                return item.category;
                            });
                            that.zdy_total_list = res.data.result.record_list;
                            that.zdy_List = that.zdy_total_list[0].data;
                        } else {
                            that.zdy_child = false;
                            that.zdy_List = res.data.result.record_list;
                        }
                        // that.check_zdy_level();
                        that.zdy_load_ing = false;
                    } else {
                        that.$message({
                            type: 'warning',
                            message: '暂无数据！'
                        });
                        that.zdy_load_ing = false;
                    }
                });
            },
            //自定义弹窗关闭
            zdy_dialog_close() {
                this.choose_zdy_list = false;
                this.zdy_cptablist = [];
                this.zdy_activeName = '';
                this.zdy_child = false;
                this.zdy_child_category = [];
                this.zdy_List = [];
            },
            zdy_handleClick(tab, event) {
                // console.log(tab);
                // console.log(this.zdy_total_list);
                this.zdy_total_list.map((item, index) => {
                    if (tab.label === item.category) {
                        this.zdy_List = item.data;
                    }
                })
            },
            zdy_pre() {

            },
            zdy_next() {

            },
            close_zdy_type() {

            },
            sel_zdy_category(item, index) {

            },
            choose_zdy_item(item, itemm) {
                console.log(item, itemm);
                item.value = itemm.id;
                this.zdy_dialog_close();
            },
            //判断是否分级
            check_zdy_level() {
                this.tabindex = this.zdy_activeName;
                this.index = this.zdy_activeName;
                if (this.tabindex !== 0) {
                    // console.log(this.cptablist[this.tabindex]);
                    this.zdy_child_category = this.zdy_cptablist[this.tabindex].child_category;
                    this.zdy_category_id = this.zdy_cptablist[this.tabindex].id;
                    // if(this.cp_child_category.length > 0){
                    this.zdy_child_category.map(res => {
                        res.style = ""
                    });
                    // }
                    if (this.zdy_child === false && this.zdy_cptablist[this.tabindex].child_category.length > 0) {
                        this.zdy_child = true;
                        // console.log(this.child);
                        let list = this.zdy_cptablist;
                        this.zdy_cp_ids = [];
                        this.zdy_cp_ids.push(this.zdy_cptablist[this.tabindex].id);
                        this.zdy_cp_ids = Array.from(new Set(this.zdy_cp_ids))
                    } else if (this.zdy_child === true) {
                        this.zdy_child = false
                    } else if (this.zdy_cptablist[this.tabindex].child_category.length === 0) {
                        this.zdy_child = false
                    }
                    this.zdy_handleClick(this.zdy_activeName)
                } else {
                    // this.productsList = this.no_order_cpList;
                    this.zdy_child = false;
                }
            },
            // 自定义搜索
            remoteMethod(val, item) {
                let that = this;
                if (val !== '' && val == undefined) {
                    that.loading = true;
                    setTimeout(() => {
                        //模糊搜索
                        axios.request({
                            // url: "/roke/get_staging_base_data",
                            url: "/roke/get_general_base_data",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'model_index': item.relation,
                                // 'search_mode': '',
                                // 'category_id': '',
                                // 'page_no': '',
                                // 'page_size': ''
                                'page_no': 1,
                                'page_size': 20,
                                'ttype': item.ttype
                            }
                        }).then(function (res) {
                            // console.log(res);
                            that.loading = false;
                            if (res.data.result.state === 'error') {
                                that.$message({
                                    type: 'error',
                                    message: res.data.result.msgs
                                });
                            } else {
                                item.record_list = res.data.result.record_list;
                            }
                        });
                    }, 300);
                } else {
                    if (!that.checkFlag) {
                        return
                    }
                    that.loading = true;
                    axios.request({
                        url: "/roke/get_staging_order_list",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            'model_index': 'roke.work.order',
                            'work_desk_index': 'gongdanbaogong',
                            'domain_dict': that.filter_list,
                            'code': '',
                            'search_type': '',
                            'page_no': 1,
                            'page_size': 10
                        }
                    }).then(function (res) {
                        // console.log(res);
                        if (res.data.result.state === 'success') {
                            if (that.total === 0) {
                                that.$message({
                                    type: 'success',
                                    message: '未查到对应数据！'
                                });
                                that.loading = false;
                            } else {
                                if (that.checkFlag) {
                                    that.jl_record_filter_data = res.data.result.record_filter_data;
                                    that.jl_record_filter_data.map((items, index) => {
                                        if (items.ttype === 'many2one' || items.ttype === 'many2many' || items.ttype === 'one2many') {
                                            // console.log(item);
                                            axios.request({
                                                url: "/roke/get_general_base_data",
                                                method: "post",
                                                headers: {
                                                    'Content-Type': 'application/json'
                                                },
                                                data: {
                                                    "ttype": items.ttype,
                                                    "page_no": 1,
                                                    "page_size": 20,
                                                    "model_index": items.relation,
                                                    "domain_data": '',
                                                    "selection_mode": ''
                                                }
                                            }).then((res) => {
                                                // console.log(res);
                                                if (res.data.result.state === 'success') {
                                                    items.record_list = res.data.result.record_list;
                                                    // console.log(item);
                                                } else if (res.data.result.state === 'error') {
                                                    that.$message({
                                                        type: 'error',
                                                        message: res.data.result.msgs
                                                    });
                                                } else if (res.data.error) {
                                                    that.$message({
                                                        type: 'error',
                                                        message: res.data.error.message
                                                    });
                                                }
                                            });
                                        }
                                    });
                                } else {
                                    that.record_filter_data = res.data.result.record_filter_data;
                                    that.record_filter_data.map((items, index) => {
                                        if (items.ttype === 'many2one' || items.ttype === 'many2many' || items.ttype === 'one2many') {
                                            // console.log(item);
                                            axios.request({
                                                url: "/roke/get_general_base_data",
                                                method: "post",
                                                headers: {
                                                    'Content-Type': 'application/json'
                                                },
                                                data: {
                                                    "ttype": items.ttype,
                                                    "page_no": 1,
                                                    "page_size": 20,
                                                    "model_index": items.relation,
                                                    "domain_data": '',
                                                    "selection_mode": ''
                                                }
                                            }).then((res) => {
                                                // console.log(res);
                                                if (res.data.result.state === 'success') {
                                                    items.record_list = res.data.result.record_list;
                                                    // console.log(item);
                                                } else if (res.data.result.state === 'error') {
                                                    that.$message({
                                                        type: 'error',
                                                        message: res.data.result.msgs
                                                    });
                                                } else if (res.data.error) {
                                                    that.$message({
                                                        type: 'error',
                                                        message: res.data.error.message
                                                    });
                                                }
                                            });
                                        }
                                    });
                                }
                                that.loading = false;
                            }
                        } else {
                            that.$message({
                                type: 'error',
                                message: '未查到该数据！'
                            });
                            that.loading = false;
                        }
                    });
                }
            },
            // 弹窗选择框自定义搜索
            dialog_remoteMethod(val, item) {
                // console.log(val,item);
                let that = this;
                // if(val !== ''){
                that.dia_loading = true;
                setTimeout(() => {
                    //模糊搜索
                    axios.request({
                        url: "/roke/get_staging_base_data",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: {
                            'model_index': item.relation,
                            'search_str': val,
                            'category_id': '',
                            'page_no': 1,
                            'page_size': 20
                        }
                    }).then(function (res) {
                        // console.log(res);
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs
                            });
                        } else {
                            item.select_vals = res.data.result.record_list;
                            // console.log(item.select_vals);
                        }
                    });
                    that.dia_loading = false;
                }, 1000);
                // }
                // else{
                //
                // }
            },
            // 弹窗选择框清除搜索条件
            reset_vals(val, item) {
                if (this.dialog_key) {
                    this.dialog_remoteMethod(val, item);
                } else {
                    this.remoteMethod(val, item);
                }
            },
        },
        beforeDestroy: function () {
            clearInterval(this.timer);
        }
    };
    var Ctor = Vue.extend(Main);
    new Ctor().$mount('#app')
</script>
<style>
    .el-input__icon {
        height: auto !important;
    }

    .search {
        margin-bottom: 10px;
    }
</style>

</html>