odoo.define('jzjx_project.roke_craft_design', function (require) {
    "use strict";

    var core = require('web.core');
    var session = require('web.session');
    var AbstractAction = require('web.AbstractAction');
    const Dialog = require("web.Dialog");

    var RokeCraftDesign = AbstractAction.extend({
        template: 'jzjx_project.roke_craft_design',

        init: function (parent, action, params) {
            this.action_controller = `${action.params.controller}&user_id=${session.uid}`;
            return this._super.apply(this, arguments);
        },

        start: async function () {
            await this._super(...arguments);

            let self = this;

            window.addEventListener("message", function (event) {
                if (event.data && event.data.method === "XFilePreview") {
                    return self._rpc({
                        model: "documents.document",
                        method: "preview_attachment_url",
                        args: [event.data.doc_id],
                    }).then(async (result) => {
                        const { code, message, data } = result;
                        console.log("result", result)   ;
                        if (code === 1) {
                            return
                        }
                        if (data.extra) {
                            let preview = data.url;
                            self._openPreviewInDialog(preview);
                            return
                        }

                        return self._rpc({
                            model: "ir.config_parameter",
                            method: "get_param",
                            args: ["kkfileview.url"],
                        }).then((preview_service) => {
                            console.log("preview_service", preview_service)
                            console.log("xxx", session["web.base.url"])
                            console.log("data.url", data.url)
                            let url = `${session["web.base.url"]}${data.url}`;
                            if (preview_service) {
                                let preview = `${preview_service}/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}`
                                self._openPreviewInDialog(preview);
                            }
                            else {
                                self.do_notify(
                                    "无法预览文件", "没有找到预览服务器的相关配置。请到设置-常规设置-预览服务中设置 Server URL。"
                                );
                            }
                        })
                    });
                }
            });
        },

        _openPreviewInDialog(preview) {
            let dialog = new Dialog(this, {
                title: '文档预览', size: 'large', renderFooter: false,
                $content: $('<div>', {
                    html: `  
                        <iframe src="${preview}" width="100%" height="600px" scrolling="yes" frameborder="0"></iframe>                    
                    `,
                }),
            });
            dialog.open();
        }
    });

    core.action_registry.add('jzjx_project.roke_craft_design', RokeCraftDesign);
    return RokeCraftDesign;
});