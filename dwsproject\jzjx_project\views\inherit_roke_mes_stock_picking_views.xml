<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherit_view_roke_mes_purchase_receiving_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.stock.picking.form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="replace">
                <field name="partner_id" options="{'no_create': True}"
                       attrs="{'readonly':[('state', '!=', '草稿')]}"
                       domain="[('supplier', '=', True)]"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_view_roke_mes_general_in_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.stock.picking.form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_in_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="replace">
                <field name="partner_id" options="{'no_create': True}"
                       attrs="{'readonly':[('state', '!=', '草稿')]}"
                       domain="[('supplier', '=', True)]"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_view_roke_mes_sale_deliver_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.stock.picking</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="replace">
                <field name="partner_id" options="{'no_create': True}"
                       attrs="{'readonly':[('state', '!=', '草稿')]}"
                       domain="[('customer', '=', True)]"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_view_roke_mes_general_out_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.stock.picking</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_out_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="replace">
                <field name="partner_id" options="{'no_create': True}"
                       attrs="{'readonly':[('state', '!=', '草稿')]}"
                       domain="[('customer', '=', True)]"/>
            </xpath>
        </field>
    </record>
</odoo>