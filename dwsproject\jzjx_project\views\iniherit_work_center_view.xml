<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="jzjx_inherit_work_center_kanban_view" model="ir.ui.view">
        <field name="name">jzjx.inherit.roke.work.center.kanban.view</field>
        <field name="model">roke.work.center</field>
        <field name="inherit_id" ref="roke_mes_production.roke_work_center_kanban_view"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='work_center_wo']" position="replace">
                <div id="jzjx_work_center_state" class="row" style="display:flex;justify-content: center;align-items: center;font-size: 30px;">
                    <p style="color:#dee2e6;"><field name="deviceState"/></p>
                </div>
                <div id="work_center_wo"/>
            </xpath>
            <xpath expr="//div[@id='work_center_empty']" position="replace">
                <div id="work_center_empty"/>
            </xpath>
        </field>
    </record>
    <record id="jzjx_inherit_base_work_center_form_view" model="ir.ui.view">
        <field name="name">jzjx.inherit.base.roke.work.center.form.view</field>
        <field name="model">roke.work.center</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_work_center_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='type_id']" position="after">
                <field name="filename" readonly="1"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="机床切割系统状态">
                    <group col="8">
                        <field name="axisX" readonly="1"/>
                        <field name="axisY" readonly="1"/>
                        <field name="axisZ" readonly="1"/>
                        <field name="workTimeStr" readonly="1"/>
                        <field name="workSpeed" readonly="1"/>
                        <field name="cutPercent" readonly="1"/>
                        <field name="laserPower" readonly="1"/>
                        <field name="pwmFreq" readonly="1"/>
                        <field name="gasType" readonly="1"/>
                        <field name="gasPressure" readonly="1"/>
                        <field name="pwmRatio" readonly="1"/>
                        <field name="targetHeight" readonly="1"/>
                        <field name="diodeCurrent" readonly="1"/>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    <record id="jzjx_inherit_wc_work_center_form_view" model="ir.ui.view">
        <field name="name">jzjx.inherit.wc.work.center.form.view</field>
        <field name="model">roke.work.center</field>
        <field name="inherit_id" ref="roke_mes_work_center.view_roke_production_wc_inherit_work_center_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='stock_location_id']" position="after">
                <field name="portionId" readonly="1"/>
            </xpath>
        </field>
    </record>
    <!--TODO 模块没依赖roke_mes_workshop_inspect-->
    <record id="jzjx_inherit_inspect_work_center_form_view" model="ir.ui.view">
        <field name="name">jzjx.inherit.inspect.roke.work.center.form.view</field>
        <field name="model">roke.work.center</field>
        <field name="inherit_id" ref="roke_mes_workshop_inspect.view_roke_wi_inherit_work_center_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='before_inspect_scheme_id']" position="after">
                <field name="startTime" readonly="1"/>
            </xpath>
        </field>
    </record>
</odoo>

