<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--切割机加工记录-->
    <!--search-->
    <record id="view_roke_work_log_search" model="ir.ui.view">
        <field name="name">roke.work.log.search</field>
        <field name="model">roke.work.log</field>
        <field name="arch" type="xml">
            <search string="切割机加工记录">
                <field name="work_center_id"/>
                <field name="portionId"/>
                <field name="material"/>
                <field name="materialName"/>
                <field name="portionName"/>
                <field name="plateSize"/>
                <field name="workUuid"/>
                <filter name="endState_0" string="已完成" domain="[('endState', '=', '0')]"/>
                <filter name="endState_-1" string="正在加工" domain="[('endState', '=', '-1')]"/>
                <filter name="endState_1" string="暂停" domain="[('endState', '=', '1')]"/>
                <filter name="endState_2" string="停止" domain="[('endState', '=', '2')]"/>
                <separator/>
                <filter name="filter_startTime" date="startTime" default_period="this_month"/>
                <filter name="filter_endTime" date="endTime" default_period="this_month"/>
                <filter name="filter_syncTime" date="syncTime" default_period="this_month"/>
                <group expand="0" string="Group By">
                    <filter string="材质" name="group_material" context="{'group_by': 'material'}"/>
                    <filter string="加工状态" name="group_endState" context="{'group_by': 'endState'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_work_log_tree" model="ir.ui.view">
        <field name="name">roke.work.log.tree</field>
        <field name="model">roke.work.log</field>
        <field name="priority">1000</field><!--数越大优先级越小-->
        <field name="arch" type="xml">
            <tree string="切割机加工记录" create="false" edit="false" delete="false" duplicate="false">
                <field name="work_center_id" optional="show"/>
                <field name="fileName" optional="show"/>
                <field name="portionId" optional="show"/>
                <field name="startTime" optional="show"/>
                <field name="endTime" optional="show"/>
                <field name="timeTaken" optional="show"/>
                <field name="endState" optional="show"/>
                <field name="tmEstimate" optional="hide"/>
                <field name="curveLength" optional="hide"/>
                <field name="moveLength" optional="hide"/>
                <field name="pierceCount" optional="hide"/>
                <field name="material" optional="show"/>
                <field name="materialName" optional="show"/>
                <field name="portionName" optional="show"/>
                <field name="thickness" optional="show"/>
                <field name="plateSize" optional="hide"/>
                <field name="selected" optional="hide"/>
                <field name="boundSize" optional="hide"/>
                <field name="syncTime" optional="hide"/>
                <field name="partAmount" optional="show"/>
                <field name="workUuid" optional="hide"/>
                <field name="taskUuid" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_log_form" model="ir.ui.view">
        <field name="name">roke.work.log.form</field>
        <field name="model">roke.work.log</field>
        <field name="arch" type="xml">
            <form string="切割机加工记录" create="false" edit="false" delete="false" duplicate="false">
                <header>
                    <field name="endState" widget="statusbar"/>
                </header>
                <group id="g1" col="4">
                    <group>
                        <field name="work_center_id" optional="show"/>
                        <field name="fileName" optional="show"/>
                        <field name="portionId" optional="show"/>
                        <field name="startTime" optional="show"/>
                        <field name="endTime" optional="show"/>
                    </group>
                    <group>
                        <field name="timeTaken" optional="show"/>
                        <field name="tmEstimate" optional="hide"/>
                        <field name="curveLength" optional="hide"/>
                        <field name="moveLength" optional="hide"/>
                        <field name="pierceCount" optional="hide"/>
                    </group>
                    <group>
                        <field name="material" optional="show"/>
                        <field name="materialName" optional="show"/>
                        <field name="portionName" optional="show"/>
                        <field name="thickness" optional="show"/>
                    </group>
                    <group>
                        <field name="selected" optional="hide"/>
                        <field name="boundSize" optional="hide"/>
                        <field name="syncTime" optional="hide"/>
                        <field name="workUuid" optional="hide"/>
                    </group>
                </group>
                <notebook>
                    <page string="零件信息">
                        <field name="part_ids">
                            <tree>
                                <field name="log_id" invisible="1"/>
                                <field name="partId"/>
                                <field name="name"/>
                                <field name="amount"/>
                            </tree>
                            <form>
                                <group>
                                    <group>
                                        <field name="log_id"/>
                                        <field name="partId"/>
                                    </group>
                                    <group>
                                        <field name="name"/>
                                        <field name="amount"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_work_log_action" model="ir.actions.act_window">
        <field name="name">切割机加工记录</field>
        <field name="res_model">roke.work.log</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
    </record>

    <menuitem id="roke_work_log_menu" name="切割机加工记录" parent="roke_mes_production.roke_production_report_menu"
              action="view_roke_work_log_action" sequence="100"/>

</odoo>
