<odoo>
  <data>
    <!-- explicit list view definition -->
<!--
    <record model="ir.ui.view" id="qdry_project.list">
      <field name="name">qdry_project list</field>
      <field name="model">qdry_project.qdry_project</field>
      <field name="arch" type="xml">
        <tree>
          <field name="name"/>
          <field name="value"/>
          <field name="value2"/>
        </tree>
      </field>
    </record>
-->

    <!-- actions opening views on models -->
<!--
    <record model="ir.actions.act_window" id="qdry_project.action_window">
      <field name="name">qdry_project window</field>
      <field name="res_model">qdry_project.qdry_project</field>
      <field name="view_mode">tree,form</field>
    </record>
-->

    <!-- server action to the one above -->
<!--
    <record model="ir.actions.server" id="qdry_project.action_server">
      <field name="name">qdry_project server</field>
      <field name="model_id" ref="model_qdry_project_qdry_project"/>
      <field name="state">code</field>
      <field name="code">
        action = {
          "type": "ir.actions.act_window",
          "view_mode": "tree,form",
          "res_model": model._name,
        }
      </field>
    </record>
-->

    <!-- Top menu item -->
<!--
    <menuitem name="qdry_project" id="qdry_project.menu_root"/>
-->
    <!-- menu categories -->
<!--
    <menuitem name="Menu 1" id="qdry_project.menu_1" parent="qdry_project.menu_root"/>
    <menuitem name="Menu 2" id="qdry_project.menu_2" parent="qdry_project.menu_root"/>
-->
    <!-- actions -->
<!--
    <menuitem name="List" id="qdry_project.menu_1_list" parent="qdry_project.menu_1"
              action="qdry_project.action_window"/>
    <menuitem name="Server to list" id="qdry_project" parent="qdry_project.menu_2"
              action="qdry_project.action_server"/>
-->
  </data>
</odoo>