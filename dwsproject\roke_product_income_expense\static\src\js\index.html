<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <title>财务收支记录</title>
    <!-- 引入element-ui CSS样式 -->
    <link rel="stylesheet" href="/roke_product_income_expense/static/src/js/element-ui/index.css" />
    <!-- 引入 vue js文件 -->
    <script src="/roke_product_income_expense/static/src/js/js/vue.js"></script>
    <!-- 引入 axios js文件 -->
    <script src="/roke_product_income_expense/static/src/js/js/axios.min.js"></script>
    <!-- moment.js 处理时间、日期 -->
    <script src="/roke_product_income_expense/static/src/js/js/moment.js"></script>
    <!-- 引入 element-ui js文件 -->
    <script src="/roke_product_income_expense/static/src/js/element-ui/index.js"></script>
</head>

<body>
    <div id="app" v-loading="loading" element-loading-text="加载中...">
        <div style="margin-bottom: 20px;">
            <el-table :data="createList" border :row-style="{height: '0'}"
                style="width: 100%;border: 1px solid black;border-color: black" :cell-style="tableCellStyle"
                :header-cell-style="tableHeaderCellStyle">
                <el-table-column label="业务日期" align="center" width="166">
                    <template slot-scope="scope">
                        <el-date-picker v-model="scope.row.business_date" type="date" format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd" placeholder="选择日期">
                        </el-date-picker>
                    </template>
                </el-table-column>
                <el-table-column label="摘要" align="center">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.abstract" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="客户" align="center" width="180">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.customer" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="收入" align="center" width="120">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.income" type="number" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="支出" align="center" width="120">
                    <template slot-scope="scope">
                        <el-input v-model="scope.row.expenditure" type="number" placeholder="请输入"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="类型" align="center" width="180">
                    <template slot-scope="scope">
                        <!-- <el-input v-model="scope.row.machinery_type" type="number" placeholder="请输入"></el-input> -->
                        <el-select v-model="scope.row.machinery_type" clearable placeholder="请选择类型">
                            <el-option v-for="item in machinery_type_options" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>

                <!-- <el-table-column label="创建人" align="center" width="110">
                    <template slot-scope="scope">
                        <div>
                            [[ scope.row.user_name ]]
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column label="操作" align="center" width="95">
                    <template slot-scope="scope">
                        <el-button type="primary" @click="saveListData">保存</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>


        <div style="display: flex;justify-content: flex-end;  text-align: right; margin-bottom: 5px;">
            <el-input style="width: 15%;" v-model="abstract_value" placeholder="请填写摘要信息" @change="select_change"></el-input>

            <el-select v-model="select_value" clearable placeholder="请选择收支类型" @change="select_change">
                <el-option v-for="item in select_options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>

            <el-select class="margin_sty" v-model="machinery_type_value" clearable placeholder="请选择类型"
                @change="machinery_type_change">
                <el-option v-for="item in machinery_type_options" :key="item.value" :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>

            <el-input style="width: 15%;" v-model="customer_value" placeholder="请填写客户名称" @change="select_change"></el-input>

            <el-date-picker class="margin_sty" v-model="datePickerValue" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="datePickerChange">
            </el-date-picker>

            <el-button type="primary" @click="export_click">导出</el-button>
            <el-button v-if="edit_state" type="primary" @click="save_modifications">保存修改</el-button>
        </div>
        <el-table :data="dataList" border height="500" :row-style="{height: '0'}" :key="table_key"
            style="width: 100%;border: 1px solid black;border-color: black" :cell-style="tableCellStyle"
            :header-cell-style="tableHeaderCellStyle">
            <el-table-column label="业务日期" align="center" width="150">
                <template slot-scope="scope">
                    <el-date-picker v-if="scope.row.whether_edit" v-model="scope.row.business_date" type="date"
                        format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="选择日期">
                    </el-date-picker>
                    <div v-else>
                        [[ scope.row.business_date ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="摘要" align="center">
                <template slot-scope="scope">
                    <el-input v-if="scope.row.whether_edit" type="textarea" v-model="scope.row.abstract"
                        placeholder="请输入"></el-input>
                    <div style="text-align: left;" v-else>
                        [[ scope.row.abstract ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="客户" align="center">
                <template slot-scope="scope">
                    <el-input v-if="scope.row.whether_edit" v-model="scope.row.customer" placeholder="请输入"></el-input>
                    <div style="text-align: left;" v-else>
                        [[ scope.row.customer ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="收入" align="center" width="100">
                <template slot-scope="scope">
                    <el-input v-if="scope.row.whether_edit" v-model="scope.row.income" type="number"
                        placeholder="请输入"></el-input>
                    <div style="text-align: right;" v-else>
                        [[ scope.row.income ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="支出" align="center" width="100">
                <template slot-scope="scope">
                    <el-input v-if="scope.row.whether_edit" v-model="scope.row.expenditure" type="number"
                        placeholder="请输入"></el-input>
                    <div style="text-align: right;" v-else>
                        [[ scope.row.expenditure ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="结余" align="center" width="120">
                <template slot-scope="scope">
                    <div>
                        [[ scope.row.balance ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="类型" align="center" width="120">
                <template slot-scope="scope">
                    <el-select v-if="scope.row.whether_edit" v-model="scope.row.machinery_type" clearable
                        placeholder="请选择类型">
                        <el-option v-for="item in machinery_type_options" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                    <div v-else>
                        [[ scope.row.machinery_type ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="创建人" align="center" width="100">
                <template slot-scope="scope">
                    <div>
                        [[ scope.row.user_name ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" width="160">
                <template slot-scope="scope">
                    <div>
                        [[ scope.row.create_date ]]
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
                <template slot-scope="scope">
                    <!-- <el-button v-if="scope.row.whether_edit" type="primary">保存</el-button> -->
                    <el-button v-if="!scope.row.whether_edit" type="primary" icon="el-icon-edit" circle
                        @click="item_edit(scope)"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle @click="deleteItem(scope)"></el-button>
                </template>
            </el-table-column>
        </el-table>
        <div style="margin-top: 10px; text-align: center;">
            <el-pagination :current-page="currentPageNo" :total="paginationTotal" @current-change="handleCurrentChangee"
                :page-size="10" layout="prev, pager, next,total">
            </el-pagination>
        </div>
    </div>
</body>
<script>
    new Vue({
        el: "#app",
        delimiters: ["[[", "]]"], // 替换原本vue的{{ key }}取值方式(与odoo使用的jinja2冲突问题)

        data() {
            return {
                TableHeight: 0,
                currentPageNo: 1,
                paginationTotal: 10,
                loading: false,
                dataList: [],
                table_key: false,
                user_name: '',
                user_id: null,
                createList: [
                    {
                        business_date: '',
                        abstract: '',
                        customer: '',
                        income: '',
                        expenditure: '',
                        user_name: '',
                        user_id: null,
                        machinery_type: ''
                    }
                ],
                datePickerValue: ['', ''],
                select_options: [{
                    value: 'income',
                    label: '收入'
                }, {
                    value: 'expenditure',
                    label: '支出'
                }],
                machinery_type_options: [{
                    value: '烘干桶',
                    label: '烘干桶'
                }, {
                    value: '钣金',
                    label: '钣金'
                }, {
                    value: '颗粒机',
                    label: '颗粒机'
                }, {
                    value: '其他',
                    label: '其他'
                }],
                select_value: '',
                machinery_type_value: '',
                customer_value: '',
                abstract_value: '',
                edit_state: false,
                edit_list: []
            };
        },

        created() {
            this.user_name = this.getUrlSearch('user_name')  //截取url后面的参数
            this.user_id = this.getUrlSearch('user_id')
            this.createList[0].user_name = this.user_name
            this.createList[0].business_date = moment().format("YYYY-MM-DD")
            this.createList[0].user_id = this.user_id
            this.TableHeight = window.innerHeight;
            this.getDataList()
        },
        methods: {
            // 保存修改
            save_modifications() {
                for (let i = 0; i < this.edit_list.length; i++) {
                    if (!this.edit_list[i].business_date) {
                        return this.$message({
                            type: "warning",
                            message: "请选择业务日期",
                        });
                    } else if (this.edit_list[i].machinery_type == '') {
                        return this.$message({
                            type: "warning",
                            message: "请选择类型",
                        });
                    } else if ((this.edit_list[i].income == '' && this.edit_list[i].income != 0) || (this.edit_list[i].expenditure == '' && this.edit_list[i].expenditure != 0)) {
                        return this.$message({
                            type: "warning",
                            message: "请填写收入或支出后保存",
                        });
                    }
                    this.edit_list[i].user_id = this.user_id
                }
                this.loading = true;
                axios.request({
                    url: "/roke/product/product_income_expense/create",
                    method: "post",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: {
                        data_list: this.edit_list
                    }
                }).then((res) => {
                    if (res.data.result.code === 0) {
                        this.$message({
                            type: "success",
                            message: res.data.result.message || "修改成功",
                        });
                        this.currentPageNo = 1
                        this.createList = [
                            {
                                business_date: moment().format("YYYY-MM-DD"),
                                abstract: '',
                                income: '',
                                customer: '',
                                expenditure: '',
                                user_name: this.user_name,
                                user_id: this.user_id,
                                machinery_type: ''
                            }
                        ]
                        this.select_value = ''
                        this.machinery_type_value = ''
                        this.customer_value = ''
                        this.datePickerValue = ''
                        this.edit_list = []
                        this.edit_state = false
                        this.getDataList()
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.result.message || "修改失败",
                        });
                    }
                    this.loading = false;
                });
            },


            machinery_type_change(e) {
                this.getDataList()
            },
            select_change(e) {
                console.log(e);
                console.log(this.select_value);
                this.getDataList()
            },
            // 列表 时间过滤
            datePickerChange(e) {
                if (!e) {
                    this.datePickerValue = ['', '']
                }
                this.getDataList()
            },
            // 分页请求数据
            handleCurrentChangee(val) {
                if (this.edit_list.length > 0) {
                    this.$confirm('有编辑的内容未保存, 是否保存?', '提示', {
                        confirmButtonText: '确认保存',
                        cancelButtonText: '取消保存',
                        type: 'warning'
                    }).then(() => {
                        this.save_modifications()
                    }).catch(() => {
                        this.edit_list = []
                        this.currentPageNo = val;
                        this.getDataList()
                    });
                } else {
                    this.currentPageNo = val;
                    this.getDataList()
                }
            },
            // 获取列表数据
            getDataList() {
                if (!this.datePickerValue) {
                    this.datePickerValue = ['', '']
                }
                this.loading = true;
                let parameter = {
                    limit: 10, //每页数量 非必填
                    page: this.currentPageNo, //当前页码
                    start_date: this.datePickerValue[0],
                    end_date: this.datePickerValue[1],
                    type_str: this.select_value,
                    machinery_type: this.machinery_type_value,
                    abstract: this.abstract_value,
                    customer: this.customer_value
                }
                axios.request({
                    url: "/roke/product/product_income_expense/get",
                    method: "post",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: parameter,
                }).then((res) => {
                    if (res.data.result.code === 0) {
                        this.paginationTotal = res.data.result.count
                        res.data.result.data.forEach(item => {
                            item.whether_edit = false
                        });
                        this.dataList = res.data.result.data
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.result.message || "获取失败请稍后重试",
                        });
                    }
                    this.loading = false;
                }).catch(err => {
                    console.log(err);
                    this.$message({
                        type: "error",
                        message: "接口报错，获取列表数据失败",
                    });
                })
            },
            item_edit(scope) {
                console.log(scope);
                this.edit_list.push(scope.row)
                this.edit_state = true
                scope.row.whether_edit = true
                this.table_key = !this.table_key
            },
            // 删除
            deleteItem(item, index) {
                if (item.row.id) {
                    this.loading = true;
                    axios.request({
                        url: "/roke/product/product_income_expense/delete",
                        method: "post",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        data: {
                            del_id: item.row.id
                        }
                    }).then((res) => {
                        if (res.data.result.code === 0) {
                            this.$message({
                                type: "success",
                                message: res.data.result.message || "删除成功",
                            });
                            this.getDataList()
                        } else {
                            this.$message({
                                type: "error",
                                message: res.data.result.message || "删除失败",
                            });
                        }
                        this.loading = false;
                    });
                } else {
                    this.dataList.splice(item.$index, 1)
                }
                if (this.edit_list.length > 0) {
                    this.edit_list.forEach((res_itm, res_idx) => {
                        if (res_itm.id == item.row.id) {
                            this.edit_list.splice(res_idx, 1)
                        }
                    })
                }
            },
            // 导出
            export_click() {
                if (this.dataList.length > 0) {
                    this.loading = true;
                    let formData = new FormData();
                    let _config = { responseType: 'blob' }
                    if (!this.datePickerValue) {
                        this.datePickerValue = ['', '']
                    }
                    formData.append("start_date", this.datePickerValue[0]);
                    formData.append("end_date", this.datePickerValue[1]);
                    formData.append("type_str", this.select_value);
                    formData.append("machinery_type", this.machinery_type_value);
                    formData.append("abstract", this.abstract_value);
                    formData.append("customer", this.customer_value);
                    axios.post("/roke/product/product_income_expense/export",
                        formData,
                        _config,
                    ).then(res => {
                        this.loading = false
                        let blob = new Blob([res.data], { type: res.data.type });
                        let url = window.URL.createObjectURL(blob);
                        let link = document.createElement("a");
                        link.style.display = "none";
                        link.href = url;
                        link.setAttribute("download", `财务收入支出表.xlsx`);
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                    })
                } else {
                    this.$message({
                        type: 'error',
                        message: '请根据配置的筛选条件查询数据后在导出报表！'
                    });
                }
            },
            // 保存
            saveListData() {
                if (!this.createList[0].business_date) {
                    return this.$message({
                        type: "warning",
                        message: "请选择业务日期",
                    });
                }
                if (this.createList[0].machinery_type == '') {
                    this.$message({
                        type: "warning",
                        message: "请选择类型",
                    });
                } else {
                    if (this.createList[0].income != '' || this.createList[0].expenditure != '') {
                        this.loading = true;
                        axios.request({
                            url: "/roke/product/product_income_expense/create",
                            method: "post",
                            headers: {
                                "Content-Type": "application/json",
                            },
                            data: {
                                data_list: this.createList
                            }
                        }).then((res) => {
                            if (res.data.result.code === 0) {
                                this.$message({
                                    type: "success",
                                    message: res.data.result.message || "创建成功",
                                });
                                this.currentPageNo = 1
                                this.createList = [
                                    {
                                        business_date: moment().format("YYYY-MM-DD"),
                                        abstract: '',
                                        income: '',
                                        customer: '',
                                        expenditure: '',
                                        user_name: this.user_name,
                                        user_id: this.user_id,
                                        machinery_type: ''
                                    }
                                ]
                                this.select_value = ''
                                this.machinery_type_value = ''
                                this.customer_value = ''
                                this.abstract_value = ''
                                this.datePickerValue = ''
                                this.getDataList()
                            } else {
                                this.$message({
                                    type: "error",
                                    message: res.data.result.message || "创建失败",
                                });
                            }
                            this.loading = false;
                        });
                    } else {
                        this.$message({
                            type: "warning",
                            message: "请填写收入或支出后保存",
                        });
                    }
                }

            },
            // table样式
            tableCellStyle() {
                return "border-color:black;"
            },
            // 修改 table header cell的背景色
            tableHeaderCellStyle() {
                return 'background: #eef1f6;border-color: black;color:black'
            },
            // 通过网址跳转过来的页面,截取后面的参数
            getUrlSearch(name) {
                // 未传参，返回空
                if (!name) return '';
                // 查询参数：先通过search取值，如果取不到就通过hash来取
                var after = window.location.search;
                after = after.substr(1) || window.location.hash.split('?')[1];
                // 地址栏URL没有查询参数，返回空
                if (!after) return null;
                // 如果查询参数中没有"name"，返回空
                if (after.indexOf(name) === -1) return null;
                var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
                // 当地址栏参数存在中文时，需要解码，不然会乱码
                var r = decodeURI(after).match(reg);
                // 如果url中"name"没有值，返回空
                if (!r) return null;
                return r[2];
            },
        },
    });
</script>
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        padding: 15px 25px 25px 25px;
    }

    .el-table .el-table__cell {
        padding: 3px 0;
    }

    .el-date-editor.el-input {
        width: 140px;
    }

    .margin_sty {
        margin: 0 10px;
    }

    .el-textarea__inner {
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
            "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    }
</style>

</html>