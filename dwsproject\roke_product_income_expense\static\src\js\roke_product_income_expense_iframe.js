odoo.define('roke_product_income_expense.product_income_expense_iframe', function (require) {
    "use strict";

    var core = require('web.core');
    var session = require('web.session');
    var AbstractAction = require('web.AbstractAction');

    var ProductIncomeExpenseIFrame = AbstractAction.extend({
        template: 'ProductIncomeExpenseIFrame',

        init: function (parent, action, params) {
            this.add_sum = 0
            this.key = ""
            this.old_data = {}
            this.action_controller = `${action.params.controller}?user_id=${session.uid}&user_name=${session.name}`
            return this._super.apply(this, arguments);
        },
    });

    core.action_registry.add('product_income_expense_iframe', ProductIncomeExpenseIFrame);
    return ProductIncomeExpenseIFrame;
});