from odoo import api, fields, models


class PlantWorkingTimeConfig(models.Model):
    _name = "plant.working.time.config"
    _description = "车间工作时间配置"

    plant_id = fields.Many2one("roke.plant", string="车间")
    start_time = fields.Float(string="开始时间", default="0")
    end_time = fields.Float(string="结束时间", default="0")
    color = fields.Selection([
        ('red', '红'),
        ('yellow', '黄'),
        ('green', '绿'),
        ('blue', '蓝'),
        ('gray', '灰')
    ], string="颜色")
    wait_time = fields.Float(string="等待时间")