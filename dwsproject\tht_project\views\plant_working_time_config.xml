<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_plant_working_time_config_tree" model="ir.ui.view">
        <field name="name">view_plant_working_time_config_tree</field>
        <field name="model">plant.working.time.config</field>
        <field name="arch" type="xml">
            <tree>
                <field name="plant_id"/>
                <field name="start_time" widget="float_time"/>
                <field name="end_time" widget="float_time"/>
            </tree>
        </field>
    </record>

    <record id="view_plant_working_time_config_form" model="ir.ui.view">
        <field name="name">view_plant_working_time_config_form</field>
        <field name="model">plant.working.time.config</field>
        <field name="arch" type="xml">
            <form>
                <group col="3">
                    <group>
                        <field name="plant_id"/>
                    </group>
                    <group>
                        <field name="start_time" widget="float_time"/>
                    </group>
                    <group>
                        <field name="end_time" widget="float_time"/>
                    </group>
                </group>
                <notebook>
                    <page string="停机规则设置">
                        <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                            <div class="ml16" style="width: 150px">
                                <field name="color"/>
                            </div>
                            <span class="ml16">色下连续超过</span>
                            <div class="ml16" style="width: 150px">
                                <field name="wait_time"/>
                            </div>
                            <span class="ml16">分钟，页面展示停机状态。</span>
                        </div>
                    </page>
                </notebook>
            </form>
        </field>
    </record>

    <record id="action_plant_working_time_config" model="ir.actions.act_window">
        <field name="name">车间工作时间配置</field>
        <field name="res_model">plant.working.time.config</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>