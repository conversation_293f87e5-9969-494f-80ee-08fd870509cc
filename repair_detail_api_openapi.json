{"openapi": "3.0.0", "info": {"title": "设备维修任务详情接口", "description": "获取设备维修任务详细信息接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/equipment/repair_detail": {"post": {"summary": "获取维修任务详情", "description": "根据维修任务ID或编号获取详细的维修任务信息，包括设备信息、报修信息、维修信息等", "tags": ["设备维修"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RepairDetailRequest"}, "examples": {"byOrderId": {"summary": "通过维修任务ID查询", "value": {"order_id": 123}}, "byCode": {"summary": "通过维修任务编号查询", "value": {"code": "WX20240115001"}}}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RepairDetailResponse"}, "examples": {"success": {"summary": "获取成功", "value": {"state": "success", "msgs": "获取成功", "order_detail": {"id": 123, "code": "WX20240115001", "equipment": {"e_id": 45, "e_code": "EQ001", "e_name": "数控机床01", "e_location": "车间A-01"}, "assign_user": "张三，李四", "assign_phone": "13800138001，13800138002", "state": "已分配", "priority": "紧急", "deadline": "2024-01-20", "create_date": "2024-01-15", "report_time": "2024-01-15 09:30:00", "report_user": "王五", "report_phone": "13800138003", "fault_description": "设备异常噪音，疑似轴承故障", "fault_files": [{"id": 1, "name": "故障照片1.jpg", "url": "/web/content/1"}], "repair_user": "张三", "repair_phone": "13800138001", "repair_description": "更换轴承，调试正常", "repair_files": [{"id": 2, "name": "维修照片1.jpg", "url": "/web/content/2"}], "repair_origin": "设备巡检", "level": "一般", "finish_time": "2024-01-16 15:30:00", "picture": "base64_encoded_image_data", "change": "新轴承", "remove": "旧轴承", "evaluate": "维修质量良好", "fault_id": 10, "fault_name": "机械故障", "repair_picture": "base64_encoded_repair_image"}}}, "errorNotFound": {"summary": "维修任务不存在", "value": {"state": "error", "msgs": "WX20240115999未获取到对应单据"}}, "errorMultiple": {"summary": "编号重复", "value": {"state": "error", "msgs": "WX20240115001获取到多个单据，请联系管理员删除重复编号的单据"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"RepairDetailRequest": {"type": "object", "properties": {"order_id": {"type": "integer", "description": "维修任务ID（与code二选一）", "example": 123}, "code": {"type": "string", "description": "维修任务编号（与order_id二选一）", "example": "WX20240115001"}}, "oneOf": [{"required": ["order_id"]}, {"required": ["code"]}]}, "RepairDetailResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success", "error"], "description": "响应状态"}, "msgs": {"type": "string", "description": "响应消息"}, "order_detail": {"type": "object", "description": "维修任务详情", "properties": {"id": {"type": "integer", "description": "维修任务ID"}, "code": {"type": "string", "description": "维修任务编号"}, "equipment": {"type": "object", "description": "设备信息", "properties": {"e_id": {"type": "integer", "description": "设备ID"}, "e_code": {"type": "string", "description": "设备编号"}, "e_name": {"type": "string", "description": "设备名称"}, "e_location": {"type": "string", "description": "设备位置"}}}, "assign_user": {"type": "string", "description": "分配的维修人员（多人用逗号分隔）"}, "assign_phone": {"type": "string", "description": "维修人员电话（多人用逗号分隔）"}, "state": {"type": "string", "description": "维修任务状态"}, "priority": {"type": "string", "description": "优先级"}, "deadline": {"type": "string", "format": "date", "description": "截止日期"}, "create_date": {"type": "string", "format": "date", "description": "创建日期"}, "report_time": {"type": "string", "format": "date-time", "description": "报修时间"}, "report_user": {"type": "string", "description": "报修人"}, "report_phone": {"type": "string", "description": "报修人电话"}, "fault_description": {"type": "string", "description": "故障描述"}, "fault_files": {"type": "array", "description": "故障照片", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "url": {"type": "string"}}}}, "repair_user": {"type": "string", "description": "维修人员"}, "repair_phone": {"type": "string", "description": "维修人员电话"}, "repair_description": {"type": "string", "description": "维修描述"}, "repair_files": {"type": "array", "description": "维修照片", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "url": {"type": "string"}}}}, "repair_origin": {"type": "string", "description": "报修来源"}, "level": {"type": "string", "description": "故障等级"}, "finish_time": {"type": "string", "format": "date-time", "description": "完成时间"}, "picture": {"type": "string", "description": "图片（Base64编码）"}, "change": {"type": "string", "description": "更换的配件"}, "remove": {"type": "string", "description": "拆下的配件"}, "evaluate": {"type": "string", "description": "维修评价"}, "fault_id": {"type": "integer", "description": "故障类型ID"}, "fault_name": {"type": "string", "description": "故障类型名称"}, "repair_picture": {"type": "string", "description": "维修图片（Base64编码）"}}}}}}}}