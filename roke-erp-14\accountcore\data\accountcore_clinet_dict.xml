<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="roke_accountcore_account_dict" model="roke.client.dict">
            <field name="name">会计科目</field>
            <field name="show_type">列表方式</field>
            <field name="main_model_id" ref="accountcore.model_accountcore_account"/>
            <field name="line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'sequence': 1,
                    'model_id': ref('accountcore.model_accountcore_account'),
                    'field_id': ref('accountcore.field_accountcore_account__name'),
                }),
                (0, 0, {
                    'sequence': 2,
                    'model_id': ref('accountcore.model_accountcore_account'),
                    'field_id': ref('accountcore.field_accountcore_account__number'),
                })
            ]"/>
        </record>
        <record id="roke_accountcore_org_dict" model="roke.client.dict">
            <field name="name">核算组织</field>
            <field name="show_type">列表方式</field>
            <field name="main_model_id" ref="accountcore.model_accountcore_org"/>
            <field name="line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'sequence': 1,
                    'model_id': ref('accountcore.model_accountcore_org'),
                    'field_id': ref('accountcore.field_accountcore_org__name'),
                }),
                (0, 0, {
                    'sequence': 2,
                    'model_id': ref('accountcore.model_accountcore_org'),
                    'field_id': ref('accountcore.field_accountcore_org__number'),
                })
            ]"/>
        </record>
        <record id="roke_accountcore_item_dict" model="roke.client.dict">
            <field name="name">核算项目</field>
            <field name="show_type">列表方式</field>
            <field name="main_model_id" ref="accountcore.model_accountcore_item"/>
            <field name="line_ids" eval="[(5, 0, 0),
                (0, 0, {
                    'sequence': 1,
                    'model_id': ref('accountcore.model_accountcore_item'),
                    'field_id': ref('accountcore.field_accountcore_item__name'),
                }),
                (0, 0, {
                    'sequence': 2,
                    'model_id': ref('accountcore.model_accountcore_item'),
                    'field_id': ref('accountcore.field_accountcore_item__number'),
                })
            ]"/>
        </record>
    </data>
</odoo>