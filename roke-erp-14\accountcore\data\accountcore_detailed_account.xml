<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="roke_accountcore_detailed_account" model="roke.sql.model.component">
            <field name="name">明细账</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">明细账</field>
            <field name="top_menu_id" ref="accountcore.account_inventory"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '开始时间',
                    'sql_field_mark': 'start_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '结束时间',
                    'sql_field_mark': 'end_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '科目范围',
                    'sql_field_mark': 'account_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_account_dict'),
                }),
                (0, 0, {
                    'name': '机构范围',
                    'sql_field_mark': 'org_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_org_dict'),
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "日期",
                    "sql_data": "(temp_table1.v_voucherdate) AS 日期",
                    "sql_order_by_data": "(temp_table1.v_voucherdate)"
                }),
                (0, 0, {
                    "name": "会计科目",
                    "sql_data": "(temp_table1.account_name) AS 会计科目",
                    "sql_order_by_data": "(temp_table1.account_name)"
                }),
                (0, 0, {
                    "name": "凭证号",
                    "sql_data": "(temp_table1.entry_number) AS 凭证号",
                    "sql_order_by_data": "(temp_table1.entry_number)"
                }),
                (0, 0, {
                    "name": "摘要",
                    "sql_data": "(temp_table1.entry_explain) AS 摘要",
                    "sql_order_by_data": "(temp_table1.entry_explain)"
                }),
                (0, 0, {
                    "name": "借方",
                    "sql_data": "COALESCE(temp_table1.entry_damount, 0) AS 借方",
                    "sql_order_by_data": "COALESCE(temp_table1.entry_damount, 0)"
                }),
                (0, 0, {
                    "name": "贷方",
                    "sql_data": "COALESCE(temp_table1.entry_camount, 0) AS 贷方",
                    "sql_order_by_data": "COALESCE(temp_table1.entry_camount, 0)"
                }),
                (0, 0, {
                    "name": "方向",
                    "sql_data": "(temp_table1.direction) AS 方向",
                    "sql_order_by_data": "temp_table1.direction"
                }),
                (0, 0, {
                    "name": "余额",
                    "sql_data": "COALESCE(temp_table1.balance, 0) AS 余额",
                    "sql_order_by_data": "COALESCE(temp_table1.balance, 0)"
                }),
                (0, 0, {
                    "name": "凭证ID",
                    "sql_data": "(temp_table1.voucher_id) AS 凭证ID",
                    "sql_order_by_data": "(temp_table1.voucher_id)",
                    "is_not_show": True
                })
            ]'/>
        </record>
        <record id="roke_accountcore_general_account" model="roke.sql.model.component">
            <field name="name">总账</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">总账</field>
            <field name="top_menu_id" ref="accountcore.account_inventory"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                        (0, 0, {
                            'name': '开始时间',
                            'sql_field_mark': 'start_dt',
                            'sql_field_mark_type': 'date',
                        }),
                        (0, 0, {
                            'name': '结束时间',
                            'sql_field_mark': 'end_dt',
                            'sql_field_mark_type': 'date',
                        }),
                        (0, 0, {
                            'name': '科目范围',
                            'sql_field_mark': 'account_ids',
                            'sql_field_mark_type': 'many2many',
                            'inherit_client_dict': ref('accountcore.roke_accountcore_account_dict'),
                        }),
                        (0, 0, {
                            'name': '机构范围',
                            'sql_field_mark': 'org_ids',
                            'sql_field_mark_type': 'many2many',
                            'inherit_client_dict': ref('accountcore.roke_accountcore_org_dict'),
                        })
                    ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                        (0, 0, {
                            "name": "会计科目",
                            "sql_data": "(temp_table1.account_name) AS 会计科目",
                            "sql_order_by_data": "(temp_table1.account_name)"
                        }),
                        (0, 0, {
                            "name": "借方",
                            "sql_data": "COALESCE(temp_table1.entry_damount, 0) AS 借方",
                            "sql_order_by_data": "COALESCE(temp_table1.entry_damount, 0)"
                        }),
                        (0, 0, {
                            "name": "贷方",
                            "sql_data": "COALESCE(temp_table1.entry_camount, 0) AS 贷方",
                            "sql_order_by_data": "COALESCE(temp_table1.entry_camount, 0)"
                        }),
                        (0, 0, {
                            "name": "方向",
                            "sql_data": "(temp_table1.direction) AS 方向",
                            "sql_order_by_data": "temp_table1.direction"
                        }),
                        (0, 0, {
                            "name": "余额",
                            "sql_data": "COALESCE(temp_table1.balance, 0) AS 余额",
                            "sql_order_by_data": "COALESCE(temp_table1.balance, 0)"
                        }),
                        (0, 0, {
                            "name": "科目ID",
                            "sql_data": "(temp_table1.account_id) AS 科目ID",
                            "sql_order_by_data": "(temp_table1.account_id)",
                            "is_not_show": True
                        })
                    ]'/>
        </record>
        <record id="roke_accountcore_account_balance_account" model="roke.sql.model.component">
            <field name="name">科目余额表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">科目余额表</field>
            <field name="top_menu_id" ref="accountcore.account_inventory"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                        (0, 0, {
                            'name': '开始时间',
                            'sql_field_mark': 'start_dt',
                            'sql_field_mark_type': 'date',
                        }),
                        (0, 0, {
                            'name': '结束时间',
                            'sql_field_mark': 'end_dt',
                            'sql_field_mark_type': 'date',
                        }),
                        (0, 0, {
                            'name': '科目范围',
                            'sql_field_mark': 'account_ids',
                            'sql_field_mark_type': 'many2many',
                            'inherit_client_dict': ref('accountcore.roke_accountcore_account_dict'),
                        }),
                        (0, 0, {
                            'name': '机构范围',
                            'sql_field_mark': 'org_ids',
                            'sql_field_mark_type': 'many2many',
                            'inherit_client_dict': ref('accountcore.roke_accountcore_org_dict'),
                        }),
                        (0, 0, {
                            'name': '隐藏余额为零的科目',
                            'sql_field_mark': 'is_hide_zero_balance',
                            'sql_field_mark_type': 'boolean',
                        }),
                        (0, 0, {
                            'name': '隐藏无发生额的科目',
                            'sql_field_mark': 'is_hide_zero_amount',
                            'sql_field_mark_type': 'boolean',
                        }),
                        (0, 0, {
                            'name': '隐藏没有任何余额的科目',
                            'sql_field_mark': 'is_hide_double_zero_balance',
                            'sql_field_mark_type': 'boolean',
                        })
                    ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                        (0, 0, {
                            "name": "科目编号",
                            "sql_data": "(temp_table1.account_number) AS 科目编号",
                            "sql_order_by_data": "(temp_table1.account_number)"
                        }),
                        (0, 0, {
                            "name": "科目名称",
                            "sql_data": "(temp_table1.account_name) AS 科目名称",
                            "sql_order_by_data": "(temp_table1.account_name)"
                        }),
                        (0, 0, {
                            "name": "方向",
                            "sql_data": "(temp_table1.direction) AS 方向",
                            "sql_order_by_data": "temp_table1.direction"
                        }),
                        (0, 0, {
                            "name": "期初余额",
                            "sql_data": "case when COALESCE(temp_table1.qc_balance, 0) > 0 then COALESCE(temp_table1.qc_balance, 0) else COALESCE(temp_table3.qc_balance, 0) end AS 期初余额",
                            "sql_order_by_data": "case when COALESCE(temp_table1.qc_balance, 0) > 0 then COALESCE(temp_table1.qc_balance, 0) else COALESCE(temp_table3.qc_balance, 0) end"
                        }),
                        (0, 0, {
                            "name": "借方",
                            "sql_data": "case when COALESCE(temp_table1.entry_damount, 0) > 0 then COALESCE(temp_table1.entry_damount, 0) else COALESCE(temp_table3.entry_damount, 0) end AS 本期借方发生",
                            "sql_order_by_data": "case when COALESCE(temp_table1.entry_damount, 0) > 0 then COALESCE(temp_table1.entry_damount, 0) else COALESCE(temp_table3.entry_damount, 0) end",
                            "parent_name": "本期金额发生"
                        }),
                        (0, 0, {
                            "name": "贷方",
                            "sql_data": "case when COALESCE(temp_table1.entry_camount, 0) > 0 then COALESCE(temp_table1.entry_camount, 0) else COALESCE(temp_table3.entry_camount, 0) end AS 本期贷方发生",
                            "sql_order_by_data": "case when COALESCE(temp_table1.entry_camount, 0) > 0 then COALESCE(temp_table1.entry_camount, 0) else COALESCE(temp_table3.entry_camount, 0) end",
                            "parent_name": "本期金额发生"
                        }),
                        (0, 0, {
                            "name": "期末方向",
                            "sql_data": "(temp_table1.end_direction) AS 期末方向",
                            "sql_order_by_data": "(temp_table1.end_direction)"
                        }),
                        (0, 0, {
                            "name": "期末余额",
                            "sql_data": "case when COALESCE(temp_table1.balance, 0) > 0 then COALESCE(temp_table1.balance, 0) else COALESCE(temp_table3.balance, 0) end AS 期末余额",
                            "sql_order_by_data": "case when COALESCE(temp_table1.balance, 0) > 0 then COALESCE(temp_table1.balance, 0) else COALESCE(temp_table3.balance, 0) end"
                        }),
                        (0, 0, {
                            "name": "科目ID",
                            "sql_data": "(temp_table1.account_id) AS 科目ID",
                            "sql_order_by_data": "temp_table1.account_id",
                            "is_not_show": True
                        }),
                    ]'/>
        </record>
    </data>
    <data noupdate="0">
        <record id="roke_query_investigation_model_general_account_01" model="roke.query.investigation.model">
            <field name="investigation_type">自定义报表</field>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">会计科目</field>
        </record>
<!--        <record id="roke_query_investigation_model_detailed_account_01" model="roke.query.investigation.model">-->
<!--            <field name="investigation_type">系统表单</field>-->
<!--            <field name="model_id" ref="accountcore.account_inventory"/>-->
<!--            <field name="investigation_way">单击指定字段联查</field>-->
<!--            <field name="name">会计科目</field>-->
<!--        </record>-->
    </data>
    <data noupdate="0">
        <record id="roke_query_investigation_model_detailed_account_01" model="roke.query.investigation.model">
            <field name="investigation_type">系统表单</field>
            <field name="model_id" ref="accountcore.model_accountcore_voucher"/>
            <field name="model_view_id" ref="accountcore.accountcore_voucher_form"/>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">凭证号</field>
        </record>
    </data>
    <data noupdate="0">
        <record id="roke_query_investigation_model_account_balance_account_01" model="roke.query.investigation.model">
            <field name="investigation_type">自定义报表</field>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">科目名称</field>
        </record>
    </data>
    <!--处理报表联查-->
    <function model="accountcore.entry" name="write_sql_query_investigation_general" eval="[[]]"/>
</odoo>