<odoo>
    <!-- 初始化数据 -->
    <data noupdate="1">
        <!-- 报表模板-开始 -->
        <record id='report_model_1' model='accountcore.report_model'>
            <field name='report_type' eval='report_type_1' />
            <field name='name' >资产负债表(预设)</field>
            <field name='version' >2019</field>
            <field name='orgs' eval='[(6, 0, [ref("org_1")])]' />
            <field name='data' >[["资产负债表","","","","","","",""],["=endDate()","","","","","","",""],["=show_orgs()","","","","","","",""],["资 产","行次","期末余额","年初余额","负债和所有者权益","行次","期末余额","年初余额"],["流动资产:","","","","流动负债:","","",""],["货币资金","1","=account('银行存款','True','期末余额','')+account('库存现金','True','期末余额','')+account('其他货币资金','True','期末余额','')","=account('库存现金','True','期初余额','')+account('银行存款','True','期初余额','')+account('其他货币资金','True','期初余额','')","短期借款","31","=account('短期借款','True','期末余额','')","=account('短期借款','True','期初余额','')"],["短期投资","2","","","应付票据","32","=account('应付票据','True','期末余额','')","=account('应付票据','True','期初余额','')"],["应收票据","3","=account('应收票据','True','期末余额','')","=account('应收票据','True','期初余额','')","应付账款","33","=account('应付账款','True','期末贷方余额','')+account('预付账款','True','期末贷方余额','')","=account('应付账款','True','期初贷方余额','')+account('预付账款','True','期初贷方余额','')"],["应收账款","4","=account('应收账款','True','期末借方余额','')+account('预收账款','True','期末借方余额','')","=account('应收账款','True','期初借方余额','')+account('预收账款','True','期初借方余额','')","预收账款","34","=account('预收账款','True','期末贷方余额','')+account('应收账款','True','期末贷方余额','')","=account('预收账款','True','期初贷方余额','')+account('应收账款','True','期初贷方余额','')"],["预付账款","5","=account('预付账款','True','期末借方余额','')+account('应付账款','True','期末借方余额','')","=account('预付账款','True','期初借方余额','')+account('应付账款','True','期初借方余额','')","应付职工薪酬","35","=account('应付职工薪酬','True','期末余额','')","=account('应付职工薪酬','True','期初余额','')"],["应收股利","6","=account('应收股利','True','期末余额','')","=account('应收股利','True','期初余额','')","应交税费","36","=account('应交税费','True','期末余额','')","=account('应交税费','True','期初余额','')"],["应收利息","7","=account('应收利息','True','期末余额','')","=account('应收利息','True','期初余额','')","应付利息","37","=account('应付利息','True','期末余额','')","=account('应付利息','True','期初余额','')"],["其他应收款","8","=account('其他应收款','True','期末余额','')","=account('其他应收款','True','期初余额','')","应付利润","38","",""],["存货","9","=account('材料采购','True','期末余额','')+account('在途物资','True','期末余额','')+account('原材料','True','期末余额','')+account('材料成本差异','True','期末余额','')+account('库存商品','True','期末余额','')+account('发出商品','True','期末余额','')+account('商品进销差价','True','期末余额','')+account('委托加工物资','True','期末余额','')+account('低值易耗品','True','期末余额','')+account('消耗性物物资产','True','期末余额','')+account('周转材料','True','期末余额','')+account('贵金属','True','期末余额','')+account('抵债资产','True','期末余额','')+account('存货跌价准备','True','期末余额','')+account('损余物资','True','期末余额','')+account('生产成本','True','期末余额','')","=account('材料采购','True','期初余额','')+account('在途物资','True','期初余额','')+account('原材料','True','期初余额','')+account('材料成本差异','True','期初余额','')+account('库存商品','True','期初余额','')+account('发出商品','True','期初余额','')+account('商品进销差价','True','期初余额','')+account('委托加工物资','True','期初余额','')+account('低值易耗品','True','期初余额','')+account('消耗性物物资产','True','期初余额','')+account('周转材料','True','期初余额','')+account('贵金属','True','期初余额','')+account('抵债资产','True','期初余额','')+account('存货跌价准备','True','期初余额','')+account('损余物资','True','期初余额','')\n+account('生产成本','True','期末余额','')","其他应付款","39","=account('其他应付款','True','期末余额','')","=account('其他应付款','True','期初余额','')"],["其中:原材料","10","=account('原材料','True','期末余额','')","=account('原材料','True','期初余额','')","其他流动负债","40","",""],["在产品","11","","","流动负债合计","41","=SUM(G6:G15)","=SUM(H6:H15)"],["库存商品","12","=account('库存商品','True','期末余额','')","=account('库存商品','True','期初余额','')","非流动负债:","","",""],["周转材料","13","=account('周转材料','True','期末余额','')","=account('周转材料','True','期初余额','')","长期借款","42","=account('长期借款','True','期末余额','')","=account('长期借款','True','期初余额','')"],["其他流动资产","14","","","长期应付款","43","=account('长期应付款','True','期末余额','')","=account('长期应付款','True','期初余额','')"],["流动资产合计","15","=SUM(C6:C14,C19)","=SUM(D6:D14,D19)","递延收益","44","=account('递延收益','True','期末余额','')","=account('递延收益','True','期初余额','')"],[" 非流动资产:","","","","其他非流动负债","45","",""],["长期债券投资","16","","","非流动负债合计","46","=SUM(G18:G21)","=SUM(H18:H21)"],["长期股权投资","17","=account('长期股权投资','True','期末余额','')","=account('长期股权投资','True','期初余额','')","负债合计","47","=SUM(G16,G22)","=SUM(H16,H22)"],["固定资产原价","18","=account('固定资产','True','期末余额','')","=account('固定资产','True','期初余额','')","","","",""],["减:累计折旧","19","=account('累计折旧','True','期末余额','')","=account('累计折旧','True','期初余额','')","","","",""],["固定资产账面价值","20","=C24-C25","=D24-D25","","","",""],["在建工程","21","=account('在建工程','True','期末余额','')","=account('在建工程','True','期初余额','')","","","",""],["工程物资","22","=account('工程物资','True','期末余额','')","=account('工程物资','True','期初余额','')","","","",""],["固定资产清理","23","=account('固定资产清理','True','期末余额','')","=account('固定资产清理','True','期初余额','')","","","",""],["生物性生物资产","24","=account('生产性生物资产','True','期末余额','')-account('生产性生物资产累计折旧','True','期末余额','')","=account('生产性生物资产','True','期初余额','')-account('生产性生物资产累计折旧','True','期初余额','')","所有者权益(或股东权益):","","",""],["无形资产","25","=account('无形资产','True','期末余额','')-account('无形资产减值准备','True','期末余额','')-account('累计摊销','True','期末余额','')","=account('无形资产','True','期初余额','')-account('无形资产减值准备','True','期初余额','')-account('累计摊销','True','期初余额','')","实收资本(或股本)","48","=account('实收资本','True','期末余额','')","=account('实收资本','True','期初余额','')"],["开发支出","26","=account('研发支出','True','期末余额','')","=account('研发支出','True','期初余额','')","资本公积","49","=account('资本公积','True','期末余额','')","=account('资本公积','True','期初余额','')"],["长期待摊费用","27","=account('长期待摊费用','True','期末余额','')","=account('长期待摊费用','True','期初余额','')","盈余公积","50","=account('盈余公积','True','期末余额','')","=account('盈余公积','True','期初余额','')"],["其他非流动资产","28","","","未分配利润","51","=account('本年利润','True','期末余额','')+account('利润分配','True','期末余额','')","=account('本年利润','True','期初余额','')+account('利润分配','True','期初余额','')"],["非流动资产合计","29","=SUM(SUM(C22,C23,),SUM(C26:C34))","=SUM(SUM(D22,D23,),SUM(D26:D34))","所有者权益或股东权益）合计","52","=SUM(G31:G34)","=SUM(H31:H34)"],["资产合计","30","=SUM(C20,C35)","=SUM(D20,D35)","负债和所有者权益   \n（或股东权益）总计","53","=SUM(G23,G35)","=SUM(H23,H35)"]]</field>
            <field name='data_style' >{"A1":"text-align: center; white-space: pre-wrap; font-size: 28px;","B1":"text-align: center; white-space: pre-wrap; font-size: 28px;","C1":"text-align: center; white-space: pre-wrap; font-size: 28px;","D1":"text-align: center; white-space: pre-wrap; font-size: 28px;","E1":"text-align: center; white-space: pre-wrap; font-size: 28px;","F1":"text-align: center; white-space: pre-wrap; font-size: 28px;","G1":"text-align: center; white-space: pre-wrap; font-size: 28px;","H1":"text-align: center; white-space: pre-wrap; font-size: 28px;","A2":"text-align: center; white-space: pre-wrap;","B2":"text-align: center; white-space: pre-wrap;","C2":"text-align: center; white-space: pre-wrap;","D2":"text-align: center; white-space: pre-wrap;","E2":"text-align: center; white-space: pre-wrap;","F2":"text-align: center; white-space: pre-wrap;","G2":"text-align: center; white-space: pre-wrap;","H2":"text-align: center; white-space: pre-wrap;","A3":"text-align: left; white-space: pre-wrap;","B3":"text-align: left; white-space: pre-wrap;","C3":"text-align: left; white-space: pre-wrap;","D3":"text-align: left; white-space: pre-wrap;","E3":"text-align: left; white-space: pre-wrap;","F3":"text-align: left; white-space: pre-wrap;","G3":"text-align: left; white-space: pre-wrap;","H3":"text-align: left; white-space: pre-wrap;","A4":"text-align: center; white-space: pre-wrap; overflow: hidden;","B4":"text-align: center; white-space: pre-wrap; overflow: hidden;","C4":"text-align: center; white-space: pre-wrap; overflow: hidden;","D4":"text-align: center; white-space: pre-wrap; overflow: hidden;","E4":"text-align: center; white-space: pre-wrap; overflow: hidden;","F4":"text-align: center; white-space: pre-wrap; overflow: hidden;","G4":"text-align: center; white-space: pre-wrap; overflow: hidden;","H4":"text-align: center; white-space: pre-wrap; overflow: hidden;","A5":"text-align: center; white-space: pre-wrap; font-weight: bold;","B5":"text-align: center; white-space: pre-wrap;","C5":"text-align: right; white-space: pre-wrap;","D5":"text-align: right; white-space: pre-wrap; overflow: hidden;","E5":"text-align: center; white-space: pre-wrap; font-weight: bold;","F5":"text-align: center; white-space: pre-wrap;","G5":"text-align: right; white-space: pre-wrap;","H5":"text-align: right; white-space: pre-wrap; overflow: hidden;","A6":"text-align: center; white-space: pre-wrap; overflow: hidden;","B6":"text-align: center; white-space: pre-wrap; overflow: hidden;","C6":"text-align: right; white-space: pre-wrap; overflow: hidden;","D6":"text-align: right; white-space: pre-wrap; overflow: hidden;","E6":"text-align: center; white-space: pre-wrap; overflow: hidden;","F6":"text-align: center; white-space: pre-wrap; overflow: hidden;","G6":"text-align: right; white-space: pre-wrap; overflow: hidden;","H6":"text-align: right; white-space: pre-wrap; overflow: hidden;","A7":"text-align: center; white-space: pre-wrap; overflow: hidden;","B7":"text-align: center; white-space: pre-wrap; overflow: hidden;","C7":"text-align: right; white-space: pre-wrap;","D7":"text-align: right; white-space: pre-wrap; overflow: hidden;","E7":"text-align: center; white-space: pre-wrap; overflow: hidden;","F7":"text-align: center; white-space: pre-wrap; overflow: hidden;","G7":"text-align: right; white-space: pre-wrap; overflow: hidden;","H7":"text-align: right; white-space: pre-wrap; overflow: hidden;","A8":"text-align: center; white-space: pre-wrap; overflow: hidden;","B8":"text-align: center; white-space: pre-wrap; overflow: hidden;","C8":"text-align: right; white-space: pre-wrap; overflow: hidden;","D8":"text-align: right; white-space: pre-wrap; overflow: hidden;","E8":"text-align: center; white-space: pre-wrap; overflow: hidden;","F8":"text-align: center; white-space: pre-wrap; overflow: hidden;","G8":"text-align: right; white-space: pre-wrap; overflow: hidden;","H8":"text-align: right; white-space: pre-wrap; overflow: hidden;","A9":"text-align: center; white-space: pre-wrap; overflow: hidden;","B9":"text-align: center; white-space: pre-wrap; overflow: hidden;","C9":"text-align: right; white-space: pre-wrap; overflow: hidden;","D9":"text-align: right; white-space: pre-wrap; overflow: hidden;","E9":"text-align: center; white-space: pre-wrap; overflow: hidden;","F9":"text-align: center; white-space: pre-wrap; overflow: hidden;","G9":"text-align: right; white-space: pre-wrap; overflow: hidden;","H9":"text-align: right; white-space: pre-wrap; overflow: hidden;","A10":"text-align: center; white-space: pre-wrap; overflow: hidden;","B10":"text-align: center; white-space: pre-wrap; overflow: hidden;","C10":"text-align: right; white-space: pre-wrap; overflow: hidden;","D10":"text-align: right; white-space: pre-wrap; overflow: hidden;","E10":"text-align: center; white-space: pre-wrap; overflow: hidden;","F10":"text-align: center; white-space: pre-wrap; overflow: hidden;","G10":"text-align: right; white-space: pre-wrap; overflow: hidden;","H10":"text-align: right; white-space: pre-wrap; overflow: hidden;","A11":"text-align: center; white-space: pre-wrap; overflow: hidden;","B11":"text-align: center; white-space: pre-wrap; overflow: hidden;","C11":"text-align: right; white-space: pre-wrap; overflow: hidden;","D11":"text-align: right; white-space: pre-wrap; overflow: hidden;","E11":"text-align: center; white-space: pre-wrap; overflow: hidden;","F11":"text-align: center; white-space: pre-wrap; overflow: hidden;","G11":"text-align: right; white-space: pre-wrap; overflow: hidden;","H11":"text-align: right; white-space: pre-wrap; overflow: hidden;","A12":"text-align: center; white-space: pre-wrap; overflow: hidden;","B12":"text-align: center; white-space: pre-wrap; overflow: hidden;","C12":"text-align: right; white-space: pre-wrap; overflow: hidden;","D12":"text-align: right; white-space: pre-wrap; overflow: hidden;","E12":"text-align: center; white-space: pre-wrap; overflow: hidden;","F12":"text-align: center; white-space: pre-wrap; overflow: hidden;","G12":"text-align: right; white-space: pre-wrap; overflow: hidden;","H12":"text-align: right; white-space: pre-wrap; overflow: hidden;","A13":"text-align: center; white-space: pre-wrap; overflow: hidden;","B13":"text-align: center; white-space: pre-wrap; overflow: hidden;","C13":"text-align: right; white-space: pre-wrap; overflow: hidden;","D13":"text-align: right; white-space: pre-wrap; overflow: hidden;","E13":"text-align: center; white-space: pre-wrap; overflow: hidden;","F13":"text-align: center; white-space: pre-wrap; overflow: hidden;","G13":"text-align: right; white-space: pre-wrap;","H13":"text-align: right; white-space: pre-wrap; overflow: hidden;","A14":"text-align: center; white-space: pre-wrap; overflow: hidden;","B14":"text-align: center; white-space: pre-wrap; overflow: hidden;","C14":"text-align: right; white-space: pre-wrap; overflow: hidden;","D14":"text-align: right; white-space: pre-wrap; overflow: hidden;","E14":"text-align: center; white-space: pre-wrap; overflow: hidden;","F14":"text-align: center; white-space: pre-wrap; overflow: hidden;","G14":"text-align: right; white-space: pre-wrap; overflow: hidden;","H14":"text-align: right; white-space: pre-wrap; overflow: hidden;","A15":"text-align: right; white-space: pre-wrap; overflow: hidden;","B15":"text-align: center; white-space: pre-wrap; overflow: hidden;","C15":"text-align: right; white-space: pre-wrap; overflow: hidden;","D15":"text-align: right; white-space: pre-wrap; overflow: hidden;","E15":"text-align: center; white-space: pre-wrap; overflow: hidden;","F15":"text-align: center; white-space: pre-wrap;","G15":"text-align: right; white-space: pre-wrap;","H15":"text-align: right; white-space: pre-wrap; overflow: hidden;","A16":"text-align: right; white-space: pre-wrap; overflow: hidden;","B16":"text-align: center; white-space: pre-wrap;","C16":"text-align: right; white-space: pre-wrap;","D16":"text-align: right; white-space: pre-wrap; overflow: hidden;","E16":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","F16":"text-align: center; white-space: pre-wrap; overflow: hidden;","G16":"text-align: right; white-space: pre-wrap; overflow: hidden;","H16":"text-align: right; white-space: pre-wrap; overflow: hidden;","A17":"text-align: right; white-space: pre-wrap; overflow: hidden;","B17":"text-align: center; white-space: pre-wrap; overflow: hidden;","C17":"text-align: right; white-space: pre-wrap; overflow: hidden;","D17":"text-align: right; white-space: pre-wrap; overflow: hidden;","E17":"text-align: center; white-space: pre-wrap; font-weight: bold;","F17":"text-align: center; white-space: pre-wrap;","G17":"text-align: right; white-space: pre-wrap;","H17":"text-align: right; white-space: pre-wrap; overflow: hidden;","A18":"text-align: right; white-space: pre-wrap; overflow: hidden;","B18":"text-align: center; white-space: pre-wrap; overflow: hidden;","C18":"text-align: right; white-space: pre-wrap; overflow: hidden;","D18":"text-align: right; white-space: pre-wrap; overflow: hidden;","E18":"text-align: center; white-space: pre-wrap; overflow: hidden;","F18":"text-align: center; white-space: pre-wrap; overflow: hidden;","G18":"text-align: right; white-space: pre-wrap; overflow: hidden;","H18":"text-align: right; white-space: pre-wrap; overflow: hidden;","A19":"text-align: center; white-space: pre-wrap; overflow: hidden;","B19":"text-align: center; white-space: pre-wrap;","C19":"text-align: right; white-space: pre-wrap;","D19":"text-align: right; white-space: pre-wrap; overflow: hidden;","E19":"text-align: center; white-space: pre-wrap; overflow: hidden;","F19":"text-align: center; white-space: pre-wrap; overflow: hidden;","G19":"text-align: right; white-space: pre-wrap; overflow: hidden;","H19":"text-align: right; white-space: pre-wrap; overflow: hidden;","A20":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B20":"text-align: center; white-space: pre-wrap; overflow: hidden;","C20":"text-align: right; white-space: pre-wrap; overflow: hidden;","D20":"text-align: right; white-space: pre-wrap; overflow: hidden;","E20":"text-align: center; white-space: pre-wrap; overflow: hidden;","F20":"text-align: center; white-space: pre-wrap; overflow: hidden;","G20":"text-align: right; white-space: pre-wrap; overflow: hidden;","H20":"text-align: right; white-space: pre-wrap; overflow: hidden;","A21":"text-align: center; white-space: pre-wrap; font-weight: bold;","B21":"text-align: center; white-space: pre-wrap;","C21":"text-align: right; white-space: pre-wrap;","D21":"text-align: right; white-space: pre-wrap; overflow: hidden;","E21":"text-align: center; white-space: pre-wrap; overflow: hidden;","F21":"text-align: center; white-space: pre-wrap;","G21":"text-align: right; white-space: pre-wrap;","H21":"text-align: right; white-space: pre-wrap; overflow: hidden;","A22":"text-align: center; white-space: pre-wrap; overflow: hidden;","B22":"text-align: center; white-space: pre-wrap; overflow: hidden;","C22":"text-align: right; white-space: pre-wrap;","D22":"text-align: right; white-space: pre-wrap; overflow: hidden;","E22":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","F22":"text-align: center; white-space: pre-wrap; overflow: hidden;","G22":"text-align: right; white-space: pre-wrap; overflow: hidden;","H22":"text-align: right; white-space: pre-wrap; overflow: hidden;","A23":"text-align: center; white-space: pre-wrap; overflow: hidden;","B23":"text-align: center; white-space: pre-wrap; overflow: hidden;","C23":"text-align: right; white-space: pre-wrap; overflow: hidden;","D23":"text-align: right; white-space: pre-wrap; overflow: hidden;","E23":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","F23":"text-align: center; white-space: pre-wrap; overflow: hidden;","G23":"text-align: right; white-space: pre-wrap; overflow: hidden;","H23":"text-align: right; white-space: pre-wrap; overflow: hidden;","A24":"text-align: center; white-space: pre-wrap; overflow: hidden;","B24":"text-align: center; white-space: pre-wrap; overflow: hidden;","C24":"text-align: right; white-space: pre-wrap; overflow: hidden;","D24":"text-align: right; white-space: pre-wrap;","E24":"text-align: center; white-space: pre-wrap;","F24":"text-align: center; white-space: pre-wrap;","G24":"text-align: right; white-space: pre-wrap;","H24":"text-align: right; white-space: pre-wrap; overflow: hidden;","A25":"text-align: right; white-space: pre-wrap; overflow: hidden;","B25":"text-align: center; white-space: pre-wrap; overflow: hidden;","C25":"text-align: right; white-space: pre-wrap; overflow: hidden;","D25":"text-align: right; white-space: pre-wrap;","E25":"text-align: center; white-space: pre-wrap;","F25":"text-align: center; white-space: pre-wrap;","G25":"text-align: right; white-space: pre-wrap;","H25":"text-align: right; white-space: pre-wrap; overflow: hidden;","A26":"text-align: center; white-space: pre-wrap; overflow: hidden;","B26":"text-align: center; white-space: pre-wrap; overflow: hidden;","C26":"text-align: right; white-space: pre-wrap; overflow: hidden;","D26":"text-align: right; white-space: pre-wrap;","E26":"text-align: center; white-space: pre-wrap;","F26":"text-align: center; white-space: pre-wrap;","G26":"text-align: right; white-space: pre-wrap;","H26":"text-align: right; white-space: pre-wrap; overflow: hidden;","A27":"text-align: center; white-space: pre-wrap; overflow: hidden;","B27":"text-align: center; white-space: pre-wrap; overflow: hidden;","C27":"text-align: right; white-space: pre-wrap; overflow: hidden;","D27":"text-align: right; white-space: pre-wrap;","E27":"text-align: center; white-space: pre-wrap;","F27":"text-align: center; white-space: pre-wrap;","G27":"text-align: right; white-space: pre-wrap;","H27":"text-align: right; white-space: pre-wrap; overflow: hidden;","A28":"text-align: center; white-space: pre-wrap; overflow: hidden;","B28":"text-align: center; white-space: pre-wrap; overflow: hidden;","C28":"text-align: right; white-space: pre-wrap; overflow: hidden;","D28":"text-align: right; white-space: pre-wrap;","E28":"text-align: center; white-space: pre-wrap;","F28":"text-align: center; white-space: pre-wrap;","G28":"text-align: right; white-space: pre-wrap;","H28":"text-align: right; white-space: pre-wrap; overflow: hidden;","A29":"text-align: center; white-space: pre-wrap; overflow: hidden;","B29":"text-align: center; white-space: pre-wrap; overflow: hidden;","C29":"text-align: right; white-space: pre-wrap; overflow: hidden;","D29":"text-align: right; white-space: pre-wrap;","E29":"text-align: center; white-space: pre-wrap;","F29":"text-align: center; white-space: pre-wrap;","G29":"text-align: right; white-space: pre-wrap;","H29":"text-align: right; white-space: pre-wrap; overflow: hidden;","A30":"text-align: center; white-space: pre-wrap; overflow: hidden;","B30":"text-align: center; white-space: pre-wrap; overflow: hidden;","C30":"text-align: right; white-space: pre-wrap; overflow: hidden;","D30":"text-align: right; white-space: pre-wrap; overflow: hidden;","E30":"text-align: center; white-space: pre-wrap; font-weight: bold;","F30":"text-align: center; white-space: pre-wrap;","G30":"text-align: right; white-space: pre-wrap;","H30":"text-align: right; white-space: pre-wrap; overflow: hidden;","A31":"text-align: center; white-space: pre-wrap; overflow: hidden;","B31":"text-align: center; white-space: pre-wrap; overflow: hidden;","C31":"text-align: right; white-space: pre-wrap; overflow: hidden;","D31":"text-align: right; white-space: pre-wrap; overflow: hidden;","E31":"text-align: center; white-space: pre-wrap; overflow: hidden;","F31":"text-align: center; white-space: pre-wrap; overflow: hidden;","G31":"text-align: right; white-space: pre-wrap; overflow: hidden;","H31":"text-align: right; white-space: pre-wrap; overflow: hidden;","A32":"text-align: center; white-space: pre-wrap; overflow: hidden;","B32":"text-align: center; white-space: pre-wrap; overflow: hidden;","C32":"text-align: right; white-space: pre-wrap; overflow: hidden;","D32":"text-align: right; white-space: pre-wrap; overflow: hidden;","E32":"text-align: center; white-space: pre-wrap; overflow: hidden;","F32":"text-align: center; white-space: pre-wrap; overflow: hidden;","G32":"text-align: right; white-space: pre-wrap; overflow: hidden;","H32":"text-align: right; white-space: pre-wrap; overflow: hidden;","A33":"text-align: center; white-space: pre-wrap; overflow: hidden;","B33":"text-align: center; white-space: pre-wrap; overflow: hidden;","C33":"text-align: right; white-space: pre-wrap; overflow: hidden;","D33":"text-align: right; white-space: pre-wrap; overflow: hidden;","E33":"text-align: center; white-space: pre-wrap; overflow: hidden;","F33":"text-align: center; white-space: pre-wrap; overflow: hidden;","G33":"text-align: right; white-space: pre-wrap; overflow: hidden;","H33":"text-align: right; white-space: pre-wrap; overflow: hidden;","A34":"text-align: center; white-space: pre-wrap; overflow: hidden;","B34":"text-align: center; white-space: pre-wrap; overflow: hidden;","C34":"text-align: right; white-space: pre-wrap;","D34":"text-align: right; white-space: pre-wrap; overflow: hidden;","E34":"text-align: center; white-space: pre-wrap; overflow: hidden;","F34":"text-align: center; white-space: pre-wrap; overflow: hidden;","G34":"text-align: right; white-space: pre-wrap; overflow: hidden;","H34":"text-align: right; white-space: pre-wrap; overflow: hidden;","A35":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B35":"text-align: center; white-space: pre-wrap; overflow: hidden;","C35":"text-align: right; white-space: pre-wrap; overflow: hidden;","D35":"text-align: right; white-space: pre-wrap; overflow: hidden;","E35":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","F35":"text-align: center; white-space: pre-wrap; overflow: hidden;","G35":"text-align: right; white-space: pre-wrap; overflow: hidden;","H35":"text-align: right; white-space: pre-wrap; overflow: hidden;","A36":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B36":"text-align: center; white-space: pre-wrap; overflow: hidden;","C36":"text-align: right; white-space: pre-wrap; overflow: hidden;","D36":"text-align: right; white-space: pre-wrap; overflow: hidden;","E36":"text-align: center; white-space: pre-wrap; overflow: hidden; font-weight: bold;","F36":"text-align: center; white-space: pre-wrap; overflow: hidden;","G36":"text-align: right; white-space: pre-wrap; overflow: hidden;","H36":"text-align: right; white-space: pre-wrap; overflow: hidden;"}</field>
            <field name='width_info' >{"0":"145","1":"28","2":"140","3":"146","4":"152","5":"31","6":"143","7":"150"}</field>
            <field name='height_info' >{"0":"25","1":"25","2":"25","3":"25","4":"25","5":"25","6":"25","7":"25","8":"25","9":"25","10":"25","11":"25","12":"25","13":"25","14":"25","15":"25","16":"25","17":"25","18":"25","19":"25","20":"25","21":"25","22":"25","23":"25","24":"25","25":"25","26":"25","27":"25","28":"25","29":"25","30":"25","31":"25","32":"25","33":"25","34":"25","35":"25"}</field>
            <field name='comments_info' >{}</field>
            <field name='merge_info' >{"A1":[8,1],"A2":[8,1],"A3":[8,1]}</field>
            <field name='summary' >系统预设,根据需要修改</field>
        </record>
        <record id='report_model_2' model='accountcore.report_model'>
            <field name='report_type' eval='report_type_2' />
            <field name='name' >利润表(预设)</field>
            <field name='version' >2019</field>
            <field name='orgs' eval='[(6, 0, [ref("org_1")])]' />
            <field name='data' >[["利润表","","",""],["=betweenDate()","","",""],["=show_orgs()","","",""],["项目","行次","本期金额","本年累计金额"],["一、营业收入","1","=account('主营业务收入','True','损益表本期实际发生额','')+account('其他业务收入','True','损益表本期实际发生额','')","=account('主营业务收入','True','损益表本年实际发生额','')+account('其他业务收入','True','损益表本年实际发生额','')"],["减：营业成本","2","=account('主营业务成本','True','损益表本期实际发生额','')+account('其他业务成本','True','损益表本期实际发生额','')","=account('主营业务成本','True','损益表本年实际发生额','')+account('其他业务成本','True','损益表本年实际发生额','')"],["营业税金及附加","3","=account('营业税金及附加','True','损益表本期实际发生额','')","=account('营业税金及附加','True','损益表本年实际发生额','')"],["   其中:消费税","4","",""],["   营业税","5","",""],["   城市维护建设税","6","",""],["   资源税  ","7","",""],["   土地增值税","8","",""],["   城镇土地使用税、房产税、车船税、印花税 ","9","",""],[" 教育费附加、矿产资源补偿费、排污费","10","",""],["销售费用","11","=account('销售费用','True','损益表本期实际发生额','')","=account('销售费用','True','损益表本年实际发生额','')"],[" 其中:商品维修费","12","=account('销售费用','False','损益表本期实际发生额','商品维修费/')","=account('销售费用','True','损益表本年实际发生额','商品维修费/')"],[" 广告费和业务宣传费","13","=account('销售费用','True','损益表本期实际发生额','广告费/')","=account('销售费用','True','损益表本年实际发生额','广告费/')"],["管理费用","14","=account('管理费用','True','损益表本期实际发生额','')","=account('管理费用','True','损益表本年实际发生额','')"],[" 其中:开办费","15","=account('管理费用','True','损益表本期实际发生额','开办费/')","=account('管理费用','True','损益表本年实际发生额','开办费/')"],[" 业务招待费","16","=account('管理费用','True','损益表本期实际发生额','业务招待费/')","=account('管理费用','True','损益表本年实际发生额','业务招待费/')"],[" 研究费用","17","=account('管理费用','True','损益表本期实际发生额','研究费用/')","=account('管理费用','True','损益表本年实际发生额','研究费用/')"],["财务费用","18","=account('财务费用','True','损益表本期实际发生额','')","=account('财务费用','True','损益表本年实际发生额','')"],[" 其中:利息费用（收入以“-”填列）","19","=account('财务费用','True','损益表本期实际发生额','利息收入/')","=-account('财务费用','True','损益表本年实际发生额','利息收入/')"],["加:投资收益（亏损以“-”填列）","20","=account('投资收益','True','损益表本期实际发生额','')","=account('投资收益','True','损益表本年实际发生额','')"],["二、营业利润（亏损以“-”填列）","21","=SUM(C5,-C6,-C7,-C15,-C18,-C22,C24)","=SUM(D5,-D6,-D7-D15,-D18,-D22,D24)"],["加:营业外收入","22","=account('营业外收入','True','损益表本期实际发生额','')","=account('营业外收入','True','损益表本年实际发生额','')"],[" 其中:政府补助","23","=account('营业外收入---政府补助','True','损益表本期实际发生额','')","=account('营业外收入---政府补助','True','损益表本年实际发生额','')"],["减:营业外支出","24","=account('营业外支出','True','损益表本期实际发生额','')","=account('营业外支出','True','损益表本年实际发生额','')"],[" 其中:坏账损失","25","=account('营业外支出---坏账损失','True','损益表本期实际发生额','')","=account('营业外支出---坏账损失','True','损益表本年实际发生额','')"],[" 无法收回的长期债券投资损失","26","=account('营业外支出---无法收回的长期债券投资损失','True','损益表本期实际发生额','')","=account('营业外支出---无法收回的长期债券投资损失','True','损益表本年实际发生额','')"],[" 无法收回的长期股权投资损失","27","=account('营业外支出---无法收回的长期股权投资损失','True','损益表本期实际发生额','')","=account('营业外支出---无法收回的长期股权投资损失','True','损益表本年实际发生额','')"],["   自然灾害等不可抗力因素造成的损失    ","28","=account('营业外支出---自然灾害等不可抗力因素造成的损失','True','损益表本期实际发生额','')","=account('营业外支出---自然灾害等不可抗力因素造成的损失','True','损益表本年实际发生额','')"],[" 税收滞纳金","29","=account('营业外支出---税收滞纳金','True','损益表本期实际发生额','')","=account('营业外支出---税收滞纳金','True','损益表本年实际发生额','')"],["三、利润总额(亏损总额以“-”号填列)","30","=SUM(C25,C26,-D28)","=SUM(D25,D26,-D28)"],["减:所得税费用","31","=account('所得税','True','损益表本期实际发生额','')","=account('所得税','True','损益表本年实际发生额','')"],["四、净利润(净亏损以“-”号填列)","32","=SUM(C34,-C35)","=SUM(D34,-D35)"]]</field>
            <field name='data_style' >{"A1":"text-align: center; white-space: pre-wrap; font-size: 28px;","B1":"text-align: center; white-space: pre-wrap; font-size: 28px;","C1":"text-align: center; white-space: pre-wrap; font-size: 28px;","D1":"text-align: center; white-space: pre-wrap; font-size: 28px;","A2":"text-align: center; white-space: pre-wrap;","B2":"text-align: center; white-space: pre-wrap;","C2":"text-align: center; white-space: pre-wrap;","D2":"text-align: center; white-space: pre-wrap;","A3":"text-align: left; white-space: pre-wrap;","B3":"text-align: left; white-space: pre-wrap;","C3":"text-align: left; white-space: pre-wrap;","D3":"text-align: left; white-space: pre-wrap;","A4":"text-align: center; white-space: pre-wrap; overflow: hidden;","B4":"text-align: center; white-space: pre-wrap; overflow: hidden;","C4":"text-align: center; white-space: pre-wrap; overflow: hidden;","D4":"text-align: center; white-space: pre-wrap; overflow: hidden;","A5":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B5":"text-align: center; white-space: pre-wrap; overflow: hidden;","C5":"text-align: right; white-space: pre-wrap; overflow: hidden;","D5":"text-align: right; white-space: pre-wrap; overflow: hidden;","A6":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B6":"text-align: center; white-space: pre-wrap; overflow: hidden;","C6":"text-align: right; white-space: pre-wrap; overflow: hidden;","D6":"text-align: right; white-space: pre-wrap; overflow: hidden;","A7":"text-align: left; white-space: pre-wrap; overflow: hidden;","B7":"text-align: center; white-space: pre-wrap; overflow: hidden;","C7":"text-align: right; white-space: pre-wrap; overflow: hidden;","D7":"text-align: right; white-space: pre-wrap; overflow: hidden;","A8":"text-align: left; white-space: pre-wrap; overflow: hidden;","B8":"text-align: center; white-space: pre-wrap; overflow: hidden;","C8":"text-align: right; white-space: pre-wrap; overflow: hidden;","D8":"text-align: right; white-space: pre-wrap; overflow: hidden;","A9":"white-space: pre-wrap; overflow: hidden; text-align: left;","B9":"text-align: center; white-space: pre-wrap;","C9":"text-align: right; white-space: pre-wrap;","D9":"text-align: right; white-space: pre-wrap; overflow: hidden;","A10":"white-space: pre-wrap; overflow: hidden; text-align: left;","B10":"text-align: center; white-space: pre-wrap; overflow: hidden;","C10":"text-align: right; white-space: pre-wrap; overflow: hidden;","D10":"text-align: right; white-space: pre-wrap; overflow: hidden;","A11":"white-space: pre-wrap; overflow: hidden; text-align: left;","B11":"text-align: center; white-space: pre-wrap; overflow: hidden;","C11":"text-align: right; white-space: pre-wrap; overflow: hidden;","D11":"text-align: right; white-space: pre-wrap; overflow: hidden;","A12":"white-space: pre-wrap; overflow: hidden; text-align: left;","B12":"text-align: center; white-space: pre-wrap; overflow: hidden;","C12":"text-align: right; white-space: pre-wrap; overflow: hidden;","D12":"text-align: right; white-space: pre-wrap; overflow: hidden;","A13":"white-space: pre-wrap; overflow: hidden; text-align: left;","B13":"text-align: center; white-space: pre-wrap; overflow: hidden;","C13":"text-align: right; white-space: pre-wrap; overflow: hidden;","D13":"text-align: right; white-space: pre-wrap; overflow: hidden;","A14":"white-space: pre-wrap; overflow: hidden; text-align: left;","B14":"text-align: center; white-space: pre-wrap; overflow: hidden;","C14":"text-align: right; white-space: pre-wrap; overflow: hidden;","D14":"text-align: right; white-space: pre-wrap; overflow: hidden;","A15":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B15":"text-align: center; white-space: pre-wrap; overflow: hidden;","C15":"text-align: right; white-space: pre-wrap; overflow: hidden;","D15":"text-align: right; white-space: pre-wrap; overflow: hidden;","A16":"text-align: left; white-space: pre-wrap; overflow: hidden;","B16":"text-align: center; white-space: pre-wrap; overflow: hidden;","C16":"text-align: right; white-space: pre-wrap; overflow: hidden;","D16":"text-align: right; white-space: pre-wrap; overflow: hidden;","A17":"text-align: left; white-space: pre-wrap; overflow: hidden;","B17":"text-align: center; white-space: pre-wrap; overflow: hidden;","C17":"text-align: right; white-space: pre-wrap; overflow: hidden;","D17":"text-align: right; white-space: pre-wrap; overflow: hidden;","A18":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B18":"text-align: center; white-space: pre-wrap; overflow: hidden;","C18":"text-align: right; white-space: pre-wrap; overflow: hidden;","D18":"text-align: right; white-space: pre-wrap; overflow: hidden;","A19":"text-align: left; white-space: pre-wrap; overflow: hidden;","B19":"text-align: center; white-space: pre-wrap; overflow: hidden;","C19":"text-align: right; white-space: pre-wrap; overflow: hidden;","D19":"text-align: right; white-space: pre-wrap; overflow: hidden;","A20":"text-align: left; white-space: pre-wrap; overflow: hidden;","B20":"text-align: center; white-space: pre-wrap; overflow: hidden;","C20":"text-align: right; white-space: pre-wrap; overflow: hidden;","D20":"text-align: right; white-space: pre-wrap; overflow: hidden;","A21":"text-align: left; white-space: pre-wrap; overflow: hidden;","B21":"text-align: center; white-space: pre-wrap; overflow: hidden;","C21":"text-align: right; white-space: pre-wrap; overflow: hidden;","D21":"text-align: right; white-space: pre-wrap; overflow: hidden;","A22":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B22":"text-align: center; white-space: pre-wrap; overflow: hidden;","C22":"text-align: right; white-space: pre-wrap; overflow: hidden;","D22":"text-align: right; white-space: pre-wrap; overflow: hidden;","A23":"text-align: left; white-space: pre-wrap; overflow: hidden;","B23":"text-align: center; white-space: pre-wrap; overflow: hidden;","C23":"text-align: right; white-space: pre-wrap; overflow: hidden;","D23":"text-align: right; white-space: pre-wrap; overflow: hidden;","A24":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B24":"text-align: center; white-space: pre-wrap; overflow: hidden;","C24":"text-align: right; white-space: pre-wrap; overflow: hidden;","D24":"text-align: right; white-space: pre-wrap; overflow: hidden;","A25":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B25":"text-align: center; white-space: pre-wrap; overflow: hidden;","C25":"text-align: right; white-space: pre-wrap; overflow: hidden;","D25":"text-align: right; white-space: pre-wrap; overflow: hidden;","A26":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B26":"text-align: center; white-space: pre-wrap; overflow: hidden;","C26":"text-align: right; white-space: pre-wrap; overflow: hidden;","D26":"text-align: right; white-space: pre-wrap; overflow: hidden;","A27":"text-align: left; white-space: pre-wrap; overflow: hidden;","B27":"text-align: center; white-space: pre-wrap; overflow: hidden;","C27":"text-align: right; white-space: pre-wrap; overflow: hidden;","D27":"text-align: right; white-space: pre-wrap; overflow: hidden;","A28":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B28":"text-align: center; white-space: pre-wrap; overflow: hidden;","C28":"text-align: right; white-space: pre-wrap; overflow: hidden;","D28":"text-align: right; white-space: pre-wrap; overflow: hidden;","A29":"text-align: left; white-space: pre-wrap; overflow: hidden;","B29":"text-align: center; white-space: pre-wrap; overflow: hidden;","C29":"text-align: right; white-space: pre-wrap; overflow: hidden;","D29":"text-align: right; white-space: pre-wrap; overflow: hidden;","A30":"text-align: left; white-space: pre-wrap; overflow: hidden;","B30":"text-align: center; white-space: pre-wrap; overflow: hidden;","C30":"text-align: right; white-space: pre-wrap; overflow: hidden;","D30":"text-align: right; white-space: pre-wrap; overflow: hidden;","A31":"text-align: left; white-space: pre-wrap; overflow: hidden;","B31":"text-align: center; white-space: pre-wrap; overflow: hidden;","C31":"text-align: right; white-space: pre-wrap; overflow: hidden;","D31":"text-align: right; white-space: pre-wrap; overflow: hidden;","A32":"text-align: left; white-space: pre-wrap; overflow: hidden;","B32":"text-align: center; white-space: pre-wrap; overflow: hidden;","C32":"text-align: right; white-space: pre-wrap; overflow: hidden;","D32":"text-align: right; white-space: pre-wrap; overflow: hidden;","A33":"text-align: left; white-space: pre-wrap; overflow: hidden;","B33":"text-align: center; white-space: pre-wrap; overflow: hidden;","C33":"text-align: right; white-space: pre-wrap; overflow: hidden;","D33":"text-align: right; white-space: pre-wrap; overflow: hidden;","A34":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B34":"text-align: center; white-space: pre-wrap; overflow: hidden;","C34":"text-align: right; white-space: pre-wrap; overflow: hidden;","D34":"text-align: right; white-space: pre-wrap; overflow: hidden;","A35":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B35":"text-align: center; white-space: pre-wrap; overflow: hidden;","C35":"text-align: right; white-space: pre-wrap; overflow: hidden;","D35":"text-align: right; white-space: pre-wrap; overflow: hidden;","A36":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B36":"text-align: center; white-space: pre-wrap; overflow: hidden;","C36":"text-align: right; white-space: pre-wrap; overflow: hidden;","D36":"text-align: right; white-space: pre-wrap; overflow: hidden;"}</field>
            <field name='width_info' >{"0":"294","1":"37","2":"169","3":"175"}</field>
            <field name='height_info' >{"0":"25","1":"25","2":"25","3":"25","4":"25","5":"25","6":"25","7":"25","8":"25","9":"25","10":"25","11":"25","12":"25","13":"25","14":"25","15":"25","16":"25","17":"25","18":"25","19":"25","20":"25","21":"25","22":"25","23":"25","24":"25","25":"25","26":"25","27":"25","28":"25","29":"25","30":"25","31":"25","32":"25","33":"25","34":"25","35":"25"}</field>
            <field name='comments_info' >{}</field>
            <field name='merge_info' >{"A1":[4,1],"A2":[4,1],"A3":[4,1]}</field>
            <field name='summary' >系统预设,根据需要修改</field>
        </record>
        <record id='report_model_3' model='accountcore.report_model'>
            <field name='report_type' eval='report_type_3' />
            <field name='name' >现金流量表(预设)</field>
            <field name='version' >2019</field>
            <field name='orgs' eval='[(6, 0, [ref("org_1")])]' />
            <field name='data' >[["现金流量表","",""],["=betweenDate()","",""],["=show_orgs()","",""],["项                     目","行次","金     额"],["一、经营活动产生的现金流量：","",""],[" 销售商品、提供劳务收到的现金","1","=cashflow('+销售商品、提供劳务收到的现金','True')"],[" 收到的税费返还","3","=cashflow('+收到的税费返还','True')"],[" 收到的其他与经营活动有关的现金","8","=cashflow('+收到其他与经营活动有关的现金','True')"],["现金流入小计","9","=SUM(C6:C8)"],[" 购买商品、接受劳务支付的现金","10","=cashflow('-购买商品、接受劳务支付的现金','True')"],[" 支付给职工以及为职工支付的现金","12","=cashflow('-支付给职工以及为职工支付的现金','True')"],[" 支付的各项税费","13","=cashflow('-支付的各项税费','True')"],[" 支付的其他与经营活动有关的现金","18","=cashflow('-支付其他与经营活动有关的现金','True')"],["现金流出小计","20","=SUM(C10:C13)"],[" 经营活动产生的现金流量净额","21","=C9-C14"],["二、投资活动产生的现金流量：","",""],[" 收回投资所收到的现金","22","=cashflow('+收回投资收到的现金','True')"],[" 取得投资收益所收到的现金","23","=cashflow('+取得投资收益收到的现金','True')"],[" 处置固定资产、无形资产和其他长期资产所收回的现金净额","25","=cashflow('+处置固定资产、无形资产和其他长期资产收回的现金净额','True')"],[" 收到的其他与投资活动有关的现金","28","=cashflow('+收到其他与投资活动有关的现金','True')"],["现金流入小计","29","=SUM(C17:C20)"],[" 购建固定资产、无形资产和其他长期资产所支付的现金","30","=cashflow('-购建固定资产、无形资产和其他长期资产支付的现金','True')"],[" 投资所支付的现金","31","=cashflow('-投资支付的现金','True')"],[" 支付的其他与投资活动有关的现金","35","=cashflow('-支付其他与投资活动有关的现金','True')"],["现金流出小计","36","=SUM(C22:C24)"],[" 投资活动产生的现金流量净额","37","=C21-C25"],["三、筹资活动产生的现金流量：","",""],[" 吸收投资所收到的现金","38","=cashflow('+吸收投资收到的现金','True')"],[" 借款所收到的现金","40","=cashflow('+取得借款收到的现金','True')"],[" 收到的其他与筹资活动有关的现金","43","=cashflow('+收到其他与筹资活动有关的现金','True')"],["现金流入小计","44","=SUM(C28:C30)"],[" 偿还债务所支付的现金","45","=cashflow('-偿还债务支付的现金','True')"],[" 分配股利、利润或偿付利息所支付的现金","46","=cashflow('-分配股利、利润或偿付利息支付的现金','True')"],[" 支付的其他与筹资活动有关的现金","52","=cashflow('-支付其他与筹资活动有关的现金','True')"],["现金流出小计","53","=SUM(C32:C34)"],[" 筹资活动产生的现金流量净额","54","=C31-C35"],["四、汇率变动对现金的影响","55","=cashflow('+汇率变动流入','True')-cashflow('-汇率变动流出','True')"],["五、现金及现金等价物净增加额","56","=SUM(C15,C26,C36,C37)"]]</field>
            <field name='data_style' >{"A1":"text-align: center; white-space: pre-wrap; font-size: 28px;","B1":"text-align: center; white-space: pre-wrap; font-size: 28px;","C1":"text-align: center; white-space: pre-wrap; font-size: 28px;","A2":"text-align: center; white-space: pre-wrap;","B2":"text-align: center; white-space: pre-wrap;","C2":"text-align: center; white-space: pre-wrap;","A3":"text-align: left; white-space: pre-wrap;","B3":"text-align: left; white-space: pre-wrap;","C3":"text-align: left; white-space: pre-wrap;","A4":"text-align: center; white-space: pre-wrap; overflow: hidden;","B4":"text-align: center; white-space: pre-wrap; overflow: hidden;","C4":"text-align: center; white-space: pre-wrap; overflow: hidden;","A5":"text-align: left; white-space: pre-wrap; font-weight: bold;","B5":"text-align: center; white-space: pre-wrap;","C5":"text-align: center; white-space: pre-wrap; overflow: hidden;","A6":"text-align: left; white-space: pre-wrap; overflow: hidden;","B6":"text-align: center; white-space: pre-wrap; overflow: hidden;","C6":"text-align: right; white-space: pre-wrap; overflow: hidden;","A7":"text-align: left; white-space: pre-wrap; overflow: hidden;","B7":"text-align: center; white-space: pre-wrap; overflow: hidden;","C7":"text-align: right; white-space: pre-wrap; overflow: hidden;","A8":"text-align: left; white-space: pre-wrap; overflow: hidden;","B8":"text-align: center; white-space: pre-wrap; overflow: hidden;","C8":"text-align: right; white-space: pre-wrap; overflow: hidden;","A9":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B9":"text-align: center; white-space: pre-wrap; overflow: hidden;","C9":"text-align: right; white-space: pre-wrap; overflow: hidden;","A10":"text-align: left; white-space: pre-wrap; overflow: hidden;","B10":"text-align: center; white-space: pre-wrap; overflow: hidden;","C10":"text-align: right; white-space: pre-wrap; overflow: hidden;","A11":"text-align: left; white-space: pre-wrap; overflow: hidden;","B11":"text-align: center; white-space: pre-wrap; overflow: hidden;","C11":"text-align: right; white-space: pre-wrap; overflow: hidden;","A12":"text-align: left; white-space: pre-wrap; overflow: hidden;","B12":"text-align: center; white-space: pre-wrap; overflow: hidden;","C12":"text-align: right; white-space: pre-wrap; overflow: hidden;","A13":"text-align: left; white-space: pre-wrap; overflow: hidden;","B13":"text-align: center; white-space: pre-wrap; overflow: hidden;","C13":"text-align: right; white-space: pre-wrap; overflow: hidden;","A14":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B14":"text-align: center; white-space: pre-wrap; overflow: hidden;","C14":"text-align: right; white-space: pre-wrap; overflow: hidden;","A15":"text-align: left; white-space: pre-wrap; overflow: hidden;","B15":"text-align: center; white-space: pre-wrap; overflow: hidden;","C15":"text-align: right; white-space: pre-wrap; overflow: hidden;","A16":"text-align: left; white-space: pre-wrap; font-weight: bold;","B16":"text-align: center; white-space: pre-wrap;","C16":"text-align: right; white-space: pre-wrap; overflow: hidden;","A17":"text-align: left; white-space: pre-wrap; overflow: hidden;","B17":"text-align: center; white-space: pre-wrap; overflow: hidden;","C17":"text-align: right; white-space: pre-wrap; overflow: hidden;","A18":"text-align: left; white-space: pre-wrap; overflow: hidden;","B18":"text-align: center; white-space: pre-wrap; overflow: hidden;","C18":"text-align: right; white-space: pre-wrap; overflow: hidden;","A19":"text-align: left; white-space: pre-wrap; overflow: hidden;","B19":"text-align: center; white-space: pre-wrap; overflow: hidden;","C19":"text-align: right; white-space: pre-wrap; overflow: hidden;","A20":"text-align: left; white-space: pre-wrap; overflow: hidden;","B20":"text-align: center; white-space: pre-wrap; overflow: hidden;","C20":"text-align: right; white-space: pre-wrap; overflow: hidden;","A21":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B21":"text-align: center; white-space: pre-wrap; overflow: hidden;","C21":"text-align: right; white-space: pre-wrap; overflow: hidden;","A22":"text-align: left; white-space: pre-wrap; overflow: hidden;","B22":"text-align: center; white-space: pre-wrap; overflow: hidden;","C22":"text-align: right; white-space: pre-wrap; overflow: hidden;","A23":"text-align: left; white-space: pre-wrap; overflow: hidden;","B23":"text-align: center; white-space: pre-wrap; overflow: hidden;","C23":"text-align: right; white-space: pre-wrap; overflow: hidden;","A24":"text-align: left; white-space: pre-wrap; overflow: hidden;","B24":"text-align: center; white-space: pre-wrap; overflow: hidden;","C24":"text-align: right; white-space: pre-wrap; overflow: hidden;","A25":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B25":"text-align: center; white-space: pre-wrap; overflow: hidden;","C25":"text-align: right; white-space: pre-wrap; overflow: hidden;","A26":"text-align: left; white-space: pre-wrap; overflow: hidden;","B26":"text-align: center; white-space: pre-wrap; overflow: hidden;","C26":"text-align: right; white-space: pre-wrap; overflow: hidden;","A27":"text-align: left; white-space: pre-wrap; font-weight: bold;","B27":"text-align: center; white-space: pre-wrap;","C27":"text-align: right; white-space: pre-wrap; overflow: hidden;","A28":"text-align: left; white-space: pre-wrap; overflow: hidden;","B28":"text-align: center; white-space: pre-wrap; overflow: hidden;","C28":"text-align: right; white-space: pre-wrap; overflow: hidden;","A29":"text-align: left; white-space: pre-wrap; overflow: hidden;","B29":"text-align: center; white-space: pre-wrap; overflow: hidden;","C29":"text-align: right; white-space: pre-wrap; overflow: hidden;","A30":"text-align: left; white-space: pre-wrap; overflow: hidden;","B30":"text-align: center; white-space: pre-wrap; overflow: hidden;","C30":"text-align: right; white-space: pre-wrap; overflow: hidden;","A31":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B31":"text-align: center; white-space: pre-wrap; overflow: hidden;","C31":"text-align: right; white-space: pre-wrap; overflow: hidden;","A32":"text-align: left; white-space: pre-wrap; overflow: hidden;","B32":"text-align: center; white-space: pre-wrap; overflow: hidden;","C32":"text-align: right; white-space: pre-wrap; overflow: hidden;","A33":"text-align: left; white-space: pre-wrap; overflow: hidden;","B33":"text-align: center; white-space: pre-wrap; overflow: hidden;","C33":"text-align: right; white-space: pre-wrap; overflow: hidden;","A34":"text-align: left; white-space: pre-wrap; overflow: hidden;","B34":"text-align: center; white-space: pre-wrap; overflow: hidden;","C34":"text-align: right; white-space: pre-wrap; overflow: hidden;","A35":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B35":"text-align: center; white-space: pre-wrap; overflow: hidden;","C35":"text-align: right; white-space: pre-wrap; overflow: hidden;","A36":"text-align: left; white-space: pre-wrap; overflow: hidden;","B36":"text-align: center; white-space: pre-wrap; overflow: hidden;","C36":"text-align: right; white-space: pre-wrap; overflow: hidden;","A37":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B37":"text-align: center; white-space: pre-wrap; overflow: hidden;","C37":"text-align: right; white-space: pre-wrap; overflow: hidden;","A38":"text-align: left; white-space: pre-wrap; overflow: hidden; font-weight: bold;","B38":"text-align: center; white-space: pre-wrap; overflow: hidden;","C38":"text-align: right; white-space: pre-wrap; overflow: hidden;"}</field>
            <field name='width_info' >{"0":"502","1":"46","2":"203"}</field>
            <field name='height_info' >{"0":25,"1":25,"2":25,"3":25,"4":25,"5":25,"6":25,"7":25,"8":25,"9":25,"10":25,"11":25,"12":25,"13":25,"14":25,"15":25,"16":25,"17":25,"18":25,"19":25,"20":25,"21":25,"22":25,"23":25,"24":25,"25":25,"26":25,"27":25,"28":25,"29":25,"30":25,"31":25,"32":25,"33":25,"34":25,"35":25,"36":25,"37":25}</field>
            <field name='comments_info' >{}</field>
            <field name='merge_info' >{"A1":[3,1],"A3":[3,1],"A2":[3,1]}</field>
            <field name='summary' >系统预设,根据需要修改</field>
        </record>
        <!-- 报表模板-结束 -->
    </data>
</odoo>