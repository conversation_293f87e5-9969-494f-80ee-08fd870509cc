<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--删除原来核算项目余额帐-->
    <function model="accountcore.entry" name="delete_record" eval="[[]]"/>
    <data noupdate="0">
        <record id="roke_accountcore_detailed_account_line" model="roke.sql.model.component">
            <field name="name">项目核算明细账</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">项目核算明细账</field>
            <field name="top_menu_id" ref="accountcore.account_inventory"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '开始时间',
                    'sql_field_mark': 'start_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '结束时间',
                    'sql_field_mark': 'end_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '科目范围',
                    'sql_field_mark': 'account_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_account_dict'),
                }),
                (0, 0, {
                    'name': '机构范围',
                    'sql_field_mark': 'org_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_org_dict'),
                }),
                (0, 0, {
                    'name': '项目范围',
                    'sql_field_mark': 'item_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_item_dict'),
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "记账日期",
                    "sql_data": "(temp_table1.v_voucherdate) AS 记账日期",
                    "sql_order_by_data": "(temp_table1.v_voucherdate)"
                }),
                (0, 0, {
                    "name": "机构名称",
                    "sql_data": "(temp_table1.org_name) AS 机构名称",
                    "sql_order_by_data": "(temp_table1.org_name)"
                }),
                (0, 0, {
                    "name": "凭证号",
                    "sql_data": "(temp_table1.entry_number) AS 凭证号",
                    "sql_order_by_data": "(temp_table1.entry_number)"
                }),
                (0, 0, {
                    "name": "会计科目",
                    "sql_data": "(temp_table1.account_name) AS 会计科目",
                    "sql_order_by_data": "(temp_table1.account_name)"
                }),
                (0, 0, {
                    "name": "核算项目",
                    "sql_data": "(temp_table2.item_names) AS 核算项目",
                    "sql_order_by_data": "(temp_table2.item_names)"
                }),
                (0, 0, {
                    "name": "摘要",
                    "sql_data": "(temp_table1.entry_explain) AS 摘要",
                    "sql_order_by_data": "(temp_table1.entry_explain)"
                }),
                (0, 0, {
                    "name": "借方",
                    "sql_data": "COALESCE(temp_table1.entry_damount, 0) AS 借方",
                    "sql_order_by_data": "COALESCE(temp_table1.entry_damount, 0)"
                }),
                (0, 0, {
                    "name": "贷方",
                    "sql_data": "COALESCE(temp_table1.entry_camount, 0) AS 贷方",
                    "sql_order_by_data": "COALESCE(temp_table1.entry_camount, 0)"
                }),
                (0, 0, {
                    "name": "方向",
                    "sql_data": "(temp_table1.direction) AS 方向",
                    "sql_order_by_data": "temp_table1.direction"
                }),
                (0, 0, {
                    "name": "余额",
                    "sql_data": "COALESCE(temp_table1.balance, 0) AS 余额",
                    "sql_order_by_data": "COALESCE(temp_table1.balance, 0)"
                }),
                (0, 0, {
                    "name": "凭证ID",
                    "sql_data": "(temp_table1.voucher_id) AS 凭证ID",
                    "sql_order_by_data": "(temp_table1.voucher_id)",
                    "is_not_show": True
                })
            ]'/>
        </record>
    </data>

    <data noupdate="0">
        <record id="roke_accountcore_balance_account_line" model="roke.sql.model.component">
            <field name="name">核算项目余额帐</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">核算项目余额帐</field>
            <field name="top_menu_id" ref="accountcore.account_inventory"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '开始时间',
                    'sql_field_mark': 'start_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '结束时间',
                    'sql_field_mark': 'end_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '科目范围',
                    'sql_field_mark': 'account_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_account_dict'),
                }),
                (0, 0, {
                    'name': '机构范围',
                    'sql_field_mark': 'org_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_org_dict'),
                }),
                (0, 0, {
                    'name': '项目范围',
                    'sql_field_mark': 'item_ids',
                    'sql_field_mark_type': 'many2many',
                    'inherit_client_dict': ref('accountcore.roke_accountcore_item_dict'),
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "机构名称",
                    "sql_data": "(temp_table6.org_name) AS 机构名称",
                    "sql_order_by_data": "(temp_table6.org_name)"
                }),
                (0, 0, {
                    "name": "会计日期",
                    "sql_data": "(temp_table6.account_date) AS 会计日期",
                    "sql_order_by_data": "(temp_table6.account_date)"
                }),
                (0, 0, {
                    "name": "科目编码",
                    "sql_data": "(temp_table6.account_number) AS 科目编码",
                    "sql_order_by_data": "(temp_table6.account_number)"
                }),
                (0, 0, {
                    "name": "科目名称",
                    "sql_data": "(temp_table6.account_name) AS 科目名称",
                    "sql_order_by_data": "(temp_table6.account_name)"
                }),
                (0, 0, {
                    "name": "核算项目",
                    "sql_data": "(temp_table6.item_names) AS 核算项目",
                    "sql_order_by_data": "(temp_table6.item_names)"
                }),
                (0, 0, {
                    "name": "期初余额",
                    "sql_data": "ABS(COALESCE(temp_table6.begin_balance, 0)) AS 期初余额",
                    "sql_order_by_data": "ABS(COALESCE(temp_table6.begin_balance, 0))"
                }),
                (0, 0, {
                    "name": "期初方向",
                    "sql_data": "(temp_table6.begin_direction) AS 期初方向",
                    "sql_order_by_data": "(temp_table6.begin_direction)"
                }),
                (0, 0, {
                    "name": "本期借方发生",
                    "sql_data": "ABS(COALESCE(temp_table6.in_between_balance, 0)) AS 本期借方发生",
                    "sql_order_by_data": "ABS(COALESCE(temp_table6.in_between_balance, 0))"
                }),
                (0, 0, {
                    "name": "本期贷方发生",
                    "sql_data": "ABS(COALESCE(temp_table6.out_between_balance, 0)) AS 本期贷方发生",
                    "sql_order_by_data": "ABS(COALESCE(temp_table6.out_between_balance, 0))"
                }),
                (0, 0, {
                    "name": "期末方向",
                    "sql_data": "(temp_table6.end_direction) AS 期末方向",
                    "sql_order_by_data": "(temp_table6.end_direction)"
                }),
                (0, 0, {
                    "name": "期末余额",
                    "sql_data": "ABS(COALESCE(temp_table6.end_balance, 0)) AS 期末余额",
                    "sql_order_by_data": "ABS(COALESCE(temp_table6.end_balance, 0))"
                }),
                (0, 0, {
                    "name": "科目ID",
                    "sql_data": "(temp_table6.account_id)  AS 科目ID",
                    "sql_order_by_data": "(temp_table6.account_id)",
                    "is_not_show": True
                }),
                (0, 0, {
                    "name": "机构ID",
                    "sql_data": "(temp_table6.org_id)  AS 机构ID",
                    "sql_order_by_data": "(temp_table6.org_id)",
                    "is_not_show": True
                })
            ]'/>
        </record>
    </data>

    <data noupdate="0">
        <record id="roke_query_investigation_model_detailed_account_line_01" model="roke.query.investigation.model">
            <field name="investigation_type">系统表单</field>
            <field name="model_id" ref="accountcore.model_accountcore_voucher"/>
            <field name="model_view_id" ref="accountcore.accountcore_voucher_form"/>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">凭证号</field>
        </record>
    </data>

    <data noupdate="0">
        <record id="roke_query_investigation_model_balance_account_line_01" model="roke.query.investigation.model">
            <field name="investigation_type">自定义报表</field>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">科目编码</field>
        </record>
        <record id="roke_query_investigation_model_balance_account_line_02" model="roke.query.investigation.model">
            <field name="investigation_type">自定义报表</field>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">科目名称</field>
        </record>
    </data>

    <!--处理报表联查-->
    <function model="accountcore.entry" name="write_sql_query_investigation" eval="[[]]"/>
</odoo>
