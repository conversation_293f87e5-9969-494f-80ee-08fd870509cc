from odoo import api, SUPERUSER_ID
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
	env = api.Environment(cr, SUPERUSER_ID, {})
	sql_str = """
		            SELECT proname
		            FROM pg_proc
		            WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
		        """
	try:
		env.cr.execute(sql_str)
		data_list = env.cr.dictfetchall()
	except Exception as e:
		raise ValidationError(f"存储过程获取失败！{e}")
	proname_list = [v.get("proname", False) for v in data_list]
	for rec in proname_list:
		if rec == '明细账':
			env.cr.execute('''
					DROP FUNCTION "明细账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[]);
					''')
		elif rec == '总账':
			env.cr.execute('''
								DROP FUNCTION "总账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[]);
								''')
		elif rec == '科目余额表':
			env.cr.execute('''
											DROP FUNCTION "科目余额表"("start_dt" date, "end_dt" date, "account_ids" integer[],"org_ids" integer[],"is_hide_zero_balance" boolean, "is_hide_zero_amount" boolean,"is_hide_double_zero_balance" boolean);
											''')
	env.cr.execute('''
			CREATE OR REPLACE FUNCTION "public"."明细账"("start_dt" date, "end_dt" date, "account_ids" integer[],
                                             "org_ids" integer[])
    RETURNS TABLE
            (
                "日期"     date,
                "会计科目" varchar,
                "凭证号"   varchar,
                "摘要"     varchar,
                "借方"     numeric,
                "贷方"     numeric,
                "方向"     varchar,
                "余额"     numeric,
                "凭证ID"   integer
            )
AS
$BODY$
BEGIN
    CREATE TEMPORARY TABLE temp_table1
    (
        v_voucherdate DATE,
        account_name  VARCHAR,
        entry_number  VARCHAR,
        entry_explain VARCHAR,
        entry_damount NUMERIC,
        entry_camount NUMERIC,
        direction     VARCHAR,
        balance       NUMERIC,
        voucher_id    INTEGER
    );
    INSERT INTO temp_table1 (v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select accountcore_entry.v_voucherdate                                                                                        as 日期,
                                             accountcore_account.name                                                                                               as 会计科目,
                                             accountcore_entry.v_number                                                                                             as 凭证号,
                                             accountcore_entry.explain                                                                                              as 摘要,
                                             accountcore_entry.damount                                                                                              as 借方,
                                             accountcore_entry.camount                                                                                              as 贷方,
                                             case when accountcore_account.direction = '1' then '借' else '贷' end                                                  as 方向,
                                             case
                                                 when accountcore_account.direction = '1' then
                                                         (COALESCE(aab.begin_year_amount, 0) + ae.d_amount) +
                                                         ((sum(accountcore_entry.damount)
                                                           OVER (partition by accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.id))
                                                             - (sum(accountcore_entry.camount)
                                                                OVER (partition by accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.id)))
                                                 else COALESCE(aab.begin_year_amount, 0) + ae.c_amount +
                                                      ((sum(accountcore_entry.camount)
                                                        OVER (partition by accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.id)) -
                                                       (
                                                                   sum(accountcore_entry.damount)
                                                                   OVER (partition by accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.id))) end as 余额,
                                             accountcore_voucher.id as 凭证ID
                                      from accountcore_entry
											   LEFT JOIN accountcore_voucher
                                                         on accountcore_entry.voucher = accountcore_voucher.id
                                               LEFT JOIN accountcore_account
                                                         on accountcore_entry.account = accountcore_account.id
                                               LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = any (account_ids)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = any (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_entry.account
                                               LEFT JOIN (select ae1.account,
                                                                 sum(ae1.damount) -
                                                                 sum(ae1.camount) as d_amount,
                                                                 sum(ae1.camount) -
                                                                 sum(ae1.damount) as c_amount
                                                          from accountcore_entry ae1
                                                                   LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
                                                          where v_voucherdate < start_dt
                                                            and case
                                                                    when account_ids is not null
                                                                        then ae1.account = any (account_ids)
                                                                    else ae1.account is not null end
                                                            and case
                                                                    when org_ids is not null
                                                                        then ae1.org = any (org_ids)
                                                                    else ae1.org is not null end
                                                          group by ae1.account) as ae
                                                         on ae.account = accountcore_entry.account
                                      where v_voucherdate between start_dt and end_dt
                                        and case
                                                when account_ids is not null
                                                    then accountcore_entry.account = any (account_ids)
                                                else accountcore_entry.account is not null end
                                        and case
                                                when org_ids is not null then accountcore_entry.org = any (org_ids)
                                                else accountcore_entry.org is not null end);
    INSERT INTO temp_table1 (v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select NULL,
                                             NULL,
                                             '本期累计',
                                             NULL,
                                             sum(damount),
                                             sum(camount),
                                             NULL,
                                             NULL,
                                             NULL
                                      from accountcore_entry
                                      where v_voucherdate between start_dt and end_dt);
    INSERT INTO temp_table1 (v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select NULL,
                                             NULL,
                                             '本年累计',
                                             NULL,
                                             sum(damount),
                                             sum(camount),
                                             NULL,
                                             NULL,
                                             NULL
                                      from accountcore_entry
                                      where v_voucherdate between date_trunc('year', now()) and date_trunc('year', now()) + interval '1 year' - interval '1 day');

-- Routine body goes here...
    RETURN QUERY SELECT (temp_table1.v_voucherdate)            AS 日期,
                        (temp_table1.account_name)             AS 会计科目,
                        (temp_table1.entry_number)             AS 凭证号,
                        (temp_table1.entry_explain)            AS 摘要,
                        COALESCE(temp_table1.entry_damount, 0) AS 借方,
                        COALESCE(temp_table1.entry_camount, 0) AS 贷方,
                        (temp_table1.direction)                AS 方向,
                        COALESCE(temp_table1.balance, 0)       AS 余额,
                        (temp_table1.voucher_id)               AS 凭证ID
                 FROM temp_table1;
    DROP TABLE
        IF
            EXISTS temp_table1;

END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000
	''')
	env.cr.execute('''
	CREATE OR REPLACE FUNCTION "public"."总账"("start_dt" date, "end_dt" date, "account_ids" integer[],
                                           "org_ids" integer[])
    RETURNS TABLE
            (
                "会计科目" varchar,
                "借方"     numeric,
                "贷方"     numeric,
                "方向"     varchar,
                "余额"     numeric,
                "科目ID"   integer
            )
AS
$BODY$
BEGIN
    CREATE TEMPORARY TABLE temp_table1
    (
        account_name  VARCHAR,
        entry_damount NUMERIC,
        entry_camount NUMERIC,
        direction     VARCHAR,
        balance       NUMERIC,
        account_id    INTEGER
    );
    INSERT INTO temp_table1 (account_name,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             account_id)(select accountcore_account.name                                              as 会计科目,
                                             sum(accountcore_entry.damount)                                        as 借方,
                                             sum(accountcore_entry.camount)                                        as 贷方,
                                             case when accountcore_account.direction = '1' then '借' else '贷' end as 方向,
                                             case
                                                 when accountcore_account.direction = '1' then
                                                     (COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount,0) +
                                                      sum(accountcore_entry.damount) - sum(accountcore_entry.camount))
                                                 else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount,0) +
                                                      sum(accountcore_entry.camount) - sum(accountcore_entry.damount)
                                                 end                                                               as 余额,
                                                 accountcore_account.id                                              as 科目ID
                                      from accountcore_entry
                                               LEFT JOIN accountcore_account
                                                         on accountcore_entry.account = accountcore_account.id
                                               LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = ANY (account_ids)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = ANY (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_entry.account
                                               LEFT JOIN (select ae1.account,
                                                                 sum(ae1.damount) -
                                                                 sum(ae1.camount) as d_amount,
                                                                 sum(ae1.camount) -
                                                                 sum(ae1.damount) as c_amount
                                                          from accountcore_entry ae1
                                                                   LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
                                                          where v_voucherdate < start_dt
                                                            and case
                                                                    when account_ids is not null
                                                                        then ae1.account = any (account_ids)
                                                                    else ae1.account is not null end
                                                            and case
                                                                    when org_ids is not null
                                                                        then ae1.org = any (org_ids)
                                                                    else ae1.org is not null end
                                                          group by ae1.account) as ae
                                                         on ae.account = accountcore_entry.account
                                      where v_voucherdate between start_dt and end_dt
                                        and case
                                                when account_ids is not null
                                                    then accountcore_entry.account = ANY (account_ids)
                                                else accountcore_entry.account is not null end
                                        and case
                                                when org_ids is not null then accountcore_entry.org = ANY (org_ids)
                                                else accountcore_entry.org is not null end
                                      group by accountcore_account.name, accountcore_account.direction, ae.d_amount,
                                               ae.c_amount,accountcore_account.id,
                                               aab.begin_year_amount);
-- Routine body goes here...
    RETURN QUERY SELECT (temp_table1.account_name)             AS 会计科目,
                        COALESCE(temp_table1.entry_damount, 0) AS 借方,
                        COALESCE(temp_table1.entry_camount, 0) AS 贷方,
                        (temp_table1.direction)                AS 方向,
                        COALESCE(temp_table1.balance, 0)       AS 余额,
                        (temp_table1.account_id)               AS 科目ID
                 FROM temp_table1;
    DROP TABLE
        IF
            EXISTS temp_table1;

END
$BODY$
    LANGUAGE plpgsql VOLATILE
                     COST 100
                     ROWS 1000
	''')
	env.cr.execute('''
			CREATE OR REPLACE FUNCTION "public"."科目余额表"("start_dt" date, "end_dt" date, "account_ids" integer[],
	                                                 "org_ids" integer[],
	                                                 "is_hide_zero_balance" boolean, "is_hide_zero_amount" boolean,
	                                                 "is_hide_double_zero_balance" boolean)
	    RETURNS TABLE
	            (
	                "科目编号"     varchar,
	                "科目名称"     varchar,
	                "方向"         varchar,
	                "期初余额"     numeric,
	                "本期借方发生" numeric,
	                "本期贷方发生" numeric,
	                "余额"         numeric
	            )
	AS
	$BODY$
	BEGIN
	    CREATE TEMPORARY TABLE temp_table1
	    (
	        account_number VARCHAR,
	        account_name   VARCHAR,
	        direction      VARCHAR,
	        qc_balance     NUMERIC,
	        entry_damount  NUMERIC,
	        entry_camount  NUMERIC,
	        balance        NUMERIC
	    );
	    CREATE TEMPORARY TABLE temp_table2
	    (
	        account_id INTEGER,
	        account_number VARCHAR,
	        account_name   VARCHAR,
	        direction      VARCHAR,
	        qc_balance     NUMERIC,
	        entry_damount  NUMERIC,
	        entry_camount  NUMERIC,
	        balance        NUMERIC
	    );
	    INSERT INTO temp_table2 (
	                             account_id,
	                             account_number,
	                             account_name,
	                             direction,
	                             qc_balance,
	                             entry_damount,
	                             entry_camount,
	                             balance)(select accountcore_account.id as 科目ID,
	                                          accountcore_account.number                                                 as 科目编号,
	                                             accountcore_account.name                                                   as 科目名称,
	                                             case when accountcore_account.direction = '1' then '借' else '贷' end      as 方向,
	                                             case
	                                                 when accountcore_account.direction = '1' then
	                                                     COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0)
	                                                 else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) end as 期初余额,
	                                             sum(accountcore_entry.damount)                                             as 本期借方发生,
	                                             sum(accountcore_entry.camount)                                             as 本期贷方发生,
	                                             case
	                                                 when accountcore_account.direction = '1' then
	                                                     (COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0) +
	                                                      sum(accountcore_entry.damount) - sum(accountcore_entry.camount))
	                                                 else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) +
	                                                      sum(accountcore_entry.camount) - sum(accountcore_entry.damount)
	                                                 end                                                                    as 余额
	                                      from accountcore_account
	                                               LEFT JOIN (select *
	                                                          from accountcore_entry
	                                                          where v_voucherdate between start_dt and end_dt
	                                                            and case
	                                                                    when account_ids is not null
	                                                                        then accountcore_entry.account = ANY (account_ids)
	                                                                    else accountcore_entry.account is not null end
	                                                            and case
	                                                                    when org_ids is not null
	                                                                        then accountcore_entry.org = ANY (org_ids)
	                                                                    else accountcore_entry.org is not null end) accountcore_entry
	                                                         on accountcore_entry.account = accountcore_account.id
	                                               LEFT JOIN (select account, begin_year_amount, "kj_create_date"
	                                                          from accountcore_accounts_balance
	                                                          where isbegining = True
	                                                            and case
	                                                                    when account_ids is not null
	                                                                        then account = ANY (account_ids)
	                                                                    else account is not null end
	                                                            and case
	                                                                    when org_ids is not null then org = ANY (org_ids)
	                                                                    else org is not null end
	                                                          order by "kj_create_date" desc) aab
	                                                         on aab.account = accountcore_account.id
	                                               LEFT JOIN (select ae1.account,
	                                                                 sum(ae1.damount) -
	                                                                 sum(ae1.camount) as d_amount,
	                                                                 sum(ae1.camount) -
	                                                                 sum(ae1.damount) as c_amount
	                                                          from accountcore_entry ae1
	                                                                   LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
	                                                          where v_voucherdate < start_dt
	                                                            and case
	                                                                    when account_ids is not null
	                                                                        then ae1.account = any (account_ids)
	                                                                    else ae1.account is not null end
	                                                            and case
	                                                                    when org_ids is not null
	                                                                        then ae1.org = any (org_ids)
	                                                                    else ae1.org is not null end
	                                                          group by ae1.account) as ae
	                                                         on ae.account = accountcore_account.id
	                                      group by accountcore_account.name, accountcore_account.direction, ae.d_amount,
	                                               ae.c_amount,accountcore_account.id,
	                                               aab.begin_year_amount,
	                                               accountcore_account.number);
	    INSERT INTO temp_table1 (
	                             account_number,
	                             account_name,
	                             direction,
	                             qc_balance,
	                             entry_damount,
	                             entry_camount,
	                             balance)(select
	                                          account_number,
	                                             account_name,
	                                             direction,
	                                             qc_balance,
	                                             entry_damount,
	                                             entry_camount,
	                                             balance
	                                      from temp_table2
	                                      where case
	                                                when is_hide_zero_balance = 'True'
	                                                    then COALESCE(temp_table2.balance, 0) != 0
	                                                else 1 = 1 end
	                                        and case
	                                                when is_hide_zero_amount = 'True' then not (
	                                                            COALESCE(temp_table2.entry_damount, 0) = 0 and
	                                                            COALESCE(temp_table2.entry_camount, 0) = 0)
	                                                else
	                                                    1 = 1 end
	                                        and case
	                                                when is_hide_double_zero_balance = 'True' then not (
	                                                            COALESCE(temp_table2.balance, 0) = 0 and
	                                                            COALESCE(temp_table2.qc_balance, 0) = 0)
	                                                else 1 = 1 end and
	                                        case
	                                                                    when account_ids is not null
	                                                                        then temp_table2.account_id = any (account_ids)
	                                                                    else 1=1 end

	                                      );
	-- Routine body goes here...
	    RETURN QUERY SELECT
	                     (temp_table1.account_number)           as 科目编号,
	                        (temp_table1.account_name)             AS 会计科目,
	                        (temp_table1.direction)                AS 方向,
	                        COALESCE(temp_table1.qc_balance, 0)    AS 期初余额,
	                        COALESCE(temp_table1.entry_damount, 0) AS 本期借方发生,
	                        COALESCE(temp_table1.entry_camount, 0) AS 本期贷方发生,
	                        COALESCE(temp_table1.balance, 0)       AS 余额
	                 FROM temp_table1;

	    DROP TABLE
	        IF
	            EXISTS temp_table1;
	    DROP TABLE IF EXISTS temp_table2;
	END
	$BODY$
	    LANGUAGE plpgsql VOLATILE
	                     COST 100
	                     ROWS 1000
		''')