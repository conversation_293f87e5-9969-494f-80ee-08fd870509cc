from odoo import api, SUPERUSER_ID
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
	env = api.Environment(cr, SUPERUSER_ID, {})
	sql_str = """
		            SELECT proname
		            FROM pg_proc
		            WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
		        """
	try:
		env.cr.execute(sql_str)
		data_list = env.cr.dictfetchall()
	except Exception as e:
		raise ValidationError(f"存储过程获取失败！{e}")
	proname_list = [v.get("proname", False) for v in data_list]
	for rec in proname_list:
		if rec == '项目核算明细账':
			env.cr.execute('''
											DROP FUNCTION "项目核算明细账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[], "item_ids" integer[]);
											''')
	env.cr.execute('''CREATE OR REPLACE FUNCTION "public"."项目核算明细账"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4, "item_ids" _int4)
  RETURNS TABLE("记账日期" date, "机构名称" varchar, "凭证号" varchar, "会计科目" varchar, "核算项目" varchar, "摘要" varchar, "借方" numeric, "贷方" numeric, "方向" varchar, "余额" numeric, "凭证ID" integer) AS $BODY$ BEGIN
		CREATE TEMPORARY TABLE temp_table1 (
			entry_id NUMERIC,
			v_voucherdate DATE,
			org_name VARCHAR,
			entry_number VARCHAR,
			account_name VARCHAR,
			entry_explain VARCHAR,
			entry_damount NUMERIC,
			entry_camount NUMERIC,
			direction VARCHAR,
			balance NUMERIC,
			voucher_id integer 
		);
		CREATE TEMPORARY TABLE temp_table2 (
			entry_id NUMERIC,
			item_names VARCHAR
		);

		INSERT INTO temp_table2 (entry_id, item_names) (
		SELECT
			adict.entry_id,
			string_agg ( adict.item_name :: TEXT, ',' ) AS item_names 
		FROM
		( SELECT 
			accountcore_entry_accountcore_item_rel.accountcore_entry_id AS entry_id,
			accountcore_item.NAME AS item_name 
		  FROM accountcore_entry_accountcore_item_rel 
		  LEFT JOIN accountcore_item 
			ON accountcore_entry_accountcore_item_rel.accountcore_item_id = accountcore_item.ID 
		  WHERE 
			CASE	
				WHEN item_ids IS NOT NULL THEN
				accountcore_entry_accountcore_item_rel.accountcore_item_id = ANY ( item_ids ) ELSE accountcore_entry_accountcore_item_rel.accountcore_item_id IS 				 NOT NULL 
			END 
	) AS adict 
GROUP BY
	adict.entry_id
		);
	IF (item_ids IS NULL) THEN
	INSERT INTO temp_table2 (entry_id, item_names) (
	SELECT id AS entry_id,NUll AS names 
	FROM accountcore_entry 
	WHERE id NOT IN (SELECT accountcore_entry_id FROM accountcore_entry_accountcore_item_rel ));
	ELSE
	INSERT INTO temp_table2 (entry_id, item_names) (
	SELECT id AS entry_id,NUll AS names 
	FROM accountcore_entry 
	WHERE id = 0);
	END IF;
	INSERT INTO temp_table1 ( entry_id, v_voucherdate, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance, voucher_id ) (
		SELECT
			accountcore_entry.id AS 编号,
			accountcore_entry.v_voucherdate AS 记账日期,
			accountcore_org.NAME AS 机构名称,
			accountcore_entry.v_number AS 凭证号,
			accountcore_account.NAME AS 会计科目,
			accountcore_entry.EXPLAIN AS 摘要,
			accountcore_entry.damount AS 借方,
			accountcore_entry.camount AS 贷方,
		CASE
				
				WHEN accountcore_account.direction = '1' THEN
				'借' ELSE'贷' 
			END AS 方向,
		CASE
				
				WHEN accountcore_account.direction = '1' THEN
				( COALESCE ( aab.begin_year_amount, 0 ) + ae.d_amount ) + (
					( SUM ( accountcore_entry.damount ) OVER ( PARTITION BY accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.ID ) ) - ( SUM ( accountcore_entry.camount ) OVER ( PARTITION BY accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.ID ) ) 
					) ELSE COALESCE ( aab.begin_year_amount, 0 ) + ae.c_amount + (
					( SUM ( accountcore_entry.camount ) OVER ( PARTITION BY accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.ID ) ) - ( SUM ( accountcore_entry.damount ) OVER ( PARTITION BY accountcore_entry.account ORDER BY v_voucherdate, accountcore_entry.ID ) ) 
				) 
			END AS 余额,
			accountcore_voucher.id AS 凭证ID
		FROM
			accountcore_entry
			LEFT JOIN accountcore_account ON accountcore_entry.account = accountcore_account.
			ID LEFT JOIN accountcore_voucher ON accountcore_entry.voucher = accountcore_voucher.
			ID LEFT JOIN accountcore_org ON accountcore_voucher.org = accountcore_org.
			ID LEFT JOIN (
			SELECT
				account,
				begin_year_amount,
				"kj_create_date" 
			FROM
				accountcore_accounts_balance 
			WHERE
				isbegining = TRUE 
			AND
			CASE
					
					WHEN account_ids IS NOT NULL THEN
					account = ANY ( account_ids ) ELSE account IS NOT NULL 
				END 
				AND
				CASE
						
						WHEN org_ids IS NOT NULL THEN
						org = ANY ( org_ids ) ELSE org IS NOT NULL 
					END 
					ORDER BY
						"kj_create_date" DESC 
					) aab ON aab.account = accountcore_entry.account
					LEFT JOIN (
					SELECT
						ae1.account,
						SUM ( ae1.damount ) - SUM ( ae1.camount ) AS d_amount,
						SUM ( ae1.camount ) - SUM ( ae1.damount ) AS c_amount 
					FROM
						accountcore_entry ae1
						LEFT JOIN accountcore_account aa1 ON ae1.account = aa1.ID 
					WHERE
						v_voucherdate < start_dt 
					AND
					CASE
							
							WHEN account_ids IS NOT NULL THEN
							ae1.account = ANY ( account_ids ) ELSE ae1.account IS NOT NULL 
						END 
						AND
						CASE
								
								WHEN org_ids IS NOT NULL THEN
								ae1.org = ANY ( org_ids ) ELSE ae1.org IS NOT NULL 
							END 
							GROUP BY
								ae1.account 
							) AS ae ON ae.account = accountcore_entry.account 
						WHERE
							v_voucherdate BETWEEN start_dt 
							AND end_dt 
						AND
						CASE
								
								WHEN account_ids IS NOT NULL THEN
								accountcore_entry.account = ANY ( account_ids ) ELSE accountcore_entry.account IS NOT NULL 
							END 
							AND
							CASE
									
									WHEN org_ids IS NOT NULL THEN
									accountcore_entry.org = ANY ( org_ids ) ELSE accountcore_entry.org IS NOT NULL 
								END 
								);
							INSERT INTO temp_table1 ( v_voucherdate, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance ) (
								SELECT NULL
									,
									NULL,
									NULL,
									NULL,
									NULL,
									SUM ( damount ),
									SUM ( camount ),
									NULL,
								NULL 
								FROM
									accountcore_entry 
								WHERE
									v_voucherdate BETWEEN start_dt 
									AND end_dt 
								);
							INSERT INTO temp_table1 ( v_voucherdate, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance ) (
								SELECT NULL
									,
									NULL,
									NULL,
									NULL,
									NULL,
									SUM ( damount ),
									SUM ( camount ),
									NULL,
								NULL 
								FROM
									accountcore_entry 
								WHERE
									v_voucherdate BETWEEN date_trunc( 'year', now( ) ) 
									AND date_trunc( 'year', now( ) ) + INTERVAL '1 year' - INTERVAL '1 day' 
								);
-- Routine body goes here...
							RETURN QUERY 
							SELECT
							( temp_table1.v_voucherdate ) AS 记账日期,
							( temp_table1.org_name ) AS 机构名称,
							( temp_table1.entry_number ) AS 凭证号,
							( temp_table1.account_name ) AS 会计科目,
							( temp_table2.item_names ) AS 核算项目,
							( temp_table1.entry_explain ) AS 摘要,
							COALESCE ( temp_table1.entry_damount, 0 ) AS 借方,
							COALESCE ( temp_table1.entry_camount, 0 ) AS 贷方,
							( temp_table1.direction ) AS 方向,
							COALESCE ( temp_table1.balance, 0 ) AS 余额,
							( temp_table1.voucher_id ) AS 凭证ID
							FROM
								temp_table2 LEFT JOIN temp_table1 ON temp_table1.entry_id = temp_table2.entry_id
							WHERE temp_table1.account_name IS NOT NULL;
							DROP TABLE
							IF
								EXISTS temp_table1;
							DROP TABLE
							IF
								EXISTS temp_table2;
						
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000''')