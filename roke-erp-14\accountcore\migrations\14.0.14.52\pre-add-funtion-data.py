from odoo import api, SUPERUSER_ID
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    env = api.Environment(cr, SUPERUSER_ID, {})
    sql_str = """
		            SELECT proname
		            FROM pg_proc
		            WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
		        """
    try:
        env.cr.execute(sql_str)
        data_list = env.cr.dictfetchall()
    except Exception as e:
        raise ValidationError(f"存储过程获取失败！{e}")
    proname_list = [v.get("proname", False) for v in data_list]
    for rec in proname_list:
        if rec == '核算项目余额帐':
            env.cr.execute('''
											DROP FUNCTION "核算项目余额帐"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[], "item_ids" integer[]);
											''')
        elif rec == '科目余额表':
            env.cr.execute('''
											DROP FUNCTION "科目余额表"("start_dt" date, "end_dt" date, "account_ids" integer[],"org_ids" integer[],"is_hide_zero_balance" boolean, "is_hide_zero_amount" boolean,"is_hide_double_zero_balance" boolean);
											''')
        elif rec == '总账':
            env.cr.execute('''
									DROP FUNCTION "总账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[]);
									''')
        elif rec == '明细账':
            env.cr.execute('''
					DROP FUNCTION "明细账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[]);
					''')
        elif rec == '项目核算明细账':
            env.cr.execute('''
											DROP FUNCTION "项目核算明细账"("start_dt" date, "end_dt" date, "account_ids" integer[], "org_ids" integer[], "item_ids" integer[]);
									''')
    env.cr.execute('''CREATE OR REPLACE FUNCTION "public"."核算项目余额帐"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4, "item_ids" _int4)
  RETURNS TABLE("机构名称" varchar, "会计日期" date, "科目编码" varchar, "科目名称" varchar, "核算项目" varchar, "期初余额" numeric, "期初方向" varchar, "本期借方发生" numeric, "本期贷方发生" numeric, "期末方向" varchar, "期末余额" numeric, "科目ID" int4, "机构ID" int4) AS $BODY$

BEGIN
    CREATE TEMPORARY TABLE temp_table
    (
				entry_id            NUMERIC,
        org_name            VARCHAR,
        account_date        DATE,
        account_number      VARCHAR,
        account_name        VARCHAR,
        begin_balance       NUMERIC,
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				direction    	    	VARCHAR,
				end_balance         NUMERIC,
				account_id          INTEGER,
				org_id              INTEGER
    );
		CREATE TEMPORARY TABLE temp_table1
    (
		    entry_id            NUMERIC,
        org_name            VARCHAR,
        account_date        DATE,
        account_number      VARCHAR,
        account_name        VARCHAR,
        begin_balance       NUMERIC,
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				direction    	    	VARCHAR,
				end_balance         NUMERIC,
				account_id          INTEGER,
				org_id              INTEGER
    );
		CREATE TEMPORARY TABLE temp_table2 (
			entry_id NUMERIC,
			item_names VARCHAR
		);
		CREATE TEMPORARY TABLE temp_table3
    (
		    entry_id            NUMERIC,
        org_name            VARCHAR,
        account_date        DATE,
        account_number      VARCHAR,
        account_name        VARCHAR,
				item_names          VARCHAR,
        begin_balance       NUMERIC,
				begin_direction    	VARCHAR,
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				end_direction    	  VARCHAR,
				end_balance         NUMERIC,
				account_id          INTEGER,
				org_id              INTEGER
    );
		CREATE TEMPORARY TABLE temp_table4
    (
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				account_id          INTEGER
    );
		CREATE TEMPORARY TABLE temp_table5
    (
		    entry_id            NUMERIC,
        org_name            VARCHAR,
        account_date        DATE,
        account_number      VARCHAR,
        account_name        VARCHAR,
				item_names          VARCHAR,
        begin_balance       NUMERIC,
				begin_direction    	VARCHAR,
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				end_direction    	  VARCHAR,
				end_balance         NUMERIC,
				account_id          INTEGER,
				org_id              INTEGER
    );
		CREATE TEMPORARY TABLE temp_table6
    (
		    entry_id            NUMERIC,
        org_name            VARCHAR,
        account_date        DATE,
        account_number      VARCHAR,
        account_name        VARCHAR,
				item_names          VARCHAR,
        begin_balance       NUMERIC,
				begin_direction    	VARCHAR,
				in_between_balance  NUMERIC,
				out_between_balance NUMERIC,
				end_direction    	  VARCHAR,
				end_balance         NUMERIC,
				account_id          INTEGER,
				org_id              INTEGER
    );
-- 		CREATE TEMPORARY TABLE temp_table5
--     (
-- 				account_id          INTEGER
--     );
-- 
		
		INSERT INTO temp_table2 (entry_id, item_names) (
			SELECT
				adict.entry_id,
				string_agg ( adict.item_name :: TEXT, ',' ) AS item_names 
			FROM
			( SELECT 
				accountcore_entry_accountcore_item_rel.accountcore_entry_id AS entry_id,
				accountcore_item.NAME AS item_name 
				FROM accountcore_entry_accountcore_item_rel 
				LEFT JOIN accountcore_item 
				ON accountcore_entry_accountcore_item_rel.accountcore_item_id = accountcore_item.ID 
				WHERE 
				CASE	
					WHEN item_ids IS NOT NULL THEN
					accountcore_entry_accountcore_item_rel.accountcore_item_id = ANY ( item_ids ) ELSE accountcore_entry_accountcore_item_rel.accountcore_item_id IS 				 NOT NULL 
				END 
		) AS adict 
	GROUP BY
		adict.entry_id
			);
		IF (item_ids IS NULL) THEN
		INSERT INTO temp_table2 (entry_id, item_names) (
		SELECT id AS entry_id,NUll AS names 
		FROM accountcore_entry 
		WHERE id NOT IN (SELECT accountcore_entry_id FROM accountcore_entry_accountcore_item_rel ));
		ELSE
		INSERT INTO temp_table2 (entry_id, item_names) (
		SELECT id AS entry_id,NUll AS names 
		FROM accountcore_entry 
		WHERE id = 0);
		END IF;
		
    INSERT INTO temp_table (
			 entry_id,
			 org_name,
			 account_date,
			 account_number,
			 account_name,
			 begin_balance,
			 in_between_balance,
			 out_between_balance,
			 direction,
			 end_balance,
			 account_id,
			 org_id)
			 (select accountcore_entry.id as 记录ID,
							 accountcore_org.name as 机构名称,
							 accountcore_entry.v_voucherdate as 会计日期,
							 accountcore_account.number as 科目编码,
               accountcore_account.name as 科目名称,
							 case when accountcore_account.direction = '1' then
               COALESCE(yue.begin_year_amount, 0) + coalesce(ae.d_amount, 0)
               else COALESCE(yue.begin_year_amount, 0) + coalesce(ae.c_amount, 0) end as 期初余额,
               sum(accountcore_entry.damount) as 本期借方发生,
               sum(accountcore_entry.camount) as 本期贷方发生,
							 case when accountcore_account.direction = '1' then '借' else '贷' end as 期初方向,
               case when accountcore_account.direction = '1' then
               (COALESCE(yue.begin_year_amount, 0) + COALESCE(ae.d_amount, 0) +
               sum(COALESCE(accountcore_entry.damount,0)) - sum(COALESCE(accountcore_entry.camount,0)))
               else (COALESCE(yue.begin_year_amount, 0) + COALESCE(ae.c_amount, 0) +
               sum(COALESCE(accountcore_entry.camount,0)) - sum(COALESCE(accountcore_entry.damount,0))) end as 期末余额,
							 accountcore_account.id as 科目ID,
							 accountcore_org.id as 机构ID
							 from accountcore_entry
									 LEFT JOIN accountcore_account ON accountcore_entry.account = accountcore_account.ID
									 LEFT JOIN accountcore_org ON accountcore_entry.org = accountcore_org.ID
									 LEFT JOIN (select account, begin_year_amount, kj_create_date
															from accountcore_accounts_balance
															where isbegining = True
																and case
																				when account_ids is not null
																						then account = ANY (account_ids)
																				else account is not null end
																and case
																				when org_ids is not null then org = ANY (org_ids)
																				else org is not null end
																and case
																				when kj_create_date is not null
																						then kj_create_date >= start_dt and kj_create_date <= end_dt
																				else 1=1 end
															order by kj_create_date desc) yue
														 on yue.account = accountcore_account.id
									 LEFT JOIN (select entry.account,
														  sum(entry.damount) - sum(entry.camount) as d_amount,
														  sum(entry.camount) - sum(entry.damount) as c_amount
															from accountcore_entry entry
															LEFT JOIN accountcore_account aa1 on entry.account = aa1.id
															where v_voucherdate < start_dt
																and case
																				when account_ids is not null
																						then entry.account = any (account_ids)
																				else entry.account is not null end
																and case
																				when org_ids is not null
																						then entry.org = any (org_ids)
																				else entry.org is not null end
															group by entry.account) as ae
														 on ae.account = accountcore_account.id
									 where v_voucherdate between start_dt and end_dt
												and case
																when account_ids is not null
																		then accountcore_entry.account = ANY (account_ids)
																else accountcore_entry.account is not null end
												and case
																when org_ids is not null then accountcore_entry.org = ANY (org_ids)
																else accountcore_entry.org is not null end
								group by accountcore_account.name, accountcore_account.direction, ae.d_amount,
												 ae.c_amount,accountcore_account.id, accountcore_org.id,
												 yue.begin_year_amount,accountcore_org.name,accountcore_entry.v_voucherdate,
												 accountcore_account.number,accountcore_entry.id);
	 INSERT INTO temp_table1 (
	       entry_id,
				 org_name,
				 account_date,
				 account_number,
				 account_name,
				 begin_balance,
				 in_between_balance,
				 out_between_balance,
				 direction,
				 end_balance,
				 account_id,
				 org_id)
				 (select
						 entry_id,
					   org_name,
						 account_date,
						 account_number,
						 account_name,
						 begin_balance,
						 in_between_balance,
						 out_between_balance,
						 direction,
						 end_balance,
						 account_id,
				     org_id
						 from temp_table
						 where case
												when account_ids is not null
														then temp_table.account_id = any (account_ids)
												else 1=1 end
								and case
												when org_ids is not null
														then temp_table.org_id = any (org_ids)
												else 1=1 end
								and case
												when account_date is not null
														then account_date >= start_dt and account_date <= end_dt
												else 1=1 end

							);
		
INSERT INTO temp_table3 (
				 org_name,
				 account_date,
				 account_number,
				 account_name,
				 item_names,
				 begin_balance,
				 begin_direction,
				 in_between_balance,
				 out_between_balance,
				 end_direction,
				 end_balance,
				 account_id,
				 org_id)(select
					   temp_table1.org_name,
-- 						 temp_table1.account_date,
						 date(end_dt),
						 temp_table1.account_number,
						 temp_table1.account_name,
						 temp_table2.item_names,
						 COALESCE(temp_table1.begin_balance, 0),
						 temp_table1.direction,
						 COALESCE(temp_table1.in_between_balance, 0),
						 COALESCE(temp_table1.out_between_balance, 0),
						 NULL,
						 COALESCE(temp_table1.end_balance, 0),
						 temp_table1.account_id,
				     temp_table1.org_id
						 FROM
						 temp_table2 LEFT JOIN temp_table1 ON temp_table1.entry_id = temp_table2.entry_id
						 WHERE temp_table1.account_name IS NOT NULL);
						 

						 
		INSERT INTO temp_table4 (
												 account_id,
												 in_between_balance,
												 out_between_balance)(select
																			account_id,
																			sum(in_between_balance),
																			sum(out_between_balance)
																	from temp_table3
																	GROUP BY account_id);
												
																
		INSERT INTO temp_table5 (
				 org_name,
				 account_date,
				 account_number,
				 account_name,
				 item_names,
				 begin_balance,
				 begin_direction,
				 in_between_balance,
				 out_between_balance,
				 end_direction,
				 end_balance,
				 account_id,
				 org_id)(select
					   temp_table3.org_name,
						 temp_table3.account_date,
						 temp_table3.account_number,
						 temp_table3.account_name,
						 temp_table3.item_names,
						 COALESCE(temp_table3.begin_balance, 0),
						 temp_table3.begin_direction,
						 ABS(COALESCE(temp_table4.in_between_balance, 0)),
						 ABS(COALESCE(temp_table4.out_between_balance, 0)),
						 temp_table3.end_direction,
						 0,
						 temp_table3.account_id,
						 temp_table3.org_id
						 FROM
						 temp_table4
					   LEFT JOIN temp_table3 ON temp_table4.account_id = temp_table3.account_id
						 GROUP BY
							temp_table3.org_name,temp_table3.account_date,temp_table3.account_number,
							temp_table3.account_name,temp_table3.item_names,temp_table3.begin_balance,
							temp_table3.begin_direction,temp_table4.in_between_balance,temp_table4.out_between_balance,
							temp_table3.end_direction,temp_table3.account_id,temp_table3.org_id);			
							
																
		INSERT INTO temp_table6 (
				 org_name,
				 account_date,
				 account_number,
				 account_name,
				 item_names,
				 begin_balance,
				 begin_direction,
				 in_between_balance,
				 out_between_balance,
				 end_direction,
				 end_balance,
				 account_id,
				 org_id)(select
					   temp_table5.org_name,
						 temp_table5.account_date,
						 temp_table5.account_number,
						 temp_table5.account_name,
						 temp_table5.item_names,
						 COALESCE(temp_table5.begin_balance, 0),
						 temp_table5.begin_direction,
						 ABS(COALESCE(temp_table5.in_between_balance, 0)),
						 ABS(COALESCE(temp_table5.out_between_balance, 0)),
						 case when (temp_table5.begin_direction = '借' and (COALESCE(temp_table5.begin_balance,0) + COALESCE(temp_table5.in_between_balance,0) - COALESCE(temp_table5.out_between_balance,0)) >= 0) or (temp_table5.begin_direction = '贷' and (COALESCE(temp_table5.begin_balance,0) - COALESCE(temp_table5.in_between_balance,0) + COALESCE(temp_table5.out_between_balance,0)) < 0) then '借' else '贷' end,
-- 						 ABS(COALESCE(temp_table5.end_balance, 0)),
						 case when temp_table5.begin_direction = '借' then ABS(COALESCE(temp_table5.begin_balance, 0) + COALESCE(temp_table5.in_between_balance, 0) - COALESCE(temp_table5.out_between_balance, 0)) else ABS(COALESCE(temp_table5.begin_balance, 0) - COALESCE(temp_table5.in_between_balance, 0) + COALESCE(temp_table5.out_between_balance, 0)) end,
						 temp_table5.account_id,
						 temp_table5.org_id
						 FROM
						 temp_table5);																

		RETURN QUERY 
				SELECT
				 (temp_table6.org_name)                       AS 机构名称,
				 (temp_table6.account_date)                 	AS 会计日期,
				 (temp_table6.account_number)                 AS 科目编码,
				 (temp_table6.account_name)          	        AS 科目名称,
				 (temp_table6.item_names)                     AS 核算项目,
				 ABS(COALESCE(temp_table6.begin_balance, 0))  AS 期初余额,
				 (temp_table6.begin_direction)                AS 期初方向,
				 ABS(COALESCE(temp_table6.in_between_balance, 0))  AS 本期借方发生,
				 ABS(COALESCE(temp_table6.out_between_balance, 0)) AS 本期贷方发生,
				 (temp_table6.end_direction)          	      AS 期末方向,
				 ABS(COALESCE(temp_table6.end_balance, 0))    AS 期末余额,
				 (temp_table6.account_id)                     AS 科目ID,
				 (temp_table6.org_id)                         AS 机构ID
				FROM
					temp_table6;
				DROP TABLE
				IF
					EXISTS temp_table;
				DROP TABLE
				IF
					EXISTS temp_table1;
				DROP TABLE
				IF
					EXISTS temp_table2;
				DROP TABLE
				IF
					EXISTS temp_table3;
				DROP TABLE
				IF
					EXISTS temp_table4;
				DROP TABLE
				IF
					EXISTS temp_table5;
				DROP TABLE
				IF
					EXISTS temp_table6;

END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000''')
    env.cr.execute('''
				CREATE OR REPLACE FUNCTION "public"."科目余额表"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4, "is_hide_zero_balance" bool, "is_hide_zero_amount" bool, "is_hide_double_zero_balance" bool)
  RETURNS TABLE("科目编号" varchar, "科目名称" varchar, "方向" varchar, "期初余额" numeric, "借方" numeric, "贷方" numeric, "期末方向" varchar, "期末余额" numeric, "科目ID" int4) AS $BODY$
BEGIN
    CREATE TEMPORARY TABLE temp_table1
    (
        account_number VARCHAR,
        account_name   VARCHAR,
        direction      VARCHAR,
        qc_balance     NUMERIC,
        entry_damount  NUMERIC,
        entry_camount  NUMERIC,
				end_direction  VARCHAR,
        balance        NUMERIC,
				account_id     INTEGER,
				father_id      INTEGER
    );
    CREATE TEMPORARY TABLE temp_table2
    (
        account_id     INTEGER,
				father_id      INTEGER,
        account_number VARCHAR,
        account_name   VARCHAR,
        direction      VARCHAR,
        qc_balance     NUMERIC,
        entry_damount  NUMERIC,
        entry_camount  NUMERIC,
        balance        NUMERIC
    );
		CREATE TEMPORARY TABLE temp_table3
    (
				account_id     INTEGER,
        qc_balance     NUMERIC,
        entry_damount  NUMERIC,
        entry_camount  NUMERIC,
        balance        NUMERIC
    );
		CREATE TEMPORARY TABLE temp_table0
    (
				account_id     INTEGER
    );

		CREATE TEMPORARY TABLE temp_table4
    (
				account_id     INTEGER
    );
		
		
		IF (account_ids IS NULL) THEN
		INSERT INTO temp_table0 (account_id
														)(select id
														from accountcore_account);
		INSERT INTO temp_table4 (account_id
														)(select id
														from accountcore_account);
		ELSE
		INSERT INTO temp_table4 (account_id
														)(select id
														from accountcore_account
														where id = any (account_ids));
		INSERT INTO temp_table0 (account_id
														)(select id
														from accountcore_account
														where "fatherAccountId" = any (account_ids));
		INSERT INTO temp_table0 (account_id
														)(select id
														from accountcore_account
														where id = any (account_ids));
		END IF;
														
    INSERT INTO temp_table2 (
                             account_id,
														 father_id,
                             account_number,
                             account_name,
                             direction,
                             qc_balance,
                             entry_damount,
                             entry_camount,
                             balance)(select accountcore_account.id as 科目ID,
														 accountcore_account."fatherAccountId" as 父科目ID,
														 accountcore_account.number as 科目编号,
														 accountcore_account.name as 科目名称,
														 case when accountcore_account.direction = '1' then '借' else '贷' end as 方向,
														 case
																	 when accountcore_account.direction = '1' then
																			 COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0)
																	 else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) end as 期初余额,
															 sum(accountcore_entry.damount)                                             as 借方,
															 sum(accountcore_entry.camount)                                             as 贷方,
															 case
																	 when accountcore_account.direction = '1' then
																			 ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0) +
																				sum(COALESCE(accountcore_entry.damount,0)) - sum(COALESCE(accountcore_entry.camount,0)))
																	 else ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) +
																				sum(COALESCE(accountcore_entry.camount,0)) - sum(COALESCE(accountcore_entry.damount,0)))
																	 end                                                                    as 期末余额
                                      from accountcore_account
                                               LEFT JOIN (select *
                                                          from accountcore_entry
                                                          where v_voucherdate between start_dt and end_dt
                                                            and case
                                                                    when account_ids is not null
                                                                        then accountcore_entry.account = ANY (select account_id from temp_table0)
                                                                    else accountcore_entry.account is not null end
                                                            and case
                                                                    when org_ids is not null
                                                                        then accountcore_entry.org = ANY (org_ids)
                                                                    else accountcore_entry.org is not null end) accountcore_entry
                                                         on accountcore_entry.account = accountcore_account.id
                                               LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = ANY (select account_id from temp_table0)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = ANY (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_account.id
                                               LEFT JOIN (select ae1.account,
                                                                 sum(ae1.damount) -
                                                                 sum(ae1.camount) as d_amount,
                                                                 sum(ae1.camount) -
                                                                 sum(ae1.damount) as c_amount
                                                          from accountcore_entry ae1
                                                                   LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
                                                          where v_voucherdate < start_dt
                                                            and case
                                                                    when account_ids is not null
                                                                        then ae1.account = any (select account_id from temp_table0)
                                                                    else ae1.account is not null end
                                                            and case
                                                                    when org_ids is not null
                                                                        then ae1.org = any (org_ids)
                                                                    else ae1.org is not null end
                                                          group by ae1.account) as ae
                                                         on ae.account = accountcore_account.id
                                      group by accountcore_account.name, accountcore_account.direction, ae.d_amount,
                                               ae.c_amount,accountcore_account.id,
                                               aab.begin_year_amount,
                                               accountcore_account.number);
    INSERT INTO temp_table1 (
                             account_number,
                             account_name,
                             direction,
                             qc_balance,
                             entry_damount,
                             entry_camount,
														 end_direction,
                             balance,
														 account_id,
														 father_id)(select
                                          account_number,
                                             account_name,
                                             direction,
                                             qc_balance,
                                             entry_damount,
                                             entry_camount,
																						 case when (temp_table2.direction = '借' and (COALESCE(temp_table2.qc_balance,0) + COALESCE(temp_table2.entry_damount,0) - COALESCE(temp_table2.entry_camount,0)) >= 0) or (temp_table2.direction = '贷' and (COALESCE(temp_table2.qc_balance,0) - COALESCE(temp_table2.entry_damount,0) + COALESCE(temp_table2.entry_camount,0)) < 0) then '借' else '贷' end,
-- 																						 temp_table2.direction,
                                             balance,
																						 account_id,
																						 father_id
                                      from temp_table2
                                      where case
                                                when is_hide_zero_balance = 'True'
                                                    then COALESCE(temp_table2.balance, 0) != 0
                                                else 1 = 1 end
                                        and case
                                                when is_hide_zero_amount = 'True' then not (
                                                            COALESCE(temp_table2.entry_damount, 0) = 0 and
                                                            COALESCE(temp_table2.entry_camount, 0) = 0)
                                                else
                                                    1 = 1 end
                                        and case
                                                when is_hide_double_zero_balance = 'True' then not (
                                                            COALESCE(temp_table2.balance, 0) = 0 and
                                                            COALESCE(temp_table2.qc_balance, 0) = 0)
                                                else 1 = 1 end and
                                        case
                                                                    when account_ids is not null
                                                                        then temp_table2.account_id = any (select account_id from temp_table0)
                                                                    else 1=1 end

                                      );
																			
																			
	INSERT INTO temp_table3 (
                             account_id,
														 qc_balance,
                             entry_damount,
                             entry_camount,
                             balance)(select
                                          father_id,
																					sum(qc_balance),
																					sum(entry_damount),
																					sum(entry_camount),
																					sum(balance)
                                      from temp_table1
																			GROUP BY father_id);
						
																			
-- Routine body goes here...
    RETURN QUERY SELECT
												(temp_table1.account_number)           as 科目编号,
                        (temp_table1.account_name)             AS 会计科目,
                        (temp_table1.direction)                AS 方向,
												case when COALESCE(temp_table1.qc_balance, 0) != 0 then COALESCE(temp_table1.qc_balance, 0) else COALESCE(temp_table3.qc_balance, 0) end                   AS 期初余额,
												case when COALESCE(temp_table1.entry_damount, 0) != 0 then COALESCE(temp_table1.entry_damount, 0) else COALESCE(temp_table3.entry_damount, 0) end           AS 借方,
												case when COALESCE(temp_table1.entry_camount, 0) != 0 then COALESCE(temp_table1.entry_camount, 0) else COALESCE(temp_table3.entry_camount, 0) end           AS 贷方,
												(temp_table1.end_direction)            AS 期末方向,
												case when COALESCE(temp_table1.balance, 0) != 0 then COALESCE(temp_table1.balance, 0) else COALESCE(temp_table3.balance, 0) end                           AS 期末余额,
												(temp_table1.account_id)               as 科目ID
								FROM temp_table1 LEFT JOIN temp_table3 ON temp_table3.account_id = temp_table1.account_id
								WHERE temp_table1.account_id = any(select account_id from temp_table4)
								ORDER BY temp_table1.account_number;

    DROP TABLE IF EXISTS temp_table1;
    DROP TABLE IF EXISTS temp_table2;
		DROP TABLE IF EXISTS temp_table3;
		DROP TABLE IF EXISTS temp_table4;
		DROP TABLE IF EXISTS temp_table0;
		
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000''')
    env.cr.execute('''CREATE OR REPLACE FUNCTION "public"."总账"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4)
  RETURNS TABLE("会计科目" varchar, "借方" numeric, "贷方" numeric, "方向" varchar, "余额" numeric, "科目ID" int4) AS $BODY$
	BEGIN
	    CREATE TEMPORARY TABLE temp_table1
	    (
	        account_name  VARCHAR,
	        entry_damount NUMERIC,
	        entry_camount NUMERIC,
	        direction     VARCHAR,
	        balance       NUMERIC,
	        account_id    INTEGER,
					father_id     INTEGER
	    );

			CREATE TEMPORARY TABLE temp_table0
			(
					account_id     INTEGER
			);

			CREATE TEMPORARY TABLE temp_table2
			(
					account_id     INTEGER
			);

			CREATE TEMPORARY TABLE temp_table3
			(
					account_name  VARCHAR,
	        entry_damount NUMERIC,
	        entry_camount NUMERIC,
	        direction     VARCHAR,
	        balance       NUMERIC,
	        account_id    INTEGER,
					father_id     INTEGER
			);

			CREATE TEMPORARY TABLE temp_table4
			(
					account_name  VARCHAR,
	        entry_damount NUMERIC,
	        entry_camount NUMERIC,
	        direction     VARCHAR,
	        balance       NUMERIC,
	        account_id    INTEGER,
					father_id     INTEGER
			);

			INSERT INTO temp_table0 (account_id
															)(select id
															from accountcore_account);

			IF (account_ids IS NULL) THEN
			INSERT INTO temp_table2 (account_id
															)(select id
															from accountcore_account);
			ELSE
			INSERT INTO temp_table2 (account_id
															)(select id
															from accountcore_account
															where id = any (account_ids));

			END IF;


	    INSERT INTO temp_table1 (account_name,
	                             entry_damount,
	                             entry_camount,
	                             direction,
	                             balance,
	                             account_id,
															 father_id)(select accountcore_account.name                                              as 会计科目,
	                                             sum(accountcore_entry.damount)                                        as 借方,
	                                             sum(accountcore_entry.camount)                                        as 贷方,
	                                             case when accountcore_account.direction = '1' then '借' else '贷' end as 方向,
	                                             case
	                                                 when accountcore_account.direction = '1' then
	                                                     (COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount,0) +
	                                                      sum(accountcore_entry.damount) - sum(accountcore_entry.camount))
	                                                 else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount,0) +
	                                                      sum(accountcore_entry.camount) - sum(accountcore_entry.damount)
	                                                 end                                                               as 余额,
	                                                 accountcore_account.id as 科目ID,
																									 accountcore_account."fatherAccountId" as 父科目ID
	                                      from accountcore_entry
																				 LEFT JOIN accountcore_account
																									 on accountcore_entry.account = accountcore_account.id
																				 LEFT JOIN (select account, begin_year_amount, "kj_create_date"
																										from accountcore_accounts_balance
																										where isbegining = True
																											and case
																															when account_ids is not null
																																	then account = ANY (select account_id from temp_table0)
																															else account is not null end
																											and case
																															when org_ids is not null then org = ANY (org_ids)
																															else org is not null end
																										order by "kj_create_date" desc) aab
																									 on aab.account = accountcore_entry.account
																				 LEFT JOIN (select ae1.account,
																													 sum(ae1.damount) -
																													 sum(ae1.camount) as d_amount,
																													 sum(ae1.camount) -
																													 sum(ae1.damount) as c_amount
																										from accountcore_entry ae1
																														 LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
																										where v_voucherdate < start_dt
																											and case
																															when account_ids is not null
																																	then ae1.account = any (select account_id from temp_table0)
																															else ae1.account is not null end
																											and case
																															when org_ids is not null
																																	then ae1.org = any (org_ids)
																															else ae1.org is not null end
																										group by ae1.account) as ae
																									 on ae.account = accountcore_entry.account
	                                      where v_voucherdate between start_dt and end_dt
	                                        and case
	                                                when account_ids is not null
	                                                    then accountcore_entry.account = ANY (select account_id from temp_table0)
	                                                else accountcore_entry.account is not null end
	                                        and case
	                                                when org_ids is not null then accountcore_entry.org = ANY (org_ids)
	                                                else accountcore_entry.org is not null end
	                                      group by accountcore_account.name, accountcore_account.direction, ae.d_amount,
	                                               ae.c_amount,accountcore_account.id,
	                                               aab.begin_year_amount);



		 INSERT INTO temp_table3 (
														 entry_damount,
														 entry_camount,
														 balance,
														 account_id
                             )(select
																		sum(entry_damount),
																		sum(entry_camount),
																		sum(balance),
																		father_id
																from temp_table1
																GROUP BY father_id);


			INSERT INTO temp_table4 (
														 account_name,
														 entry_damount,
														 entry_camount,
														 direction,
														 balance,
														 account_id,
														 father_id)(select
																	accountcore_account.name,
																  COALESCE(temp_table3.entry_damount, 0),
																  COALESCE(temp_table3.entry_camount, 0),
																	case when accountcore_account.direction = '1' then '借' else '贷' end,
																  COALESCE(temp_table3.balance, 0),
																	accountcore_account.id,
																	NULL
																from temp_table3
																left join accountcore_account ON temp_table3.account_id = accountcore_account.id
																where temp_table3.account_id is not NULL);	


		 INSERT INTO temp_table1 (
												 account_name,
												 entry_damount,
												 entry_camount,
												 direction,
												 balance,
												 account_id,
												 father_id)(select
																			account_name,
																			entry_damount,
																			entry_camount,
																			direction,
																			balance,
																			account_id,
																			father_id
																	from temp_table4); 

-- 	Routine body goes here...
		RETURN QUERY SELECT
													(temp_table1.account_name)              AS 会计科目,
													case when COALESCE(temp_table1.entry_damount, 0) > 0 then COALESCE(temp_table1.entry_damount, 0) else COALESCE(temp_table3.entry_damount, 0) end AS 借方,
	                        case when COALESCE(temp_table1.entry_camount, 0) > 0 then COALESCE(temp_table1.entry_camount, 0) else COALESCE(temp_table3.entry_camount, 0) end AS 贷方,
	                        (temp_table1.direction)                 AS 方向,
													case when COALESCE(temp_table1.balance, 0) > 0 then ABS(COALESCE(temp_table1.balance, 0)) else ABS(COALESCE(temp_table3.balance, 0)) end                              AS 余额,
	                        (temp_table1.account_id)                AS 科目ID
													FROM temp_table1 LEFT JOIN temp_table3 ON temp_table3.account_id = temp_table1.account_id
									WHERE temp_table1.account_id = any(select account_id from temp_table2);
					DROP TABLE IF EXISTS temp_table0;
					DROP TABLE IF EXISTS temp_table1;
					DROP TABLE IF EXISTS temp_table2;
					DROP TABLE IF EXISTS temp_table3;
					DROP TABLE IF EXISTS temp_table4;

	END
	$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000
		''')
    env.cr.execute('''
				CREATE OR REPLACE FUNCTION "public"."明细账"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4)
  RETURNS TABLE("日期" date, "会计科目" varchar, "凭证号" varchar, "摘要" varchar, "借方" numeric, "贷方" numeric, "方向" varchar, "余额" numeric, "凭证ID" int4) AS $BODY$
BEGIN
    CREATE TEMPORARY TABLE temp_table1
    (
				entry_id integer,
				account_id  integer,
        v_voucherdate DATE,
        account_name  VARCHAR,
        entry_number  VARCHAR,
        entry_explain VARCHAR,
        entry_damount NUMERIC,
        entry_camount NUMERIC,
        direction     VARCHAR,
        balance       NUMERIC,
        voucher_id    INTEGER
    );

		CREATE TEMPORARY TABLE temp_table4 (
		  entry_id integer,
		  balance NUMERIC,
			amount NUMERIC,
			voucher_id integer,
			account_id  integer 
		);

		CREATE TEMPORARY TABLE temp_table5 (
		  entry_id integer,
		  balance NUMERIC,
			amount NUMERIC,
			voucher_id integer,
			account_id  integer
		);
		CREATE TEMPORARY TABLE temp_table0
    (
				account_id     INTEGER
    );
		CREATE TEMPORARY TABLE temp_table8
    (
				account_id     INTEGER
    );
		CREATE TEMPORARY TABLE temp_table6
    (
				entry_id integer,
				account_id  integer,
        v_voucherdate DATE,
        account_name  VARCHAR,
        entry_number  VARCHAR,
        entry_explain VARCHAR,
        entry_damount NUMERIC,
        entry_camount NUMERIC,
        direction     VARCHAR,
        balance       NUMERIC,
        voucher_id    INTEGER
    );

    INSERT INTO temp_table1 (
														 entry_id,
														 account_id,
														 v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select accountcore_entry.id           as 编号id,
																								accountcore_account.id         as 科目id,
																							accountcore_entry.v_voucherdate                                                                                        as 日期,
                                             accountcore_account.name                                                                                               as 会计科目,
                                             accountcore_entry.v_number                                                                                             as 凭证号,
                                             accountcore_entry.explain                                                                                              as 摘要,
                                             accountcore_entry.damount                                                                                              as 借方,
                                             accountcore_entry.camount                                                                                              as 贷方,
                                             case when accountcore_account.direction = '1' then '借' else '贷' end                                                  as 方向,
                                             case
                                                 when accountcore_account.direction = '1' then
                                                     ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount,0) +
                                                      sum(accountcore_entry.damount) - sum(accountcore_entry.camount))
                                                 else ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount,0) +
                                                      sum(accountcore_entry.camount) - sum(accountcore_entry.damount))
                                                 end                                                               as 余额,
                                             accountcore_voucher.id as 凭证ID
                                      from accountcore_entry
											   LEFT JOIN accountcore_voucher
                                                         on accountcore_entry.voucher = accountcore_voucher.id
                                               LEFT JOIN accountcore_account
                                                         on accountcore_entry.account = accountcore_account.id
                                               LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = any (account_ids)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = any (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_entry.account
                                               LEFT JOIN (select ae1.account,
                                                                 sum(ae1.damount) -
                                                                 sum(ae1.camount) as d_amount,
                                                                 sum(ae1.camount) -
                                                                 sum(ae1.damount) as c_amount
                                                          from accountcore_entry ae1
                                                                   LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
                                                          where v_voucherdate < start_dt
                                                            and case
                                                                    when account_ids is not null
                                                                        then ae1.account = any (account_ids)
                                                                    else ae1.account is not null end
                                                            and case
                                                                    when org_ids is not null
                                                                        then ae1.org = any (org_ids)
                                                                    else ae1.org is not null end
                                                          group by ae1.account) as ae
                                                         on ae.account = accountcore_entry.account
                                      where v_voucherdate between start_dt and end_dt
                                        and case
                                                when account_ids is not null
                                                    then accountcore_entry.account = any (account_ids)
                                                else accountcore_entry.account is not null end
                                        and case
                                                when org_ids is not null then accountcore_entry.org = any (org_ids)
                                                else accountcore_entry.org is not null end
																			group by ae.d_amount, ae.c_amount,accountcore_account.id,accountcore_entry.v_voucherdate,
                                               aab.begin_year_amount,accountcore_entry.v_number,
																							 accountcore_entry.explain,accountcore_entry.damount,
																							 accountcore_entry.camount,accountcore_voucher.id,accountcore_entry.id,
																							 accountcore_account.id);

		IF (account_ids IS NULL) THEN
		INSERT INTO temp_table1 (
														 entry_id,
														 account_id,
														 v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select NULL,
																						 NULL,
																						 NULL,
                                             NULL,
                                             '本期累计',
                                             NULL,
                                             sum(damount),
                                             sum(camount),
                                             NULL,
                                             NULL,
                                             NULL
                                      from accountcore_entry
                                      where v_voucherdate between start_dt and end_dt);
    INSERT INTO temp_table1 (
														 entry_id,
														 account_id,
														 v_voucherdate,
                             account_name,
                             entry_number,
                             entry_explain,
                             entry_damount,
                             entry_camount,
                             direction,
                             balance,
                             voucher_id)(select NULL,
                                             NULL,
																						 NULL,
                                             NULL,
                                             '本年累计',
                                             NULL,
                                             sum(damount),
                                             sum(camount),
                                             NULL,
                                             NULL,
                                             NULL
                                      from accountcore_entry
                                      where v_voucherdate between date_trunc('year', now()) and date_trunc('year', now()) + interval '1 year' - interval '1 day');

		ELSE
		INSERT INTO temp_table8 (account_id
															)(select id
															from accountcore_account);			
		END IF;														
		INSERT INTO temp_table4 (
				 entry_id,
				 balance,
				 amount,
				 voucher_id,
				 account_id)(select
				     temp_table1.entry_id,
						 COALESCE ( temp_table1.balance, 0 ),
						 case when temp_table1.direction = '借' then COALESCE (temp_table1.entry_damount,0) - COALESCE(temp_table1.entry_camount,0) else COALESCE(temp_table1.entry_camount,0) - COALESCE(temp_table1.entry_damount,0) end,
						 temp_table1.voucher_id,
						 temp_table1.account_id
						from temp_table1
						order by voucher_id);



	INSERT INTO temp_table5 (
				 entry_id,
				 balance,
				 amount,
				 voucher_id,
				 account_id)(
				 SELECT a.entry_id,(SELECT (case when (a.balance + sum(b.amount))>0 then (a.balance + sum(b.amount)) else a.balance end) FROM temp_table4 b WHERE b.voucher_id < a.voucher_id and b.account_id = a.account_id),
				            COALESCE(a.amount,0),a.voucher_id,a.account_id
						FROM temp_table4 a
						order by voucher_id);

		INSERT INTO temp_table6 (
									v_voucherdate,
									account_name,
									entry_number,
									entry_explain,
									entry_damount,
									entry_camount,
									direction,
									balance,
									voucher_id)
									(select
									temp_table1.v_voucherdate,
									temp_table1.account_name,
									temp_table1.entry_number,
									temp_table1.entry_explain,
									COALESCE(temp_table1.entry_damount, 0),
									COALESCE(temp_table1.entry_camount, 0),
									temp_table1.direction,
									temp_table5.balance,
									temp_table1.voucher_id
						from temp_table1
						LEFT JOIN temp_table5 ON temp_table1.entry_id = temp_table5.entry_id
								 ORDER BY temp_table1.account_name, temp_table1.voucher_id);				


		IF (account_ids IS NULL) THEN
		INSERT INTO temp_table0 (account_id
															)(select id
															from accountcore_account);
		ELSE
		INSERT INTO temp_table6 (
														 account_name,
														 entry_explain,
														 balance,
														 direction,
														 voucher_id
														)(select 
														accountcore_account.name,
														'期初余额',
														case when accountcore_account.direction = '1' then
															 COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0)
													  else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) end,
														case when accountcore_account.direction = '1' then '借' else '贷' end,
														0
														from accountcore_account
														LEFT JOIN (select *
																			from accountcore_entry
																			where v_voucherdate between start_dt and end_dt
																				and case
																								when account_ids is not null
																										then accountcore_entry.account = ANY (select account_id from temp_table0)
																								else accountcore_entry.account is not null end
																				and case
																								when org_ids is not null
																										then accountcore_entry.org = ANY (org_ids)
																								else accountcore_entry.org is not null end) accountcore_entry
																		 on accountcore_entry.account = accountcore_account.id
														LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = ANY (account_ids)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = ANY (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_account.id
													  LEFT JOIN (select ae1.account,
																			 sum(ae1.damount) -
																			 sum(ae1.camount) as d_amount,
																			 sum(ae1.camount) -
																			 sum(ae1.damount) as c_amount
																from accountcore_entry ae1
																				 LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
																where v_voucherdate < start_dt
																	and case
																					when account_ids is not null
																							then ae1.account = any (account_ids)
																					else ae1.account is not null end
																	and case
																					when org_ids is not null
																							then ae1.org = any (org_ids)
																					else ae1.org is not null end
																group by ae1.account) as ae
															 on ae.account = accountcore_account.id
														where accountcore_account.id = any (account_ids));

		END IF;															



-- Routine body goes here...
    RETURN QUERY SELECT (temp_table6.v_voucherdate)            AS 日期,
                        (temp_table6.account_name)             AS 会计科目,
                        (temp_table6.entry_number)             AS 凭证号,
                        (temp_table6.entry_explain)            AS 摘要,
                        COALESCE(temp_table6.entry_damount, 0) AS 借方,
                        COALESCE(temp_table6.entry_camount, 0) AS 贷方,
                        (temp_table6.direction)                AS 方向,
                        (temp_table6.balance)                  AS 余额,
                        (temp_table6.voucher_id)               AS 凭证ID
                 FROM temp_table6
								 ORDER BY temp_table6.account_name, temp_table6.voucher_id;
    DROP TABLE
		IF
				EXISTS temp_table1;
	  DROP TABLE
		IF
			EXISTS temp_table4;
		DROP TABLE
		IF
			EXISTS temp_table5;
		DROP TABLE
		IF
			EXISTS temp_table6;
		DROP TABLE
		IF
			EXISTS temp_table8;
		DROP TABLE
		IF
			EXISTS temp_table0;

END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000
		''')
    env.cr.execute('''CREATE OR REPLACE FUNCTION "public"."项目核算明细账"("start_dt" date, "end_dt" date, "account_ids" _int4, "org_ids" _int4, "item_ids" _int4)
  RETURNS TABLE("记账日期" date, "机构名称" varchar, "凭证号" varchar, "会计科目" varchar, "核算项目" varchar, "摘要" varchar, "借方" numeric, "贷方" numeric, "方向" varchar, "余额" numeric, "凭证ID" int4) AS $BODY$ BEGIN
		CREATE TEMPORARY TABLE temp_table1 (
			entry_id NUMERIC,
			account_id integer, 
			v_voucherdate DATE,
			org_name VARCHAR,
			entry_number VARCHAR,
			account_name VARCHAR,
			entry_explain VARCHAR,
			entry_damount NUMERIC,
			entry_camount NUMERIC,
			direction VARCHAR,
			balance NUMERIC,
			voucher_id integer 
		);
		CREATE TEMPORARY TABLE temp_table2 (
			entry_id NUMERIC,
			item_names VARCHAR
		);

		CREATE TEMPORARY TABLE temp_table3 (
			entry_id NUMERIC,
			account_id integer,
			v_voucherdate DATE,
			org_name VARCHAR,
			entry_number VARCHAR,
			account_name VARCHAR,
			item_names VARCHAR,
			entry_explain VARCHAR,
			entry_damount NUMERIC,
			entry_camount NUMERIC,
			direction VARCHAR,
			balance NUMERIC,
			voucher_id integer 
		);
		CREATE TEMPORARY TABLE temp_table4 (
		  entry_id integer,
		  balance NUMERIC,
			amount NUMERIC,
			voucher_id integer,
			account_id  integer 
		);
		CREATE TEMPORARY TABLE temp_table5 (
		  entry_id integer,
		  balance NUMERIC,
			amount NUMERIC,
			voucher_id integer,
			account_id  integer
		);
		CREATE TEMPORARY TABLE temp_table6 (
			entry_id NUMERIC,
			account_id integer, 
			v_voucherdate DATE,
			org_name VARCHAR,
			entry_number VARCHAR,
			account_name VARCHAR,
			item_names VARCHAR,
			entry_explain VARCHAR,
			entry_damount NUMERIC,
			entry_camount NUMERIC,
			direction VARCHAR,
			balance NUMERIC,
			voucher_id integer 
		);
		CREATE TEMPORARY TABLE temp_table0
    (
				account_id     INTEGER
    );
		INSERT INTO temp_table2 (entry_id, item_names) (
		SELECT
			adict.entry_id,
			string_agg ( adict.item_name :: TEXT, ',' ) AS item_names 
		FROM
		( SELECT 
			accountcore_entry_accountcore_item_rel.accountcore_entry_id AS entry_id,
			accountcore_item.NAME AS item_name 
		  FROM accountcore_entry_accountcore_item_rel 
		  LEFT JOIN accountcore_item 
			ON accountcore_entry_accountcore_item_rel.accountcore_item_id = accountcore_item.ID 
		  WHERE 
			CASE	
				WHEN item_ids IS NOT NULL THEN
				accountcore_entry_accountcore_item_rel.accountcore_item_id = ANY ( item_ids ) ELSE accountcore_entry_accountcore_item_rel.accountcore_item_id IS 				 NOT NULL 
			END 
	) AS adict 
GROUP BY
	adict.entry_id
		);
	IF (item_ids IS NULL) THEN
	INSERT INTO temp_table2 (entry_id, item_names) (
	SELECT id AS entry_id,NUll AS names 
	FROM accountcore_entry 
	WHERE id NOT IN (SELECT accountcore_entry_id FROM accountcore_entry_accountcore_item_rel ));
	ELSE
	INSERT INTO temp_table2 (entry_id, item_names) (
	SELECT id AS entry_id,NUll AS names 
	FROM accountcore_entry 
	WHERE id = 0);
	END IF;
	INSERT INTO temp_table1 ( entry_id, account_id, v_voucherdate, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance, voucher_id ) (
		SELECT
			accountcore_entry.id AS 编号,
			accountcore_account.id AS 科目id,
			accountcore_entry.v_voucherdate AS 记账日期,
			accountcore_org.NAME AS 机构名称,
			accountcore_entry.v_number AS 凭证号,
			accountcore_account.NAME AS 会计科目,
			accountcore_entry.EXPLAIN AS 摘要,
			accountcore_entry.damount AS 借方,
			accountcore_entry.camount AS 贷方,
		CASE

				WHEN accountcore_account.direction = '1' THEN
				'借' ELSE'贷' 
			END AS 方向,
		CASE
				when accountcore_account.direction = '1' then
											 ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount,0) +
												sum(accountcore_entry.damount) - sum(accountcore_entry.camount))
									 else ABS(COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount,0) +
												sum(accountcore_entry.camount) - sum(accountcore_entry.damount))
									 end                                                               as 余额,
			accountcore_voucher.id AS 凭证ID
		FROM
			accountcore_entry
			LEFT JOIN accountcore_account ON accountcore_entry.account = accountcore_account.
			ID LEFT JOIN accountcore_voucher ON accountcore_entry.voucher = accountcore_voucher.
			ID LEFT JOIN accountcore_org ON accountcore_voucher.org = accountcore_org.
			ID LEFT JOIN (
			SELECT
				account,
				begin_year_amount,
				"kj_create_date" 
			FROM
				accountcore_accounts_balance 
			WHERE
			kj_create_date between start_dt and end_dt AND
				isbegining = TRUE 
			AND
			CASE

					WHEN account_ids IS NOT NULL THEN
					account = ANY ( account_ids ) ELSE account IS NOT NULL 
				END 
				AND
				CASE

						WHEN org_ids IS NOT NULL THEN
						org = ANY ( org_ids ) ELSE org IS NOT NULL 
					END 
					ORDER BY
						"kj_create_date" DESC 
					) aab ON aab.account = accountcore_entry.account
					LEFT JOIN (
					SELECT
						ae1.account,
						SUM ( ae1.damount ) - SUM ( ae1.camount ) AS d_amount,
						SUM ( ae1.camount ) - SUM ( ae1.damount ) AS c_amount 
					FROM
						accountcore_entry ae1
						LEFT JOIN accountcore_account aa1 ON ae1.account = aa1.ID 
					WHERE
						v_voucherdate < start_dt 
					AND
					CASE

							WHEN account_ids IS NOT NULL THEN
							ae1.account = ANY ( account_ids ) ELSE ae1.account IS NOT NULL 
						END 
						AND
						CASE

								WHEN org_ids IS NOT NULL THEN
								ae1.org = ANY ( org_ids ) ELSE ae1.org IS NOT NULL 
							END 
							GROUP BY
								ae1.account 
							) AS ae ON ae.account = accountcore_entry.account 
						WHERE
							v_voucherdate BETWEEN start_dt 
							AND end_dt 
						AND
						CASE

								WHEN account_ids IS NOT NULL THEN
								accountcore_entry.account = ANY ( account_ids ) ELSE accountcore_entry.account IS NOT NULL 
							END 
							AND
							CASE

									WHEN org_ids IS NOT NULL THEN
									accountcore_entry.org = ANY ( org_ids ) ELSE accountcore_entry.org IS NOT NULL 
								END 
								group by ae.d_amount, ae.c_amount,accountcore_account.id,accountcore_entry.v_voucherdate,
                                               aab.begin_year_amount,accountcore_entry.v_number,
																							 accountcore_entry.explain,accountcore_entry.damount,
																							 accountcore_entry.camount,accountcore_voucher.id,accountcore_entry.id,
																							 accountcore_org.name);
							INSERT INTO temp_table1 ( v_voucherdate,account_id, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance ) (
								SELECT NULL
									,
									NULL,
									NULL,
									NULL,
									NULL,
									NULL,
									SUM ( damount ),
									SUM ( camount ),
									NULL,
								NULL 
								FROM
									accountcore_entry 
								WHERE
									v_voucherdate BETWEEN start_dt 
									AND end_dt 
								);
							INSERT INTO temp_table1 ( v_voucherdate,account_id, org_name, entry_number, account_name, entry_explain, entry_damount, entry_camount, direction, balance ) (
								SELECT NULL
									,
									NULL,
									NULL,
									NULL,
									NULL,
									NULL,
									SUM ( damount ),
									SUM ( camount ),
									NULL,
								NULL 
								FROM
									accountcore_entry 
								WHERE
									v_voucherdate BETWEEN date_trunc( 'year', now( ) ) 
									AND date_trunc( 'year', now( ) ) + INTERVAL '1 year' - INTERVAL '1 day' 
								);


		INSERT INTO temp_table3 (
				 entry_id,account_id, v_voucherdate, org_name, entry_number, account_name, item_names, entry_explain, entry_damount, entry_camount, direction, balance, voucher_id)(select
						 temp_table1.entry_id,
						 temp_table1.account_id,
					   temp_table1.v_voucherdate,
						 temp_table1.org_name,
						 temp_table1.entry_number,
						 temp_table1.account_name,
						 temp_table2.item_names,
						 temp_table1.entry_explain,
						 COALESCE ( temp_table1.entry_damount, 0 ),
						 COALESCE ( temp_table1.entry_camount, 0 ),
						 temp_table1.direction,
						 COALESCE ( temp_table1.balance, 0 ),
						 temp_table1.voucher_id
						 FROM
								temp_table2 
								LEFT JOIN temp_table1 ON temp_table1.entry_id = temp_table2.entry_id
							WHERE temp_table1.account_name IS NOT NULL);	

	INSERT INTO temp_table4 (
				 entry_id,
				 balance,
				 amount,
				 voucher_id,
				 account_id)(select
				     temp_table3.entry_id,
						 COALESCE ( temp_table3.balance, 0 ),
						 case when temp_table3.direction = '借' then COALESCE (temp_table3.entry_damount,0) - COALESCE(temp_table3.entry_camount,0) else COALESCE(temp_table3.entry_camount,0) - COALESCE(temp_table3.entry_damount,0) end,
						 temp_table3.voucher_id,
						 temp_table3.account_id
						from temp_table3
						order by voucher_id);



	INSERT INTO temp_table5 (
				 entry_id,
				 balance,
				 amount,
				 voucher_id,
				 account_id)(
				 SELECT a.entry_id,(SELECT (case when (a.balance + sum(b.amount))>0 then (a.balance + sum(b.amount)) else a.balance end) FROM temp_table4 b WHERE b.voucher_id < a.voucher_id and b.account_id = a.account_id),
				            COALESCE(a.amount,0),a.voucher_id,a.account_id
						FROM temp_table4 a
						order by voucher_id);

	INSERT INTO temp_table6 (entry_id, account_id, v_voucherdate, org_name, entry_number, account_name, item_names, entry_explain, entry_damount, entry_camount, direction, balance, voucher_id)(select
	                                        temp_table3.entry_id,
						                              temp_table3.account_id,
                                          temp_table3.v_voucherdate,
																					temp_table3.org_name,
																					temp_table3.entry_number,
																					temp_table3.account_name,
																					temp_table3.item_names,
																					temp_table3.entry_explain,
																					COALESCE(temp_table3.entry_damount, 0),
																					COALESCE(temp_table3.entry_camount, 0),
																					temp_table3.direction,
																					COALESCE(temp_table5.balance, 0),
																					temp_table3.voucher_id
                                      from temp_table3 
																			LEFT JOIN temp_table5 ON temp_table3.entry_id = temp_table5.entry_id
																			ORDER BY temp_table3.account_name, temp_table3.voucher_id);	
		IF (account_ids IS NULL) THEN
		INSERT INTO temp_table0 (account_id
															)(select id
															from accountcore_account);
		ELSE
		INSERT INTO temp_table6 (account_id,
														 account_name,
														 entry_explain,
														 balance,
														 direction,
														 voucher_id
														)(select 
														accountcore_account.id,
														accountcore_account.name,
														'期初余额',
														case when accountcore_account.direction = '1' then
															 COALESCE(aab.begin_year_amount, 0) + coalesce(ae.d_amount, 0)
													  else COALESCE(aab.begin_year_amount, 0) + coalesce(ae.c_amount, 0) end,
														case when accountcore_account.direction = '1' then '借' else '贷' end,
														0
														from accountcore_account
														LEFT JOIN (select *
																			from accountcore_entry
																			where v_voucherdate between start_dt and end_dt
																				and case
																								when account_ids is not null
																										then accountcore_entry.account = ANY (select account_id from temp_table0)
																								else accountcore_entry.account is not null end
																				and case
																								when org_ids is not null
																										then accountcore_entry.org = ANY (org_ids)
																								else accountcore_entry.org is not null end) accountcore_entry
																		 on accountcore_entry.account = accountcore_account.id
														LEFT JOIN (select account, begin_year_amount, "kj_create_date"
                                                          from accountcore_accounts_balance
                                                          where isbegining = True
                                                            and case
                                                                    when account_ids is not null
                                                                        then account = ANY (account_ids)
                                                                    else account is not null end
                                                            and case
                                                                    when org_ids is not null then org = ANY (org_ids)
                                                                    else org is not null end
                                                          order by "kj_create_date" desc) aab
                                                         on aab.account = accountcore_account.id
													  LEFT JOIN (select ae1.account,
																			 sum(ae1.damount) -
																			 sum(ae1.camount) as d_amount,
																			 sum(ae1.camount) -
																			 sum(ae1.damount) as c_amount
																from accountcore_entry ae1
																				 LEFT JOIN accountcore_account aa1 on ae1.account = aa1.id
																where v_voucherdate < start_dt
																	and case
																					when account_ids is not null
																							then ae1.account = any (account_ids)
																					else ae1.account is not null end
																	and case
																					when org_ids is not null
																							then ae1.org = any (org_ids)
																					else ae1.org is not null end
																group by ae1.account) as ae
															 on ae.account = accountcore_account.id
														where accountcore_account.id = any (account_ids));

		END IF;															

-- Routine body goes here...

				RETURN QUERY 
							SELECT
							( temp_table6.v_voucherdate ) AS 记账日期,
							( temp_table6.org_name ) AS 机构名称,
							( temp_table6.entry_number ) AS 凭证号,
							( temp_table6.account_name ) AS 会计科目,
							( temp_table6.item_names ) AS 核算项目,
							( temp_table6.entry_explain ) AS 摘要,
							COALESCE ( temp_table6.entry_damount, 0 ) AS 借方,
							COALESCE ( temp_table6.entry_camount, 0 ) AS 贷方,
							( temp_table6.direction ) AS 方向,
							COALESCE ( temp_table6.balance, 0 ) AS 余额,
							( temp_table6.voucher_id ) AS 凭证ID
							FROM
								temp_table6
							ORDER BY temp_table6.account_name, temp_table6.voucher_id;
							DROP TABLE
							IF
								EXISTS temp_table1;
							DROP TABLE
							IF
								EXISTS temp_table2;
							DROP TABLE
							IF
								EXISTS temp_table3;
							DROP TABLE
							IF
								EXISTS temp_table4;
							DROP TABLE
							IF
								EXISTS temp_table5;
							DROP TABLE
							IF
								EXISTS temp_table6;
							DROP TABLE
							IF
								EXISTS temp_table0;



END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000''')