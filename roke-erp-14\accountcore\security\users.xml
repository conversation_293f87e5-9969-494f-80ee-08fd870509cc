<odoo>
    <data noupdate="1">
        <!-- 预设用户 -->
        <record id="accounter_1" model="res.users">
            <field name="name">李会计</field>
            <field name="login"><EMAIL></field>
            <field name="password">666666</field>
        </record>
        <!-- <record id='accounter_2' model='res.users'>
            <field name='name' >user2</field>
            <field name='login' >user2</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_3' model='res.users'>
            <field name='name' >user3</field>
            <field name='login' >user3</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_4' model='res.users'>
            <field name='name' >user4</field>
            <field name='login' >user4</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_5' model='res.users'>
            <field name='name' >user5</field>
            <field name='login' >user5</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_6' model='res.users'>
            <field name='name' >user6</field>
            <field name='login' >user6</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_7' model='res.users'>
            <field name='name' >user7</field>
            <field name='login' >user7</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_8' model='res.users'>
            <field name='name' >user8</field>
            <field name='login' >user8</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_9' model='res.users'>
            <field name='name' >user9</field>
            <field name='login' >user9</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_10' model='res.users'>
            <field name='name' >user10</field>
            <field name='login' >user10</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_11' model='res.users'>
            <field name='name' >user11</field>
            <field name='login' >user11</field>
            <field name='password' >666666</field>
        </record>
        <record id='accounter_12' model='res.users'>
            <field name='name' >user12</field>
            <field name='login' >user12</field>
            <field name='password' >666666</field>
        </record> -->
    </data>
    <data noupdate="1">
        <!-- 预设用户-结束 -->
        <!-- 创建用户组类别-开始 -->
        <record model="ir.module.category" id="accountcore_group_default">
            <field name="name">AccountCore总账</field>
            <field name="sequence">1</field>
        </record>
        <!-- 创建用户组类别-结束 -->
        <!-- 预设用户添加进“AccountCore总账类别”-开始 -->
        <record id="group_role_ac" model="res.groups">
            <field name="name">通用会计组(预设)</field>
            <field name="category_id" ref="accountcore_group_default" />
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]" />
            <field name="users" eval="[(6,0,[ref('accounter_1')])]" />
            <!-- <field name="users" eval="[(6,0,[ref('accounter_1'),ref('accounter_2'),ref('accounter_3'),ref('accounter_4'),ref('accounter_5'),ref('accounter_6'),ref('accounter_7'),ref('accounter_8'),ref('accounter_9'),ref('accounter_10'),ref('accounter_11'),ref('accounter_12')])]" /> -->
        </record>
        <record id="group_role_search" model="res.groups">
            <field name="name">只查询组(预设)</field>
            <field name="category_id" ref="accountcore_group_default" />
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]" />
        </record>
        <!-- 预设用户添加进“AccountCore总账类别”-开始 -->
    </data>
</odoo>