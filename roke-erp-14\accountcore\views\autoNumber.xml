<odoo>
    <!-- odoo自动编号 -->
    <!-- 记账凭证唯一编号 -->
    <data noupdate='True'>
        <record id='voucher_unique_number' model='ir.sequence'>
            <field name='name'>记账凭证唯一号</field>
            <field name='code'>voucher.uniqueNumber</field>
            <field name='prefix'>V</field>
            <field name='suffix'></field>
            <field name='padding'>8</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
    <!-- 核算项目唯一编号 -->
    <data noupdate='True'>
        <record id='item_unique_number' model='ir.sequence'>
            <field name='name'>核算项目唯一号</field>
            <field name='code'>item.uniqueNumber</field>
            <field name='prefix'>I</field>
            <field name='suffix'></field>
            <field name='padding'>8</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
    <data noupdate="True">
        <record id="accountcore_voucher_sequence" model="ir.sequence">
            <field name="name">凭证号</field>
            <field name="code">accountcore.voucher.code</field>
            <field name="prefix"></field>
            <field name="padding">4</field>
            <field name="company_id" eval="False"/>
            <field eval="True" name="use_date_range"/>
        </record>
    </data>
</odoo>