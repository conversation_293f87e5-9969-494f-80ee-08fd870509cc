<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- 主菜单 -->
        <menuitem name='总账管理' id='accountcore_menu' web_icon="accountcore,static/description/main_menu.png"
                  sequence="90"/>

        <!--菜单重构-->
        <!--初始 -->
        <menuitem name='初始' id='accountcore_begining_menu' parent='accountcore_menu' sequence="10"
                  groups="base.group_system"/>
        <menuitem name="科目初始余额" id='accountcore_begining_balance_menu' parent='accountcore_begining_menu'
                  sequence="10" action='accountcore_accounts_balance_actions_window' groups="base.group_system"/>
        <menuitem name="下载导入模板" id='accountcore_down_begin_model_menu' parent='accountcore_begining_menu'
                  sequence="20" action='accountcore_down_begin_model_action_url' groups="base.group_system"/>
        <menuitem name="导入初始余额" id='accountcore_import_begin_menu' parent='accountcore_begining_menu'
                  sequence="30" action='accountcore_import_begin_action' groups="base.group_system"/>

        <!--凭证 -->
        <menuitem name='凭证' id='account_invoice' parent='accountcore_menu' sequence="20" groups="base.group_system"/>
        <menuitem name='制单' id='accountcore_voucher_new_menu' parent='account_invoice'
                  action='accountore_voucher_new_actions_window' sequence='10' groups="base.group_system"/>
        <menuitem name='复核' id='accountcore_vouchers_menu_fh' parent='account_invoice'
                  action='accountcore_vouchers_action_server_fh' sequence="20"/>
        <menuitem name='凭证列表' id='accountcore_vouchers_menu' parent='account_invoice'
                  action='accountcore_vouchers_action_server' sequence="30"/>
        <menuitem name='凭证分录' id='accountcore_entrys_menu' parent='account_invoice'
                  action='accountcore_entrys_action_server' sequence="40"/>

        <!--账表-->
        <menuitem name='账表' id='account_inventory' parent='accountcore_menu' sequence="30"
                  groups="base.group_system"/>
        <!--        <menuitem name='科目余额表' id='accountcore_accountsBalance_menu' parent='account_inventory'-->
        <!--                  action='accountcore_get_accounts_balance_actions_window' sequence="10" groups="base.group_system"/>-->
        <!--        <menuitem name='总账' id='accountcore_subsidiary_book_menu' parent='account_inventory'-->
        <!--                  action='accountcore_get_subsidiary_book_window_2' sequence="20" groups="base.group_system"/>-->
        <!--        <menuitem name='明细账' id='accountcore_subsidiary_book_detail_menu' parent='account_inventory'-->
        <!--                  action='accountcore_get_subsidiary_book_window' sequence="30" groups="base.group_system"/>-->
        <!--        <menuitem name='核算项目余额帐' id='accountcore_subsidiary_book_menu11' parent='account_inventory'-->
        <!--                  action='accountcore_get_subsidiary_book_window_22' sequence="40" groups="base.group_system"/>-->
        <!--        <menuitem name='核算项目明细帐' id='accountcore_subsidiary_book_detail_menu22' parent='account_inventory'-->
        <!--                  action='accountcore_get_subsidiary_book_window11' sequence="50" groups="base.group_system"/>-->

        <!--月末-->
        <menuitem name='月末' id='account_month_end' parent='accountcore_menu' sequence="40"
                  groups="base.group_system"/>
        <menuitem name='结转损益' id='accountcore_voucher_sunyi_menu' parent='account_month_end'
                  action='accountore_voucher_shunyi_actions_window' sequence='10' groups="base.group_system"/>
<!--        <menuitem name='平衡检查' id='accountcore_check_menu' parent='account_month_end'-->
<!--                  action='accountcore_begin_balance_check_actions_window_2' sequence='20' groups="base.group_system"/>-->
        <!--TODO 检查缺号-->

        <!--财务报表-->
        <menuitem name='财务报表' id='account_report' parent='accountcore_menu' sequence="50"
                  groups="base.group_system"/>
        <menuitem name='报表类型' id='accountcore_report_type_menu' parent='account_report'
                  action='accountcore_report_type_actions_window' sequence="10" groups="base.group_system"/>
        <menuitem name='报表模板' id='accountcore_report_model_menu' parent='account_report'
                  action='accountcore_report_model_actions_window' sequence="20" groups="base.group_system"/>
        <menuitem name='报表生成' id='accountcore_get_report_menu' parent='account_report'
                  action='accountcore_get_report_actions_window' sequence="30" groups="base.group_system"/>
        <menuitem name='已归档报表' id='accountcore_storage_report_menu' parent='account_report'
                  action='accountcore_storage_report_actions_window' sequence="40" groups="base.group_system"/>

        <!--总账设置-->
        <menuitem name='总账设置' id='account_settings' parent='accountcore_menu' sequence="60"
                  groups="base.group_system"/>
        <menuitem name='核算机构' id='accountcore_org_menu' parent='account_settings'
                  action='accountcore_org_action_window' sequence="10" groups="base.group_system"/>
        <menuitem name='科目体系' id='accountcore_accountarch_menu' parent='account_settings' sequence="30"
                  action='accountcore_accountsarch_action_window' groups="base.group_system"/>
        <menuitem name='科目类别' id='acctountcore_accountclass_menu' parent='account_settings' sequence="40"
                  action='accountcore_accountclass_action_window' groups="base.group_system"/>
        <menuitem name='科目设置' id='accountcore_accounts_menu' parent='account_settings' sequence="50"
                  action='accountcore_account_action_window' groups="base.group_system"/>
        <menuitem name='核算项目类别' id='accountcore_itemclass_menu' parent='account_settings' sequence="60"
                  action='accountcore_itemclass_action_window' groups="base.group_system"/>
        <menuitem name='特殊科目' id='accountcore_special_accounts_menu' parent='account_settings' sequence="80"
                  action='accountcore_special_accounts_actions_window' groups="base.group_system"/>
        <menuitem name='凭证默认值' id='accountcore_serdefaults_menu' parent='account_settings'
                  action='accountcore_userdefaults_action' sequence="90" groups="base.group_system"/>
        <menuitem name='凭证策略' id='accountcore_voucher_number_tastics_menu' parent='account_settings'
                  action='accountcore_voucher_number_acionts_window' sequence='100' groups="base.group_system"/>
        <menuitem name='切换策略' id='accountcore_set_number_tastics_menu' parent='account_settings'
                  action='accountcore_set_number_acionts_window' sequence='110' groups="base.group_system"/>
        <menuitem name='管理核算项目' id='accountcore_items_menu' parent='account_settings'
                  action='accountcore_items_action_window' sequence="120" groups="base.group_system"/>

        <!--使用向导-->
        <menuitem name='使用向导' id='accountcore_guide_menu' parent='accountcore_menu'
                  action='accountcore_org_action_window' sequence="70" groups="base.group_user"/>
        <menuitem name='第1步:添加核算机构' id='accountcore_guide_menu_1' parent='accountcore_guide_menu'
                  action='accountcore_org_action_window' sequence="10" groups="base.group_system"/>
        <menuitem name='第2步:添加核算项目类别' id='accountcore_guide_menu_2' parent='accountcore_guide_menu'
                  action='accountcore_itemclass_action_window' sequence="20" groups="base.group_system"/>
        <menuitem name='第3步:添加核算项目' id='accountcore_guide_menu_3' parent='accountcore_guide_menu'
                  action='accountcore_items_action_window' sequence="30" groups="base.group_system"/>
        <menuitem name='第4步:完善会计科目' id='accountcore_guide_menu_4' parent='accountcore_guide_menu'
                  action='accountcore_account_action_window' sequence="40" groups="base.group_system"/>
        <menuitem name='第5步:录入期初余额' id='accountcore_guide_menu_5' parent='accountcore_guide_menu'
                  action='accountcore_accounts_balance_actions_window' sequence="50" groups="base.group_system"/>
        <menuitem name='第6步:设置凭证默认值' id='accountcore_guide_menu_6' parent='accountcore_guide_menu'
                  action='accountcore_userdefaults_action' sequence="60"/>
        <menuitem name='第7步:设置凭证编号策略' id='accountcore_guide_menu_7' parent='accountcore_guide_menu'
                  action='accountcore_set_number_acionts_window' sequence="70" groups="base.group_system"/>
        <menuitem name='第8步:可以开始录入凭证了' id='accountcore_guide_menu_8' parent='accountcore_guide_menu'
                  action='accountcore_vouchers_action_window' sequence="80" groups="base.group_system"/>

        <!--扩展-->
        <menuitem name='扩展' id='account_extra' parent='accountcore_menu' sequence="80"
                  groups="base.group_system"/>
        <menuitem name='科目余额调整' id='accountcore_accounts_balance__menu' parent='account_extra' sequence="30"
                  action='accountcore_accounts_balance_window' groups="base.group_system"/>
        <menuitem name='全局标签类别' id='accountcore_glob_tag_class_menu' parent='account_extra' sequence="40"
                  action='accountcore_glob_tag_class_actions_window' groups="base.group_system"/>
        <menuitem name='全局标签设置' id='accountcore_globs_tag_menu' parent='account_extra' sequence="50"
                  action='accountcore_glob_tag_actions_window' groups="base.group_system"/>
    </data>
</odoo>