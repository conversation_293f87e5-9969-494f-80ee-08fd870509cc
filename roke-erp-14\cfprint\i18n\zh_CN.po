# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* cfprint
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e-20190726\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-18 01:45+0000\n"
"PO-Revision-Date: 2019-09-18 01:45+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: cfprint
#: code:addons/cfprint/ir/ir_qweb/fields.py:381
#, python-format
msgid "A unit must be provided to duration widgets"
msgstr "持续时间小部件必须要有单位"

#. module: cfprint
#: model:ir.module.category,name:cfprint.module_cfprint
#: model:ir.ui.menu,name:cfprint.menu_cf_root
msgid "CFPrint"
msgstr "打印设置"

#. module: cfprint
#: model:ir.actions.act_window,name:cfprint.action_cf_template_category
msgid "CFPrint Template Category Management"
msgstr "模板类别管理"

#. module: cfprint
#: model:ir.actions.act_window,name:cfprint.action_cf_template
msgid "CFPrint Template Management"
msgstr "模板管理"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_list
msgid "CFPrint Templates"
msgstr "康虎云报表模板"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_category_list
msgid "CFPrint Templates Category"
msgstr "康虎云报表模板类别"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__category_id
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__category_id
msgid "Category"
msgstr "类别"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__name
msgid "Category Name"
msgstr "类别名称"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__template
msgid "Content"
msgstr "内容"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_template__template
#: model:ir.model.fields,help:cfprint.field_cf_template_history__template
msgid "Content of template"
msgstr "模板内容"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__create_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template__create_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__create_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__create_uid
msgid "Created by"
msgstr "创建人"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__create_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template__create_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__create_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__create_date
msgid "Created on"
msgstr "创建时间"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__description
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__description
msgid "Description"
msgstr "说明"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_category_form
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "Detail of template"
msgstr "模板详情"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__display_name
#: model:ir.model.fields,field_description:cfprint.field_cf_template__display_name
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__display_name
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: cfprint
#: model:ir.ui.menu,name:cfprint.menu_cf_download_cfprint
msgid "Download CFPrint"
msgstr "下载打印工具"

#. module: cfprint
#: code:addons/cfprint/ir/ir_qweb/fields.py:378
#, python-format
msgid "Durations can't be negative"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__templ_histories
msgid "History"
msgstr "历史"

#. module: cfprint
#: code:addons/cfprint/models/cf_templates.py:227
#: model:ir.model,name:cfprint.model_cf_template_history
#, python-format
msgid "History of report templates of CFPrint"
msgstr "模板历史版本"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_template__templ_histories
msgid "History of template"
msgstr "模板历史版本"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__id
#: model:ir.model.fields,field_description:cfprint.field_cf_template__id
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__id
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__id
msgid "ID"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping____last_update
#: model:ir.model.fields,field_description:cfprint.field_cf_template____last_update
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category____last_update
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history____last_update
msgid "Last Modified on"
msgstr "最后修改日"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__write_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template__write_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__write_uid
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__write_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template__write_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__write_date
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "Manage CFPrint Template"
msgstr "管理康虎云报表模板"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_category_form
msgid "Manage CFPrint Template Category"
msgstr "管理康虎云报表模板类别"

#. module: cfprint
#: model:res.groups,name:cfprint.cfprint_group_manager
msgid "Manager"
msgstr "经理"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__name
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__name
msgid "Name"
msgstr "名称"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__origin
msgid "Origin Template"
msgstr "原始模板"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_template__preview_img
#: model:ir.model.fields,help:cfprint.field_cf_template_history__preview_img
msgid "Picture used to preview a report"
msgstr "预览报表样式的图片"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__preview_img
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__preview_img
msgid "Preview image"
msgstr "预览图片"

#. module: cfprint
#: model:ir.ui.menu,name:cfprint.menu_cf_prn_server_ip
#: model_terms:ir.ui.view,arch_db:cfprint.cf_print_server_mapping_form
msgid "Print Server IP"
msgstr "打印服务器地址"

#. module: cfprint
#: code:addons/cfprint/models/cfprint_server_user_map.py:27
#: model:ir.model,name:cfprint.model_cf_print_server_user_mapping
#, python-format
msgid "Print Server and User Mapping"
msgstr "用户使用的打印服务器配置"

#. module: cfprint
#: model:ir.model,name:cfprint.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: cfprint
#: model:ir.model,name:cfprint.model_ir_actions_report
msgid "Report Action"
msgstr "报告动作"

#. module: cfprint
#: code:addons/cfprint/models/cf_templates.py:159
#: model:ir.model,name:cfprint.model_cf_template_category
#, python-format
msgid "Report templates category of CFPrint"
msgstr "康虎云报表模板类别"

#. module: cfprint
#: code:addons/cfprint/models/cf_templates.py:174
#: model:ir.model,name:cfprint.model_cf_template
#, python-format
msgid "Report templates of CFPrint"
msgstr "康虎云报表模板"

#. module: cfprint
#: sql_constraint:cf.template.history:0
msgid "Same version and template ID already exists!"
msgstr "相同版本的同一模板已存在"

#. module: cfprint
#: model:ir.ui.menu,name:cfprint.menu_cf_cfprint_conf
msgid "Setting"
msgstr "设置"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__template
msgid "Template"
msgstr "打印模板"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__template_filename
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__template_filename
msgid "Template Filename"
msgstr "模板名称"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template__templ_id
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__templ_id
msgid "Template ID"
msgstr "模板ID"

#. module: cfprint
#: sql_constraint:cf.template:0
msgid "Template ID already exists!"
msgstr "模板ID已存在"

#. module: cfprint
#: sql_constraint:cf.template.category:0
msgid "Template category name already exists!"
msgstr "模板类别已存在。"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template_category__lines
#: model:ir.ui.menu,name:cfprint.menu_cf_template
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_category_form
msgid "Templates"
msgstr "打印模板"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_template__templ_id
#: model:ir.model.fields,help:cfprint.field_cf_template_history__templ_id
msgid "Unique ID of template"
msgstr "模板唯一ID"

#. module: cfprint
#: model:res.groups,name:cfprint.cfprint_group_user
msgid "User"
msgstr "用户"

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_template_history__ver
msgid "Version"
msgstr "版本"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "Version History"
msgstr "历史版本"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_template_history__ver
msgid "Version of template"
msgstr "模板版本"

#. module: cfprint
#: model:ir.actions.act_url,name:cfprint.act_cf_download_cfprint
msgid "下载康虎云报表"
msgstr "下载康虎云报表"

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_print_server_user_mapping__user_id
msgid "关联的用户"
msgstr "关联的用户"

#. module: cfprint
#: model:res.groups,comment:cfprint.cfprint_group_user
msgid "康虎云报表普通用户"
msgstr "康虎云报表普通用户"

#. module: cfprint
#: model:res.groups,comment:cfprint.cfprint_group_manager
msgid "康虎云报表管理员"
msgstr "康虎云报表管理员"

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_print_server_mapping_form
msgid "当从移动设备（Android或iOS）等无法运行康虎云报表打印伺服器的设备上打印时，需要在配备一台Windows电脑运行康虎云报表打印伺服器，然后从移动设备上向该Windows电脑发送打印任务。<br/>\n"
"                                在上面的“打印服务器IP”中填入Windows电脑的IP地址和“打印服务器端口”填入打印伺服器监听端口（默认则不需要修改)即可。"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_print_server_user_mapping__prn_server_ip
msgid "当前用户使用的打印服务器的IP地址。如果不需要发送到别的电脑上打印，请勿修改！"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,help:cfprint.field_cf_print_server_user_mapping__prn_server_port
msgid "当前用户使用的打印服务器的监听端口。如果不需要发送到别的电脑上打印，请勿修改！"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__prn_server_ip
msgid "打印服务器IP"
msgstr ""

#. module: cfprint
#: code:addons/cfprint/models/cfprint_server_user_map.py:44
#: model:ir.actions.server,name:cfprint.act_cf_print_svr_ip
#, python-format
msgid "打印服务器地址"
msgstr ""

#. module: cfprint
#: code:addons/cfprint/models/cfprint_server_user_map.py:61
#: code:addons/cfprint/models/cfprint_server_user_map.py:71
#, python-format
msgid "打印服务器的IP地址错误，请确认！"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__prn_server_port
msgid "打印服务器端口"
msgstr ""

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "模板中文名称"
msgstr ""

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "模板唯一标识，不允许中文和特殊字符"
msgstr ""

#. module: cfprint
#: model:ir.model.fields,field_description:cfprint.field_cf_print_server_user_mapping__user_id
msgid "用户"
msgstr ""

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "请输入模板详细说明"
msgstr ""

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_category_form
msgid "输入分类名称"
msgstr ""

#. module: cfprint
#: model_terms:ir.ui.view,arch_db:cfprint.cf_template_form
msgid "选择模板分类"
msgstr ""

