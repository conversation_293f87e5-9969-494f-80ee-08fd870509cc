/**
 * CFPrint打印类
 * ver 1.3
 * 康虎软件工作室
 * Email: <EMAIL>
 * QQ: 360026606
 * 微信: 360026606
 */
if(typeof(String.prototype.removeHTMLTag)=="undefined")String.prototype.removeHTMLTag=function(){return this.replace(/<.+?>([^<>]+?)<.+?>/g,"$1")};(function(_,$){var A=$(_);if(typeof define==="function"&&define.amd)define([],$);else if(typeof module!=="undefined"&&module.exports)module.exports=$();else _.ws=$()}(this,function(C,H){if(!("WebSocket"in window))return;var F={},D,$=document.createElement("div"),_={automaticOpen:true,reconnectDecay:1.5,host:"127.0.0.1",port:54321,protocols:"ws"},B=function($){switch($.type){case"connecting":console.log("Connecting to cfprint.",$);break;case"open":console.log("Connected to cfprint.",$);break;case"close":console.log("Disconnected from cfprint.",$);break;case"message":console.log("Got a message from cfprint: "+$.data,$);break;case"error":console.log("A error occured: "+$.data,$);break;default:console.log("EVENT: ",$)}},E={CONNECTING:WebSocket.CONNECTING,OPEN:WebSocket.OPEN,CLOSING:WebSocket.CLOSING,CLOSED:WebSocket.CLOSED};function G(C,B,D){var F=this;this.ver=1.3;if(!D)D={};for(var G in _)if(typeof D[G]!=="undefined")this[G]=D[G];else this[G]=_[G];this.host=C||this.host;this.port=B||this.port;this.protocol=this.protocols;this.url=this.protocol+"://"+this.host+":"+this.port;this.readyState=WebSocket.CONNECTING;for(var A in E)this[A]=E[A];$.addEventListener("connecting",function($){F!==window&&F.onconnecting($)});$.addEventListener("open",function($){F!==window&&F.onopen($)});$.addEventListener("message",function($){F!==window&&F.onmessage($)});$.addEventListener("close",function($){F!==window&&F.onclose($)});$.addEventListener("error",function($){F!==window&&F.onerror($)});this.addEventListener=$.addEventListener.bind($);this.removeEventListener=$.removeEventListener.bind($);this.dispatchEvent=$.dispatchEvent.bind($);if(this.automaticOpen===true&&this!==window)this.open();return this}function A(A,$){var _=document.createEvent("CustomEvent");_.initCustomEvent(A,false,false,$);return _}G.prototype.onconnecting=B;G.prototype.onopen=B;G.prototype.onmessage=B;G.prototype.onclose=B;G.prototype.onerror=B;G.prototype.send=function(B){if(this.wsocket&&this.wsocket.readyState===1){var C="";if(typeof(B)==="string")C=B;else if(typeof(B)==="object")C=JSON.stringify(B,null,2);else{var _=A("error",{"message":"Invalid data format, only json string or json object is allowed."});$.dispatchEvent(_);return}try{this.wsocket.send(C)}catch(D){_=A("error",{"message":D.message});$.dispatchEvent(_)}}else{_=A("error",{"message":"Not connected to cfprint server or disconnected."});$.dispatchEvent(_)}};G.prototype.close=function(_,$){if(typeof _==="undefined")_=1000;if(this.wsocket)this.wsocket.close(_,$)};G.prototype.open=function(){var _=this;D=new WebSocket(this.url,this.protocol||[]);$.dispatchEvent(A("connecting"));D.onopen=function(C){_.protocol=G.protocol;_.readyState=WebSocket.OPEN;var B=A("open");$.dispatchEvent(B)};D.onclose=function(C){_.readyState=WebSocket.CLOSED;var B=A("connecting");B.code=C.code;B.reason=C.reason;B.wasClean=C.wasClean;$.dispatchEvent(B);$.dispatchEvent(A("close"))};D.onmessage=function(B){var _=A("message");_.data=B.data;$.dispatchEvent(_)};D.onerror=function(B){var _=A("error");$.dispatchEvent(_)};this.wsocket=D;return this};G.prototype.state=function(){if(this.wsocket)return this.wsocket.readyState;else return WebSocket.CLOSED};G.prototype.log=function(A,B){var C=this.output||"output",$=document.getElementById(C);if($&&$.appendChild){var _=document.createElement("p");_.style.wordWrap="break-word";_.style.padding="8px";_.style.background="#eee";_.innerHTML="LOG: "+A;$.appendChild(_)}B?console.log(A,B):console.log(A)};return G}))