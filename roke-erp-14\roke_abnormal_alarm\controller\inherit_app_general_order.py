# -*- coding: utf-8 -*-
"""
Description:
    通用单据配置接口
Versions:
    Created by www.rokedata.com
"""
import math
from datetime import timedelta
from odoo import models, fields, http, SUPERUSER_ID, api, _
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)
import requests, json
from datetime import date, datetime
import time, re, sys, os
import jinja2
import qrcode
import io
import base64
from odoo.addons.roke_mes_client.controller.app_general_order import Mobile

if hasattr(sys, 'frozen'):
    # When running on compiled windows binary, we don't have access to package loader.
    path = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'views'))
    loader = jinja2.FileSystemLoader(path)
else:
    loader = jinja2.PackageLoader('odoo.addons.roke_mes_client', "views")

env = jinja2.Environment(loader=loader, autoescape=True)
env.filters["json"] = json.dumps


class InheritMobile(Mobile):

    # 安灯异常状态
    def process_abnormal_alarm(self, order_id):
        """
        单独安灯异常状态
        :return:
        """
        if order_id:
            work_id = http.request.env['roke.abnormal.alarm'].browse(int(order_id))
            next_states = http.request.env(user=SUPERUSER_ID)['roke.abnormal.alarm.state'].search([('id', '>', work_id.state_id.id)], order="id")
            if next_states:
                return {
                    'is_show_alarm': True,
                    'data': {'state_id': next_states[0].id},
                    'show_button_name': work_id.state_id.name + '收到'
                }
            else:
                return {
                    'is_show_alarm': False,
                    'data': {},
                    'show_button_name': ''
                }
        else:
            return {
                'is_show_alarm': False,
                'data': {},
                'show_button_name': ''
            }

    # 获取单据详情接口
    @http.route('/roke/get_general_order_detail', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_general_order_detail(self):
        model_index = http.request.jsonrequest.get('model_index', "")
        order_id = http.request.jsonrequest.get('order_id')
        res = super(InheritMobile, self).get_general_order_detail()
        # 安灯异常状态（单独处理）
        if model_index == 'roke.abnormal.alarm':
            res.update({'alarm_order_state': self.process_abnormal_alarm(int(order_id))})
        return res
