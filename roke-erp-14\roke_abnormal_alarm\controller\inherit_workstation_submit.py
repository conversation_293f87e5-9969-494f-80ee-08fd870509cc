# -*- coding: utf-8 -*-
"""
Description:
    工控机报告异常
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import datetime
import logging
from odoo import fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_production.controller.workstation_submit import WorkstationSubmit
_logger = logging.getLogger(__name__)


class InheritWorkstationSubmit(WorkstationSubmit):

    @http.route("/roke/workstation/alarm_submit", type="json", methods=["POST", "OPTIONS"], auth="none", csrf=False, cors="*")
    def workstation_alarm_submit(self):
        """
        工控机-MQTT-Telegraf-异常
        :return:
        """
        metrics = http.request.jsonrequest.get("metrics")
        for metric in metrics:
            values = metric.get("fields")
            user_id = values.get("user_id")
            work_center_id = values.get("work_center_id")
            alarm_item_id = values.get("alarm_item_id")
            alarm_time = values.get("alarm_time")
            if alarm_time:
                alarm_time_utc = datetime.datetime.strptime(alarm_time, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=8)
            else:
                alarm_time_utc = fields.Datetime.now()
            if not user_id or not alarm_item_id or not work_center_id:
                return
            alarm_item = http.request.env(user=SUPERUSER_ID)["roke.abnormal.alarm.item"].browse(int(alarm_item_id))
            alarm_item_type = alarm_item.type_id
            try:
                http.request.env(user=SUPERUSER_ID)["roke.abnormal.alarm"].create({
                    "sponsor": int(user_id),
                    "work_center": int(work_center_id),
                    "abnormal_id": alarm_item_type.id,
                    "abnormal_item_ids": [(6, 0, [alarm_item.id])],
                    "originating_time": alarm_time_utc
                })
            except Exception as e:
                _logger.error(f"工控机提报常报错：{e}")
            _logger.error(f"工控机提报常成功：{alarm_item.name}")
        return {"state": "success"}