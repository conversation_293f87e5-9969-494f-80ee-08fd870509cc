# -*- coding: utf-8 -*-
"""
Description:
    异常相关接口
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import datetime
import math
import logging
from odoo import models, fields, api, http, SUPERUSER_ID, _
_logger = logging.getLogger(__name__)


class Main(http.Controller):

    @http.route('/roke/get_abnormal_state', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_state(self):
        """
        获取异常状态
        :return:
        """
        _logger.info("获取异常状态")
        records = http.request.env["roke.abnormal.alarm.state"].search([])
        # 异常项目列表
        abnormal_states = []
        for record in records:
            abnormal_states.append({
                "id": record.id,
                "name": record.name
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "abnormal_states": abnormal_states
        }

    @http.route('/roke/get_abnormal_type', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_type(self):
        """
        获取异常类型
        :return:
        """
        _logger.info("获取异常项目")
        type_name = http.request.jsonrequest.get("type_name")
        domain = []
        if type_name:
            domain.append(("name", "ilike", type_name))
        records = http.request.env["roke.abnormal.alarm.type"].search(domain)
        # 分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 500)  # 每页记录数
        total_number = len(records)
        total_page = math.ceil(len(records) / page_size)  # 总页数
        records = records[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        # 异常项目列表
        abnormal_types = []
        for record in records:
            abnormal_types.append({
                "id": record.id,
                "name": record.name or "",
                "note": record.note or ""
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "page_no": page_no,
            "total_page": total_page,
            "total_number": total_number,
            "abnormal_types": abnormal_types
        }

    @http.route('/roke/get_abnormal_item', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_item(self):
        """
        获取异常项目
        :return:
        """
        _logger.info("获取异常项目")
        item_code = http.request.jsonrequest.get("item_code")
        item_name = http.request.jsonrequest.get("item_name")
        type_id = http.request.jsonrequest.get("type_id")
        domain = []
        if item_name:
            domain.append(("name", "ilike", item_name))
        if item_code:
            domain.append(("code", "ilike", item_code))
        if type_id:
            domain.append(("type_id", "=", int(type_id)))
        records = http.request.env["roke.abnormal.alarm.item"].search(domain)
        # 分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 500)  # 每页记录数
        total_number = len(records)
        total_page = math.ceil(len(records) / page_size)  # 总页数
        records = records[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        # 异常项目列表
        abnormal_items = []
        for record in records:
            abnormal_items.append({
                "id": record.id,
                "code": record.code or "",
                "name": record.name or "",
                "note": record.note or "",
                "type_id": record.type_id.id or "",
                "type_name": record.type_id.name or "",
                "color": record.color or "#0099FF"
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "page_no": page_no,
            "total_page": total_page,
            "total_number": total_number,
            "abnormal_items": abnormal_items
        }

    @http.route('/roke/submit_abnormal_order', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def submit_abnormal_order(self):
        """
        提交异常表单
        :return:
        """
        _logger.info("提交异常表单")
        type_id = http.request.jsonrequest.get("type_id")
        item_ids = http.request.jsonrequest.get("item_ids") or []
        wo_id = http.request.jsonrequest.get("wo_id")
        work_center_id = http.request.jsonrequest.get("work_center_id") or False
        process_id = http.request.jsonrequest.get("process_id", False)
        product_id = http.request.jsonrequest.get("product_id", False)
        note = http.request.jsonrequest.get("reason") or ""
        files = http.request.jsonrequest.get("files") or []
        if wo_id:
            work_order = http.request.env["roke.work.order"].browse(int(wo_id))
            if not process_id:
                process_id = work_order.process_id.id
            if not product_id:
                product_id = work_order.product_id.id
        items = http.request.env["roke.abnormal.alarm.item"].browse(item_ids)
        departments = items.mapped("department_id")
        order = http.request.env["roke.abnormal.alarm"].create({
            "abnormal_id": type_id,
            "abnormal_item_ids": [(6, 0, item_ids)],
            "work_center": work_center_id,
            "process": process_id,
            "product": product_id,
            "handle_department_id": departments[0].id if departments else False,
            "handle_employee_ids": [(6, 0, items.employee_ids.ids)],
            "note": note
        })
        # 照片
        attachment_ids = []
        Attachment = http.request.env['ir.attachment']
        i = 1
        for file in files:
            file_data = file.get("data")
            file_type = file.get("type") or ""
            attachment = Attachment.create({
                'name': "异常%s-%s.%s" % (order.display_name, str(i), file_type),
                'datas': file_data,
                'res_model': "roke.abnormal.alarm",
                'res_id': order.id
            })
            attachment_ids.append(attachment.id)
            i += 1
        order.write({"image_ids": [(6, 0, attachment_ids)]})
        return {"state": "success", "msgs": "提交成功"}

    def get_abnormal_order_vals(self, record, detail=False):
        """
        获取异常单内容
        :param order:
        :param detail:
        :return:
        """
        res = {
            "id": record.id,
            "code": record.code or "",
            "abnormal_type": record.abnormal_id.display_name or "",
            "abnormal_item": "、".join(record.abnormal_item_ids.mapped("name")),
            "initiation_time": (record.originating_time + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S"),
            "handle_employee": "、".join(record.handle_employee_ids.mapped("name")),
            "work_center": record.work_center.display_name or "",
            "state": record.state_id.display_name or "",
        }
        if detail:
            # 获取状态变更时间
            state_times = [{
                "description": "创建时间",
                "time": (record.create_date + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S")
            }]
            mail_messages = http.request.env(user=SUPERUSER_ID)["mail.message"].search([
                ("model", "=", "roke.abnormal.alarm"), ("res_id", "=", record.id)
            ])
            tracking_values = mail_messages.tracking_value_ids.filtered(lambda tv: tv.field.name == "state_id")
            for tracking_value in tracking_values:
                state_times.append({
                    "description": tracking_value.new_value_char or "",
                    "time": (tracking_value.create_date + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S")
                })
            res.update({
                "sponsor": record.sponsor.display_name or "",
                "note": record.note or "",
                "reason": record.abnormal_note or "",
                "processing_results": record.processing_results or "",
                "images": record.get_image_urls(file_type="url") or [],
                "state_times": state_times
            })
        return res

    @http.route('/roke/get_abnormal_order_list', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_order_list(self):
        """
        获取异常表单列表
        :return:
        """
        _logger.info("获取异常表单")
        code = http.request.jsonrequest.get("code")
        state_id = http.request.jsonrequest.get("state_id")
        domain = []
        if code:
            domain.append(("code", "ilike", code))
        if state_id:
            domain.append(("state_id", "=", state_id))
        records = http.request.env["roke.abnormal.alarm"].search(domain)
        # 分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 500)  # 每页记录数
        total_number = len(records)
        total_page = math.ceil(len(records) / page_size)  # 总页数
        records = records[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        # 异常项目列表
        abnormal_orders = []
        for record in records:
            abnormal_orders.append(self.get_abnormal_order_vals(record))
        return {
            "state": "success",
            "msgs": "获取成功",
            "page_no": page_no,
            "total_page": total_page,
            "total_number": total_number,
            "abnormal_orders": abnormal_orders
        }

    @http.route('/roke/get_abnormal_order_detail', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False,
                cors='*')
    def get_abnormal_order_detail(self):
        """
        获取异常表单详情
        :return:
        """
        _logger.info("获取异常表单")
        order_id = http.request.jsonrequest.get("order_id")
        if not order_id:
            return {"state": "error", "msgs": "必须入参单据ID"}
        record = http.request.env["roke.abnormal.alarm"].browse(int(order_id))
        return {
            "state": "success",
            "msgs": "获取成功",
            "abnormal_order": self.get_abnormal_order_vals(record, detail=True)
        }


