<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--异常-->
        <record id="mobile_menu_yc" model="roke.app.function.category">
            <field name="name">异常</field>
            <field name="index">yc</field>
        </record>

        <!--异常-异常处理单-->
        <record id="mobile_roke_abnormal_alarm_state" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_abnormal_alarm.model_roke_abnormal_alarm_state"/>
            <field name="model_name">处理状态</field>
            <field name="app_category_id" ref="roke_abnormal_alarm.mobile_menu_yc"/>
            <field name="base_data">True</field>
        </record>

        <!--异常-异常处理单-->
        <record id="mobile_roke_abnormal_alarm" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_abnormal_alarm.model_roke_abnormal_alarm"/>
            <field name="model_name">异常处理单</field>
            <field name="app_category_id" ref="roke_abnormal_alarm.mobile_menu_yc"/>
            <field name="base_data">False</field>
        </record>

        <!--筛选字段-->
        <record id="mobile_roke_abnormal_alarm_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__code"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_id"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_search_field_ids03" model="roke.app.general.order.fields.search">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__sponsor"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_search_field_ids04" model="roke.app.general.order.fields.search">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__originating_time"/>
        </record>

        <!--编号、异常类型、异常项目、备注、工作中心、发起时间-->
        <!--列表字段-->
        <record id="mobile_roke_abnormal_alarm_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__code"/>
            <field name="primary">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_id"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_item_ids"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__work_center"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__originating_time"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids06" model="roke.app.general.order.fields.list">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__note"/>
        </record>
        <record id="mobile_roke_abnormal_alarm_list_field_ids07" model="roke.app.general.order.fields.list">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__state_id"/>
            <field name="zdy_field_description">状态</field>
        </record>

        <!--异常类型、异常项目、工作中心、发起时间、发起人、备注、图片附件、异常原因、处理结果、接收时间、现场时间、完成时间、确认时间。-->
        <!--表头字段-->
        <record id="mobile_roke_abnormal_alarm_header_field_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_id"/>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_item_ids"/>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids03" model="roke.app.general.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__originating_time"/>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids04" model="roke.app.general.order.fields.header">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__sponsor"/>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids05" model="roke.app.general.order.fields.header">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__note"/>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids06" model="roke.app.general.order.fields.header">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__image_ids"/>
            <field name="zdy_field_description">图片附件</field>
            <field name="field_readonly">True</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids07" model="roke.app.general.order.fields.header">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__abnormal_note"/>
            <field name="zdy_field_description">异常原因</field>
        </record>
        <record id="mobile_roke_abnormal_alarm_header_field_ids08" model="roke.app.general.order.fields.header">
            <field name="sequence">8</field>
            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>
            <field name="field_id" ref="field_roke_abnormal_alarm__processing_results"/>
        </record>
<!--        <record id="mobile_roke_abnormal_alarm_header_field_ids09" model="roke.app.general.order.fields.header">-->
<!--            <field name="sequence">9</field>-->
<!--            <field name="order_id" ref="mobile_roke_abnormal_alarm"/>-->
<!--            <field name="field_id" ref="field_roke_abnormal_alarm__state_id"/>-->
<!--            <field name="zdy_field_description">状态</field>-->
<!--        </record>-->

    </data>
</odoo>