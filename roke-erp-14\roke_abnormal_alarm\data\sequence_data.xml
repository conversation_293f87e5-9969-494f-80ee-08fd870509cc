<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--异常表单编号-->
        <record id="roke_abnormal_alarm_code_sequence" model="ir.sequence">
            <field name="name">异常表单编号</field>
            <field name="code">roke.abnormal.alarm.code</field>
            <field name="prefix">AND</field>
            <field name="padding">4</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--异常表单编号-->
        <record id="roke_abnormal_alarm_item_code_sequence" model="ir.sequence">
            <field name="name">异常项目编号</field>
            <field name="code">roke.abnormal.alarm.item.code</field>
            <field name="padding">3</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
</odoo>
