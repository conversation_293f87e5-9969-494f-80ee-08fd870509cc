# -*- coding: utf-8 -*-
"""
Description:
    异常单据
Versions:
    Created by www.rokedata.com
"""
from odoo import api, exceptions, fields, models

class AlarmInheritMaintenanceOrder(models.Model):
    _inherit = "roke.mes.maintenance.order"

    alarm_id = fields.Many2one("roke.abnormal.alarm",string="告警记录")

class AlarmInheritQualityOrder(models.Model):
    _inherit = "roke.quality.order"

    alarm_id = fields.Many2one("roke.abnormal.alarm", string="告警记录")

class AlarmInheritScrapOrder(models.Model):
    _inherit = "roke.scrap.order"

    alarm_id = fields.Many2one("roke.abnormal.alarm", string="告警记录")

class AlarmInheritWorkOrder(models.Model):
    _inherit = "roke.work.order"

    alarm_id = fields.Many2one("roke.abnormal.alarm", string="告警记录")