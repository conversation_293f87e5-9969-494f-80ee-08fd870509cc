odoo.define('roke_mes_abnormal.button', function (require){
    "use strict";
    var rpc = require("web.rpc");
    var ListController = require('web.ListController');
    var ListView = require('web.ListView');


    var ImportViewMixin = {
        /**
         * @override
         */
        init: function (viewInfo, params) {
            var importEnabled = 'import_enabled' in params ? params.import_enabled : true;
            this.controllerParams.importEnabled = importEnabled;
        },
    };
    var ImportControllerMixin = {
        /**
         * @override
         */
        init: function (parent, model, renderer, params) {
            this.importEnabled = params.importEnabled;
        },
        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------
        /**
         * Adds an event listener on the import button.
         *
         * @private
         */
        _bindImport: function () {
            if (!this.$buttons) {
                return;
            }
            var self = this;
            /*报工异常查询*/
            this.$buttons.on('click', '.o_button_action_show_work_abnormal', function () {
                rpc.query({
                    model: 'roke.work.abnormal.record.wizard',
                    method: 'action_entrance',
                    args: [""]
                }).then(function (action_dict) {
                   self.do_action(
                       action_dict
                   )
                });
            });
        }
    };
    ListView.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportViewMixin.init.apply(this, arguments);
        },
    });
    ListController.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin.init.apply(this, arguments);
        },
        renderButtons: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin._bindImport.call(this);
            var self = this;
            /*报工异常查询*/
            if (this.modelName === "roke.work.abnormal.record.wizard"){
                self.$buttons.find(".o_button_action_show_work_abnormal").css("display","inline-block");
            }
        }
    });
}
);