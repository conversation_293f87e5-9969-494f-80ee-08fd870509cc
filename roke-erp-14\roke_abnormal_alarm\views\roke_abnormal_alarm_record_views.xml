<?xml version="1.0"?>
<odoo>
    <!--search-->
    <record id="roke_abnormal_alarm_views_filter" model="ir.ui.view">
        <field name="model">roke.abnormal.alarm</field>
        <field name="arch" type="xml">
            <search>
                <field name="abnormal_id"/>
                <field name="sponsor"/>
                <filter name="group_by_abnormal_id" context="{'group_by': 'abnormal_id'}"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="roke_abnormal_alarm_views_tree" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.tree</field>
        <field name="model">roke.abnormal.alarm</field>
        <field name="arch" type="xml">
            <tree>
                <field name="code"/>
                <field name="abnormal_id"/>
                <field name="abnormal_item_ids" widget="many2many_tags"/>
                <field name="work_center"/>
                <field name="sponsor"/>
                <field name="recipient"/>
                <field name="handle_employee_ids" widget="many2many_tags"/>
                <field name="originating_time"/>
                <field name="state_id" string="异常状态"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="roke_abnormal_alarm_views_form" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.form</field>
        <field name="model">roke.abnormal.alarm</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state_id" widget="statusbar" clickable="True"/>
                    <button name="maintenance_alarm" type="object" string="设备告警" class='oe_highlight'
                            attrs="{'invisible': [('is_equipment','=',False)]}"/>
                    <button name="quality_alarm" type="object" string="质量告警" class='oe_highlight'
                            attrs="{'invisible': [('is_enterprise','=',False)]}"/>
                    <field name="is_equipment" invisible="1"/>
                    <field name="is_enterprise" invisible="1"/>
                    <field name="is_quality" invisible="1"/>
                    <field name="is_stock" invisible="1"/>
                    <field name="res_model" invisible="1"/>
                </header>
                <div name="button_box" class="oe_button_box">
                    <button type="object"
                        name="action_view_maintenance"
                        class="oe_stat_button"
                        icon="fa-truck" attrs="{'invisible':[('maintenance_ids','=',[])]}">
                        <field name="maintenance_count" widget="statinfo" string="设备维修单" help="查看设备维修单"/>
                        <field name="maintenance_ids" invisible="1"/>
                    </button>
                </div>
                <group name="group_top" col="4">
                    <group>
                        <field name="code" readonly="1"/>
                        <field name="originating_time"/>
                        <field name="priority" widget="priority"/>
                    </group>
                    <group>
                        <field name="abnormal_id" required="1" options="{'no_create': True}"/>
                        <field name="sponsor" options="{'no_create': True}"/>
                        <field name="handle_employee_ids" widget="many2many_tags" options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="recipient" options="{'no_create': True}"/>
                        <field name="handle_department_id" options="{'no_create': True}"/>
                        <field name="product" options="{'no_create': True}"/>
                    </group>
                    <group>
                        <field name="work_center" options="{'no_create': True}"/>
                        <field name="process" options="{'no_create': True}"/>
                    </group>
                </group>
                <group>
                    <field name="note"/>
                    <field name="abnormal_note"/>
                    <field name="processing_results"/>
                </group>
                <notebook>
                    <page string="相关附件">
                        <field name="image_ids" widget="many2many_pic_preview"/>
                    </page>
                </notebook>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--kanban-->
    <record id="roke_abnormal_alarm_kanban" model="ir.ui.view">
        <field name="model">roke.abnormal.alarm</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state_id" class="o_kanban_small_column">
                <field name="state_id"/>
                <field name="id"/>
                <field name="color"/>
                <field name="priority"/>
                <field name="message_partner_ids"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="
                            oe_kanban_color_#{kanban_getcolor(record.color.raw_value)}
                            oe_kanban_global_click">
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle btn"
                                    data-toggle="dropdown" role="button"
                                    aria-label="Dropdown menu"
                                    title="Dropdown menu"
                                    href="#">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable">
                                        <a role="menuitem" type="edit" class="dropdown-item">Edit</a>
                                    </t>
                                    <t t-if="widget.deletable">
                                        <a role="menuitem" type="delete" class="dropdown-item">Delete</a>
                                    </t>
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                            <div class="oe_kanban_body">
                                <div>
                                    <strong>
                                        <a type="open">异常：<field name="abnormal_id"/></a>
                                    </strong>
                                </div>
                                <ul>
                                    <li t-attf-class="oe_kanban_text_{{
                                        record.priority.raw_value lt '2'
                                        ? 'black' : 'red'}}">
                                        发起人：<field name="sponsor"/>
                                    </li>
                                    <li>异常项目：<field name="abnormal_item_ids" widget="many2many_tags"/></li>
                                    <li>工作中心：<field name="work_center"/></li>
                                    <li>处理部门：<field name="handle_department_id"/></li>
                                    <li>处理人：<field name="handle_employee_ids" widget="many2many_tags"/></li>
                                    <li>发起时间：<field name="originating_time"/></li>
                                </ul>
                            </div>
                            <div class="o_kanban_record_bottom">
                                <div class="oe_kanban_bottom_left">
                                    <field name="priority" widget="priority"/>
                                    <field name="activity_ids" widget="kanban_activity"/>
                                    <t t-set="calendar_sign">
                                        <i class="fa fa-calendar"/>
                                    </t>
                                    <t t-raw="calendar_sign"/>
                                </div>
                            </div>
                            <div class="oe_clear"/>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <!--action-->
    <record id="roke_abnormal_alarm_views_action" model="ir.actions.act_window">
        <field name="name">异常表单</field>
        <field name="res_model">roke.abnormal.alarm</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="form_view_id" ref="roke_abnormal_alarm_views_form"/>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>