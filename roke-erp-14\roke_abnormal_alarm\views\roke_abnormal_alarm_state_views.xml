<?xml version="1.0"?>
<odoo>
    <record id="roke_abnormal_alarm_state_views_tree" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.state.tree</field>
        <field name="model">roke.abnormal.alarm.state</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>

    <record id="roke_abnormal_alarm_state_views_form" model="ir.ui.view">
        <field name="name">roke.abnormal.alarm.state.form</field>
        <field name="model">roke.abnormal.alarm.state</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="roke_abnormal_alarm_state_views_action" model="ir.actions.act_window">
        <field name="name">异常类型</field>
        <field name="res_model">roke.abnormal.alarm.state</field>
        <field name="view_mode">tree,form</field>
        <field name="form_view_id" ref="roke_abnormal_alarm_state_views_form"/>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>