# -*- coding: utf-8 -*-
"""
Description:
    告警
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RokeAlarmWizard(models.TransientModel):
    _name = "roke.alarm.wizard"
    _description = "告警"

    abnormal_id = fields.Many2one("roke.abnormal.alarm.type",string="异常类型")
    sponsor = fields.Many2one("res.users",string="发起人",default=lambda s: s.env.uid)
    originating_time = fields.Datetime(string="发起时间",default=fields.Datetime.now())
    abnormal_note = fields.Text(string="异常描述")
    recipient = fields.Many2one("res.users",string="接受人")
    work_center = fields.Many2one("roke.work.center", string="工作中心")
    process = fields.Many2one("roke.process", string="工序")
    product = fields.Many2one("roke.product", string="产品")
    color = fields.Integer(string="颜色")
    priority = fields.Selection([('0', '正常'),('1', '普通'),('2', '加急'),('3', '危险')],'Priority',default='1')

    res_id = fields.Integer(string="记录id")
    res_model = fields.Char(string="记录模型")

    def confirm(self):
        alarm = self.env['roke.abnormal.alarm'].create({
            'abnormal_id': self.abnormal_id.id,
            'sponsor': self.sponsor.id,
            'originating_time': self.originating_time,
            'abnormal_note': self.abnormal_note,
            'recipient': self.recipient.id,
            'work_center': self.work_center.id,
            'process': self.process.id,
            'product': self.product.id,
            'color': self.color,
            'priority': self.priority,
            'res_id': self.res_id,
            'res_model': self.res_model
        })
        if not alarm:
            raise UserError('数据填写错误，数据创建失败!')