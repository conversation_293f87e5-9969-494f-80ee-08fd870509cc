<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_alarm_wizard_form" model="ir.ui.view">
        <field name="name">roke.alarm.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.alarm.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <div class="oe_title">
                    <label for="abnormal_id" class="oe_edit_only"/>
                    <h1>
                        <field name="abnormal_id"/>
                    </h1>
                    <h2>
                        <field name="priority" widget="priority"/>
                    </h2>
                </div>
                <group id="g1">
                    <group>
                        <field name="sponsor" readonly="1"/>
                        <field name="recipient"/>
                        <field name="originating_time" readonly="1"/>
                    </group>
                    <group>
                        <field name="process"/>
                        <field name="product"/>
                    </group>
                </group>
                <group id="g2">
                    <field name="abnormal_note" nolabel="1" placeholder="此处可以填写备注或描述" />
                </group>
                <footer>
                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>
                    <button string="取消" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>
