# -*- coding: utf-8 -*-
"""
Description:
    设备维修单
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class RokeMaintenanceOrderWizard(models.TransientModel):
    _name = "roke.maintenance.order.wizard"
    _description = "设备维修单"

    alarm_id = fields.Many2one("roke.abnormal.alarm",string="告警记录")
    code = fields.Char(string="编号", default="新建")
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备")
    equipment_code = fields.Char(related="equipment_id.code", string="设备编号", store=True)
    user_id = fields.Many2many("res.users", string="指派人")
    state = fields.Selection([("wait", "待派工"), ("assign", "已派工"), ("finish", "完成"), ("postpone", "延期"), ("cancel", "取消")], string="状态", default="wait")
    priority = fields.Selection([('low', '低'), ('normal', '中'), ('high', '高'), ('urgent', '急')], string="紧急程度", default="normal")
    deadline = fields.Date(string="最后期限")
    fault_description = fields.Text(string="故障描述")
    fault_files = fields.Many2many("ir.attachment", string="故障附件")
    picture = fields.Binary('图片')
    report_time = fields.Datetime(string="报修时间", default=fields.Datetime.now)
    report_user_id = fields.Many2one("res.users", string="报修人", default=lambda self: self.env.user.id)
    fault_id = fields.Many2one('roke.mes.equipment.fault', string='故障类型')
    e_location = fields.Char('设备地点')
    repair_origin = fields.Selection([('normal', '日常报修'), ('maintain', '维保报修'), ('check', '巡检报修')], string='报修来源')

    def confirm(self):
        maintenance_id = self.env['roke.mes.maintenance.order'].create({
            'alarm_id': self.alarm_id.id,
            'code': self.code,
            'equipment_id': self.equipment_id.id,
            'equipment_code': self.equipment_code,
            'user_id': self.user_id.ids if self.user_id.ids else None,
            'state': self.state,
            'priority': self.priority,
            'deadline': self.deadline,
            'fault_description': self.fault_description,
            'fault_files': self.fault_files.ids if self.fault_files.ids else None,
            'picture': self.picture,
            'report_time': self.report_time,
            'report_user_id': self.report_user_id.id,
            'fault_id': self.fault_id.id,
            'e_location': self.e_location,
            'repair_origin': self.repair_origin
        })
        if not maintenance_id:
            raise UserError('数据填写错误，数据创建失败!')
