<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_maintenance_order_wizard_form" model="ir.ui.view">
        <field name="name">roke.maintenance.order.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.maintenance.order.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="code" class="oe_edit_only"/>
                        <h1><field name="code" readonly="1"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="equipment_id" required="1" options="{'no_create': True}"/>
                            <field name="user_id" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="report_user_id" required="1" options="{'no_create': True}"/>
                            <field name="report_time" required="1"/>
                            <field name="deadline"/>
                            <field name="priority"/>
                            <field name="e_location"/>
                        </group>
                        <group>
                            <field name="fault_description"/>
                            <field name="fault_id"/>
                            <field name="repair_origin"/>
                            <field name="fault_files" widget="many2many_binary" string="添加故障照片" nolabel="1" colspan="2"/>
                        </group>
                        <group>
                            <groupo>
                                <field name="picture" widget="image"/>
                            </groupo>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>
                    <button string="取消" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>
