<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_scrap_order_wizard_form" model="ir.ui.view">
        <field name="name">roke.scrap.order.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.scrap.order.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="code" readonly="1"/>
                            <field name="wr_id"/>
                            <field name="wo_id"/>
                            <field name="pt_id"/>
                            <field name="po_id"/>
                        </group>
                        <group>
                            <field name="product_id" readonly="1"/>
                            <field name="process_id" readonly="1"/>
                            <label for="total"/>
                            <div name="total" class="o_row">
                                <field name="total" readonly="1"/>
                                <span>
                                    <field name="uom_id"/>
                                </span>
                            </div>
                            <field name="employee_ids" readonly="1" widget="many2many_tags"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="报废明细">
                            <field name="line_ids" attrs="{'readonly': [('state', '!=', '待确认')]}">
                                <tree string="报废明细" editable="bottom">
                                    <field name="order_id" optional="hide" readonly="1"/>
                                    <field name="wr_id" optional="show" readonly="1"/>
                                    <field name="product_id" optional="show" readonly="1"/>
                                    <field name="reason_id" optional="show"/>
                                    <field name="qty" optional="show" sum="报废合计"/>
                                    <field name="clean_qty" optional="show" sum="已处理数量" readonly="1"/>
                                    <field name="replenish_qty" optional="show" sum="已补件数量" readonly="1"/>
                                    <field name="replenish_pt_ids" optional="show" readonly="1"/>
                                    <field name="allow_replenish_qty" invisible="1"/>
                                    <field name="state" invisible="1"/>
                                    <field name="note" optional="show"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                </sheet>
                <footer>
                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>
                    <button string="取消" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>
