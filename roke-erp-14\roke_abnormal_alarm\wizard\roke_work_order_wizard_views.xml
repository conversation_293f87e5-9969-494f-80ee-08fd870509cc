<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_work_order_wizard_form" model="ir.ui.view">
        <field name="name">roke.work.order.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.work.order.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="未完工,已完工"/>
                </header>
                <field name="allow_edit" invisible="1"/>
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="code" readonly="1" attrs="{'readonly': [('allow_edit', '!=', True)], 'invisible':[('code','=',False)]}"/>
                            <field name="task_id" readonly="1" attrs="{'readonly': [('allow_edit', '!=', True)], 'invisible':[('task_id','=',False)]}"/>
                            <field name="process_id" required="1" attrs="{'readonly': [('allow_edit', '!=', True)]}"/>
                            <field name="product_id" required="1" attrs="{'readonly': [('allow_edit', '!=', True)]}"/>
                            <label for="plan_qty"/>
                            <div name="plan_qty" class="o_row">
                                <field name="plan_qty" required="1" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                                <span>
                                    <field name="uom_id"/>
                                </span>
                            </div>
                            <field name="type" readonly="1"/>
                            <field name="priority" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="order_id" attrs="{'invisible': [('order_id', '=', False)]}"/>
                            <field name="project_code" attrs="{'invisible': [('project_code', '=', False)]}"/>
                            <field name="customer_id"/>
                        </group>
                        <group>
                            <field name="team_id" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="employee_ids" widget="many2many_tags" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="work_center_id" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <label for="finish_qty"/>
                            <div name="finish_qty" class="o_row">
                                <field name="finish_qty" readonly="1"/>
                                <span>
                                    <field name="uom_id"/>
                                </span>
                            </div>
                            <label for="unqualified_qty"/>
                            <div name="unqualified_qty" class="o_row">
                                <field name="unqualified_qty" readonly="1"/>
                                <span>
                                    <field name="uom_id"/>
                                </span>
                            </div>
                            <field name="work_hours" readonly="1"/>
                            <field name="plan_date" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <field name="dispatch_time" readonly="1" attrs="{'invisible': [('dispatch_time', '=', False)]}"/>
                            <field name="finish_time" readonly="1" attrs="{'invisible': [('finish_time', '=', False)]}"/>
                            <field name="create_uid" string="创建人" attrs="{'invisible': [('create_uid', '=', False)]}"/>
                            <field name="create_date" string="创建时间" attrs="{'invisible': [('create_date', '=', False)]}"/>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                </sheet>
                <footer>
                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>
                    <button string="取消" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>
