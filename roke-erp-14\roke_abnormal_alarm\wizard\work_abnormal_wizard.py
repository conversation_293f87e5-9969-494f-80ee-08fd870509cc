# -*- coding: utf-8 -*-
"""
Description:
    报工异常查询
    TODO APP接口
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class RokeActionShowWorkAbnormalWizard(models.TransientModel):
    _name = "roke.action.show.work.abnormal.wizard"
    _description = "检查报工异常"

    select_range = fields.Selection(
        [("当日", "当日"), ("本周", "本周"), ("本月", "本月"), ("昨日", "昨日"), ("上周", "上周"), ("上月", "上月")],
        string="报工日期", default="本月"
    )
    wr_range = fields.Selection([("无工单报工", "无工单报工"), ("所有报工", "所有报工")], string="检查范围", default="无工单报工")
    product_ids = fields.Many2many("roke.product", string="筛选产品", help="为空表示筛选所有产品的报工")
    start_date = fields.Date(string="开始日期")
    end_date = fields.Date(string="结束日期")

    def _get_abnormal_record_line(self):
        """
        获取异常明细
        :return:
        """
        domain = [
            ("work_time", "<=", self.end_date),
            ("work_time", ">=", self.start_date)
        ]
        if self.wr_range == "无工单报工":
            domain.append(("work_order_id.task_id", "=", False))
        if self.product_ids:
            domain.append(("product_id", "in", self.product_ids.ids))
        wrs = self.env["roke.work.record"].search(domain)
        # 整理数据，合并相同产品和工序的值
        group_wr = {}  # {“dd-ss”: {}}
        for wr in wrs:
            if wr.work_order_id.task_id:
                wr_type = "有工单报工"
            else:
                wr_type = "无工单报工"
            index = "%s-%s-%s" % (str(wr.product_id.id), str(wr.process_id.id), wr_type)
            if index in group_wr:
                vals = group_wr.get(index)
                vals["finish_qty"] += wr.finish_qty
                vals["work_hours"] += wr.work_hours
                vals["scrap_qty"] += sum(wr.scrap_line_ids.mapped("qty"))
                vals["repair_qty"] += sum(wr.repair_line_ids.mapped("qty"))
                vals["wr_ids"] += [wr.id]
            else:
                routing_line = wr.work_order_id.routing_line_id
                if not routing_line and wr.product_id.routing_id:
                    routing_line = wr.product_id.routing_id.line_ids.filtered(lambda rl: rl.process_id == wr.process_id)
                group_wr[index] = {
                    "sequence": routing_line.sequence if routing_line else 1,
                    "routing_id": routing_line.routing_id.id,
                    "routing_line_id": routing_line.id,
                    "finish_qty": wr.finish_qty,
                    "work_hours": wr.work_hours,
                    "scrap_qty": sum(wr.scrap_line_ids.mapped("qty")),
                    "repair_qty": sum(wr.repair_line_ids.mapped("qty")),
                    "wr_ids": [wr.id],
                }
        # 生成明细值
        line_list = []
        for group_index, group_value in group_wr.items():
            product_id, process_id, wr_type = group_index.split("-")
            line_list.append((0, 0, {
                "product_id": int(product_id),
                "process_id": int(process_id),
                "wr_type": wr_type,
                "routing_id": group_value.get("routing_id", False),
                "routing_line_id": group_value.get("routing_line_id", False),
                "sequence": group_value.get("sequence", 1),
                "finish_qty": group_value.get("finish_qty", 0),
                "work_hours": group_value.get("work_hours", 0),
                "scrap_qty": group_value.get("scrap_qty", 0),
                "repair_qty": group_value.get("repair_qty", 0),
                "wr_ids": [(6, 0, group_value.get("wr_ids", 0))],
            }))
        return line_list

    def confirm(self):
        """确认检索报工异常"""

        record = self.env["roke.work.abnormal.record.wizard"].create({
            "start_date": self.start_date,
            "end_date": self.end_date,
            "wr_range": self.wr_range,
            "product_ids": [(6, 0, self.product_ids.ids)],
            "line_ids": self._get_abnormal_record_line()
        })
        return {
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'target': 'current',
            'res_id': record.id,
            'context': {'create': False, 'edit': False},
            'res_model': 'roke.work.abnormal.record.wizard'
        }

    @api.onchange("select_range")
    def _onchange_select_range(self):
        if self.select_range:
            start_date, end_date = False, False
            today = fields.Date.context_today(self)
            if self.select_range == "当日":
                start_date, end_date = today, today
            elif self.select_range == "本周":
                start_date, end_date = today - timedelta(days=today.weekday()), today - timedelta(
                    days=today.weekday()) + timedelta(days=6)
            elif self.select_range == "本月":
                start_date = today.replace(day=1)
                if today.month != 12:
                    end_date = today.replace(day=1, month=today.month + 1) - timedelta(days=1)
                else:
                    end_date = today.replace(year=today.year + 1, day=1, month=1)
            elif self.select_range == "昨日":
                start_date, end_date = today - timedelta(days=1), today - timedelta(days=1)
            elif self.select_range == "上周":
                start_date, end_date = today - timedelta(days=today.weekday() + 7), today - timedelta(
                    days=today.weekday() + 1)
            elif self.select_range == "上月":
                end_date = today.replace(day=1) - timedelta(days=1)
                if today.month != 1:
                    start_date = today.replace(day=1, month=today.month - 1)
                else:
                    start_date = today.replace(year=today.year - 1, day=1, month=12)
            return {"value": {"start_date": start_date, "end_date": end_date if end_date <= today else today}}
        else:
            return {}


class RokeWorkAbnormalRecordWizard(models.TransientModel):
    _name = "roke.work.abnormal.record.wizard"
    _description = "报工异常查询记录"

    start_date = fields.Date(string="开始日期")
    end_date = fields.Date(string="结束日期")
    wr_range = fields.Selection([("无工单报工", "无工单报工"), ("所有报工", "所有报工")], string="检查范围", default="无工单报工")
    product_ids = fields.Many2many("roke.product", string="筛选产品", help="为空表示筛选所有产品的报工")
    line_ids = fields.One2many("roke.work.abnormal.line.wizard", "abnormal_id", string="报工明细")

    def name_get(self):
        res = []
        for record in self:
            name = "%s至%s" % (record.start_date, record.end_date)
            res.append((record.id, name))
        return res

    def action_by_group(self):
        """
        分组查看
        :return:
        """
        return {
            'name': '分组查看',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('abnormal_id', '=', self.id)],
            'context': {'create': False, 'edit': False, 'delete': False, 'search_default_groupby_product': True},
            'res_model': 'roke.work.abnormal.line.wizard'
        }

    def action_entrance(self):
        """返回查询筛选条件"""
        # if not self.env.user.has_group(''):
        #     raise ValidationError("没有查询权限，请联系管理员。")
        view = self.env.ref('roke_abnormal_alarm.roke_action_show_work_abnormal_wizard_form_view')
        return {
            'name': '报工异常查询',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'target': 'new',
            'res_model': 'roke.action.show.work.abnormal.wizard'
        }


class RokeWorkAbnormalLineWizard(models.TransientModel):
    _name = "roke.work.abnormal.line.wizard"
    _description = "报工异常查询记录"
    _order = 'product_id, wr_type, sequence'

    abnormal_id = fields.Many2one("roke.work.abnormal.record.wizard", string="异常查询记录", required=True, ondelete='cascade')
    wr_type = fields.Selection([("无工单报工", "无工单报工"), ("有工单报工", "有工单报工")], string="报工类型")
    sequence = fields.Integer(string="序号")
    product_id = fields.Many2one("roke.product", string="产品", required=True, ondelete='cascade')
    process_id = fields.Many2one("roke.process", string="工序", required=True, ondelete='cascade')
    routing_id = fields.Many2one("roke.routing", string="工艺路线", ondelete='cascade')
    routing_line_id = fields.Many2one("roke.routing.line", string="工艺明细", ondelete='cascade')
    finish_qty = fields.Float(string="合格数")
    scrap_qty = fields.Float(string="报废数")
    repair_qty = fields.Float(string="返修数")
    work_hours = fields.Float(string="工时数")
    wr_ids = fields.Many2many("roke.work.record", "roke_work_abnormal_line_wr_rel", string="报工记录")
    abnormal = fields.Boolean(string="异常", compute="_compute_abnormal", store=True)

    def check_abnormal(self):
        """
        校验是否异常
            异常校验规则：本次报工的数量是否超过同工艺路线下的前工序报工数量
        :return:
        """
        routing = self.product_id.routing_id
        if not routing:
            # 未设置产品工艺路线表示无异常
            return False
        previous_routing_line = routing.line_ids.filtered(lambda rl: rl.sequence < self.sequence)  # 获取前道工序对应的工艺路线明细
        if not previous_routing_line:
            # 工艺路线无上道工序，表示无异常
            return False
        previous_records = self.abnormal_id.line_ids.filtered(lambda l: l.routing_id == self.routing_id and l.sequence < self.sequence)  # 获取前道工序的
        if not previous_records:
            # 有上道工序，但是本次汇总无上道工序对应报工记录，表示异常
            return True
        if self.finish_qty + self.scrap_qty + self.repair_qty > min(previous_records.mapped("finish_qty")):
            # 当前统计的合格数+报废数+返修数 大于 前道工序最小的合格数 表示异常
            return True
        return False

    @api.depends("product_id", "process_id")
    def _compute_abnormal(self):
        for record in self:
            record.abnormal = record.check_abnormal()

    def action_wr_detail(self):
        """
        查看报工记录明细
        :return:
        """
        action = self.env["ir.actions.actions"]._for_xml_id("roke_mes_production.view_roke_work_record_action")
        action['domain'] = [("id", "in", self.wr_ids.ids)]
        return action


