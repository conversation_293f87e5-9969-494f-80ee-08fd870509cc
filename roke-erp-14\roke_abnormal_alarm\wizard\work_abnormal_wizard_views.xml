<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--报工异常查询记录-->
    <!--search-->
    <record id="view_roke_work_abnormal_record_wizard_search" model="ir.ui.view">
        <field name="name">roke.work.abnormal.record.wizard.search</field>
        <field name="model">roke.work.abnormal.record.wizard</field>
        <field name="arch" type="xml">
            <search string="报工异常查询">
            </search>
        </field>
    </record>
    <record id="view_roke_work_abnormal_record_wizard_tree" model="ir.ui.view">
        <field name="name">roke.work.abnormal.record.wizard.tree</field>
        <field name="model">roke.work.abnormal.record.wizard</field>
        <field name="arch" type="xml">
            <tree string="报工异常查询">
                <field name="start_date" optional="show"/>
                <field name="end_date" optional="show"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_abnormal_record_wizard_form" model="ir.ui.view">
        <field name="name">roke.work.abnormal.record.wizard.form</field>
        <field name="model">roke.work.abnormal.record.wizard</field>
        <field name="arch" type="xml">
            <form string="报工异常查询">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                        </group>
                        <group>
                            <field name="wr_range"/>
                            <field name="product_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"
                                   placeholder="此处不填表示默认检查所有产品"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="报工统计">
                            <button name="action_by_group" type="object" class="btn-primary" string="分组查看"/>
                            <field name="line_ids" readonly="1"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_work_abnormal_record_wizard_action" model="ir.actions.act_window">
        <field name="name">报工异常查询</field>
        <field name="res_model">roke.work.abnormal.record.wizard</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{'create': False, 'edit': False}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“报工异常查询”按钮执行查询。
          </p>
        </field>
    </record>
    <!--报工异常明细-->
    <!--search-->
    <record id="view_roke_work_abnormal_line_wizard_search" model="ir.ui.view">
        <field name="name">roke.work.abnormal.line.wizard.search</field>
        <field name="model">roke.work.abnormal.line.wizard</field>
        <field name="arch" type="xml">
            <search string="报工异常明细">
                <field name="product_id"/>
                <field name="process_id"/>
                <filter string="异常" name="异常" domain="[('abnormal', '=', True)]"/>
                <filter string="全部" name="全部" domain="[('abnormal', '!=', True)]"/>
                <separator/>
                <filter string="无工单报工" name="无工单报工" domain="[('wr_type', '!=', '无工单报工')]"/>
                <filter string="有工单报工" name="有工单报工" domain="[('wr_type', '!=', '有工单报工')]"/>
                <group expand="0" string="Group By">
                    <filter string="产品" name="groupby_product" domain="[]" context="{'group_by':'product_id'}"/>
                    <filter string="工序" name="groupby_process" domain="[]" context="{'group_by':'process_id'}"/>
                    <filter string="报工类型" name="groupby_wr_type" domain="[]" context="{'group_by':'wr_type'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="view_roke_work_abnormal_line_wizard_tree" model="ir.ui.view">
        <field name="name">roke.work.abnormal.line.wizard.tree</field>
        <field name="model">roke.work.abnormal.line.wizard</field>
        <field name="arch" type="xml">
            <tree string="报工异常明细" decoration-danger="abnormal" expand="True">  <!--expand参数默认展开分组-->
                <field name="abnormal_id" invisible="1"/>
                <field name="sequence" invisible="1"/>
                <field name="abnormal" invisible="1"/>
                <field name="wr_ids" invisible="1"/>
                <field name="product_id" optional="show"/>
                <field name="process_id" optional="show"/>
                <field name="routing_id" optional="hide"/>
                <field name="finish_qty" optional="show" sum="合格数"/>
                <field name="scrap_qty" optional="show" sum="报废数"/>
                <field name="repair_qty" optional="show" sum="返修数"/>
                <field name="work_hours" optional="show" sum="工时数"/>
                <field name="wr_type" optional="show"/>
                <button name="action_wr_detail" icon="fa-list" type="object" string="报工记录"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_abnormal_line_wizard_form" model="ir.ui.view">
        <field name="name">roke.work.abnormal.line.wizard.form</field>
        <field name="model">roke.work.abnormal.line.wizard</field>
        <field name="arch" type="xml">
            <form string="报工异常明细">
                <sheet>
                    <header>
                        <button name="action_wr_detail" type="object" class="btn-primary" string="报工记录"/>
                    </header>
                    <group>
                        <group>
                            <field name="finish_qty"/>
                            <field name="scrap_qty"/>
                            <field name="repair_qty"/>
                            <field name="work_hours"/>
                        </group>
                        <group>
                            <field name="product_id"/>
                            <field name="process_id"/>
                            <field name="routing_id"/>
                            <field name="wr_type"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--查询条件-->
    <record id="roke_action_show_work_abnormal_wizard_form_view" model="ir.ui.view">
        <field name="name">roke.action.show.work.abnormal.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.action.show.work.abnormal.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <div name="message" class="alert alert-info" role="alert" style="margin-bottom:0px;">
                    筛选起止日期范围内的报工记录生产异常报告。
                </div>
                <group>
                    <group>
                        <field name="select_range"/>
                        <label for="start_date" string="日期起止"/>
                        <div name="date_range" class="o_row">
                            <field name="start_date" required="1"/>-
                            <field name="end_date" required="1"/>
                        </div>
                    </group>
                    <group>
                        <field name="wr_range" required="1"/>
                        <field name="product_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"
                               placeholder="此处不填表示默认检查所有产品"/>
                    </group>
                </group>
                <footer>
                    <button name='confirm' string='确认创建' type='object' class='oe_highlight'/>
                    <button string="关闭" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>