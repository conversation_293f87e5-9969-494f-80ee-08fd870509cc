# -*- coding: utf-8 -*-
from . import models
from . import wizard
from . import controller

from odoo import api, SUPERUSER_ID

def predefined_menu_permissions(cr, registry):
    """
    预定义菜单权限
    :param cr:
    :param registry:
    :return:
    """
    env = api.Environment(cr, SUPERUSER_ID, {})
    # 固定资产
    menu1_id = env.ref('roke_account_asset.view_account_asset_purchase_menu', raise_if_not_found=False)
    groups = [
        "roke_pub_access.group_account_manager",
    ]
    group1_ids = [
        env.ref(group, raise_if_not_found=False)
        for group in groups if env.ref(group, raise_if_not_found=False)
    ]
    env['ir.ui.menu'].set_x_access_menus(menu1_id, group1_ids)

