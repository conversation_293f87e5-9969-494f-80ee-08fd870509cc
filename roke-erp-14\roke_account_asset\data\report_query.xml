<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_account_asset_report" model="roke.sql.model.component">
            <field name="name">卡片台账查询</field>
            <field name="journaling_type">客户端报表</field>
            <field name="sql_statement">
                SELECT
                roke_account_asset.code AS "资产编号",
                roke_account_asset.name AS "资产名称",
                roke_account_asset.asset_category AS "资产类别",
                roke_account_asset.asset_location AS "资产位置",
                roke_account_asset.asset_origin AS "资产来源",
                roke_account_asset.asset_purpose AS "资产用途",
                COALESCE(roke_account_asset.asset_qty,0) AS "资产数量",
                COALESCE(roke_account_asset.original_value,0) AS "资产原值",
                COALESCE(roke_account_asset.residual_total,0) AS "累计折旧",
                COALESCE(roke_account_asset.value_residual,0) AS "资产净值",
                COALESCE(roke_account_asset.salvage_value,0) AS "净残值",
                COALESCE(roke_account_asset.net_salvage_rate,0) AS "净残值率(%)",
                roke_department.name AS "使用部门",
                CONCAT(COALESCE(roke_account_asset.method_number,0),'月') AS "持续时间",
                roke_account_asset.depreciation_month AS "折扣月份",
                roke_account_asset.first_depreciation_date AS "首次折旧日期",
                COALESCE(roke_account_asset.area_floor,0) AS "建筑面积",
                roke_account_asset.specification_model AS "规格型号",
                CASE roke_account_asset.method WHEN 'linear' THEN '线性' WHEN 'degressive' THEN '递减' ELSE '加速递减' END AS "计算方法",
                res_users.login AS "录入人",
                roke_account_asset.create_date AS "录入时间"
                FROM roke_account_asset
                LEFT JOIN roke_department ON roke_account_asset.asset_use_department = roke_department.ID
                LEFT JOIN res_users ON roke_account_asset.create_uid = res_users.ID
                WHERE
                roke_account_asset.create_date between :create_date and :create_date
                AND roke_account_asset.code LIKE :roke_account_asset.code
                AND roke_account_asset.name LIKE :roke_account_asset.name
                AND roke_department.name = :roke_department.name
            </field>
            <field name="top_menu_id" ref="roke_account_asset.view_account_asset_menu"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '录入时间',
                    'field_id': ref('roke_account_asset.field_roke_account_asset__create_date'),
                    'sql_decider': 'between',
                    'sql_data': ' roke_account_asset.create_date between :create_date and  :create_date ',
                    'sql_field_mark': ':create_date',
                    'sql_field_mark_type': 'datetime'
                }),
                (0, 0, {
                    'name': '资产编号',
                    'field_id': ref('roke_account_asset.field_roke_account_asset__code'),
                    'sql_decider': 'LIKE',
                    'sql_data': ' roke_account_asset.code LIKE :roke_account_asset.code ',
                    'sql_field_mark': ':roke_account_asset.code',
                    'sql_field_mark_type': 'char'
                }),
                (0, 0, {
                    'name': '资产名称',
                    'field_id': ref('roke_account_asset.field_roke_account_asset__name'),
                    'sql_decider': 'LIKE',
                    'sql_data': ' roke_account_asset.name LIKE :roke_account_asset.name ',
                    'sql_field_mark': ':roke_account_asset.name',
                    'sql_field_mark_type': 'char'
                }),
                (0, 0, {
                    'name': '使用部门',
                    'field_id': ref('roke_mes_base.field_roke_department__name'),
                    'sql_inherit_field_id': ref('roke_account_asset.field_roke_account_asset__asset_use_department'),
                    'sql_decider': '=',
                    'sql_data': ' roke_department.name = :roke_department.name ',
                    'sql_field_mark': ':roke_department.name',
                    'sql_field_mark_type': 'many2one'
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "资产编号",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__code"),
                    "sequence": 1,
                    "sql_order_by_data": "roke_account_asset.code"
                }),
                (0, 0, {
                    "name": "资产名称",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__name"),
                    "sequence": 2,
                    "sql_order_by_data": "roke_account_asset.name"
                }),
                (0, 0, {
                    "name": "资产类别",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__asset_category"),
                    "sequence": 3,
                    "sql_order_by_data": "roke_account_asset.asset_category"
                }),
                (0, 0, {
                    "name": "资产位置",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__asset_location"),
                    "sequence": 4,
                    "sql_order_by_data": "roke_account_asset.asset_location"
                }),
                (0, 0, {
                    "name": "资产来源",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__asset_origin"),
                    "sequence": 5,
                    "sql_order_by_data": "roke_account_asset.asset_origin"
                }),
                (0, 0, {
                    "name": "资产用途",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__asset_purpose"),
                    "sequence": 6,
                    "sql_order_by_data": "roke_account_asset.asset_purpose"
                }),
                (0, 0, {
                    "name": "资产数量",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__asset_qty"),
                    "sequence": 7,
                    "sql_order_by_data": "COALESCE(roke_account_asset.asset_qty,0)"
                }),
                (0, 0, {
                    "name": "资产原值",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__original_value"),
                    "sequence": 8,
                    "sql_order_by_data": "COALESCE(roke_account_asset.original_value,0)"
                }),
                (0, 0, {
                    "name": "累计折旧",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__residual_total"),
                    "sequence": 9,
                    "sql_order_by_data": "COALESCE(roke_account_asset.residual_total,0)"
                }),
                (0, 0, {
                    "name": "资产净值",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__value_residual"),
                    "sequence": 10,
                    "sql_order_by_data": "COALESCE(roke_account_asset.value_residual,0)"
                }),
                (0, 0, {
                    "name": "净残值",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__salvage_value"),
                    "sequence": 11,
                    "sql_order_by_data": "COALESCE(roke_account_asset.salvage_value,0)"
                }),
                (0, 0, {
                    "name": "净残值率(%)",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__net_salvage_rate"),
                    "sequence": 12,
                    "sql_order_by_data": "COALESCE(roke_account_asset.net_salvage_rate,0)"
                }),
                (0, 0, {
                    "name": "使用部门",
                    "field_id": ref("roke_mes_base.field_roke_department__name"),
                    "sequence": 13,
                    "sql_order_by_data": "roke_department.name"
                }),
                (0, 0, {
                    "name": "持续时间",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__method_number"),
                    "sequence": 14,
                    "sql_order_by_data": "CONCAT(COALESCE(roke_account_asset.method_number,0),&apos;月&apos;)"
                }),
                (0, 0, {
                    "name": "折扣月份",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__depreciation_month"),
                    "sequence": 15,
                    "sql_order_by_data": "roke_account_asset.depreciation_month"
                }),
                (0, 0, {
                    "name": "首次折旧日期",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__first_depreciation_date"),
                    "sequence": 16,
                    "sql_order_by_data": "roke_account_asset.first_depreciation_date"
                }),
                (0, 0, {
                    "name": "建筑面积",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__area_floor"),
                    "sequence": 17,
                    "sql_order_by_data": "COALESCE(roke_account_asset.area_floor,0)"
                }),
                (0, 0, {
                    "name": "规格型号",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__specification_model"),
                    "sequence": 18,
                    "sql_order_by_data": "roke_account_asset.specification_model"
                }),
                (0, 0, {
                    "name": "计算方法",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__method"),
                    "sequence": 19,
                    "sql_order_by_data": "CASE roke_account_asset.method WHEN &apos;linear&apos; THEN &apos;线性&apos; WHEN &apos;degressive&apos; THEN &apos;递减&apos; ELSE &apos;加速递减&apos; END"
                }),
                (0, 0, {
                    "name": "录入人",
                    "field_id": ref("base.field_res_users__login"),
                    "sequence": 20,
                    "sql_order_by_data": "res_users.login"
                }),
                (0, 0, {
                    "name": "录入时间",
                    "field_id": ref("roke_account_asset.field_roke_account_asset__create_date"),
                    "sequence": 21,
                    "sql_order_by_data": "roke_account_asset.create_date"
                })
            ]'/>
        </record>
    </data>
    <data noupdate="0">
        <record id="roke_query_account_asset_report_investigation_line_01" model="roke.query.investigation.model">
            <field name="sql_investigation_id" ref="roke_account_asset.roke_query_account_asset_report"/>
            <field name="investigation_type">系统表单</field>
            <field name="model_id" ref="roke_account_asset.model_roke_account_asset"/>
            <field name="model_view_id" ref="roke_account_asset.view_roke_account_asset_form"/>
            <field name="investigation_way">单击指定字段联查</field>
            <field name="name">资产编号</field>
        </record>
    </data>
</odoo>
