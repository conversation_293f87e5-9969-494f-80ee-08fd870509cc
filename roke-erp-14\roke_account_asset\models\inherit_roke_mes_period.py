# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_compare


class InheritAccountCorVoucher(models.Model):
    _inherit = 'accountcore.voucher'

    asset_id = fields.Many2one('roke.mes.account.asset', string='资产')
    is_depreciation = fields.Boolean(string='折旧', default=False)
    amount_total = fields.Float('合计')
    asset_remaining_value = fields.Float(string='可贬值价值', copy=False)
    asset_depreciated_value = fields.Float(string='累计折旧', copy=False)
    depreciation_date = fields.Date(string='折旧日期')
