import calendar
from dateutil.relativedelta import relativedelta
from datetime import datetime
from dateutil.relativedelta import relativedelta
from math import copysign

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.tools import float_compare, float_is_zero, float_round
from odoo.tools.misc import DEFAULT_SERVER_DATE_FORMAT



class RokeAccountAsset(models.Model):
    _name = 'roke.account.asset'
    _description = '资产档案'
    _inherit = ['mail.thread', "mail.activity.mixin"]

    depreciation_entries_count = fields.Integer(compute='_entry_count', string='已发布的折旧记录数量')
    gross_increase_count = fields.Integer(compute='_entry_count', string='增加的总值数量',
                                          help="增加资产价值的资产数量")
    total_depreciation_entries_count = fields.Integer(compute='_entry_count', string='折旧记录总数',
                                                      help="折旧记录的数量（已发布或未发布）")
    code = fields.Char(string='资产编号', tracking=True, copy=False)
    name = fields.Char(string='资产名称')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    state = fields.Selection(
        [('model', '模型'), ('draft', '草稿'), ('open', '运行中'), ('paused', '暂停中'), ('close', '已关闭')], '状态',
        copy=False, default='draft',
        help="当创建资产时，状态为“草稿”。\n"
             "如果确认资产，则状态变为“运行中”，可在会计中发布折旧行。\n"
             "可以手动设置“暂停中”状态，暂停资产的折旧一段时间。\n"
             "当折旧结束时，可以手动关闭资产。如果最后一行折旧已发布，则资产自动进入该状态。")
    active = fields.Boolean(default=True)
    asset_type = fields.Selection([('sale', '销售：收入确认'), ('purchase', '购买：资产'), ('expense', '预付费用')],
                                  index=True, readonly=False, default='purchase', string='资产类型')

    # 价值
    original_value = fields.Float(string="资产原值")
    book_value = fields.Float(string='账面价值', compute='_compute_book_value',
                              help="可折旧价值、残值和所有价值增加项的总和")
    value_residual = fields.Float(string='资产净值', compute='_compute_value_residual', store=True)
    salvage_value = fields.Float(string='净残值', help="您计划拥有但无法折旧的金额。")
    gross_increase_value = fields.Float(string="总增加值")
    # 折旧参数
    method = fields.Selection(
        [('linear', '线性'), ('degressive', '递减'), ('degressive_then_linear', '加速递减')],
        string='计算方法', default='linear',
        help="选择计算折旧行金额的方法。\n"
             "  * 线性: 根据资产净值 / 折旧次数进行计算\n"
             "  * 递减: 根据残值 * 递减系数进行计算\n"
             "  * 加速递减: 类似递减计算，但最低折旧值等于线性值。")
    method_number = fields.Integer(string='折旧次数', default=5, help="折旧资产所需的折旧次数")
    method_period = fields.Selection([('1', '月'), ('12', '年')], string='每期月数', default='1',
                                     help="两次折旧之间的时间间隔")
    method_progress_factor = fields.Float(string='递减系数', default=0.3)
    pro_rata = fields.Boolean(string='按比例计算',
                              help='指示此资产的第一个折旧入账应该从资产日期（购买日期）开始，而不是从每年的1月份或财务年度的开始日期')
    pro_rata_date = fields.Date(string='按比例计算日期')
    account_asset_id = fields.Many2one('accountcore.account', string='固定资产科目')
    account_depreciation_id = fields.Many2one('accountcore.account', string='固定资产科目')
    account_depreciation_expense_id = fields.Many2one('accountcore.account', string='费用科目')
    voucher_id = fields.Many2one('accountcore.voucher', string='凭证')
    # =================================
    depreciation_move_ids = fields.One2many('roke.account.depreciation', 'asset_id', string='折旧行')
    original_move_line_ids = fields.One2many('roke.account.depreciation.line', 'asset_id', string='账目凭证项')
    account_analytic_id = fields.Many2one('accountcore.account', string='分析科目')
    analytic_tag_ids = fields.Many2one('accountcore.glob_tag', string='分析标签')
    # 模型相关字段
    user_type_id = fields.Many2one('accountcore.accountclass', string='科目类型')

    # 资本收益
    parent_id = fields.Many2one('roke.account.asset', help="当资产的价值增长时，它具有一个父级")
    children_ids = fields.One2many('roke.account.asset', 'parent_id', help="这些资产是该资产的价值增长结果")
    org_id = fields.Many2one('accountcore.org', string='机构')
    first_depreciation_date = fields.Date(string='首次折旧日期', default=fields.Date.today(),
                                          help='请注意，对于按比例计算的资产，此日期不会影响首个分录的计算。它只是更改其会计日期。')
    acquisition_date = fields.Date(string='购买日期')
    disposal_date = fields.Date(string='处理日期')
    currency_id = fields.Many2one('res.currency', default=lambda x: x.env.user.company_id.currency_id, string='币别')
    next_number = fields.Integer(string='本次折旧次数', default=1)

    asset_category = fields.Char(string='资产类别')
    asset_origin = fields.Char(string='资产来源')
    asset_purpose = fields.Char(string='资产用途')
    asset_use_department = fields.Many2one('roke.department', string='使用部门')
    asset_qty = fields.Float(string='资产数量', default=1)
    area_floor = fields.Float(string='建筑面积')
    residual_total = fields.Float(string='累计折旧')
    reserve_impairment = fields.Float(string='减值准备')
    net_salvage_rate = fields.Float(string='净残值率(%)', compute='_compute_net_salvage_rate', store=True)
    depreciation_month = fields.Integer(string='折旧月份')
    monthly_depreciation_rate = fields.Float(string='月折旧率(%)', compute='_compute_depreciation')
    monthly_depreciation = fields.Float(string='月折旧额', compute='_compute_depreciation')
    annual_depreciation_rate = fields.Float(string='年折旧率(%)', compute='_compute_depreciation')
    annual_depreciation = fields.Float(string='年折旧额', compute='_compute_depreciation')
    asset_location = fields.Char(string='资产位置')
    specification_model = fields.Char(string='规格型号')

    @api.constrains('depreciation_month', 'original_value')
    def _check_depreciation_month(self):
        for rec in self:
            if rec.depreciation_month <= 0:
                raise UserError('折旧月份必须大于0')
            if rec.original_value <= 0:
                raise UserError('资产原值必须大于0')

    @api.onchange('method')
    def _onchange_method(self):
        if self.method:
            self.first_depreciation_date = None

    @api.onchange('salvage_value')
    def _onchange_salvage_value(self):
        self.value_residual = self.original_value - self.salvage_value

    @api.onchange('original_value')
    def _onchange_value(self):
        self._set_value()

    def _set_value(self):
        for record in self:
            record.acquisition_date = min(record.original_move_line_ids.mapped('date') + [
                record.pro_rata_date or record.first_depreciation_date or fields.Date.today()])
            record.first_depreciation_date = record._get_first_depreciation_date()
            record.value_residual = record.original_value - record.salvage_value
            record.name = record.name or (record.original_move_line_ids and record.original_move_line_ids[0].name or '')
            if not record.asset_type and 'asset_type' in self.env.context:
                record.asset_type = self.env.context['asset_type']
            if not record.asset_type and record.original_move_line_ids:
                account = record.original_move_line_ids.account_id
                record.asset_type = account.asset_type
            # record._onchange_depreciation_account()

    @api.depends('value_residual', 'salvage_value', 'children_ids.book_value')
    def _compute_book_value(self):
        for record in self:
            record.book_value = record.value_residual + record.salvage_value + sum(
                record.children_ids.mapped('book_value'))
            record.gross_increase_value = sum(record.children_ids.mapped('original_value'))

    @api.depends('salvage_value', 'original_value')
    def _compute_net_salvage_rate(self):
        """
        净残值率=净残值/资产原值。
        """
        for record in self:
            if record.original_value:
                record.net_salvage_rate = record.salvage_value / record.original_value * 100
            else:
                record.net_salvage_rate = 0

    @api.depends('original_value', 'reserve_impairment', 'residual_total')
    def _compute_value_residual(self):
        """
        资产净值=资产原值-累计折旧-减值准备
        """
        for record in self:
            record.value_residual = record.original_value - record.residual_total - record.reserve_impairment

    @api.depends('original_value', 'method', 'method_number', 'salvage_value', 'reserve_impairment',
                 'depreciation_month',
                 'residual_total')
    def _compute_depreciation(self):
        """
        计算月折旧额/年折旧额/月折旧率/年折旧率
        """
        for rec in self:
            if rec.method == 'linear':
                # 月折旧额=（资产原值-净残值）/使用期限（月）；年折旧额=月折旧额*12；月折旧率=月折旧额/资产原值*100%; 年折旧率=年折旧额/资产原值*100%
                rec.monthly_depreciation = (
                                                   rec.original_value - rec.salvage_value) / rec.method_number if rec.method_number else 0
                rec.annual_depreciation = rec.monthly_depreciation * 12
                rec.monthly_depreciation_rate = rec.monthly_depreciation / rec.original_value * 100 if rec.original_value else 0
                rec.annual_depreciation_rate = rec.annual_depreciation / rec.original_value * 100 if rec.original_value else 0
            elif rec.method == 'degressive':
                # 月折旧率=2/使用期限（月）；月折旧额=（资产原值-累计折旧-减值准备）*月折旧率；年折旧率=月折旧率*12，年折旧额=月折旧额*12。
                rec.monthly_depreciation_rate = 2 / rec.method_number % 100
                rec.monthly_depreciation = (
                                                   rec.original_value - rec.residual_total - rec.reserve_impairment) * rec.monthly_depreciation_rate
                rec.annual_depreciation_rate = rec.monthly_depreciation_rate * 12
                rec.annual_depreciation = rec.monthly_depreciation * 12
            else:
                # 月折旧率=（使用期限（月）/12） /(（使用期限（月）/12)*（使用期限（月）/12+1）/2）)；
                # 年折旧率=月折旧率*12；
                # 月折旧额=（资产原值-减值准备-净残值）* 月折旧率；
                # 年折旧额=月折旧额*12。
                rec.monthly_depreciation_rate = (rec.method_number / 12) / (
                        rec.method_number / 12 * ((rec.method_number / 12 + 1) / 2))
                rec.annual_depreciation_rate = rec.monthly_depreciation_rate * 12
                rec.monthly_depreciation = (
                                                   rec.original_value - rec.reserve_impairment - rec.salvage_value) * rec.monthly_depreciation_rate
                rec.annual_depreciation = rec.monthly_depreciation * 12

    def _get_first_depreciation_date(self, vals={}):
        pre_depreciation_date = fields.Date.to_date(
            vals.get('acquisition_date') or vals.get('date') or min(self.original_move_line_ids.mapped('date'),
                                                                    default=self.acquisition_date or fields.Date.today()))
        depreciation_date = pre_depreciation_date + relativedelta(day=31)
        # ... or fiscalyear depending the number of period
        if '12' in (self.method_period, vals.get('method_period')):
            depreciation_date = depreciation_date + relativedelta(month=int(12))
            depreciation_date = depreciation_date + relativedelta(day=31)
            if depreciation_date < pre_depreciation_date:
                depreciation_date = depreciation_date + relativedelta(years=1)
        return depreciation_date

    @api.depends('depreciation_move_ids.state', 'parent_id')
    def _entry_count(self):
        for asset in self:
            res = self.env['roke.account.depreciation'].search_count(
                [('asset_id', '=', asset.id), ('state', '=', '已过账')])
            asset.depreciation_entries_count = res or 0
            asset.total_depreciation_entries_count = len(asset.depreciation_move_ids)
            asset.gross_increase_count = len(asset.children_ids)

    def validate(self):
        # self.compute_depreciation_board()
        self.write({'state': 'open'})
        # fields = [
        #     'method',
        #     'method_number',
        #     'method_period',
        #     'method_progress_factor',
        #     'salvage_value',
        #     'original_move_line_ids',
        # ]
        # ref_tracked_fields = self.env['roke.account.asset'].fields_get(fields)
        # self.write({'state': 'open'})
        # for asset in self:
        #     tracked_fields = ref_tracked_fields.copy()
        #     if asset.method == 'linear':
        #         del (tracked_fields['method_progress_factor'])
        #     dummy, tracking_value_ids = asset._message_track(tracked_fields, dict.fromkeys(fields))
        #     asset_name = {
        #         'purchase': (_('资产创建'), _('已为此移动创建资产：')),
        #         'sale': (_('已创建待摊收入'), _('已为此移动创建待摊收入：')),
        #         'expense': (_('已创建待摊费用'), _('已为此移动创建待摊费用：')),
        #     }[asset.asset_type]
        #     msg = asset_name[1] + ' <a href=# data-oe-model=roke.mes.account.asset data-oe-id=%d>%s</a>' % (
        #         asset.id, asset.name)
        #     asset.message_post(body=asset_name[0], tracking_value_ids=tracking_value_ids)
        #     # for entry in asset.original_move_line_ids.mapped('voucher'):
        #     #     entry.message_post(body=msg)
        #     if not asset.depreciation_move_ids:
        #         asset.compute_depreciation_board()
        #     # asset._check_depreciations()
        #     asset.depreciation_move_ids.write({'auto_post': True})

    def _recompute_board(self, depreciation_number, starting_sequence, amount_to_depreciate, depreciation_date,
                         already_depreciated_amount, amount_change_ids):
        """
        折旧次数、序号、折旧金额、折旧日期、已折旧金额、折旧明细
        """
        self.ensure_one()
        residual_amount = amount_to_depreciate
        # Remove old unposted depreciation lines. We cannot use unlink() with One2many field
        move_vals = []
        pro_rata = self.pro_rata and not self.env.context.get("ignore_pro_rata")
        if amount_to_depreciate != 0.0:
            for asset_sequence in range(starting_sequence + 1, depreciation_number + 1):
                while amount_change_ids and amount_change_ids[0].date <= depreciation_date:
                    residual_amount -= amount_change_ids[0].amount_total
                    amount_to_depreciate -= amount_change_ids[0].amount_total
                    already_depreciated_amount += amount_change_ids[0].amount_total
                    amount_change_ids[0].write({
                        'asset_remaining_value': float_round(residual_amount,
                                                             precision_rounding=self.currency_id.rounding),
                        'accumulated_depreciation': amount_to_depreciate - residual_amount + already_depreciated_amount,
                    })
                    amount_change_ids -= amount_change_ids[0]
                amount = self._compute_board_amount(asset_sequence, residual_amount, amount_to_depreciate,
                                                    depreciation_number, starting_sequence, depreciation_date)
                pro_rata_factor = 1
                move_ref = self.name + ' (%s/%s)' % (
                    pro_rata and asset_sequence - 1 or asset_sequence, self.method_number)
                if pro_rata and asset_sequence == 1:
                    move_ref = self.name + ' ' + _('(pro_rata entry)')
                    first_date = self.pro_rata_date
                    if int(self.method_period) % 12 != 0:
                        month_days = calendar.monthrange(first_date.year, first_date.month)[1]
                        days = month_days - first_date.day + 1
                        pro_rata_factor = days / month_days
                    else:
                        total_days = (depreciation_date.year % 4) and 365 or 366
                        days = (self.company_id.compute_fiscalyear_dates(first_date)['date_to'] - first_date).days + 1
                        pro_rata_factor = days / total_days
                amount = self.currency_id.round(amount * pro_rata_factor)
                if float_is_zero(amount, precision_rounding=self.currency_id.rounding):
                    continue
                residual_amount -= amount

                move_vals.append(self.env['roke.account.depreciation']._prepare_move_for_asset_depreciation({
                    'amount': amount,
                    'asset_id': self,
                    'move_ref': move_ref,
                    'date': depreciation_date,
                    'asset_remaining_value': float_round(residual_amount, precision_rounding=self.currency_id.rounding),
                    'asset_depreciated_value': amount_to_depreciate - residual_amount + already_depreciated_amount,
                }))

                depreciation_date = depreciation_date + relativedelta(months=+int(self.method_period))
                # datetime doesn't take into account that the number of days is not the same for each month
                if (not self.pro_rata or self.env.context.get("ignore_pro_rata")) and int(self.method_period) % 12 != 0:
                    max_day_in_month = calendar.monthrange(depreciation_date.year, depreciation_date.month)[1]
                    depreciation_date = depreciation_date.replace(day=max_day_in_month)
        return move_vals

    def check_period(self):
        """判断该核算机构折旧"""
        # 查询当天是否在核算机构的会计期间内，且月结未完毕
        res = self.env['accountcore.org.period.line'].search(
            [('start_date', '<=', fields.Date.today()), ('end_date', '>=', fields.Date.today()),
             ('is_over', '=', False), ('period_id', '=', self.org_id.id)])
        if res:
            # 判断是否存在本月的折旧数据
            date_list = self.env['roke.account.depreciation'].search([('asset_id', '=', self.id)]).mapped('date')
            # 获取当月
            current_month = datetime.now().month
            for date_m in date_list:
                if current_month == date_m.month:
                    raise UserError('该机构当前会计期间已进行折旧，无法重复操作')
            # 判断上个期间是否月结，如果没有月结也不能折旧
            # last_res = self.env['accountcore.org.period.line'].search(
            #     [('is_over', '=', False), ('period_id', '=', self.org_id.id), ('id', '<', res.id)], order='id desc',
            #     limit=1)
            # if not last_res:

        else:
            raise UserError('未查到该机构会计期间数据或已月结完毕，请检查')

    def compute_dapreciation_boards(self):
        """
        计算折旧
        1、平均年限法
        折旧额=（资产原值-净残值）/使用期限（月）
        2、双倍余额递减法
        月折旧率=2/使用期限（月）
        折旧额=（资产原值-累计折旧-减值准备）*月折旧率，
        最后两年的月折旧额=(资产原值-累计折旧-减值准备-净残值）/2/12
        3、年数总和法
        年折旧率=(使用期限（年）-已计提年数)/(使用期限（年）*使用期限
        （年）+1)/2
        月折旧率=年折旧率/12
        折旧额=（资产原值-累计折旧-减值准备-净残值）*月折旧率
        """
        # 校验是否可以折旧
        # 根据上次折旧日期和当前日期以及折旧月份计算折旧次数
        # 上次折旧日期
        account_depreciation_record = self.env['roke.account.depreciation'].search([('asset_id', '=', self.id)],
                                                                                   order='create_date desc', limit=1)
        last_depreciation_date = False
        if account_depreciation_record:
            last_depreciation_date = account_depreciation_record.create_date
        difference_month = self.diff_in_months(datetime.now(),
                                               last_depreciation_date) if last_depreciation_date else self.depreciation_month
        # 判断是否可以折旧
        if difference_month <= 0 or self.depreciation_month <= 0:
            raise UserError('本月已折旧或无需折旧')
        else:
            # 计算折旧次数
            current_depreciation_number = difference_month if difference_month < self.depreciation_month else self.depreciation_month
            # 计算折旧金额
            # 1、平均年限法
            if self.method == 'linear':
                depreciation_amount = (self.original_value - self.salvage_value) / self.method_number
            # 2、双倍余额递减法
            elif self.method == 'degressive':
                # 判断是否是最后两年折旧
                if self.depreciation_month - len(self.depreciation_move_ids.ids) <= 24:
                    depreciation_amount = (
                                                  self.original_value - self.residual_total - self.reserve_impairment - self.salvage_value) / 2 / 12
                else:
                    depreciation_amount = (
                                                  self.original_value - self.residual_total - self.reserve_impairment) * 2 / self.method_number
            # 3、年数总和法
            else:
                depreciation_amount = (self.method_number - len(self.depreciation_move_ids.ids)) / 12 / (
                        (
                                self.method_number / 12) * (
                                (
                                        self.method_number / 12) + 1) / 2)
            self.residual_total += depreciation_amount
            # 生成折旧记录
            self.create_account_depreciation(depreciation_amount)

    def create_account_depreciation(self, depreciation_amount):
        """生成折旧记录"""
        line1 = {
            "asset_id": self.id,
            "account_id": self.account_asset_id.id,
            "partner_id": None,
            "tags": "",
            "debit": 0,
            "credit": depreciation_amount,
            "date": fields.Date.today(),
            "debit_credit_type": "借"
        }
        line2 = {
            "asset_id": self.id,
            "account_id": self.account_depreciation_expense_id.id,
            "partner_id": None,
            "tags": "",
            "debit": depreciation_amount,
            "credit": 0,
            "date": fields.Date.today(),
            "debit_credit_type": "贷"
        }
        # 可贬值值
        value_r = self.original_value - self.residual_total - self.salvage_value,
        data = {
            "asset_id": self.id,
            "name": self.name,
            "code": "",
            "date": fields.Date.today(),
            "amount_total": depreciation_amount,
            "accumulated_depreciation": sum(
                self.depreciation_move_ids.mapped('amount_total')) + depreciation_amount,
            "asset_remaining_value": value_r[0],
            "company_id": self.company_id.id,
            "state": '草稿',
            'sequence': self.next_number,
            "line_ids": [(0, 0, line1), (0, 0, line2)],
        }
        self.env['roke.account.depreciation'].create(data)


    def diff_in_months(self, date1, date2):
        r = relativedelta(date2, date1)
        return r.years * 12 + r.months

    def compute_depreciation_board(self):
        """
        折旧计算
        4.根据折旧方法的数量和是否按比例计算折旧，计算起始序列号和待折旧金额。
        5.确定第一次计提折旧的日期，如果已存在先前验证的折旧明细，则起始日期为最后一个明细的日期加上折旧方法的周期。
        6.准备对depreciation_move_ids进行操作的命令列表。
        7.调用_recompute_board方法重新计算折旧表的行，得到一个包含每行值的列表。
        8.根据行的值列表创建新的account.move对象，并将其添加到commands列表中。
        9.最后，将commands列表写入数据库，并更新当前对象的depreciation_move_ids字段。
        """
        self.check_period()
        # 如果是会计期间折旧走其他逻辑
        self.period_depreciation()
        return
        # self.ensure_one()
        # # 1.首先，从depreciation_move_ids中筛选出所有is_depreciation(折旧)为True的折旧明细，并按日期排序，保存在amount_change_ids中。
        # amount_change_ids = self.depreciation_move_ids
        # # .filtered(lambda x: x.is_depreciation).sorted(
        # #     key=lambda l: l.voucherdate)
        # # 2.从depreciation_move_ids中筛选出状态为已过账且asset_value_change为False的折旧明细，并按日期排序，保存在posted_depreciation_move_ids中。
        # posted_depreciation_move_ids = self.depreciation_move_ids.filtered(
        #     lambda x: x.state == 'reviewed' and not x.is_depreciation).sorted(key=lambda l: l.voucherdate)
        # # 3.累计已计提的折旧金额，即将posted_depreciation_move_ids中的每个明细的amount_total相加得到already_depreciated_amount。
        # already_depreciated_amount = sum([m.amount_total for m in posted_depreciation_move_ids])
        # # 4.根据折旧方法的数量和是否按比例计算折旧，计算起始序列号和待折旧金额。
        # depreciation_number = self.method_number
        # if self.pro_rata:
        #     depreciation_number += 1
        # starting_sequence = 0
        # amount_to_depreciate = self.value_residual + sum([m.amount_total for m in amount_change_ids])
        # depreciation_date = self.first_depreciation_date
        # # if we already have some previous validated entries, starting date is last entry + method period
        # if posted_depreciation_move_ids and posted_depreciation_move_ids[-1].voucherdate:
        #     last_depreciation_date = fields.Date.from_string(posted_depreciation_move_ids[-1].voucherdate)
        #     if last_depreciation_date > depreciation_date:  # in case we unpause the asset
        #         depreciation_date = last_depreciation_date + relativedelta(months=+int(self.method_period))
        # commands = [(2, line_id.id, False) for line_id in
        #             self.depreciation_move_ids.filtered(lambda x: x.state == 'creating')]
        # # 折旧次数、序号、折旧金额、折旧日期、已折旧金额、折旧明细
        # newlines = self._recompute_board(depreciation_number, starting_sequence, amount_to_depreciate,
        #                                  depreciation_date, already_depreciated_amount, amount_change_ids)
        # newline_vals_list = []
        # for newline_vals in newlines:
        #     del (newline_vals['amount_total'])
        #     newline_vals_list.append(newline_vals)
        # # TODO 创建明细
        # new_voucher = self.env['roke.account.depreciation'].create(newline_vals_list)
        # for v in new_voucher:
        #     commands.append((4, v.id))
        # return self.write({'depreciation_move_ids': commands})

    # def period_depreciation(self):
    #     """
    #     1.获取资产的机构，根据机构的当前会计期间判断是否已经折旧，如果没有折旧就进行折旧
    #     2.本资产档案没有折旧过，根据折旧次数进行计算每次折旧一次
    #     """
    #     if not self.org_id:
    #         raise UserError('请选择机构')
    #     a = 0 if self.method_period == '1' else round(self.value_residual / self.method_number, 2)
    #
    #     # 获取当前机构的会计期间进行折旧
    #     if self.org_id.activation_period.depreciation:
    #         raise UserError('当前期间已折旧')
    #     # 是否存在折旧记录
    #     if len(self.depreciation_move_ids) == 0:
    #         pass

    def compute_fiscalyear_dates(self, current_date):
        self.ensure_one()
        year = current_date.year
        last_day = calendar.monthrange(year, 12)[-1]
        date_to = datetime(year, 12, last_day).date()
        return {'date_to': date_to}

    def period_depreciation(self):
        # 如果是会计期间类型的才这样计算折旧
        if not self.org_id:
            raise UserError('请选择机构')
        number_method_number = self.method_number
        if self.pro_rata:
            number_method_number += 1
        if self.next_number > number_method_number:
            return
        if self.state == 'draft':
            self.state = 'open'
        # 当前序列号
        asset_sequence = self.next_number
        # 上次折旧信息序列号
        sequence = asset_sequence - 1
        # 获取最后一条折旧信息
        account_depreciation_id = None
        if sequence:
            account_depreciation_id = self.env['roke.account.depreciation'].search([
                '&', ('asset_id', '=', self.id),
                ('sequence', '=', sequence)
            ])
        # 折旧金额（计算）
        if account_depreciation_id:
            residual_amount = account_depreciation_id.asset_remaining_value
        else:
            residual_amount = self.value_residual
        #   折旧金额(总)
        amount_to_depreciate = self.value_residual
        # 折旧次数
        depreciation_number = self.method_number
        if self.pro_rata:
            # 折旧次数+1
            depreciation_number += 1
        # 开始序列号
        starting_sequence = 0
        depreciation_date = fields.Date.today()
        amount = self._compute_board_amount(asset_sequence, residual_amount, amount_to_depreciate,
                                   depreciation_number, starting_sequence, depreciation_date)
        prorata_factor = 1
        prorata = self.pro_rata
        move_ref = self.name + ' (%s/%s)' % (prorata and asset_sequence - 1 or asset_sequence, self.method_number)
        if prorata and asset_sequence == 1:
            move_ref = self.name + ' ' + _('(按比例计算)')
            first_date = self.pro_rata_date
            if int(self.method_period) % 12 != 0:
                month_days = calendar.monthrange(first_date.year, first_date.month)[1]
                days = month_days - first_date.day + 1
                prorata_factor = days / month_days
            else:
                total_days = (depreciation_date.year % 4) and 365 or 366
                days = (self.compute_fiscalyear_dates(first_date)['date_to'] - first_date).days + 1
                prorata_factor = days / total_days
        amount = self.currency_id.round(amount * prorata_factor)
        # 计算每期的价值
        # average_value = 0 if self.method_period == 1 else round(self.value_residual / self.method_number, 2)
        line1 = {
            "asset_id": self.id,
            "account_id": self.account_asset_id.id,
            "partner_id": None,
            "tags": "",
            "debit": amount,
            "credit": 0,
            "date": fields.Date.today()
        }
        line2 = {
            "asset_id": self.id,
            "account_id": self.account_depreciation_expense_id.id,
            "partner_id": None,
            "tags": "",
            "debit": 0,
            "credit": amount,
            "date": fields.Date.today()
        }
        # 可贬值值
        value_r = self.value_residual - sum(self.depreciation_move_ids.mapped('amount_total')) - amount,
        # if
        data = {
            "asset_id": self.id,
            "name": self.name,
            # "ref": "%s(%s/%s)" % (self.name, self.next_number, self.method_number),
            "ref": move_ref,
            "code": "",
            "date": fields.Date.today(),
            "amount_total": amount,
            "accumulated_depreciation": sum(
                self.depreciation_move_ids.mapped('amount_total')) + amount,
            "asset_remaining_value": value_r[0],
            "voucher_id": self.voucher_id.id,
            "company_id": self.company_id.id,
            "state": '草稿',
            'sequence': self.next_number,
            "line_ids": [(0, 0, line1), (0, 0, line2)],
        }
        self.env['roke.account.depreciation'].create(data)
        if self.next_number == 1:
            self.first_depreciation_date = fields.Date.today()
        self.next_number += 1

    def action_set_to_close(self):
        pass

    def set_to_draft(self):
        """置为草稿"""
        self.write({'state': 'draft', 'next_number': 1, 'depreciation_move_ids': None})

    def set_to_running(self):
        pass

    def action_asset_pause(self):
        pass

    def resume_after_pause(self):
        pass

    def action_asset_modify(self):
        pass

    def _compute_board_amount(self, computation_sequence, residual_amount, total_amount_to_depr, max_depreciation_nb,
                              starting_sequence, depreciation_date):
        amount = 0
        if computation_sequence == max_depreciation_nb:
            # last depreciation always takes the asset residual amount
            amount = residual_amount
        else:
            if self.method in ('degressive', 'degressive_then_linear'):
                amount = residual_amount * self.method_progress_factor
            if self.method in ('linear', 'degressive_then_linear'):
                nb_depreciation = max_depreciation_nb - starting_sequence
                if self.pro_rata:
                    nb_depreciation -= 1
                linear_amount = min(total_amount_to_depr / nb_depreciation, residual_amount)
                if self.method == 'degressive_then_linear':
                    amount = max(linear_amount, amount)
                else:
                    amount = linear_amount
        return amount
