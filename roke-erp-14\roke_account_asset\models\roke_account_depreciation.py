import calendar
from dateutil.relativedelta import relativedelta
from math import copysign

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.tools import float_compare, float_is_zero, float_round


class RokeAccountDepreciation(models.Model):
    _name = 'roke.account.depreciation'
    _description = '折旧信息'
    _inherit = ['mail.thread', "mail.activity.mixin"]

    asset_id = fields.Many2one('roke.account.asset', string='资产档案')
    name = fields.Char('名称')
    ref = fields.Char('参考')
    code = fields.Char('凭证号')
    date = fields.Date('折旧日期')
    amount_total = fields.Float('折旧金额')
    accumulated_depreciation = fields.Float('累计折旧')
    asset_remaining_value = fields.Float('剩余可贬值价值')
    voucher_id = fields.Many2one('accountcore.voucher', string='凭证')
    company_id = fields.Many2one('res.company', '公司')
    auto_post = fields.Boolean('自动发布')
    state = fields.Selection([('草稿', '草稿'), ('已过账', '已过账'), ('已取消', '已取消')], string='状态')
    line_ids = fields.One2many('roke.account.depreciation.line', 'depreciation_id', string='折旧明细')
    sequence = fields.Integer(string='序号')

    @api.model
    def _prepare_move_for_asset_depreciation(self, vals):
        """
        组织折旧明细行
        """
        missing_fields = {'asset_id', 'move_ref', 'amount', 'asset_remaining_value',
                          'asset_depreciated_value'} - set(vals)
        if missing_fields:
            raise UserError('缺少字段 {}').format(', '.join(missing_fields))
        # 资产对象
        asset = vals['asset_id']
        # account_analytic_id = asset.account_analytic_id  # 分析科目
        # analytic_tag_ids = asset.analytic_tag_ids  # 分析标签
        depreciation_date = vals.get('date', fields.Date.context_today(self))  # 日期
        company_currency = asset.company_id.currency_id  # 币种-company
        current_currency = asset.currency_id  # 币种-资产
        prec = company_currency.decimal_places
        amount = current_currency._convert(vals['amount'], company_currency, asset.company_id, depreciation_date)
        # 暂时先不考虑币种问题
        line1 = {
            "asset_id": asset.id,
            "account_id": asset.account_asset_id.id,
            "tags": vals.get('move_ref'),
            "debit": amount if float_compare(amount, 0.0, precision_digits=prec) > 0 else 0.0,
            "credit": 0.0 if float_compare(amount, 0.0, precision_digits=prec) > 0 else -amount,
            "date_maturity":  depreciation_date,
            "date": depreciation_date
        }

        line2 = {
            "asset_id": asset.id,
            "account_id": asset.account_depreciation_expense_id.id,
            "tags": vals.get('move_ref'),
            "credit": amount if float_compare(amount, 0.0, precision_digits=prec) > 0 else 0.0,
            "debit": 0.0 if float_compare(amount, 0.0, precision_digits=prec) > 0 else -amount,
            "date_maturity": depreciation_date,
            "date": depreciation_date
        }
        depreciation = {
            "asset_id": asset.id,
            "name": vals.get('move_ref'),
            "ref": vals.get('move_ref'),
            "code": "",
            "date": depreciation_date,
            "amount_total": vals.get('amount'),
            "accumulated_depreciation": vals.get('asset_depreciated_value'),
            "asset_remaining_value": vals.get('asset_remaining_value'),
            "company_id": asset.company_id.id,
            "state": "草稿",
            "line_ids": [(0, 0, line1), (0, 0, line2)],
        }
        print(depreciation)
        return depreciation

    def button_create_accountcore(self):
        # 组装借贷字典
        org_records = self.asset_id.mapped('org_id')
        for org in org_records:
            debit_accountcore_dict = {}
            credit_accountcore_dict = {}
            for rec in self.filtered(lambda r: r.asset_id.org_id.id == org.id):
                for line in rec.line_ids:
                    if line.debit_credit_type == '借':
                        if line.account_id.id not in debit_accountcore_dict:
                            debit_accountcore_dict[line.account_id.id] = line.debit
                        else:
                            debit_accountcore_dict[line.account_id.id] += line.debit
                    else:
                        if line.account_id.id not in credit_accountcore_dict:
                            credit_accountcore_dict[line.account_id.id] = line.credit
                        else:
                            credit_accountcore_dict[line.account_id.id] += line.credit
            # 组装制单数据
            line_values = []
            for key, value in debit_accountcore_dict.items():
                line_values.append((0, 0, {
                    'account': key,
                    'damount': value,
                }))
            for key, value in credit_accountcore_dict.items():
                line_values.append((0, 0, {
                    'account': key,
                    'camount': value,
                }))
            voucher_record = self.env['accountcore.voucher'].create({
                'org': org.id,
                'entrys': line_values,
            })
            for rec in self:
                rec.voucher_id = voucher_record.id


class RokeAccountDepreciationLine(models.Model):
    _name = 'roke.account.depreciation.line'
    _description = '折旧明细'
    _inherit = ['mail.thread', "mail.activity.mixin"]

    depreciation_id = fields.Many2one('roke.account.depreciation', string='折旧信息')
    asset_id = fields.Many2one('roke.account.asset', string='资产档案')
    account_id = fields.Many2one('accountcore.account', string='会计科目')
    partner_id = fields.Many2one('res.partner', string='客户/供应商')
    tags = fields.Char('标签')
    debit = fields.Float('借方')
    credit = fields.Float('贷方')
    date_maturity = fields.Date('到期日期')
    date = fields.Date('日期')
    debit_credit_type = fields.Selection([('借', '借'), ('贷', '贷')], string='借贷类型')
