<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--资产档案-->
    <!--search-->
    <record id="view_roke_account_asset_search" model="ir.ui.view">
        <field name="name">roke.account.asset.search</field>
        <field name="model">roke.account.asset</field>
        <field name="arch" type="xml">
            <search string="资产档案">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_account_asset_tree" model="ir.ui.view">
        <field name="name">roke.account.asset.tree</field>
        <field name="model">roke.account.asset</field>
        <field name="arch" type="xml">
            <tree string="资产档案">
                <field name="name"/>
                <field name="first_depreciation_date"/>
                <field name="book_value"/>
                <field name="value_residual"/>
                <field name="company_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_account_asset_form" model="ir.ui.view">
        <field name="name">roke.account.asset.form</field>
        <field name="model">roke.account.asset</field>
        <field name="arch" type="xml">
            <form string="资产档案">
                <header>
                    <button name="validate" states="draft" string="确认" type="object" class="oe_highlight"/>
                    <button type="object" name="compute_dapreciation_boards" string="计算折旧" states="draft"
                            attrs="{'invisible': [('state', '!=', 'open')]}"/>
                    <!--                    <button name="action_set_to_close" string="销售或处置" type="object" class="oe_highlight"-->
                    <!--                            attrs="{'invisible': ['|', ('state', '!=', 'open'), ('asset_type', '!=', 'purchase')]}"/>-->
                    <button name="set_to_draft" string="设置为草稿" type="object"
                            attrs="{'invisible': ['|', ('depreciation_entries_count', '!=', 0), ('state', '!=', 'open')]}"/>
                    <!--                    <button name="set_to_running" string="设置为运行中" type="object"-->
                    <!--                            attrs="{'invisible': [('state', '!=', 'close')]}"/>-->
                    <!--                    <button name="action_asset_pause" string="暂停折旧" type="object"-->
                    <!--                            attrs="{'invisible': ['|', ('state', '!=', 'open'), ('asset_type', '!=', 'purchase')]}"/>-->
                    <!--                    <button name="resume_after_pause" string="恢复折旧" type="object"-->
                    <!--                            attrs="{'invisible': [('state', '!=', 'paused')]}"/>-->
                    <!--                    <button name="action_asset_modify" states="open" string="修改折旧计算" type="object"/>-->
                    <field name="asset_type" invisible="1"/>
                    <field name="depreciation_entries_count" invisible="1"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <group id="g1">
                    <group>
                        <group>
                            <field name="code" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="asset_location" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="asset_use_department" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="method" required="1" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="residual_total" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="net_salvage_rate"/>
                            <field name="annual_depreciation_rate" invisible="1"/>
                        </group>
                        <group>
                            <field name="name" required="1" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="area_floor" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="asset_qty" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <label for="method_number" string="持续时间"/>
                            <div class="o_row">
                                <field name="method_number" required="1"
                                       attrs="{'readonly': [('state','!=', 'draft')]}"/>
                                <field name="method_period" required="1" nolabel="1"
                                       readonly="1" force_save="1"/>
                            </div>
                            <field name="reserve_impairment" invisible="1"/>
                            <field name="monthly_depreciation" invisible="1"/>
                            <field name="account_asset_id" required="1"
                                   attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="org_id" required="1"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="asset_category" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="asset_origin" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="create_date" readonly="1" string="录入日期"/>
                            <field name="depreciation_month" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="value_residual"/>
                            <field name="monthly_depreciation_rate" invisible="1"/>
                            <field name="account_depreciation_expense_id" required="1"
                                   attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="first_depreciation_date" required="1"/>
                        </group>
                        <group>
                            <field name="specification_model" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="asset_purpose" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="create_uid" readonly="1" string="录入人"/>
                            <field name="original_value" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="salvage_value" attrs="{'readonly': [('state','!=', 'draft')]}"/>
                            <field name="annual_depreciation" invisible="1"/>

                        </group>
                    </group>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_account_asset_action" model="ir.actions.act_window">
        <field name="name">资产档案</field>
        <field name="res_model">roke.account.asset</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个资产档案。
            </p>
        </field>
    </record>

    <record id="view_account_asset_compute_board_action" model="ir.actions.server">
        <field name="name">计算折旧</field>
        <field name="type">ir.actions.server</field>
        <field name="binding_model_id" ref="roke_account_asset.model_roke_account_asset"/>
        <field name="model_id" ref="roke_account_asset.model_roke_account_asset"/>
        <field name="state">code</field>
        <field name="code">
            for rec in records:
            action = rec.compute_depreciation_board()
        </field>
    </record>

</odoo>
