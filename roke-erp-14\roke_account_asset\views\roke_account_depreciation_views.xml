<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--资产档案-->
    <!--search-->
    <!--    <record id="view_roke_account_asset_search" model="ir.ui.view">-->
    <!--        <field name="name">roke.account.asset.search</field>-->
    <!--        <field name="model">roke.account.asset</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <search string="资产档案">-->
    <!--                <field name="name"/>-->
    <!--            </search>-->
    <!--        </field>-->
    <!--    </record>-->
    <!--tree-->
    <record id="view_roke_account_depreciation_tree" model="ir.ui.view">
        <field name="name">roke.account.depreciation.tree</field>
        <field name="model">roke.account.depreciation</field>
        <field name="arch" type="xml">
            <tree string="折旧记录" create='false'>
                <field name="ref"/>
                <field name="date"/>
                <field name="amount_total"/>
                <field name="accumulated_depreciation"/>
                <field name="asset_remaining_value"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_account_depreciation_form" model="ir.ui.view">
        <field name="name">roke.account.depreciation.form</field>
        <field name="model">roke.account.depreciation</field>
        <field name="arch" type="xml">
            <form string="折旧记录" create='false' edit='false' delete='false'>
                <header>
                    <button name="button_create_accountcore" string="生成凭证" type="object" class="oe_highlight"/>
                </header>
                <group id="g1">
                    <group>
                        <group>
                            <field name="asset_id" required="1"/>
                            <field name="name"/>
                            <field name="ref"/>
                            <field name="code"/>
                        </group>
                        <group>
                            <field name="date"/>
                            <field name="amount_total"/>
                            <field name="accumulated_depreciation"/>
                            <field name="asset_remaining_value"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="voucher_id"/>
                            <field name="company_id"/>
                        </group>
                    </group>
                </group>
                <notebook>
                    <page string="折旧明细">
                        <field name="line_ids"/>
                    </page>
                </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_account_depreciation_action" model="ir.actions.act_window">
        <field name="name">折旧记录</field>
        <field name="res_model">roke.account.depreciation</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            </p>
        </field>
    </record>

    <record id="view_roke_account_depreciation_line_tree" model="ir.ui.view">
        <field name="name">roke.account.depreciation.line.tree</field>
        <field name="model">roke.account.depreciation.line</field>
        <field name="arch" type="xml">
            <tree string="折旧记录明细">
                <field name="account_id"/>
                <field name="partner_id"/>
                <field name="tags"/>
                <field name="debit"/>
                <field name="credit"/>
                <field name="date"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_account_depreciation_line_form" model="ir.ui.view">
        <field name="name">roke.account.depreciation.line.form</field>
        <field name="model">roke.account.depreciation.line</field>
        <field name="arch" type="xml">
            <form string="折旧记录明细">
                <group>
                    <group>
                        <field name="account_id"/>
                        <field name="partner_id"/>
                        <field name="tags"/>
                    </group>
                    <group>
                        <field name="debit"/>
                        <field name="credit"/>
                        <field name="date"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="view_button_create_accountcore_action" model="ir.actions.server">
        <field name="name">生成凭证</field>
        <field name="type">ir.actions.server</field>
        <field name="binding_model_id" ref="roke_account_asset.model_roke_account_depreciation"/>
        <field name="model_id" ref="roke_account_asset.model_roke_account_depreciation"/>
        <field name="state">code</field>
        <field name="code">
            if records:
            action = records.button_create_accountcore()
        </field>
    </record>
</odoo>
