# -*- coding: utf-8 -*-


from odoo import models, fields, api, tools
from binascii import Error as binascii_error
import logging
_logger = logging.getLogger(__name__)


class RokeMesAccountPeriod(models.Model):
    _name = "roke.mes.account.period"
    _description = "会计期间"
    _rec_name = "activation_year"

    activation_year = fields.Char('启用年度')
    current_year = fields.Char('当前年度', compute='_compute_current_year')
    activation_period = fields.Char('启用期间')
    account_org_id = fields.Many2one('accountcore.org', string='核算机构')
    line_ids = fields.One2many('roke.mes.account.period.line', 'period_id', string='会计期间明细')

    def _compute_current_year(self):
        for record in self:
            record.current_year = fields.date.today().year


class RokeMesAccountPeriodLine(models.Model):
    _name = "roke.mes.account.period.line"
    _description = "会计期间明细"

    period_id = fields.Many2one('roke.mes.account.period', string='会计期间')
    start_date = fields.Date('开始日期')
    end_date = fields.Date('结束日期')
    account_period = fields.Char('会计期间')
    is_over = fields.Boolean('已月结', default=False)