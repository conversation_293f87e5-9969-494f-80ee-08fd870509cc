# -*- coding: utf-8 -*-

# @Time   : 2022/12/22 15:04
# <AUTHOR> 贾浩天
# @Email  : ji<PERSON><PERSON><PERSON>@rokedata.com
# @File   : roke_inherit_accountcore.py
# @Description:

from odoo import models, fields, api, tools
from binascii import Error as binascii_error
import logging
import re
_image_dataurl = re.compile(r'(data:image/[a-z]+?);base64,([a-z0-9+/\n]{3,}=*)\n*([\'"])(?: data-filename="([^"]*)")?', re.I)
_logger = logging.getLogger(__name__)


class InheritPayment(models.Model):
    _inherit = "roke.mes.payment"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritStockPicking(models.Model):
    _inherit = "roke.mes.stock.picking"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritStockInventory(models.Model):
    _inherit = "roke.mes.stock.inventory"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritSaleOrder(models.Model):
    _inherit = "roke.sale.order"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritPurchaseOrder(models.Model):
    _inherit = "roke.purchase.order"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritSalaryOrder(models.Model):
    _inherit = "roke.salary.order"

    def get_voucher(self):
        model_id = self.env['ir.model'].search([('model', '=', self._name)])
        voucher = self.env['accountcore.voucher'].search([('order_id', '=', self.id), ('model_id', '=', model_id.id)],
                                                         limit=1, order='id desc')
        # 凭证查看
        if not voucher:
            return {}

        return {
            'name': '凭证',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': 'accountcore.voucher',
            'res_id': voucher.id,
            'context': {
                'create': False, 'edit': False
            }
        }
