# -*- coding: utf-8 -*-

# @Time   : 2022/12/22 15:04
# <AUTHOR> 贾浩天
# @Email  : ji<PERSON><PERSON><PERSON>@rokedata.com
# @File   : roke_inherit_accountcore.py
# @Description: 

from odoo import models, fields, api, tools
from odoo.exceptions import UserError
from binascii import Error as binascii_error
from dateutil.relativedelta import relativedelta

import time
import logging
import re

_image_dataurl = re.compile(r'(data:image/[a-z]+?);base64,([a-z0-9+/\n]{3,}=*)\n*([\'"])(?: data-filename="([^"]*)")?',
                            re.I)
_logger = logging.getLogger(__name__)


class InheritItemClass(models.Model):
    _inherit = 'accountcore.itemclass'

    number = fields.Char(string='核算项目类别编码', copy=False,
                         default=lambda self: self.env['ir.sequence'].next_by_code('accountcore.itemclass.code'))
    item_origin = fields.Selection([('手工录入', '手工录入'), ('模型字段', '模型字段')], string='项目来源明细')

    item_origin_model = fields.Many2one('ir.model', string='项目明细来源模型')
    item_origin_field = fields.Many2one('ir.model.fields', string='项目明细来源字段')
    name_field_id = fields.Many2one('ir.model.fields', string='生成项目名称')

    def write(self, vals):
        res = super(InheritItemClass, self).write(vals)
        if self.item_origin_field.model_id != self.item_origin_model:
            raise UserError('所选字段和模型无关联，请重新选择模型和字段')
        return res


class RokeVoucherConfig(models.Model):
    _name = 'account.voucher.config'
    _description = '凭证定义'
    _rec_name = 'model_id'

    type = fields.Char(string='凭证类型')
    model_id = fields.Many2one('ir.model', string='主表模型')
    description = fields.Char(string='备注')
    config_control = fields.Char(string='定义约束')
    line_ids = fields.One2many('account.voucher.config.line', 'voucher_id', string='定义明细')

    def get_origin_field(self, model, model_id):
        # 获取源单据字段
        model_id = self.env['ir.model'].search([('id', '=', model_id)])
        for field in model_id.field_id:
            if field.ttype == 'many2one' and field.relation == model:
                return field.name

    def create_voucher(self, order_id, model):
        """
        两种情况：
        ①-源单据无单据明细单据id只有一个
        ②-源单据有明细，需要循环明细生成
        """

        voucher_obj = self.env['accountcore.voucher']
        # 查询来源
        source_id = self.env['accountcore.source'].search([('name', '=', '推送')])
        model_id = self.env['ir.model'].search([('model', '=', model)])
        conf = self.env['account.voucher.config'].search([('model_id', '=', model_id.id)], limit=1)
        if not conf:
            raise UserError('【生成凭证】未找到该配置')
        # 核算机构取当前登录用户的企业的核算机构
        org_id = self.env['accountcore.org'].search([('company_id', '=', self.env.company.id)], limit=1)
        line_list = []
        for line in conf.line_ids:
            order_line = self.env[line.model_id.model].search([('id', '=', int(order_id))])
            line_data = {
                "explain": line.abstract,
                "account": line.subject_id.id,
                # "items": line.account_item_id.ids,
            }
            # 判断子表和主表是否一致
            _field = getattr(order_line, line.field_id.name)
            # ①-单表
            if conf.model_id == line.model_id:
                order_id = order_id
                # 处理核算项目：根据核算项目类别生成核算项目，如果存在就选择否则生成新的核算项目。
                item_list = []
                for item_class in line.class_id:
                    # 获取源单据的字段
                    o_field = conf.get_origin_field(item_class.item_origin_model.model, conf.model_id.id)
                    if o_field:
                        order_field = getattr(order_line, o_field)
                        if not item_class.name_field_id:
                            raise UserError('核算项目类别生成核算项目无名称字段')
                        item_name = getattr(order_field, item_class.name_field_id.name)
                        item_data = {
                            "org": org_id,
                            "number": int(time.time()),
                            "itemClass": item_class.id,
                            "name": item_name
                        }
                        # 先查询是否存在
                        item = self.env['accountcore.item'].search(
                            [("name", '=', item_name), ('itemClass', '=', item_class.id)])
                        if not item:
                            item = self.env['accountcore.item'].create(item_data)
                        item_list.append(item.id)
                if line.voucher == '借':
                    line_data.update({"damount": _field})
                if line.voucher == '贷':
                    line_data.update({"camount": _field})
                line_data.update({"items": item_list})
                r = self.env['accountcore.entry'].create(line_data)
                line_list.append(r.id)
            # ②-多表
            else:
                # 获取关联字段
                line_model = self.env['ir.model'].search([('model', '=', line.model_id.model)])
                l_f = line_model.field_id.filtered(
                    lambda field: field.ttype == 'many2one' and field.relation == conf.model_id.model)
                line_obj = self.env[line.model_id.model].search([(l_f.name, '=', order_id)])
                count = 0
                for detail in line_obj:
                    _field = getattr(detail, line.field_id.name)
                    # 处理核算项目：根据核算项目类别生成核算项目，如果存在就选择否则生成新的核算项目。
                    item_list = []
                    for item_class in line.class_id:
                        # 获取源单据的字段
                        o_field = conf.get_origin_field(item_class.item_origin_model.model, line.model_id.id)
                        if o_field:
                            order_field = getattr(detail, o_field)
                            if not item_class.name_field_id:
                                raise UserError('核算项目类别生成核算项目无名称字段')
                            item_name = getattr(order_field, item_class.name_field_id.name)
                            item_data = {
                                "org": org_id,
                                "number": int(time.time()),
                                "itemClass": item_class.id,
                                "name": item_name
                            }
                            # 先查询是否存在
                            item = self.env['accountcore.item'].search(
                                [("name", '=', item_name), ('itemClass', '=', item_class.id)])
                            if not item:
                                _logger.info('不存在创建核算项目')
                                item = self.env['accountcore.item'].create(item_data)
                            item_list.append(item.id)
                    if line.voucher == '借':
                        count += 1
                        line_data.update({"damount": _field, "seq": count})
                    if line.voucher == '贷':
                        count += 1
                        line_data.update({"camount": _field, "seq": count})
                    line_data.update({"items": item_list})
                    r = self.env['accountcore.entry'].create(line_data)
                    line_list.append(r.id)
        data = {
            "soucre": source_id.id,
            "createUser": self.env.user.id,
            "order_id": int(order_id),
            "model_id": model_id.id,
            "org": org_id.id,
            "appendixCount": 1,
            "voucherdate": fields.date.today(),
            "real_date": fields.date.today(),
            "entrys": line_list
        }
        if not org_id:
            raise ValueError('未找到核算机构')
        _logger.info('调用凭证生成')
        _logger.info(data)
        voucher_obj.create(data)


class RokeVoucherConfigLine(models.Model):
    _name = 'account.voucher.config.line'
    _description = '凭证定义明细'
    _rec_name = 'voucher_id'

    voucher_id = fields.Many2one('account.voucher.config', string='凭证定义')
    voucher = fields.Selection([('借', '借'), ('贷', '贷')], string='凭证方向')
    model_id = fields.Many2one('ir.model', string='模型')
    field_id = fields.Many2one('ir.model.fields', string='字段')
    subject_id = fields.Many2one('accountcore.account', string='对应科目')
    class_id = fields.Many2many('accountcore.itemclass', string='对应科目类别')
    account_item_id = fields.Many2many('accountcore.item', string='核算项目')
    config_control = fields.Char(string='定义约束')
    abstract = fields.Char(string='摘要', compute='_compute_abstract')
    partner_id = fields.Many2one('res.partner', string='客户/供应商')
    abstract_1 = fields.Char('摘要前半段')
    abstract_2 = fields.Char('摘要后半段')

    def _compute_abstract(self):
        for record in self:
            data = "".join([record.abstract_1 or "", record.partner_id.name or "", record.abstract_2 or ""])
            record.abstract = data

    @api.onchange('model_id')
    def _onchange_model_id(self):
        if self.voucher_id.model_id:
            # 过滤主表以及子表
            res_list = [self.voucher_id.model_id.id]
            model = self.env['ir.model'].search([('id', '=', self.voucher_id.model_id.id)])
            for field in model.field_id:
                if field.ttype == 'one2many':
                    c_model = self.env['ir.model'].search([('model', '=', field.relation)])
                    res_list.append(c_model.id)
            domain = {'model_id': [('id', 'in', res_list)]}
        else:
            domain = {'model_id': [('id', 'in', [])]}
        return {"domain": domain}


class InheritAccountCoreVoucher(models.Model):
    _inherit = 'accountcore.voucher'

    order_id = fields.Char(string='单据id')
    model_id = fields.Many2one('ir.model', string='单据模型')

    def get_origin_order(self):
        # 源单据查看
        if not self.order_id and not self.model_id:
            return {}
        return {
            'name': '来源单据',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': self.model_id.model,
            'res_id': int(self.order_id),
            'context': {
                'create': False, 'edit': False
            }
        }


class InheritAccountCoreEntry(models.Model):
    _inherit = 'accountcore.entry'

    seq = fields.Integer(string='序号')


class RokeInheritAccountOrg(models.Model):
    _inherit = 'accountcore.org'

    company_id = fields.Many2one('res.company', string='公司')
    period_id = fields.Many2one('roke.mes.account.period', string='会计期间')
    active_state = fields.Boolean('启用状态', default=False)
    activation_year = fields.Char('启用年度')
    current_year = fields.Char('当前年度', compute='_compute_current_year')
    activation_period = fields.Char('启用期间')
    account_org_id = fields.Many2one('accountcore.org', string='核算机构')
    line_ids = fields.One2many('accountcore.org.period.line', 'period_id', string='会计期间明细', order='sequence')
    period_config_ids = fields.One2many('accountcore.org.period.config', 'org_id', string='会计期间')

    def _compute_current_year(self):
        for record in self:
            today = fields.date.today()
            record.current_year = str(today)[0:4].replace('-', '')

    def get_first_and_last_day_of_month(self, month):
        today = fields.Date.today()
        year = today.year
        date_str = f"{year}-{month:02d}-01"
        first_day_of_month = fields.Date.from_string(date_str)
        last_day_of_month = (first_day_of_month + relativedelta(day=1, months=1, days=-1)).day
        last_day_of_month = first_day_of_month + relativedelta(day=last_day_of_month)
        return first_day_of_month, last_day_of_month

    @api.onchange('period_config_ids')
    def _onchange_period_config_ids(self):
        if not self.period_config_ids:
            sequence = 0
            data_list = []
            while sequence < 12:
                sequence += 1
                first_day, last_day = self.get_first_and_last_day_of_month(sequence)
                data_list.append((0, 0, {
                    "sequence": sequence,
                    "start_date": first_day,
                    "end_date": last_day
                }))
            return {"value": {"period_config_ids": data_list}}

    def button_open(self):
        # 生成10年的会计期间数据
        today = fields.Date.today()
        current_year = today.year
        current_month = today.month
        data_list = []
        if not self.period_config_ids:
            raise UserError('未设置会计期间，请先设置会计期间')
        for year in range(current_year, current_year + 10):
            for line in self.period_config_ids:
                start_date = "".join([str(year), '-', line.start_date_formatted])
                end_date = "".join([str(year), '-', line.end_date_formatted])
                data_list.append((0, 0, {
                    "start_date": start_date,
                    "end_date": end_date,
                    "account_period": year,
                }))
                # "account_period": str(year) + line.start_date_formatted[0:2],
        data = {
                'activation_year': str(current_year),
                'activation_period': f"{current_year}{current_month:02d}",
                'start_date': fields.Date.today(),
                'active_state': True,
                "line_ids": data_list}
        self.write(data)

    def button_close(self):
        self.write({'active_state': False})

    @api.onchange('period_id')
    def _onchange_period_id(self):
        if self.period_id:
            self.activation_year = self.period_id.activation_year
            self.current_year = self.period_id.current_year
            self.activation_period = self.period_id.activation_period
            self.account_org_id = self.id
            _list = []
            for line in self.period_id.line_ids:
                _list.append((0, 0, {
                    "start_date": line.start_date,
                    "end_date": line.end_date,
                    "account_period": line.account_period,
                    "is_over": line.is_over,
                }))
            self.line_ids = _list
        else:
            self.activation_year = None
            self.current_year = None
            self.activation_period = None
            self.account_org_id = None
            self.line_ids = None

    def set_period(self):
        """增加会计期间"""
        last_record = max(self.line_ids, key=lambda r: r.id)
        from dateutil.relativedelta import relativedelta
        start_date = last_record.end_date + relativedelta(days=1)
        # 获取上条数据的区间
        interval = last_record.end_date - last_record.start_date
        end_date = start_date + relativedelta(days=interval.days)
        self.write({"line_ids": [(0, 0, {
            "start_date": start_date,
            "end_date": end_date,
            "account_period": int(last_record.account_period) + 1
        })]})


class RokeMesAccountPeriodLine(models.Model):
    _name = "accountcore.org.period.line"
    _description = "会计期间明细"

    period_id = fields.Many2one('accountcore.org', string='会计期间', ondelete="cascade")
    start_date = fields.Date('开始日期')
    end_date = fields.Date('结束日期')
    account_period = fields.Char('会计期间')
    is_over = fields.Boolean('已月结', default=False)
    end_time = fields.Datetime('月结操作时间')
    end_user_id = fields.Many2one('res.users', string='月结操作人')
    depreciation = fields.Boolean(string='是否折旧', default=False)


class RokeMesAccountPeriodConfig(models.Model):
    _name = "accountcore.org.period.config"
    _description = "会计期间配置"

    org_id = fields.Many2one('accountcore.org', string='机构', ondelete="cascade")
    sequence = fields.Integer(string='序号', default=1)
    start_date = fields.Date('开始日期')
    end_date = fields.Date('结束日期')
    start_date_formatted = fields.Char(string='开始日期', compute='_compute_date_formatted')
    end_date_formatted = fields.Char(string='结束日期', compute='_compute_date_formatted')

    def _compute_date_formatted(self):
        for record in self:
            record.start_date_formatted = record.start_date.strftime('%m-%d')
            record.end_date_formatted = record.end_date.strftime('%m-%d')


class RokeMesAccountMonthEnd(models.TransientModel):
    _name = "roke.account.month.end"
    _description = "月末结转"
    _rec_name = "account_org_id"

    account_org_id = fields.Many2one('accountcore.org', string='核算机构')
    period = fields.Char('会计期间')
    operator_id = fields.Many2one('res.users', string='操作员', default=lambda s: s.env.uid)
    line_ids = fields.One2many('roke.account.month.end.line', 'month_id')
    line1_ids = fields.One2many('roke.account.month.end.line', 'month_id', compute="_compute_get_is_end")
    line2_ids = fields.One2many('roke.account.month.end.line', 'month_id', compute="_compute_get_is_end")

    @api.depends('line_ids')
    def _compute_get_is_end(self):
        for record in self:
            record.line1_ids = record.line_ids.filtered(lambda l: l.is_end)
            record.line2_ids = record.line_ids.filtered(lambda l: not l.is_end)

    @api.onchange('account_org_id')
    def _onchange_account_org_id(self):
        self.line_ids = None
        if self.account_org_id:
            _list = []
            for line in self.account_org_id.line_ids:
                _list.append((0, 0, {
                    "account_org_id": self.account_org_id.id,
                    "is_end": line.is_over,
                    "start_date": line.start_date,
                    "end_date": line.end_date,
                    "period": line.account_period,
                    "end_time": line.end_time,
                }))
            self.line_ids = _list
            self.period = self.account_org_id.activation_period

    def button_end(self):
        """月结"""
        # 检查当前会计期间凭证是否都已经审核完毕
        ids_list = [{"period": line.period, "obj": line} for line in self.line2_ids]
        if ids_list:
            _next = sorted(ids_list, key=lambda x: int(x['period']))[0]
            # period_line = self.account_org_id.line_ids.filtered(lambda org: _next.get('period') == org.account_period)
            period_line = self.env['accountcore.org.period.line'].search(
                [('account_period', '=', self.period), ('account_period', '=', _next.get('period'))])
            if not period_line:
                raise UserError('当前会计期间为%s没有符合的数据' % self.period)

            for line in period_line:
                voucher = self.env['accountcore.voucher'].search(
                    [('org', '=', self.account_org_id.id), ('voucherdate', '>=', line.start_date),
                     ('voucherdate', '<=', line.end_date)])
                for v in voucher:
                    if v.state == 'creating':
                        raise UserError('【%s】在当前会计期间下有未复核的凭证' % self.account_org_id)
                # 检查当前会计期间凭证平衡检查是否正常
                check_obj = self.env['accountcore.begin_balance_check'].create(
                    {'org_ids': [(6, 0, [self.account_org_id.id])]})
                res = check_obj._do_check()
                if res != 'success':
                    raise UserError('平衡检查异常【%s】' % res)
                self.account_org_id.lock_date = fields.date.today()
                # 更新月结状态
                _next.get('obj').write(
                    {"is_end": True, "end_time": fields.datetime.now(), "end_user_id": self.env.user.id})
                line.write({"is_over": True, "end_time": fields.datetime.now(), "end_user_id": self.env.user.id})

    def button_cancel_end(self):
        """取消月结"""
        ids_list = [{"period": line.period, "obj": line} for line in self.line1_ids]
        if ids_list:
            _next = sorted(ids_list, key=lambda x: int(x['period']), reverse=True)[0]
            # period_line = self.account_org_id.line_ids.filtered(
            #     lambda org: _next.get('period') == org.account_period)
            period_line = self.env['accountcore.org.period.line'].search(
                [('account_period', '=', self.period), ('account_period', '=', _next.get('period'))])
            if not period_line:
                raise UserError('当前会计期间为%s没有符合的数据' % self.period)
            _next.get('obj').write({"is_end": False})
            period_line.write({"is_over": False})


class RokeMesAccountMonthEndLine(models.TransientModel):
    _name = "roke.account.month.end.line"
    _description = "月末结转明细"

    month_id = fields.Many2one('roke.account.month.end', string='月末')
    period = fields.Char('会计期间')
    account_org_id = fields.Many2one('accountcore.org', string='核算机构')
    is_end = fields.Boolean('是否月结')
    end_time = fields.Datetime('月结操作时间')
    end_user_id = fields.Many2one('res.users', string='月结操作人')
    start_date = fields.Date('开始日期')
    end_date = fields.Date('结束日期')
