# -*- coding: utf-8 -*-

# @Time   : 2022/12/22 15:04
# <AUTHOR> 贾浩天
# @Email  : ji<PERSON><PERSON><PERSON>@rokedata.com
# @File   : roke_inherit_accountcore.py
# @Description:

from odoo import models, fields, api, tools
from binascii import Error as binascii_error
import logging
import re
_image_dataurl = re.compile(r'(data:image/[a-z]+?);base64,([a-z0-9+/\n]{3,}=*)\n*([\'"])(?: data-filename="([^"]*)")?', re.I)
_logger = logging.getLogger(__name__)


class InheritMessage(models.Model):
    _inherit = "mail.message"

    @api.model_create_multi
    def create(self, values_list):
        tracking_values_list = []
        for values in values_list:
            if 'email_from' not in values:  # needed to compute reply_to
                author_id, email_from = self.env['mail.thread']._message_compute_author(values.get('author_id'),
                                                                                        email_from=None,
                                                                                        raise_exception=False)
                values['email_from'] = email_from
            if not values.get('message_id'):
                values['message_id'] = self._get_message_id(values)
            if 'reply_to' not in values:
                values['reply_to'] = self._get_reply_to(values)
            if 'record_name' not in values and 'default_record_name' not in self.env.context:
                values['record_name'] = self._get_record_name(values)

            if 'attachment_ids' not in values:
                values['attachment_ids'] = []
            # extract base64 images
            if 'body' in values:
                Attachments = self.env['ir.attachment']
                data_to_url = {}

                def base64_to_boundary(match):
                    key = match.group(2)
                    if not data_to_url.get(key):
                        name = match.group(4) if match.group(4) else 'image%s' % len(data_to_url)
                        try:
                            attachment = Attachments.create({
                                'name': name,
                                'datas': match.group(2),
                                'res_model': values.get('model'),
                                'res_id': values.get('res_id'),
                            })
                        except binascii_error:
                            _logger.warning(
                                "Impossible to create an attachment out of badly formated base64 embedded image. Image has been removed.")
                            return match.group(
                                3)  # group(3) is the url ending single/double quote matched by the regexp
                        else:
                            attachment.generate_access_token()
                            values['attachment_ids'].append((4, attachment.id))
                            data_to_url[key] = [
                                '/web/image/%s?access_token=%s' % (attachment.id, attachment.access_token), name]
                    return '%s%s alt="%s"' % (data_to_url[key][0], match.group(3), data_to_url[key][1])

                values['body'] = _image_dataurl.sub(base64_to_boundary, tools.ustr(values['body']))

            # delegate creation of tracking after the create as sudo to avoid access rights issues
            tracking_values_list.append(values.pop('tracking_value_ids', False))

        messages = super(InheritMessage, self).create(values_list)

        check_attachment_access = []
        if all(isinstance(command, int) or command[0] in (4, 6) for values in values_list for command in
               values.get('attachment_ids')):
            for values in values_list:
                for command in values.get('attachment_ids'):
                    if isinstance(command, int):
                        check_attachment_access += [command]
                    elif command[0] == 6:
                        check_attachment_access += command[2]
                    else:  # command[0] == 4:
                        check_attachment_access += [command[1]]
        else:
            check_attachment_access = messages.mapped('attachment_ids').ids  # fallback on read if any unknow command
        if check_attachment_access:
            self.env['ir.attachment'].browse(check_attachment_access).check(mode='read')

        for message, values, tracking_values_cmd in zip(messages, values_list, tracking_values_list):
            if tracking_values_cmd:
                vals_lst = [dict(cmd[2], mail_message_id=message.id) for cmd in tracking_values_cmd if
                            len(cmd) == 3 and cmd[0] == 0]
                other_cmd = [cmd for cmd in tracking_values_cmd if len(cmd) != 3 or cmd[0] != 0]
                if vals_lst:
                    self.env['mail.tracking.value'].sudo().create(vals_lst)
                if other_cmd:
                    message.sudo().write({'tracking_value_ids': tracking_values_cmd})

            if message.is_thread_message(values):
                message._invalidate_documents(values.get('model'), values.get('res_id'))

        try:
            # 是否存在自动生成凭证的配置
            for _messages in messages:
                res = self.env['roke.account.create.config'].search([('model_id.model', '=', _messages.model)])
                if res:
                    # 凭证生成因为有参数，所以写死。后期进行细节优化
                    if res.fun_model_id.model == 'account.voucher.config':
                        for tracking in _messages.tracking_value_ids:
                            if tracking.field == res.field_id and tracking.old_value_char == res.old_value and \
                                    tracking.new_value_char == res.new_value:
                                param1 = _messages.res_id
                                param2 = _messages.model
                                _function = res.function
                                _param = "(%s, '%s')" % (param1, param2)
                                # 方法拼接参数
                                _model_function = "".join([_function, _param])
                                _code = 'self.env["%s"].%s' % (res.fun_model_id.model, _model_function)
                                exec(_code)
                            # else:
                            #     _logger.info('进入false')
                            #     _logger.info(tracking.field)
                            #     _logger.info(res.field_id)
                            #     _logger.info(tracking.old_value_char)
                            #     _logger.info(res.old_value)
                            #     _logger.info(tracking.new_value_char)
                            #     _logger.info(res.new_value)
                    else:
                        _model_function = "".join([res.function, "()"])
                        _code = 'self.env["%s"].%s' % (res.fun_model_id.model, _model_function)
                        exec(_code)
        except Exception as e:
            _logger.info(e)

        return messages


class RokeVoucherConfig(models.Model):
    _name = 'roke.account.create.config'
    _description = '凭证自动生成配置'
    _rec_name = 'model_id'

    voucher_id = fields.Many2one('account.voucher.config', string='凭证定义')
    model_id = fields.Many2one('ir.model', string='模型')
    model_name = fields.Char(related='fun_model_id.model', string='模型名称')
    field_id = fields.Many2one('ir.model.fields', string='约束字段')
    old_value = fields.Char('旧值')
    new_value = fields.Char('新值')
    fun_model_id = fields.Many2one('ir.model', string='调用模型')
    function = fields.Char(string='调用方法')
    line_ids = fields.One2many('roke.account.create.config.line', 'config_id', string='参数')


class RokeVoucherConfigLine(models.Model):
    _name = 'roke.account.create.config.line'
    _description = '凭证调用参数'
    _rec_name = 'config_id'

    config_id = fields.Many2one('roke.account.create.config', string='配置')
    value = fields.Char('参数')
    type = fields.Selection([('字符', '字符'), ('整数', '整数'), ('浮点', '浮点'), ('布尔', '布尔')], string='参数类型')
