<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_mes_account_period_tree" model="ir.ui.view">
        <field name="name">roke.mes.account.period.tree</field>
        <field name="model">roke.mes.account.period</field>
        <field name="arch" type="xml">
            <tree string="会计期间">
                <field name="activation_year"/>
                <field name="activation_period"/>
                <field name="current_year"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="roke_mes_account_period_form" model="ir.ui.view">
        <field name="name">roke.mes.account.period.form</field>
        <field name="model">roke.mes.account.period</field>
        <field name="arch" type="xml">
            <form string="会计期间">
                <sheet>
                    <group col="3">
                        <group>
                            <field name="activation_year" required="1"/>
                        </group>
                        <group>
                            <field name="activation_period" required="1"/>
                        </group>
                        <group>
                            <field name="current_year"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="凭证定义明细">
                            <field name="line_ids">
                                <tree editable="bottom"
                                      decoration-muted="is_over==True"
                                      decoration-info ="account_period==parent.activation_period">
                                    <field name="period_id" invisible="1"/>
                                    <field name="account_period" required="1"/>
                                    <field name="start_date" required="1"/>
                                    <field name="end_date" required="1"/>
                                    <field name="is_over"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="roke_mes_account_period_action" model="ir.actions.act_window">
        <field name="name">会计期间</field>
        <field name="res_model">roke.mes.account.period</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>
