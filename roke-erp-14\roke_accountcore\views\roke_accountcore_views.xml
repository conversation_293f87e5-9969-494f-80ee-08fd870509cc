<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <!--    <record id="view_roke_mes_equipment_category_search" model="ir.ui.view">-->
    <!--        <field name="name">roke.mes.equipment.category.search</field>-->
    <!--        <field name="model">roke.mes.equipment.category</field>-->
    <!--        <field name="arch" type="xml">-->
    <!--            <search string="">-->
    <!--                <field name="name"/>-->
    <!--            </search>-->
    <!--        </field>-->
    <!--    </record>-->
    <!--tree-->
    <record id="roke_account_voucher_config_tree" model="ir.ui.view">
        <field name="name">account.voucher.config.tree</field>
        <field name="model">account.voucher.config</field>
        <field name="arch" type="xml">
            <tree string="凭证定义">
                <field name="type"/>
                <field name="model_id"/>
                <field name="description"/>
                <field name="config_control"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="roke_account_voucher_config_form" model="ir.ui.view">
        <field name="name">account.voucher.config.form</field>
        <field name="model">account.voucher.config</field>
        <field name="arch" type="xml">
            <form string="凭证定义">
                <group col="3">
                    <group>
                        <field name="type" required="1"/>
                    </group>
                    <group>
                        <field name="model_id" required="1"/>
                    </group>
                    <group>
                        <field name="description"/>
                    </group>
                </group>
                <group>
                    <field name="config_control"/>
                </group>
                <notebook>
                    <page string="凭证定义明细">
                        <field name="line_ids">
                            <tree>
                                <field name="voucher" required="1"/>
                                <field name="model_id" required="1"/>
                                <field name="field_id" domain="[('model_id', '=', model_id)]" required="1"/>
                                <field name="subject_id"/>
                                <field name="class_id" widget="many2many_tags"/>
                                <field name="abstract"/>
                            </tree>
                            <form>
                                <group>
                                    <group>
                                        <field name="voucher" required="1"/>
                                        <field name="subject_id" required="1"/>

                                    </group>
                                    <group>
                                        <field name="model_id" required="1"/>
                                        <field name="field_id" domain="[('model_id', '=', model_id)]" required="1"/>
                                    </group>
                                </group>
                                <group>
                                    <field name="class_id"/>
                                    <field name="partner_id" invisible="1"/>
                                    <field name="abstract" invisible="1"/>
                                </group>
                                <group>
                                    <label for="abstract"/>
                                    <div name="abstract" class="o_row">
                                        <field name="abstract_1"/>
                                        <span>
                                            <field name="partner_id"/>
                                        </span>
                                        <span>
                                            <field name="abstract_2"/>
                                        </span>
                                    </div>
                                </group>
                            </form>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="roke_account_voucher_config_action" model="ir.actions.act_window">
        <field name="name">凭证定义</field>
        <field name="res_model">account.voucher.config</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
    <!--月末结转-->
    <record id="roke_account_month_end_tree" model="ir.ui.view">
        <field name="name">roke.account.month.end.tree</field>
        <field name="model">roke.account.month.end</field>
        <field name="arch" type="xml">
            <tree string="月末结转">
                <field name="account_org_id"/>
                <field name="period"/>
                <field name="operator_id"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="roke_account_month_end_form" model="ir.ui.view">
        <field name="name">roke.account.month.end.form</field>
        <field name="model">roke.account.month.end</field>
        <field name="arch" type="xml">
            <form string="月末结转">
                <header>
                    <button name="button_end" type="object" string="月结" class='oe_highlight oe_read_only'/>
                    <button name="button_cancel_end" type="object" string="取消月结" class='btn-danger oe_read_only'/>
                </header>
                <group col="3">
                    <group>
                        <field name="account_org_id" required="1"/>
                    </group>
                    <group>
                        <field name="period" required="1"/>
                    </group>
                    <group>
                        <field name="operator_id"/>
                    </group>
                </group>
                <notebook>
                    <page string="月结记录">
                        <field name="line_ids">
                            <tree>
                                <field name="period"/>
                                <field name="account_org_id"/>
                                <field name="start_date"/>
                                <field name="end_date"/>
                                <field name="is_end"/>
                                <field name="end_time"/>
                                <field name="end_user_id"/>
                            </tree>
                        </field>
                    </page>
                </notebook>

                <!--                        <page string="已月结" invisible="1">-->
                <!--                            <field name="line_ids">-->
                <!--                                <tree>-->
                <!--                                    <field name="period"/>-->
                <!--                                    <field name="account_org_id"/>-->
                <!--                                    <field name="is_end"/>-->
                <!--                                    <field name="end_time"/>-->
                <!--                                    <field name="end_user_id"/>-->
                <!--                                </tree>-->
                <!--                            </field>-->
                <!--                        </page>-->
                <!--                    </notebook>-->
                <!--                    <notebook>-->
                <!--                        <page string="已月结">-->
                <!--                            <field name="line1_ids">-->
                <!--                                <tree>-->
                <!--                                    <field name="period"/>-->
                <!--                                    <field name="account_org_id"/>-->
                <!--                                    <field name="start_date"/>-->
                <!--                                    <field name="end_date"/>-->
                <!--                                    <field name="is_end"/>-->
                <!--                                    <field name="end_time"/>-->
                <!--                                    <field name="end_user_id"/>-->
                <!--                                </tree>-->
                <!--                            </field>-->
                <!--                        </page>-->
                <!--                    </notebook>-->
            </form>
        </field>
    </record>
    <!--action-->
<!--    <record id="roke_account_month_end_action" model="ir.actions.act_window">-->
<!--        <field name="name">月末结转</field>-->
<!--        <field name="res_model">roke.account.month.end</field>-->
<!--        <field name="view_mode">tree,form,pivot</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="domain">[]</field>-->
<!--        <field name="context">{}</field>-->
<!--        <field name='target'>current</field>-->
<!--    </record>-->

    <record model='ir.actions.act_window' id='roke_account_month_end_action'>
            <field name="name">月末结转</field>
            <field name="res_model">roke.account.month.end</field>
            <field name="view_mode">form</field>
            <field name="target">current</field>
        </record>

</odoo>
