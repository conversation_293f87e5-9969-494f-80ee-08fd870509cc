<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--核算项目类别form-->
        <record id="inherit_accountcore_itemclass_form" model="ir.ui.view">
            <field name="name">inherit_accountcore_itemclass_form</field>
            <field name="model">accountcore.itemclass</field>
            <field name="inherit_id" ref="accountcore.accountcore_itemclass_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group/group[3]" position="after">
                    <group>
                        <field name="item_origin" widget="radio"/>
                    </group>
                </xpath>
                <xpath expr="//group" position="after">
                    <group col="4">
                        <group>
                            <field name="item_origin_model" attrs="{'invisible': [('item_origin', '!=', '模型字段')]}"/>
                        </group>
                        <group>
                            <field name="item_origin_field" domain="[('model_id', '=', item_origin_model)]"
                                   attrs="{'invisible': [('item_origin', '!=', '模型字段')]}"/>
                        </group>
                        <group>
                            <field name="name_field_id" domain="[('model_id', '=', item_origin_model)]"
                                   attrs="{'invisible': [('item_origin', '!=', '模型字段')], 'required':  [('item_origin', '=', '模型字段')]}"/>
                        </group>
                    </group>
                </xpath>
            </field>
        </record>
        <!--核算项目类别tree-->
        <record id="inherit_accountcore_itemclass_list" model="ir.ui.view">
            <field name="name">inherit_accountcore_itemclass_list</field>
            <field name="model">accountcore.itemclass</field>
            <field name="inherit_id" ref="accountcore.accountcore_itemclass_list"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='glob_tag']" position="after">
                    <field name="item_origin"/>
                    <field name="item_origin_model"/>
                    <field name="item_origin_field"/>
                </xpath>
            </field>
        </record>
        <!--凭证-->
        <record id="inherit_accountcore_voucher_form" model="ir.ui.view">
            <field name="name">inherit_accountcore_voucher_form</field>
            <field name="model">accountcore.voucher</field>
            <field name="inherit_id" ref="accountcore.accountcore_voucher_form"/>
            <field name="arch" type="xml">
                <xpath expr="//div[1]" position="before">
                    <div name="button_box" class="oe_button_box">
                        <button class="oe_stat_button" string="来源单据" name="get_origin_order"
                                type="object" icon="fa-list"/>
                    </div>
                </xpath>
                <xpath expr="//field[@name='camount']" position="after">
                    <field name="seq" invisible="1"/>
                </xpath>
                <xpath expr="//tree" position="attributes">
                    <attribute name="default_order">seq</attribute>
                </xpath>
            </field>
        </record>
        <!--机构-->
        <record id="inherit_accountcore_org_action_window_form" model="ir.ui.view">
            <field name="name">inherit_accountcore_org_action_window_form</field>
            <field name="model">accountcore.org</field>
            <field name="inherit_id" ref="accountcore.accountcore_org_action_window_form"/>
            <field name="arch" type="xml">
                <xpath expr="//h1" position="before">
                    <header>
                        <button name="button_open" type="object" string="机构启用" class="oe_highlight oe_read_only" attrs="{'invisible': [('active_state', '=', True)]}"/>
                        <button name="button_close" type="object" string="取消启用" attrs="{'invisible': [('active_state', '=', False)]}"/>
                        <field name="active_state" invisible="1"/>
                    </header>
                </xpath>

                <xpath expr="//field[@name='user_ids']" position="before">
                    <field name="company_id" required="1"/>
                </xpath>
                <xpath expr="//field[@name='glob_tag']" position="after">
                    <field name="period_id"/>
                </xpath>
                <xpath expr="//group[1]" position="after">

                    <notebook>
                        <page string="会计期间">
                            <field name="period_config_ids">
                                <tree editable="bottom">
                                    <field name="sequence"/>
                                    <field name="start_date"/>
                                    <field name="end_date"/>
                                    <field name="start_date_formatted"/>
                                    <field name="end_date_formatted"/>
                                </tree>
                            </field>
                        </page>
                        <page string="会计期间明细">
                            <!--                            <button name="set_period" string="增加会计区间"/>-->
                            <button class="oe_stat_button" string="增加会计区间" name="set_period"
                                    type="object" icon="fa-list"/>
                            <field name="line_ids">
                                <tree editable="bottom"
                                      decoration-muted="is_over==True"
                                      decoration-info="account_period==parent.activation_period">
                                    <field name="period_id" invisible="1"/>
                                    <field name="account_period" required="1"/>
                                    <field name="start_date" required="1"/>
                                    <field name="end_date" required="1"/>
                                    <field name="is_over"/>
                                    <field name="end_time"/>
                                    <field name="end_user_id"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group col="3">
                        <group>
                            <field name="activation_year"/>
                        </group>
                        <group>
                            <field name="activation_period"/>
                        </group>
                        <group>
                            <field name="current_year" readonly="1" foce_save="1"/>
                        </group>
                    </group>
                </xpath>
            </field>
        </record>

    </data>
</odoo>