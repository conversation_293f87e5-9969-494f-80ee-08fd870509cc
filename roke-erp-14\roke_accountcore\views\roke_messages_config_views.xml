<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="roke_account_create_config_tree" model="ir.ui.view">
        <field name="name">roke.account.create.config.tree</field>
        <field name="model">roke.account.create.config</field>
        <field name="arch" type="xml">
            <tree string="凭证生成定义">
                <field name="model_id"/>
                <field name="field_id"/>
                <field name="old_value"/>
                <field name="new_value"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="roke_account_create_config_form" model="ir.ui.view">
        <field name="name">roke.account.create.config.form</field>
        <field name="model">roke.account.create.config</field>
        <field name="arch" type="xml">
            <form string="凭证生成定义">
                <div name="message" class="alert alert-info" role="alert" style="margin-bottom:0px;">
                    1.约束字段必须为追踪字段，若源单据中无追踪字段，请在字段中进行配置。
                    <br/>
                    2.旧值与新值是约束字段发生变化的旧值与新值，例如“状态”从“草稿”更新为“确认”。
                    <br/>
                    3.若要生成凭证，调用模型为【凭证定义】，调用方法为【create_voucher】
                </div>
                <group col="4">
                    <group>
                        <field name="model_id" required="1"/>
                        <field name="field_id" domain="[('model_id', '=', model_id)]" required="1"/>
                    </group>
                    <group>
                        <field name="old_value" required="1"/>
                        <field name="new_value" required="1"/>
                    </group>
                    <group>
                        <field name="fun_model_id" required="1"/>
                        <field name="function"/>
                    </group>
                    <group>
                        <field name="model_name" invisible="1"/>
                        <field name="voucher_id"
                               attrs="{'invisible': [('model_name', '!=', 'account.voucher.config')], 'required':  [('model_name', '=', 'account.voucher.config')]}"/>
                    </group>
                </group>
                <notebook invisible="1">
                    <page string="参数">
                        <field name="line_ids">
                            <tree editable="bottom">
                                <field name="type" required="1"/>
                                <field name="value" required="1"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="roke_account_create_config_action" model="ir.actions.act_window">
        <field name="name">凭证生成定义</field>
        <field name="res_model">roke.account.create.config</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>


</odoo>
