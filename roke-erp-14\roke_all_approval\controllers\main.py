# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError
import json
import logging
import base64
from datetime import datetime,timedelta

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


class Main(http.Controller):

    def _get_field_valus(self, record, fields, detail=False):
        """
        获取返回值
        :param general_order:
        :param records:
        :return:
        """
        if record:
            id_result = {
                "field_index": "id",
                "field_description": "ID",
                "value": record.id,
                "ttype": "integer",
                "required": False,
                "readonly": True,
                "relation": "",
                "select_vals": []
            }
            if '驳回' in record.doc_approval_state:
                doc_approval_state = '已驳回'
            else:
                doc_approval_state = record.doc_approval_state
            approval_info = {
                "field_index": 'doc_approval_state',
                "field_description": '审批状态',
                "value": doc_approval_state,
            }
            if detail:
                id_result["list"] = False
            record_vals = [id_result,approval_info]
        else:
            record_vals = []
        # 匹配返回字段
        for roke_field in fields:
            field = roke_field.field_id
            ttype = field.ttype
            description = field.field_description
            # 获取字段值
            if record:
                field_value = getattr(record, field.name)
                if ttype == "many2one":
                    value = field_value.display_name if field_value else ""
                    value_id = field_value.id if field_value else False
                elif ttype in ["one2many", "many2many"]:
                    value_id = []
                    value = []
                    for v in field_value:
                        value.append(v.display_name)
                        value_id.append(v.id)
                else:
                    value = field_value or ""
                    value_id = False
            else:
                value = ""
                value_id = False
            # 获取选择项
            select_vals = []
            if ttype == "selection":
                for selection in field.selection_ids:
                    select_vals.append(selection.value)
            result = {
                "sequence": roke_field.sequence,
                "field_index": field.name,
                "field_description": description,
                "value": value or "",
                "ttype": ttype,
                "required": field.required,
                "readonly": roke_field.field_readonly,
                "relation": field.relation or "",
                "select_vals": select_vals,
                "primary": roke_field.primary
            }
            if value_id:
                result["value_id"] = value_id
            if detail:
                result["list"] = roke_field.detail_list_field
            record_vals.append(result)
        return record_vals

    # 获取审批列表
    @http.route('/roke/get/approval_list', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_get_approval_list(self):
        """
        获取审批列表
        :parameter: {user: 用户名, state: 状态}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        user = http.request.jsonrequest.get('user')
        state = http.request.jsonrequest.get('state')
        try:
            # 判断入参
            if user and state:
                if state not in ['complete','running']:
                    raise UserError('状态入参错误!')
                user_id = http.request.env(user=SUPERUSER_ID)['res.users'].search([('login', '=', user)],limit=1)
                if not user_id:
                    raise UserError('入参错误,未查找到对应数据!')
                # 根据状态取值判断已完成未完成
                if state == 'running':
                    approval_ids = http.request.env['wait.approval'].search(
                        [('index_state', '=', '待审批'),('user_id','=',user_id.id)],order='create_date desc')
                else:
                    approval_ids = http.request.env['wait.approval'].search(
                        [('index_state', 'in', ('审批未完成','审批完成','被驳回')),('user_id','=',user_id.id)],order='create_date desc')
                # 汇总数据
                approval_list,info = [],[]
                for i in approval_ids:
                    approval_list.append(str({
                        'model': i.model_name,
                        'model_name': http.request.env(user=SUPERUSER_ID)['ir.model'].search([
                            ('model', '=', i.model_name)], limit=1).name,
                    }))
                set_list,end_list = list(set(approval_list)),[]
                for i in set_list:
                    end_list.append({
                        'model': eval(i)['model'],
                        'model_name': eval(i)['model_name'],
                        'num': approval_list.count(i)
                    })
                for i in end_list:
                    # 取配置
                    setting_id = http.request.env['roke.app.general.order.setting'].search(
                        [('model_id.model', '=', i['model']), ('is_approval', '=', True)], limit=1)
                    if not setting_id:
                        raise UserError('数据未配置,请前往通用单据配置完成[%s]配置!' %i['model_name'])
                    # 取目标审批单据
                    if state == 'running':
                        app_ids = http.request.env['wait.approval'].search(
                            [('index_state', '=', '待审批'),('user_id','=',user_id.id),('model_name','=',i['model'])],order='create_date desc')
                    else:
                        app_ids = http.request.env['wait.approval'].search(
                            [('index_state', 'in', ('审批未完成','审批完成','被驳回')),('user_id','=',user_id.id),('model_name','=',i['model'])],order='create_date desc')
                    # 循环遍历取待需数据
                    record_list = []
                    if setting_id.detail_field_id:
                        for app in app_ids:
                            record = http.request.env[app.model_name].search([('id','=',app.res_id)])
                            if record:
                                record_valus = self._get_field_valus(record, setting_id.list_field_ids,True)
                                record_list.append(record_valus)
                    else:
                        for app in app_ids:
                            record = http.request.env[app.model_name].search([('id','=',app.res_id)])
                            if record:
                                record_valus = self._get_field_valus(record, setting_id.list_field_ids)
                                record_list.append(record_valus)
                    i.update({'num': len(record_list)})
                    # 总数据
                    info.append({
                        'total_value': i,
                        'record_valus': record_list
                    })
                result = {'state': 'success', 'msgs': '获取成功', 'info': info}
            else:
                raise UserError('入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    # # 获取审批列表
    # @http.route('/roke/get/approval_list', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    # def roke_get_approval_list(self):
    #     """
    #     获取审批列表
    #     :parameter: {user: 用户名, state: 状态}
    #     :return:{state: True/False, msgs: 提示信息, info: 数据信息}
    #     """
    #     user = http.request.jsonrequest.get('user')
    #     state = http.request.jsonrequest.get('state')
    #     try:
    #         # 判断入参
    #         if user and state:
    #             if state not in ['complete','running']:
    #                 raise UserError('状态入参错误!')
    #             user_id = http.request.env(user=SUPERUSER_ID)['res.users'].search([('login', '=', user)],limit=1)
    #             if not user_id:
    #                 raise UserError('入参错误,未查找到对应数据!')
    #             # 根据状态取值判断已完成未完成
    #             if state == 'running':
    #                 approval_ids = http.request.env['wait.approval'].search(
    #                     [('state', '=', state),('user_id','=',user_id.id)])
    #             else:
    #                 approval_ids = http.request.env['approval.record'].search(
    #                     [('approval_state', 'in', ('reject',state)), ('node_user_id', '=', user_id.id)])
    #             # 汇总数据
    #             if state == 'running':
    #                 approval_list,info = [],[]
    #                 for i in approval_ids:
    #                     approval_list.append(str({
    #                         'model': i.model_name,
    #                         'model_name': http.request.env(user=SUPERUSER_ID)['ir.model'].search([
    #                             ('model', '=', i.model_name)], limit=1).name,
    #                     }))
    #             else:
    #                 approval_list, info = [], []
    #                 for i in approval_ids:
    #                     approval_list.append(str({
    #                         'model': i.model_class,
    #                         'model_name': http.request.env(user=SUPERUSER_ID)['ir.model'].search([
    #                             ('model', '=', i.model_class)], limit=1).name,
    #                     }))
    #             set_list,end_list = list(set(approval_list)),[]
    #             for i in set_list:
    #                 end_list.append({
    #                     'model': eval(i)['model'],
    #                     'model_name': eval(i)['model_name'],
    #                     'num': approval_list.count(i)
    #                 })
    #             for i in end_list:
    #                 # 取配置
    #                 setting_id = http.request.env['roke.app.general.order.setting'].search(
    #                     [('model_id.model', '=', i['model']), ('is_approval', '=', True)], limit=1)
    #                 if not setting_id:
    #                     raise UserError('数据未配置,请前往通用单据配置完成[%s]配置!' %i['model_name'])
    #                 # 取目标审批单据
    #                 if state == 'running':
    #                     app_ids = http.request.env['wait.approval'].search(
    #                         [('state', '=', state), ('user_id', '=', user_id.id),('model_name','=',i['model'])])
    #                 else:
    #                     app_ids = http.request.env['approval.record'].search(
    #                         [('approval_state', 'in', ('reject',state)), ('node_user_id', '=', user_id.id), ('model_class', '=', i['model'])])
    #                 # 循环遍历取待需数据
    #                 record_list = []
    #                 if state == 'running':
    #                     if setting_id.detail_field_id:
    #                         for app in app_ids:
    #                             record = http.request.env[app.model_name].search([('id','=',app.res_id)])
    #                             record_valus = self._get_field_valus(record, setting_id.list_field_ids,True)
    #                             record_list.append(record_valus)
    #                     else:
    #                         for app in app_ids:
    #                             record = http.request.env[app.model_name].search([('id','=',app.res_id)])
    #                             record_valus = self._get_field_valus(record, setting_id.list_field_ids)
    #                             record_list.append(record_valus)
    #                 else:
    #                     if setting_id.detail_field_id:
    #                         for app in app_ids:
    #                             record = http.request.env[app.model_class].search([('id','=',app.res_id)])
    #                             record_valus = self._get_field_valus(record, setting_id.list_field_ids,True)
    #                             record_list.append(record_valus)
    #                     else:
    #                         for app in app_ids:
    #                             record = http.request.env[app.model_class].search([('id','=',app.res_id)])
    #                             record_valus = self._get_field_valus(record, setting_id.list_field_ids)
    #                             record_list.append(record_valus)
    #                 # 总数据
    #                 info.append({
    #                     'total_value': i,
    #                     'record_valus': record_list
    #                 })
    #             result = {'state': 'success', 'msgs': '获取成功', 'info': info}
    #         else:
    #             raise UserError('入参存在为空!')
    #     except Exception as e:
    #         result = {'state': 'error', 'msgs': e}
    #     print('--->', result)
    #     return result

    # 获取审批节点
    @http.route('/roke/get/approval_node', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_get_approval_node(self):
        """
        获取审批节点
        :parameter: {model: 模型名}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        model = http.request.jsonrequest.get('model')
        res_id = http.request.jsonrequest.get('res_id')
        try:
            if model and res_id:
                info,node_ids = [],[]
                flow_id = http.request.env['approval.flow'].search([
                    ('model_id.model','=',model)],order='id desc',limit=1)
                if not flow_id:
                    raise UserError('入参错误查不到审批流程!')
                instance_node = http.request.env(user=SUPERUSER_ID)['approval.flow.instance.node'].search(
                        [('res_id', '=', int(res_id)),('model_name', '=', model),('state','=','running')],limit=1)
                if not instance_node:
                    raise UserError('入参错误查不到数据!')
                cancel_instance_obj = http.request.env(user=SUPERUSER_ID)['approval.flow.instance'].search(
                    [('model_name','=',instance_node.model_name),('res_id','=',instance_node.res_id),
                     ('state','=','active')],order = 'id desc',limit = 1)
                str_node_ids = json.loads(cancel_instance_obj.str_node_ids)
                filter_instance_node_obj = list(
                    filter(lambda x: x['node_id'] == instance_node.node_id.id, str_node_ids))
                for filter_instance_node in filter_instance_node_obj:
                    instance_node_serial_num = filter_instance_node[
                        'serial_num']
                    for node_info in str_node_ids:
                        if node_info['serial_num'] == instance_node_serial_num:
                            if instance_node.leader_parent_user_id:
                                node_ids.append(node_info['node_id'])
                        elif node_info['serial_num'] < instance_node_serial_num:
                            node_ids.append(node_info['node_id'])
                for i in node_ids:
                    node = http.request.env['approval.flow.node'].search([('id', '=', i)])
                    info.append({
                        'node_id': node.id,
                        'node_name': node.name
                    })
                result = {'state': 'success', 'msgs': '获取成功', 'info': info}
            else:
                raise UserError('入参为空!')
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    def _multi_approval(self,instance_node_id,res_id,model,user_id):
        instance_node_obj = http.request.env['approval.flow.instance.node']
        if not instance_node_id:
            return False
        instance_node = instance_node_obj.search([('id','=',int(instance_node_id))])
        serial_num = instance_node.serial_num  # 当前节点的序号
        instance = instance_node.instance_id  # 当前节点对应的实例
        instance_id = instance.id
        next_instance_nodes = instance_node_obj.search([('res_id','=',res_id),
                                                        ('model_name','=',model),
                                                        ('serial_num','>=',serial_num + 1),
                                                        ('instance_id','=',instance_id),
                                                        ('state','=','active')])
        if not next_instance_nodes:
            return False
        next_wait_user_ids = []
        for next_node in next_instance_nodes:
            next_wait_user_ids.extend(next_node.get_wait_approval_users())
        if user_id in next_wait_user_ids:
            return True
        else:
            return False

    def send_email(self,user_id,document,msg):
        _logger.info("sendEmail Called")
        return http.request.env(user=SUPERUSER_ID)['mail.send'].send_mail(document._name, document.id, user_id, msg)

    def send_msg(self,m_partner_id,wait_approval_or_instance_id,document,body=None):
        return http.request.env(user=SUPERUSER_ID)['mail.message'].sudo().message_post(m_partner_id,wait_approval_or_instance_id,document,body)

    # 抄送
    def copy_for(self,document,instance_node_id,copy_for_ids,user_id):
        """抄送处理"""
        copy_for_obj = http.request.env(user=user_id)['approval.copy_for']
        for copy_for_user_id in copy_for_ids:
            copy_for_obj.create({
                'instance_node_id':instance_node_id,
                'model':document._name,
                'res_id':document.id,
                'copy_for_user_id':copy_for_user_id,
                'from_user_id':user_id
            })
            copy_user_id = http.request.env(user=SUPERUSER_ID)['res.users'].search([('id','=',int(copy_for_user_id))]).partner_id.id
            msg = self.send_msg(copy_user_id,None,document,
                                "%s发起的%s流程抄送给您" % (document.create_uid.partner_id.name,document._description))
            self.send_email(http.request.env(user=SUPERUSER_ID)['res.users'].search([('id','=',int(copy_for_user_id))]),document,msg)

    # 确认审批
    @http.route('/roke/approval/sure', type='json', method=["POST", "OPTIONS"], auth='user', cors='*',csrf=False)
    def roke_approval_sure(self):
        """
        确认审批
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        state = http.request.jsonrequest.get('state')
        res_id = http.request.jsonrequest.get('res_id')
        model = http.request.jsonrequest.get('model')
        note = http.request.jsonrequest.get('note')
        user = http.request.jsonrequest.get('user')
        node_id = http.request.jsonrequest.get('node_id')
        copy_for_ids = http.request.jsonrequest.get('copy_for_ids')
        try:
            if state and res_id and model:
                user_id = http.request.env.user.id
                # instance_obj = http.request.env(user=user_id)['approval.flow.instance'].search([('model_name', '=', model), ('res_id', '=', int(res_id)),('state','=','active')])
                # if not instance_obj:
                #     raise UserError('instance_obj未查找到对应数据!')
                res_state = http.request.env(user=user_id)['record.approval.state'].search([('model', '=', model), ('res_id', '=', int(res_id))])
                wait_approval_obj = http.request.env(user=user_id)['wait.approval'].search(
                    [('model_name', '=', model), ('res_id', '=', int(res_id)), ('user_id', '=', int(user_id)),('index_state', '=', '待审批')])
                wait_approval_obj.write({'state':'complete',
                                     'model_class':http.request.env(user=SUPERUSER_ID)['ir.model'].search([('model', '=', model)],limit=1).name,
                                     'approvaled_date':datetime.now()-timedelta(hours=8)})
                approval_obj = http.request.env(user=user_id)['approval']
                if res_state.approval_state == 'pause':
                    raise UserError(u'审批流程暂停!')
                # 实例节点信息
                instance_node_id = http.request.env(user=user_id)['approval.flow.instance.node'].search(
                    [('model_name', '=', model), ('res_id', '=', int(res_id)),('state','=','running')],order='serial_num asc',limit=1)
                if not instance_node_id:
                    raise UserError(u'该流程已经审批，请不要重复审批!')
                # 当前记录
                document = http.request.env(user=user_id)[model].sudo().with_context(chat_manager_redirect=1,
                                                                                     approval_callback=1).browse(res_id)
                # 创建审批信息
                approval_obj.create_approval(instance_node_id.id, state, note)
                # 存入审批回执消息
                document.write({'approval_note': note})
                # 审批后的下一步动作
                if state == 'accept':
                    instance_node_id.accept_next_action(document, True, self._multi_approval(instance_node_id.id,res_id,model,user_id))
                    if copy_for_ids:
                        self.copy_for(document, instance_node_id.id,copy_for_ids,user_id)
                    # 发送通过消息
                    document.write({'user_id': user_id})
                    document.accept_info()
                elif state == 'refuse':
                    node = http.request.env(user=user_id)['approval.flow.node'].search([('id','=',int(node_id))])
                    if not node:
                        raise UserError('node_id入参错误!')
                    instance_node_id.refuse_next_action(document, node, res_state)
                    # 发送驳回消息
                    document.write({'user_id': user_id})
                    document.refuse_info()
                else:
                    raise UserError('state入参错误!')
                # 全部完成发送消息
                if '完成' in document.doc_approval_state:
                    document.write({'user_id': user_id})
                    document.all_info()
                result = {'state': 'success', 'msgs': '审批成功'}
            else:
                raise UserError('必填入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    # 批量审批
    @http.route('/roke/batch/approval', type='json', method=["POST", "OPTIONS"], auth='user', cors='*',csrf=False)
    def roke_batch_approval(self):
        """
        批量审批
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        state = http.request.jsonrequest.get('state')
        res_ids = http.request.jsonrequest.get('res_ids')
        model = http.request.jsonrequest.get('model')
        note = http.request.jsonrequest.get('note')
        user_id = http.request.env.user.id
        node_id = http.request.jsonrequest.get('node_id')
        copy_for_ids = http.request.jsonrequest.get('copy_for_ids')
        try:
            if state and res_ids and model:
                for res_id in res_ids:
                    # instance_obj = http.request.env(user=user_id)['approval.flow.instance'].search(
                    #     [('model_name', '=', model), ('res_id', '=', int(res_id)), ('state', '=', 'active')])
                    # if not instance_obj:
                    #     raise UserError('instance_obj未查找到对应数据!')
                    res_state = http.request.env(user=user_id)['record.approval.state'].search(
                        [('model', '=', model), ('res_id', '=', int(res_id))])
                    wait_approval_obj = http.request.env(user=user_id)['wait.approval'].search(
                        [('model_name', '=', model), ('res_id', '=', int(res_id)), ('user_id', '=', int(user_id)),('index_state', '=', '待审批')])
                    wait_approval_obj.write({'state': 'complete',
                                             'model_class': http.request.env(user=user_id)['ir.model'].search([('model', '=', model)],
                                                                                        limit=1).name,
                                             'approvaled_date': datetime.now() - timedelta(hours=8)})
                    approval_obj = http.request.env(user=user_id)['approval']
                    if res_state.approval_state == 'pause':
                        raise UserError(u'审批流程暂停!')
                    # 实例节点信息
                    instance_node_id = http.request.env(user=user_id)['approval.flow.instance.node'].search(
                        [('model_name', '=', model), ('res_id', '=', int(res_id)),('state','=','running')],order='serial_num asc',limit=1)
                    if not instance_node_id:
                        raise UserError(u'该流程已经审批，请不要重复审批!')
                    # 当前记录
                    document = http.request.env(user=user_id)[model].sudo().with_context(chat_manager_redirect=1, approval_callback=1).browse(res_id)
                    # 创建审批信息
                    approval_obj.create_approval(instance_node_id.id, state, note)
                    # 存入审批回执消息
                    document.write({'approval_note': note})
                    # 审批后的下一步动作
                    if state == 'accept':
                        instance_node_id.accept_next_action(document, True,self._multi_approval(instance_node_id.id, res_id, model,user_id))
                        if copy_for_ids:
                            self.copy_for(document, instance_node_id.id,copy_for_ids,user_id)
                        # 发送通过消息
                        document.write({'user_id': user_id})
                        document.accept_info()
                    elif state == 'refuse':
                        node = http.request.env(user=user_id)['approval.flow.node'].search(
                            [('id', '=', int(node_id))])
                        if not node:
                            raise UserError('node_id入参错误!')
                        instance_node_id.refuse_next_action(document, node, res_state)
                        # 发送驳回消息
                        document.write({'user_id': user_id})
                        document.refuse_info()
                    else:
                        raise UserError('state入参错误!')
                    # 全部完成发送消息
                    if '完成' in document.doc_approval_state:
                        document.write({'user_id': user_id})
                        document.all_info()
                result = {'state': 'success', 'msgs': '审批成功'}
            else:
                raise UserError('必填入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    # 抄送人员列表
    @http.route('/roke/get/copy_for_ids', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_get_copy_for_ids(self):
        """
        抄送人员列表
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        try:
            info = []
            params = http.request.jsonrequest.get('params')
            if params:
                user_id_list = []
                company_id = params.get('context').get('allowed_company_ids')[0]
                user_ids = http.request.env['res.users'].search([])
                for i in user_ids:
                    if company_id in i.company_ids.ids:
                        user_id_list.append(i.id)
                users = http.request.env['res.users'].search([('id', 'in', user_id_list)])
            else:
                users = http.request.env['res.users'].search([])
            for user in users:
                info.append({
                    'user_id': user.id,
                    'user_name': user.name
                })
            result = {'state': 'success', 'msgs': '获取成功', 'info': info}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    @http.route('/roke/approval/submit', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_approval_submit(self):
        """
        提交审批
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        model = http.request.jsonrequest.get('model')
        res_id = http.request.jsonrequest.get('res_id')
        try:
            flow_obj = http.request.env['approval.flow'].with_context(chat_manager_redirect=1)
            approval_flow = flow_obj.get_approval_flow_and_check_approval(model, res_id)
            approval_flow.commit_approval(model, res_id)
            result = {'state': 'success', 'msgs': '成功'}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    # 获取待审批数量
    @http.route('/roke/approval/number', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_approval_number(self):
        """
        获取待审批数量
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        user_id = http.request.env.user.id
        company_id = http.request.env.user.company_id.id
        print(company_id)
        try:
            # 判断是否安装审批
            module = http.request.env(user=SUPERUSER_ID)['ir.module.module'].search(
                [('name', '=', 'web_approval')])
            if module.state == 'installed':
                module_tag = True
            else:
                module_tag = False
            if module_tag:
                approval_ids = http.request.env['wait.approval'].search(
                    [('index_state', '=', '待审批'),('user_id','=',user_id)])
                result = {'state': 'success', 'msgs': '成功', 'number': len(approval_ids)}
            else:
                result = {'state': 'success', 'msgs': '成功', 'number': 0}
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result

    # 获取审批流程
    @http.route('/roke/approval/flow', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def roke_approval_flow(self):
        """
        获取审批流程
        :parameter: {}
        :return:{state: True/False, msgs: 提示信息, info: 数据信息}
        """
        model_index = http.request.jsonrequest.get('model_index')
        res_id = http.request.jsonrequest.get('res_id')
        try:
            if model_index and res_id:
                # 顶部显示
                res_state = http.request.env['record.approval.state'].search([('model', '=', model_index), ('res_id', '=', int(res_id))])
                if res_state.approval_state == 'active':
                    top_state = '审批(审批中)'
                elif res_state.approval_state == 'complete':
                    top_state = '审批(完成)'
                elif res_state.approval_state == 'pause':
                    top_state = '审批(暂停)'
                else:
                    top_state = '审批(取消)'
                # 流程显示
                instance_obj = http.request.env['approval.flow.instance'].with_context(chat_manager_redirect=1)
                document = http.request.env[model_index].sudo().with_context(chat_manager_redirect=1).browse(int(res_id))
                instance_nodes = instance_obj.get_instance_nodes(model_index, int(res_id), res_state, document)
                show_nodes = []
                for instance in instance_nodes:
                    show_str,approval_info = '',instance.get('approval_info')
                    if approval_info:
                        for approval in approval_info:
                            if str(approval.get('action_type')) == '同意':
                                show = 'blue'
                            elif str(approval.get('action_type')) == '拒绝':
                                show = 'red'
                            else:
                                show = False
                            copy_for = (' 抄送：' + str(approval.get('copy_for'))) if str(approval.get('copy_for')) else ''
                            say = (' 审批意见：' + str(approval.get('say'))) if str(approval.get('say')) else ''
                            show_nodes.append({
                                'node_name': instance.get('node_info')['node_name'] or '',
                                'show_str': str(approval.get('user_name')) + ' ' + str(
                                    approval.get('approval_time')) + ' ' + str(
                                    approval.get('action_type')) + say + copy_for,
                                'show': show,
                                'is_node': True if instance.get('node_info')['node_name'] else False
                            })
                    else:
                        show_nodes.append({
                            'node_name': instance.get('node_info')['node_name'] or '',
                            'show_str': '',
                            'show': 'white' if instance.get('node_info')['node_name'] else False,
                            'is_node': True if instance.get('node_info')['node_name'] else False
                        })
                result = {'state': 'success', 'msgs': '获取成功', 'approval_state': top_state,'show_nodes':show_nodes}
            else:
                raise UserError('必填入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msgs': e}
        print('--->', result)
        return result