# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError

class InheritRokeEmployee(models.Model):
    _inherit = "roke.employee"

    department_id = fields.Many2one('roke.department', string='部门', store=True,copy=False)

    job_id = fields.Many2one('roke.job', string='岗位',copy=False)

    # 直属领导
    direct_head_id = fields.Many2one('roke.employee', string='直属领导', copy=False)