# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _

# 添加审批的模块,在继承中添加剂approval.from与对应模,在__manifest__.py添加依赖

ALLOW_APPROVAL = {}

class InheritApprovalFlow(models.Model):
    _inherit = 'approval.flow'

    def _compute_domain(self):
        manual_models = [model.model for model in self.env['ir.model'].search([
            ('state', '=', 'manual'), ('transient', '!=', True)
        ])]
        # 处理卸载之后过滤已有数据
        ALLOW_MODEL = []
        model_obj = self.env['ir.model']
        for model in ALLOW_APPROVAL:
            model_id = model_obj.sudo().search([('model', '=', model)], limit=1)
            if model_id:
                if ALLOW_APPROVAL[model] in model_id.modules:
                    ALLOW_MODEL.append(model)
        return [('model', 'in', ALLOW_MODEL + manual_models)]

    model_id = fields.Many2one('ir.model', u'模型', domain=_compute_domain, index=1)