# -*- coding: utf-8 -*-
"""
Description:
    移动端设置接口
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, http, SUPERUSER_ID, api, _
import logging
import os
from odoo.addons.roke_mes_client.controller.mobile import Mobile
BASE_DIR = os.path.dirname(os.path.dirname(__file__))

try:
    import simplejson as json
except ImportError:
    import json
_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]
class InheritMobile(Mobile):

    def _get_function_info(self, function, icon):
        """
        获取app功能内容
        :param function:
        :return:
        """
        res = super(InheritMobile, self)._get_function_info(function, icon)
        if not function.xbg_app_hide:
            return res

    @http.route('/roke/mes/get_function_list', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_function_list(self):
        """
        获取全部功能接口
        :return:
        """
        user = http.request.env.user
        allow_functions = user.groups_id.app_function_ids
        categories = http.request.env(user=SUPERUSER_ID)['roke.app.function.category'].search([], order='sequence')
        category_info = []
        if categories:
            for category in categories:
                functions = []
                for function in category.function_ids:
                    if not user.has_group("base.group_system") and function not in allow_functions:
                        # 系统管理员不做限制
                        continue
                    if function.icon_image:
                        icon = function.icon_image
                    else:
                        icon = function.index
                    functions.append(self._get_function_info(function, icon))
                if functions:
                    if category.app_attribute == '移动端' and not category.xbg_app_hide:
                        category_info.append({
                            "title": category.name,
                            "icon_1": "../../static/%s.png" % (str(category.index) + '_1'),
                            "icon_2": "../../static/%s.png" % (str(category.index) + '_2'),
                            "icon_3": "../../static/%s.png" % (str(category.index) + '_3'),
                            "icon_4": "../../static/%s.png" % (str(category.index) + '_4'),
                            "details": functions
                        })
            return {"state": "success", "msgs": "获取成功", "result": category_info}