# -*- coding: utf-8 -*-
"""
Description:
    APP功能
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class InheritRokeAppFunctionCategory(models.Model):
    _inherit = "roke.app.function.category"

    xbg_app_hide = fields.Boolean(string="小报工隐藏", default=False)

class InheritRokeAppFunction(models.Model):
    _inherit = "roke.app.function"

    xbg_app_hide = fields.Boolean(string="小报工隐藏", default=False)

    def init(self):
        ids = [
            'roke_mes_stock.mobile_menu_wms',
            'roke_mes_stock.mobile_menu_wms_scrk',
            'roke_mes_stock.mobile_menu_wms_qtrk',
            'roke_mes_stock.mobile_menu_wms_bgrk',
            'roke_mes_stock.mobile_menu_wms_scll',
            'roke_mes_stock.mobile_menu_wms_qtck',
            'roke_mes_stock.mobile_menu_wms_nbtb',
            'roke_mes_quality_enterprise.mobile_menu_zhijian',
            'roke_mes_quality_enterprise.mobile_roke_quality_inspection_tasks',
            'roke_mes_quality_enterprise.mobile_roke_quality_task',
            'roke_mes_equipment.mobile_menu_equipment',
            'roke_mes_equipment.roke_equipment_from_sbbx',
            'roke_mes_equipment.roke_equipment_from_bxjl',
            'roke_mes_equipment.roke_equipment_from_bxzy',
            'roke_mes_equipment.roke_equipment_from_xjzy',
            'roke_mes_equipment.roke_equipment_from_xjjl',
            'roke_mes_equipment.roke_equipment_from_wbgd',
            'roke_mes_equipment.roke_equipment_from_wbzy',
            'roke_mes_equipment.roke_equipment_from_wbjl'
        ]
        for i in ids:
            record = self.env.ref(i,raise_if_not_found=False)
            if record:
                try:
                    record.write({'xbg_app_hide': True})
                except Exception:
                    if record.app_function_id:
                        record.app_function_id.write({'xbg_app_hide': True})
