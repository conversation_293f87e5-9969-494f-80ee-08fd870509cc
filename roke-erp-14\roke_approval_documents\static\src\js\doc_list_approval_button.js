odoo.define('roke_approval_documents.doc_list_approval_button', function (require) {
    "use strict";

    var DocumentsListController = require('documents.DocumentsListController');
    var DocumentsKanbanController = require('documents.DocumentsKanbanController');
    var Dialog = require('web.Dialog');


    var DocumentsListControllerMixin = {
        init: function (parent, model, renderer, params) {
            this._super.apply(this, arguments);
            this.setted = params.initialState.fields.hasOwnProperty('approval_status');
        },

        _bindApproval: function () {
            if (!this.$buttons) {
                return;
            }

            let self = this;

            this.$buttons.on('click', '.f_approval_submit', function (ev) {
                ev.preventDefault();
                ev.stopPropagation();

                let active_res_ids = self.getSelectedIds();
                console.log(active_res_ids);
                console.log(this._selectedRecordIds);
                self._confirmDialog("提交审批", "提交审批", active_res_ids);

            });

            this.$buttons.on('click', '.f_approval_agree', function (ev) {
                ev.preventDefault();
                ev.stopPropagation();
                let active_res_ids = self.getSelectedIds();
                self._confirmDialog("通过审批", "通过审批", active_res_ids);

            });
        }
    };

    DocumentsListController.include({
        init: function () {
            this._super.apply(this, arguments);
            DocumentsListControllerMixin.init.apply(this, arguments);
        },

        willStart() {
            let self = this;
            return this._super(...arguments).then(async function () {
                if (self.setted) {
                    self.show_approval_button = await self.getFolw();
                } else {
                    self.show_approval_button = false;
                }
            });
        },

        getFolw() {
            let self = this;
            return this._rpc({
                model: 'approval.flow',
                method: 'search_count',
                args: [[['model_id.model', '=', self.modelName], ['active', '=', true]]],
            }).then(function (count) {
                return count > 0;
            });
        },

        _commit_approval: function (active_ids) {
            return this._rpc({
                route: "/web/approval/commit_approval_batch",
                params: {
                    model: this.modelName,
                    res_ids: active_ids
                }
            }).then(result => {
                console.log("提交审批结果", result);
                return result;
            });
        },

        _agree_approval: function (active_ids) {
            return this._rpc({
                route: "/web/approval/accept_approval_batch",
                params: {
                    model: this.modelName,
                    res_ids: active_ids
                }
            }).then(result => {
                console.log("通过审批结果", result);
                return result;
            });
        },

        _confirmDialog: function (title, action, active_ids) {
            let self = this;
            let dialog = new Dialog(this, {
                title: title,
                size: 'medium',
                $content: $('<div>', {
                    html: `  
                        <div>确认将所选中的 ${active_ids.length} 条单据 ${action} 吗？</div>                    
                    `,
                }),
                buttons: [
                    {
                        text: '确认', classes: 'btn-primary', click: async function () {
                            if (action == '提交审批') { // 提交审批
                                let result = await self._commit_approval(active_ids);
                                const { commited, success, unnecessary } = result.result;
                                var message = `本次提交审批结果：<br/>提交成功的数量为${success}<br/>已提交审批的数量为${commited}<br/>无需提交的数量为${unnecessary}。`
                            } else { // 通过审批
                                let result = await self._agree_approval(active_ids);
                                const { success, pause, approvaled, unaccess } = result.result;
                                var message = `本次通过审批结果：<br/>审批成功的数量为${success}<br/>已暂停审批的数量为${pause}<br/>已通过审批的数量为${approvaled}<br/>无权审批的数量为${unaccess}。`
                            }
                            self.do_notify(`已批量 ${action}`, message);
                            self.reload();
                            dialog.close();
                        }
                    },
                    { text: '取消', close: true }
                ],
            });
            dialog.open();
        },

        _onSelectionChanged: function (ev) {
            this._super.apply(this, arguments);
            if (this.selectedRecords.length > 0 && this.show_approval_button) {
                this.$buttons.find('.o_list_buttons_approval').show();
            } else {
                this.$buttons.find('.o_list_buttons_approval').hide();
            }
        },

        updateButtons: function (mode) {
            this._super.apply(this, arguments);
            if (this.selectedRecords.length > 0 && this.show_approval_button) {
                this.$buttons.find('.o_list_buttons_approval').show();
            } else {
                this.$buttons.find('.o_list_buttons_approval').hide();
            }
        },

        renderButtons: function () {
            this._super.apply(this, arguments);
            DocumentsListControllerMixin._bindApproval.call(this);
        }
    });


    var DocumentsKanbanControllerMixin = {
        init: function (parent, model, renderer, params) {
            this._super.apply(this, arguments);
        },

        _bindApproval: function () {
            if (!this.$buttons) {
                return;
            }

            let self = this;

            this.$buttons.on('click', '.f_approval_submit', function (ev) {
                ev.preventDefault();
                ev.stopPropagation();

                let active_res_ids = self._selectedRecordIds;
                self._confirmDialog("提交审批", "提交审批", active_res_ids);

            });

            this.$buttons.on('click', '.f_approval_agree', function (ev) {
                ev.preventDefault();
                ev.stopPropagation();
                let active_res_ids = self._selectedRecordIds;
                self._confirmDialog("通过审批", "通过审批", active_res_ids);
            });
        }
    };

    DocumentsKanbanController.include({
        init: function () {
            this._super.apply(this, arguments);
            this.setted = this.initialState.fields.hasOwnProperty('approval_status');
            DocumentsKanbanControllerMixin._bindApproval.call(this);
        },

        willStart() {
            let self = this;
            return this._super(...arguments).then(async function () {
                if (self.setted) {
                    self.show_approval_button = await self.getFolw();
                } else {
                    self.show_approval_button = false;
                }
            });
        },

        getFolw() {
            let self = this;
            return this._rpc({
                model: 'approval.flow',
                method: 'search_count',
                args: [[['model_id.model', '=', self.modelName], ['active', '=', true]]],
            }).then(function (count) {
                return count > 0;
            });
        },


        _commit_approval: function (active_ids) {
            return this._rpc({
                route: "/web/approval/commit_approval_batch",
                params: {
                    model: this.modelName,
                    res_ids: active_ids
                }
            }).then(result => {
                console.log("提交审批结果", result);
                return result;
            });
        },

        _agree_approval: function (active_ids) {
            return this._rpc({
                route: "/web/approval/accept_approval_batch",
                params: {
                    model: this.modelName,
                    res_ids: active_ids
                }
            }).then(result => {
                console.log("通过审批结果", result);
                return result;
            });
        },

        _confirmDialog: function (title, action, active_ids) {
            let self = this;
            let dialog = new Dialog(this, {
                title: title,
                size: 'medium',
                $content: $('<div>', {
                    html: `  
                        <div>确认将所选中的 ${active_ids.length} 条单据 ${action} 吗？</div>                    
                    `,
                }),
                buttons: [
                    {
                        text: '确认', classes: 'btn-primary', click: async function () {
                            if (action == '提交审批') { // 提交审批
                                let result = await self._commit_approval(active_ids);
                                const { commited, success, unnecessary } = result.result;
                                var message = `本次提交审批结果：<br/>提交成功的数量为${success}<br/>已提交审批的数量为${commited}<br/>无需提交的数量为${unnecessary}。`
                            } else { // 通过审批
                                let result = await self._agree_approval(active_ids);
                                const { success, pause, approvaled, unaccess } = result.result;
                                var message = `本次通过审批结果：<br/>审批成功的数量为${success}<br/>已暂停审批的数量为${pause}<br/>已通过审批的数量为${approvaled}<br/>无权审批的数量为${unaccess}。`
                            }
                            self.do_notify(`已批量 ${action}`, message);
                            self.reload();
                            dialog.close();
                        }
                    },
                    { text: '取消', close: true }
                ],
            });
            dialog.open();
        },

        async _onSelectRecord(ev) {
            await this._super(...arguments);
            if (this._selectedRecordIds.length > 0 && this.show_approval_button) {
                this.$buttons.find('.o_list_buttons_approval').show();
            } else {
                this.$buttons.find('.o_list_buttons_approval').hide();
            }
        },

        updateButtons: function (mode) {
            this._super.apply(this, arguments);
            this.$buttons.find('.o_list_buttons_approval').hide();
        },

        renderButtons: function () {
            this._super.apply(this, arguments);
            DocumentsKanbanControllerMixin._bindApproval.call(this);
            if (this.show_approval_button > 0 && this.show_approval_button) {
                this.$buttons.find('.o_list_buttons_approval').show();
            } else {
                this.$buttons.find('.o_list_buttons_approval').hide();
            }
        }
    });

});