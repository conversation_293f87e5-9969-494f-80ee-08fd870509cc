# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.addons.roke_all_approval.model.roke_all_approval import ALLOW_APPROVAL

# 生产订单
class RokeProductionOrder(models.Model):
    _name = "roke.production.order"
    _inherit = ["approval.from", "mail.thread", "roke.production.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.production.order": "roke_mes_production"})

# 生产任务
class RokeProductionTask(models.Model):
    _name = "roke.production.task"
    _inherit = ["approval.from", "mail.thread", "roke.production.task"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.production.task": "roke_mes_production"})

# 生产工单
class RokeWorkOrder(models.Model):
    _name = "roke.work.order"
    _inherit = ["approval.from", "mail.thread", "roke.work.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.work.order": "roke_mes_production"})

# 报工记录
class RokeWorkRecord(models.Model):
    _name = "roke.work.record"
    _inherit = ["approval.from", "mail.thread", "roke.work.record"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.work.record": "roke_mes_production"})

class InheritApprovalFlow(models.Model):
    _inherit = 'approval.flow'

    def _compute_domain(self):
        manual_models = [model.model for model in self.env['ir.model'].search([
            ('state', '=', 'manual'), ('transient', '!=', True)
        ])]
        # 处理卸载之后过滤已有数据
        ALLOW_MODEL = []
        model_obj = self.env['ir.model']
        for model in ALLOW_APPROVAL:
            model_id = model_obj.sudo().search([('model', '=', model)], limit=1)
            if model_id:
                if ALLOW_APPROVAL[model] in model_id.modules:
                    ALLOW_MODEL.append(model)
        return [('model', 'in', ALLOW_MODEL + manual_models)]


    model_id = fields.Many2one('ir.model', u'模型', domain=_compute_domain, index=1)