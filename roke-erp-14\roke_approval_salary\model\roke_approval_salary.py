# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.addons.roke_all_approval.model.roke_all_approval import ALLOW_APPROVAL

# 计价确认单
class RokeBaseSalaryOrder(models.Model):
    _name = "roke.work.salary.confirm.order"
    _inherit = ["mail.thread", "approval.from", "roke.work.salary.confirm.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.work.salary.confirm.order": "roke_mes_salary"})

# 工资单
class RokeSalaryOrder(models.Model):
    _name = "roke.salary.order"
    _inherit = ["mail.thread", "approval.from", "roke.salary.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.salary.order": "roke_mes_salary"})

# 工资奖惩单
class RokeExtraSalaryOrder(models.Model):
    _name = "roke.extra.salary.order"
    _inherit = ["mail.thread", "approval.from", "roke.extra.salary.order"]
    _rec_name = "code"

ALLOW_APPROVAL.update({"roke.extra.salary.order": "roke_mes_salary"})

class InheritApprovalFlow(models.Model):
    _inherit = 'approval.flow'

    def _compute_domain(self):
        manual_models = [model.model for model in self.env['ir.model'].search([
            ('state', '=', 'manual'), ('transient', '!=', True)
        ])]
        # 处理卸载之后过滤已有数据
        ALLOW_MODEL = []
        model_obj = self.env['ir.model']
        for model in ALLOW_APPROVAL:
            model_id = model_obj.sudo().search([('model', '=', model)], limit=1)
            if model_id:
                if ALLOW_APPROVAL[model] in model_id.modules:
                    ALLOW_MODEL.append(model)
        return [('model', 'in', ALLOW_MODEL + manual_models)]


    model_id = fields.Many2one('ir.model', u'模型', domain=_compute_domain, index=1)