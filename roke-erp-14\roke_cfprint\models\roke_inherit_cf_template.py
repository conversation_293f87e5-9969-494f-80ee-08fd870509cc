# -*- coding: utf-8 -*-
"""
Description:
    继承康虎模板管理
        创建report_action
        创建qweb
        TODO 代码美化
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError


QWeb_Template = """
<t t-call="cfprint.html_container">   <!-- 这里调用 cfprint.html_container ，以便于引入康虎云报表JavaScript基础库-->
    <div t-if="len(docs) &lt;= 0">
        <h2 style="text-align: center;">没有可打印的数据，请返回。</h2>
    </div>
    <div t-if="len(docs) &gt; 0">
        <h2 style="text-align: center;">正在打印，请稍候...</h2>
    </div>
    <!--必须先安装cfprint模块，以引入基础类库-->
    <script type="text/javascript">
        var cfprint_addr = "127.0.0.1"; //打印服务器监听地址
        var _delay_close = -1; //打印完成后关闭窗口的延时时长(毫秒), -1则表示不关闭
        /******************************* 康虎云报表与ODOO集成时，模板调用方法 **********************************/
        /*方法1：
        从数据库中获取打印模板（调用简短方法）（在正式使用时要把全角尖括号替换成半角尖括号，并且把sale_order_demo换成实际的模板templ_id）（在真正使用时，必须把全角尖括号改成半角）：*/
        var _data = {"template": "base64:<t t-esc="user.env['cf.template'].search([('templ_id', '=', '%s')], limit=1).template"/>", "ver": 4, "Copies": 1,
        "Duplex": 0, "Tables":[]};
        /*******************************************************************************************************/

        //生成表结构->主表、从表
        %s
        var _reportData = JSON.stringify(_data); //转成json字符串
        /*生成数据之后，在cfprint_ext.js中会自动调用进行打印*/
    </script>
</t> <!-- End of cfprint.html_container -->
"""

DataTemplate = """
        //生成主表结构
        var %s = {
        "Name": "%s",
        "Cols":[
        %s
        ],
        "Data":[ ]
        };
        <t t-foreach="docs" t-as="doc">
            %s
            %s.Data.push(
            {
            %s
            });
        </t>
        /*数据合并到总的数据对象*/
        _data["Tables"].push(%s);
"""

RelationDataTemplate = """
        //生成从表结构
        var %s = {
        "Name": "%s",
        "Cols":[
        %s
        ],
        "Data":[ ]
        };
        <t t-foreach="docs" t-as="doc">
            <t t-foreach="%s" t-as="%s">
                %s
                %s.Data.push(
                {
                %s
                });
            </t>
        </t>
        /*数据合并到总的数据对象*/
        _data["Tables"].push(%s);
"""


class RokeInheritCFTemplate(models.Model):
    _inherit = "cf.template"

    print_data_ids = fields.One2many("roke.cf.data.template", "cf_id", string="打印数据")
    demo = fields.Boolean(string="预制数据", default=False)

    def unlink(self):
        self.print_data_ids.unlink()
        return super(RokeInheritCFTemplate, self).unlink()


class RokeCFDataTemplate(models.Model):
    _name = "roke.cf.data.template"
    _inherit = ['mail.thread']
    _description = "打印配置及数据集"

    cf_id = fields.Many2one("cf.template", string="康虎模板ID", required=True, ondelete="cascade")
    name = fields.Char(string="名称", required=True)
    model_id = fields.Many2one("ir.model", string="来源模型", required=True, ondelete="cascade")
    field_ids = fields.Many2many("ir.model.fields", string="数据字段")
    qweb_id = fields.Many2one("ir.ui.view", string="QWeb视图")
    action_id = fields.Many2one("ir.actions.report", string="报表动作ID")
    module = fields.Char(string="应用")

    @api.model
    def _get_id(self, module, xml_id):
        print(module,xml_id)

    @staticmethod
    def _generate_col_title(field):
        """
        生成打印数据集列明
        """
        qweb_col = '{"type": "str", "size": 300, "name": "%s", "required": false },' % field.name
        return qweb_col

    @staticmethod
    def _generate_col_data(field, obj="doc"):
        """
        生成打印数据集内容
        """
        # if field.ttype in ("many2one",):
        #     qweb_col_data = '"%s":"<t t-esc="%s.%s.display_name"/>",' % (field.name, obj, field.name)
        # else:
        #     qweb_col_data = '"%s":"<t t-esc="%s.%s"/>",' % (field.name, obj, field.name)
        return '"%s":"<t t-esc="%s_%s"/>",' % (field.name, obj, field.name)

    @staticmethod
    def _generate_col_data_format(field, format="0", obj="doc"):
        """
        生成打印数据集内容;回车转义
        """
        if field.ttype in ("many2one",):
            qweb_col_data_format = """
                <t t-if="%s == 1 and %s.%s.display_name">
                  <t t-set="%s_%s" t-value="(%s.%s.display_name or '').replace('\\n','\\\\n').encode('utf-8')"/>
                </t>
                <t t-else="">
                  <t t-set="%s_%s" t-value="%s.%s.display_name"/>
                </t>
            """ % (format, obj, field.name, obj, field.name, obj, field.name, obj, field.name, obj, field.name)
        else:
            qweb_col_data_format = """
                <t t-if="%s == 1 and %s.%s">
                  <t t-set="%s_%s" t-value="(%s.%s or '').replace('\\n','\\\\n').encode('utf-8')"/>
                </t>
                <t t-else="">
                  <t t-set="%s_%s" t-value="%s.%s"/>
                </t>
            """ % (format, obj, field.name, obj, field.name, obj, field.name, obj, field.name, obj, field.name)
        return qweb_col_data_format

    def _create_qweb_view(self):
        """创建Qweb 视图"""
        ViewObj = self.sudo().env["ir.ui.view"]
        DataObj = self.sudo().env["ir.model.data"]
        # 生成表结构
        data = ""
        # 主表
        qweb_col = ""
        qweb_col_data_format = ""
        qweb_col_data = ""
        relation_fields = []
        m2o_fields = []
        for field in self.field_ids:
            format = "0"
            if field.ttype in ("many2many", "one2many"):
                relation_fields.append(field)
                continue
            elif field.ttype == "many2one":
                m2o_fields.append(field)
            elif field.ttype in ["char", "text", "html"]:
                format = "1"
            qweb_col += self._generate_col_title(field)
            qweb_col_data_format += self._generate_col_data_format(field, format=format)
            qweb_col_data += self._generate_col_data(field)
        data += DataTemplate % (
            self.model_id.model.replace(".", "_"),
            self.model_id.name,
            qweb_col,
            qweb_col_data_format,
            self.model_id.model.replace(".", "_"),
            qweb_col_data,
            self.model_id.model.replace(".", "_")
        )
        # 多对一字段
        for m2o in m2o_fields:
            relation_model =  self.sudo().env["ir.model"].search([("model", "=", m2o.relation)], limit=1)
            if not relation_model:
                continue
            relation_field_cols = self.sudo().env["ir.model.fields"].search([
                ("model_id", "=", relation_model.id), ("ttype", "not in", ("many2many", "one2many"))
            ])
            qweb_col = ""
            qweb_col_data = ""
            qweb_col_data_format = ""
            for field in relation_field_cols:
                if field.name == "__last_update":
                    continue
                format = 0
                if field.ttype in ["char", "text", "html"]:
                    format = "1"
                qweb_col += self._generate_col_title(field)
                qweb_col_data_format += self._generate_col_data_format(field, format=format, obj=m2o.name)
                qweb_col_data += self._generate_col_data(field, obj=m2o.name)
            data += RelationDataTemplate % (
                relation_model.model.replace(".", "_"),
                relation_model.name,
                qweb_col,
                "doc.%s" % m2o.name,
                m2o.name,
                qweb_col_data_format,
                relation_model.model.replace(".", "_"),
                qweb_col_data,
                relation_model.model.replace(".", "_")
            )
        # 遍历子表字段
        for relation_field in relation_fields:
            relation_model =  self.sudo().env["ir.model"].search([("model", "=", relation_field.relation)], limit=1)
            if not relation_model:
                continue
            relation_field_cols = self.sudo().env["ir.model.fields"].search([
                ("model_id", "=", relation_model.id), ("ttype", "not in", ("many2many", "one2many"))
            ])
            qweb_col = ""
            qweb_col_data_format = ""
            qweb_col_data = ""
            for field in relation_field_cols:
                if field.name == "__last_update":
                    continue
                format = 0
                if field.ttype in ["char", "text", "html"]:
                    format = "1"
                qweb_col += self._generate_col_title(field)
                qweb_col_data_format += self._generate_col_data_format(field, format=format, obj=relation_field.name)
                qweb_col_data += self._generate_col_data(field, obj=relation_field.name)
            data += RelationDataTemplate % (
                relation_model.model.replace(".", "_"),
                relation_model.name,
                qweb_col,
                "doc.%s" % relation_field.name,
                relation_field.name,
                qweb_col_data_format,
                relation_model.model.replace(".", "_"),
                qweb_col_data,
                relation_model.model.replace(".", "_")
            )

        # 生成qweb模板
        qweb_data = QWeb_Template % (self.cf_id.templ_id, data)
        # 创建视图
        view = ViewObj.create({
            "name": "%s_print" % self.name,
            "key": "roke_cfprint.%s_print" % self.name,
            "type": "qweb",
            "model": "primary",
            "arch_fs": "roke_cfprint/report/roke_print_tmpl_views.xml",
            "arch_db": qweb_data
        })
        # 创建视图模型数据-->ir.model.data
        DataObj.create({
            "noupdate": True,
            "name": "%s_print" % self.name,
            "module": self.module or '__report__',
            "model": "ir.ui.view",
            "res_id": view.id
        })
        self.write({"qweb_id": view.id})

    def _create_report_action(self):
        """创建报表动作"""
        ActionObj = self.sudo().env["ir.actions.report"]
        DataObj = self.sudo().env["ir.model.data"]
        action = ActionObj.create({
            'name': self.name,
            'model': self.model_id.model,
            'report_type': 'qweb-html',
            'report_name': "roke_cfprint.%s_print" % self.name,  # 与Qweb key一致
            'binding_model_id': self.model_id.id
        })
        DataObj.create({
            "noupdate": True,
            "name": self.name,
            "module": self.module or '__report__',
            "model": "ir.actions.report",
            "res_id": action.id
        })
        self.write({"action_id": action.id})

    @api.model
    def create(self, vals):
        """创建时生成qweb、报表动作"""
        res = super(RokeCFDataTemplate, self).create(vals)
        res._create_qweb_view()
        res._create_report_action()
        return res

    def remodel_qweb(self):
        """存在时修改，不存在时创建"""
        if self.qweb_id:
            self._update_qweb_view()
        else:
            self._create_qweb_view()
            self._create_report_action()

    def _update_qweb_view(self):
        """修改Qweb 视图"""
        # 生成表结构
        data = ""
        # 主表
        qweb_col = ""
        qweb_col_data_format = ""
        qweb_col_data = ""
        m2o_fields = []
        relation_fields = []
        for field in self.field_ids:
            format = "0"
            if field.ttype in ("many2many", "one2many"):
                relation_fields.append(field)
                continue
            elif field.ttype == "many2one":
                m2o_fields.append(field)
            elif field.ttype in ["char", "text", "html"]:
                format = "1"
            qweb_col += self._generate_col_title(field)
            qweb_col_data_format += self._generate_col_data_format(field, format=format)
            qweb_col_data += self._generate_col_data(field)
        data += DataTemplate % (
            self.model_id.model.replace(".", "_"),
            self.model_id.name,
            qweb_col,
            qweb_col_data_format,
            self.model_id.model.replace(".", "_"),
            qweb_col_data,
            self.model_id.model.replace(".", "_")
        )
        # 多对一字段
        for m2o in m2o_fields:
            relation_model = self.sudo().env["ir.model"].search([("model", "=", m2o.relation)], limit=1)
            if not relation_model:
                continue
            relation_field_cols = self.sudo().env["ir.model.fields"].search([
                ("model_id", "=", relation_model.id), ("ttype", "not in", ("many2many", "one2many"))
            ])
            qweb_col = ""
            qweb_col_data_format = ""
            qweb_col_data = ""
            for field in relation_field_cols:
                if field.name == "__last_update":
                    continue
                format = 0
                if field.ttype in ["char", "text", "html"]:
                    format = "1"
                qweb_col += self._generate_col_title(field)
                qweb_col_data_format += self._generate_col_data_format(field, format=format, obj=m2o.name)
                qweb_col_data += self._generate_col_data(field, obj=m2o.name)
            data += RelationDataTemplate % (
                relation_model.model.replace(".", "_"),
                relation_model.name,
                qweb_col,
                "doc.%s" % m2o.name,
                m2o.name,
                qweb_col_data_format,
                relation_model.model.replace(".", "_"),
                qweb_col_data,
                relation_model.model.replace(".", "_")
            )
        # 遍历子表字段
        for relation_field in relation_fields:
            relation_model =  self.sudo().env["ir.model"].search([("model", "=", relation_field.relation)], limit=1)
            if not relation_model:
                continue
            relation_field_cols = self.sudo().env["ir.model.fields"].search([
                ("model_id", "=", relation_model.id), ("ttype", "not in", ("many2many", "one2many"))
            ])
            qweb_col = ""
            qweb_col_data_format = ""
            qweb_col_data = ""
            for field in relation_field_cols:
                if field.name == "__last_update":
                    continue
                format = 0
                if field.ttype in ["char", "text", "html"]:
                    format = "1"
                qweb_col += self._generate_col_title(field)
                qweb_col_data_format += self._generate_col_data_format(field, format=format, obj=relation_field.name)
                qweb_col_data += self._generate_col_data(field, obj=relation_field.name)
            data += RelationDataTemplate % (
                relation_model.model.replace(".", "_"),
                relation_model.name,
                qweb_col,
                "doc.%s" % relation_field.name,
                relation_field.name,
                qweb_col_data_format,
                relation_model.model.replace(".", "_"),
                qweb_col_data,
                relation_model.model.replace(".", "_")
            )

        # 生成qweb模板
        qweb_data = QWeb_Template % (self.cf_id.templ_id, data)
        # 修改视图
        self.qweb_id.sudo().write({
            "name": "%s_print" % self.name,
            "key": "roke_cfprint.%s_print" % self.name,
            "type": "qweb",
            "model": "primary",
            "arch_fs": "roke_cfprint/report/roke_print_tmpl_views.xml",
            "arch_db": qweb_data
        })

    def unlink(self):
        DataObj = self.sudo().env["ir.model.data"]
        DataObj.search([("model", "=", "ir.ui.view"), ("res_id", "in", self.qweb_id.ids)]).unlink()
        DataObj.search([("model", "=", "ir.actions.report"), ("res_id", "in", self.action_id.ids)]).unlink()
        self.qweb_id.unlink()
        self.action_id.unlink()
        return super(RokeCFDataTemplate, self).unlink()
