<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="roke_inherit_cf_template_form" model="ir.ui.view">
        <field name="name">roke.inherit.cf.template.form</field>
        <field name="model">cf.template</field>
        <field name="inherit_id" ref="cfprint.cf_template_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page" position="before">
                <page string="打印配置及数据集">
                    <field name="print_data_ids">
                        <tree editable="bottom">
                            <field name="cf_id" invisible="1"/>
                            <field name="name"/>
                            <field name="model_id" options="{'no_open': True, 'no_create': True}"/>
                            <field name="field_ids" widget="many2many_tags" domain="[('model_id', '=', model_id)]"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="module" required="1"/>
                            <field name="qweb_id" groups="base.group_no_one" readonly="1"/>
                            <field name="action_id" groups="base.group_no_one" readonly="1"/>
                            <button name="remodel_qweb" string="重新生成Qweb视图" type="object" icon="fa-refresh"/>
                        </tree>
                        <form>
                            <group>
                                <group>
                                    <field name="cf_id" invisible="1"/>
                                    <field name="name"/>
                                    <field name="model_id" options="{'no_open': True, 'no_create': True}"/>
                                    <field name="field_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
                                </group>
                                <group>
                                    <field name="qweb_id" groups="base.group_no_one" readonly="1"/>
                                    <field name="action_id" groups="base.group_no_one" readonly="1"/>
                                </group>
                            </group>
                        </form>
                    </field>
                </page>
            </xpath>
        </field>
    </record>

</odoo>
