# -*- coding: utf-8 -*-

import base64
import datetime
import hmac
import json
import time
from hashlib import sha1 as sha

from odoo import http

EXPIRE_TIME = 300


class OssController(http.Controller):

    @staticmethod
    def get_iso_8601(expire):
        gmt = datetime.datetime.utcfromtimestamp(expire).isoformat()
        gmt += 'Z'
        return gmt

    def get_token(self, host, upload_dir, access_key_id, access_key_secret):
        now = int(time.time())
        expire_sync_point = now + EXPIRE_TIME
        expire = self.get_iso_8601(expire_sync_point)

        policy_dict = {}
        token_dict = {}
        array_item = []
        condition_array = []

        policy_dict['expiration'] = expire
        array_item.append('starts-with')
        array_item.append("$key")
        array_item.append(upload_dir)
        condition_array.append(array_item)
        policy_dict['conditions'] = condition_array
        policy = json.dumps(policy_dict).strip()
        policy_encode = base64.b64encode(policy.encode())
        h = hmac.new(access_key_secret.encode(), policy_encode, sha)
        sign_result = base64.encodebytes(h.digest()).strip()

        token_dict['accessid'] = access_key_id
        token_dict['host'] = host
        token_dict['policy'] = policy_encode.decode()
        token_dict['signature'] = sign_result.decode()
        token_dict['expire'] = expire_sync_point
        token_dict['dir'] = upload_dir
        return token_dict

    @http.route('/roke/oss/policy', type='json', auth='none', csrf=False, cors='*')
    def roke_oss_policy(self):
        params = http.request.env["res.config.settings"].sudo().get_oss_parameters()
        access_key = params["access_key"]
        secret_key = params["secret_key"]
        protocol = params["protocol"]
        bucket_name = params["bucket_name"]
        endpoint = params["endpoint"]
        folder = params["folder"]
        if access_key and secret_key and bucket_name and endpoint and protocol:
            host = f"//{bucket_name}.{endpoint}"
            result = self.get_token(host, folder, access_key, secret_key)
            return {"code": 0, "message": "签名成功", "data": result}
        else:
            return {"code": 1, "message": "签名失败，请到后台配置OSS信息。", "data": None}

    @http.route('/roke/save/cloud/attachment', type='json', auth='none', csrf=False, cors='*')
    def roke_save_cloud_attachment(self):
        name = http.request.jsonrequest.get('name', False)
        host = http.request.jsonrequest.get('host', False)
        path = http.request.jsonrequest.get('path', False)
        size = http.request.jsonrequest.get('size', False)
        mime = http.request.jsonrequest.get('mime', False)

        if name and host and path:
            attachment = http.request.env["roke.cloud.attachment"].create_attachment(
                host, path, name, size, mime
            )
            data = {
                "name": attachment.name, "path": attachment.path, "host": attachment.host,
                "size": attachment.size, "mime": attachment.mime
            }
            return {"code": 0, "message": "保存成功", "data": data}
        else:
            return {"code": 1, "message": "签名失败，请联系管理员在后台配置OSS存储信息。", "data": None}
