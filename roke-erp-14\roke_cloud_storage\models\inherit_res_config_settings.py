# -*- coding: utf-8 -*-

from odoo import models, fields, api


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    oss_access_key = fields.Char(string="AccessKey ID", required=True)
    oss_secret_key = fields.Char(string="AccessKey Secret", required=True)
    oss_protocol = fields.Selection(
        [("http", "HTTP"), ("https", "HTTPS")], required=True, string="Protocol", default="http")
    oss_bucket_name = fields.Char(string="Bucket", required=True)
    oss_endpoint = fields.Char(string="Endpoint", required=True)
    oss_folder = fields.Char(string="Folder", default="resources/")

    def set_values(self):
        super(ResConfigSettings, self).set_values()
        icp = self.env['ir.config_parameter']
        icp.sudo().set_param('oss.access_key', self.oss_access_key)
        icp.sudo().set_param('oss.secret_key', self.oss_secret_key)
        icp.sudo().set_param('oss.protocol', self.oss_protocol or "http")
        icp.sudo().set_param('oss.bucket_name', self.oss_bucket_name)
        icp.sudo().set_param('oss.endpoint', self.oss_endpoint)
        icp.sudo().set_param('oss.folder', self.oss_folder)

    @api.model
    def get_values(self):
        icp = self.env['ir.config_parameter']
        res = super(ResConfigSettings, self).get_values()
        res.update(
            oss_access_key=icp.sudo().get_param('oss.access_key'),
            oss_secret_key=icp.sudo().get_param('oss.secret_key'),
            oss_protocol=icp.sudo().get_param('oss.protocol', 'http'),
            oss_bucket_name=icp.sudo().get_param('oss.bucket_name'),
            oss_endpoint=icp.sudo().get_param('oss.endpoint'),
            oss_folder=icp.sudo().get_param('oss.folder', "resources/"),
        )
        return res

    def get_oss_parameters(self):
        icp = self.env['ir.config_parameter']
        return {
            "access_key": icp.sudo().get_param('oss.access_key', False),
            "secret_key": icp.sudo().get_param('oss.secret_key', False),
            "protocol": icp.sudo().get_param('oss.protocol', False),
            "bucket_name": icp.sudo().get_param('oss.bucket_name', False),
            "endpoint": icp.sudo().get_param('oss.endpoint', False),
            "folder": icp.sudo().get_param('oss.folder', False)
        }
