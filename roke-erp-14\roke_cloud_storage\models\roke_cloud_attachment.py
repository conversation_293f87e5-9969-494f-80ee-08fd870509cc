# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class RokeCloudAttachment(models.Model):
    _name = 'roke.cloud.attachment'
    _description = """云文件"""
    _order = 'id desc'

    name = fields.Char(string="名称", reuqired=True)
    host = fields.Char(string="外链Host", required=True)
    path = fields.Char(string="原图地址")
    mime = fields.Char(string="Mime Type", reuqired=True)
    size = fields.Integer(string="文件大小", default=0, readonly=True)
    active = fields.Boolean(string="归档", default=True)

    def create_attachment(self, host, path, name, size, mime):
        """
        创建云文件记录
        :param host: 访问外链Domain，例如：https://odooattachment.oss-cn-qingdao.aliyuncs.com
        :param path: 文件地址， 例如：resources/23031501520.png
        :param name: 文件名称，例如：23031501520.png
        :param size: 文件大小，例如：1556
        :param mime: 文件类型，例如：image/png
        :return:
        """
        return self.env["roke.cloud.attachment"].create({
            "host": host, "path": path, "name": name, "size": size, "mime": mime
        })
