odoo.define('roke_cloud_storage.cloud_image_gallery', function (require) {
    'use strict';

    const {Component, QWeb} = owl;
    const {xml, css} = owl.tags
    const {useRef, useState} = owl.hooks;

    const MIN_SCALE = 0.5;
    const SCROLL_ZOOM_STEP = 0.1;
    const ZOOM_STEP = 0.5;
    const ANGLE = 0
    const SCALE = 1

    class CloudPictureGallery extends Component {

        constructor(...args) {
            super(...args);
            this.MIN_SCALE = MIN_SCALE;
            this.state = useState({
                angle: ANGLE,
                scale: SCALE,
                isImageLoading: true,
                picture: this.env.gallery_metadata.picture,
                pictures: this.env.gallery_metadata.pictures
            })
            this._isDragging = false;
            this._zoomerRef = useRef('zoomer');
            this._translate = {x: 0, y: 0, dx: 0, dy: 0};
            this._onClickGlobal = this._onClickGlobal.bind(this);
        }

        mounted() {
            this.el.focus();
            this._handleImageLoad();
            document.addEventListener('click', this._onClickGlobal);
        }

        patched() {
            this._handleImageLoad();
        }

        willUnmount() {
            document.removeEventListener('click', this._onClickGlobal);
        }

        //--------------------------------------------------------------------------
        // Public
        //--------------------------------------------------------------------------

        /**
         * 计算图片的 (scale + rotation) 样式.
         *
         * @returns {string}
         */
        get imageStyle() {
            let style = `transform: ` +
                `scale3d(${this.state.scale}, ${this.state.scale}, 1) ` +
                `rotate(${this.state.angle}deg);`;

            if (this.state.angle % 180 !== 0) {
                style += `` +
                    `max-height: ${window.innerWidth}px; ` +
                    `max-width: ${window.innerHeight - 2 * $(".o_main_navbar").height() - 20}px;`;
            } else {
                style += `` +
                    `max-height: ${window.innerHeight - 2 * $(".o_main_navbar").height() - 20}px;` +
                    `max-width: ${window.innerWidth}px; `;
            }
            return style;
        }

        /**
         * 对话框组件的强制方法
         * 当用户当前正在拖动图像时，在单击蒙版时防止关闭对话框。
         *
         * @returns {boolean}
         */
        isCloseable() {
            return !this._isDragging;
        }

        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------

        _close() {
            $(".o_CloudPictureGallery").remove()
        }

        _download() {
            window.location.href = this.state.picture.url
        }

        _handleImageLoad() {
            if (!this.state.picture) {
                return;
            }
            const image = useRef(`image_${this.state.picture.id}`)
            if (!image) {
                this.state.isImageLoading = true;
            }
        }

        _next() {
            this.state.scale = SCALE
            this.state.angle = ANGLE
            const index = this.state.pictures.findIndex(picture => picture.id === this.state.picture.id);
            const nextIndex = (index + 1) % this.state.pictures.length;
            this.state.picture = this.state.pictures[nextIndex]
        }

        _previous() {
            this.state.scale = SCALE
            this.state.angle = ANGLE
            const index = this.state.pictures.findIndex(picture => picture.id === this.state.picture.id);
            const nextIndex = index === 0 ? this.state.pictures.length - 1 : index - 1;
            this.state.picture = this.state.pictures[nextIndex]
        }

        _rotate() {
            this.state.angle = this.state.angle + 90
        }

        /**
         * 停止拖拽用户交互
         *
         * @private
         */
        _stopDragging() {
            this._isDragging = false;
            this._translate.x += this._translate.dx;
            this._translate.y += this._translate.dy;
            this._translate.dx = 0;
            this._translate.dy = 0;
            this._updateZoomerStyle();
        }

        _updateZoomerStyle() {
            const tx = this._translate.x + this._translate.dx;
            const ty = this._translate.y + this._translate.dy;
            if (tx === 0) {
                this._translate.x = 0;
            }
            if (ty === 0) {
                this._translate.y = 0;
            }
            this._zoomerRef.el.style = `transform: translate(${tx}px, ${ty}px)`;
        }

        _zoomIn({scroll = false} = {}) {
            this.state.scale = this.state.scale + (scroll ? SCROLL_ZOOM_STEP : ZOOM_STEP)
            this._updateZoomerStyle();
        }

        _zoomOut({scroll = false} = {}) {
            if (this.state.scale === MIN_SCALE) {
                return;
            }
            const unflooredAdaptedScale = (this.state.scale - (scroll ? SCROLL_ZOOM_STEP : ZOOM_STEP));
            this.state.scale = Math.max(MIN_SCALE, unflooredAdaptedScale)
            this._updateZoomerStyle();
        }

        _zoomReset() {
            this.state.scale = SCALE
            this._updateZoomerStyle();
        }

        //--------------------------------------------------------------------------
        // Handlers
        //--------------------------------------------------------------------------

        /**
         * 单击Gallery的Mask时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClick(ev) {
            if (this._isDragging) {
                return;
            }
            this._close();
        }

        /**
         * 单击关闭图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickClose(ev) {
            this._close();
        }

        /**
         * 单击下载图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickDownload(ev) {
            ev.stopPropagation();
            this._download();
        }

        /**
         * @private
         * @param {MouseEvent} ev
         */
        _onClickGlobal(ev) {
            if (!this._isDragging) {
                return;
            }
            ev.stopPropagation();
            this._stopDragging();
        }

        /**
         * 单击标题时调用。停止传播事件以防止关闭对话框。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickHeader(ev) {
            ev.stopPropagation();
        }

        /**
         * 单击图像时调用。停止传播事件以防止关闭对话框。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickImage(ev) {
            if (this._isDragging) {
                return;
            }
            ev.stopPropagation();
        }

        /**
         * 单击下一个图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickNext(ev) {
            ev.stopPropagation();
            this._next();
        }

        /**
         * 单击上一个图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickPrevious(ev) {
            ev.stopPropagation();
            this._previous();
        }

        /**
         * 单击旋转图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickRotate(ev) {
            ev.stopPropagation();
            this._rotate();
        }

        /**
         * 单击嵌入视频播放器时调用。停止传播以防止关闭对话框。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickVideo(ev) {
            ev.stopPropagation();
        }

        /**
         * 单击放大图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickZoomIn(ev) {
            ev.stopPropagation();
            this._zoomIn();
        }

        /**
         * 单击缩小图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickZoomOut(ev) {
            ev.stopPropagation();
            this._zoomOut();
        }

        /**
         * 单击重置缩放图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickZoomReset(ev) {
            ev.stopPropagation();
            this._zoomReset();
        }

        /**
         * 单击重置位置图标时调用。
         *
         * @private
         * @param {MouseEvent} ev
         */
        _onClickPositionReset(ev) {
            ev.stopPropagation();
            this._translate.dx = 0
            this._translate.dy = 0
            this._translate.x = 0
            this._translate.y = 0
            this._zoomReset();
            this._zoomerRef.el.style = `transform: translate(0px, 0px)`;
        }

        /**
         * @private
         * @param {KeyboardEvent} ev
         */
        _onKeydown(ev) {
            switch (ev.key) {
                case 'ArrowRight':
                    this._next();
                    break;
                case 'ArrowLeft':
                    this._previous();
                    break;
                case 'Escape':
                    this._close();
                    break;
                case 'q':
                    this._close();
                    break;
                case 'r':
                    this._rotate();
                    break;
                case '+':
                    this._zoomIn();
                    break;
                case '-':
                    this._zoomOut();
                    break;
                case '0':
                    this._zoomReset();
                    break;
                default:
                    return;
            }
            ev.stopPropagation();
        }

        /**
         * 加载新图像时调用
         *
         * @private
         * @param {Event} ev
         */
        _onLoadImage(ev) {
            ev.stopPropagation();
            this.state.isImageLoading = false
        }

        /**
         * @private
         * @param {DragEvent} ev
         */
        _onMousedownImage(ev) {
            if (this._isDragging) {
                return;
            }
            if (ev.button !== 0) {
                return;
            }
            ev.stopPropagation();
            this._isDragging = true;
            this._dragstartX = ev.clientX;
            this._dragstartY = ev.clientY;
        }

        /**
         * @private
         * @param {DragEvent}
         */
        _onMousemoveView(ev) {
            if (!this._isDragging) {
                return;
            }
            this._translate.dx = ev.clientX - this._dragstartX;
            this._translate.dy = ev.clientY - this._dragstartY;
            this._updateZoomerStyle();
        }

        /**
         * @private
         * @param {Event} ev
         */
        _onWheelImage(ev) {
            ev.stopPropagation();
            if (!this.el) {
                return;
            }
            if (ev.deltaY > 0) {
                this._zoomOut({scroll: true});
            } else {
                this._zoomIn({scroll: true});
            }
        }

    }

    CloudPictureGallery.template = xml`
        <div class="o_CloudPictureGallery" t-on-click="_onClick" t-on-keydown="_onKeydown" tabindex="0">
            <div class="o_CloudPictureGallery_header" t-on-click="_onClickHeader">
                <div class="o_CloudPictureGallery_headerItem o_CloudPictureGallery_icon">
                    <i class="fa fa-picture-o" role="img" title="Image"/>
                </div>
                <div class="o_CloudPictureGallery_headerItem o_CloudPictureGallery_name">
                    <t t-esc="state.picture.name"/>
                </div>
                <div class="o_CloudPictureGallery_buttonDownload o_CloudPictureGallery_headerItem o_CloudPictureGallery_headerItemButton"
                    t-on-click="_onClickDownload" role="button" title="Download">
                    <i class="fa fa-download fa-fw" role="img"/>
                </div>
                <div class="o-autogrow"/>
                <div class="o_CloudPictureGallery_headerItem o_CloudPictureGallery_headerItemButton o_CloudPictureGallery_headerItemButtonClose" t-on-click="_onClickClose" role="button" title="Close (Esc)" aria-label="Close">
                    <i class="fa fa-fw fa-times" role="img"/>
                </div>
            </div>
            <div class="o_CloudPictureGallery_main" t-att-class="{ o_with_img: true }" t-on-mousemove="_onMousemoveView">
                <div class="o_CloudPictureGallery_zoomer" t-ref="zoomer">
                    <t t-if="state.isImageLoading">
                        <div class="o_CloudPictureGallery_loading">
                            <i class="fa fa-3x fa-circle-o-notch fa-fw fa-spin" role="img" title="Loading"/>
                        </div>
                    </t>
                    <img class="o_CloudPictureGallery_view o_CloudPictureGallery_viewImage"
                         draggable="false"
                         t-on-click="_onClickImage"
                         t-on-mousedown="_onMousedownImage"
                         t-on-wheel="_onWheelImage"
                         t-on-load="_onLoadImage"
                         t-att-src="state.picture.url"
                         t-att-style="imageStyle"
                         t-att-alt="state.picture.name"
                         t-key="'image_' + state.picture.id"
                         t-ref="image_{{ state.picture.id }}"/>
                </div>
            </div>
            <div class="o_CloudPictureGallery_toolbar" role="toolbar">
                <div class="o_CloudPictureGallery_toolbarButton" t-on-click="_onClickPositionReset" title="重置位置" role="button">
                    <i class="fa fa-fw fa-refresh" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_toolbarButton" t-on-click="_onClickZoomIn" title="放大 (+)" role="button">
                    <i class="fa fa-fw fa-plus" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_toolbarButton" t-att-class="{ o_disabled: state.scale === 1 }" t-on-click="_onClickZoomReset" role="button" title="重置大小 (0)">
                    <i class="fa fa-fw fa-search" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_toolbarButton" t-att-class="{ o_disabled: state.scale === MIN_SCALE }" t-on-click="_onClickZoomOut" title="缩小 (-)" role="button">
                    <i class="fa fa-fw fa-minus" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_toolbarButton" t-on-click="_onClickRotate" title="Rotate (r)" role="button">
                    <i class="fa fa-fw fa-repeat" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_buttonDownload o_CloudPictureGallery_toolbarButton" t-on-click="_onClickDownload" title="Download" role="button">
                    <i class="fa fa-fw fa-download" role="img"/>
                </div>
            </div>
            <t t-if="state.pictures.length > 1">
                <div class="o_CloudPictureGallery_buttonNavigation o_CloudPictureGallery_buttonNavigationPrevious" t-on-click="_onClickPrevious" title="Previous (Left-Arrow)" role="button">
                    <span class="fa fa-chevron-left" role="img"/>
                </div>
                <div class="o_CloudPictureGallery_buttonNavigation o_CloudPictureGallery_buttonNavigationNext" t-on-click="_onClickNext" title="Next (Right-Arrow)" role="button">
                    <span class="fa fa-chevron-right" role="img"/>
                </div>
            </t>
        </div>
    `

    QWeb.registerComponent('CloudPictureGallery', CloudPictureGallery);
    return CloudPictureGallery;
});
