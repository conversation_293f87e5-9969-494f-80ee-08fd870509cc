.o_CloudPictureGallery {
  display: flex;
  width: 100%;
  height: 100%;
  flex-flow: column;
  align-items: center;
  z-index: 1050;
  position: absolute;
  top: 0;
}

.o_CloudPictureGallery_buttonNavigation {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  top: 50%;
  transform: translateY(-50%);
}

.o_CloudPictureGallery_buttonNavigationNext {
  right: 15px;

  > .fa {
    margin: 1px 0 0 1px; // not correctly centered for some reasons
  }
}

.o_CloudPictureGallery_buttonNavigationPrevious {
  left: 15px;

  > .fa {
    margin: 1px 1px 0 0; // not correctly centered for some reasons
  }
}

.o_CloudPictureGallery_header {
  display: flex;
  height: $o-navbar-height;
  align-items: center;
  padding: 0 15px;
  width: 100%;
  z-index: 1051;
  position: absolute;
  top: 0;
}

.o_CloudPictureGallery_headerItem {
  margin: 0 5px;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

.o_CloudPictureGallery_loading {
  position: absolute;
}

.o_CloudPictureGallery_main {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: -1;
  padding: 45px 0;
  background-color: #4c4c4c;

  &.o_with_img {
    overflow: hidden;
  }
}

.o_CloudPictureGallery_toolbar {
  position: absolute;
  bottom: 45px;
  transform: translateY(100%);
  display: flex;
}

.o_CloudPictureGallery_toolbarButton {
  padding: 8px;
}

.o_CloudPictureGallery_viewImage {
  max-height: calc(100vh - #{$o-navbar-height} * 2);
  //max-height: 100%;
  max-width: 100%;
  cursor: pointer;
}

.o_CloudPictureGallery_viewIframe {
  width: 90%;
  height: 100%;
}

.o_CloudPictureGallery_viewVideo {
  width: 75%;
  height: 75%;
}

.o_CloudPictureGallery_zoomer {
  position: absolute;
  padding: 45px 0;
  width: 100%;
  display: flex;
  background-color: rgb(76, 76, 76);
  justify-content: center;
}

.o_CloudPictureGallery {
  outline: none;
}

.o_CloudPictureGallery_buttonNavigation {
  color: gray('400');
  background-color: lighten(black, 15%);
  border-radius: 100%;
  cursor: pointer;

  &:hover {
    color: lighten(gray('400'), 15%);
    background-color: black;
  }
}

.o_CloudPictureGallery_header {
  background-color: rgba(0, 0, 0, 0.7);
  color: gray('400');
}

.o_CloudPictureGallery_headerItemButton {
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
    color: lighten(gray('400'), 15%);
  }
}

.o_CloudPictureGallery_headerItemButtonClose {
  cursor: pointer;
  font-size: 1.3rem;
}

.o_CloudPictureGallery_toolbar {
  cursor: pointer;
}

.o_CloudPictureGallery_toolbarButton {
  background-color: lighten(black, 15%);

  &.o_disabled {
    cursor: not-allowed;
    filter: brightness(1.3);
  }

  &:not(.o_disabled) {
    color: gray('400');
    cursor: pointer;

    &:hover {
      background-color: black;
      color: lighten(gray('400'), 15%);
    }
  }
}

.o_CloudPictureGallery_view {
  //background-color: black;
  //box-shadow: 0 0 40px black;
  outline: none;
  border: none;

  &.o_text {
    background-color: white;
  }
}

.o_CloudPictureGallery_viewImage {
  transition: transform 0.3s ease;
}

.o_warning_Tips {
  z-index: 99999;
  position: fixed;
  top: 0px;
  right: 0px;
}

.gallery_picture_box {
  max-width: 90px;
  max-height: 90px;
  vertical-align: top;
  border: 1px solid #a8a8a8;
  text-align: center;
}

.img-cloud-url {
  max-width: 90px;
  max-height: 90px;
  vertical-align: top;
}