odoo.define('roke_cloud_storage.many2many_cloud_attachment', function (require) {
    "use strict";

    const AbstractField = require('web.AbstractField');
    const field_registry = require('web.field_registry');
    const CloudPictureGallery = require("roke_cloud_storage.cloud_image_gallery")
    const env = require("web.env");


    const FieldMany2ManyImageCloudUrl = AbstractField.extend({
        template: "RokeCloudAttachment",
        template_files: "RokeCloudAttachment.file",
        supportedFieldTypes: ['many2many'],
        fieldsToFetch: {
            id: {type: 'integer'},
            name: {type: 'char'},
            path: {type: 'char'},
            host: {type: 'char'},
            mime: {type: 'char'},
            size: {type: 'integer'},
        },
        events: {
            'click .img-cloud-url': '_onCloudImageClick',
        },
        /**
         * @constructor
         */
        init: function () {
            this._super.apply(this, arguments);
            if (this.field.type !== 'many2many' || this.field.relation !== 'roke.cloud.attachment') {
                throw `字段 ${this.field.string} 的必须是关联 "roke.cloud.attachment" 模型的Many2many类型的字段。`;
            }
        },

        _onCloudImageClick: function (ev) {
            ev.stopPropagation();
            let pictures = this.value.data.map(value => {
                let picture = value.data;
                return {
                    id: picture.id,
                    name: picture.name,
                    mime: picture.mime,
                    url: `${picture.host}/${picture.path}`
                };
            })
            let picture = pictures.find(item => item.id === parseInt(ev.target.dataset.id))
            env.gallery_metadata = {
                picture: picture,
                pictures: pictures
            }
            let gallery = new CloudPictureGallery()
            gallery.mount(document.body)
        }

    });
    // 注册为 widget
    field_registry.add('many2many_cloud_attachment', FieldMany2ManyImageCloudUrl);

});

