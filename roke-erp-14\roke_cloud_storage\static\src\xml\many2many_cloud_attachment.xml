<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="RokeCloudAttachment">
        <div t-foreach="widget.value.data" t-as="picture" class="gallery_picture_box">
            <t t-call="RokeCloudAttachment.picture"/>
        </div>
    </t>

    <t t-name="RokeCloudAttachment.picture">
        <img class="img img-fluid img-cloud-url"
             t-att-data-id="picture.data.id"
             t-attf-src='{{picture.data.host}}/{{picture.data.path}}'
             t-att-alt="picture.data.name"
             t-att-name="picture.data.name"/>
    </t>

</templates>
