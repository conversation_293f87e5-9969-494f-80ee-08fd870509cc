<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" id="watermark_setting" data-string="OSS设置" string="OSS设置"
                     data-key="roke_cloud_storage">
                    <h2 id="web_theme_title">OSS设置</h2>
                    <div class="row mt16 o_settings_container" name="watermark_setting_container">
                        <div class="col-12 col-lg-6 o_setting_box" id="oss_domain" groups="base.group_system">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <span class="o_form_label">Protocol</span>
                                <div class="text-muted">
                                    绑定的协议类型
                                </div>
                                <div class="text-muted">
                                    <field name="oss_protocol" widget="radio" options="{'horizontal': True}"/>
                                </div>
                                <span class="o_form_label">Bucket</span>
                                <div class="text-muted">
                                    存储空间名称
                                </div>
                                <div class="text-muted">
                                    <field name="oss_bucket_name" placeholder="存储空间名称" style="width: 90%;"/>
                                </div>
                                <span class="o_form_label">Endpoint</span>
                                <div class="text-muted">
                                    地域节点
                                </div>
                                <div class="text-muted">
                                    <field name="oss_endpoint" placeholder="例如：oss-cn-qingdao.aliyuncs.com" style="width: 90%;"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" id="oss_key"
                             groups="base.group_system">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <span class="o_form_label">AccessKey ID &amp; AccessKey Secret</span>
                                <div class="text-muted">
                                    登录阿里云开发者后台，获取 AccessKey ID 和 AccessKey Secret
                                </div>
                                <div class="text-muted">
                                    <field name="oss_access_key" placeholder="AccessKey ID" style="width: 90%;"/>
                                </div>
                                <div class="text-muted">
                                    <field name="oss_secret_key" placeholder="AccessKey Secret" style="width: 90%;"/>
                                </div>
                            </div>
                            <br/>
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <span class="o_form_label">Folder</span>
                                <div class="text-muted">
                                    文件存放的目录名
                                </div>
                                <div class="text-muted">
                                    <field name="oss_folder" placeholder="不填写则上传到根目录，建议填写并以/结尾" style="width: 90%;"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
