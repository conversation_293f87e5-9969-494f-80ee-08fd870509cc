# -*- coding: utf-8 -*-
"""
Description:
    考试管理
Versions:
    Created by www.rokedata.com<Wangzan>
"""
import logging
import os
import random
from passlib.context import CryptContext
from jinja2 import FileSystemLoader, Environment
from odoo import http, SUPERUSER_ID, fields
from datetime import datetime, timedelta

# 设置查找html文件的路径
BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static/index/index")
env = Environment(loader=templateloader)
_logger = logging.getLogger(__name__)


class WorkReportKanban(http.Controller):

    def get_title_images(self, title_date_id):
        image_datas = []
        # 获取考试图片
        subject_images = http.request.env(user=SUPERUSER_ID)["roke.subject.title.data.images"].search([
            ("parent_id", "=", title_date_id),
            ("img_data", "!=", None),
        ], order="sequence asc")
        for subject_image in subject_images:
            image_url = subject_image.get_image_url()
            image_datas.append({
                "sequence": subject_image.sequence,
                "img_url": image_url
            })
        return image_datas

    @http.route('/roke/student/exam/html', type='http', auth='public', csrf=False)
    def production_kanban(self):
        """返回报表"""
        values = {}
        # 修改Jinja2 分隔符
        env.variable_start_string = '[['
        env.variable_end_string = ']]'
        template = env.get_template('detail_answer.html')
        html = template.render(values)
        return html

    @http.route('/roke/exam_login', type='http', auth='public', csrf=False)
    def student_exam_login(self):
        """返回报表"""
        values = {}
        # 修改Jinja2 分隔符
        env.variable_start_string = '[['
        env.variable_end_string = ']]'
        template = env.get_template('index.html')
        html = template.render(values)
        return html

    # 点击开始考试
    @http.route('/roke/start_exam', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def start_exam(self):
        print(http.request.jsonrequest)
        exam_id = http.request.jsonrequest.get('exam_id', False)
        student_id = http.request.jsonrequest.get('student_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试ID，请刷新再试",
                "data": ""
            }
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生ID，请刷新再试",
                "data": ""
            }
        # 判断是否存在考试
        exam_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', int(exam_id))])
        # 校验考生ID和考试是否一致
        if exam_obj.employee_id.id != int(student_id):
            return {
                "state": "error",
                "msgs": "考生与考试不匹配，请刷新再试",
                "data": ""
            }
        if exam_obj.pattern_type == 'exam':
            # 校验当前时间是否在考试时间内
            if datetime.now() >= exam_obj.end_time or datetime.now() < exam_obj.start_time:
                return {
                    "state": "error",
                    "msgs": "当前时间不能开始此场考试，请刷新再试",
                    "data": ""
                }
        if exam_obj.state != 'wait_exam':
            return {
                "state": "error",
                "msgs": "当前考试不是等待考试状态，请刷新再试",
                "data": ""
            }
        else:
            # 状态置为考试中
            exam_obj.state = 'exam_taking'
        if not exam_obj.real_start_time:
            exam_obj.real_start_time = datetime.now()
        if exam_obj.parent_id.state == 'wait_exam':
            exam_obj.parent_id.state = 'exam_taking'
        plan_end_time = False
        if exam_obj.pattern_type == 'exam':
            # 计算最晚结束时间
            if (exam_obj.end_time - datetime.now()).days > 0:
                time_difference = 1440 * (exam_obj.end_time - datetime.now()).days + int((exam_obj.end_time - datetime.now()).seconds / 60)
            else:
                time_difference = int((exam_obj.end_time - datetime.now()).seconds / 60)
            if time_difference >= exam_obj.time_length:
                minute_item = exam_obj.time_length
            else:
                minute_item = time_difference
            plan_end_time = exam_obj.real_start_time + timedelta(hours=8,
                                                                 minutes=(minute_item + exam_obj.suspend_length))
        return {
            "state": "success",
            "msgs": "考试开始成功",
            "data": "",
            "plan_end_time": plan_end_time
        }

    # 登录时调用接口
    @http.route('/roke/student_login', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def student_login(self):
        print(http.request.jsonrequest)

        user_login = http.request.jsonrequest.get('userlogin', False)
        user_pwd = http.request.jsonrequest.get('userpwd', False)
        if not user_login or not user_pwd:
            return {
                "state": "error",
                "msgs": "未获取到考生账号/密码",
                "data": ""
            }
        user_login = str(user_login)
        user_pwd = str(user_pwd)
        # 查找账号
        user_obj = http.request.env(user=SUPERUSER_ID)['res.users'].search([("login", "=", user_login)])
        if not user_obj:
            return {
                "state": "error",
                "msgs": "考生账号不存在",
                "data": ""
            }
        # storage_pwd 需要从数据库获取，比如用SQL
        # storage_pwd = "$pbkdf2-sha512$25000$iZESAoAwhjDGeC9l7P0/hw$SNWdRWaNmQhjB3JZpV1gW28J7V.eTORQslGAXagOUSYIONKmLOv.ZF09.RNxmNT/boMaww/3pcSQiDtYeRO3mw"
        # sql查询用户密码
        http.request.env(user=SUPERUSER_ID).cr.execute("""select password from res_users where id= %s""" % user_obj.id)
        result = http.request.env(user=SUPERUSER_ID).cr.fetchone()
        print(http.request.env(user=SUPERUSER_ID).cr.fetchone())
        storage_pwd = result[0]

        print("表单传入登录名：", user_login)
        print("表单传入密码：", user_pwd)
        print("密码转化所得值：", CryptContext(['pbkdf2_sha512']).encrypt(user_pwd))
        print("数据库中存储的：", storage_pwd)
        # CryptContext(['pbkdf2_sha512']).verify('输入的密码','res_users表中的密码')
        match = CryptContext(['pbkdf2_sha512']).verify(user_pwd, storage_pwd)
        print("是否匹配：", match)
        if match:
            employee_obj = http.request.env(user=SUPERUSER_ID)['roke.employee'].search([("user_id", "=", user_obj.id)])
            data = {
                'user_id': employee_obj.id,
                'user_code': employee_obj.job_number,
                'user_name': employee_obj.name,
                'user_card_code': employee_obj.id_number if employee_obj.id_number else "",
            }
            return {
                "state": "success",
                "msgs": "登录成功",
                "data": data
            }
        else:
            return {
                "state": "error",
                "msgs": "密码输入不正确",
                "data": ""
            }

    # 获取考题
    @http.route('/roke/student_conduct_exam', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def student_conduct_exam(self):
        print(http.request.jsonrequest)

        user_id = http.request.jsonrequest.get('student_id', False)
        pattern_type = http.request.jsonrequest.get('pattern_type', False)
        if not user_id:
            return {
                "state": "error",
                "msgs": "未获取到考生信息，请重新进入考试！"
            }
        if not pattern_type:
            return {
                "state": "error",
                "msgs": "未获取到考试类型",
                "data": ""
            }
        pattern_type_dict = {
            'practice': '练习模式',
            'exam': '考试模式'
        }
        if pattern_type not in pattern_type_dict.keys():
            return {
                "state": "error",
                "msgs": "考试类型【%s】不合法" % pattern_type,
                "data": ""
            }
        employee = http.request.env(user=SUPERUSER_ID)['roke.employee'].search([("user_id", "=", user_id)])
        student_exam = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
            [('employee_id', '=', employee.id), ('state', 'in', ['wait_exam', 'exam_taking']),
             ('pattern_type', '=', pattern_type), ('start_time', '<=', datetime.now()),
             ('end_time', '>=', datetime.now())])

        if len(student_exam) == 1:
            # 组装题目信息
            student_info = {
                'exam_id': student_exam.id,
                'student_name': employee.name,
                'student_code': employee.job_number,
                'student_card_code': employee.id_number if employee.id_number else "",
                'start_time': student_exam.start_time + timedelta(hours=8),
                'end_time': student_exam.end_time + timedelta(hours=8),
                'time_length': student_exam.time_length,
            }
            data = [student_info, [], [], [], []]
            option_list = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K']
            # 考试内容明细
            radio_sequence = 1
            judge_sequence = 1
            checkbox_sequence = 1
            for exam_item in student_exam.line_ids:
                if exam_item.project_id.project_type == 'subjectivity':
                    continue
                # 需要区分实操类和客观类题目
                # 客观类题目：需要区分单选和多选
                if exam_item.title_date_id.project_type == 'objective':
                    if exam_item.title_date_id.data_type == 'radio':
                        objective_info = {'title': exam_item.title_date_id.description,
                                          'data_sequence': radio_sequence,
                                          'title_id': exam_item.title_date_id.id}
                        option_detail_list = []
                        if exam_item.is_random:
                            option_info_list = []
                            # 需要打乱选项
                            item_list = [line for line in exam_item.title_date_id.objective_line_ids]
                            while len(item_list) > 0:
                                option_item = random.choice(item_list)
                                option_info_list.append(option_item)
                                item_list.remove(option_item)
                            for i, option_info in enumerate(option_info_list):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': option_info.name,
                                    'is_correct': option_info.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        else:
                            for i, line in enumerate(exam_item.title_date_id.objective_line_ids):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': line.name,
                                    'is_correct': line.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        data[2].append(objective_info)
                        radio_sequence += 1
                    elif exam_item.title_date_id.data_type == 'judge':
                        objective_info = {'title': exam_item.title_date_id.description,
                                          'data_sequence': judge_sequence,
                                          'title_id': exam_item.title_date_id.id}
                        option_detail_list = []
                        if exam_item.is_random:
                            option_info_list = []
                            # 需要打乱选项
                            item_list = [line for line in exam_item.title_date_id.objective_line_ids]
                            while len(item_list) > 0:
                                option_item = random.choice(item_list)
                                option_info_list.append(option_item)
                                item_list.remove(option_item)
                            for i, option_info in enumerate(option_info_list):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': option_info.name,
                                    'is_correct': option_info.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        else:
                            for i, line in enumerate(exam_item.title_date_id.objective_line_ids):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': line.name,
                                    'is_correct': line.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        data[3].append(objective_info)
                        judge_sequence += 1
                    else:
                        objective_info = {'title': exam_item.title_date_id.description,
                                          'data_sequence': checkbox_sequence,
                                          'title_id': exam_item.title_date_id.id}
                        option_detail_list = []
                        if exam_item.is_random:
                            option_info_list = []
                            # 需要打乱选项
                            item_list = [line for line in exam_item.title_date_id.objective_line_ids]
                            while len(item_list) > 0:
                                option_item = random.choice(item_list)
                                option_info_list.append(option_item)
                                item_list.remove(option_item)
                            for i, option_info in enumerate(option_info_list):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': option_info.name,
                                    'is_correct': option_info.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        else:
                            for i, line in enumerate(exam_item.title_date_id.objective_line_ids):
                                option_detail_list.append({
                                    'option': option_list[i],
                                    'option_name': line.name,
                                    'is_correct': line.is_correct
                                })
                            objective_info['options'] = option_detail_list
                        data[4].append(objective_info)
                        checkbox_sequence += 1
                else:  # 实操类题目
                    data_process = []
                    # 题目中的做题步骤
                    for data_item in exam_item.title_date_id.line_ids.sorted(key='sequence'):
                        # 每个步骤中的答案标准
                        # data_standard = []
                        # i = 1
                        # for standard in data_item.line_ids:
                        #     data_standard.append({
                        #         'id': standard.id,  # 当前记录id
                        #         'model': standard.model_id.model,  # 模型
                        #         'fileld': standard.field_id.name,  # 字段名称
                        #         'content': standard.content,  # 答案内容
                        #         'mark': standard.mark,  # 所占分数
                        #         'url': data_item.url,  # url地址,
                        #         'data_exam': "第" + str(i) + "题"  #  ..题
                        #     })
                        #     i += 1
                        data_process.append({
                            'process': data_item.name,  # 过程名称
                            'url': data_item.url,  # url地址
                            'remark': data_item.remark,  # 备注
                            # 'data_standard': data_standard  # 答案列表
                        })
                    data[1].append({
                        'title': exam_item.title_date_id.description,  # 题目信息
                        'data_process': data_process,  # 过程集合
                        'url': "http://localhost:8069/web#id=&action=215&model=roke.impersonate.login&view_type=form&cids=1&menu_id=127"
                    })
            print(data)
            return {
                "state": "success",
                "msgs": "获取成功",
                "data": data
            }
        else:
            return {
                "state": "error",
                "msgs": "当前学生未查到或对应多条考试记录，请先进行处理！"
            }

    # 创建考试记录
    @http.route('/roke/student_exam_record', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def student_exam_record(self):
        # print(http.request.jsonrequest)

        return {
            "state": "success",
            "msgs": "保存成功",
            "data": ""
        }

    # 交卷
    @http.route('/roke/student_end_exam', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def student_end_exam(self):
        print(333333, http.request.jsonrequest)
        exam_id = http.request.jsonrequest.get('exam_id', False)
        user_id = http.request.jsonrequest.get('student_id', False)
        pattern_type = http.request.jsonrequest.get('pattern_type', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试信息！"
            }
        if not user_id:
            return {
                "state": "error",
                "msgs": "未获取到考生信息，请重新进入考试！"
            }
        if not pattern_type:
            return {
                "state": "error",
                "msgs": "未获取到考试类型",
                "data": ""
            }
        pattern_type_dict = {
            'practice': '练习模式',
            'exam': '考试模式'
        }
        if pattern_type not in pattern_type_dict.keys():
            return {
                "state": "error",
                "msgs": "考试类型【%s】不合法" % pattern_type,
                "data": ""
            }
        objective_data_list = http.request.jsonrequest.get('objective_data', False)
        judge_data_list = http.request.jsonrequest.get('judgment_data', False)
        checkbox_data_list = http.request.jsonrequest.get('checkbox_data', False)
        employee = http.request.env(user=SUPERUSER_ID)['roke.employee'].search([("user_id", "=", user_id)])
        print(employee, employee.name)
        exam_record = http.request.env(user=SUPERUSER_ID)['roke.subject.examination.record'].search(
            [('student_id', '=', employee.id), ('pattern_type', '=', pattern_type),
             ('exam_id', '=', exam_id)], order='id desc', limit=1)
        if exam_record:
            # 先处理实操类题目
            # auto_line = exam_record.line_ids
            # 计算得分并将实际录入结果写入到考试记录
            objectivity_score = 0
            # for line in auto_line:
            #     model_obj = http.request.env(user=SUPERUSER_ID)[line.model_id.model].search(
            #         [('create_uid', '=', employee.user_id.id), ('create_date', '>=', exam_record.exam_id.start_time),
            #          ('create_date', '<=', exam_record.exam_id.end_time)], order='id desc', limit=1)
            #     if model_obj:
            #         real_content = ''
            #         result = False
            #         # 判断字段类型，char date many2one 不同类型的需要进行不同的判断
            #         if line.field_id.ttype == 'char':
            #             real_content = model_obj[line.field_id.name]
            #             if model_obj[line.field_id.name] == line.content:
            #                 result = True
            #         elif line.field_id.ttype == 'date':
            #             real_content = model_obj[line.field_id.name]
            #             if str(model_obj[line.field_id.name]) == line.content:
            #                 result = True
            #         elif line.field_id.ttype == 'many2one':
            #             # 查出该字段对应模型的name与字段值进行比较
            #             if model_obj[line.field_id.name]:
            #                 related_model_obj = http.request.env(user=SUPERUSER_ID)[line.field_id.relation].browse(
            #                     int(model_obj[line.field_id.name]))
            #                 real_content = related_model_obj.name
            #                 if related_model_obj.name == line.content:
            #                     result = True
            #         elif line.field_id.ttype == 'selection':
            #             # 查出该字段对应的值与字段值进行比较
            #             if model_obj[line.field_id.name]:
            #                 real_value = model_obj[line.field_id.name]
            #                 selection_obj = http.request.env(user=SUPERUSER_ID)['ir.model.fields.selection'].search(
            #                     [('field_id', '=', line.field_id.id), ('value', '=', real_value)])
            #                 real_content = selection_obj.name
            #                 if real_content == line.content:
            #                     result = True
            #         if result:
            #             objectivity_score += line.proportion_mark
            #             line.write({'mark': line.proportion_mark, 'real_content': real_content,
            #                         'state': 'confirm'})
            #         else:
            #             line.write({'mark': 0, 'real_content': real_content,
            #                         'state': 'confirm'})
            #     else:
            #         line.write({'mark': 0, 'state': 'confirm'})
            # 处理客观类单选考题
            if objective_data_list:
                for objective_data in objective_data_list:
                    # 获取考试记录下客观题明细
                    objective_obj = exam_record.objective_line_ids.filtered(
                        lambda item: item.title_data_id.id == int(objective_data['title_id']))
                    # 将考生选择的回填入客观题明细
                    write_data = {'real_answer': objective_data['option_name']}
                    if objective_data['option_name'] == objective_obj.true_answer:
                        # 将该考题分数填入
                        write_data.update({'mark': objective_obj.proportion_mark})
                    objective_obj.write(write_data)
            # 处理客观类判断考题
            if judge_data_list:
                for judge_data in judge_data_list:
                    # 获取考试记录下客观题明细
                    judge_obj = exam_record.objective_line_ids.filtered(
                        lambda item: item.title_data_id.id == int(judge_data['title_id']))
                    # 将考生选择的回填入客观题明细
                    write_data = {'real_answer': judge_data['option_name']}
                    if judge_data['option_name'] == judge_obj.true_answer:
                        # 将该考题分数填入
                        write_data.update({'mark': judge_obj.proportion_mark})
                    judge_obj.write(write_data)
            # 处理客观类多选题
            if checkbox_data_list:
                for checkbox_data in checkbox_data_list:
                    # 获取考试记录下客观题明细
                    checkbox_obj = exam_record.objective_line_ids.filtered(
                        lambda item: item.title_data_id.id == int(checkbox_data['title_id']))
                    # 组装考题正确答案
                    checkbox_title_data_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.title.data'].search(
                        [('id', '=', int(checkbox_data['title_id']))])
                    true_answer_list = [option.name for option in checkbox_title_data_obj.objective_line_ids.filtered(
                        lambda objective_line: objective_line.is_correct)]
                    # 组装考生选择的选项
                    real_answer_list = []
                    if checkbox_data.get('answer', False):
                        for answer in checkbox_data['answer']:
                            real_answer_list.append(answer['option_name'])

                    # 将考生选择的回填入客观题明细
                    write_data = {'real_answer': '、'.join(real_answer_list)}
                    if set(real_answer_list) == set(true_answer_list):
                        # 将该考题分数填入
                        write_data.update({'mark': checkbox_obj.proportion_mark})
                    checkbox_obj.write(write_data)
            # 将前厅管理权限去掉,加上考试管理权限
            # hotel_manager_group = http.request.env(user=SUPERUSER_ID).ref('roke_hotel_manager.group_hotel_manager',
            #                                                               raise_if_not_found=False)
            # exam_manager_group = http.request.env(user=SUPERUSER_ID).ref('roke_education_manager.group_exam_manager',
            #                                                              raise_if_not_found=False)
            # user_group_list = employee.user_id.groups_id.ids
            # if hotel_manager_group.id in user_group_list:
            #     user_group_list.remove(hotel_manager_group.id)
            # if exam_manager_group.id not in user_group_list:
            #     user_group_list.append(exam_manager_group.id)
            # employee.user_id.sudo().write({
            #     'groups_id': [(6, 0, user_group_list)],
            #     'sidebar_type': 'large'
            # })
            # 判断主观评分是否已全部完成
            if all(line.state == 'confirm' for line in exam_record.line1_ids):
                exam_record.write({
                    'state': 'done',
                    'end_time': datetime.now(),
                    'duration': (datetime.now() - exam_record.start_time).seconds / 60,
                })
                # 学生考试完成
                exam_record.exam_id.write({'state': 'done'})
                # 学生密码重置
                employee.user_id.sudo().write({'password': employee.job_number})
                # 判断考试下是否所有考试都已完成
                if all(base_exam.state == 'done' for base_exam in exam_record.exam_id.parent_id.exam_line_ids):
                    exam_record.sudo().exam_id.parent_id.write({'state': 'done'})
                return {
                    "state": "success",
                    "msgs": "交卷成功",
                    "data": exam_record.total_score
                }
            else:
                return {
                    "state": "success",
                    "msgs": "交卷完成",
                    # "msgs": "等待主观评分完成",
                    "data": ""
                }
        else:
            return {
                "state": "error",
                "msgs": "未查询到考试记录",
                "data": ""
            }

    # 交卷
    @http.route('/roke/end_exam', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def end_exam(self):
        print(http.request.jsonrequest)
        exam_id = http.request.jsonrequest.get('exam_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试ID，请刷新再试",
                "data": ""
            }
        student_id = http.request.jsonrequest.get('student_id', False)
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生ID，请刷新再试",
                "data": ""
            }
        # 判断是否存在考试
        exam_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', int(exam_id))])
        # 校验考生ID和考试是否一致
        if exam_obj.employee_id.id != int(student_id):
            return {
                "state": "error",
                "msgs": "考生与考试不匹配，请刷新再试",
                "data": ""
            }
        # if exam_obj.state != 'exam_taking':
        #     return {
        #         "state": "error",
        #         "msgs": "当前考试不是进行中状态，请刷新再试",
        #         "data": ""
        #     }
        else:
            new_state = 'done'
            # 判断当前考试是否有主观评分
            grade_obj = http.request.env(user=SUPERUSER_ID)['roke.subjectivity.grade'].search(
                [('exam_id', '=', int(exam_id))])
            if grade_obj:
                if grade_obj.state == 'wait_confirm':
                    new_state = 'wait_subjectivity'
            # 状态置为完成
            exam_obj.write({
                'state': new_state,
                'real_end_time': datetime.now(),
                'real_time_length': (datetime.now() - exam_obj.real_start_time).seconds / 60 if exam_obj.real_start_time else False
            })
        if exam_obj.pattern_type == 'exam':
            pattern_state = 'wait_exam'
        else:
            pattern_state = 'not_done'
        return {
            "state": "success",
            "msgs": "交卷成功",
            "data": "",
            "pattern_state": pattern_state
        }

    # 查询考试状态
    @http.route('/roke/query_exam_state', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def query_exam_state(self):
        # print(http.request.jsonrequest)
        exam_id = http.request.jsonrequest.get('exam_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "参数不全！"
            }
        exam_id = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', exam_id)])
        if exam_id:
            if exam_id.is_compel_over_exam:  # 强制交卷
                print('强制交卷')
                exam_id.sudo().write({'is_compel_over_exam': False})
                return {
                    "state": "success",
                    "msgs": "后台发起了强制交卷，即将强制交卷！",
                    "data": "compel_over_exam",
                    "end_time": exam_id.end_time + timedelta(hours=8)
                }
            else:
                plan_end_time = False
                if exam_id.pattern_type == 'exam':
                    if exam_id.state == 'exam_taking':
                        # 计算最晚结束时间
                        if (exam_id.end_time - datetime.now()).days > 0:
                            time_difference = 1440 * (exam_id.end_time - datetime.now()).days + int(
                                (exam_id.end_time - datetime.now()).seconds / 60)
                        else:
                            time_difference = int((exam_id.end_time - datetime.now()).seconds / 60)
                        if time_difference >= exam_id.time_length:
                            minute_item = exam_id.time_length
                        else:
                            minute_item = time_difference
                        plan_end_time = exam_id.real_start_time + timedelta(hours=8, minutes=(minute_item + exam_id.suspend_length))
                if exam_id.state == 'exam_suspend':
                    print('考试暂停')
                    return {
                        "state": "success",
                        "msgs": "考试暂停，请等待后台指令！",
                        "data": "exam_suspend",
                        "end_time": False
                    }
                elif exam_id.suspend_trait:
                    print('暂停已恢复')
                    exam_id.sudo().write({
                        'suspend_trait': False
                    })
                    return {
                        "state": "success",
                        "msgs": "考试暂停已恢复，请继续考试",
                        "data": "suspend_continue",
                        "end_time": exam_id.end_time + timedelta(hours=8),
                        'plan_end_time': plan_end_time
                    }
                elif exam_id.delayed_trait:
                    print('考试延时')
                    result = {
                        "state": "success",
                        "msgs": "考试发生延时",
                        "data": "exam_delayed",
                        "delayed_time": exam_id.delayed_count,
                        "end_time": exam_id.end_time + timedelta(hours=8),
                        'plan_end_time': plan_end_time
                    }
                    exam_id.sudo().write({
                        'delayed_trait': False,
                        'delayed_count': False
                    })
                    return result
                else:
                    return {
                        "state": "success",
                        "msgs": "",
                        "data": "",
                        "end_time": exam_id.end_time + timedelta(hours=8) if exam_id.pattern_type == 'exam' else False,
                        'plan_end_time': plan_end_time if exam_id.pattern_type == 'exam' else False
                    }
        else:
            return {
                "state": "error",
                "msgs": "未查询到考试",
                "data": "",
                "end_time": ""
            }

    # 获取考试列表
    @http.route('/roke/query_exam_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def query_exam_list(self):
        student_id = http.request.jsonrequest.get('student_id', False)
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生信息，请刷新重试",
                "data": ""
            }
        pattern_type = http.request.jsonrequest.get('pattern_type', '')
        if not pattern_type:
            return {
                "state": "error",
                "msgs": "未获取到模式类型，请刷新重试",
                "data": ""
            }
        pattern_state = http.request.jsonrequest.get('pattern_state', '')
        exam_list = []
        exam_state_dict = {
            'draft': '草稿',
            'data_dispatch': '考题分配',
            'wait_exam': '等待考试',
            'exam_taking': '考试中',
            'exam_suspend': '考试暂停',
            'wait_subjectivity': '等待主观评分',
            'done': '已交卷',
        }
        # 练习模式，默认未完成的练习
        if pattern_type == 'practice':
            if pattern_state == 'not_done':
                exam_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
                    [('employee_id', '=', int(student_id)), ('pattern_type', '=', 'practice'),
                     ('state', 'in', ['wait_exam', 'exam_taking', 'exam_suspend'])], order='start_time')
            else:
                exam_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
                    [('employee_id', '=', int(student_id)), ('pattern_type', '=', 'practice'),
                     ('state', 'in', ['wait_subjectivity', 'done'])], order='start_time desc')
        else:
            if pattern_state == 'wait_exam':
                exam_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
                    [('employee_id', '=', int(student_id)), ('pattern_type', '=', 'exam'),
                     ('state', '=', 'wait_exam')], order='start_time')
            elif pattern_state == 'exam_taking':
                exam_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
                    [('employee_id', '=', int(student_id)), ('pattern_type', '=', 'exam'),
                     ('state', 'in', ['exam_taking', 'exam_suspend'])], order='start_time')
            else:
                exam_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search(
                    [('employee_id', '=', int(student_id)), ('pattern_type', '=', 'exam'),
                     ('state', 'in', ['wait_subjectivity', 'done'])], order='start_time desc')
        for exam_id in exam_ids:
            exam_list.append({
                'exam_id': exam_id.id,
                'student_name': exam_id.employee_id.name,
                'exam_name': exam_id.parent_id.name,
                'course_name': exam_id.course_id.name,
                'start_time': exam_id.start_time + timedelta(hours=8) if exam_id.start_time else False,
                'end_time': exam_id.end_time + timedelta(hours=8) if exam_id.end_time else False,
                'time_length': exam_id.time_length,
                'exam_state': exam_state_dict[exam_id.state]
            })
        return {
            "state": "success",
            "msgs": "请求成功！",
            "data": exam_list
        }

    # 获取考试考题信息
    @http.route('/roke/get_exam_detail', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_exam_detail(self):
        exam_id = http.request.jsonrequest.get('exam_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试信息，请刷新重试",
                "data": ""
            }
        student_id = http.request.jsonrequest.get('student_id', False)
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生ID，请刷新再试",
                "data": ""
            }
        exam_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', int(exam_id))])
        # 校验考生ID和考试是否一致
        if exam_obj.employee_id.id != int(student_id):
            return {
                "state": "error",
                "msgs": "考生与考试不匹配，请刷新再试",
                "data": ""
            }
        grade_obj = http.request.env(user=SUPERUSER_ID)['roke.subjectivity.grade'].search(
            [('exam_id', '=', exam_obj.id)])
        record_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.examination.record'].search(
            [('exam_id', '=', exam_obj.id)])
        if exam_obj.state not in ['wait_exam', 'exam_taking', 'exam_suspend']:
            return {
                "state": "error",
                "msgs": "当前考试状态不能进入考试，请刷新考试列表重试",
                "data": ""
            }
        plan_end_time = False
        is_can_see_true_answer = True
        is_show_time = False
        if exam_obj.pattern_type == 'exam':
            is_can_see_true_answer = False
            is_show_time = True
            if exam_obj.real_start_time:
                # 计算最晚结束时间
                if (exam_obj.end_time - datetime.now()).days > 0:
                    time_difference = 1440 * (exam_obj.end_time - datetime.now()).days + int(
                        (exam_obj.end_time - datetime.now()).seconds / 60)
                else:
                    time_difference = int((exam_obj.end_time - datetime.now()).seconds / 60)
                if time_difference >= exam_obj.time_length:
                    minute_item = exam_obj.time_length
                else:
                    minute_item = time_difference
                plan_end_time = exam_obj.real_start_time + timedelta(hours=8,
                                                                     minutes=(minute_item + exam_obj.suspend_length))
        exam_info = {
            'exam_id': exam_obj.id,
            'student_name': exam_obj.employee_id.name,
            'student_code': exam_obj.employee_id.job_number,
            'student_card_code': exam_obj.employee_id.id_number if exam_obj.employee_id.id_number else "",
            'start_time': exam_obj.start_time + timedelta(hours=8) if exam_obj.start_time else False,
            'end_time': exam_obj.end_time + timedelta(hours=8) if exam_obj.end_time else False,
            'time_length': exam_obj.time_length,
            'state': exam_obj.state,
            'plan_end_time': plan_end_time,
            'is_can_see_true_answer': is_can_see_true_answer,
            'is_show_time': is_show_time,
            'employee_team': exam_obj.employee_id.employee_team,
        }
        subject_list = []
        option_list = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K']
        subject_line_ids = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam.line'].search(
            [('parent_id', '=', int(exam_id))], order='sequence')
        radio_dict = {'subject_type': '单选题', 'title_list': []}
        checkbox_dict = {'subject_type': '多选题', 'title_list': []}
        judge_dict = {'subject_type': '判断题', 'title_list': []}
        gap_filling_dict = {'subject_type': '填空题', 'title_list': []}
        subjectivity_dict = {'subject_type': '主观题', 'title_list': []}
        radio_sequence = 1
        checkbox_sequence = 1
        judge_sequence = 1
        gap_filling_sequence = 1
        subjectivity_sequence = 1
        for subject_line in subject_line_ids:

            image_datas = self.get_title_images(subject_line.title_date_id.id)
            # 单选
            if subject_line.title_date_id.project_id.project_type == 'objective' and subject_line.title_date_id.data_type == 'radio':
                radio_info = {
                    'title': subject_line.title_date_id.description,
                    'data_sequence': radio_sequence,
                    'title_id': subject_line.title_date_id.id,
                    'is_answer': False,
                    'answer_analysis': subject_line.title_date_id.remark or '',
                    'image_datas': image_datas
                }
                # 查询已选择
                answer_line = record_obj.objective_line_ids.filtered(
                    lambda objective_line: objective_line.title_data_id.id == subject_line.title_date_id.id)
                if answer_line.real_answer:
                    radio_info['is_answer'] = True
                option_detail_list = []
                option_info_list = []
                if subject_line.is_random:
                    # 需要打乱选项
                    item_list = [line for line in subject_line.title_date_id.objective_line_ids]
                    while len(item_list) > 0:
                        option_item = random.choice(item_list)
                        option_info_list.append(option_item)
                        item_list.remove(option_item)
                else:  # 选项不随机
                    for objective_line in subject_line.title_date_id.objective_line_ids:
                        option_info_list.append(objective_line)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if answer_line.real_answer:
                        if option_info.name == answer_line.real_answer:
                            option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                radio_info['options'] = option_detail_list
                radio_dict['title_list'].append(radio_info)
                radio_sequence += 1
            # 多选
            elif subject_line.title_date_id.project_id.project_type == 'objective' and subject_line.title_date_id.data_type == 'checkbox':
                checkbox_info = {
                    'title': subject_line.title_date_id.description,
                    'data_sequence': checkbox_sequence,
                    'title_id': subject_line.title_date_id.id,
                    'is_answer': False,
                    'answer_analysis': subject_line.title_date_id.remark or '',
                    'image_datas': image_datas
                }
                # 查询已选择
                answer_line = record_obj.objective_line_ids.filtered(
                    lambda objective_line: objective_line.title_data_id.id == subject_line.title_date_id.id)
                real_answer_list = []
                if answer_line.real_answer:
                    checkbox_info['is_answer'] = True
                    real_answer_list = answer_line.real_answer.split('、')
                option_detail_list = []
                option_info_list = []
                if subject_line.is_random:
                    # 需要打乱选项
                    item_list = [line for line in subject_line.title_date_id.objective_line_ids]
                    while len(item_list) > 0:
                        option_item = random.choice(item_list)
                        option_info_list.append(option_item)
                        item_list.remove(option_item)
                else:
                    for objective_line in subject_line.title_date_id.objective_line_ids:
                        option_info_list.append(objective_line)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if real_answer_list:
                        if option_info.name in real_answer_list:
                            option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                checkbox_info['options'] = option_detail_list
                checkbox_dict['title_list'].append(checkbox_info)
                checkbox_sequence += 1
            # 判断
            elif subject_line.title_date_id.project_id.project_type == 'objective' and subject_line.title_date_id.data_type == 'judge':
                judge_info = {
                    'title': subject_line.title_date_id.description,
                    'data_sequence': judge_sequence,
                    'title_id': subject_line.title_date_id.id,
                    'is_answer': False,
                    'answer_analysis': subject_line.title_date_id.remark or '',
                    'image_datas': image_datas
                }
                # 查询已选择
                answer_line = record_obj.objective_line_ids.filtered(
                    lambda objective_line: objective_line.title_data_id.id == subject_line.title_date_id.id)
                if answer_line.real_answer:
                    judge_info['is_answer'] = True
                option_detail_list = []
                option_info_list = []
                if subject_line.is_random:
                    # 需要打乱选项
                    item_list = [line for line in subject_line.title_date_id.objective_line_ids]
                    while len(item_list) > 0:
                        option_item = random.choice(item_list)
                        option_info_list.append(option_item)
                        item_list.remove(option_item)
                else:
                    for objective_line in subject_line.title_date_id.objective_line_ids:
                        option_info_list.append(objective_line)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if answer_line.real_answer:
                        if option_info.name == answer_line.real_answer:
                            option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                judge_info['options'] = option_detail_list
                judge_dict['title_list'].append(judge_info)
                judge_sequence += 1
            # 填空
            elif subject_line.title_date_id.project_id.project_type == 'objective' and subject_line.title_date_id.data_type == 'gap_filling':
                gap_filling_info = {
                    'title': subject_line.title_date_id.description,
                    'data_sequence': gap_filling_sequence,
                    'title_id': subject_line.title_date_id.id,
                    'is_answer': False,
                    'answer_analysis': subject_line.title_date_id.remark or '',
                    'image_datas': image_datas
                }
                gap_filling_answer_list = []
                # 组装填空列表
                for objective_line in subject_line.title_date_id.objective_line_ids:
                    gap_filling_answer_list.append({
                        'true_answer': objective_line.name,
                        'real_answer': ''
                    })
                # 查询已填空
                answer_line = record_obj.objective_line_ids.filtered(
                    lambda objective_line: objective_line.title_data_id.id == subject_line.title_date_id.id)
                if answer_line.real_answer:
                    gap_filling_info['is_answer'] = True
                    gap_filling_real_answer_list = answer_line.real_answer.split('、')
                    for i, item_dict in enumerate(gap_filling_answer_list):
                        gap_filling_answer_list[i]['real_answer'] = gap_filling_real_answer_list[i]
                gap_filling_info['answer_detail'] = gap_filling_answer_list
                gap_filling_dict['title_list'].append(gap_filling_info)
                gap_filling_sequence += 1
            # 主观
            elif subject_line.title_date_id.project_id.project_type == 'subjectivity':
                subjectivity_info = {
                    'title': subject_line.title_date_id.description,
                    'data_sequence': subjectivity_sequence,
                    'title_id': subject_line.title_date_id.id,
                    'content': subject_line.title_date_id.content,
                    'textarea': '',
                    'is_answer': False,
                    'answer_analysis': subject_line.title_date_id.remark or '',
                    'image_datas': image_datas
                }
                # 查询回答
                answer_line = grade_obj.line_ids.filtered(
                    lambda garde_line: garde_line.title_data_id.id == subject_line.title_date_id.id)
                if answer_line.answer:
                    subjectivity_info['is_answer'] = True
                    subjectivity_info.update({'textarea': answer_line.answer})
                subjectivity_dict['title_list'].append(subjectivity_info)
                subjectivity_sequence += 1
        if radio_dict['title_list']:
            subject_list.append(radio_dict)
        if checkbox_dict['title_list']:
            subject_list.append(checkbox_dict)
        if judge_dict['title_list']:
            subject_list.append(judge_dict)
        if gap_filling_dict['title_list']:
            subject_list.append(gap_filling_dict)
        if subjectivity_dict['title_list']:
            subject_list.append(subjectivity_dict)
        return {
            "state": "success",
            "msgs": "请求成功！",
            "data": subject_list,
            "exam_info": exam_info
        }

    # 实时保存答题信息
    @http.route('/roke/save_answer', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def save_answer(self):
        print(1111111111, http.request.jsonrequest)
        exam_id = http.request.jsonrequest.get('exam_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试信息，请刷新重试",
                "data": ""
            }
        title_id = http.request.jsonrequest.get('title_id', False)
        if not title_id:
            return {
                "state": "error",
                "msgs": "未获取到考题信息，请刷新重试",
                "data": ""
            }
        student_id = http.request.jsonrequest.get('student_id', False)
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生ID，请刷新再试",
                "data": ""
            }
        exam_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', int(exam_id))])
        # 校验考生ID和考试是否一致
        if exam_obj.employee_id.id != int(student_id):
            return {
                "state": "error",
                "msgs": "考生与考试不匹配，请刷新再试",
                "data": ""
            }
        title_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.title.data'].search([('id', '=', int(title_id))])
        # 判断题目类型
        # 主观题
        if title_obj.project_type == 'subjectivity':
            answer_state = http.request.jsonrequest.get('answerState', False)
            answer = http.request.jsonrequest.get('answer', False)
            if not answer_state:
                if answer:
                    grade_obj = http.request.env(user=SUPERUSER_ID)['roke.subjectivity.grade'].search(
                        [('exam_id', '=', exam_obj.id)])
                    title_line = grade_obj.line_ids.filtered(lambda grade_line: grade_line.title_data_id.id == int(title_id))
                    if title_line:
                        title_line.write({'answer': answer})
            else:
                grade_obj = http.request.env(user=SUPERUSER_ID)['roke.subjectivity.grade'].search(
                    [('exam_id', '=', exam_obj.id)])
                title_line = grade_obj.line_ids.filtered(
                    lambda grade_line: grade_line.title_data_id.id == int(title_id))
                if title_line:
                    title_line.write({'answer': ''})
        else:
            # 获取考试记录
            exam_record_id = http.request.env(user=SUPERUSER_ID)['roke.subject.examination.record'].search(
                [('exam_id', '=', exam_obj.id)])
            title_line = exam_record_id.objective_line_ids.filtered(lambda line: line.title_data_id.id == int(title_id))
            # 单选、判断
            if title_obj.data_type in ['radio', 'judge']:
                choose_dict = http.request.jsonrequest.get('choose', False)
                # 将考生选择的回填入客观题明细
                if choose_dict:
                    write_data = {'mark': 0}
                    write_data.update({'real_answer': choose_dict['option_name']})
                    if choose_dict['option_name'] == title_line.true_answer:
                        # 将该考题分数填入
                        write_data.update({'mark': title_line.proportion_mark})
                    title_line.write(write_data)
            elif title_obj.data_type == 'checkbox':  # 多选
                choose_list = http.request.jsonrequest.get('choose_list', False)
                if choose_list:
                    # 组装正确答案
                    true_answer_list = [option.name for option in title_obj.objective_line_ids.filtered(
                        lambda objective_line: objective_line.is_correct)]
                    # 组装错误答案
                    mistake_answer_list = [option.name for option in title_obj.objective_line_ids.filtered(
                        lambda objective_line: not objective_line.is_correct)]
                    # 回答包含错误选项
                    has_mistake_option = False
                    # 组装考生选择的选项
                    real_answer_list = []
                    for answer_item in choose_list:
                        if answer_item['option_name'] in mistake_answer_list:
                            has_mistake_option = True
                        real_answer_list.append(answer_item['option_name'])
                    # 将考生选择的回填入客观题明细
                    write_data = {'real_answer': '、'.join(real_answer_list), 'mark': 0}
                    if not has_mistake_option:  # 没有错误答案：半对或全对
                        # 全对
                        if set(real_answer_list) == set(true_answer_list):
                            # 将该考题分数填入
                            write_data.update({'mark': title_line.proportion_mark})
                        else:  # 半对
                            # 半对给分
                            if exam_record_id.checkbox_score_type == 'give':
                                write_data.update({'mark': title_line.proportion_mark / 2})
                    title_line.write(write_data)
            elif title_obj.data_type == 'gap_filling':  # 填空
                answer_detail = http.request.jsonrequest.get('answer_detail', [])
                total_count = len(answer_detail)
                true_count = 0
                # 取答案
                true_answer_list = http.request.env(user=SUPERUSER_ID)['roke.subject.objective.title.data.line'].search(
                    [('parent_id', '=', title_obj.id)], order='sequence')
                if len(true_answer_list) != total_count:
                    _logger.warning("填空题【%s】实际填写数【%s】和需要填写数【%s】不一致" % (title_id, total_count, len(true_answer_list)))
                    return {
                            "state": "error",
                            "msgs": "填空题【%s】实际填写数【%s】和需要填写数【%s】不一致" % (title_id, total_count, len(true_answer_list)),
                            "data": ""
                        }
                for i in range(total_count):
                    if answer_detail[i]['real_answer'] == true_answer_list[i].name:
                        true_count += 1
                answer_detail_list = [answer_detail_item['real_answer'] for answer_detail_item in answer_detail]
                write_dict = {'real_answer': '、'.join(answer_detail_list),
                              'mark': true_count / total_count * title_line.proportion_mark}
                title_line.write(write_dict)
        return {
            "state": "success",
            "msgs": "请求成功！",
            "data": ""
        }

    # 查看已完成考试
    @http.route('/roke/get_done_exam_detail', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_done_exam_detail(self):
        exam_id = http.request.jsonrequest.get('exam_id', False)
        if not exam_id:
            return {
                "state": "error",
                "msgs": "未获取到考试信息，请刷新重试",
                "data": ""
            }
        student_id = http.request.jsonrequest.get('student_id', False)
        if not student_id:
            return {
                "state": "error",
                "msgs": "未获取到考生ID，请刷新再试",
                "data": ""
            }
        exam_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.student.exam'].search([('id', '=', int(exam_id))])
        # 校验考生ID和考试是否一致
        if exam_obj.employee_id.id != int(student_id):
            return {
                "state": "error",
                "msgs": "考生与考试不匹配，请刷新再试",
                "data": ""
            }
        record_obj = http.request.env(user=SUPERUSER_ID)['roke.subject.examination.record'].search(
            [('exam_id', '=', exam_obj.id)])
        if exam_obj.pattern_type == 'practice':
            is_can_see_score = True
            is_can_see_true_answer = True
        else:
            is_can_see_score = exam_obj.is_can_see_score
            is_can_see_true_answer = exam_obj.is_can_see_true_answer
        exam_info = {
            'exam_id': exam_obj.id,
            'total_marks': record_obj.total_marks,
            'exam_score': record_obj.total_score,
            'subjectivity_score': record_obj.subjectivity_score,
            'objective_score': record_obj.objective_score,
            'student_name': exam_obj.employee_id.name,
            'student_code': exam_obj.employee_id.job_number,
            'student_card_code': exam_obj.employee_id.id_number if exam_obj.employee_id.id_number else "",
            'start_time': exam_obj.start_time + timedelta(hours=8) if exam_obj.start_time else False,
            'real_start_time': exam_obj.real_start_time + timedelta(hours=8) if exam_obj.real_start_time else False,
            'end_time': exam_obj.end_time + timedelta(hours=8) if exam_obj.end_time else False,
            'real_end_time': exam_obj.real_end_time + timedelta(hours=8) if exam_obj.real_end_time else False,
            'time_length': exam_obj.time_length,
            'real_time_length': exam_obj.real_time_length,
            'is_can_see_score': is_can_see_score,
            'is_can_see_true_answer': is_can_see_true_answer,
            'employee_team': exam_obj.employee_id.employee_team,
        }

        subject_list = []
        option_list = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K']
        radio_dict = {'subject_type': '单选题', 'title_list': []}
        checkbox_dict = {'subject_type': '多选题', 'title_list': []}
        judge_dict = {'subject_type': '判断题', 'title_list': []}
        gap_filling_dict = {'subject_type': '填空题', 'title_list': []}
        subjectivity_dict = {'subject_type': '主观题', 'title_list': []}
        radio_sequence = 1
        checkbox_sequence = 1
        judge_sequence = 1
        gap_filling_sequence = 1
        # 客观
        for objective_line in record_obj.objective_line_ids:
            # 单选
            if objective_line.title_data_id.data_type == 'radio':
                radio_info = {
                    'title': objective_line.title_description,
                    'data_sequence': radio_sequence,
                    'title_id': objective_line.title_data_id.id,
                    'proportion_mark': objective_line.proportion_mark,
                    'mark': objective_line.mark,
                    'answer_analysis': objective_line.title_data_id.remark or ''
                }
                option_detail_list = []
                option_info_list = []
                for radio_item in objective_line.title_data_id.objective_line_ids:
                    option_info_list.append(radio_item)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if option_info.name == objective_line.real_answer:
                        option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                radio_info['options'] = option_detail_list
                radio_dict['title_list'].append(radio_info)
                radio_sequence += 1
            # 多选
            elif objective_line.title_data_id.data_type == 'checkbox':
                checkbox_info = {
                    'title': objective_line.title_description,
                    'data_sequence': checkbox_sequence,
                    'title_id': objective_line.title_data_id.id,
                    'proportion_mark': objective_line.proportion_mark,
                    'mark': objective_line.mark,
                    'answer_analysis': objective_line.title_data_id.remark or ''
                }
                real_answer_list = []
                # 查询已选择
                if objective_line.real_answer:
                    real_answer_list = objective_line.real_answer.split('、')
                option_detail_list = []
                option_info_list = []
                for checkbox_item in objective_line.title_data_id.objective_line_ids:
                    option_info_list.append(checkbox_item)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if real_answer_list:
                        if option_info.name in real_answer_list:
                            option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                checkbox_info['options'] = option_detail_list
                checkbox_dict['title_list'].append(checkbox_info)
                checkbox_sequence += 1
            # 判断
            elif objective_line.title_data_id.data_type == 'judge':
                judge_info = {
                    'title': objective_line.title_description,
                    'data_sequence': judge_sequence,
                    'title_id': objective_line.title_data_id.id,
                    'proportion_mark': objective_line.proportion_mark,
                    'mark': objective_line.mark,
                    'answer_analysis': objective_line.title_data_id.remark or ''
                }
                option_detail_list = []
                option_info_list = []
                for judge_item in objective_line.title_data_id.objective_line_ids:
                    option_info_list.append(judge_item)
                for i, option_info in enumerate(option_info_list):
                    option_detail_item = {
                        'option': option_list[i],
                        'option_name': option_info.name,
                        'is_correct': option_info.is_correct,
                        'is_choose': False
                    }
                    if option_info.name == objective_line.real_answer:
                        option_detail_item.update({'is_choose': True})
                    option_detail_list.append(option_detail_item)
                judge_info['options'] = option_detail_list
                judge_dict['title_list'].append(judge_info)
                judge_sequence += 1
            # 填空
            elif objective_line.title_data_id.data_type == 'gap_filling':
                gap_filling_info = {
                    'title': objective_line.title_data_id.description,
                    'data_sequence': gap_filling_sequence,
                    'title_id': objective_line.title_data_id.id,
                    'proportion_mark': objective_line.proportion_mark,
                    'mark': objective_line.proportion_mark,
                    'answer_analysis': objective_line.title_data_id.remark or ''
                }
                gap_filling_answer_list = []
                # 组装填空列表
                for gap_filling_item in objective_line.title_data_id.objective_line_ids:
                    gap_filling_answer_list.append({
                        'true_answer': gap_filling_item.name,
                        'real_answer': ''
                    })
                gap_filling_real_answer_list = objective_line.real_answer.split('、') if objective_line.real_answer else []
                if gap_filling_real_answer_list:
                    for i, item_dict in enumerate(gap_filling_real_answer_list):
                        gap_filling_answer_list[i]['real_answer'] = gap_filling_real_answer_list[i]
                gap_filling_info['answer_detail'] = gap_filling_answer_list
                gap_filling_dict['title_list'].append(gap_filling_info)
                gap_filling_sequence += 1
        subjectivity_sequence = 1
        # 主观
        # 主观评分
        grade_obj = http.request.env(user=SUPERUSER_ID)['roke.subjectivity.grade'].search(
            [('exam_id', '=', exam_obj.id)])
        if grade_obj:
            for line in grade_obj.line_ids:
                subjectivity_info = {
                    'title': line.title_data_id.description,
                    'data_sequence': subjectivity_sequence,
                    'content': line.title_data_id.content,
                    'title_id': line.title_data_id.id,
                    'real_content': line.answer or '',
                    'proportion_mark': line.proportion_mark,
                    'mark': line.mark,
                    'answer_analysis': line.title_data_id.remark
                }
                subjectivity_dict['title_list'].append(subjectivity_info)
                subjectivity_sequence += 1
        if radio_dict['title_list']:
            subject_list.append(radio_dict)
        if checkbox_dict['title_list']:
            subject_list.append(checkbox_dict)
        if judge_dict['title_list']:
            subject_list.append(judge_dict)
        if gap_filling_dict['title_list']:
            subject_list.append(gap_filling_dict)
        if subjectivity_dict['title_list']:
            subject_list.append(subjectivity_dict)
        return {
            "state": "success",
            "msgs": "请求成功！",
            "data": subject_list,
            "exam_info": exam_info
        }

