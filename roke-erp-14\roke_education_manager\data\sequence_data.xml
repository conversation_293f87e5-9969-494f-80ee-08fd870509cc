<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--教师唯一编号-->
<!--    <record id="roek_base_teacher_code_sequence" model="ir.sequence">-->
<!--        <field name="name">教师唯一编号</field>-->
<!--        <field name="code">roke.base.teacher.code</field>-->
<!--        <field name="prefix">TCH</field>-->
<!--        <field name="padding">3</field>-->
<!--        <field name="company_id" eval="False"/>-->
<!--    </record>-->
    <!--考试科目唯一编号-->
    <record id="roek_subject_course_code_sequence" model="ir.sequence">
        <field name="name">考试科目唯一编号</field>
        <field name="code">roke.subject.course.code</field>
        <field name="prefix">KSKM</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--科目考试项目唯一编号-->
    <record id="roek_subject_project_code_sequence" model="ir.sequence">
        <field name="name">科目考试项目唯一编号</field>
        <field name="code">roke.subject.project.code</field>
        <field name="prefix">KSXM</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--题库管理-->
    <record id="roek_subject_title_data_code_sequence" model="ir.sequence">
        <field name="name">题库管理唯一编号</field>
        <field name="code">roke.subject.title.data.code</field>
        <field name="prefix">TK%(y)s%(month)s%(day)s</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--抽题规则-->
    <record id="roek_subject_rules_code_sequence" model="ir.sequence">
        <field name="name">抽题规则唯一编号</field>
        <field name="code">roke.subject.rules.code</field>
        <field name="prefix">CTGZ</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--评分规则-->
    <record id="roek_subject_mark_evaluation_sequence" model="ir.sequence">
        <field name="name">评分规则唯一编号</field>
        <field name="code">roke.subject.evaluation.code</field>
        <field name="prefix">PFGZ</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--评分方式-->
    <record id="roek_subject_mark_type_code_sequence" model="ir.sequence">
        <field name="name">评分方式唯一编号</field>
        <field name="code">roke.subject.mark.type.code</field>
        <field name="prefix">PFFS</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>


    <!--学生考试-->
    <record id="roek_base_exam_code_sequence" model="ir.sequence">
        <field name="name">学生考试编号</field>
        <field name="code">roke.base.exam</field>
        <field name="prefix">XSKS%(y)s%(month)s%(day)s</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>

    <!--试卷管理-->
    <record id="roek_base_test_paper_code_sequence" model="ir.sequence">
        <field name="name">试题编号</field>
        <field name="code">roke.base.test.paper.code</field>
        <field name="prefix">STBH</field>
        <field name="padding">4</field>
        <field name="company_id" eval="False"/>
    </record>
</odoo>
