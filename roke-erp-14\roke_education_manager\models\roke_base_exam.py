# -*- coding: utf-8 -*-
"""
Description:
考试
"""
import datetime
import string
import random
from io import BytesIO

import xlwt
from odoo import models, fields, api, tools
from odoo.exceptions import ValidationError
import base64


class RokeBaseExam(models.Model):
    _name = "roke.base.exam"
    _inherit = ['mail.thread']
    _description = "考试"
    _rec_name = "name"

    name = fields.Char(string="考试名称", required=True, index=True, tracking=True)
    number = fields.Char(string="考试编号", copy=False, required=True, index=True, tracking=True)
    dispatch_type = fields.Selection([('same', '考题一致'), ('different', '随机分配')], string='考题分配方式',
                                     default='same')
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='启用状态', default='normal')
    pattern_type = fields.Selection([('practice', '练习模式'), ('exam', '考试模式')], string='模式类型', default='exam')
    state = fields.Selection(
        [('draft', '草稿'), ('data_dispatch', '考题分配'), ('wait_exam', '等待考试'), ('exam_taking', '考试中'),
         ('exam_suspend', '考试暂停'), ('wait_subjectivity', '等待主观评分'), ('done', '已交卷')], string='状态',
        default='draft', tracking=True)
    rule_id = fields.Many2one('roke.subject.rules', string='抽题规则')
    test_paper_id = fields.Many2one('roke.base.test.paper', string="试题")
    course_id = fields.Many2one('roke.subject.course', string="科目")
    is_dispatch = fields.Boolean(string='是否已分配考题', default=False, tracking=True)
    start_time = fields.Datetime(string="开始时间")
    end_time = fields.Datetime(string="结束时间")
    time_length = fields.Integer(string="时长(分钟)")
    total_marks = fields.Float(string='总分数', digits=(8, 2))
    exam_line_ids = fields.One2many('roke.subject.student.exam', 'parent_id', string='考试学生明细')
    remark = fields.Text(string='备注')
    is_main_exam = fields.Boolean(string='是否为主考试', default=False)
    exam_ids = fields.One2many('roke.base.exam', 'parent_id', string='场次明细')
    parent_id = fields.Many2one('roke.base.exam', string='主考试')
    round_count = fields.Integer(string='第几场')
    round_name = fields.Char(string='场次', compute='_compute_round_name')
    checkbox_score_type = fields.Selection([('give', '多选题半对给分'), ('not_give', '多选题半对不给分')],
                                           string='多选题给分模式', default='give')

    @api.onchange('start_time', 'time_length')
    def _onchange_start_time_length(self):
        """
        根据开始时间、时长计算结束时间
        :return:
        """
        if self.time_length and self.start_time:
            self.end_time = self.start_time + datetime.timedelta(minutes=self.time_length)

    @api.onchange('end_time')
    def _onchange_end_time(self):
        """
        校验结束时间是否正确
            需要大于等于开始时间+时长
        :return:
        """
        if self.end_time:
            if self.start_time and self.time_length:
                if self.end_time < self.start_time + datetime.timedelta(minutes=self.time_length):
                    self.end_time = False
                    return {"warning": {
                        "title": "提醒", "message": "结束时间必须大于等于 开始时间 + 时长"
                    }, "value": {}}

    @api.depends('round_count')
    def _compute_round_name(self):
        """
        计算场次名
        :return:
        """
        for res in self:
            if res.parent_id and res.round_count:
                res.round_name = '第' + str(res.round_count) + '场'

    def split_round(self):
        """
        拆分场次
        :return:
        """
        if self.state != 'draft':
            raise ValidationError('当前考试不是草稿状态，不能拆分场次')
        view = self.env.ref('roke_education_manager.roke_exam_split_round_wizard_form')
        return {
            'name': "拆分场次",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.split.round.wizard',
            'context': {
                'default_exam_id': self.id,
                'default_has_student': True if self.exam_line_ids else False,
            },
            'target': 'new',
        }

    # 分配考题
    # def btn_title_data(self):
    #     """
    #     分配考题
    #     :return:
    #     """
    #     if not self:
    #         raise ValidationError('请选择考试再分配考题')
    #     not_draft_exam_obj = self.filtered(lambda exam: exam.state != 'draft')
    #     if not_draft_exam_obj:
    #         not_draft_exam_list = [not_draft_exam.name for not_draft_exam in not_draft_exam_obj]
    #         raise ValidationError(
    #             '请选择草稿状态的考试进行分配考题，当前选择考试不是草稿状态:\n 【%s】' % '、'.join(not_draft_exam_list))
    #     not_student_exam_obj = self.filtered(lambda exam: not exam.exam_line_ids)
    #     if not_student_exam_obj:
    #         not_student_exam_list = [not_student_exam.name for not_student_exam in not_student_exam_obj]
    #         raise ValidationError(
    #             '请选择已经导入学生的考试进行分配考题，当前选择考试未导入学生:\n 【%s】' % '、'.join(not_student_exam_list))
    #     main_exam_obj = self.filtered(lambda exam: exam.is_main_exam)
    #     if main_exam_obj:
    #         main_exam_list = [main_exam.name for main_exam in main_exam_obj]
    #         raise ValidationError(
    #             '当前选择考试下有多个场次，请选择准确场次再进行考题分配:\n 【%s】' % '、'.join(main_exam_list))
    #     for res in self:
    #         res.is_dispatch = True
    #         res.state = 'data_dispatch'
    #         if res.rule_id:
    #             if res.dispatch_type == 'same':  # 考题一致
    #                 data_sequence = 1
    #                 exam_line_list = []
    #                 total_marks = 0
    #                 # 从题库中抽取对应科目的题目, 按照数量抽取
    #                 # # 实操类
    #                 # operation_data_ids = self.env['roke.subject.title.data'].search(
    #                 #     [('project_id.project_type', '=', 'operation')])
    #                 # operation_data_list = []
    #                 # operation_title_data_list = [item for item in operation_data_ids]
    #                 # while len(operation_data_list) < res.rule_id.operation_count:
    #                 #     data_id = random.choice(operation_title_data_list)
    #                 #     if data_id not in operation_data_list:
    #                 #         operation_data_list.append(data_id)
    #                 #         operation_title_data_list.remove(data_id)
    #                 # for operation_item in operation_data_list:
    #                 #     total_marks += operation_item.total_marks
    #                 #     # 创建考试明细
    #                 #     exam_line_list.append({
    #                 #         'sequence': data_sequence,
    #                 #         'title_date_id': operation_item.id,
    #                 #         'course_id': operation_item.project_id.course_id.id,
    #                 #         'project_id': operation_item.project_id.id,
    #                 #         'total_marks': operation_item.total_marks,
    #                 #         'remark': operation_item.remark,
    #                 #     })
    #                 #     data_sequence += 1
    #                 # 客观类单选
    #                 radio_data_ids = self.env['roke.subject.title.data'].search(
    #                     [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'radio')])
    #                 radio_data_list = []
    #                 radio_title_data_list = [item for item in radio_data_ids]
    #                 while len(radio_data_list) < res.rule_id.radio_count:
    #                     data_id = random.choice(radio_title_data_list)
    #                     if data_id not in radio_data_list:
    #                         radio_data_list.append(data_id)
    #                         radio_title_data_list.remove(data_id)
    #                 for radio_item in radio_data_list:
    #                     total_marks += radio_item.total_marks
    #                     # 创建考试明细
    #                     exam_line_list.append({
    #                         'sequence': data_sequence,
    #                         'title_date_id': radio_item.id,
    #                         'course_id': radio_item.project_id.course_id.id,
    #                         'project_id': radio_item.project_id.id,
    #                         'total_marks': radio_item.total_marks,
    #                         'remark': radio_item.remark,
    #                         'is_random': res.rule_id.is_random
    #                     })
    #                     data_sequence += 1
    #                 # 客观类多选
    #                 checkbox_data_ids = self.env['roke.subject.title.data'].search(
    #                     [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'checkbox')])
    #                 checkbox_data_list = []
    #                 checkbox_title_data_list = [item for item in checkbox_data_ids]
    #                 while len(checkbox_data_list) < res.rule_id.checkbox_count:
    #                     data_id = random.choice(checkbox_title_data_list)
    #                     if data_id not in checkbox_data_list:
    #                         checkbox_data_list.append(data_id)
    #                         checkbox_title_data_list.remove(data_id)
    #                 for checkbox_item in checkbox_data_list:
    #                     total_marks += checkbox_item.total_marks
    #                     # 创建考试明细
    #                     exam_line_list.append({
    #                         'sequence': data_sequence,
    #                         'title_date_id': checkbox_item.id,
    #                         'course_id': checkbox_item.project_id.course_id.id,
    #                         'project_id': checkbox_item.project_id.id,
    #                         'total_marks': checkbox_item.total_marks,
    #                         'remark': checkbox_item.remark,
    #                         'is_random': res.rule_id.is_random
    #                     })
    #                     data_sequence += 1
    #                 # 客观类判断
    #                 judge_data_ids = self.env['roke.subject.title.data'].search(
    #                     [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'judge')])
    #                 judge_data_list = []
    #                 judge_title_data_list = [item for item in judge_data_ids]
    #                 while len(judge_data_list) < res.rule_id.judge_count:
    #                     data_id = random.choice(judge_title_data_list)
    #                     if data_id not in judge_data_list:
    #                         judge_data_list.append(data_id)
    #                         judge_title_data_list.remove(data_id)
    #                 for judge_item in judge_data_list:
    #                     total_marks += judge_item.total_marks
    #                     # 创建考试明细
    #                     exam_line_list.append({
    #                         'sequence': data_sequence,
    #                         'title_date_id': judge_item.id,
    #                         'course_id': judge_item.project_id.course_id.id,
    #                         'project_id': judge_item.project_id.id,
    #                         'total_marks': judge_item.total_marks,
    #                         'remark': judge_item.remark,
    #                         'is_random': res.rule_id.is_random
    #                     })
    #                     data_sequence += 1
    #                 # 主观类
    #                 subjectivity_data_ids = self.env['roke.subject.title.data'].search(
    #                     [('project_id.project_type', '=', 'subjectivity')])
    #                 subjectivity_data_list = []
    #                 subjectivity_title_data_list = [item for item in subjectivity_data_ids]
    #                 while len(subjectivity_data_list) < res.rule_id.subjectivity_count:
    #                     data_id = random.choice(subjectivity_title_data_list)
    #                     if data_id not in subjectivity_data_list:
    #                         subjectivity_data_list.append(data_id)
    #                         subjectivity_title_data_list.remove(data_id)
    #                 for subjectivity_item in subjectivity_data_list:
    #                     total_marks += subjectivity_item.total_marks
    #                     # 创建考试明细
    #                     exam_line_list.append({
    #                         'sequence': data_sequence,
    #                         'title_date_id': subjectivity_item.id,
    #                         'course_id': subjectivity_item.project_id.course_id.id,
    #                         'project_id': subjectivity_item.project_id.id,
    #                         'total_marks': subjectivity_item.total_marks,
    #                         'remark': subjectivity_item.remark,
    #                     })
    #                     data_sequence += 1
    #                 res.total_marks = total_marks
    #                 for exam_item in res.exam_line_ids:
    #                     for exam_line in exam_line_list:
    #                         exam_line['parent_id'] = exam_item.id
    #                         # 创建考试明细
    #                         self.env['roke.subject.student.exam.line'].create(exam_line)
    #                     exam_item.total_marks = total_marks
    #                     exam_item.state = 'data_dispatch'
    #
    #             else:  # 随机抽题
    #                 for line in res.exam_line_ids:
    #                     data_sequence = 1
    #                     exam_line_list = []
    #                     total_marks = 0
    #                     # 从题库中抽取对应科目的题目, 实操类、主观类抽取对应分数的一道题，客观类按照数量抽取
    #                     # 实操类
    #                     # operation_data_ids = self.env['roke.subject.title.data'].search(
    #                     #     [('project_id.project_type', '=', 'operation')])
    #                     # operation_data_list = []
    #                     # operation_title_data_list = [item for item in operation_data_ids]
    #                     # while len(operation_data_list) < res.rule_id.operation_count:
    #                     #     data_id = random.choice(operation_title_data_list)
    #                     #     if data_id not in operation_data_list:
    #                     #         operation_data_list.append(data_id)
    #                     #         operation_title_data_list.remove(data_id)
    #                     # for operation_item in operation_data_list:
    #                     #     total_marks += operation_item.total_marks
    #                     #     # 创建考试明细
    #                     #     exam_line_list.append({
    #                     #         'parent_id': line.id,
    #                     #         'sequence': data_sequence,
    #                     #         'title_date_id': operation_item.id,
    #                     #         'course_id': operation_item.project_id.course_id.id,
    #                     #         'project_id': operation_item.project_id.id,
    #                     #         'total_marks': operation_item.total_marks,
    #                     #         'remark': operation_item.remark,
    #                     #     })
    #                     #     data_sequence += 1
    #                     # 客观类单选
    #                     radio_data_ids = self.env['roke.subject.title.data'].search(
    #                         [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'radio')])
    #                     radio_data_list = []
    #                     radio_title_data_list = [item for item in radio_data_ids]
    #                     while len(radio_data_list) < res.rule_id.radio_count:
    #                         data_id = random.choice(radio_title_data_list)
    #                         if data_id not in radio_data_list:
    #                             radio_data_list.append(data_id)
    #                             radio_title_data_list.remove(data_id)
    #                     for radio_item in radio_data_list:
    #                         total_marks += radio_item.total_marks
    #                         # 创建考试明细
    #                         exam_line_list.append({
    #                             'parent_id': line.id,
    #                             'sequence': data_sequence,
    #                             'title_date_id': radio_item.id,
    #                             'course_id': radio_item.project_id.course_id.id,
    #                             'project_id': radio_item.project_id.id,
    #                             'total_marks': radio_item.total_marks,
    #                             'remark': radio_item.remark,
    #                             'is_random': res.rule_id.is_random
    #                         })
    #                         data_sequence += 1
    #                     # 客观类多选
    #                     checkbox_data_ids = self.env['roke.subject.title.data'].search(
    #                         [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'checkbox')])
    #                     checkbox_data_list = []
    #                     checkbox_title_data_list = [item for item in checkbox_data_ids]
    #                     while len(checkbox_data_list) < res.rule_id.checkbox_count:
    #                         data_id = random.choice(checkbox_title_data_list)
    #                         if data_id not in checkbox_data_list:
    #                             checkbox_data_list.append(data_id)
    #                             checkbox_title_data_list.remove(data_id)
    #                     for checkbox_item in checkbox_data_list:
    #                         total_marks += checkbox_item.total_marks
    #                         # 创建考试明细
    #                         exam_line_list.append({
    #                             'parent_id': line.id,
    #                             'sequence': data_sequence,
    #                             'title_date_id': checkbox_item.id,
    #                             'course_id': checkbox_item.project_id.course_id.id,
    #                             'project_id': checkbox_item.project_id.id,
    #                             'total_marks': checkbox_item.total_marks,
    #                             'remark': checkbox_item.remark,
    #                             'is_random': res.rule_id.is_random
    #                         })
    #                         data_sequence += 1
    #                     # 客观类判断
    #                     judge_data_ids = self.env['roke.subject.title.data'].search(
    #                         [('project_id.project_type', '=', 'objective'), ('data_type', '=', 'judge')])
    #                     judge_data_list = []
    #                     judge_title_data_list = [item for item in judge_data_ids]
    #                     while len(judge_data_list) < res.rule_id.judge_count:
    #                         data_id = random.choice(judge_title_data_list)
    #                         if data_id not in judge_data_list:
    #                             judge_data_list.append(data_id)
    #                             judge_title_data_list.remove(data_id)
    #                     for judge_item in judge_data_list:
    #                         total_marks += judge_item.total_marks
    #                         # 创建考试明细
    #                         exam_line_list.append({
    #                             'parent_id': line.id,
    #                             'sequence': data_sequence,
    #                             'title_date_id': judge_item.id,
    #                             'course_id': judge_item.project_id.course_id.id,
    #                             'project_id': judge_item.project_id.id,
    #                             'total_marks': judge_item.total_marks,
    #                             'remark': judge_item.remark,
    #                             'is_random': res.rule_id.is_random
    #                         })
    #                         data_sequence += 1
    #                     # 主观类
    #                     subjectivity_data_ids = self.env['roke.subject.title.data'].search(
    #                         [('project_id.project_type', '=', 'subjectivity')])
    #                     subjectivity_data_list = []
    #                     subjectivity_title_data_list = [item for item in subjectivity_data_ids]
    #                     while len(subjectivity_data_list) < res.rule_id.subjectivity_count:
    #                         data_id = random.choice(subjectivity_title_data_list)
    #                         if data_id not in subjectivity_data_list:
    #                             subjectivity_data_list.append(data_id)
    #                             subjectivity_title_data_list.remove(data_id)
    #                     for subjectivity_item in subjectivity_data_list:
    #                         total_marks += subjectivity_item.total_marks
    #                         # 创建考试明细
    #                         exam_line_list.append({
    #                             'parent_id': line.id,
    #                             'sequence': data_sequence,
    #                             'title_date_id': subjectivity_item.id,
    #                             'course_id': subjectivity_item.project_id.course_id.id,
    #                             'project_id': subjectivity_item.project_id.id,
    #                             'total_marks': subjectivity_item.total_marks,
    #                             'remark': subjectivity_item.remark,
    #                         })
    #                         data_sequence += 1
    #                     self.env['roke.subject.student.exam.line'].create(exam_line_list)
    #                     line.write({
    #                         'total_marks': total_marks,
    #                         'state': 'data_dispatch'
    #                     })
    #
    #                     res.total_marks = total_marks
    #
    #         else:  # 处理预设试题的
    #             data_sequence = 1
    #             res.state = 'data_dispatch'
    #             res.total_marks = res.test_paper_id.total_mark
    #             for data_id in res.test_paper_id.title_ids:
    #                 if data_id.project_id.project_type == 'objective' and res.test_paper_id.is_random:
    #                     data_random = True
    #                 else:
    #                     data_random = False
    #                 for exam_line in res.exam_line_ids:
    #                     exam_line.total_marks = res.test_paper_id.total_mark
    #                     # 创建考试明细
    #                     self.env['roke.subject.student.exam.line'].create({
    #                         'parent_id': exam_line.id,
    #                         'sequence': data_sequence,
    #                         'title_date_id': data_id.id,
    #                         'course_id': data_id.project_id.course_id.id,
    #                         'project_id': data_id.project_id.id,
    #                         'total_marks': data_id.total_marks,
    #                         'remark': data_id.remark,
    #                         'is_random': data_random
    #                     })
    #                     data_sequence += 1

    def btn_title_data(self):
        """
        分配考题
        :return:
        """
        if not self:
            raise ValidationError('请选择考试再分配考题')
        not_draft_exam_obj = self.filtered(lambda exam: exam.state != 'draft')
        if not_draft_exam_obj:
            not_draft_exam_list = [not_draft_exam.name for not_draft_exam in not_draft_exam_obj]
            raise ValidationError(
                '请选择草稿状态的考试进行分配考题，当前选择考试不是草稿状态:\n 【%s】' % '、'.join(not_draft_exam_list))
        not_student_exam_obj = self.filtered(lambda exam: not exam.exam_line_ids)
        if not_student_exam_obj:
            not_student_exam_list = [not_student_exam.name for not_student_exam in not_student_exam_obj]
            raise ValidationError(
                '请选择已经导入学生的考试进行分配考题，当前选择考试未导入学生:\n 【%s】' % '、'.join(not_student_exam_list))
        main_exam_obj = self.filtered(lambda exam: exam.is_main_exam)
        if main_exam_obj:
            main_exam_list = [main_exam.name for main_exam in main_exam_obj]
            raise ValidationError(
                '当前选择考试下有多个场次，请选择准确场次再进行考题分配:\n 【%s】' % '、'.join(main_exam_list))
        for res in self:
            res.is_dispatch = True
            res.state = 'data_dispatch'
            if res.rule_id:
                if res.dispatch_type == 'same':  # 考题一致
                    data_sequence = 1
                    exam_line_list = []
                    total_marks = 0
                    # 先取单选题
                    radio_line = res.rule_id.line_ids.filtered(
                        lambda rule_line:
                        rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'radio')
                    if radio_line:
                        for radio_line_item in radio_line:
                            # 从题库中抽取对应科目的题目, 按照数量抽取
                            # 客观类单选
                            radio_data_ids = self.env['roke.subject.title.data'].search(
                                [('project_id', '=', radio_line_item.project_id.id)])
                            radio_data_list = []
                            radio_title_data_list = [item for item in radio_data_ids]
                            while len(radio_data_list) < radio_line_item.title_count:
                                data_id = random.choice(radio_title_data_list)
                                if data_id not in radio_data_list:
                                    radio_data_list.append(data_id)
                                    radio_title_data_list.remove(data_id)
                            for radio_item in radio_data_list:
                                total_marks += radio_item.total_marks
                                # 创建考试明细
                                exam_line_list.append({
                                    'sequence': data_sequence,
                                    'title_date_id': radio_item.id,
                                    'course_id': radio_item.project_id.course_id.id,
                                    'project_id': radio_item.project_id.id,
                                    'total_marks': radio_item.total_marks,
                                    'remark': radio_item.remark,
                                    'is_random': res.rule_id.is_random
                                })
                                data_sequence += 1
                    # 客观类多选
                    checkbox_line = res.rule_id.line_ids.filtered(
                        lambda rule_line:
                        rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'checkbox')
                    if checkbox_line:
                        for checkbox_line_item in checkbox_line:
                            checkbox_data_ids = self.env['roke.subject.title.data'].search(
                                [('project_id', '=', checkbox_line_item.project_id.id)])
                            checkbox_data_list = []
                            checkbox_title_data_list = [item for item in checkbox_data_ids]
                            while len(checkbox_data_list) < checkbox_line_item.title_count:
                                data_id = random.choice(checkbox_title_data_list)
                                if data_id not in checkbox_data_list:
                                    checkbox_data_list.append(data_id)
                                    checkbox_title_data_list.remove(data_id)
                            for checkbox_item in checkbox_data_list:
                                total_marks += checkbox_item.total_marks
                                # 创建考试明细
                                exam_line_list.append({
                                    'sequence': data_sequence,
                                    'title_date_id': checkbox_item.id,
                                    'course_id': checkbox_item.project_id.course_id.id,
                                    'project_id': checkbox_item.project_id.id,
                                    'total_marks': checkbox_item.total_marks,
                                    'remark': checkbox_item.remark,
                                    'is_random': res.rule_id.is_random
                                })
                                data_sequence += 1
                    # 客观类判断
                    judge_line = res.rule_id.line_ids.filtered(
                        lambda rule_line:
                        rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'judge')
                    if judge_line:
                        for judge_line_item in judge_line:
                            judge_data_ids = self.env['roke.subject.title.data'].search(
                                [('project_id', '=', judge_line_item.project_id.id)])
                            judge_data_list = []
                            judge_title_data_list = [item for item in judge_data_ids]
                            while len(judge_data_list) < judge_line_item.title_count:
                                data_id = random.choice(judge_title_data_list)
                                if data_id not in judge_data_list:
                                    judge_data_list.append(data_id)
                                    judge_title_data_list.remove(data_id)
                            for judge_item in judge_data_list:
                                total_marks += judge_item.total_marks
                                # 创建考试明细
                                exam_line_list.append({
                                    'sequence': data_sequence,
                                    'title_date_id': judge_item.id,
                                    'course_id': judge_item.project_id.course_id.id,
                                    'project_id': judge_item.project_id.id,
                                    'total_marks': judge_item.total_marks,
                                    'remark': judge_item.remark,
                                    'is_random': res.rule_id.is_random
                                })
                                data_sequence += 1
                    # 客观类填空
                    gap_filling_line = res.rule_id.line_ids.filtered(
                        lambda rule_line:
                        rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'gap_filling')
                    if gap_filling_line:
                        for gap_filling_line_item in gap_filling_line:
                            gap_filling_data_ids = self.env['roke.subject.title.data'].search(
                                [('project_id', '=', gap_filling_line_item.project_id.id)])
                            gap_filling_data_list = []
                            gap_filling_title_data_list = [item for item in gap_filling_data_ids]
                            while len(gap_filling_data_list) < gap_filling_line_item.title_count:
                                data_id = random.choice(gap_filling_title_data_list)
                                if data_id not in gap_filling_data_list:
                                    gap_filling_data_list.append(data_id)
                                    gap_filling_title_data_list.remove(data_id)
                            for gap_filling_item in gap_filling_data_list:
                                total_marks += gap_filling_item.total_marks
                                # 创建考试明细
                                exam_line_list.append({
                                    'sequence': data_sequence,
                                    'title_date_id': gap_filling_item.id,
                                    'course_id': gap_filling_item.project_id.course_id.id,
                                    'project_id': gap_filling_item.project_id.id,
                                    'total_marks': gap_filling_item.total_marks,
                                    'remark': gap_filling_item.remark,
                                    'is_random': False
                                })
                                data_sequence += 1
                    # 主观类
                    subjectivity_line = res.rule_id.line_ids.filtered(
                        lambda rule_line: rule_line.project_id.project_type == 'subjectivity')
                    if subjectivity_line:
                        for subjectivity_line_item in subjectivity_line:
                            subjectivity_data_ids = self.env['roke.subject.title.data'].search(
                                [('project_id', '=', subjectivity_line_item.project_id.id)])
                            subjectivity_data_list = []
                            subjectivity_title_data_list = [item for item in subjectivity_data_ids]
                            while len(subjectivity_data_list) < subjectivity_line_item.title_count:
                                data_id = random.choice(subjectivity_title_data_list)
                                if data_id not in subjectivity_data_list:
                                    subjectivity_data_list.append(data_id)
                                    subjectivity_title_data_list.remove(data_id)
                            for subjectivity_item in subjectivity_data_list:
                                total_marks += subjectivity_item.total_marks
                                # 创建考试明细
                                exam_line_list.append({
                                    'sequence': data_sequence,
                                    'title_date_id': subjectivity_item.id,
                                    'course_id': subjectivity_item.project_id.course_id.id,
                                    'project_id': subjectivity_item.project_id.id,
                                    'total_marks': subjectivity_item.total_marks,
                                    'remark': subjectivity_item.remark,
                                })
                                data_sequence += 1
                    res.total_marks = total_marks
                    for exam_item in res.exam_line_ids:
                        for exam_line in exam_line_list:
                            exam_line['parent_id'] = exam_item.id
                            # 创建考试明细
                            self.env['roke.subject.student.exam.line'].create(exam_line)
                        exam_item.total_marks = total_marks
                        exam_item.state = 'data_dispatch'

                else:  # 随机抽题
                    for line in res.exam_line_ids:
                        data_sequence = 1
                        exam_line_list = []
                        total_marks = 0
                        # 先取单选题
                        radio_line = res.rule_id.line_ids.filtered(
                            lambda rule_line:
                            rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'radio')
                        if radio_line:
                            for radio_line_item in radio_line:
                                # 客观类单选
                                radio_data_ids = self.env['roke.subject.title.data'].search(
                                    [('project_id', '=', radio_line_item.project_id.id)])
                                radio_data_list = []
                                radio_title_data_list = [item for item in radio_data_ids]
                                while len(radio_data_list) < radio_line_item.title_count:
                                    data_id = random.choice(radio_title_data_list)
                                    if data_id not in radio_data_list:
                                        radio_data_list.append(data_id)
                                        radio_title_data_list.remove(data_id)
                                for radio_item in radio_data_list:
                                    total_marks += radio_item.total_marks
                                    # 创建考试明细
                                    exam_line_list.append({
                                        'parent_id': line.id,
                                        'sequence': data_sequence,
                                        'title_date_id': radio_item.id,
                                        'course_id': radio_item.project_id.course_id.id,
                                        'project_id': radio_item.project_id.id,
                                        'total_marks': radio_item.total_marks,
                                        'remark': radio_item.remark,
                                        'is_random': res.rule_id.is_random
                                    })
                                    data_sequence += 1
                        # 客观类多选
                        checkbox_line = res.rule_id.line_ids.filtered(
                            lambda rule_line:
                            rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'checkbox')
                        if checkbox_line:
                            for checkbox_line_item in checkbox_line:
                                checkbox_data_ids = self.env['roke.subject.title.data'].search(
                                    [('project_id', '=', checkbox_line_item.project_id.id)])
                                checkbox_data_list = []
                                checkbox_title_data_list = [item for item in checkbox_data_ids]
                                while len(checkbox_data_list) < checkbox_line_item.title_count:
                                    data_id = random.choice(checkbox_title_data_list)
                                    if data_id not in checkbox_data_list:
                                        checkbox_data_list.append(data_id)
                                        checkbox_title_data_list.remove(data_id)
                                for checkbox_item in checkbox_data_list:
                                    total_marks += checkbox_item.total_marks
                                    # 创建考试明细
                                    exam_line_list.append({
                                        'parent_id': line.id,
                                        'sequence': data_sequence,
                                        'title_date_id': checkbox_item.id,
                                        'course_id': checkbox_item.project_id.course_id.id,
                                        'project_id': checkbox_item.project_id.id,
                                        'total_marks': checkbox_item.total_marks,
                                        'remark': checkbox_item.remark,
                                        'is_random': res.rule_id.is_random
                                    })
                                    data_sequence += 1
                        # 客观类判断
                        judge_line = res.rule_id.line_ids.filtered(
                            lambda rule_line:
                            rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'judge')
                        if judge_line:
                            for judge_line_item in judge_line:
                                judge_data_ids = self.env['roke.subject.title.data'].search(
                                    [('project_id', '=', judge_line_item.project_id.id)])
                                judge_data_list = []
                                judge_title_data_list = [item for item in judge_data_ids]
                                while len(judge_data_list) < judge_line_item.title_count:
                                    data_id = random.choice(judge_title_data_list)
                                    if data_id not in judge_data_list:
                                        judge_data_list.append(data_id)
                                        judge_title_data_list.remove(data_id)
                                for judge_item in judge_data_list:
                                    total_marks += judge_item.total_marks
                                    # 创建考试明细
                                    exam_line_list.append({
                                        'parent_id': line.id,
                                        'sequence': data_sequence,
                                        'title_date_id': judge_item.id,
                                        'course_id': judge_item.project_id.course_id.id,
                                        'project_id': judge_item.project_id.id,
                                        'total_marks': judge_item.total_marks,
                                        'remark': judge_item.remark,
                                        'is_random': res.rule_id.is_random
                                    })
                                    data_sequence += 1
                        # 客观类填空
                        gap_filling_line = res.rule_id.line_ids.filtered(
                            lambda rule_line:
                            rule_line.project_id.project_type == 'objective' and rule_line.project_id.data_type == 'gap_filling')
                        if gap_filling_line:
                            for gap_filling_line_item in gap_filling_line:
                                gap_filling_data_ids = self.env['roke.subject.title.data'].search(
                                    [('project_id', '=', gap_filling_line_item.project_id.id)])
                                gap_filling_data_list = []
                                gap_filling_title_data_list = [item for item in gap_filling_data_ids]
                                while len(gap_filling_data_list) < gap_filling_line_item.title_count:
                                    data_id = random.choice(gap_filling_title_data_list)
                                    if data_id not in gap_filling_data_list:
                                        gap_filling_data_list.append(data_id)
                                        gap_filling_title_data_list.remove(data_id)
                                for gap_filling_item in gap_filling_data_list:
                                    total_marks += gap_filling_item.total_marks
                                    # 创建考试明细
                                    exam_line_list.append({
                                        'parent_id': line.id,
                                        'sequence': data_sequence,
                                        'title_date_id': gap_filling_item.id,
                                        'course_id': gap_filling_item.project_id.course_id.id,
                                        'project_id': gap_filling_item.project_id.id,
                                        'total_marks': gap_filling_item.total_marks,
                                        'remark': gap_filling_item.remark,
                                        'is_random': False
                                    })
                                    data_sequence += 1
                        # 主观类
                        subjectivity_line = res.rule_id.line_ids.filtered(
                            lambda rule_line: rule_line.project_id.project_type == 'subjectivity')
                        if subjectivity_line:
                            for subjectivity_line_item in subjectivity_line:
                                subjectivity_data_ids = self.env['roke.subject.title.data'].search(
                                    [('project_id', '=', subjectivity_line_item.project_id.id)])
                                subjectivity_data_list = []
                                subjectivity_title_data_list = [item for item in subjectivity_data_ids]
                                while len(subjectivity_data_list) < subjectivity_line_item.title_count:
                                    data_id = random.choice(subjectivity_title_data_list)
                                    if data_id not in subjectivity_data_list:
                                        subjectivity_data_list.append(data_id)
                                        subjectivity_title_data_list.remove(data_id)
                                for subjectivity_item in subjectivity_data_list:
                                    total_marks += subjectivity_item.total_marks
                                    # 创建考试明细
                                    exam_line_list.append({
                                        'parent_id': line.id,
                                        'sequence': data_sequence,
                                        'title_date_id': subjectivity_item.id,
                                        'course_id': subjectivity_item.project_id.course_id.id,
                                        'project_id': subjectivity_item.project_id.id,
                                        'total_marks': subjectivity_item.total_marks,
                                        'remark': subjectivity_item.remark,
                                    })
                                    data_sequence += 1
                        self.env['roke.subject.student.exam.line'].create(exam_line_list)
                        line.write({
                            'total_marks': total_marks,
                            'state': 'data_dispatch'
                        })

                        res.total_marks = total_marks

            else:  # 处理预设试题的
                data_sequence = 1
                res.state = 'data_dispatch'
                res.total_marks = res.test_paper_id.total_mark
                for data_id in res.test_paper_id.title_ids:
                    if data_id.project_id.project_type == 'objective' and res.test_paper_id.is_random and\
                            data_id.data_type != 'gap_filling':
                        data_random = True
                    else:
                        data_random = False
                    for exam_line in res.exam_line_ids:
                        exam_line.total_marks = res.test_paper_id.total_mark
                        # 创建考试明细
                        self.env['roke.subject.student.exam.line'].create({
                            'parent_id': exam_line.id,
                            'sequence': data_sequence,
                            'title_date_id': data_id.id,
                            'course_id': data_id.project_id.course_id.id,
                            'project_id': data_id.project_id.id,
                            'total_marks': data_id.total_marks,
                            'remark': data_id.remark,
                            'is_random': data_random
                        })
                        data_sequence += 1

    # 发起考试
    def btn_exam(self):
        view = self.env.ref('roke_education_manager.roke_student_allot_exam_wizard_form')
        return {
            'name': "分配考试",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.subject.student.allot.exam.wizard',
            'context': {
            },
            'target': 'new',
        }

    def btn_import_student(self):
        """
        导入学生
        :return:
        """
        if len(self) != 1:
            raise ValidationError('请选择一条考试进行导入学生')
        if self.is_main_exam:
            raise ValidationError('当前考试下有多个场次，请选择场次再进行导入学生')
        if self.state != 'draft':
            raise ValidationError('【%s】考试不是草稿状态，不能导入学生' % self.name)
        view = self.env.ref('roke_education_manager.roke_exam_import_student_wizard_form')
        return {
            'name': "导入学生",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.import.student.wizard',
            'context': {
                'default_exam_id': self.id
            },
            'target': 'new',
        }

    def btn_generate_password(self):
        """
        选中考试重新生成密码
        :return:
        """
        if not self:
            raise ValidationError('请选择考试再生成密码')
        exam_obj = self.filtered(lambda exam: exam.state != 'data_dispatch')
        if exam_obj:
            exam_list = [exam.name for exam in exam_obj]
            raise ValidationError(
                '请选择考题分配状态的考试进行生成密码，当前选择考试不是考题分配状态:\n 【%s】' % '、'.join(exam_list))
        main_exam_obj = self.filtered(lambda exam: exam.is_main_exam)
        if main_exam_obj:
            main_exam_list = [main_exam.name for main_exam in main_exam_obj]
            raise ValidationError(
                '当前选择考试下有多个场次，请选择准确场次再进行生成密码:\n 【%s】' % '、'.join(main_exam_list))
        # 处理考生密码
        student_list = []
        for res in self:
            student_list.extend([item.employee_id for item in res.exam_line_ids])
        for student in student_list:
            # 生成随机密码
            words = string.ascii_lowercase + string.ascii_uppercase + string.digits
            new_password = random.sample(words, 8)
            real_password = ''.join(new_password)
            for res in self:
                self.env['roke.base.exam.password'].create({
                    'exam_id': res.id,
                    'employee_id': student.id,
                    'current_password': real_password
                })
            student.user_id.sudo().write({'password': real_password})

    def start_exam(self):
        """
        开始考试
        选择考试开始考试
        :return:
        """
        # 判断是否选择了考试记录
        if not self:
            raise ValidationError('请选择考试再进行开始考试操作')
        exam_list = []
        exam_name_list = []
        for exam_obj in self:
            exam_list.append(exam_obj)
            if exam_obj.state != 'data_dispatch':
                exam_name_list.append(exam_obj.number)
        if exam_name_list:
            raise ValidationError(
                '当前所选考试中存在不是考题分配状态的，请选择考题分配状态的进行开始考试\n 【{exam_name}】'.format(
                    exam_name='、'.join(exam_name_list)))
        main_exam_obj = self.filtered(lambda exam: exam.is_main_exam)
        if main_exam_obj:
            main_exam_list = [main_exam.name for main_exam in main_exam_obj]
            raise ValidationError(
                '当前选择考试下有多个场次，请选择准确场次再进行开始考试:\n 【%s】' % '、'.join(main_exam_list))
        for exam_item in exam_list:
            exam_item.write({'state': 'wait_exam'})
            for exam_line in exam_item.exam_line_ids:
                # # 处理用户权限及视图样式
                # hotel_manager_group = self.env.ref('roke_hotel_manager.group_hotel_manager', raise_if_not_found=False)
                # user_group_list = exam_line.employee_id.user_id.groups_id.ids
                # if hotel_manager_group.id not in user_group_list:
                #     user_group_list.append(hotel_manager_group.id)
                #     exam_line.employee_id.user_id.sudo().write({
                #         'groups_id': [(6, 0, user_group_list)],
                #         'sidebar_type': 'invisible'
                #     })
                exam_line.write({'state': 'wait_exam'})
                exam_record = self.env['roke.subject.examination.record'].create({
                    'main_exam_id': exam_line.parent_id.id,
                    'exam_id': exam_line.id,
                    'org_id': exam_line.employee_id.org_id.id,
                    'pattern_type': exam_line.pattern_type,
                    'student_id': exam_line.employee_id.id,
                    'start_time': exam_line.start_time,
                    'end_time': exam_line.end_time,
                    'duration': exam_line.time_length,
                    'total_marks': exam_line.total_marks,
                    'can_see_score': True if exam_line.pattern_type == 'practice' else False,
                    'can_see_true_answer': True if exam_line.pattern_type == 'practice' else False
                })
                subjectivity_record = False
                # 判断是否存在主观题目
                if exam_line.line_ids.filtered(
                        lambda line_item: line_item.title_date_id.project_id.project_type == 'subjectivity'):
                    subjectivity_record = self.env['roke.subjectivity.grade'].create({
                        'main_exam_id': exam_line.parent_id.id,
                        'exam_id': exam_line.id,
                        'exam_record_id': exam_record.id,
                        'org_id': exam_line.employee_id.org_id.id,
                        'student_id': exam_line.employee_id.id,
                        'pattern_type': exam_line.pattern_type,
                    })
                for line in exam_line.line_ids:
                    # 主观评分
                    if line.project_id.project_type == 'subjectivity':
                        subjectivity_record_line = self.env['roke.subjectivity.record.line'].create({
                            'record_id': exam_record.id,
                            'course_id': line.title_date_id.project_id.course_id.id,
                            'project_id': line.title_date_id.project_id.id,
                            'title_data_id': line.title_date_id.id,
                            'content': line.title_date_id.description,
                            'true_content': line.title_date_id.content,
                            'proportion_mark': line.title_date_id.total_marks,
                        })
                        self.env['roke.subjectivity.grade.line'].create({
                            'parent_id': subjectivity_record.id if subjectivity_record else False,
                            'record_line_id': subjectivity_record_line.id,
                            'course_id': line.title_date_id.project_id.course_id.id,
                            'project_id': line.title_date_id.project_id.id,
                            'title_data_id': line.title_date_id.id,
                            'content': line.title_date_id.description,
                            'true_content': line.title_date_id.content,
                            'proportion_mark': line.title_date_id.total_marks,
                        })
                    # elif line.project_id.project_type == 'operation':
                    #     # 实操类题目信息
                    #     for title_line in line.title_date_id.line_ids:
                    #         for standard_line in title_line.line_ids:
                    #             self.env['roke.subject.examination.record.line'].create({
                    #                 'record_id': exam_record.id,
                    #                 'course_id': line.title_date_id.project_id.course_id.id,
                    #                 'project_id': line.title_date_id.project_id.id,
                    #                 'title_data_id': line.title_date_id.id,
                    #                 'title_data_line_id': title_line.id,
                    #                 'standard_line_id': standard_line.id,
                    #                 'model_id': standard_line.model_id.id,
                    #                 'field_id': standard_line.field_id.id,
                    #                 'content': standard_line.content,
                    #                 'proportion_mark': standard_line.mark,
                    #             })
                    else:
                        if line.project_id.data_type != 'gap_filling':
                            # 取正确答案
                            true_answer = line.title_date_id.objective_line_ids.filtered(lambda option: option.is_correct)
                        else:
                            # 取正确答案
                            true_answer = line.title_date_id.objective_line_ids
                        true_answer_name = '、'.join([option.name for option in true_answer])
                        self.env['roke.objective.record.line'].create({
                            'record_id': exam_record.id,
                            'course_id': line.title_date_id.project_id.course_id.id,
                            'project_id': line.title_date_id.project_id.id,
                            'title_data_id': line.title_date_id.id,
                            'true_answer': true_answer_name
                        })
            if exam_item.pattern_type == 'exam':
                # 增加定时任务
                self.env['ir.cron'].sudo().create({
                    'name': '【%s】考试到时间自动交卷' % exam_item.name,
                    'model_id': self.sudo().env["ir.model"].search([("model", "=", self._name)]).id,
                    'state': 'code',
                    'code': 'model.exam_cron_submit(%s)' % exam_item.id,
                    'user_id': self.env.ref('base.user_root').id,
                    'nextcall': exam_item.end_time,
                    'doall': True,
                    'active': True,
                    'numbercall': 1,
                })

    def download_password(self):
        if len(self) != 1:
            raise ValidationError('请选择一条考试进行下载密码')
        if self.is_main_exam:
            raise ValidationError('当前考试下有多个场次，请选择场次再进行下载密码')
        if self.state != 'data_dispatch':
            raise ValidationError('【%s】考试不是考题分配状态，不能下载密码' % self.name)
        res = self.env["roke.exam.download.student.password.wizard"].create({'file': self.generate_excel()})
        excel_url = '/web/content?model=%s&id=%s&field=file&download=true&filename=%s.xls' % (
            "roke.exam.download.student.password.wizard", res.id, "考生密码")

        value = dict(
            type='ir.actions.act_url',
            target='new',
            url=excel_url
        )
        return value

    def generate_excel(self):
        """
        生成Excel文件
        :return:
        """
        result = [['考试', '学生', '登录名', '密码', '所属组织']]
        password_obj = self.env['roke.base.exam.password'].search([('exam_id', '=', self.id)])
        for item in password_obj:
            # 获取考生所属组织
            org_name = item.employee_id.get_org_name()
            result.append(
                [item.exam_id.name, item.employee_id.name, item.employee_id.user_id.login, item.current_password,
                 org_name])
        workbook = xlwt.Workbook()
        sheet = workbook.add_sheet('Sheet', cell_overwrite_ok=True)
        for i in range(len(result)):
            for j in range(len(result[i])):
                sheet.write(i, j, result[i][j])
        buffer = BytesIO()
        workbook.save(buffer)
        data = base64.encodebytes(buffer.getvalue())
        return data

    def exam_cron_submit(self, exam_id):
        """
        时间到把考试下除暂停、等待主观评分、完成状态的都交卷
        :param exam_id: 考试ID，roke.base.exam
        :return:
        """
        exam_obj = self.browse(exam_id)
        for line in exam_obj.exam_line_ids:
            if line.end_time == exam_obj.end_time and line.state not in ['exam_suspend', 'wait_subjectivity', 'done']:
                # 判断主观评分是否完成
                subjectivity_grade_id = self.env['roke.subjectivity.grade'].search(
                    [('main_exam_id', '=', exam_obj.id), ('exam_id', '=', line.id)])
                write_dict = {
                    'real_end_time': datetime.datetime.now(),
                }
                if line.real_start_time:
                    write_dict.update({'real_time_length': (datetime.datetime.now() - line.real_start_time).seconds / 60})
                if subjectivity_grade_id.state == 'wait_confirm':
                    write_dict.update({'state': 'wait_subjectivity'})
                else:
                    write_dict.update({'state': 'done'})
                line.write(write_dict)

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=80, order=None):
        domain = domain or []
        for item in domain:
            if len(item) == 3:
                if item[1] == 'child_of':
                    item[1] = '='
        result = super(RokeBaseExam, self).search_read(domain=domain, fields=fields, offset=offset, limit=limit,
                                                       order=order)
        return result

