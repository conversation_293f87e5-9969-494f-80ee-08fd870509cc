# -*- coding: utf-8 -*-
"""
Description:
学校组织管理
"""
from odoo import models, fields, api


class RokeBaseOrg(models.Model):
    _name = "roke.base.org"
    _inherit = ['mail.thread']
    _description = "学校组织管理"
    _rec_name = "class_name"

    class_name = fields.Char(string="组织名称", required=True, index=True, tracking=True)
    parent_id = fields.Many2one('roke.base.org', string='上级组织', tracking=True)
    child_ids = fields.One2many('roke.base.org', 'parent_id', string='下级组织')
    student_ids = fields.One2many('roke.employee', 'org_id', string='学生明细')
    student_count = fields.Integer(string='学生人数', compute='_compute_student_count')
    describe = fields.Text(string='描述')
    active = fields.Boolean(string='有效', default=True)

    def get_org_name(self, org_name):
        """
        递归获取组织名
        :param org_name: 当前拼接的组织名
        :return:
        """
        item = self
        while item.parent_id:
            org_name = item.parent_id.class_name + '_' + org_name
            item = item.parent_id
        return org_name

    @api.depends('student_ids')
    def _compute_student_count(self):
        """
        计算学生人数
        :return:
        """
        for res in self:
            res.student_count = len(res.student_ids)

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=80, order=None):
        domain = domain or []
        for item in domain:
            if len(item) == 3:
                if item[1] == 'child_of':
                    item[1] = '='
        result = super(RokeBaseOrg, self).search_read(domain=domain, fields=fields, offset=offset, limit=limit,
                                                      order=order)
        return result
