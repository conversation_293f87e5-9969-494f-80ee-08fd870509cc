# -*- coding: utf-8 -*-
"""
Description:
教师档案
"""
import re

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeBaseTeacher(models.Model):
    _name = "roke.base.teacher"
    _inherit = ['mail.thread']
    _description = "教师档案"
    _rec_name = "name"

    number = fields.Char(string="教师账号", copy=False, required=True, index=True, tracking=True)
    name = fields.Char(string="教师名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    team_ids = fields.Many2many('roke.base.org', string='对应班级')
    phone = fields.Char(string='手机号')
    remark = fields.Text(string='备注')
    user_id = fields.Many2one('res.users', string='对应系统用户')

    @api.onchange("phone")
    def _onchange_phone(self):
        """
        校验电话格式
        :return:
        """
        if self.phone and not re.match(r"^(1[3|4|5|6|7|8|9])\d{9}$|^0\d{2,3}-?\d{7,8}$", self.phone):
            return {"warning": {
                "title": "提醒", "message": "电话格式错误"
            }, "value": {
                "phone": ""
            }}

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'
