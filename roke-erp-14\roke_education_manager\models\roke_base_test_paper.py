# -*- coding: utf-8 -*-
"""
Description:
试卷
"""
from odoo import models, fields, api


class RokeBaseTestPaper(models.Model):
    _name = "roke.base.test.paper"
    _inherit = ['mail.thread']
    _description = "试卷管理"
    _rec_name = "display_name"

    display_name = fields.Char(string="显示名称", compute='_compute_display_name')
    number = fields.Char(string="试卷编号", copy=False, default="保存后自动生成编号", required=True, index=True,
                         tracking=True)
    name = fields.Char(string="试卷名称", required=True, index=True, tracking=True)
    course_id = fields.Many2one('roke.subject.course', string='对应科目')
    title_ids = fields.Many2many('roke.subject.title.data', string='对应题目')
    total_mark = fields.Float(string='总分数', compute='_compute_total_mark')
    is_random = fields.<PERSON><PERSON><PERSON>(string='客观题选项随机', default=False)
    remark = fields.Text(string='备注')
    title_detail = fields.Char(string='考题明细', compute='_compute_total_mark', store=True)

    @api.depends('name', 'total_mark')
    def _compute_display_name(self):
        """
        计算显示名称
        :return:
        """
        for res in self:
            if res.name:
                res.display_name = res.name + '(' + str(res.total_mark) + ')'

    @api.depends('title_ids')
    def _compute_total_mark(self):
        """
        计算总分数
        各项目题目数
        :return:
        """
        for res in self:
            res.total_mark = sum([title_data.total_marks for title_data in res.title_ids])
            # 取试卷下所有题目项目
            project_list = res.title_ids.mapped("project_id")
            title_detail = ''
            for project in project_list:
                title_ids = res.title_ids.filtered(lambda title_id: title_id.project_id.id == project.id)
                if not title_detail:
                    title_detail += project.name + '：' + str(len(title_ids))
                else:
                    title_detail += '\n' + project.name + '：' + str(len(title_ids))
            res.title_detail = title_detail

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.base.test.paper.code')
        return super(RokeBaseTestPaper, self).create(vals)
