# -*- coding: utf-8 -*-
"""
Description:
4.2.3.学生档案
使用人员信息表，roke.employee
去掉无用信息，如ERP_ID等，增加类型区分，是学生还是前厅班组人员
"""
from odoo import models, fields, api
from odoo.exceptions import UserError


class RokeEmployee(models.Model):
    _inherit = "roke.employee"

    name = fields.Char(string="姓名", required=True, index=True, tracking=True)
    student_type = fields.Selection([('student', '学生'), ('class_people', '前厅班组人员')], string='类型', default='student')
    org_id = fields.Many2one('roke.base.org', string="所属组织", tracking=True)
    gender = fields.Selection([('男', '男'), ('女', '女')], string='性别', default='男')
    employee_email = fields.Char(string='电子邮箱')
    employee_team = fields.Char(string='所属团队')

    def get_org_name(self):
        """
        递归获取组织名
        :return:
        """
        if self.org_id:
            return self.org_id.get_org_name(self.org_id.class_name)

    def excel_import_student(self):
        """
        excel导入学生
        :return:
        """
        view = self.env.ref('roke_education_manager.roke_excel_import_student_wizard_form')
        return {
            'name': "导入学生",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.excel.import.student.wizard',
            'context': {
            },
            'target': 'new',
        }
