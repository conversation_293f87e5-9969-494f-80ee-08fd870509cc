# -*- coding: utf-8 -*-
"""
Description:
考试科目设置
"""
from odoo import models, fields, api
from odoo.exceptions import UserError


class RokeSubjectCourse(models.Model):
    _name = "roke.subject.course"
    _inherit = ['mail.thread']
    _description = "考试科目设置"
    _rec_name = "name"

    number = fields.Char(string="编号", copy=False, default="保存后自动生成编号", required=True, index=True, tracking=True)
    name = fields.Char(string="科目名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    org_ids = fields.Many2many('roke.base.org', string='对应班级')
    remark = fields.Text(string='备注')

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.course.code')
        return super(RokeSubjectCourse, self).create(vals)

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'

    @api.constrains('name')
    def _check_name_uniq(self):
        """
        校验名称不能重复
        :return:
        """
        for rec in self:
            if self.sudo().search_count([('name', '=', rec.name)]) > 1:
                raise UserError("考试科目名称必须唯一：名称【%s】重复" % rec.name)
