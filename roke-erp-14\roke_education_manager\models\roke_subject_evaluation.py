# -*- coding: utf-8 -*-
"""
Description:
评分规则
"""
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectEvaluation(models.Model):
    _name = "roke.subject.evaluation"
    _inherit = ['mail.thread']
    _description = "评分规则"
    _rec_name = "name"

    number = fields.Char(string="编号", copy=False, default="保存后自动生成编号", required=True, index=True, tracking=True)
    name = fields.Char(string="评分规则名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    course_id = fields.Many2one('roke.subject.course', string='对应科目')
    marks_type_id = fields.Many2one('roke.subject.mark.type', string='评分方式')
    remark = fields.Text(string='备注')

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.evaluation.code')
        return super(RokeSubjectEvaluation, self).create(vals)

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'
