# -*- coding: utf-8 -*-
"""
Description:
考试记录
"""
from odoo.exceptions import ValidationError
from odoo import models, fields, api


class RokeSubjectExaminationRecord(models.Model):
    _name = "roke.subject.examination.record"
    _inherit = ['mail.thread']
    _description = "考试记录"
    _rec_name = "student_id"

    main_exam_id = fields.Many2one('roke.base.exam', string='考试')
    exam_id = fields.Many2one('roke.subject.student.exam', string='考试', ondelete='cascade')
    org_id = fields.Many2one('roke.base.org', string='组织')
    student_id = fields.Many2one('roke.employee', string='学生', required=True)
    start_time = fields.Datetime(string='开始时间')
    end_time = fields.Datetime(string='结束时间', tracking=True)
    duration = fields.Integer(string='时长：分钟', tracking=True)
    objectivity_score = fields.Float(string='实操得分', digits=(8, 2), compute='_compute_objectivity_score')
    subjectivity_score = fields.Float(string='主观得分', digits=(8, 2), compute='_compute_subjectivity_score')
    objective_score = fields.Float(string='客观得分', digits=(8, 2), compute='_compute_objective_score')
    total_marks = fields.Float(string='总分数', digits=(8, 2))
    total_score = fields.Float(string='总得分', digits=(8, 2), compute='_compute_total_marks', store=True)
    state = fields.Selection([('wait_confirm', '待确认'), ('done', '完成'), ('cancel', '作废')], string='状态',
                             default='wait_confirm', copy=False)
    pattern_type = fields.Selection([('practice', '练习模式'), ('exam', '考试模式')], string='模式类型', default='exam')
    line_ids = fields.One2many('roke.subject.examination.record.line', 'record_id', string='实操评分')
    line1_ids = fields.One2many('roke.subjectivity.record.line', 'record_id', string='主观评分', copy=True)
    objective_line_ids = fields.One2many('roke.objective.record.line', 'record_id', string='客观评分', copy=True)
    remark = fields.Text(string='备注')
    active = fields.Boolean(string='已存档', default=True, tracking=True, copy=False)
    can_see_score = fields.Boolean(string='可查看成绩', default=False)
    can_see_true_answer = fields.Boolean(string='可查看答案', default=False)
    employee_team = fields.Char(string='所属团队', related='student_id.employee_team')
    checkbox_score_type = fields.Selection([('give', '多选题半对给分'), ('not_give', '多选题半对不给分')],
                                           string='多选题给分模式', related='main_exam_id.checkbox_score_type')

    def replenish_duration(self):
        """
        补时
        :return:
        """
        view = self.env.ref('roke_education_manager.roke_exam_replenish_duration_wizard_form')
        return {
            'name': "考试补时",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.replenish.duration.wizard',
            'context': {
                'default_exam_record_id': self.id
            },
            'target': 'new',
        }

    @api.depends('line_ids.mark')
    def _compute_objectivity_score(self):
        """
        计算客观得分
        :return:
        """
        for res in self:
            res.objectivity_score = sum(line.mark for line in res.line_ids)

    @api.depends('line1_ids.mark')
    def _compute_subjectivity_score(self):
        """
        计算主观得分
        :return:
        """
        for res in self:
            res.subjectivity_score = sum(line.mark for line in res.line1_ids)

    @api.depends('objective_line_ids.mark')
    def _compute_objective_score(self):
        """
        计算主观得分
        :return:
        """
        for res in self:
            res.objective_score = sum(line.mark for line in res.objective_line_ids)

    @api.depends('subjectivity_score', 'objectivity_score', 'objective_score')
    def _compute_total_marks(self):
        """
        计算总得分
        :return:
        """
        for res in self:
            res.total_score = res.objectivity_score + res.subjectivity_score + res.objective_score


class RokeSubjectExaminationRecordLine(models.Model):
    _name = "roke.subject.examination.record.line"
    _description = "考试记录明细"

    record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    course_id = fields.Many2one('roke.subject.course', string='科目')
    project_id = fields.Many2one('roke.subject.project', string='项目')
    title_data_id = fields.Many2one('roke.subject.title.data', string='题库')
    title_data_line_id = fields.Many2one('roke.subject.title.data.line', string='题目')
    standard_line_id = fields.Many2one('roke.subject.title.data.standard.line', string='题目')
    model_id = fields.Many2one('ir.model', string="当前项对应模型")
    field_id = fields.Many2one('ir.model.fields', string="当前项对应字段")
    field_name =  fields.Char(string="字段名称",related='field_id.field_description')
    content = fields.Char(string="内容")
    real_content = fields.Char(string="实际录入结果")
    proportion_mark = fields.Float(string='所占分数', digits=(8, 2))
    mark = fields.Float(string='得分', digits=(8, 2))
    state = fields.Selection([('wait_confirm', '待确认'), ('confirm', '已确认')], string='状态', default='wait_confirm')


class RokeSubjectivityRecordLine(models.Model):
    _name = "roke.subjectivity.record.line"
    _description = "主观评分明细"

    record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    course_id = fields.Many2one('roke.subject.course', string='科目')
    project_id = fields.Many2one('roke.subject.project', string='项目')
    title_data_id = fields.Many2one('roke.subject.title.data', string='题目')
    content = fields.Char(string="题目信息")
    true_content = fields.Char(string="答案")
    real_content = fields.Char(string="考生填写内容", copy=False)
    proportion_mark = fields.Float(string='题目分数', digits=(8, 2))
    mark = fields.Float(string='得分', digits=(8, 2), copy=False)
    state = fields.Selection([('wait_confirm', '待确认'), ('confirm', '已确认')], string='状态', default='wait_confirm')


class RokeObjectiveRecordLine(models.Model):
    _name = "roke.objective.record.line"
    _description = "客观题评分明细"

    record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    course_id = fields.Many2one('roke.subject.course', string='科目')
    project_id = fields.Many2one('roke.subject.project', string='项目')
    title_data_id = fields.Many2one('roke.subject.title.data', string='题库')
    title_description = fields.Text(related='title_data_id.description', string='题目详情')
    true_answer = fields.Char(string="正确答案")
    real_answer = fields.Char(string="考生选择", copy=False)
    proportion_mark = fields.Float(related='title_data_id.total_marks', string='所占分数', digits=(8, 2))
    mark = fields.Float(string='得分', digits=(8, 2), copy=False)
