# -*- coding: utf-8 -*-
"""
Description:
评分方式
"""
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectMarkType(models.Model):
    _name = "roke.subject.mark.type"
    _inherit = ['mail.thread']
    _description = "评分方式"
    _rec_name = "name"

    number = fields.Char(string="编号", copy=False, default="保存后自动生成编号", required=True, index=True, tracking=True)
    name = fields.Char(string="评分方式名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    rules = fields.Selection([('average', '平均'), ('accumulation', '累加'), ('sole', '唯一'), ('manual', '手动评分')],
                             string='评分方式', default='average')
    remark = fields.Text(string='备注')

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.mark.type.code')
        return super(RokeSubjectMarkType, self).create(vals)

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'
