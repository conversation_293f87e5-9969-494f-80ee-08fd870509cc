# -*- coding: utf-8 -*-
"""
Description:
科目考试项目设置
"""
from odoo import models, fields, api
from odoo.exceptions import UserError


class RokeSubjectProject(models.Model):
    _name = "roke.subject.project"
    _inherit = ['mail.thread']
    _description = "科目考试项目设置"
    _rec_name = "name"

    number = fields.Char(string="编号", copy=False, default="保存后自动生成编号", required=True, index=True, tracking=True)
    name = fields.Char(string="考试项目名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    # project_type = fields.Selection([('subjectivity', '主观类'), ('objective', '客观类'), ('operation', '实操类')],
    project_type = fields.Selection([('subjectivity', '主观类'), ('objective', '客观类')],
                                    string='项目类别', default='subjectivity')
    data_type = fields.Selection([('radio', '单选'), ('checkbox', '多选'), ('judge', '判断'), ('gap_filling', '填空')],
                                 string='客观题类型', default='radio')
    standard_score = fields.Float(string='题目标准分数', digits=(8, 2))
    course_id = fields.Many2one('roke.subject.course', string='对应科目')
    evaluation_id = fields.Many2one('roke.subject.evaluation', string='评分规则')
    remark = fields.Text(string='备注')
    parent_id = fields.Many2one('roke.subject.project', string='上级项目')

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.project.code')
        return super(RokeSubjectProject, self).create(vals)

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'

    @api.constrains('name')
    def _check_name_uniq(self):
        """
        校验名称不能重复
        :return:
        """
        for rec in self:
            if self.sudo().search_count([('name', '=', rec.name)]) > 1:
                raise UserError("考试项目名称必须唯一：名称【%s】重复" % rec.name)
