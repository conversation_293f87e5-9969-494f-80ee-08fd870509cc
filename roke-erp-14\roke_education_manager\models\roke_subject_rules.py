# -*- coding: utf-8 -*-
"""
Description:
抽题规则
"""
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectRules(models.Model):
    _name = "roke.subject.rules"
    _inherit = ['mail.thread']
    _description = "抽题规则"
    _rec_name = "display_name"

    display_name = fields.Char(string='显示名称', compute='_compute_display_name')
    number = fields.Char(string="编号", copy=False, default="保存后自动生成编号", required=True, index=True, tracking=True)
    name = fields.Char(string="规则名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    start_time = fields.Datetime(string='有效期开始')
    end_time = fields.Datetime(string='有效期结束')
    remark = fields.Text(string='备注')
    line_ids = fields.One2many('roke.subject.rules.line', 'parent_id', string='抽题规则明细')
    is_random = fields.Boolean(string='客观题选项随机', default=False)
    title_detail = fields.Char(string='题目明细', compute='_compute_title_detail')
    sum_mark = fields.Float(string='总分数', compute='_compute_sum_mark')

    @api.depends('line_ids.project_mark')
    def _compute_sum_mark(self):
        """
        计算抽题规则总分数
        :return:
        """
        for res in self:
            res.sum_mark = sum(item.project_mark for item in res.line_ids)

    @api.depends('name', 'line_ids.project_mark')
    def _compute_display_name(self):
        """
        计算抽题规则显示名称
        :return:
        """
        for res in self:
            res.display_name = res.name + '(' + str(int(res.sum_mark)) + ')'

    @api.depends('line_ids.project_id', 'line_ids.title_count')
    def _compute_title_detail(self):
        """
        拼接抽题规则下各类型题目数量
        :return:
        """
        for res in self:
            title_detail = ''
            for line in res.line_ids:
                if not title_detail:
                    title_detail += line.project_id.name + '：' + str(line.title_count)
                else:
                    title_detail += '\n' + line.project_id.name + '：' + str(line.title_count)
            res.title_detail = title_detail

    @api.model
    def create(self, vals):
        vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.rules.code')
        return super(RokeSubjectRules, self).create(vals)

    @api.onchange('start_time', 'end_time')
    def _onchange_start_end_time(self):
        if self.start_time and self.end_time:
            if self.start_time >= self.end_time:
                raise ValidationError('有效期结束时间必须在开始时间之前')

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'


class RokeSubjectRulesLine(models.Model):
    _name = "roke.subject.rules.line"
    _description = "抽题规则明细"

    parent_id = fields.Many2one('roke.subject.rules', string='抽题规则')
    project_id = fields.Many2one('roke.subject.project', string="项目")
    project_type = fields.Selection(related='project_id.project_type', string="项目类型")
    standard_score = fields.Float(string='项目标准分数', related='project_id.standard_score')
    title_count = fields.Integer(string='题目数量', default=1)
    project_mark = fields.Float(string='项目总分数', compute="_compute_project_mark")
    remark = fields.Text(string='备注')

    @api.depends('title_count', 'standard_score')
    def _compute_project_mark(self):
        for res in self:
            res.project_mark = res.standard_score * res.title_count

    @api.onchange('project_id', 'title_count')
    def _onchange_project_title_count(self):
        """
        判断题目数量和题库
        :return:
        """
        if self.project_id:
            if self.title_count <= 0:
                raise ValidationError('题目数量需大于0')
            title_count = self.env['roke.subject.title.data'].search_count([('project_id', '=', self.project_id.id)])
            if self.title_count > title_count:
                raise ValidationError('当前题库中多选题数量为%s, 不足%s道，请确认' % (title_count, self.title_count))

    def write(self, vals):
        """重写write方法，确保一个抽提规则下一个项目只能出现一次"""
        res = super(RokeSubjectRulesLine, self).write(vals)
        if self.parent_id and self.project_id:
            current_project_count = self.parent_id.line_ids.filtered(
                lambda line: line.project_id.id == self.project_id.id)
            if len(current_project_count) > 1:
                raise ValidationError('当前抽题规则下【%s】考试项目出现了多次' % self.project_id.name)
        return res

    @api.model_create_multi
    def create(self, vals):
        """
        重写创建方法，确保一个抽提规则下一个项目只能出现一次
        :param vals:
        :return:
        """
        res = super(RokeSubjectRulesLine, self).create(vals)
        for item in res:
            if item.parent_id and item.project_id:
                current_project_count = item.parent_id.line_ids.filtered(
                    lambda line: line.project_id.id == item.project_id.id)
                if len(current_project_count) > 1:
                    raise ValidationError('当前抽题规则下【%s】考试项目出现了多次' % item.project_id.name)
        return res
