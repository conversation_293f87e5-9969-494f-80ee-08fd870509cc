# -*- coding: utf-8 -*-
"""
Description:
题库设置
"""
import datetime
import random

from odoo import models, fields, api
from odoo.exceptions import ValidationError
import os
from jinja2 import FileSystemLoader, Environment

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static/html")
env = Environment(loader=templateloader)


class RokeSubjectStudentExam(models.Model):
    _name = "roke.subject.student.exam"
    _inherit = ['mail.thread']
    _description = "学生考试"
    _rec_name = "parent_id"

    employee_id = fields.Many2one('roke.employee', string="学生", required=True)
    dispatch_type = fields.Selection([('same', '考题一致'), ('different', '随机分配')], string='考题分配方式',
                                     default='same')
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    pattern_type = fields.Selection([('practice', '练习模式'), ('exam', '考试模式')], string='模式类型', default='exam')
    # 草稿、密码生成、考试中、完成
    state = fields.Selection(
        [('draft', '草稿'), ('data_dispatch', '考题分配'), ('wait_exam', '等待考试'), ('exam_taking', '考试中'),
         ('exam_suspend', '考试暂停'), ('wait_subjectivity', '等待主观评分'), ('done', '已交卷'), ('cancel', '作废')],
        string='状态', default='draft', tracking=True)
    org_id = fields.Many2one('roke.base.org', string='所属组织')
    rule_id = fields.Many2one('roke.subject.rules', string='抽题规则')
    course_id = fields.Many2one('roke.subject.course', string="科目")
    start_time = fields.Datetime(string="开始时间")
    end_time = fields.Datetime(string="结束时间")
    time_length = fields.Integer(string="时长(分钟)")
    total_marks = fields.Float(string='总分数', digits=(8, 2))
    line_ids = fields.One2many('roke.subject.student.exam.line', 'parent_id', string='考试内容')
    person_ids = fields.One2many('roke.subject.student.exam.person', 'exam_id', string='考试学生')
    remark = fields.Text(string='备注')
    test_paper_id = fields.Many2one('roke.base.test.paper', string="试题")
    parent_id = fields.Many2one('roke.base.exam', string="考试", ondelete='cascade')
    is_compel_over_exam = fields.Boolean(string='是否强制交卷', default=False)
    suspend_trait = fields.Boolean(string='暂停恢复标识', default=False)
    delayed_trait = fields.Boolean(string='考试延时标识', default=False)
    delayed_count = fields.Integer(string='延时时间')
    is_can_see_score = fields.Boolean(string='是否已推送成绩', default=False)
    is_can_see_true_answer = fields.Boolean(string='是否可查看答案解析', default=False)
    real_start_time = fields.Datetime(string="实际开始时间")
    real_end_time = fields.Datetime(string="实际结束时间")
    real_time_length = fields.Integer(string="实际时长(分钟)")
    delayed_length = fields.Integer(string='延时总时长', default=0, tracking=True)
    suspend_time = fields.Datetime(string='暂停时间')
    suspend_length = fields.Integer(string='暂停总时长', default=0, tracking=True)
    employee_team = fields.Char(string='所属团队', related='employee_id.employee_team')
    checkbox_score_type = fields.Selection([('give', '多选题半对给分'), ('not_give', '多选题半对不给分')],
                                           string='多选题给分模式', related='parent_id.checkbox_score_type')

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'

    # 重新考试
    def restart_exam(self):
        """
        重新考试
        选择考试下学生进行重新考试
        :return: 瞬态视图
        """
        # 判断是否选择了一个考试
        exam_id = self._context.get('active_ids', False)
        if exam_id:
            if len(exam_id) != 1:
                raise ValidationError('重新考试只能选择单条考试，请确认')
        else:
            raise ValidationError('请选择考试后再进行重新考试')
        exam_obj = self.search([('id', '=', exam_id)])
        if exam_obj.state != 'exam_taking':
            raise ValidationError('请选择考试中的考试进行重新考试')
        return {
            'name': '重新考试',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.subject.restart.exam.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_exam_ids': exam_obj.id,
                'default_time_length': exam_obj.time_length,
            }
        }

    # 已完成的考试重置为进行中
    def reset_state(self):
        """
        已完成的考试重置为进行中
        :return:
        """
        view = self.env.ref('roke_education_manager.roke_exam_reset_state_wizard_form')
        return {
            'name': "重置为进行中",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.reset.state.wizard',
            'context': {
                'default_exam_id': self.id
            },
            'target': 'new',
        }

    def push_grade(self):
        """
        推送成绩
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再进行推送成绩')
        not_done_list = []
        for exam_obj in self.filtered(lambda item: item.state not in ['done', 'wait_subjectivity']):
            not_done_list.append(exam_obj.parent_id.name + '-' + exam_obj.employee_id.name)
        if not_done_list:
            raise ValidationError(
                '当前所选考试中存在不是已交卷和等待主观评分状态的，请选择已交卷和等待主观评分状态的进行推送成绩\n 【{exam_name}】'.format(
                    exam_name='、'.join(not_done_list)))
        is_push_list = []
        for exam_obj in self.filtered(lambda item: item.is_can_see_score):
            is_push_list.append(exam_obj.parent_id.name + '-' + exam_obj.employee_id.name)
        if is_push_list:
            raise ValidationError(
                '下列考试已推送成绩，请选择未推送成绩的考试进行推送成绩\n 【{exam_name}】'.format(
                    exam_name='、'.join(is_push_list)))
        # 修改对应考试记录下的属性
        self.env['roke.subject.examination.record'].search([('exam_id', 'in', self.ids)]).write(
                {'can_see_score': True})
        self.is_can_see_score = True

    def answer_analysis(self):
        """
        答案解析
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再推送答案解析')
        is_push_list = []
        for exam_obj in self.filtered(lambda item: item.is_can_see_true_answer):
            is_push_list.append(exam_obj.parent_id.name + '-' + exam_obj.employee_id.name)
        if is_push_list:
            raise ValidationError(
                '下列考试已推送答案解析，请选择未推送成绩的考试进行推送答案解析\n 【{exam_name}】'.format(
                    exam_name='、'.join(is_push_list)))
        # 修改对应考试记录下的属性
        self.env['roke.subject.examination.record'].search([('exam_id', 'in', self.ids)]).write(
                {'can_see_true_answer': True})
        self.is_can_see_true_answer = True

    def exam_suspend(self):
        """
        考试暂停
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再进行考试暂停')
        not_ongoing_list = []
        for item in self:
            if item.state != 'exam_taking':
                not_ongoing_list.append(item.parent_id.name + '-' + item.employee_id.name)
        if not_ongoing_list:
            raise ValidationError(
                '下列考试不是暂停状态，请选择进行中状态的考试进行考试暂停\n 【{exam_name}】'.format(
                    exam_name='、'.join(not_ongoing_list)))
        view = self.env.ref('roke_education_manager.roke_exam_suspend_wizard_form')
        return {
            'name': "考试暂停",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.suspend.wizard',
            'context': {
                'default_suspend_type': 'suspend',
                'default_exam_ids': [(6, 0, self.ids)]
            },
            'target': 'new',
        }

    def exam_delayed(self):
        """
        考试延时
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再进行考试补时')
        not_ongoing_list = []
        for item in self:
            if item.state != 'exam_taking':
                not_ongoing_list.append(item.parent_id.name + '-' + item.employee_id.name)
        if not_ongoing_list:
            raise ValidationError(
                '下列考试不是进行中状态，请选择进行中状态的考试进行考试延时\n 【{exam_name}】'.format(
                    exam_name='、'.join(not_ongoing_list)))
        practice_list = []
        for item in self:
            if item.pattern_type == 'practice':
                not_ongoing_list.append(item.parent_id.name + '-' + item.employee_id.name)
        if practice_list:
            raise ValidationError(
                '下列考试为练习模式，请选择考试模式的进行考试延时\n 【{exam_name}】'.format(
                    exam_name='、'.join(practice_list)))
        view = self.env.ref('roke_education_manager.roke_exam_suspend_wizard_form')
        return {
            'name': "考试延时",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.suspend.wizard',
            'context': {
                'default_suspend_type': 'delayed',
                'default_exam_ids': self.ids
            },
            'target': 'new',
        }

    def exam_continue(self):
        """
        继续考试
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再进行继续考试')
        not_suspend_list = []
        for item in self:
            if item.state != 'exam_suspend':
                not_suspend_list.append(item.parent_id.name + '-' + item.employee_id.name)
        if not_suspend_list:
            raise ValidationError(
                '下列考试不是暂停状态，请选择暂停状态的考试进行继续考试\n 【{exam_name}】'.format(
                    exam_name='、'.join(not_suspend_list)))
        remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
            "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '考试恢复'
        for exam_line in self:
            if exam_line.remark:
                new_remark = exam_line.remark + '\n' + remark
            else:
                new_remark = remark
            # 计算暂停时长，自动补充
            current_length = (datetime.datetime.now() - exam_line.suspend_time).seconds / 60
            exam_line.write({
                'suspend_trait': True,
                'suspend_length': exam_line.suspend_length + current_length,
                'suspend_time': False,
                'state': 'exam_taking',
                'remark': new_remark
            })

    def compel_over_exam(self):
        """
        强制交卷
        :return:
        """
        if not self:
            raise ValidationError('请先选择考试再进行强制交卷')
        not_ongoing_list = []
        for item in self:
            if item.state != 'exam_taking':
                not_ongoing_list.append(item.parent_id.name + '-' + item.employee_id.name)
        if not_ongoing_list:
            raise ValidationError(
                '下列考试不是进行中状态，请选择进行中状态的考试进行强制交卷\n 【{exam_name}】'.format(
                    exam_name='、'.join(not_ongoing_list)))
        view = self.env.ref('roke_education_manager.roke_exam_suspend_wizard_form')
        return {
            'name': "强制交卷",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.exam.suspend.wizard',
            'context': {
                'default_suspend_type': 'over_exam',
                'default_exam_ids': self.ids
            },
            'target': 'new',
        }
        # # 将考试及考试下不是已交卷状态的学生考试明细强制交卷标识置为True
        # remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
        #     "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '强制交卷'
        # for exam_line in self.exam_line_ids.filtered(lambda item: item.state != 'done'):
        #     if exam_line.remark:
        #         new_remark = exam_line.remark + '\n' + remark
        #     else:
        #         new_remark = remark
        #     exam_line.write({
        #         'remark': new_remark,
        #         'is_compel_over_exam': True
        #     })
        # if self.remark:
        #     self.write({
        #         'remark': self.remark + '\n' + remark,
        #         'is_compel_over_exam': True
        #     })
        # else:
        #     self.write({
        #         'remark': remark,
        #         'is_compel_over_exam': True
        #     })

    def write(self, vals):
        """
        继承write方法，在考试完成时判断是否需要将主考试完成
        :param vals:
        :return:
        """
        res = super().write(vals)
        if vals.get('state', '') and vals.get('state', '') == 'done':
            # 判断主考试下考试是否都已完成
            if all(item.state == 'done' for item in self.parent_id.exam_line_ids):
                self.parent_id.state = 'done'
        return res


class RokeSubjectStudentExamLine(models.Model):
    _name = "roke.subject.student.exam.line"
    _description = "考试内容"
    _order = 'sequence'

    sequence = fields.Integer(string="序号")
    title_date_id = fields.Many2one('roke.subject.title.data', string="考试题目")
    course_id = fields.Many2one('roke.subject.course', string="所属科目")
    project_id = fields.Many2one('roke.subject.project', string="考试项目")
    title_description = fields.Text(related='title_date_id.description', string="题目内容")
    total_marks = fields.Float(string='总分数', digits=(8, 2))
    parent_id = fields.Many2one('roke.subject.student.exam', string='考试内容')
    is_random = fields.Boolean(string='选项随机', default=False)
    remark = fields.Text(string='备注')

    @api.onchange('title_date_id')
    def _on_change_title_date_id(self):
        if self.title_date_id:
            self.project_id = self.title_date_id.project_id
            self.course_id = self.title_date_id.project_id.course_id
            self.total_marks = self.title_date_id.total_marks


class RokeSubjectStudentExamPerson(models.Model):
    _name = "roke.subject.student.exam.person"
    _description = "考试学生"
    _order = 'sequence'

    exam_id = fields.Many2one('roke.subject.student.exam', string='考试', ondelete='cascade')
    sequence = fields.Integer(string="序号")
    employee_id = fields.Many2one('roke.employee', string="学生", required=True)
    state = fields.Selection([('wait', '等待考试'), ('ongoing', '正在考试'), ('done', '考试完成'), ('cancel', '作废')],
                             string='状态', default='wait')
    remark = fields.Text(string='备注')
