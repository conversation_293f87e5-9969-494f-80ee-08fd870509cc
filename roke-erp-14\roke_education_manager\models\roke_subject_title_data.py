# -*- coding: utf-8 -*-
"""
Description:
题库设置
"""
from odoo import models, fields, api
from odoo.exceptions import UserError

PROJECT_TYPE_DICT = {
    'subjectivity': 'ZG',
    'operation': 'SC',
    'radio': 'DX',
    'judge': 'PD',
    'checkbox': 'MX',
    'gap_filling': 'GF'
}


class RokeSubjectTitleData(models.Model):
    _name = "roke.subject.title.data"
    _inherit = ['mail.thread']
    _description = "题库管理"
    _rec_name = "number"

    number = fields.Char(string="编号", copy=False, required=True, index=True, tracking=True,
                         default=lambda self: self.env['ir.sequence'].next_by_code('roke.subject.title.data.code'))
    # name = fields.Char(string="题目名称", required=True, index=True, tracking=True)
    forbidden_state = fields.Selection([('normal', '正常'), ('forbidden', '禁用')], string='状态', default='normal')
    project_id = fields.Many2one('roke.subject.project', string='对应项目')
    project_type = fields.Selection(related='project_id.project_type', string='项目类型')
    total_marks = fields.Float(string='分数', related='project_id.standard_score')
    line_ids = fields.One2many('roke.subject.title.data.line', 'parent_id', string='题目详情')
    objective_line_ids = fields.One2many('roke.subject.objective.title.data.line', 'parent_id', string='客观题选项明细')
    description = fields.Text(string="题目信息")
    content = fields.Text(string="主观题答案")
    remark = fields.Text(string='试题解析')
    data_type = fields.Selection([('radio', '单选'), ('checkbox', '多选'), ('judge', '判断'), ('gap_filling', '填空')],
                                 string='客观题类型', default='radio')
    subject_images_ids = fields.One2many('roke.subject.title.data.images', 'parent_id', string='题目图片列表')

    def _default_teacher(self):
        return self.env['roke.base.teacher'].search([("user_id", "=", self.env.user.id)], limit=1)

    teacher_ids = fields.Many2many("roke.base.teacher", string="教师", default=_default_teacher)
    user_ids = fields.Many2many("res.users", string="用户")

    def excel_import_subject_title(self):
        """
        excel导入题库
        :return:
        """
        view = self.env.ref('roke_education_manager.roke_excel_import_subject_title_wizard_form')
        return {
            'name': "导入题库",
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'res_model': 'roke.excel.import.subject.title.wizard',
            'context': {
            },
            'target': 'new',
        }

    @api.model
    def create(self, vals):
        res = super(RokeSubjectTitleData, self).create(vals)
        userids = []
        for teacher in res.teacher_ids:
            if teacher.user_id.id:
                userids.append(teacher.user_id.id)
        res.user_ids = [(6, 0, userids)]
        return res

    def write(self, vals):
        res = super(RokeSubjectTitleData, self).write(vals)
        if vals.__contains__("teacher_ids"):
            for rel in self:
                userids = []
                for teacher in rel.teacher_ids:
                    if teacher.user_id.id:
                        userids.append(teacher.user_id.id)
                rel.user_ids = [(6, 0, userids)]
        return res

    @api.onchange('project_id', 'data_type')
    def _onchange_project_data_type(self):
        if self.project_id:
            self.data_type = self.project_id.data_type
            if self.number:
                if self.project_id.project_type == 'objective':
                    number_prefix = PROJECT_TYPE_DICT[self.data_type]
                else:
                    number_prefix = PROJECT_TYPE_DICT[self.project_id.project_type]
                self.number = number_prefix + self.number[2:]

    # def check_correct_option_count(self):
    #     """
    #     校验客观题正确选项数量
    #     :return: True : 符合标准
    #              False：不符合标准
    #     """
    #     flag_dict = {'flag': True, 'info': ''}
    #     if self.project_type == 'objective':  # 客观题
    #         if self.data_type == 'radio':
    #             # 单选：有且只能有一个正确答案
    #             if len(self.objective_line_ids.filtered(lambda line: line.is_correct)) != 1:
    #                 flag_dict.update({'flag': False, 'info': '单选题应该有且只有一个正确答案'})
    #         else:
    #             # 多选：至少有两个正确答案
    #             if len(self.objective_line_ids.filtered(lambda line: line.is_correct)) <= 1:
    #                 flag_dict.update({'flag': False, 'info': '多选题应该至少有一个正确答案'})
    #     return flag_dict

    # @api.model
    # def create(self, vals):
    #     vals["number"] = self.env['ir.sequence'].next_by_code('roke.subject.title.data.code')
    #     return super(RokeSubjectTitleData, self).create(vals)

    # 禁用
    def btn_forbid(self):
        self.forbidden_state = 'forbidden'

    # 启用
    def btn_normal(self):
        self.forbidden_state = 'normal'

    @api.constrains('number')
    def _check_number_uniq(self):
        """
        校验题目编号不能重复
        :return:
        """
        for rec in self:
            if self.sudo().search_count([('number', '=', rec.number)]) > 1:
                raise UserError("题目编号必须唯一：编号【%s】重复" % rec.name)


class RokeSubjectTitleDataLine(models.Model):
    _name = 'roke.subject.title.data.line'
    _description = "题目设置"

    parent_id = fields.Many2one('roke.subject.title.data', string='题目')
    sequence = fields.Integer(string="序号")
    name = fields.Char(string="题目名称")
    menu_fuc = fields.Many2one('ir.ui.menu', string="功能")
    url = fields.Char(string="对应url")
    total_marks = fields.Float(string="总分数", compute='_compute_total_marks')
    remark = fields.Text(string="备注")
    line_ids = fields.One2many('roke.subject.title.data.standard.line', 'parent_id', string='题目详情')

    @api.depends('line_ids.mark')
    def _compute_total_marks(self):
        """
        计算题目总分数
        :return:
        """
        for res in self:
            res.total_marks = sum(line.mark for line in res.line_ids)


class RokeSubjectTitleDataStandardLine(models.Model):
    _name = "roke.subject.title.data.standard.line"
    _description = "题目标准"

    parent_id = fields.Many2one('roke.subject.title.data.line', string='题目设置')
    sequence = fields.Integer(string="序号")
    project_id = fields.Many2one('roke.subject.project', string="项目类别")
    model_id = fields.Many2one('ir.model', string="当前项对应模型")
    field_id = fields.Many2one('ir.model.fields', string="当前项对应字段")
    content = fields.Char(string="内容")
    total_marks = fields.Float(string='总分数', digits=(8, 2))
    mark = fields.Float(string='所占分数', digits=(8, 2))
    remark = fields.Text(string='备注')


class RokeSubjectObjectiveTitleDataLine(models.Model):
    _name = 'roke.subject.objective.title.data.line'
    _description = "客观题题目设置"
    parent_id = fields.Many2one('roke.subject.title.data', string='题目')
    sequence = fields.Integer(string="序号")
    name = fields.Char(string="选项内容")
    is_correct = fields.Boolean(string='是否是正确答案', default=False)
    remark = fields.Text(string='备注')

    # @api.model_create_multi
    # def create(self, vals):
    #     """
    #     继承create方法，校验：
    #         单选题要有且只能有一个正确答案
    #         多选题至少有两个正确答案
    #     :param vals:
    #     :return:
    #     """
    #     res = super().create(vals)
    #     for item in res:
    #         flag_dict = item.parent_id.check_correct_option_count()
    #         if flag_dict['flag']:
    #             raise UserError('{error_info}'.format(error_info=flag_dict['info']))
    #     return res
    #
    # def write(self, vals):
    #     """
    #     继承write方法，校验：
    #         单选题要有且只能有一个正确答案
    #         多选题至少有两个正确答案
    #     :param vals:
    #     :return:
    #     """
    #     res = super().write(vals)
    #     flag_dict = self.parent_id.check_correct_option_count()
    #     if flag_dict['flag']:
    #         raise UserError('{error_info}'.format(error_info=flag_dict['info']))
    #     return res
    #
    # def unlink(self):
    #     """
    #     继承unlink方法，校验：
    #         单选题要有且只能有一个正确答案
    #         多选题至少有两个正确答案
    #     :return:
    #     """
    #     for res in self:
    #         title_data_id = res.parent_id
    #

class RokeSubjectTitleDataImages(models.Model):
    _name = "roke.subject.title.data.images"
    _description = "题目图片列表"

    parent_id = fields.Many2one('roke.subject.title.data', string="题目")
    sequence = fields.Integer(string="序号")
    img_data = fields.Binary("图片")
    note = fields.Text(string="备注")

    def get_image_url(self, file_type=None):
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachment = self.sudo().env['ir.attachment'].search([
            ("res_model", "=", "roke.subject.title.data.images"),
            ("res_id", "=", self.id),
            ("res_field", "=", "img_data")
        ])
        if not attachment:
            return False
        if file_type == "base64":
            return attachment.datas
        if not attachment.access_token:
            attachment.generate_access_token()
        if attachment.mimetype == "application/pdf":
            # pdf 预览
            content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
            return "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
        else:
            # 图片 预览
            return "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
