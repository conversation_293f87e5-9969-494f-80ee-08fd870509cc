# -*- coding: utf-8 -*-
"""
Description:
主观评分
"""
import datetime

from odoo.exceptions import ValidationError
from odoo import models, fields, api


class RokeSubjectivityGrade(models.Model):
    _name = "roke.subjectivity.grade"
    _inherit = ['mail.thread']
    _description = "主观评分"
    _rec_name = "student_id"

    main_exam_id = fields.Many2one('roke.base.exam', string='考试')
    exam_id = fields.Many2one('roke.subject.student.exam', string='考试', ondelete='cascade')
    exam_record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    org_id = fields.Many2one('roke.base.org', string='组织', required=True)
    student_id = fields.Many2one('roke.employee', string='学生', required=True)
    total_marks = fields.Float(string='总分数', digits=(8, 2), compute='_compute_total_proportion_mark')
    score = fields.Float(string='得分', digits=(8, 2), compute='_compute_total_mark')
    state = fields.Selection([('wait_confirm', '待确认'), ('confirm', '已确认'), ('cancel', '作废')], string='状态',
                             default='wait_confirm', copy=False)
    line_ids = fields.One2many('roke.subjectivity.grade.line', 'parent_id', string='主观评分明细', copy=True)
    pattern_type = fields.Selection([('practice', '练习模式'), ('exam', '考试模式')], string='模式类型', default='exam')
    active = fields.Boolean(string='已存档', default=True, tracking=True, copy=False)
    checkbox_score_type = fields.Selection([('give', '多选题半对给分'), ('not_give', '多选题半对不给分')],
                                           string='多选题给分模式', related='main_exam_id.checkbox_score_type')

    # 确认
    def confirm(self):
        """
        确认，同步主观评分到考试记录下
        :return:
        """
        self.state = 'confirm'
        for line in self.line_ids:
            line.record_line_id.write({
                'mark': line.mark,
                'real_content': line.answer,
                'state': 'confirm'
            })
            line.state = 'confirm'
        # 判断实际考试是否完成
        if self.exam_id.state == 'wait_subjectivity':
            # 考试记录完成
            self.exam_record_id.write({
                'state': 'done',
                'end_time': datetime.datetime.now(),
                'duration': (datetime.datetime.now() - self.exam_record_id.start_time).seconds / 60,
            })
            self.exam_id.state = 'done'

    @api.depends('line_ids.mark')
    def _compute_total_mark(self):
        """
        计算总得分
        :return:
        """
        for res in self:
            res.score = sum(line.mark for line in res.line_ids)

    @api.depends('line_ids.proportion_mark')
    def _compute_total_proportion_mark(self):
        """
        计算总分
        :return:
        """
        for res in self:
            res.total_marks = sum(line.proportion_mark for line in res.line_ids)


class RokeSubjectivityGradeLine(models.Model):
    _name = "roke.subjectivity.grade.line"
    _description = "主观评分明细"

    parent_id = fields.Many2one('roke.subjectivity.grade', string='主观评分')
    record_line_id = fields.Many2one('roke.subjectivity.record.line', string='考试记录主观评分ID')
    course_id = fields.Many2one('roke.subject.course', string='科目')
    project_id = fields.Many2one('roke.subject.project', string='项目')
    title_data_id = fields.Many2one('roke.subject.title.data', string='题目')
    content = fields.Char(string="题目信息")
    true_content = fields.Char(string="答案")
    answer = fields.Char(string="考生填写内容", copy=False)
    proportion_mark = fields.Float(string='所占分数', digits=(8, 2))
    mark = fields.Float(string='得分', digits=(8, 2), copy=False)
    state = fields.Selection([('wait_confirm', '待确认'), ('confirm', '已确认')], string='状态', default='wait_confirm',
                             copy=False)

    @api.onchange('mark')
    def _onchange_mark(self):
        if self.mark:
            if self.mark > self.proportion_mark:
                raise ValidationError('得分不能大于所占分数，请检查')
