<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>前厅服务与管理技能考评系统</title>
    <!-- <link rel="stylesheet" href="../src/css/work_report/index.css">
    <link rel="stylesheet" href="../src/css/work_report/main.css"> -->

    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/index.css">
    <!-- <link rel="stylesheet" href="../src/css/work_report/index.css"> -->
    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/main.css">
    <!-- <link rel="stylesheet" href="../src/css/work_report/main.css"> -->
    <style>
        body {
            margin: 0;
        }

        .el-tabs__header {
            width: 13%;
        }

        .move_box {
            position: fixed;
            height: 10px;
            background-color: #d1d9d9;
            position: fixed;
            left: 398px;
            top: 275px;
            width: 79%;
            padding: 1% 0 0 0;
        }

        .move_box:active {
            background-color: #262b2b;
        }

        .active {
            background-color: greenyellow;
        }

        .box {
            display: flex;
            flex-direction: column;
        }

        .el-radio {
            margin-top: .5vw;
        }

        .el-radio__label {
            white-space: normal;
            word-break: break-all;
        }
    </style>
</head>

<body>
    <div id="app">
        <div style="display: flex;min-height: 100vh;">
            <div style="width: 20%;border-right: 6px solid #ccc; padding-top:2vw; background-color: #e7e7e7;">
                <div>
                    <div style="text-align: center;
                    font-weight: 600;
                    font-size: 22px;
                    font-family: cursive;" v-if="examinationTime">考试时间：<i class="el-icon-time"></i> {{ `${hr}: ${min}: ${sec}` }}
                    </div>
                    <div style="padding-left: 36%;padding-top: 20px;">
                        <el-button v-show="isshow1" @click="begin" round type="primary">开始答题</el-button>
                        <el-button v-show="isshow2" @click="open" round type="danger">交卷</el-button>
                        <el-button v-show="isshows" round type="info" disabled>已交卷</el-button>
                    </div>
                </div>
                <div style="   text-align: center;
                background-color: rgb(233 230 230);
                margin: 2vw auto 0px;
                width: 18vw;
                height: 150px;
                border-radius: 15px;
                padding-top: 1px;
                box-shadow: 11px 8px 9px #c3c9c8;
            ">
                    <div style="margin-top: 15px; font-weight: 600;">请核对身份信息是否正确</div>
                    <div style="padding-top: 10px;">姓名：{{this.userName}}</div>
                    <div style="padding-top: 10px;">身份证号：{{this.userCard}}</div>
                    <div style="padding-top: 10px;">考号：{{this.userCode}}</div>
                </div>
                <div style="display: flex; flex-direction: column; margin: 6% auto 0; width: 8vw;" v-if="icon">
                    <div v-for="(item, index) in btnList" :key="index" @click="btnStn(item)">
                        <el-button style="height: 40px;margin:10px 0 0 20px;" type="primary">
                            {{ item }}</el-button>
                    </div>
                    <!-- <el-button v-if="btnShow" style="margin-left: 67%;" type="primary" @click='btnClicj'>
                        保存</el-button> -->
                </div>
                <!-- 题目数量 -->
                <div style="margin-top: 5%;margin-left: 30%;">
                    <div v-for="(item, index) in list" :key="index" v-if="examinations" @click="urlClicks(item)">
                        <div v-for="(ite,ind) in item.data_process" @click="urlClick(ite)">
                            <el-button style="height: 40px;margin:20px 0 0 20px;" type="primary">
                                {{ite.process}}</el-button>
                        </div>
                    </div>
                </div>

            </div>
            <div style="    width: 79.5%;
            background-color: rgb(247, 247, 247);
            padding: .5vw 0 0 0.5vw;" v-if="fetchStateBack">
                <div style="font-size: 22px;
                font-weight: 600; font-family: cursive;" v-if="examinationTitle">{{this.topicTitle}}</div>
                <div style="margin: 8vw auto 0;
                width: 15vw;
                font-size: 27px;
                font-family: cursive;
                font-weight: 600;" v-if="off">{{this.offvalue}}</div>
                <!-- 单选 -->
                <div v-for="(item, index) in list" :key="index" style="margin-top: 0.5vw;     margin-top: 0.5vw;
                background-color: rgb(251 251 251);
                min-height: 136px;
                border-radius: 15px;
                padding-top: 1px;
                box-shadow: rgb(195 201 200) 11px 8px 9px;
                padding-left: 1vw;
                padding-top: .5vw; width: 77vw; position: relative;" v-if="examination">
                    <div style=" font-size: 19px;">
                        {{item.data_sequence}}. {{item.title}}
                    </div>
                    <div class="box">
                        <el-radio v-for="(ite,ind) in item.options" v-model="item.option" :label="ite.option"
                            :key="item.option"
                            @change.native="choose(ite,ind)">{{ite.option}}.{{ite.option_name}}</el-radio>
                    </div>
                    <el-row style="    position: absolute;
                        right: 30px;
                        top: 91px;" v-if="answer">
                        <el-button type="info" round @click="viewAnswer(item.options)">查看答案</el-button>
                    </el-row>
                    <div>
                    </div>
                </div>

                <!-- 多选 -->
                <div v-for="(item, index) in duoxuanArray" :key="index" style="margin-top: 0.5vw;     margin-top: 0.5vw;
                background-color: rgb(251 251 251);
                min-height: 136px;
                border-radius: 15px;
                padding-top: 1px;
                box-shadow: rgb(195 201 200) 11px 8px 9px;
                padding-left: 1vw;
                padding-top: .5vw; width: 77vw; position: relative;" v-if="examinationDuo">
                    <div style=" font-size: 19px;">
                        {{item.data_sequence}}. {{item.title}}
                    </div>
                    <div class="box">
                        <el-checkbox v-for="(ite,ind) in item.options" v-model="ite.state" :label="ite.option"
                            :key="item.options"
                            @change.native="chooseMany(ite,item)">{{ite.option}}.{{ite.option_name}}</el-checkbox>
                    </div>
                    <el-row style="    position: absolute;
                        right: 30px;
                        top: 91px;" v-if="answer">
                        <el-button type="info" round @click="viewAnswer(item.options)">查看答案</el-button>
                    </el-row>
                    <div>
                    </div>
                </div>
                <!-- 判断 -->
                <div v-for="(item, index) in panduanArray" :key="index" style="margin-top: 0.5vw;     margin-top: 0.5vw;
                background-color: rgb(251 251 251);
                min-height: 136px;
                border-radius: 15px;
                padding-top: 1px;
                box-shadow: rgb(195 201 200) 11px 8px 9px;
                padding-left: 1vw;
                padding-top: .5vw; width: 77vw; position: relative;" v-if="examinationPan">
                    <div style=" font-size: 19px;">
                        {{item.data_sequence}}. {{item.title}}
                    </div>
                    <div class="box">
                        <el-radio v-for="(ite,ind) in item.options" v-model="item.option" :label="ite.option"
                            :key="item.option"
                            @change.native="chooseJudgment(ite,ind)">{{ite.option}}.{{ite.option_name}}</el-radio>
                    </div>
                    <el-row style="    position: absolute;
                        right: 30px;
                        top: 91px;" v-if="answer">
                        <el-button type="info" round @click="viewAnswer(item.options)">查看答案</el-button>
                    </el-row>
                    <div>
                    </div>
                </div>

                <div style="font-size: 20px;
                background-color: #ededed;
                padding: 15px;
                border: 2px #d5cccc solid;
                margin-top: 1vw;
                box-shadow: 11px 8px 9px #c3c9c8;
                border-radius: 20px; width: 77vw;" v-if="examinationss">
                    {{this.titles}}
                </div>
                <div style="    margin-top: 1vw;
                font-size: 17px;
                font-family: cursive;
                font-weight: 600;" v-if="examinationss">
                    {{this.remark}}
                </div>
                <div v-drag class="move_box" v-if="examinationss">
                    <iframe :src="url" width="100%" height="600px" scrolling="yes" frameborder="0"></iframe>
                </div>
            </div>
        </div>
    </div>
</body>
<!-- <script type="text/javascript" src="../src/js/work_report/vue.min.js"></script>
<script type="text/javascript" src="../src/js/work_report/index.js"></script>
<script type="text/javascript" src="../src/js/work_report/axios.min.js"></script> -->

<!-- <script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/vue.min.js"></script> -->
<!-- <script type="text/javascript" src="../src/js/work_report/vue.min.js"></script>
<script type="text/javascript" src="../src/js/work_report/index.js"></script>
<script type="text/javascript" src="../src/js/work_report/axios.min.js"></script> -->


<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/vue.min.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/index.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/axios.min.js"></script>

<script>

    var Main = {
        directives: {
            drag(el) {
                // 鼠标移动到目标盒子上--监听鼠标按下事件
                el.onmousedown = function (e) {
                    // 计算出此时点击目标盒子 
                    var disx = e.offsetX
                    var disy = e.offsetY

                    document.onmousemove = function (e2) {
                        var move_box = document.getElementsByClassName('move_box')[0]
                        move_box.style.position = 'fixed'
                        // move_box.style.left = e2.clientX - disx + 'px'
                        move_box.style.top = e2.clientY - disy + 'px'
                    }
                    document.onmouseup = function () {
                        document.onmousemove = document.onmouseup = null
                    }
                }
            }
        },
        data() {
            return {
                isshow1: true,
                isshow2: false,
                hr: 0,
                min: 0,
                sec: 0,
                tabPosition: 'left',
                value: '',
                input: '',
                num: 1,
                title: "",
                btnList: ["实操题", "单选题", "多选题", "判断题"],
                data_standardList: [],
                text: '',
                url: '',
                list: [],
                activeName: '1',
                tabPosition: 'left',
                btnShow: false,
                content: '',
                fileld: '',
                id: '',
                mark: '',
                model: '',
                getTime: '',
                num1: "",
                num2: "",
                num3: "",
                isshows: false,
                icon: false,
                type: '',
                userId: '',
                //考生姓名
                userName: '',
                //账号
                userCode: '',
                //身份证
                userCard: '',
                shiCaoArray: [],
                keGuanArray: [],
                duoxuanArray: [],
                topicArray: [],
                inde: '',
                startTime: '',
                endTime: '',
                time: '',
                odd: false,
                radio: '',
                it: '',
                examination: false,
                examinations: false,
                examinationss: false,
                examinationDuo: false,
                examinationTitle: false,
                examinationPan: false,
                answerArray: [],
                remark: '',
                off: true,
                offvalue: '请点击开始答题',
                correct: '',
                answer: false,
                topicTitle: '',
                checkboxGroup1: [],
                checkboxData: [],
                answers: [],
                duoxuanAnswer: {},
                tianzai: '',
                listMany: [],
                panduanArray: [],
                judgment: [],
                examId: '',
                timer: null,
                fetchStateBack: true,
                hrStop: "",
                minStop: "",
                secStop: "",
                yearMonth: '',
                openTime: true,
                examinationTime: true
            }
        },
        created() {
            // let that = this
            // let date = new Date();
            // let y = date.getFullYear();
            // let m = date.getMonth() + 1;
            // m = m < 10 ? ('0' + m) : m;
            // let d = date.getDate();
            // d = d < 10 ? ('0' + d) : d;
            // this.getTime = y + '-' + m + '-' + d;
            // console.log(this.getTime);
        },

        mounted() {
            this.userId = parseInt(this.GetRequest().id)
            this.type = this.GetRequest().type
            this.getList()
            // console.log(this.time);
            if (this.type == "practice") {
                this.answer = true
            }
            // this.timer = setInterval(() => {
            //     this.fetchState(this.examId)
            // }, 5000)
        },
        watch: {
            hr(newVal, oldVal) {
                this.num1 = setInterval(function () {
                    // console.log('oldVal', oldVal)
                    // console.log('newVal', newVal)
                }, 1000);

            },
            min(newVal, oldVal) {
                this.num2 = setInterval(function () {
                    // console.log('oldVal', oldVal)
                    // console.log('newVal', newVal)
                }, 1000);

            },
            sec(newVal, oldVal) {
                this.num3 = setInterval(function () {
                    // console.log('oldVal', oldVal)
                    // console.log('newVal', newVal)
                }, 1000);

            }
        },
        methods: {
            viewAnswer(item) {
                // console.log(item);
                let that = this
                var tian = item.filter(item => {
                    return item.is_correct == true
                })
                that.correct = tian[0]
                var result = tian.map(item => item.option);
                // console.log(result);
                that.$message({
                    showClose: true,
                    message: "第" + that.correct.data_sequence + '题的正确答案是' + result,
                    duration: 2500
                });
            },
            timeStamp(second_time) {

                var time = parseInt(second_time) + "秒";
                // console.log(time);
                if (parseInt(second_time) > 60) {

                    var second = parseInt(second_time) % 60;
                    var min = parseInt(second_time / 60);
                    time = min + "分" + second + "秒";
                    this.min = min
                    this.sec = second
                    if (min > 60) {
                        min = parseInt(second_time / 60) % 60;
                        var hour = parseInt(parseInt(second_time / 60) / 60);
                        time = hour + "小时" + min + "分" + second + "秒";
                        // console.log(hour);
                        this.hr = hour
                        this.min = min
                        this.sec = second

                        if (hour > 24) {
                            hour = parseInt(parseInt(second_time / 60) / 60) % 24;
                            var day = parseInt(parseInt(parseInt(second_time / 60) / 60) / 24);
                            time = day + "天" + hour + "小时" + min + "分" + second + "秒";
                            this.hr = hour
                            this.min = min
                            this.sec = second
                        }
                    }
                }

                return time;
            },
            divTime(time1) {
                time1 = Date.parse(new Date(time1));
                time2 = Date.parse(new Date());
                this.time = Math.abs((time2 - time1) / 1000)
                var time = this.timeStamp(Math.abs((time2 - time1) / 1000))
            },
            //多选题目的事件
            chooseMany(ite, item) {

                this.answers.push(ite)
                //过滤器只要选中的
                this.tianzai = this.answers.filter(item => {
                    return item.state == true
                })
                //把答案添加数组方法
                for (var i = 0; i < this.duoxuanArray.length; i++) {
                    this.duoxuanArray[i].answer.filter(item => {
                        return item.state == true
                    })
                    for (var j = 0; j < this.tianzai.length; j++) {
                        if (this.duoxuanArray[i].title_id == this.tianzai[j].title_id) {
                            this.duoxuanArray[i].answer.push(this.tianzai[j])
                        }
                    }
                }
                //数组去重
                for (var a = 0; a < this.duoxuanArray.length; a++) {
                    for (let i = 0; i < this.duoxuanArray[a].answer.length; i++) {
                        for (let j = i + 1; j < this.duoxuanArray[a].answer.length; j++) {
                            if (this.duoxuanArray[a].answer[i].option == this.duoxuanArray[a].answer[j].option && this.duoxuanArray[a].answer[i].title_id == this.duoxuanArray[a].answer[j].title_id) {
                                this.duoxuanArray[a].answer.splice(j, 1);
                                j--;
                            };
                            var tian = this.duoxuanArray[a].answer.filter(item => {
                                return item.state == true
                            })
                            this.duoxuanArray[a].answer = tian
                        };
                    };

                }

            },
            //单选题目点击事件
            choose(item, ind) {
                if (this.answerArray.length == 0) {
                    this.answerArray.push(item)
                } else {
                    for (var i = 0; i < this.answerArray.length; i++) {
                        if (item.data_sequence == this.answerArray[i].data_sequence) {
                            this.answerArray.splice(i, 1)
                        }
                    }
                    this.answerArray.push(item)
                }
            },
            //判断题
            chooseJudgment(item, ind) {
                if (this.judgment.length == 0) {
                    this.judgment.push(item)
                } else {
                    for (var i = 0; i < this.judgment.length; i++) {
                        if (item.data_sequence == this.judgment[i].data_sequence) {
                            this.judgment.splice(i, 1)
                        }
                    }
                    this.judgment.push(item)
                }
            },
            GetRequest() {
                var url = location.search; //获取url中"?"符后的字串
                var theRequest = new Object();
                if (url.indexOf("?") != -1) {
                    var str = url.substr(1);
                    strs = str.split("&");
                    for (var i = 0; i < strs.length; i++) {
                        theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
                return theRequest;
            },
            getDate() {
                var date = new Date() // 获取时间
                var year = date.getFullYear() // 获取年
                var month = date.getMonth() + 1  // 获取月
                var strDate = date.getDate() // 获取日
                // var day = date.getDate() //
                var day = '日一二三四五六'.charAt(new Date().getDay()) // 周一返回的是1，周六是6，但是周日是0
                var hour = date.getHours() // 获取小时
                var minute = date.getMinutes() // 获取分钟
                var second = date.getSeconds() // 获取秒
                // 由于部分业务处理 是需要月份日份前面有0 故新增一个函数
                this.getNum()
                this.yearMonth = year +
                    '-' +
                    this.getNum(month) +
                    '-' +
                    this.getNum(strDate) + " " + this.getNum(hour) + ':' + this.getNum(minute) + ':' + this.getNum(second)


            },
            getNum(i) {
                return i < 10 ? '0' + i : i
            },
            getList() {
                let that = this;
                axios.request({
                    url: "/roke/student_conduct_exam",
                    method: "post",
                    // headers: {
                    //     'Content-Type': 'application/json'
                    // },
                    data: {
                        "student_id": that.userId,
                        "pattern_type": that.type
                    }
                }).then(function (res) {
                    // console.log(res);
                    if (res.data.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.$message({
                            type: 'success',
                            message: res.data.result.msgs
                        });
                        that.userName = res.data.result.data[0].student_name
                        that.userCode = res.data.result.data[0].student_code
                        that.userCard = res.data.result.data[0].student_card_code
                        that.startTime = res.data.result.data[0].start_time
                        that.endTime = res.data.result.data[0].end_time
                        // that.time = res.data.result.data[0].end_time
                        that.examId = res.data.result.data[0].exam_id
                        that.shiCaoArray = res.data.result.data[1]
                        that.keGuanArray = res.data.result.data[2]
                        that.duoxuanArray = res.data.result.data[4]
                        that.panduanArray = res.data.result.data[3]
                        that.divTime(that.endTime)
                        console.log(res)
                    }
                });
            },
            begin() {
                this.icon = true
                let that = this
                that.offvalue = '请选择左侧题目类型'
                // 点击按钮后开始计算指定长度的时间
                that.time = (Date.parse(new Date()) + ((that.time)) * 1000);
                // Date.parse(new Date(time1));
                // console.log(Date.parse(new Date()) + ((that.time)) * 1000);
                //that开始执行倒计时
                that.countdown();
                // 更换按钮，根据情况选择v-if或v-show
                that.isshow1 = false;
                that.isshow2 = true
                axios.request({
                    url: "/roke/start_exam",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        "student_id": that.userId,
                        "pattern_type": that.type
                    }
                }).then(function (res) {

                    // console.log(res);
                    if (res.data.result.state == 'success') {
                        console.log(11111111111);
                        axios.request({
                            url: "/roke/student_conduct_exam",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                'student_id': that.userId,
                                'pattern_type': that.type
                            }
                        }).then(res => {
                            // console.log(res);
                        })
                    } else {
                        that.$message({
                            message: res.data.result.msgs
                        })
                    }
                });

            },
            countdown() {
                const end = this.time; // 定义结束时间
                // console.log(end);
                const now = Date.parse(new Date()); // 获取本地时间
                // console.log(now);
                const msec = end - now; // 定义总共所需的时间
                // console.log(msec);
                // 将时间戳进行格式化
                let hr = parseInt(msec / 1000 / 60 / 60 % 24);
                // console.log(hr);
                let min = parseInt(msec / 1000 / 60 % 60);
                let sec = parseInt(msec / 1000 % 60);
                // 倒计时结束时的操作
                const that = this;
                if (this.hr == 0 && this.min == 0 && this.sec == 1) {
                    if (openTime) {
                        // console.log('时间已经结束，答题完毕');
                        this.isshows = true
                        this.isshow2 = false
                        this.icon = false
                        that.examination = false
                        that.examinations = false
                        that.examinationss = false
                        that.examinationDuo = false
                        that.examinationPan = false
                        that.examinationTitle = false
                        axios.request({
                            url: "/roke/student_end_exam ",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                "student_id": that.userId,
                                "pattern_type": that.type,
                                "objective_data": that.answerArray,
                                "checkbox_data": that.duoxuanArray,
                                "judgment_data": that.judgment,
                                'exam_id': that.examId
                            }
                        }).then(function (res) {
                            // console.log(res);
                            that.$message({
                                type: 'success',
                                message: res.data.result.msgs
                            })
                        });
                        // this.icon=false
                        that.$message({
                            // type: 'success',
                            message: '时间已经结束，答题完毕'
                        });
                        // this.hr = 3;
                        // this.min = 30;
                        // this.sec = 0;
                    }
                } else if (this.hr == 0 && this.min == 1 && this.sec == 0) {
                    that.$message({
                        // type: 'success',
                        message: '时间还剩一分钟,请考生注意答题时间'
                    });
                    this.hr = hr > 9 ? hr : '0' + hr;
                    this.min = min > 9 ? min : '0' + min;
                    this.sec = sec > 9 ? sec : '0' + sec;
                    setTimeout(that.countdown, 1000)
                } else {
                    // 如时间未归零则继续在一秒后执行
                    this.hr = hr > 9 ? hr : '0' + hr;
                    this.min = min > 9 ? min : '0' + min;
                    this.sec = sec > 9 ? sec : '0' + sec;
                    setTimeout(that.countdown, 1000)
                }
            },
            open() {
                let that = this
                that.$confirm('即将结束答题, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then((action) => {
                    // eleUI的确定结束回调函数方法
                    // console.log(action)
                    if (action === 'confirm') {
                        // console.log(that.num1)
                        clearInterval(that.num1);
                        clearInterval(that.num2);
                        clearInterval(that.num3);
                        clearInterval(that.timer)
                        that.hr = 0;
                        that.min = 0;
                        that.sec = 0;

                        that.examination = false
                        that.examinations = false
                        that.examinationss = false
                        that.examinationTitle = false
                        that.examinationDuo = false
                        that.examinationPan = false
                        that.isshow2 = false
                        that.isshows = true
                        this.icon = false
                        this.$message({
                        type: 'info',
                        message: '已交卷'
                    });
                    axios.request({
                            url: "/roke/student_end_exam ",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                "student_id": that.userId,
                                "pattern_type": that.type,
                                "objective_data": that.answerArray,
                                "checkbox_data": that.duoxuanArray,
                                "judgment_data": that.judgment,
                                'exam_id': that.examId
                            }
                        }).then(function (res) {
                            // console.log(res);
                            that.$message({
                                type: 'success',
                                message: res.data.result.msgs
                            })
                        });
                    }

                }).catch(() => {
                    // 点击取消后
                    // console.log("==============================");
                    this.$message({
                        type: 'info',
                        message: '已取消交卷'
                    });
                });
            },
            handleTabsChange(tab) {
                this.$store.commit('handleTabsChange', tab.name)
            },
            handleChange(value) {
                // console.log(value);
            },
            btnStn(item) {
                if (item == "实操题") {
                    // console.log("111111111111111");
                    this.offvalue = '请选择考题'
                    this.off = true
                    this.list = this.shiCaoArray
                    this.titles = ''
                    this.remark = ''
                    this.examinationss = false
                    // this.titles=this.shiCaoArray[0].title
                    this.examination = false
                    this.examinations = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTitle = false
                } else if (item == "单选题") {
                    this.list = this.keGuanArray
                    // console.log(this.list);
                    this.off = false
                    this.examination = true
                    this.examinations = false
                    this.examinationss = false
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTitle = true
                    this.topicTitle = '单选题'
                    for (var i = 0; i < this.list.length; i++) {
                        // console.log(this.topicArray[i]);
                        for (var j = 0; j < this.list[i].options.length; j++) {
                            // console.log(this.list[i].options[j]);
                            this.$set(this.list[i].options[j], "data_sequence", this.list[i].data_sequence)
                            this.$set(this.list[i].options[j], "title_id", this.list[i].title_id)
                        }
                    }
                } else if (item == "多选题") {
                    // console.log('=============');
                    // console.log(this.list);
                    this.off = false
                    this.examination = false
                    this.topicTitle = '多选题'
                    this.examinations = false
                    this.examinationss = false
                    this.examinationTitle = true
                    this.examinationPan = false
                    this.examinationDuo = true
                    for (var i = 0; i < this.duoxuanArray.length; i++) {
                        // console.log(this.topicArray[i]);
                        this.$set(this.duoxuanArray[i], "answer", [])

                        for (var j = 0; j < this.duoxuanArray[i].options.length; j++) {
                            // console.log(this.list[i].options[j]);

                            if (this.duoxuanArray[i].options[j].hasOwnProperty("state") == false) {
                                this.$set(this.duoxuanArray[i].options[j], "state", false)
                            }
                            this.$set(this.duoxuanArray[i].options[j], "title_id", this.duoxuanArray[i].title_id)
                            this.$set(this.duoxuanArray[i].options[j], "data_sequence", this.duoxuanArray[i].data_sequence)

                        }
                    }
                    // console.log(this.duoxuanArray);
                } else if (item == "判断题") {
                    // console.log(this.list);
                    this.off = false
                    this.examination = false
                    this.examinations = false
                    this.examinationss = false
                    this.examinationDuo = false
                    this.examinationTitle = true
                    this.examinationPan = true
                    this.topicTitle = '判断题'
                    for (var i = 0; i < this.panduanArray.length; i++) {
                        // console.log(this.topicArray[i]);
                        for (var j = 0; j < this.panduanArray[i].options.length; j++) {
                            // console.log(this.list[i].options[j]);
                            this.$set(this.panduanArray[i].options[j], "data_sequence", this.panduanArray[i].data_sequence)
                            this.$set(this.panduanArray[i].options[j], "title_id", this.panduanArray[i].title_id)
                        }
                    }
                }
            },
            fetchState(examId) {
                let that = this
                axios.request({
                    url: '/roke/query_exam_state',
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'exam_id': examId
                    }
                }).then(res => {
                    // console.log(res);
                    if (res.data.result.data == 'compel_over_exam') {
                        clearInterval(that.num1);
                            clearInterval(that.num2);
                            clearInterval(that.num3);
                            clearInterval(that.timer)
                            that.hr = 0;
                            that.min = 0;
                            that.sec = 0;
                            // console.log(that.hr + ',' + that.min + ',' + that.sec);
                            // that.isshow1 = true;
                            that.examination = false
                            that.examinations = false
                            that.examinationss = false
                            that.examinationTitle = false
                            that.examinationDuo = false
                            that.examinationPan = false
                            that.isshow2 = false
                            that.isshows = true
                            this.icon = false
                        axios.request({
                            url: "/roke/student_end_exam ",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                "student_id": that.userId,
                                "pattern_type": that.type,
                                "objective_data": that.answerArray,
                                "checkbox_data": that.duoxuanArray,
                                "judgment_data": that.judgment,
                                'exam_id': that.examId
                            }
                        }).then(function (res) {


                            that.$message.error({
                                message: '已强制交卷'
                            })
                        });
                    } else if (res.data.result.data == 'exam_suspend') {
                        that.fetchStateBack = false
                        this.openTime = false
                        if (that.heStop == "" && that.min == "") {
                            that.heStop = JSON.stringify(that.hr)
                            that.minStop = JSON.stringify(that.min)
                            that.secStop = JSON.stringify(that.sec)

                        }
                        that.$message({
                            message: '考试已被考官暂停，请稍等',
                            duration: 3000,
                            // showClose: true,
                        });
                        this.examinationTime=false
                    } else if (res.data.result.data == 'suspend_continue') {
                        that.fetchStateBack = true
                        that.hr = that.heStop;
                        that.min = that.minStop;
                        that.sec = that.secStop;
                        that.$message({
                            // type: 'success',
                            message: '考试已恢复，请继续答题',
                            duration: 0,
                            showClose: true,
                        });
                        this.openTime = true
                        this.examinationTime=true
                    } else if (res.data.result.data == 'exam_delayed') {
                        this.getDate()
                        this.divTime(res.data.result.end_time, this.yearMonth)
                        // console.log();
                        this.time = Date.parse(new Date(res.data.result.end_time));
                        that.$message({
                            // type: 'success',
                            message: '考试延时'+res.data.result.delayed_time+"分钟",
                            duration: 0,
                            showClose: true,
                        });

                    }
                })
            },
            urlClicks(item) {
                // console.log(item);
                this.titles = item.title
            },
            urlClick(item) {
                this.off = false
                // console.log(item);
                this.url = item.url
                this.remark = item.remark
                this.examinationss = true
            },

            btnClicj() {
                let that = this
                axios.request({
                    url: "/roke/student_exam_record",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'content': that.content,
                        'fileld': that.fileld,
                        'id': that.id,
                        'mark': that.mark,
                        'model': that.model
                    }
                }).then(function (res) {
                    if (res.data.state === 'error') {
                        that.$message({
                            type: 'error',
                            message: res.data.result.msgs
                        });
                    } else {
                        that.$message({
                            type: 'success',
                            message: res.data.result.msgs
                        });
                    }
                });
            }
        },
        beforeDestroy: function () {
            // clearInterval(this.timer);
        }
    };
    var Ctor = Vue.extend(Main);
    new Ctor().$mount('#app')
</script>

</html>