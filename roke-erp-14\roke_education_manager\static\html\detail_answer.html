<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>数字化考试云平台</title>
    <!-- <link rel="stylesheet" href="../src/css/work_report/index.css">
    <link rel="stylesheet" href="../src/css/work_report/main.css"> -->

    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/index.css">
    <!-- <link rel="stylesheet" href="../src/css/work_report/index.css"> -->
    <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/main.css">
    <!-- <link rel="stylesheet" href="../src/css/work_report/main.css"> -->
    <style>
        h1,
        ul,
        li,
        a,
        body,
        p {
            margin: 0;
            padding: 0;
            text-decoration: none;
        }


        .el-tabs__header {
            width: 13%;
        }

        .move_box {
            position: fixed;
            height: 10px;
            background-color: #d1d9d9;
            position: fixed;
            left: 398px;
            top: 275px;
            width: 79%;
            padding: 1% 0 0 0;
        }

        .move_box:active {
            background-color: #262b2b;
        }

        .active {
            background-color: greenyellow;
        }

        .box {
            display: flex;
            flex-direction: column;
        }

        .el-radio {
            margin-top: .5vw;
        }

        .el-radio__label {
            white-space: normal;
            word-break: break-all;
        }

        .title {
            width: 100%;
            height: 5vh;
            background-color: #FFFFFF;

        }

        .title p {
            width: 63vw;
            height: 5vh;
            font-size: 24px;
            font-family: cursive;
            font-weight: 600;
            color: #4F80F7;
            line-height: 5vh;
            margin-left: 10vw;
        }

        .el-icon-time {
            color: #4F80F7 !important;
        }

        .elIcon {
            background: url(../src/images/) no-repeat;
            display: inline-block;
            width: 50px;
            height: 50px;
        }

        .el-button--primary {
            background-color: #FFFFFF;
            border-color: #979797;
        }

        .el-button--primary:hover {
            background-color: #bdb9b9;
            border-color: #979797;
        }

        .el-button--primary:focus {
            background-color: #4F80F7;
            border-color: #4F80F7;
            color: white !important;
        }

        .active {
            background-color: #4F80F7;
            border-color: #4F80F7;
            color: white !important;
        }

        .el-radio__inner {
            height: 16px;
            width: 16px;
        }

        .el-radio__label {
            font-size: 16px;
        }

        .el-checkbox__inner {
            height: 16px;
            width: 16px;
        }

        .el-checkbox__label {
            font-size: 16px;
        }

        .el-input__inner {
            width: 67%;
            margin-top: 2vh;
        }
    </style>
</head>

<body>
<div id="app">
    <div class="title">
        <div>
            <p>数字化考试云平台</p>
        </div>

    </div>
    <div style="display: flex;height: 95vh; ">
        <div style="width: 20%; padding-top:2vw; background-color: #F2F8FF; padding-left: 5%;">
            <div style="background-color: #FFFFFF;
                height: 14vh; border-radius: 12px; box-shadow: 0px 0px 9px 5px rgba(38,110,255,0.1); width: 85%;">
                <div style="text-align: center;
                    font-weight: 600;
                    font-size: 22px; padding-top: 4%; color: #4F80F7; 
                    font-family: cursive;" v-if="examinationTime">
                    <p><i class="el-icon-time" style="font-size: 26px;"></i>&nbsp剩余时间</p>
                    <div style=" height: 5vh; background: #4F80F7;
                        border-radius: 6px; width: 60%; margin: 2vh auto 0;line-height: 5vh; color: #FFFFFF; font-size: 20px; cursor: pointer;"
                         @click="begin" v-if="beginBtn">开始答题
                    </div>
                    <div style=" height: 6vh; background: #F6F9FC;
                    border-radius: 6px; width: 80%; margin: 2vh auto 0;line-height: 6vh; color: #4F80F7; font-size: 24px;"
                         v-if="exTiam">
                        {{ `${hr}: ${min}: ${sec}` }}
                    </div>
                </div>
            </div>
            <div style="
                background: #FFFFFF;
                box-shadow: 0px 0px 9px 5px rgba(38,110,255,0.1);
                border-radius: 12px;
                margin: 2vh 0 0;
                width: 85%;
                min-height: 12vh;
                border-radius: 12px;
                padding-top: 1px; padding-bottom: 5px;">
                <div style="padding-top: 10px; padding-left: 1vw;">考号：{{this.userCode}}</div>
                <div style="padding-top: 10px;padding-left: 1vw;">姓名：{{this.userName}}</div>
                <div style="padding-top: 10px; padding-left: 1vw;">身份证号：{{this.userCard}}</div>
                <div style="padding-top: 10px; padding-left: 1vw;">所属团队：{{this.userCard}}</div>
            </div>

            <!-- 题目数量 -->
            <div
                    style="margin-top: 5%; height: 50vh; overflow: auto; background: #FFFFFF; width: 85%; box-shadow: 0px 0px 9px 5px rgba(38,110,255,0.1); border-radius: 12px; padding-top: 2%;">
                <div v-for="(item, index) in topicList" :key="index" @click="urlClicks(item)" v-if="topicMutex">
                    <div
                            style="text-align: center; width: 40%; background-color: #FFFFFF; border: 1px solid #4F80F7; margin: 2vh auto 0; border-radius: 6px; height: 3vh; line-height: 3vh;">
                        {{item.subject_type}}
                    </div>
                    <div style="display: flex; flex-wrap: wrap;">
                        <div v-for="(ite,ind) in item.title_list" @click="topicBtn(ite,ind,item.subject_type,item)">
                            <el-button style="height: 40px;margin:20px 0 0 20px; color: #666666;"
                                       :class="{'active': topicId === ite.title_id ||ite.state==true||ite.is_answer==true}"
                                       type="primary">
                                {{ite.data_sequence}}
                            </el-button>
                        </div>
                    </div>
                </div>
                <div v-for="(item, index) in topicList" :key="index" @click="urlClicks(item)" v-if="topicMutexs">
                    <div
                            style="text-align: center; width: 40%; background-color: #FFFFFF; border: 1px solid #4F80F7; margin: 2vh auto 0; border-radius: 6px; height: 3vh; line-height: 3vh;">
                        {{item.subject_type}}
                    </div>
                    <div style="display: flex; flex-wrap: wrap;">
                        <div v-for="(ite,ind) in item.title_list"
                             @click="topicBtnFinish(ite,ind,item.subject_type,item)">
                            <el-button style="height: 40px;margin:20px 0 0 20px; color: #666666;"
                                       :class="{'active': topicId === ite.title_id ||ite.state==true||ite.is_answer==true}"
                                       type="primary">
                                {{ite.data_sequence}}
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
            <el-button type="primary" style="width: 85%; margin-top: 4%; height: 5.5vh; background-color: #4F80F7;cursor: pointer; font-size: 22px;font-weight: 600;
                 text-align: center; color: #FFFFFF; border-radius: 6px;" @click="submit"
                       v-if="submitBtn">交卷
            </el-button>
            <el-button type="info" disabled style="width: 85%; margin-top: 4%; height: 5.5vh; background-color: #7e9de9; font-size: 22px;font-weight: 600;
                    text-align: center; color: #FFFFFF; border-radius: 6px;" v-if="befores">交卷
            </el-button>
            <el-button :disabled="true" type="primary" style="width: 85%; margin-top: 4%; height: 6.5vh; background-color: rgb(79 128 247 / 72%); font-size: 15px;font-weight: 600;
                 text-align: center; color: #e7e5e5; border-radius: 6px;" v-if="submitBtns">总分：{{score}}分 <br/>
                总得分：{{totalScore}}分
            </el-button>
        </div>
        <div style="    width: 79.5%;
            background-color: #F2F8FF;
            padding: 2vw 0 0 0;" v-if="fetchStateBack">
            <div style="width: 90%; height: 88vh; background: #FAFAFA;
                    box-shadow: 0px 0px 9px 5px rgba(38,110,255,0.1);
                    border-radius: 12px;">
                <div
                        style="width: 97%; 
                    border-radius: 12px 12px 0px 0px; background: #DCECFE; height: 5vh; font-size: 18px; line-height: 5vh; padding-left: 3%; font-weight: 600;">
                    <div v-if="topicMutex">
                        {{topicType}}
                    </div>
                    <div v-if="topicMutexs">
                        {{topicType}}
                    </div>
                </div>
                <div style="width: 100%; height: 74vh; padding-top: 2vh; padding-left: 3%;">
                    <div style="width: 97%;
                        font-size: 19px;
                        font-weight: 600;" v-if="topicMutex">
                        {{topicObj.data_sequence}}、{{topicObj.title}}
                    </div>

                    <div style="display: flex">
                        <div v-for="(item,index) in topicObj.image_datas" v-if="topicMutex"
                             style="width:15vw;height:10vw;box-sizing: border-box;margin-right: 20px;">
                            <div style="width: 100%;height: 100%;display: flex;justify-content: center;align-items: center;">
                                <img :src="item.img_url"
                                     alt="" style="max-width: 100%;max-height: 100%;object-fit: scale-down;">
                            </div>
                        </div>
                    </div>

                    <div v-if="topicMutex" style="display: flex; flex-direction: column;">
                        <el-radio v-for="(ite,ind) in topicObj.options" v-model="ite.is_choose" :label="true"
                                  :key="ite.option" @change.native="choose(ite,ind,topicObj)"
                                  v-if="examination">{{ite.option}}.{{ite.option_name}}
                        </el-radio>

                        <el-checkbox v-for="(ite,ind) in topicObj.options" v-model="ite.is_choose" :key="ite.option"
                                     @change.native="chooseMany(ite,topicObj.options)"
                                     v-if="examinationDuo">{{ite.option}}.{{ite.option_name}}
                        </el-checkbox>
                        <el-input  type="textarea" :rows="10"
                                  placeholder="请输入内容" @input="textBtn(topicObj.textarea,topicObj)"
                                  v-model="topicObj.textarea" v-if="examinationPan"
                                  style="width: 95%; margin: 2vh auto 0;">
                        </el-input>
                        <el-input @input="fillBtn(topicObj.answer_detail)" v-model="item.real_answer"
                                  placeholder="请输入答案" v-for="(item,index) in topicObj.answer_detail"
                                  v-if="examinationTian"></el-input>
                    </div>

                    <div style="width: 97%;
                            font-size: 19px;
                            font-weight: 600;" v-if="topicMutexs">
                        {{topicObj.data_sequence}}、{{topicObj.title}}
                    </div>
                    <div v-if="topicMutexs">
                        <el-radio v-for="(ite,ind) in topicObj.options" v-model="ite.is_choose" disabled
                                  :label="true" :key="ite.option" @change.native="choose(ite,ind,topicObj)"
                                  v-if="examination">{{ite.option}}.{{ite.option_name}}
                        </el-radio>

                        <el-checkbox v-for="(ite,ind) in topicObj.options" v-model="ite.is_choose" disabled
                                     :key="ite.option" @change.native="chooseMany(ite,topicObj.options)"
                                     v-if="examinationDuo">{{ite.option}}.{{ite.option_name}}
                        </el-checkbox>
                        <el-input :disabled="true" type="textarea" :rows="10" placeholder="请输入内容"
                                  v-model="topicObj.real_content" v-if="examinationPan"
                                  style="width: 95%; margin: 2vh auto 0;">
                        </el-input>
                        <el-input :disabled="true" v-model="item.real_answer"
                                  v-for="(item,index) in topicObj.answer_detail" v-if="examinationTian"></el-input>
                    </div>
                    <el-button type="primary" style="width: 17%;float: right; margin-right: 15%; margin-top: 4%;  background-color: #4F80F7;cursor: pointer; font-size: 16px;font-weight: 600;
                        text-align: center; color: #FFFFFF; border-radius: 6px;"
                               @click="seeAnswer(topicObj.options,topicObj,topicObj.answer_detail)"
                               v-if="topicMutexss">查看答案
                    </el-button>
                    <div style="    width: 50%;
                        min-height: 10vh;
                        background-color: rgb(242 242 242);
                        margin-top: 3%;
                        padding-top: 2%;
                        padding-left: 2%; word-break: break-all;border-radius: 5px;" v-if="seeAnswerMutex">
                        <div style="display: flex;">

                            <div>该题的正确答案是：</div>
                            <div v-for='(item,index) in seeAnswerText' v-if="panduan"
                                 style="width: 50%; text-align: initial;">{{item.option}}
                            </div>
                            <div v-if="!panduan">{{seeAnswerText}}{{tiankongAnswer}}</div>
                        </div>
                        <div style="margin-top: 2vh;">
                            <div>答案解析:{{answerParsing}}</div>
                        </div>


                    </div>

                </div>

                <div
                        style="width: 100%; height: 7vh; background-color: #FAFAFA; display: flex; justify-content: flex-end; align-items: center; font-family: cursive; font-weight: 600; border-radius: 0 0 12px 12px;">
                    <div style="width: 15%; height: 5vh; background: #DCECFE; border-radius: 6px;
                        font-size: 18px; align-items: center; text-align: center;color: #4F80F7; line-height: 5vh; margin-right: 3vw; cursor: pointer;"
                         @click="last">
                        上一题
                    </div>
                    <div style="width: 15%; height: 5vh; background: #4F80F7; border-radius: 6px;
                        font-size: 18px; align-items: center; text-align: center; color: #FFFFFF; line-height: 5vh; margin-right: 2vw; cursor: pointer;"
                         @click="next">
                        下一题
                    </div>
                </div>
            </div>

            <div style="font-size: 22px;
                font-weight: 600; font-family: cursive;" v-if="examinationTitle">{{this.topicTitle}}
            </div>
            <div style="margin: 8vw auto 0;
                width: 15vw;
                font-size: 27px;
                font-family: cursive;
                font-weight: 600;" v-if="off">{{this.offvalue}}
            </div>


            <div style="font-size: 20px;
                background-color: #ededed;
                padding: 15px;
                border: 2px #d5cccc solid;
                margin-top: 1vw;
                box-shadow: 11px 8px 9px #c3c9c8;
                border-radius: 20px; width: 77vw;" v-if="examinationss">
                {{this.titles}}
            </div>
            <div style="    margin-top: 1vw;
                font-size: 17px;
                font-family: cursive;
                font-weight: 600;" v-if="examinationss">
                {{this.remark}}
            </div>
            <!-- <div v-drag class="move_box" v-if="examinationss">
                <iframe :src="url" width="100%" height="600px" scrolling="yes" frameborder="0"></iframe>
            </div> -->
        </div>
    </div>
</div>
</body>


<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/vue.min.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/index.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/axios.min.js"></script>

<script>

    var Main = {
        directives: {
            drag(el) {
                // 鼠标移动到目标盒子上--监听鼠标按下事件
                el.onmousedown = function (e) {
                    // 计算出此时点击目标盒子
                    var disx = e.offsetX
                    var disy = e.offsetY

                    document.onmousemove = function (e2) {
                        var move_box = document.getElementsByClassName('move_box')[0]
                        move_box.style.position = 'fixed'
                        // move_box.style.left = e2.clientX - disx + 'px'
                        move_box.style.top = e2.clientY - disy + 'px'
                    }
                    document.onmouseup = function () {
                        document.onmousemove = document.onmouseup = null
                    }
                }
            }
        },
        data() {
            return {
                befores: true,
                submitBtns: false,
                panduan: false,
                topicMutexs: false,
                topicMutexss: false,
                seeAnswerMutex: false,
                seeAnswerText: '',
                submitBtn: false,
                topicMutex: false,
                textarea: '',
                indeT: '',
                beginBtn: true,
                exTiam: false,
                isshow1: true,
                isshow2: false,
                hr: 0,
                min: 0,
                sec: 0,
                tabPosition: 'left',
                value: '',
                input: '',
                num: 1,
                title: "",
                data_standardList: [],
                text: '',
                url: '',
                list: [],
                activeName: '1',
                tabPosition: 'left',
                btnShow: false,
                content: '',
                fileld: '',
                id: '',
                mark: '',
                model: '',
                getTime: '',
                num1: "",
                num2: "",
                num3: "",
                isshows: false,
                icon: false,
                type: '',
                userId: '',
                studentId: '',
                //考生姓名
                userName: '',
                //账号
                userCode: '',
                //身份证
                userCard: '',
                shiCaoArray: [],
                keGuanArray: [],
                duoxuanArray: [],
                topicArray: [],
                inde: '',
                startTime: '',
                endTime: '',
                time: '',
                odd: false,
                radio: '',
                it: '',
                examination: true,
                examinations: false,
                examinationss: false,
                examinationDuo: false,
                examinationTitle: false,
                examinationPan: false,
                examinationTian: false,
                answerArray: [],
                remark: '',
                off: true,
                // offvalue: '请点击开始答题',
                correct: '',
                answer: false,
                topicTitle: '',
                checkboxGroup1: [],
                checkboxData: [],
                answers: [],
                duoxuanAnswer: [],
                tianzai: '',
                listMany: [],
                panduanArray: [],
                judgment: [],
                examId: '',
                timer: null,
                fetchStateBack: true,
                hrStop: "",
                minStop: "",
                secStop: "",
                yearMonth: '',
                openTime: true,
                examinationTime: true,
                topicList: [],
                topicObj: '',
                topicId: '',
                titleId: '',
                titleData: '',
                topicType: '',
                topicIndex: 0,
                topicTypeIndex: 0,
                answerText: '',
                score: '',
                totalScore: '',
                answerMutex: false,
                tianText: '',
                userTeam: "",
                answerParsing: '',
                answerDetail: [],
                tiankongAnswer: ''
            }
        },
        created() {
        },

        mounted() {
            this.userId = parseInt(this.GetRequest().id)
            this.timer = setInterval(() => {
                this.fetchState(this.userId)
            }, 5000)
            if (this.GetRequest().state == '已交卷' || this.GetRequest().state == '等待主观评分') {
                this.finish()
                this.beginBtn = false
                this.submitBtn = false
                clearInterval(this.timer)
            } else {
                this.getTopic()
                // this.beginBtn = true
                // this.submitBtn = true
            }
            if (this.type == "practice") {
                this.answer = true
            }

        },
        watch: {
            hr(newVal, oldVal) {
                this.num1 = setInterval(function () {
                }, 1000);

            },
            min(newVal, oldVal) {
                this.num2 = setInterval(function () {
                }, 1000);

            },
            sec(newVal, oldVal) {
                this.num3 = setInterval(function () {
                }, 1000);

            },
            topicId(newVal, oldVal) {
                this.titleId = newVal
            }
        },
        methods: {
            fillBtn(item) {
                console.log(item);
                // console.log("222");
                this.answerDetail = item
                this.topicObj.state = true
            },
            seeAnswer(item, ite, val) {
                console.log(item);
                console.log(ite);
                console.log(val);
                this.tiankongAnswer=''
                // console.log(val.hasOwnProperty(val.real_answer))
                if (val!=undefined) {
                    this.tiankongAnswer = val.map(v => {
                        console.log(v.true_answer);
                        return v.true_answer
                    })
                    this.tiankongAnswer = this.tiankongAnswer.toString()
                }

                this.answerParsing = ite.answer_analysis
                if (item == undefined) {
                    this.panduan = false
                    this.seeAnswerText = ite.content
                    console.log(this.seeAnswerText)
                } else {
                    this.panduan = true
                    this.seeAnswerText = item.filter(ite => {
                        return ite.is_correct == true
                    })
                }
                console.log(this.seeAnswerText);

                this.seeAnswerMutex = true
            },
            finish() {
                axios.request({
                    url: '/roke/get_done_exam_detail',
                    method: 'post',
                    data: {
                        exam_id: this.userId,
                        student_id: parseInt(this.GetRequest().studentId)
                    }
                }).then(res => {
                    console.log(res);
                    this.userName = res.data.result.exam_info.student_name
                    this.userCode = res.data.result.exam_info.student_code
                    this.userCard = res.data.result.exam_info.student_card_code
                    // this.userTeam=
                    this.topicList = res.data.result.data
                    this.topicObj = res.data.result.data[0].title_list[0]
                    this.titleId = res.data.result.data[0].title_list[0].title_id
                    if (res.data.result.exam_info.is_can_see_true_answer) {
                        this.topicMutexss = true
                    }
                    if (res.data.result.exam_info.is_can_see_score) {
                        this.score = res.data.result.exam_info.total_marks
                        this.totalScore = res.data.result.exam_info.exam_score
                        this.submitBtns = true
                        this.befores = false
                    }
                    this.topicMutex = false
                    this.topicMutexs = true
                    this.topicType = res.data.result.data[0].subject_type
                    console.log(this.topicType);
                    if (this.topicType == "单选题") {
                        this.examination = true
                        this.examinationDuo = false
                        this.examinationPan = false
                        this.examinationTian = false
                    } else if (this.topicType == "多选题") {
                        this.examination = false
                        this.examinationDuo = true
                        this.examinationPan = false
                        this.examinationTian = false
                    } else if (this.topicType == "判断题") {
                        this.examination = true
                        this.examinationDuo = false
                        this.examinationPan = false
                        this.examinationTian = false
                    } else if (this.topicType == "填空题") {
                        console.log("触发");
                        this.examination = false
                        this.examinationDuo = false
                        this.examinationPan = false
                        this.examinationTian = true
                    } else {
                        this.examination = false
                        this.examinationDuo = false
                        this.examinationTian = false
                        this.examinationPan = true
                    }
                })
            },
            last() {
                this.seeAnswerMutex = false
                axios.request({
                    url: "/roke/save_answer",
                    method: "post",
                    data: {
                        exam_id: this.userId,
                        title_id: this.topicList[this.topicTypeIndex].title_list[this.topicIndex].title_id,
                        student_id: parseInt(this.GetRequest().studentId),
                        answer: this.answerText,
                        choose: this.answerArray[0],
                        choose_list: this.duoxuanAnswer,
                        answerState: this.answerMutex,
                        answer_detail: this.answerDetail
                    }
                }).then(res => {
                    console.log(res);
                    this.answerArray = []
                    this.duoxuanAnswer = []
                    this.answer = ""
                    this.answerText=''
                     this.answerMutex = false
                })
                if (this.topicTypeIndex == 0 && this.topicIndex <= 0) {
                    this.topicIndex = 0
                } else {
                    if (this.topicIndex == 0) {
                        this.topicTypeIndex--
                        this.topicIndex = this.topicList[this.topicTypeIndex].title_list.length - 1
                    } else {
                        this.topicIndex--
                    }
                }
                this.topicType = this.topicList[this.topicTypeIndex].subject_type
                if (this.topicList[this.topicTypeIndex].subject_type == '多选题') {
                    this.examination = false
                    this.examinationDuo = true
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '判断题') {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '单选题') {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '填空题') {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = true
                }
                this.titleId = this.topicList[this.topicTypeIndex].title_list[this.topicIndex].title_id
                this.topicObj = this.topicList[this.topicTypeIndex].title_list[this.topicIndex]

            },
            next() {
                this.seeAnswerMutex = false
                axios.request({
                    url: "/roke/save_answer",
                    method: "post",
                    data: {
                        exam_id: this.userId,
                        title_id: this.topicList[this.topicTypeIndex].title_list[this.topicIndex].title_id,
                        student_id: parseInt(this.GetRequest().studentId),
                        answer: this.answerText,
                        choose: this.answerArray[0],
                        choose_list: this.duoxuanAnswer,
                        answerState: this.answerMutex,
                        answer_detail: this.answerDetail
                    }
                }).then(res => {
                     this.answerText=''
                     this.answerMutex = false
                    this.answerArray = []
                    this.duoxuanAnswer = []
                    this.answer = ""
                })
                if (this.topicList.length - 1 == this.topicTypeIndex && this.topicIndex == this.topicList[this.topicTypeIndex].title_list.length - 1) {
                    this.topicIndex--
                }
                if (this.topicList[this.topicTypeIndex].title_list.length - 1 == this.topicIndex) {
                    this.topicTypeIndex++
                    this.topicIndex = 0
                } else {
                    this.topicIndex++
                }
                this.topicType = this.topicList[this.topicTypeIndex].subject_type
                if (this.topicList[this.topicTypeIndex].subject_type == '多选题') {
                    this.examination = false
                    this.examinationDuo = true
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '判断题') {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '主观题') {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = true
                    this.examinationTian = false
                } else if (this.topicList[this.topicTypeIndex].subject_type == '填空题') {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = true
                }
                this.titleId = this.topicList[this.topicTypeIndex].title_list[this.topicIndex].title_id
                this.topicObj = this.topicList[this.topicTypeIndex].title_list[this.topicIndex]

            },
            submit() {
                this.$confirm('是否继续交卷?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.submits()
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消交卷'
                    });
                });
            },
            submits() {
                if (this.answerText.length == 0 && this.answerArray[0] == undefined && this.duoxuanAnswer.length == 0) {
                    axios.request({
                        url: "/roke/end_exam",
                        method: "post",
                        data: {
                            exam_id: this.userId,
                            student_id: parseInt(this.GetRequest().studentId)

                        }
                    }).then(res => {
                        console.log(res);
                        if (res.data.result.state == 'success') {
                            this.$message({
                                message: res.data.result.msgs,
                                type: 'success'
                            });
                            if (res.data.result.pattern_state == "wait_exam") {
                                var type = 'exam'
                            } else if (res.data.result.pattern_state == "not_done") {
                                var type = 'practice'
                            }
                            window.location.replace('../../../../roke_education_manager/static/index/index/hot.html?name=' + this.userName + '&id=' + parseInt(this.GetRequest().studentId) + '&type=' + type + "&state=" + res.data.result.pattern_state);
                        } else {
                            this.$message.error(res.data.result.msgs)
                        }
                    })
                } else {
                    axios.request({
                        url: "/roke/save_answer",
                        method: "post",
                        data: {
                            exam_id: this.userId,
                            title_id: this.titleId,
                            student_id: parseInt(this.GetRequest().studentId),
                            answer: this.answerText,
                            choose: this.answerArray[0],
                            choose_list: this.duoxuanAnswer,
                            answerState: this.answerMutex,
                            answer_detail: this.answerDetail
                        }
                    }).then(res => {
                        console.log(res);
                        if (res.data.result.state == "success") {
                            this.topicMutex = false
                            this.answerArray = []
                            this.duoxuanAnswer = []
                             this.answerText=''
                            this.answer = ""
                             this.answerMutex = false
                            axios.request({
                                url: "/roke/end_exam",
                                method: "post",
                                data: {
                                    exam_id: this.userId,
                                    student_id: parseInt(this.GetRequest().studentId)
                                }
                            }).then(res => {
                                console.log(res);
                                if (res.data.result.state == 'success') {
                                    this.$message({
                                        message: res.data.result.msgs,
                                        type: 'success'
                                    });
                                    if (res.data.result.pattern_state == "wait_exam") {
                                        var type = 'exam'
                                    } else if (res.data.result.pattern_state == "not_done") {
                                        var type = 'practice'
                                    }
                                    window.location.replace('../../../../roke_education_manager/static/index/index/hot.html?name=' + this.userName + '&id=' + parseInt(this.GetRequest().studentId) + '&type=' + type + "&state=" + res.data.result.pattern_state);
                                } else {
                                    this.$message.error(res.data.result.msgs)
                                }
                            })
                        } else {
                            this.$message.error(res.data.result.msgs)
                        }
                    })
                }
            },
            textBtn(text,val) {
                console.log(val)
                console.log(text)
                console.log(this.topicObj.textarea)
                if (this.tianText != text && text == '') {
                    console.log("zoule")
                    this.answerMutex = true
                }else{
                    this.answerMutex = false
                }
                this.topicObj.state = true
                this.answerText = text
            },
            topicBtnFinish(ite, ind, type, item) {
                var index = this.topicList.findIndex(item => item.subject_type === type)
                this.topicIndex = ind
                this.topicTypeIndex = index
                this.seeAnswerMutex = false
                this.topicId = ite.title_id
                this.topicObj = ite
                this.topicType = type
                if (type == "单选题") {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "多选题") {
                    this.examination = false
                    this.examinationDuo = true
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "判断题") {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "填空题") {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = true
                } else {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = true
                    this.examinationTian = false
                }
            },
            topicBtn(ite, ind, type, item) {
                this.seeAnswerMutex = false
                this.topicId = ite.title_id
                var index = this.topicList.findIndex(item => item.subject_type === type)
                this.topicObj = ite
                this.topicType = type
                this.topicIndex = ind
                this.topicTypeIndex = index
                if (type == "单选题") {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "多选题") {
                    this.examination = false
                    this.examinationDuo = true
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "判断题") {
                    this.examination = true
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = false
                } else if (type == "填空题") {
                    console.log("触发");
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationPan = false
                    this.examinationTian = true
                } else {
                    this.examination = false
                    this.examinationDuo = false
                    this.examinationTian = false
                    this.examinationPan = true
                    this.tianText = JSON.parse(JSON.stringify(ite.textarea))
                    console.log(this.tianText)
                }

                axios.request({
                    url: "/roke/save_answer",
                    method: "post",
                    data: {
                        exam_id: this.userId,
                        title_id: this.titleId,
                        student_id: parseInt(this.GetRequest().studentId),
                        answer: this.answerText,
                        choose: this.answerArray[0],
                        choose_list: this.duoxuanAnswer,
                        answerState: this.answerMutex,
                        answer_detail: this.answerDetail
                    }
                }).then(res => {
                    // if (res.data.result.state == "success") {
                    this.answerArray = []
                    this.duoxuanAnswer = []
                     this.answerText=''
                     this.answerMutex = false
                    this.answer = ""
                    // } else {
                    //     this.$message.error(res.result.msgs)
                    // }
                })
            },
            getTopic() {
                axios.request({
                    url: "/roke/get_exam_detail",
                    method: 'post',
                    data: {
                        exam_id: this.userId,
                        student_id: parseInt(this.GetRequest().studentId)
                    }
                }).then(res => {
                    console.log(res);
                    if (res.data.result.state == "success") {
                        this.userName = res.data.result.exam_info.student_name
                        this.userCode = res.data.result.exam_info.student_code
                        this.userCard = res.data.result.exam_info.student_card_code
                        // this.userTeam=
                        this.topicList = res.data.result.data
                        this.topicObj = res.data.result.data[0].title_list[0]
                        this.titleId = res.data.result.data[0].title_list[0].title_id
                        this.endTime = res.data.result.exam_info.plan_end_time
                        if (res.data.result.exam_info.is_show_time == true && res.data.result.exam_info.state == "exam_taking") {
                            this.befores = false
                            this.divTime(this.endTime)
                            this.time = (Date.parse(new Date()) + ((this.time)) * 1000);
                            // 开始执行倒计时
                            this.countdown();
                            this.exTiam = true
                            this.submitBtn = true
                        }
                        if (res.data.result.exam_info.state == "exam_taking") {
                            this.befores = false
                            this.submitBtn = true
                        }
                        if (this.endTime != false) {
                            this.beginBtn = false
                            this.topicMutex = true
                        }
                        if (res.data.result.exam_info.state == "exam_taking") {
                            this.beginBtn = false
                            this.topicMutex = true
                        }
                        if (res.data.result.exam_info.is_can_see_true_answer && !this.beginBtn) {
                            this.topicMutexss = true
                        }
                        if (res.data.result.exam_info.is_can_see_score) {
                            this.score = res.data.result.exam_info.total_marks
                            this.totalScore = res.data.result.exam_info.exam_score
                            this.submitBtns = true
                        }

                        this.topicType = res.data.result.data[0].subject_type
                        console.log(this.topicType);
                        if (this.topicType == "单选题") {
                            this.examination = true
                            this.examinationDuo = false
                            this.examinationPan = false
                            this.examinationTian = false
                        } else if (this.topicType == "多选题") {
                            this.examination = false
                            this.examinationDuo = true
                            this.examinationPan = false
                            this.examinationTian = false
                        } else if (this.topicType == "判断题") {
                            this.examination = true
                            this.examinationDuo = false
                            this.examinationPan = false
                            this.examinationTian = false
                        } else if (this.topicType == "填空题") {
                            console.log("触发");
                            this.examination = false
                            this.examinationDuo = false
                            this.examinationPan = false
                            this.examinationTian = true
                        } else {
                            this.examination = false
                            this.examinationDuo = false
                            this.examinationTian = false
                            this.examinationPan = true
                            this.tianText = JSON.parse(JSON.stringify(ite.textarea))
                            console.log(this.tianText)
                        }

                        if (res.data.result.exam_info.state == "wait_exam") {

                        }
                    }
                })
            },
            viewAnswer(item) {
                let that = this
                var tian = item.filter(item => {
                    return item.is_correct == true
                })
                that.correct = tian[0]
                var result = tian.map(item => item.option);
                that.$message({
                    showClose: true,
                    message: "第" + that.correct.data_sequence + '题的正确答案是' + result,
                    duration: 2500
                });
            },
            timeStamp(second_time) {

                var time = parseInt(second_time) + "秒";
                if (parseInt(second_time) > 60) {

                    var second = parseInt(second_time) % 60;
                    var min = parseInt(second_time / 60);
                    time = min + "分" + second + "秒";
                    this.min = min
                    this.sec = second
                    if (min > 60) {
                        min = parseInt(second_time / 60) % 60;
                        var hour = parseInt(parseInt(second_time / 60) / 60);
                        time = hour + "小时" + min + "分" + second + "秒";
                        this.hr = hour
                        this.min = min
                        this.sec = second

                        if (hour > 24) {
                            hour = parseInt(parseInt(second_time / 60) / 60) % 24;
                            var day = parseInt(parseInt(parseInt(second_time / 60) / 60) / 24);
                            time = day + "天" + hour + "小时" + min + "分" + second + "秒";
                            this.hr = hour
                            this.min = min
                            this.sec = second
                        }
                    }
                }

                return time;
            },
            divTime(time1) {
                time1 = Date.parse(new Date(time1));
                time2 = Date.parse(new Date());
                this.time = Math.abs((time2 - time1) / 1000)
                var time = this.timeStamp(Math.abs((time2 - time1) / 1000))
            },
            //多选题目的事件
            chooseMany(ite, item) {
                this.topicObj.state = true
                this.duoxuanAnswer = item.filter(item => {
                    return item.is_choose == true
                })
            },
            //单选题目点击事件
            choose(item, ind, data) {
                data.options.forEach((value, index) => {
                    if (item != value) {
                        value.is_choose = false
                    }
                });

                if (this.answerArray.length == 0) {
                    this.answerArray.push(item)
                } else {
                    for (var i = 0; i < this.answerArray.length; i++) {
                        if (item.data_sequence == this.answerArray[i].data_sequence) {
                            this.answerArray.splice(i, 1)
                        }
                    }
                    this.answerArray.push(item)
                }
                this.topicObj.state = true
            },
            GetRequest() {
                var url = location.search; //获取url中"?"符后的字串
                url = decodeURI(url)
                var theRequest = new Object();
                if (url.indexOf("?") != -1) {
                    var str = url.substr(1);
                    strs = str.split("&");
                    for (var i = 0; i < strs.length; i++) {
                        theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
                return theRequest;
            },
            getDate() {
                var date = new Date() // 获取时间
                var year = date.getFullYear() // 获取年
                var month = date.getMonth() + 1  // 获取月
                var strDate = date.getDate() // 获取日
                // var day = date.getDate() //
                var day = '日一二三四五六'.charAt(new Date().getDay()) // 周一返回的是1，周六是6，但是周日是0
                var hour = date.getHours() // 获取小时
                var minute = date.getMinutes() // 获取分钟
                var second = date.getSeconds() // 获取秒
                // 由于部分业务处理 是需要月份日份前面有0 故新增一个函数
                this.getNum()
                this.yearMonth = year +
                    '-' +
                    this.getNum(month) +
                    '-' +
                    this.getNum(strDate) + " " + this.getNum(hour) + ':' + this.getNum(minute) + ':' + this.getNum(second)


            },
            getNum(i) {
                return i < 10 ? '0' + i : i
            },

            begin() {
                let that = this
                axios.request({
                    url: "/roke/start_exam",
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        exam_id: this.userId,
                        student_id: parseInt(this.GetRequest().studentId)
                    }
                }).then(res => {
                    // that.$message({
                    //     type: 'success',
                    //     message: res.data.result.msgs
                    // })
                    // this.time = Date.parse(new Date(res.data.result.end_time));
                    if (res.data.result.state == "error") {
                        this.$message.error(res.data.result.msgs)
                    } else {
                        that.endTime = res.data.result.plan_end_time
                        that.divTime(that.endTime)
                        this.time = (Date.parse(new Date()) + ((that.time)) * 1000);
                        // 开始执行倒计时
                        this.countdown();
                        this.beginBtn = false
                        // this.exTiam = true
                        this.topicMutex = true
                        this.submitBtn = true
                        this.befores = false
                        this.getTopic()
                    }


                });

            },
            countdown() {
                const end = this.time; // 定义结束时间
                const now = Date.parse(new Date()); // 获取本地时间
                const msec = end - now; // 定义总共所需的时间
                // 将时间戳进行格式化
                let hr = parseInt(msec / 1000 / 60 / 60 % 24);
                let min = parseInt(msec / 1000 / 60 % 60);
                let sec = parseInt(msec / 1000 % 60);
                // 倒计时结束时的操作
                const that = this;
                if (this.hr == 0 && this.min == 0 && this.sec == 1) {
                    this.isshows = true
                    this.isshow2 = false
                    this.icon = false
                    that.examination = false
                    that.examinations = false
                    that.examinationss = false
                    that.examinationDuo = false
                    that.examinationPan = false
                    that.examinationTitle = false
                    that.examinationTian = false
                    console.log("时间到了");
                    this.submits()
                } else if (this.hr == 0 && this.min == 1 && this.sec == 0) {
                    that.$message({
                        // type: 'success',
                        message: '时间还剩一分钟,请考生注意答题时间'
                    });
                    this.hr = hr > 9 ? hr : '0' + hr;
                    this.min = min > 9 ? min : '0' + min;
                    this.sec = sec > 9 ? sec : '0' + sec;
                    setTimeout(that.countdown, 1000)
                } else {
                    // 如时间未归零则继续在一秒后执行
                    this.hr = hr > 9 ? hr : '0' + hr;
                    this.min = min > 9 ? min : '0' + min;
                    this.sec = sec > 9 ? sec : '0' + sec;
                    setTimeout(that.countdown, 1000)
                }
            },
            open() {
                let that = this
                that.$confirm('即将结束答题, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then((action) => {
                    if (action === 'confirm') {
                        clearInterval(that.num1);
                        clearInterval(that.num2);
                        clearInterval(that.num3);
                        clearInterval(that.timer)
                        that.hr = 0;
                        that.min = 0;
                        that.sec = 0;

                        that.examination = false
                        that.examinations = false
                        that.examinationss = false
                        that.examinationTitle = false
                        that.examinationDuo = false
                        that.examinationPan = false
                        that.examinationTian = false
                        that.isshow2 = false
                        that.isshows = true
                        this.icon = false
                        this.$message({
                            type: 'info',
                            message: '已交卷'
                        });
                        axios.request({
                            url: "/roke/student_end_exam ",
                            method: "post",
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: {
                                "student_id": that.userId,
                                "pattern_type": that.type,
                                "objective_data": that.answerArray,
                                "checkbox_data": that.duoxuanArray,
                                "judgment_data": that.judgment,
                                'exam_id': that.examId
                            }
                        }).then(function (res) {
                            that.$message({
                                type: 'success',
                                message: res.data.result.msgs
                            })
                        });
                    }

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消交卷'
                    });
                });
            },
            handleTabsChange(tab) {
                this.$store.commit('handleTabsChange', tab.name)
            },
            handleChange(value) {
            },
            fetchState(examId) {
                let that = this
                axios.request({
                    url: '/roke/query_exam_state',
                    method: "post",
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        'exam_id': examId
                    }
                }).then(res => {
                    if (res.data.result.data == 'compel_over_exam') {
                        clearInterval(that.num1);
                        clearInterval(that.num2);
                        clearInterval(that.num3);
                        clearInterval(that.timer)
                        that.hr = 0;
                        that.min = 0;
                        that.sec = 0;
                        that.examination = false
                        that.examinations = false
                        that.examinationss = false
                        that.examinationTitle = false
                        that.examinationDuo = false
                        that.examinationPan = false
                        that.examinationTian = false
                        that.isshow2 = false
                        that.isshows = true
                        this.icon = false
                        this.submits()
                    } else if (res.data.result.data == 'exam_suspend') {
                        // that.fetchStateBack = false
                        this.openTime = false
                        if (that.heStop == "" && that.min == "") {
                            that.heStop = JSON.stringify(that.hr)
                            that.minStop = JSON.stringify(that.min)
                            that.secStop = JSON.stringify(that.sec)
                        }
                        that.$message({
                            message: '考试已被考官暂停，请稍等',
                            duration: 2000,
                            type: 'error'
                            // showClose: true,
                        });
                        this.topicMutex = false
                        this.examinationTime = false
                    } else if (res.data.result.data == 'suspend_continue') {
                        // that.fetchStateBack = true
                        that.hr = that.heStop;
                        that.min = that.minStop;
                        that.sec = that.secStop;
                        that.$message({
                            message: '考试已恢复，请继续答题',
                            duration: 0,
                            showClose: true,
                            type: 'success'
                        });
                        this.beginBtn = false
                        this.topicMutex = true
                        this.openTime = true
                        this.examinationTime = true
                    } else if (res.data.result.data == 'exam_delayed') {
                        this.getDate()
                        this.divTime(res.data.result.end_time, this.yearMonth)
                        this.time = Date.parse(new Date(res.data.result.end_time));
                        that.$message({
                            message: '考试延时' + res.data.result.delayed_time + "分钟",
                            duration: 2000,
                        });

                    }
                })
            },
            urlClicks(item) {
                this.titles = item.title
            },
            urlClick(item) {
                this.off = false
                this.url = item.url
                this.remark = item.remark
                this.examinationss = true
            },
        },
        beforeDestroy: function () {
            // clearInterval(this.timer);
        }
    };
    var Ctor = Vue.extend(Main);
    new Ctor().$mount('#app')
</script>

</html>