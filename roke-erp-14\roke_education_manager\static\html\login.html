<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>作业平台</title>
    <!-- <link rel="stylesheet" href="/roke_mes_production/static/src/css/work_report/index.css"> -->
<!--            margin: 0;-->
<!--            margin: 0;-->
    <link rel="stylesheet" href="../../../roke_mes_production/static/src/css/work_report/index.css">
    <style>
        body {
            margin: 0;
        }

        html,
        body,
        #app {
            height: 100%;
        }

        #app {
            display: flex;
            justify-content: center;
            align-items: center;
            /*background-color: #636363;*/
            background: url("../../../roke_mes_production/static/src/images/bg.png") no-repeat center;
            background-size: 100% 100%;
        }

        #container {
            width: 50%;
            padding: 1rem 2rem;
            border: 1px solid #ddd;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;
            background-color: #fff;
        }

        .con {
            display: flex;
            width: 70%;
            margin: 2rem auto;
            justify-content: center;
            align-items: center;
        }

        .el-input.is-active .el-input__inner,
        .el-input__inner:focus,
        .el-button--primary,
        .el-button--primary:hover,
        .el-button--primary:active {
            border-color: #01875D;
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="container">
            <div style="display: flex;justify-content: center;align-items: center;margin: 2rem 0;">
                <!-- <img src="../src/images/logo.png" alt=""> -->
                <p style="color: green; font-weight: 600; letter-spacing: 6px; font-family: cursive;font-size: 27px;">
                    前厅服务与管理技能考评系统</p>
            </div>
            <div class="con">
                <img src="../../../roke_mes_production/static/src/images/user.png" alt="" style="width: 24px;height: 24px;margin-right: 13px;">
                <el-input style="width: 70%" placeholder="请输入账号" v-model="user" clearable></el-input>
            </div>
            <div class="con">
                <img src="../../../roke_mes_production/static/src/images/pwd.png" alt="" style="width: 30px;height: 30px;margin-right: 10px;">
                <el-input style="width: 70%" placeholder="请输入密码" v-model="pwd" show-password></el-input>
            </div>
            <div class="con">
                <el-button type="primary" @click="jump"
                    style="background-color: #01875D;width: 79%;margin: 1rem auto;">登录</el-button>
            </div>
        </div>
    </div>
</body>

<script type="text/javascript" src="../../../roke_mes_production/static/src/js/work_report/vue.min.js"></script>
<script type="text/javascript" src="../../../roke_mes_production/static/src/js/work_report/index.js"></script>
<script type="text/javascript" src="../../../roke_mes_production/static/src/js/work_report/axios.min.js"></script>
<script>
    var Main = {
        data() {
            return {
                user: '',
                pwd: '',
                type:''
            }
        },
        mounted() {
            let that = this;
            // axios.request({
            //     url: "http://odoo14.rokedata.com/roke/mes/check_login_state",
            //     method: "get"
            // }).then(function (res) {
            //     // console.log(res);
            //     if(res.data.state === 'success'){
            //         localStorage.setItem('user_name',res.data.result.user_name);
            //         localStorage.setItem('started',res.data.started);
            //         window.location.replace('detail.html');
            //     }
            // });
        },
        methods: {
            jump() {
                let that = this;
                if (that.user === '' || that.pwd === '') {
                    that.$message({
                        type: 'error',
                        message: '请输入账号密码！'
                    });
                } else {
                    axios.request({
                        url: "http://localhost:8069/roke/mes/client_login",
                        method: "post",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify({
                            "login": that.user,
                            "password": that.pwd,
                            "type": that.type
                        })
                    }).then(function (res) {
                        console.log(res);
                        if (res.data.result.state === 'error') {
                            that.$message({
                                type: 'error',
                                message: res.data.result.msgs
                            });
                        } else {
                            localStorage.setItem('user_id', res.data.result.data.user_id)
                            localStorage.setItem('type', that.type);
                            window.location.replace('http://localhost:8069/roke/student/exam/html?id='+res.data.result.data.user_id+ "&type="+that.type);
                        }
                    });
                }
                // window.location.replace('file:///D:/Tabloids/server/ksgl-hotel-manager/roke_education_manager/static/html/detail_answer.html');
            },
             GetRequest() {
                var url = location.search; //获取url中"?"符后的字串
                var theRequest = new Object();
                if (url.indexOf("?") != -1) {
                    var str = url.substr(1);
                    strs = str.split("&");
                    for (var i = 0; i < strs.length; i++) {
                        theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
                return theRequest;
            }
        },
        mounted(){
            this.type=this.GetRequest().type
        }
    };
    var Ctor = Vue.extend(Main);
    new Ctor().$mount('#app')
</script>

</html>