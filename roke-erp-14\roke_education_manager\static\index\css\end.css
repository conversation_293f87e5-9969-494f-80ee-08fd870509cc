/* 底部 */
.bottom {
    width: 100%;
    background: #1e1d1d;
    position: relative;
}
.bottom .bottom-first {
    width: 1200px;
    min-width: 1300px;
    margin: 0 auto;
    background: #1e1d1d;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
}
.bottom .bottom-first .bottom-first-left {
    width: 580px;
    padding-left: 20px;
    padding-top: 50px;
    float: left;
}
.bottom .bottom-first .bottom-first-left .bottom-first-wenzi{
    font-size: 20px;
    color: #fff;
    font-weight: 400;
}
.bottom .bottom-first .bottom-first-left .bottom-two-wenzi {
    margin-top: 20px;
}
.bottom .bottom-first .bottom-first-left .bottom-two-wenzi p {
    font-size: 14px;
    line-height: 25px;
    color: #999;
}
.bottom .fg {
    width: 1px;
    border-left: 1px solid #4f4f4f;
    height: 240px;
    float: right;
    margin-top: 35px;
    
}
.bottom .bottom-right {
    width: 450px;
    
}
.bottom .bottom-right .bottom-right-conter {
    margin-top: 10px;
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-one{
    margin-top: 50px;
    color: #d7d7d7;
    /* float: right; */
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-one .p1 {
    font-size: 17px;
    margin-bottom: 20px;
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-one .p2 {
    font-size: 28px;
    padding-bottom: 20px;
    font-weight: 600;
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-two {
    position: relative;
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-two div span {
    content: "";
    position: absolute;
    border: 18px solid #1e1d1d;
    border-left-color: transparent;
    border-bottom-color: transparent;
    top: 0;
    left: 0;
}
.bottom .bottom-right .bottom-right-conter .bottom-right-conter-two div {
    height: 120px;
    width: 36px;
    position: absolute;
    left: 120px;
    padding: 0 10px;
    padding-top: 36px;
    color: hsla(0,0%,100%,.8);
    font-size: 14px;
    background: #454b64;
    opacity: .8;
}

.bottom .bottom-right .bottom-right-conter .bottom-right-conter-two img {
    width: 120px;
    height: 120px;
}
.bottom .foot {
    width: 1200px;
    min-width: 1300px;
    margin: 0 auto;
    padding: 20px 0;
    line-height: 23px;
    font-size: 13px;
    color: #7f7f7f;
}

.bottom .foot img {
    vertical-align: sub;
    margin: 0px 5px;
}
.bottom .foot a {
    display: inline-block;
    margin-right: 10px;
    color: #7f7f7f;
    text-decoration: underline;
}