*{
    padding: 0;
    margin: 0;
}
a {
    text-decoration: none;
    font-size: 12px;

}
.clear:after{
    content: "";
    display: block;
    clear:both;
}
ul li {
    list-style: none;
}

/* 头部 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999999;
    height: 65px;
    width: 100%;
    line-height: 65px;
    background-color: #21252e;
    margin: auto;
    
}
.header .logo{
    height: 100%;
    float: left;
    font-size: 27px;
    margin-left: 100px;
    color: #fff;
    float: left;
    font-weight: 600;
    font-family: cursive;
}

.header .logo .logo-one{
    display: inline-block;
    margin-left: 15px;
    /* float: left; */
}
.header .logo .logo-two{
    font-size: 16px;
    cursor: pointer;
}
.header .logo .logo-two span{
    font-size: 16px;
    color: #8e9094;
}
img{
    width: 14px;
    height: 13.6px;
}
.head-right{
    width: 902px;
    height: 65px;
    line-height: 65px;
    float: right;
    
}
.head-right ul li {
    float: left;
    font-size: 16px;
    padding: 0 20px;
    color: #8e9094;
}
.head-right ul li a{
    color: #8e9094;
    font-size: 16px;
    
}
.head-right .sign{
    width:134px;
    margin-right: 100px;
    float: right;
}
.head-right .sign a{
    margin-right: 15px;
    font-size: 14px;
    color: #fff;
}
.head-right .sign-left{
    background-color: #2f54eb;
    border-radius: 16px;
    padding: 8px 20px;
}
.head-right .sign-right{
    border: 1px solid #fff;
    border-radius: 16px;
    padding:7px 22px ;
}