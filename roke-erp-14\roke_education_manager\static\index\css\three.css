*{
    padding: 0;
    margin: 0;
}
a {
    text-decoration: none;
    font-size: 12px;

}
.clear:after{
    content: "";
    display: block;
    clear:both;
}
ul li {
    list-style: none;
}

/* 头部 */
/* banner 图 */
.banner {
    position: relative;

}
.banner img{
    width: 100%;
    height: 20%;

}
.banner h1{
    font-size: 48px;
    font-weight: 400;
    color: #fff;
    position: absolute;
    left: 80px;
    top: 40%;
}
.banner p {
    width: 385px;
    padding: 30px 0;
    font-size: 18px;
    position: absolute;
    left: 80px;
    top: 55%;
    color: #FFF;
}
.left,.right{
    position: absolute;
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
    color: #fff;
    top: 50%;
    opacity: 0;
    cursor: pointer;
    transition: all 0.5s;
}
.right{
    right: 0;
}
.banner:hover .left,.banner:hover .right {
    opacity: 1;
}
.banner .span{
    width: 30px;
    height: 2px;
    background-color: #fff;
    position: absolute;
    bottom: 30px;
    right: 50%;
}

/* Banner 下面四个div */
.tuwen {
    width: 100%;
    height: 430px;
    background-color: #f9f9fa;
    
}
.tuwen .tuwen-conter {
    width: 1050px;
    margin: 0 auto;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    /* background-color: red; */
}
.ment {
    width: 230px;
    height: 244px;
    cursor: pointer;
    font-family: PingFang SC\ ,PingFang SC,digital-1;
    font-weight: 700;
    font-style: normal;
    font-size: 24px;
    position: relative;
    background-color: #fff;
    -webkit-transition: all .3s;
    transition: all .3s;
}
.ment .image {
    width: 100%;
    position: absolute;
    top: 30px;
    text-align: center;
}
.ment .image img {
    width: 60px;
    height: 60px;
}
.ment span {
    position: absolute;
    top: 110px;
    width: 96px;
    left: 50%;
    margin-left: -48px;
    font-size: 24px;
    font-weight: 400;
}
.ment .wds {
    width: 100%;
    font-size: 16px;
    position: absolute;
    top: 150px;
    height: 83px;
    font-weight: 600;
    color: #898989;
    line-height: 24px;
    padding: 15px 0px;
    padding-top: 5px;
    -webkit-line-clamp: 3;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-top: 10px;
    transition: all .3s;
}
.tuwen .ment:hover {
    margin-top: -10px;
}
.tuwen .ment:hover .wds {
    color: #fff;
    background-color: #55515186;
    box-shadow: 0px 8px 9px 6px #c3c9c8;
}
.sx {
    width: 100%;
    height: 400px;
    background: #1b2b31;
    background: url(../images/index.png) no-repeat 100%;
    background-size: 100% 100%;
}
.sx .sx-conter{
    width: 1200px;
    margin: 0 auto;
    height: 100%;
}
.sx .sx-top {
    font-weight: 700;
    font-style: normal;
    font-size: 48px;
    color: #fff;
}
.sx .sx-top .titlemake {
    padding-top: 130px;
    margin-bottom: 40px;
    text-align: center;
    font-size: 36px;
    font-weight: 600;

}
.sx .sx-top p {
    font-weight: 600;
    font-style: normal;
    font-size: 18px;
    color: #fff;
    text-align: center;
    line-height: 30px;
    width: 1007px;
    margin: auto;
}
.butt {
    height: 40px;
    margin-top: 50px;
    display: flex;
    justify-content: center;
}
.butt .butt-left {
    width: 140px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    border-radius: 5px;
    cursor: pointer;
    background: linear-gradient(-90deg,#2f54eb,#4c6ceb);
    box-shadow: 0 13px 27px 0 rgba(75,96,179,.24);
    color: #fff;
    margin-right: 40px;
    
}
.butt .butt-left a {
    color: #fff;
}
.butt .butt-right {
    width: 140px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-weight: 400;
    font-style: normal;
    font-size: 14px;
    border-radius: 5px;
    cursor: pointer;
    background: linear-gradient(-90deg,#2f54eb,#4c6ceb);
    box-shadow: 0 13px 27px 0 rgba(75,96,179,.24);
    color: #fff;
}
/* 加工制造 */
.jdbox {
    width: 100%;
    padding-top: 80px;
    background-color: #f9f9fa;
}
.jdbox .jie {
    width: 1200px;
    margin: 0 auto;
}
.jdbox .jie .title {
    font-family: Arial Negreta,Arial Normal,Arial,digital-1;
    font-style: normal;
    font-size: 32px;
    color: rgba(0,0,0,.996078);
    text-align: left;
    padding-bottom: 30px;
    font-weight: 400;
    position: relative;
    margin-bottom: 45px;
}
.jdbox .jie .title .after{
    content: "";
    position: absolute;
    width: 60px;
    height: 2px;
    background-color: #1e50ae;
    left: 0;
    bottom: -20px;
}
.jdbox .jie .js {
    font-weight: 400;
    font-style: normal;
    color: #898989;
    text-align: left;
    font-size: 18px;
    line-height: 28px;
}


.fabox{
    width: 1200px;
    margin: 0 auto;
    margin-top: 40px;
    
    display: flex;
    
    flex-direction: row;
    flex-wrap: wrap;
}
.fabox .items {
    width: 375px;
    margin-right: 25px;
    padding-bottom: 25px;
    background: #fff;
    margin-bottom: 25px;
    position: relative;
    
}
.fabox .items:hover {
    box-shadow: 0 10px 30px 0 rgba(0,0,0,.1);
}
.fabox .items .imgbox{
    width: 375px;
}
.fabox .items .imgbox img{
    width: 100%;
    height: 220px;
}
.fabox .items  p {
    font-weight: 400;
    font-style: normal;
    font-size: 20px;
    margin: 0 30px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;

}
.fabox .items  p .line{
    content: "";
    display: inline-block;
    width: 30px;
    height: 2px;
    position: absolute;
    bottom: 78px;
    left: 33px;
    background-color: #4568f5;
}
.fabox .items .items-end{
    margin-top: 20px;
    padding: 0 30px;
    justify-content: space-between;
    display: flex;
}
.fabox .items .items-end a{
    font-size: 14px;
    color: #999;
    line-height: 30px;
}
.fabox .items .items-end .ens {
    padding: 9px 20px;
    font-size: 12px;
    border-radius: 3px;
    color: #fff;
    background-color: #409eff;
    display: inline-block;
    border: 1px solid #409eff;
    
}

/* 集约采购 */
.cg{
    width: 100%;
    height: 340px;
    margin-top: 30px;
    background-image: url(../images/xtzz_bj.4abc0bef.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;   
}
.cg .cgbox{
    width: 1200px;
    height: 100%;
    display: flex;
    margin: 0 auto;
}
.cg .cgbox .cgbox-frist {
    width: 100%;
    padding: 50px 0;
    text-align: center;
}
.cg .cgbox .cgbox-frist .cgbox-zi {
    font-weight: 300;
    font-style: normal;
    font-size: 36px;
    color: #fff;
    margin-bottom: 20px;
}
.cg .cgbox .cgbox-frist .cgbox-san{
    width: 1007px;
    margin: auto;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    letter-spacing: normal;
    line-height: 35px;
    color: #fff;
}
.cg .cgbox .cgbox-frist .cgbox-bts {
    margin-top: 40px;
    text-align: center;
}
.cg .cgbox .cgbox-frist .cgbox-btn {
    width: 120px;
    color: #fff;
    background: hsla(0,0%,100%,0);
    border: 1px solid #fff;
    transition: .1s;
    font-weight: 500;
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;
}
/* 质量管理 */
.quality {
    margin: 50px 142.8px 0;
    width: 1200px;
    margin: 0 auto;
}
.quality .quality-one {
    font-style: normal;
    font-size: 32px;
    color: rgba(0,0,0,.996078);
    text-align: left;
    padding-bottom: 30px;
    font-weight: 400;
    position: relative;
    margin-bottom: 45px;
    margin-top: 45px;
}
.quality .quality-one .quality-span {
    content: "";
    position: absolute;
    width: 60px;
    height: 2px;
    background-color: #1e50ae;
    left: 0;
    display: block;
    margin-bottom: 20px;
    margin-top: 35px;
}
.quality .quality-wenzi {
    font-weight: 400;
    font-style: normal;
    color: #898989;
    text-align: left;
    font-size: 18px;
    line-height: 28px;
    width: 1200px;
    margin: auto;
    display: block;
}
/* 质量管理 */
.fabox1{
    width: 1200px;
    margin: 0 auto;
    margin-top: 40px;
    
    display: flex;
    
    flex-direction: row;
    flex-wrap: wrap;
}
.fabox1 .items1 {
    width: 375px;
    margin-right: 25px;
    padding-bottom: 25px;
    background: #fff;
    margin-bottom: 25px;
    position: relative;
    
}
.fabox1 .items1:hover {
    box-shadow: 0 10px 30px 0 rgba(0,0,0,.1);
}

.fabox1 .items1 .imgbox1{
    width: 375px;
}
.fabox1 .items1 .imgbox1 img{
    width: 100%;
    height: 220px;
}
.fabox1 .items1  p {
    font-weight: 400;
    font-style: normal;
    font-size: 20px;
    margin: 0 30px;
    padding: 20px 0;
    border-bottom: 1px solid #eee;

}
.fabox1 .items1  p .line1{
    content: "";
    display: inline-block;
    width: 30px;
    height: 2px;
    position: absolute;
    bottom: 78px;
    left: 33px;
    background-color: #4568f5;
}
.fabox1 .items1 .items-end1{
    margin-top: 20px;
    padding: 0 30px;
    justify-content: space-between;
    display: flex;
}
.fabox1 .items1 .items-end1 span {
    margin-left: 238px;
     
 }
.fabox1 .items1 .items-end1 a{
    font-size: 14px;
    color: #999;
    line-height: 30px;
}
