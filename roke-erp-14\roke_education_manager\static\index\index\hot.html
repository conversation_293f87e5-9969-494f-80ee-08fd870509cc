<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/head.css">
    <link rel="stylesheet" href="../css/three.css">
    <link rel="stylesheet" href="../css/end.css">
    <link rel="stylesheet" href="../css/index.css">
    <title>考试教育系统</title>
    <style>
        .title {
            width: 100%;
            height: 5vh;
            background-color: #FFFFFF;
            display: flex;
            justify-content: space-around;
        }

        .title p {
            width: 63vw;
            height: 5vh;
            font-size: 24px;
            font-family: cursive;
            font-weight: 600;
            color: #4F80F7;
            line-height: 5vh;
        }

        .container {
            height: 93vh;
            padding: 2vh 0 0 0;
            background-image: url(./images/modeBackground.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .information {
            height: 3vh;
            background-color: yellow;
            width: 6vw;
            margin: 1vh auto 0;
            background: #FFFFFF;
            box-shadow: 0px 0px 6px 5px rgba(0, 107, 230, 0.1);
            border-radius: 5px;
            text-align: center;
            line-height: 3vh;
            color: #4F80F7;
            cursor: pointer;
        }

        .progress {
            display: flex;
            margin-top: 10vh;
        }

        .progress>div {
            width: 20vw;
            background-color: #7B8FBF;
            color: #FFFFFF;
            height: 7vh;
            margin-left: 3vw;
            border-radius: 14px 14px 0 0;
            text-align: center;
            line-height: 7vh;
            font-size: 20px;
            font-weight: 600;
            cursor: pointer
        }

        .boxColor {
            background-color: #FFFFFF !important;
            color: #4F80F7 !important;
        }

        .el-col {
            float: right !important;
            margin-right: 10vw;
        }

        .el-input--prefix {
            width: 20vw !important;
        }

        .el-table .success-row {
            background: #F2F8FF;
        }

        .items {
            width: 10vw;
            height: 5vh;
        }

        /* 菜单与鼠标移入 */
        .menu {
            width: 100%;
            height: 4vh;
            background: #FFFFFF;
            line-height: 45px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .menu:hover {
            overflow: visible;
            background: #FFFFFF;
            color: #4F80F7;
            z-index: 999;
            cursor: pointer;
        }

        /* 下拉菜单与鼠标移入 */
        .informations {
            height: 3.5vh;
            width: 5vw;
            margin: 0 auto;
            background: #FFFFFF;
            box-shadow: 0px 0px 6px 5px rgba(0, 107, 230, 0.1);
            text-align: center;
            line-height: 3vh;
            color: #4F80F7;
            font-size: 15px;
        }

        .informations:hover {
            background: #f3f3f3;
            color: #8eadf7;
            cursor: pointer;
        }

        .el-table__row {
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="title">
            <div>
                <p>数字化考试云平台</p>
            </div>
            <div class="items">
                <div class="menu">
                    <div class="information">{{name}}</div>
                    <div>
                        <div class="informations" style="border-radius: 0px 0px 5px 5px;" @click="handoff">切换模式</div>
                        <div class="informations" style="border-radius: 0px 0px 5px 5px;" @click="quit">退出登录</div>
                    </div>
                </div>

            </div>
        </div>
        <div class="container">
            <div class="progress">
                <div :class="{'boxColor' : boxColor== index}" v-for="(item,index) in list" @click="listBtn(item,index)">
                    {{item}}
                </div>

            </div>
            <div style="height: 74vh;
            background-color: rgb(255, 255, 255);
            padding: 2vh 0 0 0;">
                <el-row>
                    <el-col :span="6" style="float:left; margin-left: 45vw;width: 270px; height: 40px;">
                        <el-input type="text" prefix-icon="el-icon-search" v-model="srarchText" placeholder="请输入关键字搜索"
                            style="width: 270px; cursor: pointer"></el-input>
                    </el-col>
                </el-row>
                <div style="width: 95vw; margin: 0 auto;">
                    <el-table :data="tables" style="width: 100%;" :row-class-name="tableRowClassName"
                        @row-click="clickData">
                        <el-table-column prop="student_name" label="考生" width="330"></el-table-column>
                        <el-table-column prop="exam_name" label="考试名称" width="450"></el-table-column>
                        <el-table-column prop="course_name" label="考试科目" width="400"></el-table-column>
                        <el-table-column prop="start_time" label="开始时间" width="320"></el-table-column>
                        <el-table-column prop="end_time" label="结束时间" width="320"></el-table-column>
                    </el-table>
                </div>
            </div>

        </div>
    </div>
</body>
<script src="../js/vue.js"></script>
<script src="../js/ui.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/axios.min.js"></script>
<script>
    document.addEventListener("click", function () {
        // 发送消息给父页面
        window.parent.postMessage("hidePopover", "*");
    });
    new Vue({
        el: '#app',
        data() {
            return {
                name: '',
                boxColor: 0,
                list: [],
                srarchText: '',
                tableData: [],
                state: ''
            }
        },
        methods: {
            handoff() {
                window.location.replace('../../../../roke_education_manager/static/index/index/mode.html?name=' + this.GetRequest().name + '&id=' + this.GetRequest().id);
            },
            listBtn(item, index) {
                this.boxColor = index
                console.log(item);
                console.log(index);
                if (item == "等待考试") {
                    this.state = 'wait_exam'
                } else if (item == "考试中") {
                    this.state = 'exam_taking'
                } else if (item == "已完成") {
                    this.state = 'exam_done'
                } else if (item == "等待练习") {
                    this.state = 'not_done'
                } else if (item == "已完成练习") {
                    this.state = 'practice_done'
                }
                axios.request({
                    url: '/roke/query_exam_list',
                    method: 'post',
                    data: {
                        student_id: this.GetRequest().id,
                        pattern_type: this.GetRequest().type,
                        pattern_state: this.state
                    }
                }).then(res => {
                    console.log(res);
                    this.tableData = res.data.result.data
                })
            },
            clickData(row, event, column) {
                console.log(row)
                console.log(event)
                console.log(column)
                window.location.replace('../../../../roke_education_manager/static/html/detail_answer.html?id=' + row.exam_id + '&studentId=' + this.GetRequest().id + '&state=' + row.exam_state)
            },
            quit() {
                window.location.replace('../../../../roke_education_manager/static/index/index/index.html');
            },
            tableRowClassName({ row, rowIndex }) {
                if (rowIndex % 2 == 0) {
                    return '';
                } else {
                    return 'success-row'
                }
                return '';
            },
            GetRequest() {
                var url = location.search; //获取url中"?"符后的字串
                url = decodeURI(url)
                var theRequest = new Object();
                if (url.indexOf("?") != -1) {
                    var str = url.substr(1);
                    strs = str.split("&");
                    for (var i = 0; i < strs.length; i++) {
                        theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
                return theRequest;
            },
            getExamList() {
                axios.request({
                    url: '/roke/query_exam_list',
                    method: 'post',
                    data: {
                        student_id: this.GetRequest().id,
                        pattern_type: this.GetRequest().type,
                        pattern_state: this.GetRequest().state
                    }
                }).then(res => {
                    console.log(res);
                    this.tableData = res.data.result.data
                })
            }
        },
        computed: {
            tables() {
                let search = this.srarchText.toLowerCase();
                if (search) {
                    // tableData是要放在表格中展示的数据
                    return this.tableData.filter(data => {
                        console.log(data);
                        // 获取对象数组的所有枚举属性，通过some来判断对象中的某一属性是否匹配上，如果匹配上了那么这个对象后面的属性就不用判断了，从而返回整个对象(毕竟我们要看整个对象的值)。
                        return Object.keys(data).some(key => {
                            console.log(data[key]);
                            if (data[key] !== null) // 对象的属性不为空才进行模糊匹配，否则会报错
                                return data[key].toString().toLowerCase().indexOf(search) > -1;
                        });
                    });
                    console.log(this.tableData);
                } else {
                    return this.tableData; // 如果搜索框没有值，就正常展示表格的所有数据
                }

            }
        },
        mounted() {
            if (this.GetRequest().type == 'practice') {
                this.list = ['等待练习', '已完成练习']
            } else {
                this.list = ['等待考试', '考试中', '已完成']
            }
            this.name = this.GetRequest().name
            this.getExamList()
        }
    });

</script>

</html>