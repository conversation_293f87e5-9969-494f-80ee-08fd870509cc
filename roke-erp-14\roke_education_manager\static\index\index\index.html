<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../../../roke_education_manager/static/index/css/head.css">
    <link rel="stylesheet" href="../../../../roke_education_manager/static/index/css/three.css">
    <link rel="stylesheet" href="../../../../roke_education_manager/static/index/css/end.css">
    <link rel="stylesheet" href="../../../../roke_education_manager/static/index/css/index.css">
    <title>考试教育系统</title>
    <style>
        .container {
            background-image: url(../../../../roke_education_manager/static/index/index/images/background.png);

            width: 100%;
            height: 100vh;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            position: relative;
        }

        .loginModule {
            background-color: #FFFFFF;
            width: 90vw;
            height: 90vh;
            position: absolute;
            top: 5%;
            left: 5%;
            box-shadow: 0px 0px 16px 12px rgba(0, 0, 0, 0.05);
            border-radius: 20px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .loginModule>div {
            width: 30vw;
            height: 50vh;
        }


        .icon>img {
            width: 100%;
            height: 100%;
        }


        .loginComponents>p {
            font-size: 40px;
            font-family: cursive;
            font-weight: 600;
            color: #4F80F7;
            text-align: center;
        }

        .login {
            width: 20vw;
            margin: 6vh auto 0;
        }

        .el-input__inner {
            border: 0px solid black;
        }

        .loginBtn {
            width: 11vw;
            height: 5vh;
            background: #6591F7;
            border-radius: 33px;
            border: 0;
            color: #FFFFFF;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div class="container" id="app">
        <div class="loginModule">
            <div class="icon">
                <img src="../../../../roke_education_manager/static/index/index/images/icon.png" alt="icon">
                <!--                <img src="images/icon.png" alt="icon">-->
            </div>
            <div class="loginComponents">
                <p>数字化考试云平台</p>
                <div class="login">
                    <el-input placeholder="请输入账号" prefix-icon="el-icon-user-solid" v-model="account"
                        style="box-shadow: 8px 8px 16px 0px rgba(0,0,0,0.05);">
                    </el-input>
                    <el-input placeholder="请输入密码" prefix-icon="el-icon-s-goods" v-model="password"
                        :show-password=showEye style="margin-top: 4vh; box-shadow: 8px 8px 16px 0px rgba(0,0,0,0.05);">
                    </el-input>
                </div>
                <div style="text-align: center; margin-top: 10vh;">
                    <button class="loginBtn" @click="login">登录</button>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="../../../../roke_education_manager/static/index/js/vue.js"></script>
<script src="../../../../roke_education_manager/static/index/js/ui.js"></script>
<script type="text/javascript" src="/roke_mes_production/static/src/js/work_report/axios.min.js"></script>
<script>
    document.addEventListener("click", function () {
        // 发送消息给父页面
        window.parent.postMessage("hidePopover", "*");
    });
    new Vue({
        el: '#app',
        data() {
            return {
                account: '',
                password: '',
                showEye: true,
                t: 333
            }
        },
        methods: {
            login() {
                axios.request({
                    url: "/roke/student_login",
                    method: "post",
                    data: {
                        userlogin: this.account,
                        userpwd: this.password
                    }
                }).then(res => {
                    console.log(res.data.result);
                    if (res.data.result.state == "success") {
                        this.$message({
                            message: res.data.result.msgs,
                            type: 'success'
                        });
                        window.location.replace('../../../../roke_education_manager/static/index/index/mode.html?name=' + res.data.result.data.user_name + '&id=' + res.data.result.data.user_id);
                    } else {
                        this.$message.error(res.data.result.msgs);
                    }
                })
            }
        }
    });
</script>

</html>