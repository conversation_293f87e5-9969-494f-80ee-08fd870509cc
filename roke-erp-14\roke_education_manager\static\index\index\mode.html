<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../css/head.css">
    <link rel="stylesheet" href="../css/three.css">
    <link rel="stylesheet" href="../css/end.css">
    <link rel="stylesheet" href="../css/index.css">
    <title>考试教育系统</title>
    <style>
        .title {
            width: 100%;
            height: 5vh;
            background-color: #FFFFFF;
            display: flex;
            justify-content: space-around;
        }

        .title p {
            width: 63vw;
            height: 5vh;
            font-size: 24px;
            font-family: cursive;
            font-weight: 600;
            color: #4F80F7;
            line-height: 5vh;
        }

        .container {
            height: 95vh;
            background-image: url(./images/modeBackground.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .iconBackground {
            background-image: url(./images/modeContainer.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            width: 90vw;
            height: 85vh;
        }

        .information {
            height: 3vh;
            width: 6vw;
            margin: 1vh auto 0;
            background: #FFFFFF;
            box-shadow: 0px 0px 6px 5px rgba(0, 107, 230, 0.1);
            border-radius: 5px;
            text-align: center;
            line-height: 3vh;
            color: #4F80F7;
            cursor: pointer;
        }

        .modeTitle {
            margin-top: 3vh;
            margin-left: 5vw;
            font-size: 37px;
            font-family: cursive;
            font-weight: 600;
            color: #4F80F7;
        }

        .mode {
            display: flex;
            margin-top: 10vh;
            height: 50vh;
            justify-content: space-around;
        }

        .leftIcon {
            width: 25vw;
            background-image: url(./images/leftIcon.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
        }

        .rightIcon {
            width: 25vw;
            background-image: url(./images/rightIcon.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            cursor: pointer;
        }

        .rightIcon:hover {
            box-shadow: 0px 8px 9px 6px #e0e6e5;
            border-radius: 20px;
        }

        .leftIcon:hover {
            box-shadow: 0px 8px 9px 6px #e0e6e5;
            border-radius: 20px;
        }

        .mode p {
            text-align: center;
            margin-top: 35vh;
            font-weight: 600;
            font-size: 20px;
            font-family: cursive;
        }


        .items {
            width: 10vw;
            height: 5vh;
        }

        /* 菜单与鼠标移入 */
        .menu {
            width: 100%;
            height: 4vh;
            background: #FFFFFF;
            line-height: 45px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .menu:hover {
            overflow: visible;
            background: #FFFFFF;
            color: #4F80F7;
            z-index: 999;
            cursor: pointer;
        }

        /* 下拉菜单与鼠标移入 */
        .informations {
            height: 3.5vh;
            width: 5vw;
            margin: 0 auto;
            background: #FFFFFF;
            box-shadow: 0px 0px 6px 5px rgba(0, 107, 230, 0.1);
            text-align: center;
            line-height: 3vh;
            color: #4F80F7;
            font-size: 15px;
        }

        .informations:hover {
            background: #f3f3f3;
            color: #8eadf7;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="title">
            <div>
                <p>数字化考试云平台</p>
            </div>
            <div class="items">
                <div class="menu">
                    <div class="information">{{name}}</div>
                    <div>
                        <div class="informations" style="border-radius: 0px 0px 5px 5px;" @click="quit">退出登录</div>
                    </div>
                </div>

            </div>

        </div>
        <div class="container">
            <div class="iconBackground">
                <div class="modeTitle">
                    <p>选择模式</p>
                </div>
                <div class="mode">
                    <div class="leftIcon" @click="practiceBtn">
                        <p>练习模式</p>
                    </div>
                    <div class="rightIcon" @click="examBtn">
                        <p>考试模式</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="../js/vue.js"></script>
<script src="../js/ui.js"></script>
<script>
    document.addEventListener("click", function () {
        // 发送消息给父页面
        window.parent.postMessage("hidePopover", "*");
    });
    new Vue({
        el: '#app',
        data() {
            return {
                name: ''
            }
        },
        methods: {
            GetRequest() {
                var url = location.search; //获取url中"?"符后的字串
                url = decodeURI(url)
                var theRequest = new Object();
                if (url.indexOf("?") != -1) {
                    var str = url.substr(1);
                    strs = str.split("&");
                    for (var i = 0; i < strs.length; i++) {
                        theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                    }
                }
                return theRequest;
            },
            quit() {
                window.location.replace('../../../../roke_education_manager/static/index/index/index.html');
            },
            practiceBtn() {
                window.location.replace('../../../../roke_education_manager/static/index/index/hot.html?name=' + this.name + '&id=' + this.GetRequest().id + '&type=' + 'practice' + "&state=" + 'not_done');
            },
            examBtn() {
                window.location.replace('../../../../roke_education_manager/static/index/index/hot.html?name=' + this.name + '&id=' + this.GetRequest().id + '&type=' + 'exam' + "&state=" + 'wait_exam');
            }
        },
        mounted() {
            this.name = this.GetRequest().name
            console.log(this.GetRequest().id);
        }

    });
</script>

</html>