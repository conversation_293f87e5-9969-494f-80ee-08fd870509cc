@charset "utf-8";
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

/**, body {*/
/*    padding: 0px;*/
/*    margin: 0px;*/
/*}*/

/*body {*/
/*    color: #666;*/
/*    font-size: 16px;*/
/*}*/

li {
    list-style-type: none;
}

table {
}

i {
    margin: 0px;
    padding: 0px;
    text-indent: 0px;
}

img {
    border: none;
    max-width: 100%;
}

/*a{ text-decoration:none; color:#399bff;}*/
/*a.active,a:focus{ outline:none!important; text-decoration:none;}*/
ol, ul, p, h1, h2, h3, h4, h5, h6 {
    padding: 0;
    margin: 0
}

/*a:hover{ color:#06c; text-decoration: none!important}*/


.clearfix:after, .clearfix:before {
    display: table;
    content: " "
}

.clearfix:after {
    clear: both
}

.pulll_left {
    float: left;
}

.pulll_right {
    float: right;
}


.loading {
    position: fixed;
    left: 0;
    top: 0;
    font-size: 16px;
    z-index: 100000000;
    width: 100%;
    height: 100%;
    background: #1a1a1c;
    text-align: center;
}

.loadbox {
    position: absolute;
    width: 160px;
    height: 150px;
    color: rgba(255, 255, 255, .6);
    left: 50%;
    top: 50%;
    margin-top: -100px;
    margin-left: -75px;
}

.loadbox img {
    margin: 10px auto;
    display: block;
    width: 40px;
}

/*.head {*/
/*    height: 105px;*/
/*    background: url(../images/head_bg.png) no-repeat center center;*/
/*    position: relative*/
/*}*/

.head h1 {
    color: #fff;
    text-align: center;
    font-size: 42px;
    line-height: 90px;;
}

.head h1 img {
    width: 1.5rem;
    display: inline-block;
    vertical-align: middle;
    margin-right: .2rem
}

.weather {
    position: absolute;
    right: 30px;
    top: 0;
    line-height: 70px;
}

.weather span {
    color: rgba(255, 255, 255, .9) !important;
    font-size: 24px;
}

.mainbox {
    padding: 10px 20px 0 20px;
}

.mainbox > ul {
    margin-left: -.4rem;
    margin-right: -.4rem;
}

.mainbox > ul > li {
    float: left;
    padding: 0 .4rem
}

.mainbox > ul > li {
    width: 24%
}

.mainbox > ul > li:nth-child(2) {
    width: 52%
}

.boxall {
    top: 0;
    padding: 5px;
    background: rgba(0, 0, 0, .2);
    position: relative;
    margin-bottom: 5px;
    z-index: 10;
}

.alltitle {
    font-size: 18px;
    color: #fff;
    position: relative;
    padding-left: 1%;
    margin-bottom: 10px;
	top: 0;
	height: 10%;
}

/*.alltitle:before{ width: 5px; height: 20px; top:2px; position: absolute; content: ""; background: #49bcf7; border-radius:20px; left: 0; }*/

.navboxall {
    /*height: calc(100% - 50px);*/
    height: 82%;
}

.navboxall-chart {
    /*height: calc(100% - 50px);*/
    height: 100%;
}

.zhibiao {
    height: 100%;
    width: 66%;
}

.zb1, .zb2 {
    float: left;
    width: 31%;
    height: 100%;
}
.zb3{
    float: left;
    width: 38%;
    height: 100%;
}

#zb1, #zb2, #zb3 {
    height: calc(100% - 30px);
}

.zhibiao span {
    padding-top: 20px;
    display: block;
    text-align: center;
    color: #fff;
    font-size: 16px;
}
.num {
    height: 100%;
    width: 34%;
    margin-top: 2%;
}

.numbt {
    font-size: 1.5rem;
    color: #fff;
    padding-top: 4%;
    top: 0;
    height: 30%;
    text-align: center;
}

@media screen and (min-width:2100px){
    #page{ width: 2100px; }#content,
                           .numbt{
    position: absolute;
    font-size: 1.0rem;
    color: #fff;
    padding-top: 1%;
    top: 0;
    height: 12%;
}#secondary{width:310px}
}

.numtxt {
    color: #fef000;
    font-size: 3.5rem;
    top: 40%;
    font-weight: bold;
    text-align: center;
}
@media screen and (min-width:2100px){
    #page{ width: 2100px; }#content,
                           .numtxt{
    position: absolute;
    color: #fef000;
    font-size: 2.5rem;
    border-top: 1px solid rgba(255, 255, 255, .1);
    border-bottom: 1px solid rgba(255, 255, 255, .1);
    top: 40%;
    font-weight: bold;
}#secondary{width:310px}
}

.table1 th {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    font-size: 16px;
    color: rgba(255, 255, 255, .6);
    font-weight: normal;
    padding: 0 0 10px 0;
}

.table1 td {
    font-size: 16px;
    color: rgba(255, 255, 255, .4);
    padding: 15px 0 0 0;
}

.table1 span {
    width: 24px;
    height: 24px;
    border-radius: 3px;
    display: block;
    background: #878787;
    color: #fff;
    line-height: 24px;
    text-align: center;
}

.table1 tr:nth-child(2) span {
    background: #ed405d
}

.table1 tr:nth-child(3) span {
    background: #f78c44
}

/*.table1 tr:nth-child(4) span{ background: #49bcf7}*/
.iconchart li {
    height: 30px;
}

.iconchart label {
    color: #fff;
    line-height: 30px;
    height: 30px;
    display: inline-block;
    opacity: .8;
}

.iconchart label img {
    display: inline-block;
    vertical-align: middle;
    opacity: .4;
}

.iconchart .bar {
    display: inline-block;
    background: rgba(255, 255, 255, .1);
    border: 1px solid rgba(255, 255, 255, .2);
    width: 78%;
    height: 20px;
    padding: 2px;
    position: relative;
    top: 5px;
}

.iconchart .bar span {
    background: linear-gradient(to right, #588fab, #5ea99c);
    display: block;
    width: 60%;
    height: 100%;
    animation: myfirst2 1s ease;
}

.iconchart .bar i {
    position: absolute;
    opacity: .5;
    right: -36px;
    color: #fff;
    line-height: 20px;
    top: 0;
}

/*Plugin CSS*/
.wrap {
    height: 82%;
    overflow: hidden;
}

.wrap-electricity {
    height: 70%;
    overflow: hidden;
}

.wrap li {
    line-height: 100%;
    height: 82%;
    padding: 6px 0;
    border-bottom: 1px dotted #00178B;
}

.wrap-electricity li {
    line-height: 100%;
    height: 84%;
}

.wrap li p, .wraptit {
    font-size: 0;
}
.wrap-electricity li p, .wraptit-electricity {
    font-size: 0;
}

.wrap li span, .wraptit span {
    display: inline-block;
    font-size: 14px;
    text-align: center;
    color: rgba(255, 255, 255, .6);
}

.wrap-electricity li span, .wraptit-electricity span {
    display: inline-block;
    font-size: 14px;
    color: rgba(255, 255, 255, .6);
}

.wraptit {
	top: 0;
	height: 10%;
    /*border-bottom: 1px solid rgba(255, 255, 255, .2);*/
    padding: 0 0 10px 0;
    margin-bottom: 10px;
    bottom: 0;
}

.wraptit-electricity {
	top: 0;
	height: 10%;
    /*border-bottom: 1px solid rgba(255, 255, 255, .2);*/
    padding: 0 0 10px 0;
    margin-bottom: 10px;
    bottom: 0;
}

.wrap li span:nth-child(1), .wraptit span:nth-child(1) {
    width: 35%
}

.wrap li span:nth-child(2), .wraptit span:nth-child(2) {
    width: 25%
}

.wrap li span:nth-child(3), .wraptit span:nth-child(3) {
    width: 20%
}

.wrap li span:nth-child(4), .wraptit span:nth-child(4) {
    width: 20%
}

.wrap-electricity li span:nth-child(1), .wraptit-electricity span:nth-child(1) {
    width: 50%
}

.wrap-electricity li span:nth-child(2), .wraptit-electricity span:nth-child(2) {
    width: 25%
}

.wrap-electricity li span:nth-child(3), .wraptit-electricity span:nth-child(3) {
    width: 25%
}

.str_wrap {
    overflow: hidden;
    width: 100%;
    position: relative;
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none;
    white-space: nowrap;
}


.str_move {
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 0;
    cursor: move;
}

.str_move_clone {
    display: inline-block;
    vertical-align: top;
    position: absolute;
    left: 100%;
    top: 0;
}

.str_vertical .str_move_clone {
    left: 0;
    top: 100%;
}

.str_down .str_move_clone {
    left: 0;
    bottom: 100%;
}

.str_vertical .str_move,
.str_down .str_move {
    white-space: normal;
    width: 100%;
}

.str_static .str_move,
.no_drag .str_move,
.noStop .str_move {
    cursor: inherit;
}

.str_wrap img {
    max-width: none !important;
}
#today_work_record_group li p span{
    width: 33%;
}

@keyframes myfirst2 {
    from {
        width: 0
    }
    to {
    }
}
