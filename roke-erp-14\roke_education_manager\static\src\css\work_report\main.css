body{
    margin: 10px;
}
.topNav{
    /*display: flex;*/
    height: 64px;
    justify-content: center;
    align-items: center;
    background-color: #02BA7E;
}
.el-button--success{
    border-color: #009688;
}
.el-table__body tr.current-row>td{
    background-color: #C4FFB9 !important;
}
.mainCon{
    display: flex;
    width: 100%;
    justify-content: center;
    /*align-items: center;*/
}
.rightCon{
    width: 76%;
    padding: 1rem;
}
.leftCon{
    width: 24%;
    padding: 1rem 1rem 1rem 0.5rem;
}
.el-button--primary{
    background-color: #009688;
    border-color: #009688;
}
.el-button--primary:hover,
.el-button--primary:active,
.el-button--primary.is-active,
.el-button--primary:active,
.el-button--primary:focus,
.el-button--primary:hover{
    background-color: #02BA7E;
    border-color: #02BA7E;
}
.el-input.is-active .el-input__inner,
.el-input__inner:focus,
.el-select .el-input.is-focus .el-input__inner,
.el-select .el-input__inner:focus,
.el-range-editor.is-active,
.el-range-editor.is-active:hover{
    border-color: #02BA7E;
}
.el-button--text{
    color: #fff;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    background: 0 0;
    padding: 0.3rem;
}
.el-table__row td:last-child .cell{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #02BA7A;
    height: 60px;
    line-height: 60px;
    margin-right: 5px;
    padding: 0;
}
/*!*滚动条的滑块*!*/
::-webkit-scrollbar-thumb {
    background-color: #D1D1D1;
    border-radius: 3px;
}
::-webkit-scrollbar {
    width: 0 !important;
    height: 15px !important;
}

.controlBtn{
    width: 46%;
    height: 4rem;
    font-weight: bold;
    font-size: 1.4rem;
    padding: 12px 10px;
}
.stopT,.stopT:active,.stopT:focus{
    background-color: #FF4539;
    border-color: #FF4539;
}
.stopT:hover{
    background-color: #FF5F40;
    border-color: #FF5F40;
}
.noOrder{
    background-color: #F8B11D;
    border-color: #F8B11D;
}
.noOrder:hover,
.noOrder:active,
.noOrder.is-active,
.noOrder:active,
.noOrder:focus,
.noOrder:hover {
    background-color: #FFCE39;
    border-color: #FFCE39;
}
.el-collapse-item__header{
    font-size: 18px;
    font-weight: bold;
    color: #333;
}
.el-select {
    width: 72%;
}
.el-range-editor.el-input__inner{
    width: 100%;
}
.searchBtn{
    width: 100%;
    margin-top: 1rem;
}
.t{
    background-color: #409EFF;
    border-color: #409EFF;
}
.t:hover,
.t:active,
.t.is-active,
.t:active,
.t:focus,
.t:hover{
    background-color: #66B1FF;
    border-color: #66B1FF;
}
.el-button-group{
    width: 100%;
    display: flex;
    margin: 1rem auto;
    justify-content: center;
}
.el-button-group .el-button{
    padding: 12px 16px;
}
.bgjl{
    width: 50px;
    height: 50px;
    border: none;
    background: url('/roke_mes_production/static/src/images/bgjl.png') no-repeat center;
    background-size: 100% 100%;
    outline: none;
    cursor: pointer;
}
.drjx{
    width: 50px;
    height: 50px;
    border: none;
    background: url('/roke_mes_production/static/src/images/drjx.png') no-repeat center;
    background-size: 100% 100%;
    outline: none;
    cursor: pointer;
}
.sybz{
    width: 50px;
    height: 50px;
    border: none;
    background: url('/roke_mes_production/static/src/images/sybz.png') no-repeat center;
    background-size: 100% 100%;
    outline: none;
    cursor: pointer;
}
.el-button--text:focus, .el-button--text:hover{
    color: #fff;
}
.el-date-table td.end-date span, .el-date-table td.start-date span{
    background-color: #02BA7A;
}
.el-date-table td.today span,
.el-date-table td.available:hover,
.el-picker-panel__shortcut:hover,
.el-picker-panel__icon-btn:hover{
    color: #02BA7A;
}
.exit{
    height: 32px;
    display: inline-block;
    outline: none;
    border: none;
    color: #fff;
    position: absolute;
    right: 3%;
    top: 27%;
    background: url("/roke_mes_production/static/src/images/exit2.png") no-repeat;
}
.exit:active,.exit:focus,.exit:hover{
    color: #fff;
    border-color: transparent;
    background-color: transparent;
}
.exit:hover{
    background: url("/roke_mes_production/static/src/images/exit1.png") no-repeat;
}
.el-table td,.el-table th>.cell{
    text-align: center;
}
.el-dialog__body{
    display: flex;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: #02BA7E;
    border-color: #02BA7E;
}
.el-checkbox__input.is-checked+.el-checkbox__label,
.el-input-number__decrease:hover,
.el-input-number__increase:hover{
    color: #02BA7E;
}
.el-checkbox__inner:hover,
.el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled),
.el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled){
    border-color: #02BA7E;
}
.dialog-footer .el-button--default:focus,
.dialog-footer .el-button--default:hover,
.el-dialog__body div .el-button+.el-button:focus,
.el-dialog__body div .el-button+.el-button:hover{
    color: #02BA7E;
    border-color: #02BA7E;
    background-color: #EDFFF0;
}
.el-dialog__body{
    padding: 0;
}
.el-menu.el-menu--horizontal{
    width: 100%;
}
.el-menu--horizontal>.el-menu-item{
    padding: 0 20px;
}
.cj_table .el-table__row td:last-child .cell,
.manyWokers .el-table__row td:last-child .cell,
.checkList .el-table__row td:last-child .cell{
    background-color: transparent;
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
    color: #FFF;
    background-color: #AFFFC1;
    border-color: #AFFFC1;
}
.el-button.is-disabled, .el-button.is-disabled:focus, .el-button.is-disabled:hover{
    color: #fff;
}
.el-dialog__header{
    font-weight: bold;
}
.dialog-footer .el-button{
    width: 16%;
}
.checkList .el-table__row td:last-child .cell{
    display: block;
}
.baogong{
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}
.el-button.is-disabled.el-button--text{
    background-color: #AFFFC1;
    border-color: #AFFFC1;
}