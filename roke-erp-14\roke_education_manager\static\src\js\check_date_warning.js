odoo.define('roke_check_date_warning.button', function (require){
    "use strict";
    var rpc = require("web.rpc");
    var ListController = require('web.ListController');
    var ListView = require('web.ListView');


    var ImportViewMixin = {
        /**
         * @override
         */
        init: function (viewInfo, params) {
            var importEnabled = 'import_enabled' in params ? params.import_enabled : true;
            this.controllerParams.importEnabled = importEnabled;
        },
    };
    var ImportControllerMixin = {
        /**
         * @override
         */
        init: function (parent, model, renderer, params) {
            this.importEnabled = params.importEnabled;
        },
        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------
        /**
         * Adds an event listener on the import button.
         *
         * @private
         */
        _bindImport: function () {
            if (!this.$buttons) {
                return;
            }
            var self = this;
            /*更新生产预警*/
            this.$buttons.on('click', '.o_button_check_date_warning', function () {
                rpc.query({
                    model: 'roke.production.date.warning',
                    method: 'check_warning',
                    args: [""]
                }).then(function () {
                   self.reload();
                });
            });
        }
    };
    ListView.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportViewMixin.init.apply(this, arguments);
        },
    });
    ListController.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin.init.apply(this, arguments);
        },
        renderButtons: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin._bindImport.call(this);
            var self = this;
            /*计价确认单*/
            if (this.modelName === "roke.production.date.warning"){
                rpc.query({
                    model: 'roke.production.date.warning',
                    method: 'check_permission',
                    args: [""]
                }).then(function (permission) {
                    if (permission){
                        self.$buttons.find(".o_button_check_date_warning").css("display","inline-block");
                    }
                });
            }
        }
    });
}
);