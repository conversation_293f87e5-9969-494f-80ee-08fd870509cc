function GetDashboardData() {
    $.ajax({
        url: '/roke/dashboard/production_order',
        type: 'post',
        headers: {
            // 请求头
            'Content-Type': 'application/json'
        },
        dataType: 'json',
        data: JSON.stringify({
        }),
        success: function (data) {
            // 接口请求成功后的操作
            if (data.result != []) {
                // console.log(data);
                for (let i = 0; i < data.result.length; i++) {
                    //动态创建一个tr行标签,并且转换成jQuery对象
                    if (data.result[i].progress >= 0 && data.result[i].progress < 50) {
                        // console.log(data.result[i].progress);
                        var trTemp = $("<tr></tr>");
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + "<div style=' display:block; width:96%; height:20px; border-radius:20px; background-color:#F56C6C;color: #ffffff;font-size: 9px;line-height: 20px;'>" + data.result[i].progress + "%" + "</div>" + "</td>");
                        $('.tableLeft').append(trTemp);
                    } else if (data.result[i].progress >= 50 && data.result[i].progress < 100) {
                        // console.log(data.result[i].progress);
                        var trTemp = $("<tr></tr>");
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + "<div style=' display:block; width:96%; height:20px; border-radius:20px; background-color:#E6A23C;color: #ffffff;font-size: 9px;line-height: 20px;'>" + data.result[i].progress + "%" + "</div>" + "</td>");
                        $('.tableLeft').append(trTemp);
                    } else if (data.result[i].progress === 100) {
                        var trTemp = $("<tr></tr>");
                        // console.log(data.result[i].progress);
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + "<div style=' display:block; width:96%; height:20px; border-radius:20px; background-color:#67C23A;color: #ffffff;font-size: 9px;line-height: 20px;'>" + data.result[i].progress + "%" + "</div>" + "</td>");
                        $('.tableLeft').append(trTemp);
                    } else {
                        var trTemp = $("<tr></tr>");
                        // console.log(data.result[i].progress);
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + "<div style=' display:block; width:96%; height:20px; border-radius:20px; background-color:fff;color: #333;font-size: 9px;line-height: 20px;'>" + data.result[i].progress + "%" + "</div>" + "</td>");
                        $('.tableLeft').append(trTemp);
                    }
                }
            } else {
                // console.log("请求失败");
            }
        }
    });
    //待派工生产任务
    $.ajax({
        url: '/roke/dashboard/wait_task',
        type: 'post',
        headers: {
            // 请求头
            'Content-Type': 'application/json'
        },
        dataType: 'json',
        data: JSON.stringify({}),
        success: function (data) {
            // 接口请求成功后的操作
            if (data.result != []) {
                // console.log(data);
                for (var i = 0; i < data.result.length; i++) {
                    //动态创建一个tr行标签,并且转换成jQuery对象
                    if (data.result[i].priority === '正常') {
                        var trTemp = $("<tr></tr>");
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].priority + "</td>");
                        $('.tabData').append(trTemp)
                    } else if (data.result[i].priority === '紧急') {
                        var trTemp = $("<tr style='color:#E6A23C;font-weight: bold;'></tr>");
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].priority + "</td>");
                        $('.tabData').append(trTemp)
                    } else {
                        var trTemp = $("<tr style='color:#F56C6C;font-weight: bold;'></tr>");
                        //往行里面追加 td单元格
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].customer + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='16%' align='center'>" + data.result[i].priority + "</td>");
                        $('.tabData').append(trTemp)
                    }

                }
            } else {
                // console.log("请求失败");
            }
        }
    });
    // 待完成工单
    $.ajax({
        url: '/roke/dashboard/wait_work_order',
        type: 'post',
        headers: {
            // 请求头
            'Content-Type': 'application/json'
        },
        dataType: 'json',
        data: JSON.stringify({}),
        success: function (data) {
            // 接口请求成功后的操作
            if (data.result != []) {
                // console.log(data);
                for (var i = 0; i < data.result.length; i++) {
                    //动态创建一个tr行标签,并且转换成jQuery对象
                    if (data.result[i].priority === '正常') {
                        //往行里面追加 td单元格
                        var trTemp = $("<tr></tr>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].process + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].team + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].work_center + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].employee + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].finish_qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].priority + "</td>");
                        $('.text1').append(trTemp)
                    } else if (data.result[i].priority === '紧急') {
                        //往行里面追加 td单元格
                        var trTemp = $("<tr style='color:#E6A23C;font-weight: bold;'></tr>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].process + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].team + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].work_center + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].employee + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].finish_qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].priority + "</td>");
                        $('.text1').append(trTemp)
                    } else {
                        var trTemp = $("<tr style='color:#F56C6C;font-weight: bold;'></tr>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].plan_date + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].code + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].process + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].product + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].team + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].work_center + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].employee + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].finish_qty + "</td>");
                        trTemp.append("<td width='10%' align='center'>" + data.result[i].priority + "</td>");
                        $('.text1').append(trTemp)
                    }

                }
            } else {
                // console.log("请求失败");
            }
        }
    });
}
