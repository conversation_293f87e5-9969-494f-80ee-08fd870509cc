function ReportEntryInit() {
    var color_index = ['primary', 'success', 'danger']
    $.ajax({
        url: '/roke/camp/get_camp_url',
        type: 'post',
        dataType: 'json',
        contentType: "application/json",
        async: false,//异步请求
        cache: false,
        data: JSON.stringify({now_person_qty: $("#now_person_qty").text()}),
        //执行成功的回调函数
        success: function (data) {
            client_datas = data["result"]["data"];
            // console.log(client_datas);
            var system_btn_html = ''
            for (var i = 0; i < client_datas.length; i++) {
                client = client_datas[i];
                system_btn_html += '<a class="btn btn-'+color_index[i%3]+' btn_camp float-right" href="'+client.url+'"><div class="camp_center"><span class="camp_center_text">'+client.name+'</span></div></a><br/>'
            }
            // console.log(system_btn_html);
            $('#camp_btns').html(system_btn_html);
        },
        //执行失败或错误的回调函数
        error: function (data) {
            console.error("获取失败!");
        }
    });
}

function ShowTime() {
    /*获取当前时间*/
    var relDate = new Date();
    var year=relDate.getFullYear()  //获取年
    var month=relDate.getMonth()+1;  //获取月，从 Date 对象返回月份 (0 ~ 11)，故在此处+1
    var day=relDate.getDay()    //获取日
    var days=relDate.getDate() //获取日期
    var hour=relDate.getHours()   //获取小时
    var minute=relDate.getMinutes()  //获取分钟
    var second=relDate.getSeconds()   //获取秒
    if(month<10) month="0"+month
    if(days<10) days="0"+days
    if(hour<10) hour="0"+hour
    if(minute<10) minute="0"+minute
    if(second<10) second="0"+second
    document.getElementById('time1').innerHTML = year+" 年 "+month+" 月 "+days+" 日 "+" "+hour+" : "+minute+" : "+second
}

function ReportInit() {
    var server_datas = {}
    var update_date = false;
    $.ajax({
        url: '/roke/production/get_kanban_data',
        type: 'post',
        dataType: 'json',
        contentType: "application/json",
        async: false,//异步请求
        cache: false,
        data: JSON.stringify({}),
        //执行成功的回调函数
        success: function (data) {
            $('#camp_name').html("实时生产看板");
            server_datas = data["result"];
            // 设置在岗人数
            $('#wait_task_qty').html(server_datas.count_wait_qty);
            $('#s1').html(server_datas.top_one.product_name);
            $('#s2').html(server_datas.top_two.product_name);
            $('#s3').html(server_datas.top_three.product_name);
            // 今日报工产品排行
            var today_work_record_group_html = ''
            for (var i = 0; i < server_datas.today_work_record_group.length; i++) {
                record = server_datas.today_work_record_group[i]
                today_work_record_group_html += '<li><p><span>'+record.process+'</span><span>'+record.product+'</span><span>'+record.qty+'</span></p></li>'
            }
            $('#today_work_record_group').html(today_work_record_group_html);
            // 今日报工明细
            var today_work_record_html = ''
            for (var i = 0; i < server_datas.today_work_record.length; i++) {
                record = server_datas.today_work_record[i]
                today_work_record_html += '<li><p><span>'+record.employee+'</span><span>'+record.product+'</span><span>'+record.process+'</span><span>'+record.qty+'</span></p></li>'
            }
            $('#today_work_record').html(today_work_record_html);
            // 订单进度
            var production_orders_html = ''
            for (var i = 0; i < server_datas.production_orders.length; i++) {
                record = server_datas.production_orders[i]
                production_orders_html += '<li><p><span>'+record.code+'</span><span>'+record.customer+'</span><span>'+record.schedule+'</span><span>'+record.plan_date+'</span></p></li>'
            }
            $('#production_orders').html(production_orders_html);
            // 设置会议签到
            var warning_html = ''
            for (var i = 0; i < server_datas.warning_record.length; i++) {
                record = server_datas.warning_record[i]
                warning_html += '<li><p><span>'+record.work_order+'</span><span>'+record.schedule+'</span><span>'+record.plan_date+'</span><span>'+record.state+'</span></p></li>'
            }
            $('#warning_data').html(warning_html);
            // 饮酒检查
            var check_drink_html = ''
            for (var i = 0; i < server_datas.check_drink_datas.length; i++) {
                check_drink = server_datas.check_drink_datas[i]
                check_drink_html += '<li><p><span>'+check_drink.check_time+'</span><span>'+check_drink.person_name+'</span><span>'+check_drink.state+'</span></p></li>'
            }
            $('#check_drink_data').html(check_drink_html);
            // console.log(server_datas);
        },
        //执行失败或错误的回调函数
        error: function (data) {
            console.error("获取失败!");
        }
    });

    echarts_1();
    zb1();
    zb2();
    zb3();
    echarts_3();

    function echarts_1() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('echart1'));
        option = {
            tooltip: {
                trigger: 'item',
                formatter: "{b} : {c} ({d}%)"
            },
            legend: {
                bottom: 0,
                height: 160,
                itemWidth: 10,
                itemHeight: 10,
                itemGap: 10,
                textStyle: {
                    color: 'rgba(255,255,255,.6)',
                    fontSize: 11
                },
                orient: 'horizontal',
                data: server_datas.work_record_title
            },
            calculable: true,
            series: [
                {
                    name: ' ',
                    color: ['#62c98d', '#cf822f', '#cf4c89', '#d02323', '#e8d057', '#67a0dc','#62c98d', '#cf822f', '#cf4c89', '#d02323', '#e8d057', '#67a0dc'],
                    type: 'pie',
                    // radius: [35, 70],
                    center: ['50%', '40%'],
                    // roseType: 'radius',
                    avoidLabelOverlap: true,
                    minAngle:10,
                    label: {
                        normal: {
                            position: 'inner',
                            show: false
                        },
                        emphasis: {
                            show: true
                        }
                    },

                    lableLine: {
                        normal: {
                            show: true
                        },
                        emphasis: {
                            show: true
                        }
                    },

                    data: server_datas.work_record_value
                },
            ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    function echarts_3() {
        // 基于准备好的dom，初始化echarts实例
        var previous_work_records = server_datas.previous_work_records
        var days = []
        var work_records = []
        for (var i = 0; i < previous_work_records.length; i++) {
            work_record = previous_work_records[i];
            days.push(work_record.range_day)
            work_records.push(work_record.qty)
        }
        console.log(work_records);
        console.log(days);
        var myChart = echarts.init(document.getElementById('echart3'));
        option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    lineStyle: {
                        color: '#57617B'
                    }
                }
            },
            legend: {

                data: ['报工数'],
                bottom: '0',
                textStyle: {
                    color: "#fff"
                },
                itemGap: 20,
            },
            grid: {
                left: '0',
                right: '20',
                top: '10',
                bottom: '20',
                containLabel: true
            },
            xAxis: [{
                type: 'category',
                boundaryGap: false,
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: 'rgba(255,255,255,.6)'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,.1)'
                    }
                },
                data: days.reverse()
            }, {}],
            yAxis: [{
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: 'rgba(255,255,255,.6)'
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,.1)'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,.1)'
                    }
                }
            }],
            series: [{
                name: '报工数',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 5,
                showSymbol: false,
                lineStyle: {
                    normal: {
                        width: 2
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(39, 122,206, 0.3)'
                        }, {
                            offset: 0.8,
                            color: 'rgba(39, 122,206, 0)'
                        }], false),
                        shadowColor: 'rgba(0, 0, 0, 0.1)',
                        shadowBlur: 10
                    }
                },
                itemStyle: {
                    normal: {
                        color: '#277ace',
                        borderColor: 'rgba(0,136,212,0.2)',
                        borderWidth: 12
                    }
                },
                data: work_records.reverse()
            }]
        };
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    function zb1() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('zb1'));
        var value = server_datas.count_wait_qty
        option = {
            series: [{
                type: 'pie',
                radius: ['60%', '70%'],
                color: '#49bcf7',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: value,
                    // name: server_datas.top_one.product_name,
                    label: {
                        normal: {
                            formatter: server_datas.top_one.qty + "",
                            textStyle: {
                                color: '#aaa',
                                fontSize: 24
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    function zb2() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('zb2'));
        var value = server_datas.top_two.qty
        option = {

            //animation: false,
            series: [{
                type: 'pie',
                radius: ['60%', '70%'],
                color: '#cdba00',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: value,
                    label: {
                        normal: {
                            formatter: server_datas.top_two.qty + "",
                            textStyle: {
                                color: '#aaa',
                                fontSize: 24
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }

    function zb3() {
        // 基于准备好的dom，初始化echarts实例
        var myChart = echarts.init(document.getElementById('zb3'));
        var value = server_datas.top_three.qty
        option = {
            series: [{

                type: 'pie',
                radius: ['60%', '70%'],
                color: '#62c98d',
                label: {
                    normal: {
                        position: 'center'
                    }
                },
                data: [{
                    value: value,
                    name: server_datas.top_three.product_name,
                    label: {
                        normal: {
                            formatter: server_datas.top_three.qty + "",
                            textStyle: {
                                color: '#aaa',
                                fontSize: 24
                            }
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: 'rgba(255,255,255,.2)'
                        },
                        emphasis: {
                            color: '#fff'
                        }
                    },
                }]
            }]
        };
        myChart.setOption(option);
        window.addEventListener("resize", function () {
            myChart.resize();
        });
    }
}



		
		
		


		









