odoo.define('roke_education_manager.button', function (require) {
        "use strict";
        var rpc = require("web.rpc");
        var ListController = require('web.ListController');
        var ListView = require('web.ListView');

    //     ListController.include({
    //     renderButtons: function ($node) {
    //         this._super.apply(this, arguments);
    //         if (this.$buttons) {
    //             this.$buttons.on('click', '.o_list_student_start_exam', this._student_start_exam.bind(this));
    //         }
    //     },
    //     _student_start_exam: function () {
    //         var self = this;
    //         self.do_action({
    //             name: '同步退货单',
    //             res_model: 'action.sync.trade.return',
    //             views: [[false, 'form']],
    //             type: 'ir.actions.act_window',
    //             view_mode: 'form',
    //             target: 'new',
    //         });
    //     }
    // })

        var ImportViewMixin = {
            /**
             * @override
             */
            init: function (viewInfo, params) {
                var importEnabled = 'import_enabled' in params ? params.import_enabled : true;
                this.controllerParams.importEnabled = importEnabled;
            },
        };
        var ImportControllerMixin = {
            /**
             * @override
             */
            init: function (parent, model, renderer, params) {
                this.importEnabled = params.importEnabled;
            },
            //--------------------------------------------------------------------------
            // Private
            //--------------------------------------------------------------------------
            /**
             * Adds an event listener on the import button.
             *
             * @private
             */
            _bindImport: function () {
                if (!this.$buttons) {
                    return;
                }
                var self = this;
                /*分配考试*/
                this.$buttons.on('click', '.o_button_roke_student_all_exam', function () {
                    rpc.query({
                        model: 'roke.base.exam',
                        method: 'btn_exam',
                        args: ['']
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
                /*导入学生*/
                this.$buttons.on('click', '.o_button_roke_exam_import_student', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.base.exam',
                        method: 'btn_import_student',
                        args: [records]
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
                /*分配考题*/
                this.$buttons.on('click', '.o_button_roke_dispatch_data', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.base.exam',
                        method: 'btn_title_data',
                        args: [records]
                    }).then(function () {
                        self.trigger_up('reload');
                    });
                });
                // /*生成密码*/
                // this.$buttons.on('click', '.o_button_roke_generate_password', function () {
                //     var records = self.getSelectedIds()
                //     rpc.query({
                //         model: 'roke.base.exam',
                //         method: 'btn_generate_password',
                //         args: [records]
                //     }).then(function () {
                //         self.trigger_up('reload');
                //     });
                // });
                // /*下载密码*/
                // this.$buttons.on('click', '.o_button_roke_download_password', function () {
                //     var records = self.getSelectedIds()
                //     rpc.query({
                //         model: 'roke.base.exam',
                //         method: 'download_password',
                //         args: [records]
                //     }).then(function (action_dict) {
                //         self.do_action(
                //             action_dict
                //         )
                //     });
                // });
                /*开始考试*/
                this.$buttons.on('click', '.o_button_roke_student_start_exam', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.base.exam',
                        method: 'start_exam',
                        args: [records]
                    }).then(function () {
                        self.trigger_up('reload');
                    });
                });
                /*推送成绩*/
                this.$buttons.on('click', '.o_button_roke_push_grade', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'push_grade',
                        args: [records]
                    }).then(function () {
                        self.trigger_up('reload');
                    });
                });
                /*答案解析*/
                this.$buttons.on('click', '.o_button_roke_answer_analysis', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'answer_analysis',
                        args: [records]
                    }).then(function () {
                        self.trigger_up('reload');
                    });
                });
                /*考试暂停*/
                this.$buttons.on('click', '.o_button_roke_exam_suspend', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'exam_suspend',
                        args: [records]
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
                /*考试延时*/
                this.$buttons.on('click', '.o_button_roke_exam_delayed', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'exam_delayed',
                        args: [records]
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
                /*继续考试*/
                this.$buttons.on('click', '.o_button_roke_exam_continue', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'exam_continue',
                        args: [records]
                    }).then(function () {
                        self.trigger_up('reload');
                    });
                });
                /*强制交卷*/
                this.$buttons.on('click', '.o_button_roke_compel_over_exam', function () {
                    var records = self.getSelectedIds()
                    rpc.query({
                        model: 'roke.subject.student.exam',
                        method: 'compel_over_exam',
                        args: [records]
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )

                    });
                });
                /*导入学生*/
                this.$buttons.on('click', '.o_button_roke_student_import', function () {
                    rpc.query({
                        model: 'roke.employee',
                        method: 'excel_import_student',
                        args: ['']
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
                /*导入题库*/
                this.$buttons.on('click', '.o_button_roke_subject_title_import', function () {
                    rpc.query({
                        model: 'roke.subject.title.data',
                        method: 'excel_import_subject_title',
                        args: ['']
                    }).then(function (action_dict) {
                        self.do_action(
                            action_dict
                        )
                    });
                });
            }
        };
        ListView.include({
            init: function () {
                this._super.apply(this, arguments);
                ImportViewMixin.init.apply(this, arguments);
            },
        });
        ListController.include({
            init: function () {
                this._super.apply(this, arguments);
                ImportControllerMixin.init.apply(this, arguments);
            },
            //--------------------------------------------------------------------------
            // Public
            //--------------------------------------------------------------------------
            /**
             * Extends the renderButtons function of ListView by adding an event listener
             * on the import button.
             *
             * @override
             */
            renderButtons: function () {
                this._super.apply(this, arguments);
                ImportControllerMixin._bindImport.call(this);
                var self = this;
                console.log('+++++++++++', this)
                var context = this.initialState.context.batch_button;
                var ongoing_context = this.initialState.context.exam_ongoing;
                var done_context = this.initialState.context.exam_done;
                /*分配考试*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_student_all_exam").css("display", "inline-block");
                }
                /*导入学生*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_exam_import_student").css("display", "inline-block");
                }
                /*分配考题*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_dispatch_data").css("display", "inline-block");
                }
                /*生成密码*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_generate_password").css("display", "inline-block");
                }
                /*下载密码*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_download_password").css("display", "inline-block");
                }
                /*开始考试*/
                if (context && this.modelName === "roke.base.exam") {
                    self.$buttons.find(".o_button_roke_student_start_exam").css("display", "inline-block");
                }
                /*推送成绩*/
                if (done_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_push_grade").css("display", "inline-block");
                }
                /*答案解析*/
                if (done_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_answer_analysis").css("display", "inline-block");
                }
                /*考试暂停*/
                if (ongoing_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_exam_suspend").css("display", "inline-block");
                }
                /*考试延时*/
                if (ongoing_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_exam_delayed").css("display", "inline-block");
                }
                /*继续考试*/
                if (ongoing_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_exam_continue").css("display", "inline-block");
                }
                /*强制交卷*/
                if (ongoing_context && this.modelName === "roke.subject.student.exam") {
                    self.$buttons.find(".o_button_roke_compel_over_exam").css("display", "inline-block");
                }
                /*导入学生*/
                if (this.modelName === "roke.employee") {
                    self.$buttons.find(".o_button_roke_student_import").css("display", "inline-block");
                }
                /*导入题库*/
                if (this.modelName === "roke.subject.title.data") {
                    self.$buttons.find(".o_button_roke_subject_title_import").css("display", "inline-block");
                }
            }
        });
    }
);