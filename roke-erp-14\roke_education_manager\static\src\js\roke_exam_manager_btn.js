odoo.define('roke_exam_manager.tree.button', function (require) {
    "use strict";
    var ajax = require('web.ajax');
    let ListController = require('web.ListController');
    ListController.include({
        renderButtons: function ($node) {
            let $buttons = this._super.apply(this, arguments);
            let tree_model = this.modelName;
            let context = this.initialState.context.batch_button;
            // 只在学生考试模型的tree视图中出现且需要有context
            if (context && tree_model === 'roke.subject.student.exam') {
                let but_suspend = "<button type=\"button\" t-if=\"widget.modelName == 'roke.subject.student.exam'\" class=\"btn btn-primary\">暂停考试</button>";
                let button_suspend = $(but_suspend).click(this.proxy('exam_suspend_button_fuc')); // 调用方法
                // this.$buttons.prepend(button_suspend); //放在前面
                this.$buttons.append(button_suspend);   // 放在后面
            }
            if (context && tree_model === 'roke.subject.student.exam') {
                let but_delayed = "<button type=\"button\" t-if=\"widget.modelName == 'roke.subject.student.exam'\" class=\"btn btn-primary\">考试延时</button>";
                let button_delayed = $(but_delayed).click(this.proxy('exam_delayed_button_fuc')); // 调用方法
                // this.$buttons.prepend(button_delayed); //放在前面
                this.$buttons.append(button_delayed);   // 放在后面
            }
            return $buttons;
        },
        // 按钮功能：弹出form视图
        // course_test_button_form: function () {
        //     let action = {
        //         name: '课程',
        //         type: 'ir.actions.act_window',
        //         res_model: 'myproject.course',
        //         view_mode: 'form',
        //         view_type: 'form',
        //         views: [[false, 'form']],
        //     };
        //     let self = this;
        //     this.do_action(action, {
        //         on_close: function () {
        //             self.trigger_up('reload');
        //         }
        //     });
        // },
        // 按钮功能，调用一个类中的方法中的指定函数
        exam_suspend_button_fuc: function () {
            var records = this.getSelectedIds();  // 获取选择的对象
            ajax.jsonRpc('/web/dataset/call_kw', 'call', {
                model: 'roke.subject.student.exam',
                method: 'exam_suspend',
                args: [records],
                kwargs: { }
            }).then(function (url) {
            });
        },
        // 按钮功能，调用一个类中的方法中的指定函数
        exam_delayed_button_fuc: function () {
            var records = this.getSelectedIds();  // 获取选择的对象
            ajax.jsonRpc('/web/dataset/call_kw', 'call', {
                model: 'roke.subject.student.exam',
                method: 'exam_delayed',
                args: [records],
                kwargs: { }
            }).then(function (url) {
            });
        },
    });
});