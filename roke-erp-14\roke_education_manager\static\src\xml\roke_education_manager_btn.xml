<?xml version="1.0" encoding="UTF-8"?>
<template id="template" xml:space="preserve">
    <t t-name="jdgl.Btn">
        <button type="button" class="btn btn-primary o_button_roke_student_all_exam mx-1" style="display:none;">
            发起考试
        </button>
        <button type="button" class="btn btn-primary o_button_roke_exam_import_student mx-1" style="display:none;">
            导入学生
        </button>
        <button type="button" class="btn btn-primary o_button_roke_dispatch_data mx-1" style="display:none;">
            分配考题
        </button>
<!--        <button type="button" class="btn btn-primary o_button_roke_generate_password mx-1" style="display:none;">-->
<!--            生成密码-->
<!--        </button>-->
<!--        <button type="button" class="btn btn-primary o_button_roke_download_password mx-1" style="display:none;">-->
<!--            下载密码-->
<!--        </button>-->
        <button type="button" class="btn btn-primary o_button_roke_student_start_exam mx-1" style="display:none;">
            开始考试
        </button>
        <button type="button" class="btn btn-primary o_button_roke_push_grade mx-1" style="display:none;">
            推送成绩
        </button>
        <button type="button" class="btn btn-primary o_button_roke_answer_analysis mx-1" style="display:none;">
            答案解析
        </button>
        <button type="button" class="btn btn-primary o_button_roke_exam_suspend mx-1" style="display:none;">
            考试暂停
        </button>
        <button type="button" class="btn btn-primary o_button_roke_exam_delayed mx-1" style="display:none;">
            考试延时
        </button>
        <button type="button" class="btn btn-primary o_button_roke_exam_continue mx-1" style="display:none;">
            继续考试
        </button>
        <button type="button" class="btn btn-primary o_button_roke_compel_over_exam mx-1" style="display:none;">
            强制交卷
        </button>
        <button type="button" class="btn btn-primary o_button_roke_student_import mx-1" style="display:none;">
            导入学生
        </button>
        <button type="button" class="btn btn-primary o_button_roke_subject_title_import mx-1" style="display:none;">
            导入题库
        </button>
    </t>
    <t t-extend="ListView.buttons">
        <t t-jquery="button:first" t-operation="after">
            <t t-call="jdgl.Btn"/>
        </t>
    </t>
</template>