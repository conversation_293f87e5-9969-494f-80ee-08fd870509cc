<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考试-->
    <!--search-->
    <record id="view_roke_base_exam_search" model="ir.ui.view">
        <field name="name">roke.base.exam.search</field>
        <field name="model">roke.base.exam</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="number"/>
                <filter string="有主考试" name="have_parent_id" domain="[('parent_id', '!=', False)]"/>
                <searchpanel>
                    <field name="parent_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>

        </field>
    </record>
<!--    <record id="view_roke_base_exam_done_search" model="ir.ui.view">-->
<!--        <field name="name">roke.base.exam.done.search</field>-->
<!--        <field name="model">roke.base.exam</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <search>-->
<!--                <field name="name"/>-->
<!--                <field name="number"/>-->
<!--                <filter string="未推送成绩" name="is_can_see_score" domain="[('is_can_see_score', '=', False)]"/>-->
<!--                <filter string="未推送答案" name="is_can_see_true_answer" domain="[('is_can_see_true_answer', '=', False)]"/>-->
<!--&lt;!&ndash;                <searchpanel>&ndash;&gt;-->
<!--&lt;!&ndash;                    <field name="parent_id" icon="fa-users"/>&ndash;&gt;-->
<!--&lt;!&ndash;                </searchpanel>&ndash;&gt;-->
<!--            </search>-->

<!--        </field>-->
<!--    </record>-->
    <!--tree-->
    <record id="view_roke_base_exam_tree" model="ir.ui.view">
        <field name="name">roke.base.exam.tree</field>
        <field name="model">roke.base.exam</field>
        <field name="arch" type="xml">
            <tree string="考试" create="0" edit="0">
                <field name="number" optional="show"/>
                <field name="name"/>
                <field name="course_id" optional="show"/>
                <field name="start_time"/>
                <field name="time_length"/>
                <field name="end_time"/>
                <field name="total_marks"/>
                <field name="pattern_type" optional="show"/>
                <field name="dispatch_type" optional="show"/>
                <field name="rule_id" optional="show"/>
                <field name="test_paper_id" optional="show"/>
                <field name="checkbox_score_type" optional="show"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_base_exam_form" model="ir.ui.view">
        <field name="name">roke.base.exam.form</field>
        <field name="model">roke.base.exam</field>
        <field name="arch" type="xml">
            <form string="考试" create="0">
                <header>
                    <button name="split_round" string="拆分场次" type="object" class="oe_highlight"
                            attrs="{'invisible':['|', ('state','!=','draft'), ('parent_id','!=',False)]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,data_dispatch,wait_exam,exam_taking,done"/>
                    <field name="forbidden_state" invisible="1"/>
                </header>
                    <widget name="web_ribbon" text="练习模式" bg_color="bg-danger"
                            attrs="{'invisible': [('pattern_type', '=', 'exam')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number" attrs="{'readonly':[('state','!=','draft')]}" force_save="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="name" attrs="{'readonly':[('state','!=','draft')]}" force_save="1"/>
<!--                            <field name="number" readonly="1" force_save="1"/>-->
                            <field name="dispatch_type" options="{'no_create': True, 'no_open': True}" required="1"
                                   attrs="{'invisible':[('rule_id','=',False)], 'readonly':[('state','!=','draft')]}" force_save="1"/>
                            <field name="course_id" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'readonly':[('state','!=','draft')]}" force_save="1"/>
                            <field name="total_marks" readonly="1" force_save="1"/>
                            <field name="is_dispatch" invisible="1"/>
                            <field name="is_main_exam" invisible="1"/>
                            <field name="parent_id" invisible="1"/>
                        </group>
                        <group>
                            <field name="pattern_type" attrs="{'readonly':[('state','!=','draft')]}"
                                   required="1" force_save="1"/>
                            <field name="rule_id" force_save="1" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible':[('rule_id','=',False)], 'readonly':[('state','!=','draft')]}"/>
                            <field name="test_paper_id" force_save="1" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible':[('test_paper_id','=',False)], 'readonly':[('state','!=','draft')]}"/>
                            <field name="checkbox_score_type" attrs="{'readonly':[('state','!=','draft')]}"
                                   required="1" force_save="1"/>
                            <field name="start_time" force_save="1" required="1"
                                   attrs="{'invisible':[('pattern_type','=','practice')],
                                            'readonly':[('state','!=','draft')], 'required': [('pattern_type','=','exam')]}"/>
                            <field name="time_length" force_save="1" required="1"
                                   attrs="{'invisible':[('pattern_type','=', 'practice')],
                                           'readonly':[('state','!=','draft')], 'required': [('pattern_type','=','exam')]}"/>
                            <field name="end_time" force_save="1" required="1"
                                   attrs="{'invisible':[('pattern_type','=', 'practice')],
                                           'readonly':[('state','!=','draft')], 'required': [('pattern_type','=','exam')]}"/>

                        </group>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述" readonly="1" force_save="1"/>
                    </group>
                    <notebook>
                        <page string="考生明细" attrs="{'invisible': [('is_main_exam', '=', True)]}">
                            <field name="exam_line_ids" readonly="1" force_save="1">
<!--                                <tree editable="bottom">-->
<!--                                    <field name="sequence" widget="handle"/>-->
<!--                                    <field name="title_date_id" options="{'no_create': True, 'no_open': True}"/>-->
<!--                                    <field name="course_id" options="{'no_create': True, 'no_open': True}"/>-->
<!--                                    <field name="project_id" options="{'no_create': True, 'no_open': True}"/>-->
<!--                                    <field name="total_marks"/>-->
<!--                                    <field name="remark"/>-->
<!--                                </tree>-->
                            </field>
                        </page>
                        <page string="场次信息" attrs="{'invisible': [('is_main_exam', '=', False)]}">
                            <field name="exam_ids" readonly="1" force_save="1">
                                <tree editable="bottom">
                                    <field name="round_name"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_base_exam_action" model="ir.actions.act_window">
        <field name="name">考前管理</field>
        <field name="res_model">roke.base.exam</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('state', 'in', ['draft', 'data_dispatch'])]</field>
        <field name="context">{'batch_button': True}</field>
        <field name="search_view_id" ref="view_roke_base_exam_search"/>

    </record>

    <record id="view_roke_base_exam_taking_action" model="ir.actions.act_window">
        <field name="name">考中管理</field>
        <field name="res_model">roke.base.exam</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('state', 'in', ['wait_exam', 'exam_taking', 'exam_suspend'])]</field>
        <field name="context">{}</field>
        <field name="search_view_id" ref="view_roke_base_exam_search"/>

    </record>
<!--    <record id="view_roke_base_exam_done_action" model="ir.actions.act_window">-->
<!--        <field name="name">已完成考试</field>-->
<!--        <field name="res_model">roke.base.exam</field>-->
<!--        <field name="view_mode">tree,form</field>-->
<!--        <field name="type">ir.actions.act_window</field>-->
<!--        <field name="domain">[('state', '=', 'done')]</field>-->
<!--        <field name="context">{'exam_done': True}</field>-->
<!--        <field name="search_view_id" ref="view_roke_base_exam_done_search"/>-->
<!--    </record>-->
<!--    <record id="action_exam_batch_dispatch_data" model="ir.actions.server">-->
<!--        <field name="name">批量分配考题</field>-->
<!--        <field name="model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_view_types">list</field>-->
<!--        <field name="state">code</field>-->
<!--        <field name="code">action = records.btn_title_data()</field>-->
<!--    </record>-->

    <record id="action_generate_password" model="ir.actions.server">
        <field name="name">生成密码</field>
        <field name="model_id" ref="roke_education_manager.model_roke_base_exam"/>
        <field name="binding_model_id" ref="roke_education_manager.model_roke_base_exam"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.btn_generate_password()</field>
    </record>

    <record id="action_download_password" model="ir.actions.server">
        <field name="name">下载密码</field>
        <field name="model_id" ref="roke_education_manager.model_roke_base_exam"/>
        <field name="binding_model_id" ref="roke_education_manager.model_roke_base_exam"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.download_password()</field>
    </record>

<!--    <record id="action_push_grade" model="ir.actions.server">-->
<!--        <field name="name">推送成绩</field>-->
<!--        <field name="model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_view_types">list</field>-->
<!--        <field name="state">code</field>-->
<!--        <field name="code">action = records.push_grade()</field>-->
<!--    </record>-->

<!--    <record id="action_answer_analysis" model="ir.actions.server">-->
<!--        <field name="name">答案解析</field>-->
<!--        <field name="model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_model_id" ref="roke_education_manager.model_roke_base_exam"/>-->
<!--        <field name="binding_view_types">list</field>-->
<!--        <field name="state">code</field>-->
<!--        <field name="code">action = records.answer_analysis()</field>-->
<!--    </record>-->

</odoo>
