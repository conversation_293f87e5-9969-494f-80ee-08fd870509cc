<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--学校组织管理-->
    <!--search-->
    <record id="view_roke_base_org_search" model="ir.ui.view">
        <field name="name">roke.base.org.search</field>
        <field name="model">roke.base.org</field>
        <field name="arch" type="xml">
            <search string="学校组织管理">
                <field name="class_name"/>
                <field name="parent_id"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="上级组织" name="groupby_parent_id" context="{'group_by':'parent_id'}"/>
                </group>
                <searchpanel>
                    <field name="parent_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_base_org_tree" model="ir.ui.view">
        <field name="name">roke.base.org.tree</field>
        <field name="model">roke.base.org</field>
        <field name="arch" type="xml">
            <tree string="学校组织管理">
                <field name="class_name"/>
                <field name="parent_id"/>
                <field name="child_ids" widget="many2many_tags"/>
                <field name="student_count"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_base_org_form" model="ir.ui.view">
        <field name="name">roke.base.org.form</field>
        <field name="model">roke.base.org</field>
        <field name="arch" type="xml">
            <form string="学校组织管理">
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger"
                            attrs="{'invisible': [('active', '=', True)]}"/>
                    <div class="oe_title">
                        <h1 class="d-flex">
                            <field name="class_name" placeholder="组织名称" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="parent_id" options="{'no_create': True, 'no_open': True}"
                            domain="[('id', '!=', id)]"/>
                            <field name="active" invisible="1"/>
                        </group>
                        <group>
                            <field name="student_count" attrs="{'invisible': [('student_count', '=', 0)]}"/>
                            <field name="student_ids" widget="many2many_tags"
                                   attrs="{'invisible': [('student_count', '=', 0)]}"/>
                        </group>
                    </group>
                    <group>
                        <field name="describe" nolabel="1" placeholder="此处可以填写备注或描述"/>
                    </group>
                    <notebook>
                        <page string="下级组织">
                            <field name="child_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="class_name"/>
                                    <field name="describe"/>
                                    <field name="student_count"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_base_org_action" model="ir.actions.act_window">
        <field name="name">学校组织管理</field>
        <field name="res_model">roke.base.org</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个组织。
            </p>
            <p>
                或者您也可以选择批量导入功能一次性导入多个组织。
            </p>
        </field>
    </record>

</odoo>
