<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--教师档案-->
    <!--search-->
    <record id="view_roke_base_teacher_search" model="ir.ui.view">
        <field name="name">roke.base.teacher.search</field>
        <field name="model">roke.base.teacher</field>
        <field name="arch" type="xml">
            <search string="教师档案">
                <field name="number"/>
                <field name="name"/>
                <field name="phone"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_base_teacher_tree" model="ir.ui.view">
        <field name="name">roke.base.teacher.tree</field>
        <field name="model">roke.base.teacher</field>
        <field name="arch" type="xml">
            <tree string="教师档案">
                <field name="number"/>
                <field name="name"/>
                <field name="phone"/>
                <field name="team_ids" widget="many2many_tags"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_base_teacher_form" model="ir.ui.view">
        <field name="name">roke.base.teacher.form</field>
        <field name="model">roke.base.teacher</field>
        <field name="arch" type="xml">
            <form string="教师档案">
                <header>
                    <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                    <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                    <field name="forbidden_state" widget="statusbar"/>
                </header>

                <widget name="web_ribbon" text="禁用" bg_color="bg-danger"
                        attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                <div class="oe_title">
                    <label for="number" class="oe_edit_only"/>
                    <h1 class="d-flex">
                        <field name="number"/>
                    </h1>
                </div>
                <div class="oe_title">
                    <label for="name" class="oe_edit_only"/>
                    <h1 class="d-flex">
                        <field name="name" required="True"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="phone" required="1"/>
                    </group>
                    <group>
                        <field name="team_ids" options="{'no_create': True, 'no_open': True}"/>
                    </group>
                </group>
                <group>
                    <field name="remark" placeholder="此处可以填写备注或描述"/>
                </group>
<!--                <notebook>-->
<!--                    <page string="班级明细">-->
<!--                        <field name="team_ids" readonly="1">-->
<!--                        </field>-->
<!--                    </page>-->
<!--                </notebook>-->

                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_base_teacher_action" model="ir.actions.act_window">
        <field name="name">教师档案</field>
        <field name="res_model">roke.base.teacher</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个教师。
            </p>
            <p>
                或者您也可以选择批量导入功能一次性导入多个教师。
            </p>
        </field>
    </record>

</odoo>
