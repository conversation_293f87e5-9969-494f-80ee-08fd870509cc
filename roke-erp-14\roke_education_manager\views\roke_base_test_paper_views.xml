<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--试卷管理-->
    <!--search-->
    <record id="view_roke_base_test_paper_search" model="ir.ui.view">
        <field name="name">roke.base.test.paper.search</field>
        <field name="model">roke.base.test.paper</field>
        <field name="arch" type="xml">
            <search string="试卷管理">
                <field name="number"/>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_base_test_paper_tree" model="ir.ui.view">
        <field name="name">roke.base.test.paper.tree</field>
        <field name="model">roke.base.test.paper</field>
        <field name="arch" type="xml">
            <tree string="试卷管理">
                <field name="number"/>
                <field name="name"/>
                <field name="course_id"/>
                <field name="total_mark"/>
                <field name="is_random"/>
                <field name="title_detail"/>
                <field name="remark"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_base_test_paper_form" model="ir.ui.view">
        <field name="name">roke.base.test.paper.form</field>
        <field name="model">roke.base.test.paper</field>
        <field name="arch" type="xml">
            <form string="试卷管理">
                <header>
                </header>
                <div class="oe_title">
                    <label for="number" class="oe_edit_only"/>
                    <h1 class="d-flex">
                        <field name="number" readonly="1" force_save="1"/>
                    </h1>
                </div>
                <div class="oe_title">
                    <label for="name" class="oe_edit_only"/>
                    <h1 class="d-flex">
                        <field name="name" required="True"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="course_id" options="{'no_create': True, 'no_open': True}"/>
                        <field name="is_random"/>
                    </group>
                    <group>
                        <field name="total_mark"/>
                    </group>
                </group>
                <group>
                    <field name="remark" placeholder="此处可以填写备注或描述"/>
                </group>
                <notebook>
                    <page string="题目明细">
                        <field name="title_ids">
                        </field>
                    </page>
                </notebook>

                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_base_test_paper_action" model="ir.actions.act_window">
        <field name="name">试卷管理</field>
        <field name="res_model">roke.base.test.paper</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个试题。
            </p>
        </field>
    </record>

</odoo>
