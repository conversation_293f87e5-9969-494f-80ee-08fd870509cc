<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--学生档案-->
    <!--search-->
    <record id="view_roke_employee_search_inherit" model="ir.ui.view">
        <field name="name">roke.employee.search.inherit</field>
        <field name="model">roke.employee</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_employee_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='team_id'][1]" position="replace">
                <field name="org_id"/>
            </xpath>
            <xpath expr="//group[1]" position="replace">
                <group expand="0" string="Group By">
                    <filter string="学校组织" name="group_school_org" context="{'group_by': 'org_id'}"/>
                </group>
            </xpath>
            <xpath expr="//searchpanel[1]" position="replace">
                <searchpanel>
                    <field name="org_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </xpath>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_employee_tree_inherit" model="ir.ui.view">
        <field name="name">roke.employee.tree.inherit</field>
        <field name="model">roke.employee</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="student_type" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='team_id']" position="replace">
                <field name="org_id"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_employee_form_inherit" model="ir.ui.view">
        <field name="name">roke.employee.form.inherit</field>
        <field name="model">roke.employee</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='team_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='team_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='code']" position="after">
                <field name="org_id"/>
            </xpath>
        </field>
    </record>
</odoo>