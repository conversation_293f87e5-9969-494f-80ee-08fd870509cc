<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考试科目设置-->
    <!--search-->
    <record id="view_roke_subject_course_search" model="ir.ui.view">
        <field name="name">roke.subject.course.search</field>
        <field name="model">roke.subject.course</field>
        <field name="arch" type="xml">
            <search string="考试科目设置">
                <field name="number"/>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_course_tree" model="ir.ui.view">
        <field name="name">roke.subject.course.tree</field>
        <field name="model">roke.subject.course</field>
        <field name="arch" type="xml">
            <tree string="考试科目设置">
                <field name="number"/>
                <field name="name"/>
                <field name="org_ids" widget="many2many_tags"/>
                <field name="forbidden_state"/>
                <field name="remark"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_course_form" model="ir.ui.view">
        <field name="name">roke.subject.course.form</field>
        <field name="model">roke.subject.course</field>
        <field name="arch" type="xml">
            <form string="考试科目设置">
                <header>
                     <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                     <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                     <field name="forbidden_state" widget="statusbar"/>
                </header>
                <sheet>
                    <widget name="web_ribbon" text="禁用" bg_color="bg-danger"
                            attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number" readonly="1" force_save="1"/>
                        </h1>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="name" required="True"/>
                        </h1>
                    </div>
<!--                    <group>-->
<!--                        <group>-->
<!--                            <field name="org_ids" widget="many2many_tags"-->
<!--                                   options="{'no_create': True, 'no_open': True}"/>-->
<!--                        </group>-->
<!--                    </group>-->
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述" />
                    </group>
                    <notebook>
                        <page string="对应班级">
                            <field name="org_ids">
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_course_action" model="ir.actions.act_window">
        <field name="name">考试科目设置</field>
        <field name="res_model">roke.subject.course</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个考试科目。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个考试科目。
          </p>
        </field>
    </record>

</odoo>
