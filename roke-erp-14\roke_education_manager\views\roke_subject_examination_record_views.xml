<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考试记录-->
    <!--search-->
    <record id="view_roke_subject_examination_record_search" model="ir.ui.view">
        <field name="name">roke.subject.examination.record.search</field>
        <field name="model">roke.subject.examination.record</field>
        <field name="arch" type="xml">
            <search string="考试记录">
                <field name="org_id"/>
                <field name="student_id"/>
                <filter string="待确认" name="wait_confirm" domain="[('state', '=', 'wait_confirm')]"/>
                <filter string="完成" name="done" domain="[('state', '=', 'done')]"/>
                <group expand="0" string="Group By">
                    <filter string="组织" name="groupby_org_id" context="{'group_by':'org_id'}"/>
                    <filter string="学生" name="groupby_student_id" context="{'group_by':'student_id'}"/>
                    <filter string="状态" name="groupby_state" context="{'group_by':'state'}"/>
                    <separator/>
                    <filter string="已归档" name="is_active" domain="[('active', '=', False)]"/>
                </group>
                <searchpanel>
                    <field name="main_exam_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_examination_record_tree" model="ir.ui.view">
        <field name="name">roke.subject.examination.record.tree</field>
        <field name="model">roke.subject.examination.record</field>
        <field name="arch" type="xml">
            <tree string="考试记录" create="0" edit="0" delete="0">
                <field name="org_id"/>
                <field name="exam_id"/>
                <field name="student_id"/>
                <field name="employee_team"/>
                <field name="checkbox_score_type"/>
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="duration"/>
                <field name="total_marks"/>
                <field name="total_score"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_examination_record_form" model="ir.ui.view">
        <field name="name">roke.subject.examination.record.form</field>
        <field name="model">roke.subject.examination.record</field>
        <field name="arch" type="xml">
            <form string="考试记录" create="0" edit="0" delete="0">
                <header>
                    <button name="replenish_duration" string="补时" type="object" class="oe_highlight"
                            attrs="{'invisible':[('state','!=','wait_confirm')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                    <group>
                        <group>
                            <field name="org_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="pattern_type"/>
                            <field name="start_time"/>
                            <field name="end_time"/>
                            <field name="duration"/>
                            <field name="checkbox_score_type"/>
                        </group>
                        <group>
                            <field name="exam_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="student_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="employee_team"/>
                            <field name="can_see_score" invisible="1"/>
                            <field name="can_see_true_answer" invisible="1"/>
                            <field name="objectivity_score"/>
                            <field name="subjectivity_score"/>
                            <field name="objective_score"/>
                            <field name="total_score"/>
                        </group>
                    </group>
                    <group>
                        <field name="remark"/>
                    </group>
                    <notebook>
                        <page string="实操评分" invisible="1">
                            <field name="line_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="course_id"/>
                                    <field name="project_id"/>
                                    <field name="title_data_id"/>
                                    <field name="title_data_line_id"/>
                                    <field name="field_name"/>
                                    <field name="content"/>
                                    <field name="real_content"/>
                                    <field name="proportion_mark" sum="总分数"/>
                                    <field name="mark" sum="总得分"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="主观评分">
                            <field name="line1_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="course_id"/>
                                    <field name="project_id"/>
                                    <field name="title_data_id"/>
                                    <field name="content"/>
                                    <field name="true_content"/>
                                    <field name="real_content"/>
                                    <field name="proportion_mark" sum="总分数"/>
                                    <field name="mark" sum="总得分"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="客观评分">
                            <field name="objective_line_ids" readonly="1">
                                <tree editable="bottom">
                                    <field name="course_id"/>
                                    <field name="project_id"/>
                                    <field name="title_data_id"/>
                                    <field name="title_description"/>
                                    <field name="true_answer"/>
                                    <field name="real_answer"/>
                                    <field name="proportion_mark" sum="总分数"/>
                                    <field name="mark" sum="总得分"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_examination_record_action" model="ir.actions.act_window">
        <field name="name">考试记录</field>
        <field name="res_model">roke.subject.examination.record</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
    <!--action-->
    <record id="view_roke_subject_examination_result_action" model="ir.actions.act_window">
        <field name="name">考试结果</field>
        <field name="res_model">roke.subject.examination.record</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('state','=','done')]</field>
        <field name="context">{}</field>
    </record>

</odoo>
