<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--评分方式-->
    <!--search-->
    <record id="view_roke_subject_mark_type_search" model="ir.ui.view">
        <field name="name">roke.subject.mark.type.search</field>
        <field name="model">roke.subject.mark.type</field>
        <field name="arch" type="xml">
            <search string="评分方式">
                <field name="number"/>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_mark_type_tree" model="ir.ui.view">
        <field name="name">roke.subject.mark.type.tree</field>
        <field name="model">roke.subject.mark.type</field>
        <field name="arch" type="xml">
            <tree string="评分方式">
                <field name="number"/>
                <field name="name"/>
                <field name="forbidden_state"/>
                <field name="rules"/>
                <field name="remark"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_mark_type_form" model="ir.ui.view">
        <field name="name">roke.subject.mark.type.form</field>
        <field name="model">roke.subject.mark.type</field>
        <field name="arch" type="xml">
            <form string="评分方式">
                <header>
                     <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                     <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                     <field name="forbidden_state" widget="statusbar"/>
                </header>
                    <widget name="web_ribbon" text="禁用" bg_color="bg-danger" attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number" readonly="1" force_save="1"/>
                        </h1>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="name" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="rules" required="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述" />
                    </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_mark_type_action" model="ir.actions.act_window">
        <field name="name">评分方式</field>
        <field name="res_model">roke.subject.mark.type</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个评分方式。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个评分方式。
          </p>
        </field>
    </record>

</odoo>
