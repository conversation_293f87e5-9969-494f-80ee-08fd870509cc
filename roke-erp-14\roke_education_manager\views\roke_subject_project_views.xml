<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考试项目设置-->
    <!--search-->
    <record id="view_roke_subject_project_search" model="ir.ui.view">
        <field name="name">roke.subject.project.search</field>
        <field name="model">roke.subject.project</field>
        <field name="arch" type="xml">
            <search string="考试项目设置">
                <field name="number"/>
                <field name="name"/>
                <group expand="0" string="Group By">
                    <filter string="考试科目" name="group_course_id" context="{'group_by': 'course_id'}"/>
                </group>
                <searchpanel>
                    <field name="course_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_project_tree" model="ir.ui.view">
        <field name="name">roke.subject.project.tree</field>
        <field name="model">roke.subject.project</field>
        <field name="arch" type="xml">
            <tree string="考试项目设置">
                <field name="number"/>
                <field name="name"/>
                <field name="forbidden_state"/>
                <field name="course_id"/>
                <field name="evaluation_id"/>
                <field name="standard_score"/>
                <field name="remark"/>
                <field name="parent_id" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_project_form" model="ir.ui.view">
        <field name="name">roke.subject.project.form</field>
        <field name="model">roke.subject.project</field>
        <field name="arch" type="xml">
            <form string="考试项目设置">
                <header>
                     <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                     <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                     <field name="forbidden_state" widget="statusbar"/>
                </header>
                    <widget name="web_ribbon" text="禁用" bg_color="bg-danger" attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number" readonly="1" force_save="1"/>
                        </h1>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="name" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="project_type" widget="radio" required="1" options="{'horizontal': True}"/>
                            <field name="data_type"
                                   attrs="{'invisible':[('project_type','!=','objective')], 'required': [('project_type','=','objective')]}"/>
                            <field name="standard_score"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="course_id" options="{'no_create': True, 'no_open': True}"
                            domain="[('forbidden_state', '=', 'normal')]"/>
                            <field name="evaluation_id" options="{'no_create': True, 'no_open': True}"
                            domain="[('forbidden_state', '=', 'normal')]"/>
                            <field name="parent_id" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述" />
                    </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_project_action" model="ir.actions.act_window">
        <field name="name">考试项目设置</field>
        <field name="res_model">roke.subject.project</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
