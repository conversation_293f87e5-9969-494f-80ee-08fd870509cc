<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--抽题规则-->
    <!--search-->
    <record id="view_roke_subject_rules_search" model="ir.ui.view">
        <field name="name">roke.subject.rules.search</field>
        <field name="model">roke.subject.rules</field>
        <field name="arch" type="xml">
            <search string="抽题规则">
                <field name="number"/>
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_rules_tree" model="ir.ui.view">
        <field name="name">roke.subject.rules.tree</field>
        <field name="model">roke.subject.rules</field>
        <field name="arch" type="xml">
            <tree string="抽题规则">
                <field name="number"/>
                <field name="name"/>
                <field name="start_time"/>
                <field name="end_time"/>
                <field name="sum_mark"/>
                <field name="title_detail"/>
                <field name="forbidden_state"/>
                <field name="remark"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_rules_form" model="ir.ui.view">
        <field name="name">roke.subject.rules.form</field>
        <field name="model">roke.subject.rules</field>
        <field name="arch" type="xml">
            <form string="抽题规则">
                <header>
                     <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                     <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                     <field name="forbidden_state" widget="statusbar"/>
                </header>
                    <widget name="web_ribbon" text="禁用" bg_color="bg-danger" attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number" readonly="1" force_save="1"/>
                        </h1>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="name" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="start_time" required="1"/>
                            <field name="is_random"/>
                        </group>
                        <group>
                            <field name="end_time" required="1"/>
                            <field name="sum_mark"/>
                        </group>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述" />
                    </group>
                    <notebook>
                        <page string="抽题规则明细">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="project_id" options="{'no_create': True, 'no_open': True}" required="1"/>
                                    <field name="project_type" invisible="1"/>
                                    <field name="standard_score"/>
                                    <field name="title_count"/>
                                    <field name="project_mark"/>
                                    <field name="remark"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_rules_action" model="ir.actions.act_window">
        <field name="name">抽题规则</field>
        <field name="res_model">roke.subject.rules</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个抽题规则。
          </p>
        </field>
    </record>

</odoo>
