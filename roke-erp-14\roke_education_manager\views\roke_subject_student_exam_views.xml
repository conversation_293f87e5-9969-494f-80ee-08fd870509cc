<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考试内容-->
    <!--search-->
    <record id="view_roke_subject_student_exam_search" model="ir.ui.view">
        <field name="name">roke.subject.student.exam.search</field>
        <field name="model">roke.subject.student.exam</field>
        <field name="arch" type="xml">
            <search string="考试内容">
                <field name="employee_id"/>
                <field name="parent_id"/>
                <searchpanel>
                    <field name="parent_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_student_exam_tree" model="ir.ui.view">
        <field name="name">roke.subject.student.exam.tree</field>
        <field name="model">roke.subject.student.exam</field>
        <field name="arch" type="xml">
            <tree string="考试内容" create="0" edit="0" limit="100">
                <field name="parent_id"/>
                <field name="employee_id"/>
                <field name="employee_team"/>
                <field name="org_id"/>
                <field name="course_id"/>
                <field name="start_time"/>
                <field name="time_length"/>
                <field name="end_time"/>
                <field name="total_marks"/>
                <field name="pattern_type"/>
                <field name="checkbox_score_type"/>
                <field name="state"/>
                <field name="parent_id" invisible="1"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_student_exam_form" model="ir.ui.view">
        <field name="name">roke.subject.student.exam.form</field>
        <field name="model">roke.subject.student.exam</field>
        <field name="arch" type="xml">
            <form string="考试内容" create="0" edit="0">
                <header>
                    <button name="reset_state" string="重置为进行中" type="object" class="oe_highlight"
                            attrs="{'invisible':[('state','!=', 'done')]}" confirm="重置考试后考生考试记录将归档，确定要重置吗？"/>
                    <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                    <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,data_dispatch,wait_exam,exam_taking,done"/>
                    <field name="forbidden_state" invisible="1"/>
                </header>
                    <widget name="web_ribbon" text="练习模式" bg_color="bg-danger"
                            attrs="{'invisible': [('pattern_type', '=', 'exam')]}"/>
                    <group>
                        <group>
                            <field name="dispatch_type" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible':[('rule_id','=',False)]}" readonly="1" force_save="1"/>
                            <field name="course_id" options="{'no_create': True, 'no_open': True}" readonly="1" force_save="1"/>
                            <field name="total_marks" readonly="1" force_save="1"/>
                            <field name="start_time" readonly="1" force_save="1"
                                   attrs="{'invisible':[('pattern_type','=','practice')]}"/>
                            <field name="end_time" readonly="1" force_save="1"
                                   attrs="{'invisible':[('pattern_type','=', 'practice')]}"/>
                            <field name="real_start_time" readonly="1" force_save="1"/>
                            <field name="real_end_time" readonly="1" force_save="1"/>
                        </group>
                        <group>
                            <field name="employee_id" readonly="1" force_save="1"
                                   options="{'no_create': True, 'no_open': True}"/>
                            <field name="employee_team"/>
                            <field name="org_id" readonly="1" force_save="1"
                                   options="{'no_create': True, 'no_open': True}"/>
                            <field name="rule_id" readonly="1" force_save="1"
                                   options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible':[('rule_id','=',False)]}"/>
                            <field name="test_paper_id" readonly="1" force_save="1"
                                   options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible':[('test_paper_id','=',False)]}"/>
                            <field name="checkbox_score_type"/>
                            <field name="pattern_type" readonly="1" force_save="1"/>
                            <field name="time_length" readonly="1" force_save="1"
                                   attrs="{'invisible':[('pattern_type','=', 'practice')]}"/>
                            <field name="real_time_length" readonly="1" force_save="1"/>
                            <field name="delayed_length" readonly="1" force_save="1"
                                   attrs="{'invisible':[('delayed_length','=', 0)]}"/>
                            <field name="suspend_length" readonly="1" force_save="1"
                                   attrs="{'invisible':[('suspend_length','=', 0)]}"/>

                        </group>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写备注或描述"/>
                    </group>
                    <notebook>
                        <page string="考试内容">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="title_date_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="course_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="project_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="title_description"/>
                                    <field name="total_marks"/>
                                    <field name="remark"/>
                                </tree>
                            </field>
                        </page>
                        <page string="考试学生" invisible="1">
                            <field name="person_ids" readonly="1" force_save="1">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="employee_id" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="remark"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_student_exam_action" model="ir.actions.act_window">
        <field name="name">学生考试</field>
        <field name="res_model">roke.subject.student.exam</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('state', 'in', ['wait_exam', 'exam_taking', 'exam_suspend', 'wait_subjectivity'])]</field>
        <field name="context">{'exam_ongoing': True}</field>
    </record>
    <record id="view_roke_subject_student_exam_done_action" model="ir.actions.act_window">
        <field name="name">已完成考试</field>
        <field name="res_model">roke.subject.student.exam</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('state', '=', 'done')]</field>
        <field name="context">{'exam_done': True}</field>
    </record>

    <record id="action_restart_exam" model="ir.actions.server">
        <field name="name">重新考试</field>
        <field name="model_id" ref="roke_education_manager.model_roke_subject_student_exam"/>
        <field name="binding_model_id" ref="roke_education_manager.model_roke_subject_student_exam"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.restart_exam()</field>
    </record>

</odoo>
