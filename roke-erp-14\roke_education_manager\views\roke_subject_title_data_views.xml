<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--题库管理-->
    <!--search-->
    <record id="view_roke_subject_title_data_search" model="ir.ui.view">
        <field name="name">roke.subject.title.data.search</field>
        <field name="model">roke.subject.title.data</field>
        <field name="arch" type="xml">
            <search string="题库管理">
                <field name="number"/>
<!--                <field name="name"/>-->
                 <searchpanel>
                    <field name="project_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subject_title_data_tree" model="ir.ui.view">
        <field name="name">roke.subject.title.data.tree</field>
        <field name="model">roke.subject.title.data</field>
        <field name="arch" type="xml">
            <tree string="题库管理">
                <field name="number"/>
<!--                <field name="name"/>-->
                <field name="forbidden_state"/>
                <field name="project_id"/>
                <field name="description"/>
                <field name="total_marks"/>
                <field name="remark"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subject_title_data_form" model="ir.ui.view">
        <field name="name">roke.subject.title.data.form</field>
        <field name="model">roke.subject.title.data</field>
        <field name="arch" type="xml">
            <form string="题库管理">
                <header>
                    <button name="btn_forbid" string="禁用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','forbidden')]}"/>
                    <button name="btn_normal" string="启用" type="object" class="oe_highlight"
                            attrs="{'invisible':[('forbidden_state','=','normal')]}"/>
                    <field name="forbidden_state" widget="statusbar"/>
                </header>
                    <widget name="web_ribbon" text="禁用" bg_color="bg-danger"
                            attrs="{'invisible': [('forbidden_state', '=', 'normal')]}"/>
                    <div class="oe_title">
                        <label for="number" class="oe_edit_only"/>
                        <h1 class="d-flex">
                            <field name="number"/>
                        </h1>
                    </div>
                    <div>
                        <label for="description"/>
                        <h1>
                            <field name="description" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="project_id" options="{'no_create': True, 'no_open': True}"
                                   domain="[('forbidden_state', '=', 'normal')]" required="1"/>
                            <field name="total_marks"/>
                        </group>
                        <group>
                            <field name="project_type"/>
                            <field name="data_type" readonly="1" force_save="1"
                                   attrs="{'invisible':[('project_type', '!=', 'objective')], 'required':[('project_type', '=', 'objective')]}"/>
                            <field name="teacher_ids" widget="many2many_tags" force_save="1" groups="base.group_system"/>
                        </group>
                    </group>
                    <group>
                        <field name="content" placeholder="此处填写题目答案"
                               attrs="{'invisible':[('project_type', '!=', 'subjectivity')]}"/>
                    </group>
                    <group>
                        <field name="remark" placeholder="此处可以填写试题解析"/>
                    </group>
                    <notebook>
                        <page string="题目设置" attrs="{'invisible':[('project_type', '!=', 'operation')]}">
                            <field name="line_ids">
                                <tree>
                                    <field name="sequence" widget="handle"/>
                                    <field name="name"/>
                                    <field name="menu_fuc" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="url" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="total_marks" sum="总分数"/>
                                    <field name="remark"/>
                                </tree>
                                <form string="题目答案标准">
                                    <sheet>
                                        <group>
                                            <field name="name"/>
                                            <field name="menu_fuc"/>
                                            <field name="url"/>
                                            <field name="total_marks"/>
                                            <field name="remark"/>
                                        </group>
                                        <notebook>
                                            <page string="题目标准答案">
                                                <field name="line_ids">
                                                    <tree editable="bottom">
                                                        <field name="sequence" widget="handle"/>
                                                        <field name="model_id"
                                                               options="{'no_create': True, 'no_open': True}"/>
                                                        <field name="field_id"
                                                               options="{'no_create': True, 'no_open': True}"
                                                               domain="[('model_id','=',model_id)]"/>
                                                        <field name="content"/>
                                                        <field name="mark" sum="总分数"/>
                                                    </tree>
                                                </field>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                        <page string="客观题选项设置" attrs="{'invisible':[('project_type', '!=', 'objective')]}">
                            <field name="objective_line_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name" required="1"/>
                                    <field name="is_correct"
                                           attrs="{'column_invisible':[('parent.data_type', '=', 'gap_filling')]}"/>
                                    <field name="remark"/>
                                </tree>
                            </field>
                        </page>
                        <page string="题目图片列表">
                            <field name="subject_images_ids">
                                <tree editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="img_data" required="1" widget="image" class="oe_avatar"/>
                                    <field name="note"/>
                                </tree>
                                <form string="题目图片列表">
                                    <sheet>
                                        <group>
                                            <group>
                                                <field name="parent_id"/>
                                            </group>
                                            <group>
                                                <field name="img_data" widget="image" class="oe_avatar"/>
                                            </group>
                                        </group>
                                        <group>
                                            <field name="note"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subject_title_data_action" model="ir.actions.act_window">
        <field name="name">题库管理</field>
        <field name="res_model">roke.subject.title.data</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
