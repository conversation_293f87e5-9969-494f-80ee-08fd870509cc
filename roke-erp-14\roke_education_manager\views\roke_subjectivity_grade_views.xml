<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--主观评分-->
    <!--search-->
    <record id="view_roke_subjectivity_grade_search" model="ir.ui.view">
        <field name="name">roke.subjectivity.grade.search</field>
        <field name="model">roke.subjectivity.grade</field>
        <field name="arch" type="xml">
            <search string="主观评分">
                <field name="org_id"/>
                <field name="student_id"/>
                <filter string="待确认" name="wait_confirm" domain="[('state', '=', 'wait_confirm')]"/>
                <filter string="完成" name="done" domain="[('state', '=', 'confirm')]"/>
                <separator/>
                <filter string="已归档" name="is_active" domain="[('active', '=', False)]"/>
                <searchpanel>
                    <field name="main_exam_id" icon="fa-users" enable_counters="1" expand="1"/>
                </searchpanel>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_subjectivity_grade_tree" model="ir.ui.view">
        <field name="name">roke.subjectivity.grade.tree</field>
        <field name="model">roke.subjectivity.grade</field>
        <field name="arch" type="xml">
            <tree string="主观评分" create="0" edit="0" delete="0">
                <field name="org_id"/>
                <field name="exam_id"/>
                <field name="student_id"/>
                <field name="pattern_type"/>
                <field name="checkbox_score_type"/>
                <field name="total_marks"/>
                <field name="score"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_subjectivity_grade_form" model="ir.ui.view">
        <field name="name">roke.subjectivity.grade.form</field>
        <field name="model">roke.subjectivity.grade</field>
        <field name="arch" type="xml">
            <form string="主观评分" create="0" delete="0">
                <header>
                    <button name="confirm" string="确认" type="object" class="oe_highlight"
                            attrs="{'invisible':[('state','=','confirm')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                    <group>
                        <group>
                            <field name="org_id" options="{'no_create': True, 'no_open': True}" readonly="1" force_save="1"/>
                            <field name="student_id" options="{'no_create': True, 'no_open': True}" readonly="1" force_save="1"/>
                            <field name="pattern_type" readonly="1" force_save="1"/>
                            <field name="checkbox_score_type"/>
                        </group>
                        <group>
                            <field name="exam_id" options="{'no_create': True, 'no_open': True}" readonly="1" force_save="1"/>
                            <field name="total_marks" readonly="1" force_save="1"/>
                            <field name="score" readonly="1" force_save="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="主观评分明细">
                            <field name="line_ids" attrs="{'readonly':[('state','=','confirm')]}">
                                <tree editable="bottom" create="0" delete="0">
                                    <field name="course_id" readonly="1" force_save="1" required="1"/>
<!--                                    <field name="project_id" readonly="1" force_save="1"/>-->
<!--                                    <field name="title_data_id" readonly="1" force_save="1"/>-->
                                    <field name="content" readonly="1" force_save="1"/>
                                    <field name="true_content" readonly="1" force_save="1"/>
                                    <field name="answer" readonly="1" force_save="1"/>
                                    <field name="proportion_mark" readonly="1" force_save="1" sum="总分数"/>
                                    <field name="mark" force_save="1" sum="总得分"/>
<!--                                    <field name="state" readonly="1" force_save="1"/>-->
                                </tree>
                            </field>
                        </page>
                    </notebook>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_subjectivity_grade_action" model="ir.actions.act_window">
        <field name="name">主观评分</field>
        <field name="res_model">roke.subjectivity.grade</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{'search_default_wait_confirm': 1}</field>
    </record>

</odoo>
