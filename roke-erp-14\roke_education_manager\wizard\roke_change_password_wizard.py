# -*- coding: utf-8 -*-
"""
Description:
    重置、修改密码
"""
import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeChangePasswordWizard(models.TransientModel):
    _name = "roke.change.password.wizard"
    _description = '修改密码'

    user_id = fields.Many2one('res.users', string='用户')
    user_type = fields.Selection([('student', '学生'), ('teacher', '老师')], string='类型')
    new_password = fields.Char(string='新密码')

    def confirm(self):
        if self.user_type == 'teacher':
            if self.env.user.id != self.user_id.id:
                raise ValidationError('只允许修改当前登录用户自己的密码；\n 当前要修改的用户和登录人不符，请刷新界面再进行修改')
        # 修改密码
        self.sudo().user_id.password = self.new_password
