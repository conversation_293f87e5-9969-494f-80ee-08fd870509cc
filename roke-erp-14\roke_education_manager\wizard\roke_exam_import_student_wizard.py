# -*- coding: utf-8 -*-
"""
Description:
    导入学生
"""
import base64
import datetime
from io import BytesIO

import xlrd
import xlwt
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExamImportStudentWizard(models.TransientModel):
    _name = "roke.exam.import.student.wizard"
    _description = '导入学生'

    exam_id = fields.Many2one('roke.base.exam', string='考试')
    import_mode = fields.Selection([('org', '按组织'), ('excel', 'Excel')], string='导入方式', default='org')
    org_id = fields.Many2one('roke.base.org', string='组织')
    file = fields.Binary('文件')
    employee_ids = fields.Many2many('roke.employee', string="学生")

    @api.onchange('org_id')
    def _onchange_org_id(self):
        if self.org_id:
            # 查询该组织下所有学生
            employee_ids = self.env['roke.employee'].search(
                [('student_type', '=', 'student'), ('org_id', '=', self.org_id.id)])
            self.write({
                'employee_ids': [(6, 0, employee_ids.ids)]
            })

    @api.onchange('import_mode')
    def _onchange_import_mode(self):
        self.org_id = False
        self.employee_ids = False

    def analysis_excel(self):
        print('analysis_excel')
        if not self.file:
            raise ValidationError('请先上传excel表格')
        # 取excel中的学生
        student_list = []
        student_table = self.env['roke.employee']
        org_table = self.env['roke.base.org']
        book = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))
        sh1 = book.sheet_by_name('考生信息')
        student_row_count = sh1.nrows

        for i in range(student_row_count - 1):
            if not sh1.row(i + 1)[1].value or not sh1.row(i + 1)[2].value or not sh1.row(i + 1)[6].value:
                raise ValidationError('姓名、账号、性别为必填信息，文件中第【%s】行未填写，请检查' % str(i + 2))
            domain = [('name', '=', sh1.row(i + 1)[1].value), ('job_number', '=', sh1.row(i + 1)[2].value),
                      ('gender', '=', sh1.row(i + 1)[5].value), ('student_type', '=', 'student')]
            org_obj = False
            # 编号
            if sh1.row(i + 1)[0].value:
                domain.append(('code', '=', sh1.row(i + 1)[0].value))
            # 电话
            if sh1.row(i + 1)[4].value:
                domain.append(('phone', '=', sh1.row(i + 1)[4].value))
            # 所属组织
            if sh1.row(i + 1)[7].value:
                # 查询组织
                org_obj = org_table.search([('class_name', '=', sh1.row(i + 1)[7].value)])
                if not org_obj:
                    raise ValidationError('第【%s】行【%s】组织在系统中不存在，请检查' % (str(i + 2), sh1.row(i + 1)[7].value))
                domain.append(('org_id', '=', org_obj.id))
            # 查询学生
            student_id = student_table.search(domain)
            if not student_id:
                # 校验账号、编号、手机号是否已存在
                code_obj = student_table.search([('code', '=', sh1.row(i + 1)[0].value)])
                if code_obj:
                    raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生编号【%s】在系统中已存在' % ((i + 2),  sh1.row(i + 1)[0].value))
                job_number_obj = student_table.search([('job_number', '=', sh1.row(i + 1)[2].value)])
                if job_number_obj:
                    raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生账号【%s】在系统中已存在' % (
                        (i + 2), sh1.row(i + 1)[2].value))
                phone_obj = student_table.search([('phone', '=', sh1.row(i + 1)[4].value)])
                if phone_obj:
                    raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生手机号【%s】在系统中已存在' % (
                        (i + 2), sh1.row(i + 1)[4].value))
                idcard_obj = student_table.search([('id_number', '=', sh1.row(i + 1)[5].value)])
                if idcard_obj:
                    raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生身份证号【%s】在系统中已存在' % (
                        (i + 2), sh1.row(i + 1)[5].value))
                # 创建
                student_id = student_table.create({
                    'code': sh1.row(i + 1)[0].value if sh1.row(i + 1)[0].value else False,
                    'name': sh1.row(i + 1)[1].value,
                    'job_number': sh1.row(i + 1)[2].value,
                    'phone': sh1.row(i + 1)[4].value if sh1.row(i + 1)[4].value else False,
                    'gender': sh1.row(i + 1)[5].value,
                    'org_id': org_obj.id if org_obj else False,
                    'note': sh1.row(i + 1)[8].value,
                })
                if sh1.row(i + 1)[3].value:
                    student_id.user_id.sudo().write({'password': sh1.row(i + 1)[3].value})
            student_list.append(student_id.id)
        self.employee_ids = [(6, 0, student_list)]
        return {
            'name': '导入学生',
            'context': self.env.context,
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'roke.exam.import.student.wizard',
            'res_id': self.id,
            'view_id': False,
            'type': 'ir.actions.act_window',
            'target': 'new',
        }

    def confirm(self):
        if not self.employee_ids:
            raise ValidationError('请先选择学生')
        student_exam_list = []
        exist_student_list = []
        for employee in self.employee_ids:
            # 判断该学生是否已在本场考试存在
            exist_exam_line = self.exam_id.exam_line_ids.filtered(lambda line: line.employee_id.id == employee.id)
            if exist_exam_line:
                exist_student_list.append(employee.name)
            student_exam_list.append({
                'parent_id': self.exam_id.id,
                'pattern_type': self.exam_id.pattern_type,
                'dispatch_type': self.exam_id.dispatch_type,
                'rule_id': self.exam_id.rule_id.id,
                'org_id': self.org_id.id,
                'test_paper_id': self.exam_id.test_paper_id.id,
                'employee_id': employee.id,
                'course_id': self.exam_id.course_id.id,
                'start_time': self.exam_id.start_time,
                'time_length': self.exam_id.time_length,
                'end_time': self.exam_id.end_time,
            })
        if exist_student_list:
            raise ValidationError('下列学生在当前考试中已存在，请确认\n【%s】' % '、'.join(exist_student_list))
        self.env['roke.subject.student.exam'].create(student_exam_list)
