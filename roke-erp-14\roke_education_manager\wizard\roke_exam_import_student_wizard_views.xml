<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="roke_exam_import_student_wizard_form" model="ir.ui.view">
        <field name="name">roke.exam.import.student.wizard.form</field>
        <field name="model">roke.exam.import.student.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
<!--                    <div name="download_excel" attrs="{'invisible': [('import_mode','!=','excel')]}">-->
<!--                        <a href="/roke_education_manager/static/file/导入学生模板.xlsx">下载模板</a>-->
<!--                    </div>-->
                    <group>
                        <group>
                            <field name="import_mode" required="1"/>
                        </group>
                        <group>
                            <field name="exam_id" invisible="1"/>
                            <field name="org_id" options="{'no_create': True, 'no_open': True}"
                                   context="{'search_default_groupby_parent_id':1}"
                                   attrs="{'invisible': [('import_mode','!=','org')], 'required': [('import_mode','=','org')]}"/>
                            <label for="file" attrs="{'invisible': [('import_mode','!=','excel')]}"/>
                            <div name="file" class="o_row" attrs="{'invisible': [('import_mode','!=','excel')]}">
                                <field name="file"/>
                                <span>
                                    <button name='analysis_excel' string='读取excel' type='object' class='oe_highlight'
                                            attrs="{'invisible': [('file','=',False)]}"/>
                                </span>
                            </div>
<!--                            <field name="file" options="{'no_create': True, 'no_open': True}"-->
<!--                                   attrs="{'invisible': [('import_mode','!=','excel')], 'required': [('import_mode','=','excel')]}"/>-->
                        </group>
                    </group>
                    <group>
                        <field name="employee_ids" widget="many2many_tags" domain="[('org_id','=',org_id)]"
                               options="{'no_create': True, 'no_open': True}"/>
                    </group>
                </sheet>

                <footer>
                    <button name='confirm' string='确定' type='object' class='oe_highlight'/>
                    <button string="取消" class="btn-default" special="cancel"/>
                    <a href="/roke_education_manager/static/file/导入学生模板.xlsx">
                        <button string="下载模板" class="oe_highlight"
                                attrs="{'invisible': [('import_mode','!=','excel')]}"/>
                    </a>
                </footer>
            </form>
        </field>
    </record>
</odoo>