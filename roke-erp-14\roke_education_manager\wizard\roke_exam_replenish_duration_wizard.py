# -*- coding: utf-8 -*-
"""
Description:
    考试补时
"""
import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExamReplenishDurationWizard(models.TransientModel):
    _name = "roke.exam.replenish.duration.wizard"
    _description = '考试补时'

    exam_record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    duration = fields.Integer(string='补时时长：分钟')
    remark = fields.Text(string='补时原因')

    def confirm(self):
        if self.duration <= 0:
            raise ValidationError('补时时长必须大于0')
        remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
            "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '补时，补时原因：' + self.remark
        # 判断当前考试记录是否有备注
        if self.exam_record_id.remark:
            new_remark = self.exam_record_id.remark + '\n' + remark
        else:
            new_remark = remark
        self.exam_record_id.write({
            'duration': self.exam_record_id.duration + self.duration,
            'end_time': self.exam_record_id.end_time + datetime.timedelta(minutes=self.duration),
            'remark': new_remark
        })
