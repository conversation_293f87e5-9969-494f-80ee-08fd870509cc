# -*- coding: utf-8 -*-
"""
Description:
    考试完成重置为进行中
"""
import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExamResetStateWizard(models.TransientModel):
    _name = "roke.exam.reset.state.wizard"
    _description = '考试完成重置为进行中'

    exam_id = fields.Many2one('roke.subject.student.exam', string='考试')
    remark = fields.Text(string='重置原因')

    def confirm(self):
        if not self.remark:
            raise ValidationError('重置原因不可为空，请填写重置原因')
        if self.exam_id.state != 'done':
            raise ValidationError('当前考试不是完成状态，不需要重置为进行中，请刷新后重试')
        else:
            # 主观评分和考试记录需要归档并重新生成
            # 主观评分归档
            subjectivity_grade_obj = self.env['roke.subjectivity.grade'].search([('exam_id', '=', self.exam_id.id)])
            subjectivity_grade_obj.active = False
            # 考试记录归档
            exam_record_id = self.env['roke.subject.examination.record'].search([('exam_id', '=', self.exam_id.id)])
            exam_record_id.active = False
            # 重新生成考试记录及主观评分
            # 查找当前登录人的考试 并 创建考试记录及考试明细、主观评分
            employee_id = self.exam_id.employee_id
            # hotel_manager_group = self.env.ref('roke_hotel_manager.group_hotel_manager', raise_if_not_found=False)
            # user_group_list = employee_id.user_id.groups_id.ids
            # if hotel_manager_group.id not in user_group_list:
            #     user_group_list.append(hotel_manager_group.id)
            #     employee_id.user_id.groups_id = [(6, 0, user_group_list)]
            # 判断是否存在考试
            exam_record = self.env['roke.subject.examination.record'].create({
                'main_exam_id': self.exam_id.parent_id.id,
                'exam_id': self.exam_id.id,
                'pattern_type': self.exam_id.pattern_type,
                'org_id': employee_id.org_id.id,
                'student_id': employee_id.id,
                'start_time': self.exam_id.start_time,
                'end_time': self.exam_id.end_time,
                'duration': self.exam_id.time_length,
                'total_marks': self.exam_id.total_marks,
            })
            subjectivity_record = False
            # 判断是否存在主观题目
            if self.exam_id.line_ids.filtered(
                    lambda line_item: line_item.title_date_id.project_id.project_type == 'subjectivity'):
                subjectivity_record = self.env['roke.subjectivity.grade'].create({
                    'main_exam_id': self.exam_id.parent_id.id,
                    'exam_id': self.exam_id.id,
                    'exam_record_id': exam_record.id,
                    'org_id': employee_id.org_id.id,
                    'student_id': employee_id.id,
                    'pattern_type': self.exam_id.pattern_type,
                })
            for line in self.exam_id.line_ids:
                # 主观评分
                if line.project_id.project_type == 'subjectivity':
                    subjectivity_record_line = self.env['roke.subjectivity.record.line'].create({
                        'record_id': exam_record.id,
                        'course_id': line.title_date_id.project_id.course_id.id,
                        'project_id': line.title_date_id.project_id.id,
                        'title_data_id': line.title_date_id.id,
                        'content': line.title_date_id.description,
                        'true_content': line.title_date_id.content,
                        'proportion_mark': line.title_date_id.total_marks,
                    })
                    self.env['roke.subjectivity.grade.line'].create({
                        'parent_id': subjectivity_record.id if subjectivity_record else False,
                        'record_line_id': subjectivity_record_line.id,
                        'course_id': line.title_date_id.project_id.course_id.id,
                        'project_id': line.title_date_id.project_id.id,
                        'title_data_id': line.title_date_id.id,
                        'content': line.title_date_id.description,
                        'true_content': line.title_date_id.content,
                        'proportion_mark': line.title_date_id.total_marks,
                    })
                elif line.project_id.project_type == 'operation':
                    # 实操类题目
                    for title_line in line.title_date_id.line_ids:
                        for standard_line in title_line.line_ids:
                            self.env['roke.subject.examination.record.line'].create({
                                'record_id': exam_record.id,
                                'course_id': line.title_date_id.project_id.course_id.id,
                                'project_id': line.title_date_id.project_id.id,
                                'title_data_id': line.title_date_id.id,
                                'title_data_line_id': title_line.id,
                                'standard_line_id': standard_line.id,
                                'model_id': standard_line.model_id.id,
                                'field_id': standard_line.field_id.id,
                                'content': standard_line.content,
                                'proportion_mark': standard_line.mark,
                            })
                else:
                    if line.project_id.data_type != 'gap_filling':
                        # 取正确答案
                        true_answer = line.title_date_id.objective_line_ids.filtered(lambda option: option.is_correct)
                    else:
                        # 取正确答案
                        true_answer = line.title_date_id.objective_line_ids
                    true_answer_name = '、'.join([option.name for option in true_answer])
                    self.env['roke.objective.record.line'].create({
                        'record_id': exam_record.id,
                        'course_id': line.title_date_id.project_id.course_id.id,
                        'project_id': line.title_date_id.project_id.id,
                        'title_data_id': line.title_date_id.id,
                        'true_answer': true_answer_name,
                    })
            remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
                "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '重置为进行中，重置原因：' + self.remark
            if self.exam_id.remark:
                new_remark = self.exam_id.remark + '\n' + remark
            else:
                new_remark = remark
            self.exam_id.write({
                'state': 'exam_taking',
                'remark': new_remark
            })
