# -*- coding: utf-8 -*-
"""
Description:
    拆分场次
"""

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExamSplitRoundWizard(models.TransientModel):
    _name = "roke.exam.split.round.wizard"
    _description = '拆分场次'

    exam_id = fields.Many2one('roke.base.exam', string='主考试')
    has_student = fields.Boolean('是否已导入学生')
    round_count = fields.Integer('拆分场次数', default=1)

    @api.onchange('round_count')
    def _onchange_round_count(self):
        if self.round_count <= 0:
            raise ValidationError('拆分场次数需大于0')
        # 判断场次数不能大于已导入学生数
        if self.has_student and self.round_count:
            if self.round_count > len(self.exam_id.exam_line_ids):
                raise ValidationError(
                    '拆分场次数【%s】大于当前考试人数【%s】，请确认' % (self.round_count, len(self.exam_id.exam_line_ids)))

    def confirm(self):
        if not self.has_student:  # 没有学生、直接拆分场次
            # 取当前主考试下所有考试场次最大值
            round_list = self.exam_id.exam_ids.mapped('round_count')
            self.exam_id.is_main_exam = True
            if not round_list:  # 第一次拆分
                max_round = 1
            else:
                max_round = max(round_list) + 1
            for i in range(self.round_count):
                self.env['roke.base.exam'].create({
                    'name': self.exam_id.name + '-' + str(i + max_round),
                    'number': self.env['ir.sequence'].next_by_code('roke.base.exam'),
                    'dispatch_type': self.exam_id.dispatch_type,
                    'pattern_type': self.exam_id.pattern_type,
                    'rule_id': self.exam_id.rule_id.id,
                    'test_paper_id': self.exam_id.test_paper_id.id,
                    'course_id': self.exam_id.course_id.id,
                    'is_dispatch': self.exam_id.is_dispatch,
                    'start_time': self.exam_id.start_time,
                    'end_time': self.exam_id.end_time,
                    'time_length': self.exam_id.time_length,
                    'remark': self.exam_id.remark,
                    'parent_id': self.exam_id.id,
                    'round_count': i + max_round,
                    'checkbox_score_type': self.exam_id.checkbox_score_type
                })
        else:  # 有学生，需要根据场次平均分配学生
            self.exam_id.is_main_exam = True
            exam_line_list = [exam_line for exam_line in self.exam_id.exam_line_ids]
            round_student_count = len(exam_line_list) // self.round_count
            for i in range(self.round_count):
                base_exam_id = self.env['roke.base.exam'].create({
                    'name': self.exam_id.name + '-' + str(i + 1),
                    'number': self.env['ir.sequence'].next_by_code('roke.base.exam'),
                    'dispatch_type': self.exam_id.dispatch_type,
                    'pattern_type': self.exam_id.pattern_type,
                    'rule_id': self.exam_id.rule_id.id,
                    'test_paper_id': self.exam_id.test_paper_id.id,
                    'course_id': self.exam_id.course_id.id,
                    'is_dispatch': self.exam_id.is_dispatch,
                    'start_time': self.exam_id.start_time,
                    'end_time': self.exam_id.end_time,
                    'time_length': self.exam_id.time_length,
                    'remark': self.exam_id.remark,
                    'parent_id': self.exam_id.id,
                    'round_count': i + 1,
                    'checkbox_score_type': self.exam_id.checkbox_score_type
                })
                if i < self.round_count - 1:
                    round_exam = exam_line_list[i * round_student_count:(i + 1) * round_student_count]
                else:
                    round_exam = exam_line_list[i * round_student_count:]
                for item in round_exam:
                    item.write({
                        'parent_id': base_exam_id.id
                    })
