<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="roke_exam_split_round_wizard_form" model="ir.ui.view">
        <field name="name">roke.exam.split.round.wizard.form</field>
        <field name="model">roke.exam.split.round.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <span>
                        根据录入的场次数自动生成对应场次的考试<br/>
                        已导入学生的考试在拆分场次时会将学生平均分配到拆分出来的各个场次
                    </span>
                    <group>
                        <group>
                            <field name="exam_id" invisible="1"/>
                            <field name="has_student" invisible="1"/>
                            <field name="round_count" required="1"/>
                        </group>
                    </group>
                </sheet>

                <footer>
                    <button name='confirm' string='确定' type='object' class='oe_highlight'/>
                    <button string="取消" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>