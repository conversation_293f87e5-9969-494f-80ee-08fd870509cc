# -*- coding: utf-8 -*-
"""
Description:
    考试暂停、延时
"""
import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExamSuspendWizard(models.TransientModel):
    _name = "roke.exam.suspend.wizard"
    _description = '考试暂停、延时'

    exam_ids = fields.Many2many('roke.subject.student.exam', string='考试')
    suspend_type = fields.Selection([('suspend', '暂停'), ('delayed', '延时'), ('over_exam', '强制交卷')],
                                    string='类型')
    time_length = fields.Integer(string='延时时长：分钟', default=1)
    reason = fields.Text(string='原因')

    @api.onchange('time_length')
    def _onchange_time_length(self):
        if self.time_length <= 0:
            raise ValidationError('延时时长需大于0')

    def confirm(self):
        status_dict = {
            'suspend': '暂停',
            'delayed': '延时',
            'over_exam': '强制交卷',
        }
        # 需要处理的考试记录
        if self.suspend_type == 'suspend':  # 暂停
            # 将考试及考试下不是已交卷状态的学生考试明细状态置为暂停状态
            remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
                "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '考试暂停，暂停原因：' + self.reason
            for exam_line in self.exam_ids:
                if exam_line.remark:
                    new_remark = exam_line.remark + '\n' + remark
                else:
                    new_remark = remark
                exam_line.write({
                    'state': 'exam_suspend',
                    'suspend_time': datetime.datetime.now(),
                    'remark': new_remark
                })
        elif self.suspend_type == 'over_exam':  # 强制交卷
            # 将考试及考试下不是已交卷状态的学生考试明细状态置为强制交卷状态
            remark = (datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
                "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '考试强制交卷，原因：' + self.reason
            for exam_line in self.exam_ids:
                # 重置考生密码
                exam_line.employee_id.user_id.sudo().write({'password': exam_line.employee_id.job_number})
                # 查找对应考试记录
                exam_record = self.env['roke.subject.examination.record'].search([('exam_id', '=', exam_line.id)])
                if exam_record:
                    if exam_record.remark:
                        exam_record.write({
                            'state': 'done',
                            'end_time': datetime.datetime.now(),
                            'remark': exam_record.remark + '\n' + remark
                        })
                    else:
                        exam_record.write({
                            'state': 'done',
                            'end_time': datetime.datetime.now(),
                            'remark': remark
                        })
                if exam_line.remark:
                    new_remark = exam_line.remark + '\n' + remark
                else:
                    new_remark = remark
                exam_line_dict = {
                    'is_compel_over_exam': True,
                    'remark': new_remark,
                    'real_end_time': datetime.datetime.now(),
                    'real_time_length': (datetime.datetime.now() - exam_line.real_start_time).seconds / 60,
                }
                # 判断考试是否有未确认的主观评分
                grade_record = self.env['roke.subjectivity.grade'].search(
                    [('exam_id', '=', exam_line.id), ('state', '=', 'wait_confirm')])
                if grade_record:
                    exam_line_dict.update({'state': 'wait_subjectivity'})
                else:
                    exam_line_dict.update({'state': 'done'})
                exam_line.write(exam_line_dict)
        else:  # 延时
            remark = ((datetime.datetime.now() + datetime.timedelta(hours=8)).strftime(
                "%Y-%m-%d %H:%M:%S") + '【' + self.env.user.name + '】' + '考试延时【{time_length}】分钟，延时原因：' + self.reason).format(
                time_length=self.time_length)
            for exam_line in self.exam_ids:
                # 查找对应考试记录
                exam_record = self.env['roke.subject.examination.record'].search([('exam_id', '=', exam_line.id)])
                if exam_record:
                    if exam_record.remark:
                        exam_record.write({
                            'duration': exam_record.duration + self.time_length,
                            'end_time': exam_record.end_time + datetime.timedelta(minutes=self.time_length),
                            'remark': exam_record.remark + '\n' + remark
                        })
                    else:
                        exam_record.write({
                            'duration': exam_record.duration + self.time_length,
                            'end_time': exam_record.end_time + datetime.timedelta(minutes=self.time_length),
                            'remark': remark
                        })
                if exam_line.remark:
                    new_remark = exam_line.remark + '\n' + remark
                else:
                    new_remark = remark
                exam_line.write({
                    'delayed_trait': True,
                    'delayed_count': self.time_length,
                    'delayed_length': exam_line.delayed_length + self.time_length,
                    'time_length': exam_line.time_length + self.time_length,
                    'end_time': exam_line.end_time + datetime.timedelta(minutes=self.time_length),
                    'remark': new_remark
                })
        return {
            'name': '本次%s的考试' % status_dict[self.suspend_type],
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', '=', self.exam_ids.ids)],
            'context': {'exam_ongoing': True},
            'res_model': 'roke.subject.student.exam'
        }
