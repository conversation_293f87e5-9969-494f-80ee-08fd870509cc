# -*- coding: utf-8 -*-
"""
Description:
    excel导入学生
"""
import base64
import datetime
from io import BytesIO

import xlrd
import xlwt
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExcelImportStudentWizard(models.TransientModel):
    _name = "roke.excel.import.student.wizard"
    _description = '导入学生'

    file = fields.Binary('文件')

    def confirm(self):
        if not self.file:
            raise ValidationError('请先上传excel表格')
        # 取excel中的学生
        student_list = []
        student_table = self.env['roke.employee']
        org_table = self.env['roke.base.org']
        book = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))
        sh1 = book.sheet_by_name('考生信息')
        student_row_count = sh1.nrows

        for i in range(student_row_count - 1):
            if not sh1.row(i + 1)[1].value or not sh1.row(i + 1)[2].value or not sh1.row(i + 1)[6].value:
                raise ValidationError('姓名、账号、性别为必填信息，文件中第【%s】行未填写，请检查' % str(i + 2))
            org_obj = False
            # 所属组织
            if sh1.row(i + 1)[7].value:
                # 查询组织
                org_obj = org_table.search([('class_name', '=', sh1.row(i + 1)[7].value)])
                if not org_obj:
                    raise ValidationError(
                        '第【%s】行【%s】组织在系统中不存在，请检查' % (str(i + 2), sh1.row(i + 1)[7].value))
            # 校验账号、编号、手机号是否已存在
            code_obj = student_table.search([('code', '=', sh1.row(i + 1)[0].value)])
            if code_obj:
                raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生编号【%s】在系统中已存在' % (
                    (i + 2), sh1.row(i + 1)[0].value))
            job_number_obj = student_table.search([('job_number', '=', sh1.row(i + 1)[2].value)])
            if job_number_obj:
                raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生账号【%s】在系统中已存在' % (
                    (i + 2), sh1.row(i + 1)[2].value))
            phone_obj = student_table.search([('phone', '=', sh1.row(i + 1)[4].value)])
            if phone_obj:
                raise ValidationError('excel中第【%s】行未匹配到对应学生，创建时发现该学生手机号【%s】在系统中已存在' % (
                    (i + 2), sh1.row(i + 1)[4].value))
            idcard_obj = student_table.search([('id_number', '=', sh1.row(i + 1)[5].value)])
            if idcard_obj:
                raise ValidationError('excel中第【%s】行学生身份证号【%s】在系统中已存在' % ((i + 2), sh1.row(i + 1)[5].value))
            # 创建
            student_id = student_table.create({
                'code': sh1.row(i + 1)[0].value if sh1.row(i + 1)[0].value else False,
                'name': sh1.row(i + 1)[1].value,
                'job_number': sh1.row(i + 1)[2].value,
                'phone': sh1.row(i + 1)[4].value if sh1.row(i + 1)[4].value else False,
                'gender': sh1.row(i + 1)[5].value,
                'org_id': org_obj.id if org_obj else False,
                'note': sh1.row(i + 1)[8].value,
            })
            if sh1.row(i + 1)[3].value:
                student_id.user_id.sudo().write({'password': sh1.row(i + 1)[3].value})
            student_list.append(student_id.id)
        return {
            'name': '本次导入的学生',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', student_list)],
            'res_model': 'roke.employee'
        }