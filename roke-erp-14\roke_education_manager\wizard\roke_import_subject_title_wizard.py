#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023-05-20 20:58
# <AUTHOR> 王赞
# @Site    : 
# @File    : roke_exam_import_student_wizard1.py.py
# @Software: PyCharm

import base64
import datetime
from io import BytesIO

import xlrd
import xlwt
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeExcelImportSubjectTitleWizard(models.TransientModel):
    _name = "roke.excel.import.subject.title.wizard"
    _description = '导入题库'

    file = fields.Binary('文件')

    def confirm(self):
        if not self.file:
            raise ValidationError('请先上传excel表格')
        # 取excel中的学生
        subject_title_ids = []
        ProjectObj = self.env['roke.subject.project']
        SubjectTitleObj = self.env['roke.subject.title.data']
        SubjectObjectiveTitleObj = self.env['roke.subject.objective.title.data.line'] # parent_id
        book = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))

        # 主观题
        zgt_sheet = book.sheet_by_name('主观题')
        zgt_row_count = zgt_sheet.nrows
        for zgt_rx in range(1, zgt_row_count):
            row = zgt_sheet.row(zgt_rx) # 行
            if not row[0].value or not row[1].value or not row[2].value:
                raise ValidationError("主观题中编号、题目信息、对应项目不能为空，表格中第【%s】未填写（不包含标题行）" % zgt_rx)
            project = ProjectObj.search([
                ("name", "=", row[1].value),
                ("project_type", "=", "subjectivity"),
            ], limit=1)
            if not project:
                raise ValidationError("主观题中项目【%s】不存在，表格中第【%s】未填写（不包含标题行）" % (row[1].value, zgt_rx))
            subject_title = SubjectTitleObj.create({
                "number": row[0].value,
                "project_id": project.id,
                "description": row[2].value,
                "content": row[4].value,
                "remark": row[5].value
            })
            subject_title_ids.append(subject_title.id)

        # 单选题
        daxt_sheet = book.sheet_by_name('单选题')
        daxt_row_count = daxt_sheet.nrows
        subject_title = None
        for daxt_rx in range(1, daxt_row_count):
            row = daxt_sheet.row(daxt_rx) # 行
            if (not row[0].value or not row[1].value or not row[3].value) and \
                    (row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value):
                raise ValidationError("单选题中编号、题目信息、对应项目不能为空，表格中第【%s】未填写（不包含标题行）" % daxt_rx)
            if row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value: # 主表有数据
                project = ProjectObj.search([
                    ("name", "=", row[1].value),
                    ("project_type", "=", "objective"),
                ], limit=1)
                if not project:
                    raise ValidationError("单选题中项目【%s】不存在，表格中第【%s】未填写（不包含标题行）" % (row[1].value, daxt_rx))
                subject_title = SubjectTitleObj.create({
                    "number": row[0].value,
                    "project_id": project.id,
                    "description": row[3].value,
                    "remark": row[5].value
                })
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })
                subject_title_ids.append(subject_title.id)
            else:
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })



        # 多选题
        duxt_sheet = book.sheet_by_name('多选题')
        duxt_row_count = duxt_sheet.nrows
        for duxt_rx in range(1, duxt_row_count):
            row = duxt_sheet.row(duxt_rx) # 行
            if (not row[0].value or not row[1].value or not row[3].value) and \
                    (row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value):
                raise ValidationError("多选题中编号、题目信息、对应项目不能为空，表格中第【%s】未填写（不包含标题行）" % duxt_rx)
            if row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value: # 主表有数据
                project = ProjectObj.search([
                    ("name", "=", row[1].value),
                    ("project_type", "=", "objective"),
                ], limit=1)
                if not project:
                    raise ValidationError("多选题中项目【%s】不存在，表格中第【%s】未填写（不包含标题行）" % (row[1].value, duxt_rx))
                subject_title = SubjectTitleObj.create({
                    "number": row[0].value,
                    "project_id": project.id,
                    "description": row[3].value,
                    "remark": row[5].value
                })
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })
                subject_title_ids.append(subject_title.id)
            else:
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })

        # 判断题
        pdt_sheet = book.sheet_by_name('判断题')
        pdt_row_count = pdt_sheet.nrows
        for pdt_rx in range(1, pdt_row_count):
            row = pdt_sheet.row(pdt_rx) # 行
            if (not row[0].value or not row[1].value or not row[3].value) and \
                    (row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value):
                raise ValidationError("判断题中编号、题目信息、对应项目不能为空，表格中第【%s】未填写（不包含标题行）" % pdt_rx)
            if row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value: # 主表有数据
                project = ProjectObj.search([
                    ("name", "=", row[1].value),
                    ("project_type", "=", "objective"),
                ], limit=1)
                if not project:
                    raise ValidationError("判断题中项目【%s】不存在，表格中第【%s】未填写（不包含标题行）" % (row[1].value, pdt_rx))
                subject_title = SubjectTitleObj.create({
                    "number": row[0].value,
                    "project_id": project.id,
                    "description": row[3].value,
                    "remark": row[5].value
                })
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })
                subject_title_ids.append(subject_title.id)
            else:
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })

        # 填空题
        tkt_sheet = book.sheet_by_name('填空题')
        tkt_row_count = tkt_sheet.nrows
        for tkt_rx in range(1, tkt_row_count):
            row = tkt_sheet.row(tkt_rx) # 行
            if (not row[0].value or not row[1].value or not row[3].value) and \
                    (row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value):
                raise ValidationError("填空题中编号、题目信息、对应项目不能为空，表格中第【%s】未填写（不包含标题行）" % tkt_rx)
            if row[0].value or row[1].value or row[2].value or row[3].value or row[4].value or row[5].value: # 主表有数据
                project = ProjectObj.search([
                    ("name", "=", row[1].value),
                    ("project_type", "=", "objective"),
                ], limit=1)
                if not project:
                    raise ValidationError("填空题中项目【%s】不存在，表格中第【%s】未填写（不包含标题行）" % (row[1].value, tkt_rx))
                subject_title = SubjectTitleObj.create({
                    "number": row[0].value,
                    "project_id": project.id,
                    "description": row[3].value,
                    "remark": row[5].value
                })
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })
                subject_title_ids.append(subject_title.id)
            else:
                SubjectObjectiveTitleObj.create({
                    "parent_id": subject_title.id,
                    "sequence": int(row[6].value),
                    "name": row[7].value,
                    "is_correct": True if row[8].value == "是" else False,
                })


        return {
            'name': '本次导入的题库',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', subject_title_ids)],
            'res_model': 'roke.subject.title.data'
        }