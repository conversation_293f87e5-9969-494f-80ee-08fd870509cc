# -*- coding: utf-8 -*-
"""
Description:
    手动评分
"""
import random

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectManualMarkWizard(models.TransientModel):
    _name = "roke.subject.manual.mark.wizard"
    _description = '手动评分'

    exam_id = fields.Many2one('roke.subject.student.exam', string='考试')
    exam_record_id = fields.Many2one('roke.subject.examination.record', string='考试记录')
    org_id = fields.Many2one('roke.base.org', string='所属组织', related='exam_id.org_id')
    course_id = fields.Many2one('roke.subject.course', string="科目", related='exam_id.course_id')
    total_marks = fields.Float(string='总分数', related='exam_id.total_marks')
    line_ids = fields.One2many('roke.subject.title.data.line.wizard', 'parent_id', steing='考试内容')

    def confirm(self):
        exam_record = self.exam_record_id
        for line in self.line_ids:
            self.env['roke.subject.examination.record.line'].browse(line.origin_id.id).write({
                'mark': line.score,
                'state': 'confirm'
            })
        if all(line.state == 'confirm' for line in exam_record.line_ids):
            exam_record.state = 'done'
            exam_record.exam_id.person_ids.filtered(
                lambda person: person.employee_id.id == exam_record.student_id.id).write({'state': 'done'})


class RokeSubjectTtitleDataLineWizard(models.TransientModel):
    _name = "roke.subject.title.data.line.wizard"
    _description = '手动评分明细'

    parent_id = fields.Many2one('roke.subject.manual.mark.wizard', string='评分')
    origin_id = fields.Many2one('roke.subject.examination.record.line', string='考试记录明细ID')
    title_data_id = fields.Many2one('roke.subject.title.data', string='题库', related='origin_id.title_data_id')
    title_data_line_id = fields.Many2one('roke.subject.title.data.line', string='题目',
                                         related='origin_id.title_data_line_id')
    standard_line_id = fields.Many2one('roke.subject.title.data.standard.line', string='题目',
                                       related='origin_id.standard_line_id')
    course_id = fields.Many2one('roke.subject.course', string='科目', related='origin_id.course_id')
    project_id = fields.Many2one('roke.subject.project', string="项目类别", related='origin_id.project_id')
    model_id = fields.Many2one('ir.model', string="当前项对应模型", related='origin_id.model_id')
    field_id = fields.Many2one('ir.model.fields', string="当前项对应字段", related='origin_id.field_id')
    content = fields.Char(string="内容", related='origin_id.content')
    mark = fields.Float(string='所占分数', digits=(8, 2), related='origin_id.proportion_mark')
    score = fields.Float(string='得分', digits=(8, 2))

    @api.onchange('score')
    def _onchange_score(self):
        if self.score:
            if self.score > self.mark:
                raise ValidationError('得分不能大于所占分数，请检查')
