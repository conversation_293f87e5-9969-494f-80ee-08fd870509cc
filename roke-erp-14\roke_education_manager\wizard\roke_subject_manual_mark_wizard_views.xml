<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="roke_student_manual_mark_wizard_form" model="ir.ui.view">
        <field name="name">roke.subject.manual.mark.wizard.form</field>
        <field name="model">roke.subject.manual.mark.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="exam_id" readonly="1" force_save="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="org_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="total_marks"/>
                        </group>
                        <group>
                            <field name="course_id" options="{'no_create': True, 'no_open': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="考试内容">
                            <field name="line_ids">
                                <tree editable="bottom" create="false" delete="0">
                                    <field name="origin_id" invisible="1" required="1"/>
                                    <field name="course_id" readonly="1" force_save="1"  options="{'no_create': True, 'no_open': True}"/>
                                    <field name="project_id" readonly="1" force_save="1"  options="{'no_create': True, 'no_open': True}"/>
                                    <field name="model_id" readonly="1" force_save="1" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="field_id" readonly="1" force_save="1" options="{'no_create': True, 'no_open': True}"/>
                                    <field name="content" readonly="1" force_save="1"/>
                                    <field name="mark" readonly="1" force_save="1"/>
                                    <field name="score"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button name='confirm' string='确定' type='object' class='oe_highlight'/>
                    <button string="取消" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>