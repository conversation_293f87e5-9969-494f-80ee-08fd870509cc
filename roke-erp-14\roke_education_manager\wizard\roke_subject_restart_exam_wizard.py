# -*- coding: utf-8 -*-
"""
Description:
    重新考试
"""
import random
import datetime
from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectRestartExamWizard(models.TransientModel):
    _name = "roke.subject.restart.exam.wizard"
    _description = '重新考试'

    exam_ids = fields.Many2one('roke.subject.student.exam', string='考试')
    # is_dispatch = fields.Bo<PERSON>an(string='是否重新分配抽题', default=False)
    start_time = fields.Datetime(string="开始时间")
    time_length = fields.Integer(string="时长(分钟)")
    end_time = fields.Datetime(string="结束时间")
    remark = fields.Text(string="说明")

    @api.onchange('start_time', 'time_length')
    def _onchange_time(self):
        if self.start_time and self.time_length:
            self.end_time = self.start_time + datetime.timedelta(minutes=self.time_length)

    @api.onchange('end_time')
    def _onchange_end_time(self):
        """
        校验结束时间是否正确
            需要大于等于开始时间+时长
        :return:
        """
        if self.end_time:
            if self.start_time and self.time_length:
                if self.end_time < self.start_time + datetime.timedelta(minutes=self.time_length):
                    self.end_time = False
                    return {"warning": {
                        "title": "提醒", "message": "结束时间必须大于等于 开始时间 + 时长"
                    }, "value": {}}

    def confirm(self):
        for exam_id in self.exam_ids:
            # 将考试作废
            exam_id.write({'state': 'cancel'})
            # if not self.is_dispatch:
            new_exam_id = self.env['roke.subject.student.exam'].create({
                'parent_id': exam_id.parent_id.id,
                'org_id': exam_id.org_id.id,
                'employee_id': exam_id.employee_id.id,
                'course_id': exam_id.course_id.id,
                'rule_id': exam_id.rule_id.id,
                'test_paper_id': exam_id.test_paper_id.id,
                'start_time': self.start_time,
                'end_time': self.end_time,
                'time_length': exam_id.time_length,
                'state': 'wait_exam',
                'remark': self.remark,
                'real_start_time': False,
                'real_end_time': False,
                'real_time_length': False,
                'delayed_length': False,
                'suspend_time': False,
                'suspend_length': False,
            })
            for line in exam_id.line_ids:
                new_line = line.copy()
                new_line.write({'parent_id': new_exam_id.id})
            new_exam_id.total_marks = sum(line.total_marks for line in new_exam_id.line_ids)
            # 将对应考试记录作废
            record_obj = self.env['roke.subject.examination.record'].search(
                [('student_id', '=', exam_id.employee_id.id), ('exam_id', '=', exam_id.id),
                 ('state', '!=', 'cancel')])
            grade_obj = self.env['roke.subjectivity.grade'].search(
                [('student_id', '=', exam_id.employee_id.id), ('exam_id', '=', exam_id.id),
                 ('state', '!=', 'cancel')])
            record_obj.write({'state': 'cancel'})
            new_record_obj = record_obj.copy()
            new_record_obj.exam_id = new_exam_id.id
            if grade_obj:
                grade_obj.write({'state': 'cancel'})
                new_grade_obj = grade_obj.copy()
                new_grade_obj.exam_id = new_exam_id.id
            # else:
            #     exam_id = self.env['roke.subject.student.exam'].create({
            #         'name': self.exam_id.name,
            #         'org_id': self.org_id.id,
            #         'course_id': self.course_id.id,
            #         'start_time': self.start_time,
            #         'end_time': self.end_time,
            #         'remark': self.remark,
            #     })
            #     self.env['roke.subject.student.exam.person'].create({
            #         'exam_id': exam_id.id,
            #         'employee_id': self.employee_id.id,
            #     })
            #     # 执行分配考题
            #     print('执行分配考题')
            #     exam_id.btn_title_data()
