# -*- coding: utf-8 -*-
"""
Description:
    发起考试
"""
import random
import datetime

from odoo import models, fields, api
from odoo.exceptions import ValidationError


class RokeSubjectStudentAllotExamWizard(models.TransientModel):
    _name = "roke.subject.student.allot.exam.wizard"
    _description = '发起考试'

    name = fields.Char(string="考试名称")
    number = fields.Char(string="考试编号", default=lambda self: self.env['ir.sequence'].next_by_code('roke.base.exam'))
    pattern_type = fields.Selection([('practice', '练习模式'), ('exam', '考试模式')], string='模式类型', default='exam')
    dispatch_type = fields.Selection([('same', '考题一致'), ('different', '随机分配')], string='考题分配方式', default='same')
    rule_id = fields.Many2one('roke.subject.rules', string='抽题规则')
    round = fields.Integer(string='场次', default=1)
    course_id = fields.Many2one('roke.subject.course', string="科目")
    start_time = fields.Datetime(string="开始时间")
    time_length = fields.Integer(string="时长", default=90)
    end_time = fields.Datetime(string="结束时间")
    description = fields.Text(string="说明")
    is_test_paper = fields.Boolean(string="使用预设试题")
    test_paper_id = fields.Many2one('roke.base.test.paper', string="试题")
    checkbox_score_type = fields.Selection([('give', '多选题半对给分'), ('not_give', '多选题半对不给分')],
                                           string='多选题给分模式', default='give')

    @api.onchange('start_time', 'time_length')
    def _onchange_time(self):
        if self.start_time and self.time_length:
            self.end_time = self.start_time + datetime.timedelta(minutes=self.time_length)

    @api.onchange('end_time')
    def _onchange_end_time(self):
        """
        校验结束时间是否正确
            需要大于等于开始时间+时长
        :return:
        """
        if self.end_time:
            if self.start_time and self.time_length:
                if self.end_time < self.start_time + datetime.timedelta(minutes=self.time_length):
                    self.end_time = False
                    return {"warning": {
                        "title": "提醒", "message": "结束时间必须大于等于 开始时间 + 时长"
                    }, "value": {}}

    @api.onchange('is_test_paper')
    def _onchange_is_test_paper(self):
        if not self.is_test_paper:
            self.test_paper_id = False
        else:
            self.rule_id = False

    @api.onchange('name')
    def _onchange_name(self):
        if self.name:
            # 判断当前名称是否被使用过
            exam_count = self.env['roke.base.exam'].search_count([('name', '=', self.name)])
            if exam_count > 0:
                raise ValidationError('当前考试名称【%s】已被使用，请确认' % self.name)

    def confirm(self):
        exam_id = self.env['roke.base.exam'].create({
                'name': self.name,
                'number': self.number,
                'pattern_type': self.pattern_type,
                'dispatch_type': self.dispatch_type,
                'rule_id': self.rule_id.id,
                'test_paper_id': self.test_paper_id.id,
                'course_id': self.course_id.id,
                'start_time': self.start_time if self.pattern_type == 'exam' else False,
                'time_length': self.time_length if self.pattern_type == 'exam' else False,
                'end_time': self.end_time if self.pattern_type == 'exam' else False,
                'remark': self.description,
                'checkbox_score_type': self.checkbox_score_type,
            })
        return {
            'name': '本次创建的考试',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', '=', exam_id.id)],
            'context': {'batch_button': True},
            'res_model': 'roke.base.exam'
        }

    @api.onchange("pattern_type")
    def _onchange_pattern_type(self):
        if self.pattern_type == 'practice':
            self.start_time = False
            self.end_time = False
            self.time_length = False

