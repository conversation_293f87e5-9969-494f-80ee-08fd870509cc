<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="roke_student_allot_exam_wizard_form" model="ir.ui.view">
        <field name="name">roke.subject.student.allot.exam.wizard.form</field>
        <field name="model">roke.subject.student.allot.exam.wizard</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name" required="1"/>
                            <field name="number" required="1"/>
                            <field name="pattern_type" required="1"/>
                            <field name="is_test_paper"/>
                            <field name="rule_id" options="{'no_create': True, 'no_open': True}"
                                   domain="[('start_time','&lt;=',time.strftime('%Y-%m-%d %H:%M:%S')), ('end_time','&gt;=',time .strftime('%Y-%m-%d %H:%M:%S'))]"
                                   attrs="{'invisible': [('is_test_paper', '=', True)], 'required': [('is_test_paper', '!=', True)]}"/>
                            <field name="test_paper_id" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible': [('is_test_paper', '!=', True)], 'required': [('is_test_paper', '=', True)]}"/>

                        </group>
                        <group>
                            <field name="course_id" required="1" options="{'no_create': True, 'no_open': True}"/>
                            <field name="dispatch_type"
                                   attrs="{'invisible': [('is_test_paper', '=', True)], 'required': [('is_test_paper', '!=', True)]}"/>
                            <field name="checkbox_score_type" required="1"/>
                            <field name="start_time"
                                   attrs="{'invisible': [('pattern_type', '!=', 'exam')], 'required': [('pattern_type', '=', 'exam')]}"/>
                            <field name="time_length"
                                   attrs="{'invisible': [('pattern_type', '!=', 'exam')], 'required': [('pattern_type', '=', 'exam')]}"/>
                            <field name="end_time"
                                   attrs="{'invisible': [('pattern_type', '!=', 'exam')], 'required': [('pattern_type', '=', 'exam')]}"/>
                        </group>
                    </group>
                </sheet>

                <footer>
                    <button name='confirm' string='确定' type='object' class='oe_highlight'/>
                    <button string="取消" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
</odoo>