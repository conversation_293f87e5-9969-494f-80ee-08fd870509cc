from odoo import http, tools
from odoo.addons.roke_mes_production.controller.production import Production


class EquipmentProduction(Production):

    @http.route('/roke/work_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_submit(self, **kwargs):
        equipment_id = http.request.jsonrequest.get('equipment_id', False)  # 报工ID
        data = super(EquipmentProduction, self).work_submit(**kwargs)
        if not data or data.get("state") == "error":
            return data
        # 判断变更
        work_record_id = data.get('work_record_id', False)  # 报工ID
        if work_record_id and equipment_id:
            http.request.env['roke.work.record'].search([("id", "=", work_record_id)]).write({
                "equipment_id": equipment_id
            })
        return data
