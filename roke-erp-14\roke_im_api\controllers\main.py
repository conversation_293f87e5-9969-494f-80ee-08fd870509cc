# -*- coding: utf-8 -*-

import json
import logging

from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.http import request, Response
_logger = logging.getLogger(__name__)

class RokeIMTencentController(http.Controller):

    @http.route('/im/sig', type='json', auth='user', csrf=False, cors="*")
    def get_im_sig(self, **kwargs):
        info = http.request.env.company.get_user_im_info(phone=http.request.env.user.phone)
        if not info:
            data = {
                "company": http.request.env.company,
                "user": http.request.env.user,
            }
            return {"code": 1, "message": "没有在组织配置IM信息", "data": data}
        data = {
            "sig": info.get("usersig"),
            "sdkappid": info.get("im_sdk_app_id")
        }
        return {"code": 0, "message": "Success", "data": data}