# -*- coding: utf-8 -*-
import random
import requests
import json
import secrets
import string

from odoo import models, fields, api
from odoo.exceptions import ValidationError

from .user_sig import TLSSigAPIv2, AsyncImUser


class InheritResCompany(models.Model):
    _inherit = 'res.company'

    im_sdk_app_id = fields.Char(string="SDKAppID")
    im_sdk_secret_key = fields.Char(string="SDKSecretKey")
    im_identifier = fields.Char(string="管理用户", default="administrator")
    source_mark = fields.Char(string="来源字段", help="必须是英文字母, 且长度不得超过8字节", size=8)


    def get_user_im_info(self, phone):
        icp = self.env['ir.config_parameter']
        im_domain = icp.sudo().get_param('im_domain', False)
        if not self.im_sdk_app_id or not self.im_sdk_secret_key or not phone or not im_domain:
            return None
        im_api = TLSSigAPIv2(int(self.im_sdk_app_id), self.im_sdk_secret_key)
        settings = {
            "im_domain": im_domain,
            "im_sdk_app_id": self.im_sdk_app_id,
            "im_sdk_secret_key": self.im_sdk_secret_key,
            "im_identifier": phone,
            "usersig": im_api.genUserSig(phone)
        }
        return settings

    @api.model
    def get_im_info(self):
        icp = self.env['ir.config_parameter']
        im_domain = icp.sudo().get_param('im_domain', False)
        if not self.im_sdk_app_id or not self.im_sdk_secret_key or not self.im_identifier or not im_domain:
            return None
        im_api = TLSSigAPIv2(int(self.im_sdk_app_id), self.im_sdk_secret_key)
        settings = {
            "im_domain": im_domain,
            "im_sdk_app_id": self.im_sdk_app_id,
            "im_sdk_secret_key": self.im_sdk_secret_key,
            "im_identifier": self.im_identifier,
            "usersig": im_api.genUserSig(self.im_identifier),
            "source_mark": self.source_mark
        }
        return settings
    
    # def manage_users(self):
    #     im_info = self.get_im_info()

    #     if not im_info:
    #         return

    #     t_domain = im_info.get("im_domain")
    #     sdkappid = im_info.get("im_sdk_app_id")
    #     sdk_secret_key = im_info.get("im_sdk_secret_key")
    #     identifier = im_info.get("im_identifier")
    #     usersig = im_info.get("usersig")

    #     for user_id in self.user_ids.filtered(lambda r: r.phone):
    #         avatar = f"/web/image?model=res.users&id={user_id.id}&field=image_128"
    #         user = {
    #             "im_account": user_id.phone,
    #             "name": user_id.name, "avatar": avatar
    #         }
    #         aiu = AsyncImUser(t_domain, sdkappid, identifier, usersig, user)
    #         result, message = aiu.create()
    #         if not result:
    #             raise ValidationError(message)
    