# -*- coding: utf-8 -*-

from odoo import models, fields, api
from .user_sig import TLSSigAPIv2


class ConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    im_domain = fields.Char(string="IM域名")


    def set_values(self):
        super(ConfigSettings, self).set_values()
        icp = self.env['ir.config_parameter']
        icp.sudo().set_param('im_domain', self.im_domain)

    @api.model
    def get_values(self):
        icp = self.env['ir.config_parameter']
        res = super(ConfigSettings, self).get_values()
        res.update(
            im_domain=icp.sudo().get_param('im_domain', default="console.tim.qq.com"),
        )
        return res
