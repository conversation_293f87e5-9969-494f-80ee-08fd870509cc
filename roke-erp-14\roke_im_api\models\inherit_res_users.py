# -*- coding: utf-8 -*-
import random
import requests
import json
import secrets
import string
import asyncio
import logging

from odoo import models, fields, api
from odoo.exceptions import ValidationError

from .user_sig import TLSSigAPIv2, AsyncImUser

_logger = logging.getLogger(__name__)

class InheritResUsers(models.Model):
    _inherit = 'res.users'

    async def manage_user_async(self):
        for company_id in self.company_ids:
            im_info = company_id.get_im_info()
            if not im_info:
                continue
            t_domain = im_info.get("im_domain")
            sdkappid = im_info.get("im_sdk_app_id")
            sdk_secret_key = im_info.get("im_sdk_secret_key")
            identifier = im_info.get("im_identifier")
            usersig = im_info.get("usersig")
            avatar = f"/web/image?model=res.users&id={self.id}&field=image_128"
            user = {
                "im_account": self.phone, "name": self.name, "avatar": avatar
            }
            aiu = AsyncImUser(t_domain, sdkappid, identifier, usersig, user)
            loop = asyncio.get_event_loop()
            result, message = await loop.run_in_executor(None, aiu.create)
            if not result:
                raise ValidationError(message)
            else:
                source_mark = im_info.get("source_mark")
                aiu.source_mark = source_mark
                aiu.friend_list = self.search([("id", "!=", self.id), ("phone", "!=", False)]).mapped("phone")
                result, message = await loop.run_in_executor(None, aiu.add_friend)
                _logger.info(f"result: {result}, message: {message}")

    def manage_user(self):
        """
        同步包装异步方法，兼容原有调用方式
        """
        try:
            asyncio.run(self.manage_user_async())
        except RuntimeError:
            # 如果已在事件循环中（如odoo shell），则直接调用异步方法
            loop = asyncio.get_event_loop()
            loop.run_until_complete(self.manage_user_async())

    @api.model
    def create(self, vals):
        res = super(InheritResUsers, self).create(vals)
        if res.phone:
            res.manage_user()
        return res

    def write(self, values):
        res = super(InheritResUsers, self).write(values)
        if values.get("phone"):
            self.manage_user()
        return res
