<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <!-- <record id="action_users_imaccount" model="ir.actions.server">
        <field name="name">同步到IM</field>
        <field name="model_id" ref="base.model_res_users"/>
        <field name="binding_model_id" ref="base.model_res_users"/>
        <field name="state">code</field>
        <field name="code">
            if records:
                action = records.manage_user()
        </field>
    </record> -->

    <!-- <record id="change_password_wizard_action" model="ir.actions.act_window">
        <field name="name">同步到IM</field>
        <field name="res_model">change.password.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" ref="base.model_res_users"/>
    </record> -->
</odoo>