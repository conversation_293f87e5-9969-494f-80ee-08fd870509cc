# -*- coding: utf-8 -*-
from odoo import api, fields, models, tools, SUPERUSER_ID, _

class User2AccountWizard(models.TransientModel):
    _name = "wizard.user.account"
    _description = "同步IM账号"

    def action_confirm(self):
        self.ensure_one()
        ids = self._context.get('active_model') == 'res.users' and self._context.get('active_ids') or []
        user_ids = self.env(user=SUPERUSER_ID)["res.users"].search([("id", "in", ids)])
        for user_id in user_ids:
            user_id.manage_user()
        return {'type': 'ir.actions.act_window_close'}