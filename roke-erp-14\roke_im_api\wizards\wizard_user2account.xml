<odoo>
    <data>
        <record id="view_wizard_user2account_form" model="ir.ui.view">
            <field name="name">wizard.user.account.form</field>
            <field name="model">wizard.user.account</field>
            <field name="arch" type="xml">
                <form string="同步到IM">
                    <group>
                        <span class="o_form_label">将所有用户同步到即时通讯</span>
                    </group>
                    <footer>
                        <button string="确认" type="object" name="action_confirm" class="btn-primary"/>
                        <button string="取消" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="change_password_wizard_action" model="ir.actions.act_window">
            <field name="name">同步到IM</field>
            <field name="res_model">wizard.user.account</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="binding_model_id" ref="base.model_res_users"/>
        </record>
    </data>
</odoo>