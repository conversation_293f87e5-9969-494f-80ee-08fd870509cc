# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
import logging

_logger = logging.getLogger(__name__)


class WorkCenterFtpFile(http.Controller):

    @http.route('/roke/get_work_center_ftp', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_work_center(self):
        """
        获取工作中心ftp服务器配置（或将以下内容放到现有工作中心接口，需与前端确认）。
        入参：工作中心ID。
        出参：IP、端口、用户名、密码、文件路径。
        """
        work_center_id = http.request.jsonrequest.get('work_center_id')
        work_center = http.request.env['roke.work.center'].browse(work_center_id)
        result = []
        result.append({
            'ip': work_center.ftp_server_ip,
            'port': work_center.ftp_server_port,
            'username': work_center.ftp_server_user,
            'password': work_center.ftp_server_password,
            'file_path': work_center.file_path
        })
        return {
            "state": "success",
            "msgs": "获取成功",
            "result": result
        }

    @http.route('/roke/get_document_datdas', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_document_datdas(self):
        """
        获取文件二进制内容。
        入参：文档ID
        出参：二进制文件
        """
        document_id = http.request.jsonrequest.get('document_id')
        document = http.request.env['documents.document'].browse(document_id)
        if document:
            return {
                "state": "success",
                "msgs": "获取成功",
                "result": document.datas
            }
        return {
            "state": "error", "msgs": "获取文件id失败"
        }

    @http.route('/roke/confirm_dispatch', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def confirm_dispatch(self):
        """
        接口3：确认下发。
        说明：前端将文件下发后调用此接口，后台修改文件列表中的文件状态和路径。
        入参：工作中心ID、文档ID、文件路径
        出参：
        """
        work_center_id = http.request.jsonrequest.get('work_center_id')
        document_id = http.request.jsonrequest.get('document_id')
        file_path = http.request.jsonrequest.get('file_path')

        ftp_file = http.request.env['roke.work.center.ftp.file'].search(
            [('work_center_id', '=', work_center_id), ('doc_id', '=', document_id)])
        if ftp_file:
            ftp_file.write({
                'file_path': file_path,
                'state': '已下发'
            })
            return {"state": "success", "msgs": "下发成功"}
        return {
            "state": "error", "msgs": "下发失败"
        }

    @http.route('/roke/get_work_center_files', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_work_center_files(self):
        """
        接口4：获取工作中心作业文件列表
        入参：工作中心ID
        出参：ID、文件名称、文件路径、状态
        """
        work_center_id = http.request.jsonrequest.get('work_center_id')
        work_center = http.request.env['roke.work.center'].browse(work_center_id)
        files = work_center.ftp_file_ids
        result = []
        for file in files:
            result.append({'id': file.id, 'file_name': file.doc_name, 'file_path': file.file_path, 'state': file.state})
        return {
            "state": "success",
            "msgs": "获取成功",
            "result": result
        }

    @http.route('/roke/confirm_delete', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def confirm_delete(self):
        """
        接口5：确认删除
        说明：前端将文件删除后调用此接口，后台修改文件列表中的文件状态和路径。
        入参：工作中心文件ID
        出参：“删除成功”
        """
        file_id = http.request.jsonrequest.get('file_id')
        ftp_file = http.request.env['roke.work.center.ftp.file'].browse(file_id)
        if ftp_file:
            ftp_file.write({
                'file_path': '',
                'state': '已删除'
            })
            return {"state": "success", "msgs": "删除成功"}
        return {
            "state": "error", "msgs": "删除失败"
        }
