# -*- coding: utf-8 -*-

from odoo import models, fields, api, _

from odoo.exceptions import ValidationError
from odoo.addons.roke_mes_documents.wizard.document_assign_production_wizard import RokeWizardAssignDocumentProduction


class RokeWizardAssignDocumentProductionInherit(RokeWizardAssignDocumentProduction):
    _inherit = "roke.wizard.document.assign.production"

    work_center_ids = fields.Many2many("roke.work.center", "roke_wizard_doc_assign_work_center_rel", string="工作中心")

    def confirm(self):
        # 首先调用父类的confirm方法
        super(RokeWizardAssignDocumentProductionInherit, self).confirm()
        documents = self.env['documents.document'].browse(self.env.context.get('active_ids'))
        # 工作中心
        for work_center in self.work_center_ids:
            # 检查FTP连接信息并提供详细的错误提示
            missing_fields = []
            if not work_center.ftp_server_ip:
                missing_fields.append("ftp_server_ip")
            if not work_center.ftp_server_port:
                missing_fields.append("ftp_server_port")
            if not work_center.ftp_server_user:
                missing_fields.append("ftp_server_user")
            if not work_center.ftp_server_password:
                missing_fields.append("ftp_server_password")
            if not work_center.file_path:
                missing_fields.append("file_path")

            if missing_fields:
                missing_fields_str = ", ".join(missing_fields)
                raise ValidationError(_("请前往工作中心配置以下字段：{}").format(missing_fields_str))

            documents.write({"work_center_ids": [(4, work_center.id)]})
            for document in documents:
                # 检查是否存在重复的roke.work.center.ftp.file记录
                existing_ftp_file = self.env['roke.work.center.ftp.file'].search([
                    ('work_center_id', '=', work_center.id),
                    ('doc_id', '=', document.id),
                ], limit=1)

                if existing_ftp_file:
                    # 如果存在重复记录，则直接在该记录上进行下发操作
                    existing_ftp_file.action_issue()
                else:
                    # 如果不存在重复记录，则创建新记录并进行下发操作
                    ftp_file = self.env['roke.work.center.ftp.file'].create({
                        'work_center_id': work_center.id,
                        'doc_id': document.id,
                        'doc_name': document.name,
                        'state': '未下发',
                    })
                    ftp_file.action_issue()