# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.addons.roke_mes_documents.wizard.document_production_wizard import RokeWizardDoucmentProduction


class RokeWizardDocumentProductionInherit(RokeWizardDoucmentProduction):
    _inherit = "roke.wizard.document.production"

    work_center_ids = fields.Many2many("roke.work.center", string="工作中心")

    def confirm(self):
        """修改文档在用工艺路线，并根据工作中心的FTP配置执行相应操作"""
        # 首先调用父类的confirm方法
        super(RokeWizardDocumentProductionInherit, self).confirm()
        # 然后添加新的逻辑来处理工作中心和FTP配置
        new_work_center_ids = self.work_center_ids.ids
        self.document_id.write({"work_center_ids": [(6, 0, new_work_center_ids)]})
        current_work_center_ids = self.document_id.work_center_ids.ids

        removed_work_centers = set(current_work_center_ids) - set(new_work_center_ids)

        for work_center in self.work_center_ids:
            # 检查FTP连接信息并提供详细的错误提示
            missing_fields = []
            if not work_center.ftp_server_ip:
                missing_fields.append("ftp_server_ip")
            if not work_center.ftp_server_port:
                missing_fields.append("ftp_server_port")
            if not work_center.ftp_server_user:
                missing_fields.append("ftp_server_user")
            if not work_center.ftp_server_password:
                missing_fields.append("ftp_server_password")
            if not work_center.file_path:
                missing_fields.append("file_path")

            if missing_fields:
                missing_fields_str = ", ".join(missing_fields)
                raise ValidationError(_("请前往工作中心配置以下字段：{}").format(missing_fields_str))
            ftp_file = self.env['roke.work.center.ftp.file'].search(
                [('work_center_id', '=', work_center.id), ('doc_id', '=', self.document_id.id)], limit=1)
            if not ftp_file:
                ftp_file = self.env['roke.work.center.ftp.file'].create({
                    'work_center_id': work_center.id,
                    'doc_id': self.document_id.id,
                    'doc_name': self.document_id.name,
                    'state': '未下发',
                })
            ftp_file.action_issue()

        for work_center_id in removed_work_centers:
            ftp_files = self.env['roke.work.center.ftp.file'].search([
                ('work_center_id', '=', work_center_id),
                ('doc_id', '=', self.document_id.id),
            ])
            for ftp_file in ftp_files:
                ftp_file.action_delete()
