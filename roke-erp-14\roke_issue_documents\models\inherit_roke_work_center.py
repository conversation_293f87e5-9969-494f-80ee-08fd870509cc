# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.osv import expression


class RokeWorkCenter(models.Model):
    _inherit = "roke.work.center"

    # 工作文件部分字段
    ftp_server_ip = fields.Char(string="文件服务器IP")
    ftp_server_port = fields.Integer(string="文件服务器端口")
    ftp_server_user = fields.Char(string="用户名")
    ftp_server_password = fields.Char(string="密码")
    file_path = fields.Char(string="文件路径")
    ftp_file_ids = fields.One2many("roke.work.center.ftp.file", "work_center_id", string="文件列表")
