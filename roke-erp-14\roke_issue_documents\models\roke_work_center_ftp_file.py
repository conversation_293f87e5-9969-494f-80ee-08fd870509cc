# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.exceptions import ValidationError
from ftplib import FTP
import base64
import io
import logging

_logger = logging.getLogger(__name__)


class RokeWorkCenterFtpFile(models.Model):
    _name = "roke.work.center.ftp.file"
    _inherit = ['mail.thread']
    _description = "工作中心文件列表"
    _order = "doc_name"

    work_center_id = fields.Many2one("roke.work.center", string="工作中心", required=True)
    doc_id = fields.Many2one("documents.document", string="文档")
    doc_name = fields.Char(string="文档名称")
    ftp_server_ip = fields.Char(string="文档服务器IP")
    file_path = fields.Char(string="文档路径")
    state = fields.Selection([("未下发", "未下发"), ("已下发", "已下发"), ("已删除", "已删除")], required=True, string="状态",
                             default="未下发")

    def action_issue(self):

        # FTP连接信息
        host = self.work_center_id.ftp_server_ip
        username = self.work_center_id.ftp_server_user
        password = self.work_center_id.ftp_server_password
        remotepath = self.work_center_id.file_path  # 远程目标路径

        # 建立FTP连接
        ftp = FTP()
        ftp.encoding = 'utf-8'  # 设置编码为utf-8
        ftp.connect(host=host,port=self.work_center_id.ftp_server_port)

        # 登录FTP服务器
        ftp.login(user=username, passwd=password)

        # 切换到指定目录
        ftp.cwd(remotepath)

        try:
            ftp.storbinary('STOR {}'.format(self.doc_name), io.BytesIO(base64.b64decode(self.doc_id.datas)))
        except Exception as e:
            ftp.quit()
            return ValidationError("下发失败")
        _logger.info("文件下发成功！")
        ftp.quit()
        # 修改状态为已下发，更新文件路径
        self.state = "已下发"
        self.ftp_server_ip = self.work_center_id.ftp_server_ip
        self.file_path = remotepath + '/' + self.doc_name

    def action_delete(self):

        # FTP连接信息
        print(1111)
        host = self.ftp_server_ip
        username = self.work_center_id.ftp_server_user
        password = self.work_center_id.ftp_server_password

        # 建立FTP连接
        ftp = FTP()
        ftp.encoding = 'utf-8'  # 设置编码为utf-8
        ftp.connect(host=host,port=self.work_center_id.ftp_server_port)
        print(3333)
        # 登录FTP服务器
        ftp.login(user=username, passwd=password)

        try:
            ftp.delete(self.file_path)
        except Exception as e:
            ftp.quit()
            return ValidationError("删除失败")
        _logger.info("文件删除成功！")
        ftp.quit()
        # 修改状态为已删除，更新文件路径为空
        self.state = "已删除"
        self.ftp_server_ip = ""
        self.file_path = ""
        # 反向操作：在roke.wizard.document.production模型上删除对应的work_center_ids数据
        self.doc_id.write({"work_center_ids": [(3, self.work_center_id.id)]})

