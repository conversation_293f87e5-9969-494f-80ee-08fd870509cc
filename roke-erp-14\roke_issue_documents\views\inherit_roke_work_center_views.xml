<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工作中心-->
    <record id="inherit_view_roke_work_center_form" model="ir.ui.view">
        <field name="name">inherit.work.center.form</field>
        <field name="model">roke.work.center</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_work_center_form"/>
        <field name="arch" type="xml">
            <xpath expr="//notebook" position="inside">
                <page string="作业文件">
                    <group col="4">
                        <group>
                            <field name="ftp_server_ip"/>
                        </group>
                        <group>
                            <field name="ftp_server_port"/>
                        </group>
                        <group>
                            <field name="ftp_server_user"/>
                        </group>
                        <group>
                            <field name="ftp_server_password" password="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="file_path"/>
                    </group>
                        <field name="ftp_file_ids">
                            <tree editable="bottom">
                                <field name="doc_name"/>
                                <field name="ftp_server_ip" string="IP"/>
                                <field name="file_path"/>
                                <field name="state"/>
                                <button name="action_issue" type="object" string="下发" class="btn btn-primary" attrs="{'invisible': [('state', '=', '已下发')]}" confirm="确认下发此作业文件？" />
                                <button name="action_delete" type="object" string="删除" class="btn btn-danger" attrs="{'invisible': [('state', '=', '已删除')]}" confirm="确认删除此作业文件？"/>
                            </tree>
                        </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
