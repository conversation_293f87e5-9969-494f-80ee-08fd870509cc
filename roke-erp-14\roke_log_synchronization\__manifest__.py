# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.


{
    'name': '数转-日志生成',
    'depends': ['roke_workstation_api', 'roke_mes_production'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """

    """,
    'data': [
        'security/ir.model.access.csv',
        'views/inherit_roke_mes_stock_picking_views.xml',
        'views/inherit_roke_production_result_views.xml',
        'views/inherit_roke_production_task_views.xml',
        'views/inherit_roke_purchase_order_views.xml',
        'views/inherit_roke_sale_order_views.xml',
        'views/inherit_roke_work_order_views.xml',
        'views/log_sync_handle_model_views.xml',
        'wizard/roke_update_create_user_wizard_views.xml',
        'wizard/multiple_assign_workers_wizard_views.xml',
        'wizard/roke_update_stock_order_date_wizard_views.xml',
        'wizard/roke_random_stock_order_date_wizard_views.xml',
    ],
    'demo': [
    ],
    'qweb':[
    ],
    'application': True,
    'installable': True,
}
