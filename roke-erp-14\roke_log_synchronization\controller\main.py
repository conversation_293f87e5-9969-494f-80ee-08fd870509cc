from odoo import http, tools


class FormlistSearchCustom(http.Controller):

    @http.route('/roke/tool/form_list_search_custom', type='json', auth='public', methods=["post"], cors='*', csrf=False)
    def form_list_search_custom(self):
        _self = http.request
        params = _self.jsonrequest.get("params")
        model_name = params.get("model_name")
        form_id = params.get("id")
        form_field = params.get("form_field")
        form_model = params.get("form_model")
        domain = params.get("domain")
        old_domain = params.get("old_domain")
        _domain = []
        field_ids = []

        if form_field:
            form_id = _self.env[form_model].sudo().search([("id", "=", form_id)])
            field_ids = form_id[form_field].ids
            if domain:
                _domain.append("&")
            _domain.append(("id", "=", field_ids))
        for i, v in enumerate(domain):
            if i != domain.__len__() - 1:
                _domain.append("&")
            _domain.append(tuple(v))
        for v in old_domain:
            if type(v) == str:
                _domain.append(v)
                continue
            _domain.append(tuple(v))
        field_data = _self.env[model_name].sudo().search(_domain).ids
        data = {"code": 0, "message": 'success', "data": field_data}
        if form_field:
            data.update({"reload_data": field_ids})
        return data
