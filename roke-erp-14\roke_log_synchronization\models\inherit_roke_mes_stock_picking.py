from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import math
import json
from datetime import datetime, timedelta, time


class RokeMesStockPicking(models.Model):
    _inherit = "roke.mes.stock.picking"


    def update_create_uid(self):
        ids_list = []
        for rec in self:
            ids_list.append(rec.id)

        res = self.env["roke.update.create.user.wizard"].create({
            "res_ids_str": json.dumps(ids_list),
            "model_name": self._name
        })

        return {
            'name': '更改创建人',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.update.create.user.wizard'
        }

    def update_create_date(self):
        # 把 当前 model 的所有数据的 创建时间都更改 为 订单日期
        orders = self.env[self._name].sudo().search([])
        for order in orders:
            # 更新 create_date 字段
            self.env.cr.execute(f"""UPDATE {self._name.replace(".", "_")}
                                    SET create_date = '{datetime.combine(order.picking_date, time.min).strftime('%Y-%m-%d %H:%M:%S')}'
                                    WHERE id = {order.id}""")

    def update_order_date(self):
        # 更改单据日期
        stock_id_list = [rec.id for rec in self]

        wizard_id = self.env["roke.update.stock.order.date.wizard"].create({
            "stock_ids": [(6, 0, stock_id_list)]
        })

        return {
            'name': '更改创建人',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': wizard_id.id,
            'res_model': 'roke.update.stock.order.date.wizard',
        }

    def random_order_date(self):
        # 随机更改单据日期
        stock_id_list = [rec.id for rec in self]

        wizard_id = self.env["roke.random.stock.order.date.wizard"].create({
            "stock_ids": [(6, 0, stock_id_list)]
        })

        return {
            'name': '更改创建人',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': wizard_id.id,
            'res_model': 'roke.random.stock.order.date.wizard',
        }

    def update_stock_move_line_data(self):
        # 修改 详细作业的 作业人员-> 单据 创建人/ 作业时间-> 单据创建时间/入库日期 -> 单据创建时间/move_date -> 单据创建时间
        for rec in self:
            for ml_id in rec.ml_ids:
                self.env.cr.execute(f"""UPDATE {ml_id._name.replace(".", "_")}
                                        SET create_uid={rec.create_uid.id}, 
                                            create_date='{rec.create_date.strftime('%Y-%m-%d %H:%M:%S')}', 
                                            inbound_date='{rec.create_date.date().strftime('%Y-%m-%d')}',
                                            move_date='{rec.create_date.date().strftime('%Y-%m-%d')}'
                                        WHERE id = {ml_id.id}""")
