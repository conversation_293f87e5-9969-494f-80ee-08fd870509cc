from odoo import models, fields, api, SUPERUSER_ID
from odoo.exceptions import ValidationError, UserError
from odoo.tools import float_is_zero
from datetime import datetime, timedelta, time
import json
import requests

class RokePurchaseOrder(models.Model):
    _inherit = "roke.purchase.order"

    def update_create_uid(self):
        ids_list = []
        for rec in self:
            ids_list.append(rec.id)

        res = self.env["roke.update.create.user.wizard"].create({
            "res_ids_str": json.dumps(ids_list),
            "model_name": self._name
        })

        return {
            'name': '更改创建人',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.update.create.user.wizard'
        }

    def update_create_date(self):
        # 把 当前 model 的所有数据的 创建时间都更改 为 订单日期
        orders = self.env[self._name].sudo().search([])
        for order in orders:
            # 更新 create_date 字段
            self.env.cr.execute(f"""UPDATE {self._name.replace(".", "_")}
                                    SET create_date = '{datetime.combine(order.order_date, time.min).strftime('%Y-%m-%d %H:%M:%S')}'
                                    WHERE id = {order.id}""")