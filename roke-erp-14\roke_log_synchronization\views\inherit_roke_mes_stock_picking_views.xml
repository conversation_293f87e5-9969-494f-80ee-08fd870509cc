<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <record id="action_update_create_uid" model="ir.actions.server">
        <field name="name">批量更新创建用户</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_uid()
        </field>
    </record>

    <record id="action_update_create_date" model="ir.actions.server">
        <field name="name">更新创建时间</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_date()
        </field>
    </record>

    <record id="action_random_order_date" model="ir.actions.server">
        <field name="name">随机单据日期</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.random_order_date()
        </field>
    </record>

    <record id="action_update_order_date" model="ir.actions.server">
        <field name="name">更改单据日期</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_order_date()
        </field>
    </record>

    <record id="action_update_stock_move_line_data" model="ir.actions.server">
        <field name="name">更改详细作业数据</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_stock_move_line_data()
        </field>
    </record>
</odoo>