<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="update_create_uid" model="ir.actions.server">
        <field name="name">批量更新创建用户</field>
        <field name="model_id" ref="model_roke_production_result"/>
        <field name="binding_model_id" ref="model_roke_production_result"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_uid()
        </field>
    </record>

    <record id="update_create_date" model="ir.actions.server">
        <field name="name">更新创建时间</field>
        <field name="model_id" ref="model_roke_production_result"/>
        <field name="binding_model_id" ref="model_roke_production_result"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_date()
        </field>
    </record>

</odoo>