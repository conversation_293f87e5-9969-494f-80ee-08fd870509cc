<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="update_create_uid_action" model="ir.actions.server">
        <field name="name">批量更新创建用户</field>
        <field name="model_id" ref="model_roke_production_task"/>
        <field name="binding_model_id" ref="model_roke_production_task"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_uid()
        </field>
    </record>

    <record id="update_create_date_action" model="ir.actions.server">
        <field name="name">更新创建时间</field>
        <field name="model_id" ref="model_roke_production_task"/>
        <field name="binding_model_id" ref="model_roke_production_task"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_date()
        </field>
    </record>

    <record id="action_multiple_create_bom_stock_picking" model="ir.actions.server">
        <field name="name">批量bom领料</field>
        <field name="model_id" ref="model_roke_production_task"/>
        <field name="binding_model_id" ref="model_roke_production_task"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.multiple_create_bom_stock_picking()
        </field>
    </record>

</odoo>