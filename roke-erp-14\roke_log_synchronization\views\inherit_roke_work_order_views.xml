<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_update_create_date_for_work_order" model="ir.actions.server">
        <field name="name">更新创建时间</field>
        <field name="model_id" ref="model_roke_work_order"/>
        <field name="binding_model_id" ref="model_roke_work_order"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_date()
        </field>
    </record>

    <record id="action_update_create_uid_for_work_order" model="ir.actions.server">
        <field name="name">批量更新创建用户</field>
        <field name="model_id" ref="model_roke_work_order"/>
        <field name="binding_model_id" ref="model_roke_work_order"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.update_create_uid()
        </field>
    </record>

    <record id="action_multiple_assign_workers" model="ir.actions.server">
        <field name="name">批量作业人员报工</field>
        <field name="model_id" ref="model_roke_work_order"/>
        <field name="binding_model_id" ref="model_roke_work_order"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.multiple_assign_workers()
        </field>
    </record>

    <record id="action_multiple_update_planned_start_time" model="ir.actions.server">
        <field name="name">批量修改排产时间</field>
        <field name="model_id" ref="model_roke_work_order"/>
        <field name="binding_model_id" ref="model_roke_work_order"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="code">
            action = records.multiple_update_planned_start_time()
        </field>
    </record>
</odoo>