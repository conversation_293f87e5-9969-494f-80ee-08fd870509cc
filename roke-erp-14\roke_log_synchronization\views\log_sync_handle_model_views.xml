<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_log_sync_handle_model_tree" model="ir.ui.view">
        <field name="name">log.sync.handle.model.tree</field>
        <field name="model">log.sync.handle.model</field>
        <field name="arch" type="xml">
            <tree string="日志生成">
                <field name="model_name"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建时间"/>
            </tree>
        </field>
    </record>

    <record id="view_log_sync_handle_model_form" model="ir.ui.view">
        <field name="name">log.sync.handle.model.form</field>
        <field name="model">log.sync.handle.model</field>
        <field name="arch" type="xml">
            <form string="日志生成">
                <group>
                    <group>
                        <group>
                            <field name="model_name"/>
                        </group>
                        <group>
                            <field name="model_type" attrs="{'invisible': [('model_name', '!=', 'roke.mes.stock.picking')]}"/>
                        </group>
                        <group>
                            <button name="add_log" string="日志生成 " type="object" class="oe_highlight"/>
                        </group>
                    </group>
                    <group>
                        <group>

                        </group>
                        <group>

                        </group>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="action_log_sync_handle_model" model="ir.actions.act_window">
        <field name="name">日志生成</field>
        <field name="res_model">log.sync.handle.model</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="log_sync_handle_model_menu"
              name="日志生成" sequence="42"
              parent="roke_pub_platform.menu"
              action="action_log_sync_handle_model"
              groups="base.group_system"/>
</odoo>