from odoo import models, fields, api, _
import json


class RokeUpdateCreateUserWizard(models.TransientModel):
    _name = "roke.update.create.user.wizard"
    _description = "更新创建用户"

    res_ids_str = fields.Char(string="来源ids")
    model_name = fields.Char(string="模型名称")
    user_id = fields.Many2one("res.users", string="用户")

    def action_confirm(self):
        res_ids_list = json.loads(self.res_ids_str)
        res_ids = self.env[self.model_name].sudo().search([("id", "in", res_ids_list)])
        for res_id in res_ids:
            # # 更新 create_uid 字段
            self.env.cr.execute(f"""
                        UPDATE {self.model_name.replace(".", "_")}
                        SET create_uid = {self.user_id.id}
                        WHERE id = {res_id.id}
                    """)

            # 刷新缓存
            # res_id.invalidate_cache()
            # res_id.sudo().write({"create_uid": self.user_id.id})