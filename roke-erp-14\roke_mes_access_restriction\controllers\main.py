# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo.addons.web.controllers import main
from odoo.addons.roke_mes_client.controller import login as mes_login
from odoo.http import request
from odoo.tools.translate import _
from odoo import http, SUPERUSER_ID
import logging

_logger = logging.getLogger(__name__)


class Home(main.Home):

    @http.route('/web/login', type='http', auth="public")
    def web_login(self, redirect=None, **kw):
        """
        网页端登录限制IP
        """
        main.ensure_db()
        values = request.params.copy()
        if request.httprequest.method == 'POST':
            request_env = request.httprequest.environ
            if 'HTTP_X_REAL_IP' in request_env:
                ip_address = request_env['HTTP_X_REAL_IP']
            else:
                ip_address = request_env['REMOTE_ADDR']
            _logger.info("登录IP: %s" % ip_address)
            if request.params['login']:
                user_rec = request.env['res.users'].sudo().search([('login', '=', request.params['login'])])
                if user_rec.allowed_ip_ids:
                    if ip_address not in user_rec.allowed_ip_ids.mapped("ip_address"):
                        values['error'] = _("当前IP禁止登录，请使用允许的网络环境登录。")
        if "error" in values:
            return request.render('web.login', values)
        return super(Home, self).web_login(redirect, **kw)


class MesClient(mes_login.Login):

    @http.route('/roke/mes/client_login', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def roke_mes_login(self):
        """
        MES 登录校验MAC
        :return:
        """
        _logger.info("MES 登录鉴权校验MAC")
        _logger.info(http.request.jsonrequest)
        phone = http.request.jsonrequest.get("phone") or ""
        login = http.request.jsonrequest.get("login") or ""
        if not login:
            user = http.request.env(user=SUPERUSER_ID)['res.users'].search([("phone", "=", phone)])
        else:
            user = http.request.env(user=SUPERUSER_ID)['res.users'].search([("login", "=", login)])
        if user and user.allowed_mac_ids.filtered(lambda mac: mac.allowed):
            mac_address = http.request.jsonrequest.get("mac_address") or ""
            # 校验入参的MAC复制是否在允许的范围内
            if mac_address not in user.allowed_mac_ids.filtered(lambda mac: mac.allowed).mapped("mac_address"):
                # 不允许时，将本次登录的MAC写入到用户信息表，便于管理设置允许
                user.write({"allowed_mac_ids": [(0, 0, {"mac_address": mac_address})]})
                return {"state": "error", "msgs": "禁止当前设备登录，请联系管理员将本次登录的设备码设为允许，当前设备码：%s" % mac_address}
        return super(MesClient, self).roke_mes_login()
