# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class ResUsersInherit(models.Model):
    _inherit = 'res.users'

    allowed_ip_ids = fields.One2many('roke.allowed.ip', 'user_id', string='IP')
    allowed_mac_ids = fields.One2many('roke.allowed.mac', 'user_id', string='MAC')


class AllowedIP(models.Model):
    _name = 'roke.allowed.ip'

    user_id = fields.Many2one('res.users', string='用户')
    ip_address = fields.Char(string='允许的IP地址')


class AllowedMAC(models.Model):
    _name = 'roke.allowed.mac'

    # 配合APP登录接口使用，roke_mes_client入参mac地址记录到本表
    user_id = fields.Many2one('res.users', string='用户')
    mac_address = fields.Char(string='设备码（MAC地址）', help="登录过的MAC地址")
    allowed = fields.<PERSON><PERSON>an(string='允许', help="勾选后仅允许允许的设备登录")