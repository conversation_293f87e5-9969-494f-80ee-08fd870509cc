# -*- coding: utf-8 -*-
from . import models
from . import wizard
from . import controller

from odoo import api, SUPERUSER_ID

def predefined_menu_permissions(cr, registry):
    """
    预定义菜单权限
    :param cr:
    :param registry:
    :return:
    """
    env = api.Environment(cr, SUPERUSER_ID, {})
    # 应收管理
    menu1_id = env.ref('roke_mes_account.roke_mes_account_receivable_menu', raise_if_not_found=False)
    groups1 = [
        "roke_pub_access.group_sale_manager",
        "roke_pub_access.group_account_manager",
    ]
    group1_ids = [
        env.ref(group, raise_if_not_found=False)
        for group in groups1 if env.ref(group, raise_if_not_found=False)
    ]
    env['ir.ui.menu'].set_x_access_menus(menu1_id, group1_ids)

    # 应付管理
    menu2_id = env.ref('roke_mes_account.roke_mes_account_payable_menu', raise_if_not_found=False)
    groups2 = [
        "roke_pub_access.group_purchase_manager",
        "roke_pub_access.group_account_manager",
    ]
    group2_ids = [
        env.ref(group, raise_if_not_found=False)
        for group in groups2 if env.ref(group, raise_if_not_found=False)
    ]
    env['ir.ui.menu'].set_x_access_menus(menu2_id, group2_ids)

