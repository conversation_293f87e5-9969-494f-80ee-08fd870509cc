# -*- coding: utf-8 -*-
{
    'name': '应收应付管理',
    'version': '1.1',
    'category': 'mes',
    # 'depends': ['roke_mes_base',"roke_false_phoenix"],
    'depends': ['roke_mes_base'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        应收应付管理
    """,
    'data': [
        'data/sequence_data.xml',
        'data/payment_method_data.xml',
        'data/order_data.xml',
        'data/fix_history_data.xml',
        'data/decimal_precision_data.xml',
        # 'data/bank_data.xml',
        'security/ir.model.access.csv',
        # 'views/roke_bank_dict_view.xml',
        'views/roke_mes_account_move_views.xml',
        'views/roke_mes_collection.xml',
        'views/roke_mes_pay.xml',
        'views/roke_mes_payment_views.xml',
        'views/inherit_res_config_settings.xml',
        'views/roke_payment_stage_type_view.xml',
        'views/inherit_roke_product_views.xml',
        'views/menus.xml'
    ],
    'demo': [
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
    'post_init_hook': 'predefined_menu_permissions',
}
