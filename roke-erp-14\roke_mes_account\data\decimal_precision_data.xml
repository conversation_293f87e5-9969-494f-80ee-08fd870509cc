<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="roke_decimal_YSYFSL" model="decimal.precision">
            <field name="name">YSYFSL</field>
            <field name="digits">2</field>
        </record>
        <record forcecreate="True" id="roke_decimal_YSYFDJ" model="decimal.precision">
            <field name="name">YSYFDJ</field>
            <field name="digits">2</field>
        </record>
        <record forcecreate="True" id="roke_decimal_YSYFJE" model="decimal.precision">
            <field name="name">YSYFJE</field>
            <field name="digits">2</field>
        </record>
    </data>
</odoo>
