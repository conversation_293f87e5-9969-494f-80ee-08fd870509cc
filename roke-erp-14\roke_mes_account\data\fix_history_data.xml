<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- 自动任务模板 -->
    <!--    <data noupdate="1">-->
    <!--        <record id="ir_cron_auto_fix_history_collection" model="ir.cron">-->
    <!--            <field name="name">修复历史数据: roke.mes.collection</field>-->
    <!--            <field name="interval_number">1</field>-->
    <!--            <field name="interval_type">days</field>-->
    <!--            <field name="numbercall">1</field>-->
    <!--            <field name="doall" eval="True"/>-->
    <!--            <field name="model_id" ref="model_roke_mes_collection"/>-->
    <!--            <field name="code">model.fix_history_data()</field>-->
    <!--            <field name="state">code</field>-->
    <!--            <field name="nextcall" eval="(DateTime.now() + timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')"/>-->
    <!--        </record>-->
    <!--    </data>-->
</odoo>