<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <record id="roke_mes_pay_type_selection_ysk" model="roke.mes.pay.type.selection">
            <field name="value">预收款</field>
            <field name="code">收款</field>
        </record>
        <record id="roke_mes_pay_type_selection_sk" model="roke.mes.pay.type.selection">
            <field name="value">收款</field>
            <field name="code">收款</field>
        </record>
        <record id="roke_mes_pay_type_selection_yfk" model="roke.mes.pay.type.selection">
            <field name="value">预付款</field>
            <field name="code">付款</field>
        </record>
        <record id="roke_mes_pay_type_selection_fk" model="roke.mes.pay.type.selection">
            <field name="value">付款</field>
            <field name="code">付款</field>
        </record>
        <record id="mobile_roke_order_collection_1" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_pay_type_selection"/>
            <field name="model_name">收款类型</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>
        <record id="mobile_roke_order_collection_2" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_payment"/>
            <field name="model_name">收/付款方式</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>
        <record id="mobile_roke_order_collection_2" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_collection"/>
            <field name="model_name">付款类型</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>
    </data>

    <data noupdate="1">
        <record id="roke_payment_stage_type_01" model="roke.payment.stage.type">
            <field name="name">未付款</field>
        </record>
        <record id="roke_payment_stage_type_02" model="roke.payment.stage.type">
            <field name="name">付款中</field>
        </record>
        <record id="roke_payment_stage_type_03" model="roke.payment.stage.type">
            <field name="name">已付款</field>
        </record>
        <record id="roke_pay_stage_type_01" model="roke.pay.stage.type">
            <field name="name">未收款</field>
        </record>
        <record id="roke_pay_stage_type_02" model="roke.pay.stage.type">
            <field name="name">收款中</field>
        </record>
        <record id="roke_pay_stage_type_03" model="roke.pay.stage.type">
            <field name="name">已收款</field>
        </record>
    </data>
</odoo>