<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--收款单编号-->
        <record id="roke_mes_payment_receive_code_sequence" model="ir.sequence">
            <field name="name">收款单</field>
            <field name="code">roke.mes.payment.receive.code</field>
            <field name="prefix">SK/%(y)s%(month)s/</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <record id="roke_mes_payment_pay_code_sequence" model="ir.sequence">
            <field name="name">付款单</field>
            <field name="code">roke.mes.payment.pay.code</field>
            <field name="prefix">FK/%(y)s%(month)s/</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--请购单编号-->
        <record id="roke_purchase_requisition_code_sequence" model="ir.sequence">
            <field name="name">请购单编号</field>
            <field name="code">roke.purchase.requisition.code</field>
            <field name="prefix">QG%(y)s%(month)s</field>
            <field eval="True" name="use_date_range"/>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--销售发票编号-->
        <record id="roke_sale_invoice_code_sequence" model="ir.sequence">
            <field name="name">销售发票编号</field>
            <field name="code">roke.sale.invoice.code</field>
            <!--格式化字符串 %(y)显示后年份后两位-->
            <field name="prefix">INV/XS/%(y)s%(month)s</field>
            <field eval="True" name="use_date_range"/>
            <!--序号数-->
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--采购发票编号-->
        <record id="roke_purchase_invoice_code_sequence" model="ir.sequence">
            <field name="name">采购发票编号</field>
            <field name="code">roke.purchase.invoice.code</field>
            <!--格式化字符串 %(y)显示后年份后两位-->
            <field name="prefix">INV/CG/%(y)s%(month)s</field>
            <field eval="True" name="use_date_range"/>
            <!--序号数-->
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--客户对账单编号-->
        <record id="roke_mes_customer_statement_code_sequence" model="ir.sequence">
            <field name="name">客户对账单编号</field>
            <field name="code">roke.mes.customer.statement.code</field>
            <field name="prefix">SA/%(y)s%(month)s/</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--应付账款编号-->
        <record id="roke_mes_accounts_payable_code_sequence" model="ir.sequence">
            <field name="name">应付账款编号</field>
            <field name="code">roke.mes.accounts.payable.code</field>
            <field name="prefix">AP/%(y)s%(month)s/</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
        <!--应收账款编号-->
        <record id="roke_mes_accounts_receivable_code_sequence" model="ir.sequence">
            <field name="name">应收账款编号</field>
            <field name="code">roke.mes.accounts.receivable.code</field>
            <field name="prefix">AR/%(y)s%(month)s/</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
</odoo>
