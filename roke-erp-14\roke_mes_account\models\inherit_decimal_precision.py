# -*- encoding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models, tools
import odoo.addons

import logging
import sys

_logger = logging.getLogger(__name__)


class InheritDecimalPrecision(models.Model):
    _inherit = 'decimal.precision'

    def get_prefabricate_dict(self):
        res = super(InheritDecimalPrecision, self).get_prefabricate_dict()
        res.update({
            self.env.ref('roke_mes_account.roke_decimal_YSYFSL').id: 'YSYFSL',
            self.env.ref('roke_mes_account.roke_decimal_YSYFDJ').id: 'YSYFDJ',
            self.env.ref('roke_mes_account.roke_decimal_YSYFJE').id: 'YSYFJE',
        })
        return res
