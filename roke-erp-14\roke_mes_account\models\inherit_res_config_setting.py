# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class ConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    is_open_tax = fields.Boolean(string="启用税率", default=False)
    tax_rate_setting = fields.Float(string='税率设置', digits='Account')
    account_qty_precision = fields.Integer(string="应收应付数量精度", default=2)
    account_price_precision = fields.Integer(string="应收应付单价精度", default=2)
    account_amount_precision = fields.Integer(string="应收应付金额精度", default=2)

    @api.constrains("account_qty_precision", "account_price_precision", "account_amount_precision")
    def check_precision(self):
        for rec in self:
            if rec.account_qty_precision <= 0 or rec.account_price_precision <= 0 or rec.account_amount_precision <= 0:
                raise ValidationError("应收应付数量,应收应付单价,应收应付金额的精度必须大于0")

    def set_values(self):
        super(ConfigSettings, self).set_values()
        ConfigParameter = self.env['ir.config_parameter'].sudo()
        ConfigParameter.set_param('is.open.tax', self.is_open_tax or False)
        ConfigParameter.set_param('tax.rate.setting', self.tax_rate_setting or 0)
        if self.is_open_tax:
            sql = 'update roke_product set tax_rate = {}'.format(self.tax_rate_setting or 0)
        else:
            sql = 'update roke_product set tax_rate = 0'
        self._cr.execute(sql)
        ConfigParameter.set_param('account.qty.precision', self.account_qty_precision or 2)
        ConfigParameter.set_param('account.price.precision', self.account_price_precision or 2)
        ConfigParameter.set_param('account.amount.precision', self.account_amount_precision or 2)
        YSYFDJ_precision_record = self.env.ref('roke_mes_account.roke_decimal_YSYFDJ')
        YSYFDJ_precision_record.digits = self.account_price_precision
        YSYFSL_precision_record = self.env.ref('roke_mes_account.roke_decimal_YSYFSL')
        YSYFSL_precision_record.digits = self.account_qty_precision
        YSYFJE_precision_record = self.env.ref('roke_mes_account.roke_decimal_YSYFJE')
        YSYFJE_precision_record.digits = self.account_amount_precision

    @api.model
    def get_values(self):
        icp = self.env['ir.config_parameter']
        res = super(ConfigSettings, self).get_values()
        res.update(
            is_open_tax=icp.sudo().get_param('is.open.tax', default=False),
            tax_rate_setting=icp.sudo().get_param('tax.rate.setting', default=0),
            account_qty_precision=icp.sudo().get_param('account.qty.precision', default=2),
            account_price_precision=icp.sudo().get_param('account.price.precision', default=False),
            account_amount_precision=icp.sudo().get_param('account.amount.precision', default=False),
        )
        return res
