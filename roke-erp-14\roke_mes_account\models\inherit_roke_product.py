# -*- coding: utf-8 -*-

from odoo import models, fields, api
from odoo.exceptions import UserError, ValidationError


class InheritRokeProduct(models.Model):
    _inherit = "roke.product"

    def _get_is_open_tax(self):
        return self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)

    def _get_tax_rate(self):
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        if is_tax:
            return self.env['ir.config_parameter'].sudo().get_param('tax.rate.setting', default=0)
        else:
            return 0

    is_open_tax = fields.<PERSON><PERSON>an('启用税率', compute='_compute_is_open_tax', default=_get_is_open_tax)
    tax_rate = fields.Float(string='税率', digits='Account', default=_get_tax_rate)

    def _compute_is_open_tax(self):
        # 税率是否启用
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        for record in self:
            record.is_open_tax = is_tax
            print(record.tax_rate)


