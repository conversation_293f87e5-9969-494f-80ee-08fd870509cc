from ast import literal_eval
from odoo import models, fields, api, _
import time
import datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError

class RokeBankDict(models.Model):
    _name = "roke.bank.dict"
    _description = "银行列表"
    _order = "sequence"

    sequence = fields.Integer(string="序号", default=10)
    name = fields.Char(string="名称",required=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

