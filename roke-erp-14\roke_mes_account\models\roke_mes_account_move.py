# -*- coding: utf-8 -*-
"""
Description:
    记账明细
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class RokeMesAccountMove(models.Model):
    _name = "roke.mes.account.move"
    _description = "记账明细"

    move_type = fields.Selection([
        ('应收', '应收'),
        ('应付', '应付'),
        ('收款', '收款'),
        ('付款', '付款'),
    ], string='类型', required=True, index=True)
    partner_id = fields.Many2one("roke.partner", string="业务伙伴", required=True, index=True, ondelete='restrict')
    amount = fields.Float(string="金额", tracking=True, digits='YSYFJE')
    move_date = fields.Date(string="日期", tracking=True)
    origin = fields.Char(string="来源单据")
    note = fields.Text(string="备注")
    payment_id = fields.Many2one("roke.mes.payment", string="收付款单", index=True, ondelete='cascade')
    payment_state = fields.Selection(related="payment_id.state", string="收付款单状态")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)



