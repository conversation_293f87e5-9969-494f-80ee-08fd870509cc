# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeMesAccountsPayable(models.Model):
    _name = "roke.mes.accounts.payable"
    _description = "应付账款"

    code = fields.Char(string="编号", index=True, tracking=True, copy=False, default="/")
    order_date = fields.Date('单据日期')
    supplier_id = fields.Many2one("roke.partner", string="供应商")
    supplier_type = fields.Selection([], string="供应商类别")
    summary_basis = fields.Selection([('供应商', '供应商')], string="汇总依据")
    initial_balance = fields.Float('期初余额', digits='YSYFJE')
    current_payable = fields.Float('本期应付', digits='YSYFJE')
    current_payment = fields.Float('本期付款', digits='YSYFJE')
    ending_balance = fields.Float('期末余额', digits='YSYFJE')

    @api.model
    def create(self, vals):
        if not vals.get('code') or vals.get('code') == '/':
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.accounts.payable.code')
        return super(RokeMesAccountsPayable, self).create(vals)
