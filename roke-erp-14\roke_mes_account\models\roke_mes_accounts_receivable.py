 # -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeMesAccountsReceivable(models.Model):
    _name = "roke.mes.accounts.receivable"
    _description = "应收账款"

    code = fields.Char(string="编号", index=True, tracking=True, copy=False, default="/")
    order_date = fields.Date('单据日期')
    customer_id = fields.Many2one("roke.partner", string="客户")
    customer_type = fields.Selection([], string="客户类别")
    summary_basis = fields.Selection([('客户', '客户')], string="汇总依据")
    initial_balance = fields.Float('期初余额', digits='YSYFJE')
    current_receivable = fields.Float('本期应收', digits='YSYFJE')
    current_payment = fields.Float('本期收款', digits='YSYFJE')
    ending_balance = fields.Float('期末余额', digits='YSYFJE')
    line_ids = fields.One2many('roke.mes.accounts.receivable.line', 'accounts_id', string='应收账款明细')

    @api.model
    def create(self, vals):
        if not vals.get('code') or vals.get('code') == '/':
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.accounts.receivable.code')
        return super(RokeMesAccountsReceivable, self).create(vals)


class RokeMesAccountsReceivableLine(models.Model):
    _name = "roke.mes.accounts.receivable.line"
    _description = "应收账款明细"

    accounts_id = fields.Many2one('roke.mes.accounts.receivable', string='应收账款')
    code = fields.Char(string="编号", index=True, tracking=True, copy=False, default="/")
    order_date = fields.Date('单据日期')
    customer_id = fields.Many2one("roke.partner", string="客户")
    business_type = fields.Selection([('预收款', '预收款'), ('收款', '收款')])
    add_receivable = fields.Float('增加应收款')
    add_advance = fields.Float('增加预收款')
    receivable_balance = fields.Float('应收款余额')
    ending_balance = fields.Float('期末余额')
