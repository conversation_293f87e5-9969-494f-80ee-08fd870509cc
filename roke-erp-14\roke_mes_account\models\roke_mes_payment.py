# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class RokeMesPaySelection(models.Model):
    _name = 'roke.mes.pay.type.selection'
    _rec_name = 'value'

    code = fields.Char('code')
    value = fields.Char('value')


class RokeMesPaymentMethod(models.Model):
    _name = "roke.mes.payment.method"
    _description = "收付款方式"

    name = fields.Char(string="名称", required=True)
    active = fields.Boolean(string="有效", default=True)

    _sql_constraints = [
        ('name_unique', 'UNIQUE(name)', '收付款方式已存在，不可重复。')
    ]


class RokeMesPayment(models.Model):
    _name = "roke.mes.payment"
    _description = "收/付款单"
    _inherit = ['mail.thread', "mail.activity.mixin", 'roke.order.print.mixin']
    _rec_name = "code"
    _order = "payment_date desc, code desc"

    code = fields.Char(string="编号", index=True, tracking=True, copy=False, default="/")
    state = fields.Selection(
        [("草稿", "草稿"), ("已过账", "已过账")], string="状态", required=True, tracking=True, copy=False,
        default="草稿"
    )
    payment_type = fields.Selection([("收款", "收款"), ("付款", "付款")], string="类型", required=True)
    partner_type = fields.Selection([("客户", "客户"), ("供应商", "供应商")], string="业务伙伴类型")
    partner_id = fields.Many2one(
        "roke.partner", string="业务伙伴", required=True, index=True, tracking=True, ondelete='restrict'
    )
    payment_method_id = fields.Many2one("roke.mes.payment.method", string="收/付款方式")
    amount = fields.Float(string="金额", tracking=True, required=True, digits='YSYFJE')
    payment_date = fields.Date(string="单据日期", tracking=True, default=fields.Date.context_today, required=True)
    note = fields.Text(string="备注")
    account_move_ids = fields.One2many("roke.mes.account.move", "payment_id", string="记账明细")

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    is_deduct = fields.Boolean(string="是否优惠支付")
    deduct_amount = fields.Float(string="抹零", tracking=True, digits='YSYFJE')
    deducted_amount = fields.Float(string="总优惠金额", compute="_compute_deducted_amount", tracking=True,
                                   digits='YSYFJE')
    pay_amount = fields.Float(string="支付金额", tracking=True, digits='YSYFJE')

    is_edit = fields.Boolean(string="是否可修改")
    is_advance_payment = fields.Boolean(string="是否预收/付款")

    payment_line_ids = fields.One2many("roke.mes.payment.line", "payment_id", string="收/付款明细")

    is_payment = fields.Boolean(string="是否收/付款创建")
    order_type = fields.Many2one('roke.mes.pay.type.selection', string='收款类型')
    is_red = fields.Boolean(default=False, string='红单')
    red_type = fields.Selection([('收款', '收款'), ('付款', '付款'), ('退款', '退款')], string='单据类型')
    origin_order = fields.Char('来源单据')
    is_treasurer = fields.Boolean(string='财务员发货', compute='_compute_is_treasurer')

    def _compute_is_treasurer(self):
        is_financial_manager_picking = self.env['ir.config_parameter'].sudo().get_param('is.financial.manager.picking',
                                                                                        default=False)
        for record in self:
            record.is_treasurer = False
            if is_financial_manager_picking:
                record.is_treasurer = True

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。')
    ]

    @api.onchange('payment_type')
    def _onchange_payment_type(self):
        if self.payment_type == '收款':
            search_list = [('code', '=', '收款')]
        else:
            search_list = [('code', '=', '付款')]
        res = self.env['roke.mes.pay.type.selection'].search(search_list)
        id_list = [r.id for r in res]
        return {
            'domain': {'order_type': [('id', 'in', id_list)]}
        }

    @api.depends("deduct_amount", "payment_line_ids")
    def _compute_deducted_amount(self):
        for record in self:
            total_deducted = sum(record.payment_line_ids.mapped("deducted_amount"))
            record.deducted_amount = total_deducted + record.deduct_amount

    def confirm(self):
        """
        确认收付款，创建记账明细
        :return:
        """
        # 收款单过账
        self.make_confirm()

    def make_confirm(self):
        """
        过账
        :return:
        """
        self.write({"state": "已过账"})

    def make_draft(self):
        """
        置为草稿，删除记账明细
        :return:
        """
        self.account_move_ids.unlink()
        self.write({"state": "草稿"})

    def unlink(self):
        if self.filtered(lambda p: p.state != "草稿"):
            raise ValidationError("非草稿状态的收/付款单禁止删除！如果您确认要删除，请先将单据“置为草稿”。")
        return super(RokeMesPayment, self).unlink()

    @api.model
    def create(self, vals):
        if vals.get("payment_type") == "收款":
            sequence_code = "roke.mes.payment.receive.code"
        else:
            sequence_code = "roke.mes.payment.pay.code"
        vals["code"] = self.env['ir.sequence'].next_by_code(sequence_code)
        # 处理采购明细付款
        if self.env.context.get("pay_line_ids", []):
            vals["payment_line_ids"] = self.env.context.get("pay_line_ids", [])
        res = super(RokeMesPayment, self).create(vals)
        # 关联发票付款、创建发票核销关系
        self.handle_invoice_and_payment(res)
        return res

    def _handle_invoice_and_payment(self, invoice_type, ctx_invoice_id, payment_id, invoice_line_info):
        """
        处理发票核销
        :param invoice_type: 发票类型：sale、purchase
        :param ctx_invoice_id: 发票ID
        :param payment_id: 收付款单ID
        :param invoice_line_info: 核销明细信息
        :return:
        """
        invoice_model = f"roke.{invoice_type}.invoice"
        invoice_verify_model = f"roke.{invoice_type}.invoice.verify"
        invoice = self.env[invoice_model].search([("id", "=", ctx_invoice_id)])
        invoice.write({"payment_ids": [(4, payment_id)]})
        # 创建发票核销关系
        for invoice_line_item in invoice_line_info:
            self.env[invoice_verify_model].sudo().create({
                "invoice_id": ctx_invoice_id,
                "invoice_line_id": invoice_line_item["invoice_line_id"],
                "verify_date": fields.Date.context_today(self),
                "payment_id": payment_id, "amount_verified": invoice_line_item["amount"]
            })

    def handle_invoice_and_payment(self, res):
        """
        关联发票付款、创建发票核销关系
        :param res:
        :return:
        """
        ctx_sale_invoice_id = self.env.context.get("m2m_sale_invoice_id", False)
        ctx_purchase_invoice_id = self.env.context.get("m2m_purchase_invoice_id", False)
        ctx_invoice_line_info = self.env.context.get("invoice_line_info", [])

        if ctx_sale_invoice_id:
            self._handle_invoice_and_payment("sale", ctx_sale_invoice_id, res.id, ctx_invoice_line_info)
        if ctx_purchase_invoice_id:
            self._handle_invoice_and_payment("purchase", ctx_purchase_invoice_id, res.id, ctx_invoice_line_info)


class RokeMesPaymentLine(models.Model):
    _name = "roke.mes.payment.line"
    _description = "收付款明细"

    payment_id = fields.Many2one("roke.mes.payment", string="收/付款单", ondelete="cascade", required=True)
    deducted_amount = fields.Float(string="优惠金额", digits='YSYFJE')
    paid_amount = fields.Float(string="收/付金额", digits='YSYFJE')


class RokeMesCollection(models.Model):
    _name = 'roke.mes.collection'
    _inherits = {'roke.mes.payment': 'payment_id'}
    _inherit = ['mail.thread', "mail.activity.mixin"]
    _description = "收款单"
    _rec_name = "code"
    _order = "payment_date desc, code desc"

    payment_id = fields.Many2one(
        'roke.mes.payment', string='收款单', auto_join=True, index=True, ondelete="cascade"
    )

    @api.onchange('payment_type')
    def _onchange_payment_type_collection(self):
        if self.payment_type == '收款':
            search_list = [('code', '=', '收款')]
        else:
            search_list = [('code', '=', '付款')]
        res = self.env['roke.mes.pay.type.selection'].search(search_list)
        id_list = [r.id for r in res]
        return {
            'domain': {'order_type': [('id', 'in', id_list)]}
        }

    @api.onchange("payment_type")
    def _onchange_payment_type(self):
        if self.payment_type == "收款":
            return {"value": {"partner_type": "客户"}}
        elif self.payment_type == "付款":
            return {"value": {"partner_type": "供应商"}}

    @api.onchange("partner_type")
    def _onchange_partner_type(self):
        if self.partner_type == "客户":
            return {"domain": {"partner_id": [("customer", "=", True)]}}
        elif self.partner_type == "供应商":
            return {"domain": {"partner_id": [("supplier", "=", True)]}}

    @api.onchange("amount")
    def _onchange_amount(self):
        if self.amount < 0:
            return {
                'warning': {
                    'title': "金额错误",
                    'message': "%s付款金额不能小于0" % self.payment_type
                },
                'value': {'amount': 0}
            }

    @api.onchange("deduct_amount")
    def _onchange_deduct_amount(self):
        if self.deduct_amount > self.amount:
            return {
                'warning': {
                    'title': "优惠金额错误",
                    'message': "优惠金额不能大于金额"
                },
                'value': {'deduct_amount': 0}
            }
        else:
            self.pay_amount = self.amount - self.deduct_amount

    @api.depends("deduct_amount", "payment_line_ids")
    def _compute_deducted_amount(self):
        for record in self:
            total_deducted = sum(record.payment_line_ids.mapped("deducted_amount"))
            record.deducted_amount = total_deducted + record.deduct_amount

    def confirm(self):
        """
        确认收付款，创建记账明细
        :return:
        """
        self.payment_id.confirm()

    def make_draft(self):
        """
        置为草稿
        :return:
        """
        self.payment_id.make_draft()

    def action_unlink(self):
        result = self.sudo().search([("id", "in", self.ids)])
        for item in result:
            print(" - ", item)
            item.unlink()
        return True
    def unlink(self):
        if self.filtered(lambda p: p.state != "草稿"):
            raise ValidationError("非草稿状态的收款单禁止删除！如果您确认要删除，请先将单据“置为草稿”。")
        return self.payment_id.unlink()


class RokeMesPay(models.Model):
    _name = 'roke.mes.pay'
    _inherits = {'roke.mes.payment': 'payment_id'}
    _inherit = ['mail.thread', "mail.activity.mixin"]
    _description = "付款单"
    _rec_name = "code"
    _order = "payment_date desc, code desc"

    payment_id = fields.Many2one(
        'roke.mes.payment', string='付款单', auto_join=True, index=True, ondelete="cascade"
    )

    @api.onchange('payment_type')
    def _onchange_payment_type_pay(self):
        if self.payment_type == '收款':
            search_list = [('code', '=', '收款')]
        else:
            search_list = [('code', '=', '付款')]
        res = self.env['roke.mes.pay.type.selection'].search(search_list)
        id_list = [r.id for r in res]
        return {
            'domain': {'order_type': [('id', 'in', id_list)]}
        }

    @api.onchange("payment_type")
    def _onchange_payment_type(self):
        if self.payment_type == "收款":
            return {"value": {"partner_type": "客户"}}
        elif self.payment_type == "付款":
            return {"value": {"partner_type": "供应商"}}

    @api.onchange("partner_type")
    def _onchange_partner_type(self):
        if self.partner_type == "客户":
            return {"domain": {"partner_id": [("customer", "=", True)]}}
        elif self.partner_type == "供应商":
            return {"domain": {"partner_id": [("supplier", "=", True)]}}

    @api.onchange("amount")
    def _onchange_amount(self):
        if self.amount < 0:
            return {
                'warning': {
                    'title': "金额错误",
                    'message': "%s收款金额不能小于0" % self.payment_type
                },
                'value': {'amount': 0}
            }

    @api.onchange("deduct_amount")
    def _onchange_deduct_amount(self):
        if self.deduct_amount > self.amount:
            return {
                'warning': {
                    'title': "优惠金额错误",
                    'message': "优惠金额不能大于金额"
                },
                'value': {'deduct_amount': 0}
            }
        else:
            self.pay_amount = self.amount - self.deduct_amount

    @api.depends("deduct_amount", "payment_line_ids")
    def _compute_deducted_amount(self):
        for record in self:
            total_deducted = sum(record.payment_line_ids.mapped("deducted_amount"))
            record.deducted_amount = total_deducted + record.deduct_amount

    def confirm(self):
        """
        确认收付款，创建记账明细
        :return:
        """
        self.payment_id.confirm()

    def make_draft(self):
        """
        置为草稿
        :return:
        """
        self.payment_id.make_draft()

    def unlink(self):
        if self.filtered(lambda p: p.state != "草稿"):
            raise ValidationError("非草稿状态的收款单禁止删除！如果您确认要删除，请先将单据“置为草稿”。")
        return self.payment_id.unlink()