# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeMesCustomerStatement(models.Model):
    _name = "roke.mes.customer.statement"
    _description = "客户对账单"

    code = fields.Char(string="编号", index=True, tracking=True, copy=False, default="/")
    order_date = fields.Date('单据日期')
    customer_id = fields.Many2one("roke.partner", string="客户")
    business_type = fields.Selection([('普通销售', '普通销售'), ('销售退回', '销售退回'), ('收款', '收款')],
                                     string='业务类别')
    sales_amount = fields.Float('销售金额', digits='YSYFJE')
    pre_amount = fields.Float('优惠金额', digits='YSYFJE')
    customer_cost = fields.Float('客户承担费用', digits='YSYFJE')
    receivable_amount = fields.Float('应收金额', digits='YSYFJE')
    actual_payment_amount = fields.Float('实际收款金额', digits='YSYFJE')
    receivable_balance = fields.Float('应收款余额', digits='YSYFJE')
    remarks = fields.Char('备注')

    @api.model
    def create(self, vals):
        if not vals.get('code') or vals.get('code') == '/':
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.customer.statement.code')
        return super(RokeMesCustomerStatement, self).create(vals)
