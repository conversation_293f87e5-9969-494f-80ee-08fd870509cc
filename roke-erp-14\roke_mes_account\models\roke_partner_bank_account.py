from ast import literal_eval
from odoo import models, fields, api, _
import time
import datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError

class RokePartnerBankAccount(models.Model):
    _name = "roke.partner.bank.account"
    _description = "业务伙伴账户"

    partner_id = fields.Many2one("roke.partner",string="业务伙伴",required=True, index=True, ondelete='cascade')
    bank_id = fields.Many2one("roke.bank.dict",string="银行",required=True)
    open_bank = fields.Char(string="开户行")
    account_number = fields.Char(string="账号",required=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def name_get(self):
        res = []
        for record in self:
            name = "%s（%s）" % (record.account_number, record.bank_id.name)
            res.append((record.id, name))
        return res
