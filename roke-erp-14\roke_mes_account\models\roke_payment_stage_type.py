from ast import literal_eval
from odoo import models, fields, api, _
import time
import datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError


class RokePaymentStageType(models.Model):
    _name = "roke.payment.stage.type"
    _description = "付款阶段类型"
    _sql_constraints = [('payment_stage_type_name_unique', 'unique(name)',
                         '付款阶段类型名称重复了!')]

    name = fields.Char(string="名称", required=True)

class RokePayStageType(models.Model):
    _name = "roke.pay.stage.type"
    _description = "收款阶段类型"
    _sql_constraints = [('pay_stage_type_name_unique', 'unique(name)',
                         '收款阶段类型名称重复了!')]

    name = fields.Char(string="名称", required=True)