<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--多公司-->
        <!--银行列表-->
        <record id="roke_bank_dict_multi_company_rule" model="ir.rule">
            <field name="name">银行列表多公司记录规则</field>
            <field name="model_id" ref="model_roke_bank_dict"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--记账明细-->
        <record id="roke_mes_account_move_multi_company_rule" model="ir.rule">
            <field name="name">记账明细多公司记录规则</field>
            <field name="model_id" ref="model_roke_mes_account_move"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--收/付款单-->
        <record id="roke_mes_payment_multi_company_rule" model="ir.rule">
            <field name="name">收/付款单多公司记录规则</field>
            <field name="model_id" ref="model_roke_mes_payment"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--业务伙伴账户-->
        <record id="roke_partner_bank_account_multi_company_rule" model="ir.rule">
            <field name="name">业务伙伴账户多公司记录规则</field>
            <field name="model_id" ref="model_roke_partner_bank_account"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--采购订单-->
        <record id="roke_purchase_order_multi_company_rule" model="ir.rule">
            <field name="name">采购订单多公司记录规则</field>
            <field name="model_id" ref="model_roke_purchase_order"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--采购明细-->
        <record id="roke_purchase_order_detail_multi_company_rule" model="ir.rule">
            <field name="name">采购明细多公司记录规则</field>
            <field name="model_id" ref="model_roke_purchase_order_detail"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--销售订单条款项目-->
        <record id="roke_sale_clause_item_multi_company_rule" model="ir.rule">
            <field name="name">销售订单条款项目多公司记录规则</field>
            <field name="model_id" ref="model_roke_sale_clause_item"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--销售订单条款-->
        <record id="roke_sale_order_clause_multi_company_rule" model="ir.rule">
            <field name="name">销售订单条款多公司记录规则</field>
            <field name="model_id" ref="model_roke_sale_order_clause"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--销售订单-->
        <record id="roke_sale_order_multi_company_rule" model="ir.rule">
            <field name="name">销售订单多公司记录规则</field>
            <field name="model_id" ref="model_roke_sale_order"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--订单明细-->
        <record id="roke_sale_order_line_multi_company_rule" model="ir.rule">
            <field name="name">订单明细多公司记录规则</field>
            <field name="model_id" ref="model_roke_sale_order_line"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--供应商价格表-->
        <record id="roke_supplier_price_multi_company_rule" model="ir.rule">
            <field name="name">供应商价格表多公司记录规则</field>
            <field name="model_id" ref="model_roke_supplier_price"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <!--报表多公司-->
        <!--业务伙伴余额账报表-->
        <record id="roke_partner_balance_report_multi_company_rule" model="ir.rule">
            <field name="name">业务伙伴余额账报表多公司记录规则</field>
            <field name="model_id" ref="model_roke_partner_balance_report"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--采购分析表-->
        <record id="roke_purchase_analytic_report_multi_company_rule" model="ir.rule">
            <field name="name">采购分析表多公司记录规则</field>
            <field name="model_id" ref="model_roke_purchase_analytic_report"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--销售分析表-->
        <record id="roke_sale_analytic_report_multi_company_rule" model="ir.rule">
            <field name="name">销售分析表多公司记录规则</field>
            <field name="model_id" ref="model_roke_sale_analytic_report"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <!--请购单-->
        <record id="roke_purchase_requisition_multi_company_rule" model="ir.rule">
            <field name="name">请购单多公司记录规则</field>
            <field name="model_id" ref="model_roke_purchase_requisition"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>