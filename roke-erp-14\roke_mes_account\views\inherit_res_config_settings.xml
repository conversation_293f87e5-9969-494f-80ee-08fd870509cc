<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_mes_sale_inherit_config_settings_view_form" model="ir.ui.view">
        <field name="name">roke.mes.sale.inherit.config.settings.view.form</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" id="roke_mes_account" data-string="应收应付设置"
                     string="应收应付设置"
                     data-key="roke_mes_account">
                    <h2>应收应付设置</h2>
                    <div class="row mt16 o_settings_container" id="account_setting_main">
                        <div class="col-12 col-lg-6 o_setting_box" title="启用税率">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="is_open_tax"/>
                                <div class="text-muted">
                                    勾选启用税率
                                </div>
                                <field name="is_open_tax" required="1"/>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" title="应收应付单价" attrs="{'invisible': [('is_open_tax', '=', False)]}">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="tax_rate_setting"/>
                                <div class="text-muted">
                                    设置整体税率。
                                </div>
                                <field name="tax_rate_setting"/>
                            </div>
                        </div>
                    </div>
                    <h2>精度设置</h2>
                    <div class="row mt16 o_settings_container" id="account_precision_setting_main">

                        <div class="col-12 col-lg-6 o_setting_box" title="应收应付数量">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="account_qty_precision"/>
                                <div class="text-muted">
                                    设置应收应付管理中数量的精度。
                                </div>
                                <field name="account_qty_precision" required="1"/>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" title="应收应付单价">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="account_price_precision"/>
                                <div class="text-muted">
                                    设置应收应付管理中单价的精度。
                                </div>
                                <field name="account_price_precision" required="1"/>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box" title="应收应付金额">
                            <div class="o_setting_left_pane"/>
                            <div class="o_setting_right_pane">
                                <label for="account_amount_precision"/>
                                <div class="text-muted">
                                    设置应收应付管理中金额的精度。
                                </div>
                                <field name="account_amount_precision" required="1"/>
                            </div>
                        </div>

                    </div>
                </div>
            </xpath>

        </field>
    </record>

<!--    <record id="roke_mes_account_inherit_production_config_form_view" model="ir.ui.view">-->
<!--        <field name="name">roke.mes.account.inherit.production.config.form.view</field>-->
<!--        <field name="model">res.config.settings</field>-->
<!--        <field name="priority" eval="20"/>-->
<!--        <field name="inherit_id" ref="roke_mes_production.roke_mes_production_inherit_config_settings_form_view"/>-->
<!--        <field name="arch" type="xml">-->
<!--            <xpath expr="//div[@id='app_show_production_order']" position="before">-->
<!--                <div class="col-12 col-lg-6 o_setting_box" title="应收应付" id="app_show_sale_order">-->
<!--                    <div class="o_setting_left_pane">-->
<!--                        <field name="app_show_sale_order"/>-->
<!--                    </div>-->
<!--                    <div class="o_setting_right_pane">-->
<!--                        <label for="app_show_sale_order"/>-->
<!--                        <div class="text-muted">-->
<!--                            勾选后移动端报工时显示销售订单字段。-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </xpath>-->
<!--            <xpath expr="//div[@id='app_required_production_order']" position="before">-->
<!--                <div class="col-12 col-lg-6 o_setting_box" title="销售订单" id="app_required_sale_order">-->
<!--                    <div class="o_setting_left_pane">-->
<!--                        <field name="app_required_sale_order"/>-->
<!--                    </div>-->
<!--                    <div class="o_setting_right_pane">-->
<!--                        <label for="app_required_sale_order"/>-->
<!--                        <div class="text-muted">-->
<!--                            勾选后移动端报工时必填销售订单字段。-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </xpath>-->
<!--        </field>-->
<!--    </record>-->
</odoo>
