<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--银行列表-->
    <!--search-->
    <record id="view_roke_bank_dict_search" model="ir.ui.view">
        <field name="name">roke.bank.dict.search</field>
        <field name="model">roke.bank.dict</field>
        <field name="arch" type="xml">
            <search string="银行列表">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_bank_dict_tree" model="ir.ui.view">
        <field name="name">roke.bank.dict.tree</field>
        <field name="model">roke.bank.dict</field>
        <field name="arch" type="xml">
            <tree string="银行列表" editable="top">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_bank_dict_action" model="ir.actions.act_window">
        <field name="name">银行列表</field>
        <field name="res_model">roke.bank.dict</field>
        <field name="view_mode">tree</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个银行。
          </p>
        </field>
    </record>

</odoo>