<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--记账明细-->
    <!--search-->
    <record id="view_roke_mes_account_move_search" model="ir.ui.view">
        <field name="name">roke.mes.account.move.search</field>
        <field name="model">roke.mes.account.move</field>
        <field name="arch" type="xml">
            <search string="记账明细">
                <field name="partner_id"/>
                <field name="move_date"/>
                <field name="origin"/>
                <field name="note"/>
                <filter string="应收" name="应收" domain="[('move_type', '=', '应收')]"/>
                <filter string="应付" name="应付" domain="[('move_type', '=', '应付')]"/>
                <filter string="收款" name="收款" domain="[('move_type', '=', '收款')]"/>
                <filter string="付款" name="付款" domain="[('move_type', '=', '付款')]"/>
                <group expand="0" string="Group By">
                    <filter string="业务伙伴" name="group_partner_id" context="{'group_by': 'partner_id'}"/>
                    <filter string="类型" name="group_move_type" context="{'group_by': 'move_type'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_account_move_tree" model="ir.ui.view">
        <field name="name">roke.mes.account.move.tree</field>
        <field name="model">roke.mes.account.move</field>
        <field name="arch" type="xml">
            <tree string="记账明细" expand="True">
                <field name="move_type"/>
                <field name="partner_id"/>
                <field name="move_date" optional="show"/>
                <field name="amount" sum="合计"/>
                <field name="origin" optional="show"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_account_move_form" model="ir.ui.view">
        <field name="name">roke.mes.account.move.form</field>
        <field name="model">roke.mes.account.move</field>
        <field name="arch" type="xml">
            <form string="记账明细">
                <header/>
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="move_type"/>
                            <field name="partner_id"/>
                            <field name="payment_id" attrs="{'invisible': [('payment_id', '=', False)]}"/>
                            <field name="amount"/>
                            <field name="move_date"/>
                        </group>
                        <group>
                            <field name="origin"/>
                            <field name="note"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    
</odoo>
