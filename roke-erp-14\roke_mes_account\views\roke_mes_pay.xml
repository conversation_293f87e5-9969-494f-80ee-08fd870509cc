<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--付款-->
    <!--search-->
    <record id="view_roke_mes_pay_search" model="ir.ui.view">
        <field name="name">roke.mes.pay.search</field>
        <field name="model">roke.mes.pay</field>
        <field name="arch" type="xml">
            <search string="付款单">
                <field name="code"/>
                <field name="partner_id"/>
                <field name="payment_date"/>
                <field name="note"/>
                <filter string="草稿" name="草稿" domain="[('state', '=', '草稿')]"/>
                <filter string="已过账" name="已过账" domain="[('state', '=', '已过账')]"/>
                <group expand="0" string="Group By">
                    <filter string="业务伙伴" name="group_partner_id" context="{'group_by': 'partner_id'}"/>
                    <filter string="状态" name="group_partner_id" context="{'group_by': 'state'}"/>
                    <filter string="日期" name="group_payment_date" context="{'group_by': 'payment_date'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_pay_tree" model="ir.ui.view">
        <field name="name">roke.mes.pay.tree</field>
        <field name="model">roke.mes.pay</field>
        <field name="arch" type="xml">
            <tree string="付款单" decoration-info="state=='草稿'">
                <field name="code"/>
                <field name="partner_id"/>
                <field name="partner_type" optional="show"/>
                <field name="payment_date" optional="show"/>
                <field name="amount" sum="合计"/>
                <field name="note" optional="show"/>
                <field name="state" optional="show"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_pay_form" model="ir.ui.view">
        <field name="name">roke.mes.pay.form</field>
        <field name="model">roke.mes.pay</field>
        <field name="arch" type="xml">
            <form string="付款单">
                <header>
                    <button name="confirm" type="object" string="确认" class="oe_highlight"
                            attrs="{'invisible': [('state', '=', '已过账')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <group id="g1">
                    <group>
                        <group>
                            <field name="partner_type" invisible="1"/>
                            <field name="partner_id" attrs="{'readonly': [('state', '!=', '草稿')]}"
                                   options="{'no_create': True}"/>
                            <field name="amount"
                                   attrs="{'readonly': ['|', ('state', '!=', '草稿'), ('is_edit', '=', True)]}"/>
                        </group>
                        <group>
                            <field name="payment_type" invisible="1"/>
                            <field name="order_type" options="{'no_create': True}"/>
                            <field name="payment_method_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="origin_order" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        </group>
                        <group>
                            <field name="payment_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="red_type" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        </group>
                    </group>
                    <field name="is_edit" invisible="1"/>
                </group>
<!--                <sheet>-->
<!--                    <div class="oe_button_box" name="button_box"/>-->
<!--                    <div class="oe_title">-->
<!--                        <h1>-->
<!--                            <field name="code" readonly="1"/>-->
<!--                        </h1>-->
<!--                        <widget name="web_ribbon" title="退款" bg_color="bg-danger"-->
<!--                                attrs="{'invisible': [('is_red','=', False)]}"/>-->
<!--                    </div>-->
<!--                    <group id="g1">-->
<!--                        <group>-->
<!--                            <field name="payment_id" readonly="1" required="0" invisible="1"/>-->
<!--                            <newline/>-->
<!--                            <field name="payment_type" widget="radio" attrs="{'readonly': [('state', '!=', '草稿')]}"-->
<!--                                   invisible="1"/>-->
<!--                            <field name="order_type"/>-->
<!--                            <field name="is_red"/>-->
<!--                            <field name="partner_type" attrs="{'readonly': [('state', '!=', '草稿')]}"/>-->
<!--                            <field name="partner_id" attrs="{'readonly': [('state', '!=', '草稿')]}"-->
<!--                                   options="{'no_create': True}"/>-->
<!--                            <field name="payment_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>-->
<!--                            <field name="amount"-->
<!--                                   attrs="{'readonly': ['|', ('state', '!=', '草稿'), ('is_edit', '=', True)]}"/>-->
<!--                            <field name="deduct_amount"-->
<!--                                   attrs="{'invisible': [('is_deduct', '!=', True)], 'readonly': [('state', '!=', '草稿')]}"/>-->
<!--                            <field name="deducted_amount" readonly="1"-->
<!--                                   attrs="{'invisible': [('is_deduct', '!=', True)]}" force_save="1"/>-->
<!--                            <field name="pay_amount" readonly="1" attrs="{'invisible': [('is_deduct', '!=', True)]}"-->
<!--                                   force_save="1"/>-->
<!--                            <field name="is_deduct" invisible="1"/>-->
<!--                            <field name="is_edit" invisible="1"/>-->
<!--                        </group>-->
<!--                        <group>-->
<!--                            <field name="payment_method_id" options="{'no_create': True}"/>-->
<!--                            <field name="is_advance_payment"/>-->
<!--                            <field name="bank_account_id" attrs="{'readonly': [('state', '!=', '草稿')]}"-->
<!--                                   domain="[('partner_id','=',partner_id)]"/>-->
<!--                            <field name="note" attrs="{'readonly': [('state', '!=', '草稿')]}"-->
<!--                                   placeholder="此处可以填写备注或说明"/>-->
<!--                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>-->
<!--                        </group>-->
<!--                    </group>-->
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!--pivot-->
    <record model="ir.ui.view" id="view_roke_mes_pay_pivot">
        <field name="name">roke.mes.pay.pivot</field>
        <field name="model">roke.mes.pay</field>
        <field name="arch" type="xml">
            <pivot string="付款单" display_quantity="True" sample="1">
                <field name="partner_id" type="row"/>
                <field name="state" type="row"/>
                <field name="amount" type="measure"/>
            </pivot>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mes_pay_action" model="ir.actions.act_window">
        <field name="name">付款单</field>
        <field name="res_model">roke.mes.pay</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_pay_form"/>
        <field name="domain">[("payment_type", "=", "付款"), ('order_type.value', '=', '付款')]</field>
        <field name="context">{"default_payment_type": "付款", "default_partner_type": "供应商", "default_red_type":
            "付款", "default_order_type": 4}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个付款单。
            </p>
        </field>
    </record>

     <record id="roke_mes_roke_mes_pay_delete" model="ir.actions.server">
        <field name="name">删除</field>
        <field name="type">ir.actions.server</field>
        <field name="binding_model_id" ref="roke_mes_account.model_roke_mes_pay"/>
        <field name="model_id" ref="roke_mes_account.model_roke_mes_pay"/>
        <!-- 放入更多插槽 -->
        <field name="button_slot">common</field>
        <field name="sequence">1</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                records.unlink()
        </field>
    </record>

</odoo>
