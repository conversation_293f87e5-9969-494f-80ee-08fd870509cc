<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--收/付款-->
    <!--search-->
    <record id="view_roke_mes_payment_search" model="ir.ui.view">
        <field name="name">roke.mes.payment.search</field>
        <field name="model">roke.mes.payment</field>
        <field name="arch" type="xml">
            <search string="收/付款">
                <field name="code"/>
                <field name="partner_id"/>
                <field name="payment_date"/>
                <field name="note"/>
                <filter string="草稿" name="草稿" domain="[('state', '=', '草稿')]"/>
                <filter string="已过账" name="已过账" domain="[('state', '=', '已过账')]"/>
                <group expand="0" string="Group By">
                    <filter string="业务伙伴" name="group_partner_id" context="{'group_by': 'partner_id'}"/>
                    <filter string="状态" name="group_partner_id" context="{'group_by': 'state'}"/>
                    <filter string="日期" name="group_payment_date" context="{'group_by': 'payment_date'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_payment_tree" model="ir.ui.view">
        <field name="name">roke.mes.payment.tree</field>
        <field name="model">roke.mes.payment</field>
        <field name="arch" type="xml">
            <tree string="收/付款" decoration-info="state=='草稿'">
                <field name="code"/>
                <field name="payment_date"/>
                <field name="partner_id"/>
                <field name="partner_type"/>
                <field name="amount"/>
                <field name="order_type" string="收款类型"/>
                <field name="payment_method_id" string="收款方式"/>
                <field name="red_type"/>
                <field name="origin_order"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
                <field name="note"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_payment_form" model="ir.ui.view">
        <field name="name">roke.mes.payment.form</field>
        <field name="model">roke.mes.payment</field>
        <field name="arch" type="xml">
            <form string="收/付款">
                <header>
                    <button name="confirm" type="object" string="确认" class="oe_highlight"
                            attrs="{'invisible': [('state', '=', '已过账')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                    <field name="red_type" invisible="1"/>
                </header>
                <div class="oe_button_box" name="button_box"/>
                <div class="oe_title">
                    <widget name="web_ribbon" title="退款" bg_color="bg-danger"
                            attrs="{'invisible': [('red_type','!=', '退款')]}"/>
                </div>
                <group id="g1">
                    <group>
                        <group>
                            <field name="partner_type" invisible="1"/>
                            <field name="partner_id" attrs="{'readonly': [('state', '!=', '草稿')]}"
                                   options="{'no_create': True}"/>
                            <field name="amount"
                                   attrs="{'readonly': ['|', ('state', '!=', '草稿'), ('is_edit', '=', True)]}"/>
                        </group>
                        <group>
                            <field name="order_type" options="{'no_create': True}"/>
                            <field name="payment_method_id" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="origin_order" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        </group>
                        <group>
                            <field name="payment_date" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                            <field name="red_type" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                        </group>
                    </group>
                    <field name="is_edit" invisible="1"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                    <field name="activity_ids"/>
                </div>
            </form>
        </field>
    </record>
    <!--pivot-->
    <record model="ir.ui.view" id="view_roke_mes_payment_pivot">
        <field name="name">roke.mes.payment.pivot</field>
        <field name="model">roke.mes.payment</field>
        <field name="arch" type="xml">
            <pivot string="收付款" display_quantity="True" sample="1">
                <field name="partner_id" type="row"/>
                <field name="state" type="row"/>
                <field name="amount" type="measure"/>
            </pivot>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mes_in_payment_action" model="ir.actions.act_window">
        <field name="name">收款单</field>
        <field name="res_model">roke.mes.payment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[("payment_type", "=", "收款"), ('order_type.value', '=', '收款')]</field>
        <field name="context">{"default_payment_type": "收款", "default_partner_type": "客户", "default_red_type":
            "收款", "default_order_type": 2}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个收/付款。
            </p>
        </field>
    </record>

    <record id="view_roke_mes_in_payment_y_action" model="ir.actions.act_window">
        <field name="name">预收款单</field>
        <field name="res_model">roke.mes.payment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_payment_form"/>
        <field name="domain">[("payment_type", "=", "收款"), ('order_type.value', '=', '预收款')]</field>
        <field name="context">{"default_payment_type": "收款", "default_partner_type": "客户", "default_red_type":
            "收款", "default_order_type": 1}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个预收款单。
            </p>
        </field>
    </record>

    <record id="view_roke_mes_out_payment_action" model="ir.actions.act_window">
        <field name="name">付款单</field>
        <field name="res_model">roke.mes.payment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_pay_form"/>
        <field name="domain">[("payment_type", "=", "付款"), ('order_type.value', '=', '付款')]</field>
        <field name="context">{"default_payment_type": "付款", "default_partner_type": "供应商", "default_red_type":
            "付款", "default_order_type": 4}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个付款单。
            </p>
        </field>
    </record>

    <record id="view_roke_mes_out_payment_y_action" model="ir.actions.act_window">
        <field name="name">预付款单</field>
        <field name="res_model">roke.mes.payment</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_pay_form"/>
        <field name="domain">[("payment_type", "=", "付款"), ('order_type.value', '=', '预付款')]</field>
        <field name="context">{"default_payment_type": "付款", "default_partner_type": "供应商", "default_red_type":
            "付款", "default_order_type": 3}
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个预付款单。
            </p>
        </field>
    </record>

      <record id="roke_mes_payment_delete" model="ir.actions.server">
        <field name="name">删除</field>
        <field name="type">ir.actions.server</field>
        <field name="binding_model_id" ref="roke_mes_account.model_roke_mes_payment"/>
        <field name="model_id" ref="roke_mes_account.model_roke_mes_payment"/>
        <!-- 放入更多插槽 -->
        <field name="button_slot">common</field>
        <field name="sequence">1</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                records.unlink()
        </field>
    </record>

    <!--  收付款方式  -->
    <!--  tree  -->
    <record id="view_roke_mes_payment_method_tree" model="ir.ui.view">
        <field name="name">roke.mes.payment.method.tree</field>
        <field name="model">roke.mes.payment.method</field>
        <field name="arch" type="xml">
            <tree string="收/付款方式">
                <field name="name"/>
                <field name="active"/>
            </tree>
        </field>
    </record>
    <!--  form  -->
    <record id="view_roke_mes_payment_method_form" model="ir.ui.view">
        <field name="name">roke.mes.payment.method.form</field>
        <field name="model">roke.mes.payment.method</field>
        <field name="arch" type="xml">
            <form string="收/付款方式">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!-- search -->
    <record id="view_roke_mes_payment_method_search" model="ir.ui.view">
        <field name="name">roke.mes.payment.method.search</field>
        <field name="model">roke.mes.payment.method</field>
        <field name="arch" type="xml">
            <search string="收/付款方式">
                <field name="name"/>
                <filter string="有效" name="filter_active" domain="[('active', '=', True)]"/>
                <filter string="无效" name="filter_archive" domain="[('active', '!=', True)]"/>
                <filter string="全部" name="filter_all" domain="['|', ('active', '=', True), ('active', '!=', True)]"/>
                <group expand="0" string="Group By">
                    <filter string="有效性" name="group_active" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_roke_mes_payment_method_action" model="ir.actions.act_window">
        <field name="name">收付款方式</field>
        <field name="res_model">roke.mes.payment.method</field>
        <field name="view_mode">tree,form</field>
        <field name="form_view_id" ref="view_roke_mes_payment_method_form"/>
        <field name="type">ir.actions.act_window</field>
        <field name="context">{"search_default_filter_all": True}</field>
    </record>

        <record id="roke_mes_payment_delete" model="ir.actions.server">
        <field name="name">删除</field>
        <field name="type">ir.actions.server</field>
        <field name="binding_model_id" ref="roke_mes_account.model_roke_mes_payment_method"/>
        <field name="model_id" ref="roke_mes_account.model_roke_mes_payment_method"/>
        <!-- 放入更多插槽 -->
        <field name="button_slot">common</field>
        <field name="sequence">1</field>
        <field name="state">code</field>
        <field name="code">
            if records:
                records.unlink()
        </field>
    </record>

</odoo>
