<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--付款阶段类型-->
    <!--search-->
    <record id="view_roke_payment_stage_type_search" model="ir.ui.view">
        <field name="name">roke.payment.stage.type.search</field>
        <field name="model">roke.payment.stage.type</field>
        <field name="arch" type="xml">
            <search string="付款阶段类型">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_payment_stage_type_tree" model="ir.ui.view">
        <field name="name">roke.payment.stage.type.tree</field>
        <field name="model">roke.payment.stage.type</field>
        <field name="arch" type="xml">
            <tree string="付款阶段类型列表">
                <field name="name"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_payment_stage_type_form" model="ir.ui.view">
        <field name="name">roke.payment.stage.type.form</field>
        <field name="model">roke.payment.stage.type</field>
        <field name="arch" type="xml">
            <form>
                <group col="4">
                    <group>
                        <field name="name"/>
                    </group>
                    <group></group>
                    <group></group>
                    <group></group>
                </group>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_payment_stage_type_action" model="ir.actions.act_window">
        <field name="name">付款阶段类型</field>
        <field name="res_model">roke.payment.stage.type</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个付款阶段类型。
            </p>
        </field>
    </record>
    <!--收款阶段类型-->
    <!--search-->
    <record id="view_roke_pay_stage_type_search" model="ir.ui.view">
        <field name="name">roke.pay.stage.type.search</field>
        <field name="model">roke.pay.stage.type</field>
        <field name="arch" type="xml">
            <search string="收款阶段类型">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_pay_stage_type_tree" model="ir.ui.view">
        <field name="name">roke.pay.stage.type.tree</field>
        <field name="model">roke.pay.stage.type</field>
        <field name="arch" type="xml">
            <tree string="收款阶段类型列表">
                <field name="name"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_pay_stage_type_form" model="ir.ui.view">
        <field name="name">roke.pay.stage.type.form</field>
        <field name="model">roke.pay.stage.type</field>
        <field name="arch" type="xml">
            <form>
                <group col="4">
                    <group>
                        <field name="name"/>
                    </group>
                    <group></group>
                    <group></group>
                    <group></group>
                </group>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_pay_stage_type_action" model="ir.actions.act_window">
        <field name="name">收款阶段类型</field>
        <field name="res_model">roke.pay.stage.type</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_pay_stage_type_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个收款阶段类型。
            </p>
        </field>
    </record>
</odoo>