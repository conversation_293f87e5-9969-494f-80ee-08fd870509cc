# -*- coding: utf-8 -*-
{
    'name': '应付-采购、库存桥接',
    'version': '1.2',
    'category': 'mes',
    'depends': ['roke_mes_account', 'roke_mes_purchase'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        应付-采购、库存桥接
    """,
    'data': [
        'security/ir.model.access.csv',
        'data/account_data.xml',
        'data/roke_mes_account_purchase_product.xml',
        'data/roke_mes_account_purchase_supplier.xml',
        'data/roke_mes_account_purchase_salesman.xml',
        'data/query_advance_pay_report.xml',
        'data/query_purchase_report.xml',
        'data/roke_mes_material_cost_statistics.xml',
        'data/query_summary_payable_report.xml',
        'views/roke_purchase_invoice.xml',
        'views/inherit_roke_purchase_views.xml',
        'views/inherit_roke_mes_payment_views.xml',
        'wizard/wizard_purchase_invoice_pay.xml',
        'wizard/wizard_purchase_invoice_verify.xml',
        'wizard/wizard_order_deduct.xml',
        'wizard/wizard_account_payable_views.xml',
        'wizard/inherit_purchase_billing_period_warning_wizard.xml',
        'views/menus.xml',
    ],
    'demo': [
    ],
    'pre_init_hook': 'add_sql_funtion_data',
    'application': True,
    'installable': True,
    'auto_install': False,
}
