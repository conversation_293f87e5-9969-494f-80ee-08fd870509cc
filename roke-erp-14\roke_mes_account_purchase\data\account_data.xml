<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--默认值-类型-默认付款-->
        <record id="default_payment_type_pay" model="roke.app.default">
            <field name="type_name">selection</field>
            <field name="str_field">付款</field>
        </record>

        <!--销售-付款-->
        <record id="mobile_roke_mes_pay" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_pay"/>
            <field name="model_name">付款</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_procure_mark"/>
            <field name="detail_field_id" ref="roke_mes_account.field_roke_mes_pay__payment_line_ids"/>
            <field name="detail_model_id" ref="roke_mes_account.model_roke_mes_pay"/>
            <field name="base_data">False</field>
            <field name="is_prefabrication">True</field>
            <field name="is_batch_add">False</field>
            <field name="default_open_state">默认新增状态</field>
        </record>

        <!--表头字段-->
        <record id="roke_mes_pay_header_field_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__partner_id"/>
        </record>
        <record id="roke_mes_pay_header_field_ids02-1" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_type"/>
            <field name="default_value" ref="default_payment_type_pay"/>
        </record>
        <record id="roke_mes_pay_header_field_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__order_type"/>
            <field name="domain_field">[["code","ilike","付款"]]</field>
            <field name="zdy_field_description">付款类型</field>
        </record>
        <record id="roke_mes_pay_header_field_ids03" model="roke.app.general.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__red_type"/>
        </record>
        <record id="roke_mes_pay_header_field_ids04" model="roke.app.general.order.fields.header">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account_purchase.field_roke_mes_pay__bank_purchase_account_id"/>
        </record>
        <record id="roke_mes_pay_header_field_ids05" model="roke.app.general.order.fields.header">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_method_id"/>
        </record>
        <record id="roke_mes_pay_header_field_ids06" model="roke.app.general.order.fields.header">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__amount"/>
        </record>
        <record id="roke_mes_pay_header_field_ids07" model="roke.app.general.order.fields.header">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_date"/>
        </record>
        <record id="roke_mes_pay_header_field_ids08" model="roke.app.general.order.fields.header">
            <field name="sequence">8</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__origin_order"/>
        </record>

        <!--列表字段-->
        <record id="roke_mes_pay_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__partner_id"/>
            <field name="primary">True</field>
        </record>
        <record id="roke_mes_pay_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_date"/>
        </record>
        <record id="roke_mes_pay_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__order_type"/>
            <field name="zdy_field_description">付款类型</field>
        </record>
        <record id="roke_mes_pay_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__amount"/>
        </record>
        <record id="roke_mes_pay_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_mes_pay"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__state"/>
        </record>

        <!--销售-收款记录-->
        <record id="mobile_roke_mes_pay_line" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_pay"/>
            <field name="model_name">付款记录</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_procure_mark"/>
            <field name="detail_field_id" ref="roke_mes_account.field_roke_mes_pay__payment_line_ids"/>
            <field name="detail_model_id" ref="roke_mes_account.model_roke_mes_pay"/>
            <field name="base_data">False</field>
            <field name="is_prefabrication">True</field>
            <field name="is_batch_add">False</field>
        </record>

        <!--列表字段-->
        <record id="roke_mes_pay_line_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__partner_id"/>
            <field name="primary">True</field>
        </record>
        <record id="roke_mes_pay_line_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_date"/>
        </record>
        <record id="roke_mes_pay_line_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__order_type"/>
            <field name="zdy_field_description">付款类型</field>
        </record>
        <record id="roke_mes_pay_line_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__amount"/>
        </record>
        <record id="roke_mes_pay_line_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__state"/>
        </record>

        <!--筛选字段-->
        <record id="roke_mes_pay_line_list_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__partner_id"/>
        </record>
        <record id="roke_mes_pay_line_list_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_date"/>
        </record>
        <record id="roke_mes_pay_line_list_search_field_ids03" model="roke.app.general.order.fields.search">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__state"/>
        </record>
        <record id="roke_mes_pay_line_list_search_field_ids04" model="roke.app.general.order.fields.search">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__amount"/>
        </record>
        <record id="roke_mes_pay_line_list_search_field_ids05" model="roke.app.general.order.fields.search">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__order_type"/>
            <field name="zdy_field_description">付款类型</field>
        </record>

        <!--表头字段-->
        <record id="roke_mes_pay_line_list_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__partner_id"/>
        </record>
        <record id="roke_mes_pay_line_list_ids02-1" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_type"/>
            <field name="default_value" ref="default_payment_type_pay"/>
        </record>
        <record id="roke_mes_pay_line_list_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__order_type"/>
            <field name="domain_field">[["code","ilike","付款"]]</field>
            <field name="zdy_field_description">付款类型</field>
        </record>
        <record id="roke_mes_pay_line_list_ids03" model="roke.app.general.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__red_type"/>
        </record>
        <record id="roke_mes_pay_line_list_ids04" model="roke.app.general.order.fields.header">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account_purchase.field_roke_mes_pay__bank_purchase_account_id"/>
        </record>
        <record id="roke_mes_pay_line_list_ids05" model="roke.app.general.order.fields.header">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_method_id"/>
        </record>
        <record id="roke_mes_pay_line_list_ids06" model="roke.app.general.order.fields.header">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__amount"/>
        </record>
        <record id="roke_mes_pay_line_list_ids07" model="roke.app.general.order.fields.header">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__payment_date"/>
        </record>
        <record id="roke_mes_pay_line_list_ids08" model="roke.app.general.order.fields.header">
            <field name="sequence">8</field>
            <field name="order_id" ref="mobile_roke_mes_pay_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_pay__origin_order"/>
        </record>
    </data>
</odoo>
