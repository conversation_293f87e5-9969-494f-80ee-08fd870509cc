<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_purchase_order_select_report" model="roke.sql.model.component">
            <field name="name">采购订单跟踪统计表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="sql_statement">
                SELECT roke_purchase_order.order_date AS "单据日期", roke_purchase_order.code AS "单据编号", roke_purchase_order.purchase_type AS "业务类型", roke_partner.name AS "供应商", roke_employee.name AS "业务员", roke_product_category.name AS "产品类别", roke_product.name AS "产品名称", roke_product.specification AS "规格", roke_product.model AS "型号", COALESCE(roke_purchase_order_detail.qty,0) AS "数量", COALESCE(roke_purchase_order_detail.receiving_qty,0) AS "收货数量", roke_uom.name AS "单位", COALESCE(roke_purchase_order_detail.unit_price,0) AS "单价", COALESCE(roke_purchase_order_detail.tax_amount,0) AS "税额", COALESCE(roke_purchase_order_detail.tax_rate,0) AS "税率", COALESCE(roke_purchase_order_detail.subtotal,0) AS "金额", COALESCE(roke_purchase_order_detail.discount_amount,0) AS "折扣额", COALESCE(roke_purchase_order_detail.whole_order_offer,0) AS "整单优惠", (COALESCE(roke_purchase_order_detail.subtotal,0) - COALESCE(roke_purchase_order_detail.discount_amount,0) - COALESCE(roke_purchase_order_detail.whole_order_offer,0)) AS "应付金额", SUM(COALESCE(roke_mes_payment_line.paid_amount,0)) AS "已付金额", (COALESCE(roke_purchase_order_detail.subtotal,0) - COALESCE(roke_purchase_order_detail.discount_amount,0) - COALESCE(roke_purchase_order_detail.whole_order_offer,0) - SUM(COALESCE(roke_mes_payment_line.paid_amount,0))) AS "未付金额", roke_purchase_order.state AS "状态", roke_purchase_order_detail.note AS "备注"
                FROM roke_purchase_order_detail 
                LEFT JOIN roke_purchase_order ON roke_purchase_order_detail.order_id = roke_purchase_order.ID
                LEFT JOIN roke_partner ON roke_purchase_order.supplier_id = roke_partner.ID
                LEFT JOIN roke_product ON roke_purchase_order_detail.product_id = roke_product.ID
                LEFT JOIN roke_product_category ON roke_product.category_id = roke_product_category.ID
                LEFT JOIN roke_employee ON roke_purchase_order.employee_id = roke_employee.ID
                LEFT JOIN roke_uom ON roke_product.uom_id = roke_uom.ID
                LEFT JOIN roke_mes_payment_line ON roke_purchase_order_detail.ID = roke_mes_payment_line.purchase_line_id
                WHERE
                    roke_purchase_order.order_date between :order_date and :order_date
                    AND roke_partner.name = :roke_partner.name
                    AND roke_employee.name = :roke_employee.name
                    AND roke_product.name = :roke_product.name
                    AND roke_product_category.name = :roke_product_category.name
                GROUP BY	roke_product_category.name, roke_product.name, roke_product.specification, roke_product.model, roke_purchase_order.order_date, roke_purchase_order.code, roke_purchase_order.purchase_type, roke_partner.name, roke_employee.name, roke_purchase_order_detail.qty, roke_purchase_order_detail.receiving_qty, roke_uom.name, roke_purchase_order_detail.unit_price, roke_purchase_order_detail.tax_amount, roke_purchase_order_detail.tax_rate, roke_purchase_order_detail.subtotal, roke_purchase_order_detail.discount_amount, roke_purchase_order_detail.whole_order_offer, roke_purchase_order.state, roke_purchase_order_detail.note
            </field>
            <field name="top_menu_id" ref="roke_mes_purchase.roke_mes_purchase_query_root"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '选择日期',
                    'field_id': ref('roke_mes_purchase.field_roke_purchase_order__order_date'),
                    'sql_decider': 'between',
                    'sql_data': ' roke_purchase_order.order_date between :order_date and  :order_date ',
                    'sql_field_mark': ':order_date',
                    'sql_field_mark_type': 'date'
                }),
                (0, 0, {
                    'name': '供应商',
                    'field_id': ref('roke_mes_base.field_roke_partner__name'),
                    'sql_inherit_field_id': ref('roke_mes_purchase.field_roke_purchase_order__supplier_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_partner.name = :roke_partner.name ',
                    'sql_field_mark': ':roke_partner.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '业务员',
                    'field_id': ref('roke_mes_base.field_roke_employee__name'),
                    'sql_inherit_field_id': ref('roke_mes_purchase.field_roke_purchase_order__employee_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_employee.name = :roke_employee.name ',
                    'sql_field_mark': ':roke_employee.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '产品',
                    'field_id': ref('roke_mes_base.field_roke_product__name'),
                    'sql_inherit_field_id': ref('roke_mes_purchase.field_roke_purchase_order_detail__product_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_product.name = :roke_product.name ',
                    'sql_field_mark': ':roke_product.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '产品类别',
                    'field_id': ref('roke_mes_base.field_roke_product_category__name'),
                    'sql_inherit_field_id': ref('roke_mes_base.field_roke_product__category_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_product_category.name = :roke_product_category.name ',
                    'sql_field_mark': ':roke_product_category.name',
                    'sql_field_mark_type': 'many2one'
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__order_date"),
                    "sequence": 1,
                    "sql_order_by_data": "roke_purchase_order.order_date"
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__code"),
                    "sequence": 2,
                    "sql_order_by_data": "roke_purchase_order.code"
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__purchase_type"),
                    "sequence": 3,
                    "sql_order_by_data": "roke_purchase_order.purchase_type"
                }),
                (0, 0, {
                    "name": "供应商",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 4,
                    "sql_order_by_data": "roke_partner.name"
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 5,
                    "sql_order_by_data": "roke_employee.name"
                }),
                (0, 0, {
                    "name": "产品类别",
                    "field_id": ref("roke_mes_base.field_roke_product_category__name"),
                    "sequence": 6,
                    "sql_order_by_data": "roke_product_category.name"
                }),
                (0, 0, {
                    "name": "产品名称",
                    "field_id": ref("roke_mes_base.field_roke_product__name"),
                    "sequence": 7,
                    "sql_order_by_data": "roke_product.name"
                }),
                (0, 0, {
                    "name": "规格",
                    "field_id": ref("roke_mes_base.field_roke_product__specification"),
                    "sequence": 8,
                    "sql_order_by_data": "roke_product.specification"
                }),
                (0, 0, {
                    "name": "型号",
                    "field_id": ref("roke_mes_base.field_roke_product__model"),
                    "sequence": 9,
                    "sql_order_by_data": "roke_product.model"
                }),
                (0, 0, {
                    "name": "数量",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__qty"),
                    "sequence": 10,
                    "sql_order_by_data": "roke_purchase_order_detail.qty"
                }),
                (0, 0, {
                    "name": "收货数量",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__receiving_qty"),
                    "sequence": 11,
                    "sql_order_by_data": "roke_purchase_order_detail.receiving_qty"
                }),
                (0, 0, {
                    "name": "单位",
                    "field_id": ref("roke_mes_base.field_roke_uom__name"),
                    "sequence": 12,
                    "sql_order_by_data": "roke_uom.name"
                }),
                (0, 0, {
                    "name": "单价",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__unit_price"),
                    "sequence": 13,
                    "sql_order_by_data": "roke_purchase_order_detail.unit_price"
                }),
                (0, 0, {
                    "name": "税额",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__tax_amount"),
                    "sequence": 14,
                    "sql_order_by_data": "roke_purchase_order_detail.tax_amount"
                }),
                (0, 0, {
                    "name": "税率",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__tax_rate"),
                    "sequence": 15,
                    "sql_order_by_data": "roke_purchase_order_detail.tax_rate"
                }),
                (0, 0, {
                    "name": "金额",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__subtotal"),
                    "sequence": 16,
                    "sql_order_by_data": "roke_purchase_order_detail.subtotal"
                }),
                (0, 0, {
                    "name": "折扣额",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__discount_amount"),
                    "sequence": 17,
                    "sql_order_by_data": "roke_purchase_order_detail.discount_amount"
                }),
                (0, 0, {
                    "name": "整单优惠",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__whole_order_offer"),
                    "sequence": 18,
                    "sql_order_by_data": "roke_purchase_order_detail.whole_order_offer"
                }),
                (0, 0, {
                    "name": "应付金额",
                    "sequence": 19,
                    "sql_data": "(roke_purchase_order_detail.subtotal - roke_purchase_order_detail.discount_amount - roke_purchase_order_detail.whole_order_offer) AS 应付金额",
                    "sql_order_by_data": "(roke_purchase_order_detail.subtotal - roke_purchase_order_detail.discount_amount - roke_purchase_order_detail.whole_order_offer)"
                }),
                (0, 0, {
                    "name": "已付金额",
                    "sequence": 20,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_mes_payment_line.paid_amount) AS 已付金额",
                    "sql_order_by_data": "SUM(roke_mes_payment_line.paid_amount)"
                }),
                (0, 0, {
                    "name": "未付金额",
                    "sequence": 21,
                    "summary_method": "SUM",
                    "sql_data": "(roke_purchase_order_detail.subtotal - roke_purchase_order_detail.discount_amount - roke_purchase_order_detail.whole_order_offer - SUM(roke_mes_payment_line.paid_amount)) AS 未付金额",
                    "sql_order_by_data": "(roke_purchase_order_detail.subtotal - roke_purchase_order_detail.discount_amount - roke_purchase_order_detail.whole_order_offer - SUM(roke_mes_payment_line.paid_amount))"
                }),
                (0, 0, {
                    "name": "状态",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__state"),
                    "sequence": 22,
                    "sql_order_by_data": "roke_purchase_order.state"
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__note"),
                    "sequence": 23,
                    "sql_order_by_data": "roke_purchase_order_detail.note"
                })
            ]'/>
            <field name="sql_group_way" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "产品类别",
                    "field_id": ref("roke_mes_base.field_roke_product_category__name"),
                    "sequence": 1
                }),
                (0, 0, {
                    "name": "产品名称",
                    "field_id": ref("roke_mes_base.field_roke_product__name"),
                    "sequence": 2
                }),
                (0, 0, {
                    "name": "规格",
                    "field_id": ref("roke_mes_base.field_roke_product__specification"),
                    "sequence": 3
                }),
                (0, 0, {
                    "name": "型号",
                    "field_id": ref("roke_mes_base.field_roke_product__model"),
                    "sequence": 4
                }),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__order_date"),
                    "sequence": 5
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__code"),
                    "sequence": 6
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__purchase_type"),
                    "sequence": 7
                }),
                (0, 0, {
                    "name": "供应商",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 8
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 9
                }),
                (0, 0, {
                    "name": "数量",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__qty"),
                    "sequence": 10
                }),
                (0, 0, {
                    "name": "收货数量",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__receiving_qty"),
                    "sequence": 11
                }),
                (0, 0, {
                    "name": "单位",
                    "field_id": ref("roke_mes_base.field_roke_uom__name"),
                    "sequence": 12
                }),
                (0, 0, {
                    "name": "单价",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__unit_price"),
                    "sequence": 13
                }),
                (0, 0, {
                    "name": "税额",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__tax_amount"),
                    "sequence": 14
                }),
                (0, 0, {
                    "name": "税率",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__tax_rate"),
                    "sequence": 15
                }),
                (0, 0, {
                    "name": "金额",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__subtotal"),
                    "sequence": 16
                }),
                (0, 0, {
                    "name": "折扣额",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__discount_amount"),
                    "sequence": 17
                }),
                (0, 0, {
                    "name": "整单优惠",
                    "field_id": ref("roke_mes_account_purchase.field_roke_purchase_order_detail__whole_order_offer"),
                    "sequence": 18
                }),
                (0, 0, {
                    "name": "状态",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order__state"),
                    "sequence": 19
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_purchase.field_roke_purchase_order_detail__note"),
                    "sequence": 20
                })
            ]'/>
        </record>
    </data>
</odoo>
