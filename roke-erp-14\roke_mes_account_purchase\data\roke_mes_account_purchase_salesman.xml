<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_mes_purchase.roke_query_component_model_purchase_salesman" model="roke.query.component.model">
            <field name="show_columns" eval="[
                (0, 0, {'name': '不含税金额', 'summary_method': 'SUM',
                 'field_id': ref('roke_mes_account_purchase.field_roke_purchase_order_detail__amount_excl_tax'), 'sequence': 12}),
                (0, 0, {'name': '折扣额', 'summary_method': 'SUM',
                 'field_id': ref('roke_mes_account_purchase.field_roke_purchase_order_detail__discount_amount'), 'sequence': 13}),
                (0, 0, {'name': '税额', 'summary_method': 'SUM',
                 'field_id': ref('roke_mes_account_purchase.field_roke_purchase_order_detail__tax_amount'), 'sequence': 14})
            ]"/>
        </record>
    </data>
</odoo>