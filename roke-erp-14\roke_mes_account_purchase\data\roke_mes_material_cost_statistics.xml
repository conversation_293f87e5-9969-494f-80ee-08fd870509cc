<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_mes_material_cost_statistics" model="roke.sql.model.component">
            <field name="name">材料成本统计</field>
            <field name="sql_statement">SELECT
    roke_purchase_order.order_date as "日期",
    roke_product.name as "产品名称",
    roke_purchase_order_detail.qty as "数量",
    roke_purchase_order_detail.unit_price as "单价",
    roke_purchase_order_detail.subtotal as "金额",
    (roke_purchase_order_detail.subtotal - COALESCE(roke_purchase_order_detail.discount_amount,0)) as "折扣后金额",
    COALESCE(roke_purchase_order_detail.whole_order_offer,0) as "整单优惠"
FROM
    roke_purchase_order
    LEFT JOIN roke_purchase_order_detail ON roke_purchase_order.id = roke_purchase_order_detail.order_id
    LEFT JOIN roke_product ON roke_purchase_order_detail.product_id = roke_product.id
WHERE
    roke_purchase_order.order_date BETWEEN :order_date AND :order_date
    and roke_product.name is not null</field>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {'name': '订单日期', 'field_id': ref('roke_mes_purchase.field_roke_purchase_order__order_date'),
                    'sql_data': '  roke_purchase_order.order_date BETWEEN :order_date and  :order_date  ',
                    'sql_field_mark': ':order_date', 'sql_field_mark_type': 'date', 'sql_decider': 'between'}),
                (0, 0, {'name': '名称', 'field_id': ref('roke_mes_base.field_roke_product__name'),
                    'field_default': 'null', 'sql_decider': 'is not'})
            ]"/>
            <field name="sql_show_columns" eval="[(5, 0, 0),
                (0, 0, {'name': '日期', 'field_id': ref('roke_mes_purchase.field_roke_purchase_order__order_date'),
                    'sql_order_by_data': 'roke_purchase_order.order_date', 'sequence': 1}),
                (0, 0, {'name': '产品名称', 'field_id': ref('roke_mes_base.field_roke_product__name'),
                    'sql_order_by_data': 'roke_product.name','sequence': 2}),
                (0, 0, {'name': '数量', 'field_id': ref('roke_mes_purchase.field_roke_purchase_order_detail__qty'),
                    'sql_order_by_data': 'roke_purchase_order_detail.qty', 'sequence': 3}),
                (0, 0, {'name': '单价', 'field_id': ref('roke_mes_purchase.field_roke_purchase_order_detail__unit_price'),
                    'sql_order_by_data': 'roke_purchase_order_detail.unit_price', 'sequence': 4}),
                (0, 0, {'name': '金额', 'field_id': ref('roke_mes_purchase.field_roke_purchase_order_detail__subtotal'),
                    'sql_order_by_data': 'roke_purchase_order_detail.subtotal', 'sequence': 5}),
                (0, 0, {'name': '折扣后金额', 'sql_data': '(roke_purchase_order_detail.subtotal - COALESCE(roke_purchase_order_detail.discount_amount,0))',
                    'sql_order_by_data': '(roke_purchase_order_detail.subtotal - COALESCE(roke_purchase_order_detail.discount_amount,0)))', 'sequence': 6}),
                (0, 0, {'name': '整单优惠', 'sql_data': 'COALESCE(roke_purchase_order_detail.whole_order_offer,0)',
                    'sql_order_by_data': 'COALESCE(roke_purchase_order_detail.whole_order_offer,0)', 'sequence': 7}),
            ]"/>
            <field name="top_menu_id" ref="roke_mes_purchase.roke_mes_purchase_query_root"/>
        </record>
    </data>
</odoo>