from odoo import api, SUPERUSER_ID
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
	env = api.Environment(cr, SUPERUSER_ID, {})
	env.cr.execute('''
    CREATE
    	OR REPLACE FUNCTION "public"."summary_payable" ( "start_dt" DATE, "end_dt" DATE, "employee" VARCHAR ) RETURNS TABLE (
    		"供应商编号" VARCHAR,
    		"供应商名称" VARCHAR,
    		"期初余额" NUMERIC,
    		"本期应付" NUMERIC,
    		"已付款" NUMERIC,
    		"期末余额" NUMERIC
    		) AS $BODY$ BEGIN
    		CREATE TEMPORARY TABLE temp_table1 (
    			rp_c VARCHAR,
    			rp_n VARCHAR,
    			qcye NUMERIC,
    			bqys NUMERIC,
    			ysk NUMERIC,
    			qmye NUMERIC
    		);
    	INSERT INTO temp_table1 (
    		rp_c,
    		rp_n,
    		qcye,
    		bqys,
    		ysk,
    		qmye
    		) (
    		SELECT
    			current_period.rp_c AS 供应商编号,
    			current_period.rp_n AS 供应商名称,
    			COALESCE ( beginning_period.qcyf, 0 ) AS 期初余额,
    			COALESCE ( current_period.bqyf, 0 ) AS 本期应付,
    			COALESCE ( current_period.bqyif, 0 ) AS 已付款,
    			SUM (
    				COALESCE ( beginning_period.qcyf, 0 ) + COALESCE ( current_period.bqyf, 0 ) - COALESCE ( current_period.bqyif, 0 )
    			) AS 期末余额
    		FROM
    			(
    				(
    				SELECT
    					roke_partner.code AS rp_c,
    					roke_partner.ID AS rp_i,
    					roke_partner.NAME AS rp_n,
    					SUM (
    						COALESCE ( roke_purchase_order_detail.subtotal, 0 ) - COALESCE ( roke_purchase_order_detail.discount_amount, 0 ) - COALESCE ( roke_purchase_order_detail.whole_order_offer, 0 )
    					) AS bqyf,
    					SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS bqyif
    				FROM
    					roke_purchase_order_detail
    					LEFT JOIN roke_purchase_order ON roke_purchase_order_detail.order_id = roke_purchase_order.
    					ID LEFT JOIN roke_mes_payment_line ON roke_purchase_order_detail.ID = roke_mes_payment_line.purchase_line_id
    					LEFT JOIN roke_partner ON roke_purchase_order.supplier_id = roke_partner.ID
    				WHERE
    					roke_purchase_order.supplier_id IS NOT NULL
    					AND roke_purchase_order.order_date BETWEEN start_dt
    					AND end_dt
    				AND
    				CASE

    						WHEN employee IS NOT NULL THEN
    						roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL
    					END
    					GROUP BY
    						GROUPING SETS ( roke_partner.ID )
    					) AS current_period
    					LEFT JOIN (
    					SELECT
    						roke_partner.ID AS rp_i,
    						SUM (
    							COALESCE ( roke_purchase_order_detail.subtotal, 0 ) - COALESCE ( roke_purchase_order_detail.discount_amount, 0 ) - COALESCE ( roke_purchase_order_detail.whole_order_offer, 0 )
    						) AS qcyf,
    						SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS qcyif
    					FROM
    						roke_purchase_order_detail
    						LEFT JOIN roke_purchase_order ON roke_purchase_order_detail.order_id = roke_purchase_order.
    						ID LEFT JOIN roke_mes_payment_line ON roke_purchase_order_detail.ID = roke_mes_payment_line.purchase_line_id
    						LEFT JOIN roke_partner ON roke_purchase_order.supplier_id = roke_partner.ID
    					WHERE
    						roke_purchase_order.supplier_id IS NOT NULL
    						AND roke_purchase_order.order_date < start_dt
    					AND
    					CASE

    							WHEN employee IS NOT NULL THEN
    							roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL
    						END
    						GROUP BY
    							GROUPING SETS ( roke_partner.ID )
    						) AS beginning_period ON current_period.rp_i = beginning_period.rp_i
    					)
    				GROUP BY
    					current_period.rp_c,
    					current_period.rp_n,
    					beginning_period.qcyf,
    					current_period.bqyf,
    					current_period.bqyif
    				);
    -- Routine body goes here...
    			RETURN QUERY SELECT
    			( temp_table1.rp_c ) AS 供应商编号,
    			( temp_table1.rp_n ) AS 供应商名称,
    			COALESCE ( temp_table1.qcye, 0 ) AS 期初余额,
    			COALESCE ( temp_table1.bqys, 0 ) AS 本期应付,
    			COALESCE ( temp_table1.ysk, 0 ) AS 已付款,
    			COALESCE ( temp_table1.qmye, 0 ) AS 期末余额
    			FROM
    				temp_table1;
    			DROP TABLE
    			IF
    				EXISTS temp_table1;

    END $BODY$ LANGUAGE plpgsql VOLATILE COST 100 ROWS 1000
    	''')
