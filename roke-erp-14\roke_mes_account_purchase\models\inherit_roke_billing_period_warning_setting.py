from ast import literal_eval
from odoo import models, fields, api, _
import time
import datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError


class InheritRokeBillingPeriodWarningSetting(models.Model):
	_inherit = "roke.billing.period.warning.setting"
	_description = "账期预警配置"
	_rec_name = 'period_type'

	def action_view_period_warning(self):
		purchase_billing_period_warning_obj = self.env['purchase.billing.period.warning.wizard']
		purchase_obj = self.env['roke.purchase.order']
		purchase_billing_period_warning_unlink_records = purchase_billing_period_warning_obj.search([])
		delete_sql = """DELETE FROM purchase_billing_period_warning_wizard;"""
		self._cr.execute(delete_sql)
		# 将符合配置的采购订单大致拿出来
		purchase_billing_period_warning_vals = []
		for rec in self.line_ids:
			from_day = fields.Date.today() + datetime.timedelta(days=rec.day_from - 1)
			to_day = fields.Date.today() + datetime.timedelta(days=rec.day_to - 1)
			purchase_records = purchase_obj.sudo().search(
				[('state', '!=', '取消'), ('pay_state', 'in', ['部分付款', '未付款']),
				 ('payment_plan_ids', '!=', False)])
			# 组装数据
			for purchase in purchase_records:
				# 已付款金额
				amount_paid = purchase.amount_paid
				payment_plan_list, payment_plan_dict = self._get_estimated_payment_list(purchase, amount_paid)
				for line in purchase.payment_plan_ids:
					if line.id in payment_plan_list and line.estimated_payment_date >= from_day and line.estimated_payment_date <= to_day:
						purchase_billing_period_warning_vals.append({
							'purchase_id': purchase.id,
							'period_line_id': rec.id,
							'estimated_payment_date': line.estimated_payment_date,
							'total_money': line.paid_amount,
							'amount_paid': line.paid_amount - payment_plan_dict.get(line.id, 0.0),
							'amount_unpaid': payment_plan_dict.get(line.id, 0.0)
						})
		for val in purchase_billing_period_warning_vals:
			self._cr.execute(
				'INSERT INTO purchase_billing_period_warning_wizard (purchase_id, period_line_id, estimated_payment_date,total_money, amount_paid, amount_unpaid) values (%s, %s, %s, %s, %s, %s)',
				(val.get('purchase_id', False), val.get('period_line_id', False),
				 val.get('estimated_payment_date', False), val.get('total_money', 0), val.get('amount_paid', 0),
				 val.get('amount_unpaid', 0)))
		return {
			'type': 'ir.actions.act_window',
			'views': [(self.env.ref('roke_mes_purchase.view_purchase_billing_period_warning_wizard_tree').id, 'tree')],
			'view_mode': 'tree',
			'name': '账期预警',
			'target': 'current',
			'res_model': 'purchase.billing.period.warning.wizard',
			'context': {'search_default_group_period_line_id': True, 'create': False}
		}

	def _get_estimated_payment_list(self, purchase, amount_paid):
		"""
			params = {
			purchase: 采购订单
			paid_amount: 已付款金额
			}
        """
		pay_amount = 0
		payment_plan_list = []
		payment_plan_dict = {}
		# 将付款计划按时间排序
		payment_plan_obj = self.env['roke.mes.purchase.payment.plan']
		payment_plan_records = payment_plan_obj.search([('purchase_id', '=', purchase.id)],
													   order='estimated_payment_date')
		for rec in payment_plan_records:
			pay_amount += rec.paid_amount
			if amount_paid < pay_amount:
				payment_plan_list.append(rec.id)
				if amount_paid > (pay_amount - rec.paid_amount):
					payment_plan_dict[rec.id] = pay_amount - amount_paid
				else:
					payment_plan_dict[rec.id] = rec.paid_amount
		return payment_plan_list, payment_plan_dict
