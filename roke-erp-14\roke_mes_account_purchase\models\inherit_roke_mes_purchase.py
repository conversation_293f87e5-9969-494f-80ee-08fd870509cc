# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import datetime


class InheritRokePurchaseOrder(models.Model):
    _inherit = "roke.purchase.order"

    def _get_is_open_tax(self):
        return self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)

    # payment_ids = fields.One2many("roke.mes.payment", "purchase_order_id", string="付款单")
    payment_ids = fields.Many2many(
        "roke.mes.payment", "roke_purchase_payment_rel", "po_id", "pay_id", string="收款单"
    )
    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('优惠金额', digits='CGJE')
    amount_after_discount = fields.Float('优惠后金额', digits='CGJE', compute='_compute_discount_amount_total',
                                         store=True)
    is_open_tax = fields.<PERSON><PERSON><PERSON>('启用税率', compute='_compute_is_open_tax', default=_get_is_open_tax)
    payment_count = fields.Integer(string="付款单数量", compute="_compute_payment_count")
    pay_state = fields.Selection([('已付款', '已付款'), ('部分付款', '部分付款'), ('未付款', '未付款')],
                                 default='未付款', string='付款状态', compute='_compute_pay_state')
    payment_plan_ids = fields.One2many('roke.mes.purchase.payment.plan', 'purchase_id', string="付款计划")
    amount_paid = fields.Float('已付金额', digits='CGJE')
    amount_unpaid = fields.Float('未付金额', compute='_compute_amount_unpaid', digits='CGJE')
    discount_amount_total = fields.Float(string='折扣后金额合计', digits='CGJE',
                                         compute='_compute_discount_amount_total')

    @api.depends('detail_ids', 'detail_ids.after_discount_amount', 'detail_ids.qty', 'detail_ids.unit_price',
                 'discount_amount', 'detail_ids.discount_rate', 'detail_ids.discount_amount',
                 'discount_rate')
    def _compute_discount_amount_total(self):
        for rec in self:
            after_discount_amount = sum(rec.detail_ids.mapped('after_discount_amount'))
            rec.discount_amount_total = after_discount_amount
            rec.amount_after_discount = after_discount_amount - rec.discount_amount

    @api.constrains('payment_plan_ids')
    def _check_payment_plan(self):
        for rec in self:
            date_list = [line.estimated_payment_date for line in rec.payment_plan_ids]
            set_date_list = list(set(date_list)) if date_list else date_list
            if len(date_list) != len(set_date_list):
                raise ValidationError("付款计划中预计付款日期不能相同")

    def _compute_amount_unpaid(self):
        for record in self:
            record.amount_unpaid = 0
            if record.amount_after_discount:
                record.amount_unpaid = record.amount_after_discount - record.amount_unpaid or 0
            else:
                record.amount_unpaid = record.total_money - record.amount_unpaid or 0

    def _compute_pay_state(self):
        for record in self:
            if record.amount_after_discount:
                if record.amount_paid >= record.amount_after_discount:
                    record.pay_state = '已付款'
                elif record.amount_paid < record.amount_after_discount and record.amount_paid != 0:
                    record.pay_state = '部分付款'
                else:
                    record.pay_state = '未付款'
            else:
                if record.amount_paid >= record.total_money:
                    record.pay_state = '已付款'
                elif record.amount_paid < record.total_money and record.amount_paid != 0:
                    record.pay_state = '部分付款'
                else:
                    record.pay_state = '未付款'


    def action_multi_order_deduct(self):
        """
        批量订单优惠
        :return:
        """
        active_order_ids = self.env["roke.purchase.order"].search([
            ("id", "in", self._context.get('active_ids', [])), ("state", "!=", '取消')
        ])
        if '退货' in active_order_ids.mapped("purchase_type"):
            raise ValidationError("退货业务无法进行批量付款，请手动创建")
        # if len(active_order_ids.mapped("supplier_id").ids) > 1:
        #     raise ValidationError("请选择同一供应商的采购订单进行批量付款")
        deduct_line_ids = self.prepare_line_ids(active_order_ids.detail_ids)
        return self.action_wizard_order_deduct(deduct_line_ids)

    def _compute_payment_count(self):
        for record in self:
            record.payment_count = len(record.payment_ids)

    def action_view_payment(self):
        result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_account.view_roke_mes_pay_action')
        result['context'] = {'default_partner_id': self.supplier_id.id}
        payment_ids = self.mapped('payment_ids')
        result_ids = self.env["roke.mes.pay"].search([("payment_id", "in", payment_ids.ids)])
        if not result_ids or len(result_ids) > 1:
            result['domain'] = "[('id','in',%s)]" % (result_ids.ids)
        elif len(result_ids) == 1:
            res = self.env.ref('roke_mes_account.view_roke_mes_pay_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = result_ids.id
        return result

    @api.onchange('discount_amount')
    def _onchange_amount(self):
        if not self.env.context.get('calculate_discount'):
            after_discount_amount = sum(self.detail_ids.mapped('after_discount_amount'))
            if after_discount_amount:
                self.discount_rate = round(self.discount_amount / after_discount_amount, 6)
            else:
                raise ValidationError('折扣后金额总计为0，无法继续优惠！')
            # 处理超过优惠金额
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('discount_rate')
    def _onchange_discount(self):
        if not self.env.context.get('calculate_discount'):
            after_discount_amount = sum(self.detail_ids.mapped('after_discount_amount'))
            self.discount_amount = after_discount_amount * self.discount_rate
            # 处理超过优惠金额
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('detail_ids', 'detail_ids.after_discount_amount', 'detail_ids.qty', 'detail_ids.unit_price',
                  'discount_amount', 'detail_ids.discount_rate', 'detail_ids.discount_amount',
                  'discount_rate'
                  )
    def _onchange_account_discount(self):
        # 计算方式：（折扣后金额合计-优惠金额）*该明细折扣后金额/折扣后总金额=整单优惠
        for line in self.detail_ids:
            if self.discount_amount_total:
                whole_order_offer = self.discount_amount * (
                        line.after_discount_amount / self.discount_amount_total)
            else:
                whole_order_offer = 0
            line.whole_order_offer = whole_order_offer

    def _compute_is_open_tax(self):
        # 税率是否启用
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        for record in self:
            record.is_open_tax = is_tax

    @staticmethod
    def prepare_line_ids(detail_ids):
        """
        归集明细行数据
        :param detail_ids:
        :return:
        """
        deduct_line_ids = []
        for line in detail_ids:
            if line.deducted_amount + line.paid_amount != line.subtotal:
                deduct_line_ids.append((0, 0, {
                    "order_line_id": line.id, "unpaid_amount": line.amount_receivable - line.paid_amount,
                    "pay_amount": line.amount_receivable,
                    "current_paid_amount": line.amount_receivable - line.paid_amount,
                    "deduct_amount": line.discount_amount + line.whole_order_offer
                }))
        return deduct_line_ids

    def action_wizard_order_deduct(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        """
        wizard_id = self.env["wizard.purchase.order.deduct"].create({
            "discount_rate": discount_rate,
            "discount_amount": discount_amount,
            "amount_after_discount": amount_after_discount,
            "deduct_line_ids": deduct_line_ids
        })
        if len(self._context.get('active_ids', [])) > 1:
            return {
                'name': '批量付款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.purchase.order.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_purchase.view_wizard_purchase_order_deduct_batch_form').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }
        else:
            return {
                'name': '付款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.purchase.order.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_purchase.view_wizard_purchase_order_deduct_form').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }

    def action_order_deduct(self):
        """
        订单优惠
        :return:
        """
        if self.purchase_type == '退货':
            raise ValidationError('退货业务请手动操作')
        deduct_line_ids = self.prepare_line_ids(self.detail_ids)
        return self.action_wizard_order_deduct(deduct_line_ids, self.discount_rate, self.discount_amount,
                                               self.amount_after_discount)

    def app_action_payment(self):
        """
        app创建付款单
        :return:
        """
        deduct_line_ids = self.prepare_line_ids(self.detail_ids)
        self.env["wizard.purchase.order.deduct"].create({
            "discount_rate": self.discount_rate,
            "discount_amount": self.discount_amount,
            "amount_after_discount": self.amount_after_discount,
            "deduct_line_ids": deduct_line_ids
        }).app_action_confirm_deduct(self.id)

    # def add_sql_function(self):


class InheritRokePurchaseOrderDetail(models.Model):
    _inherit = "roke.purchase.order.detail"

    payment_line_ids = fields.One2many("roke.mes.payment.line", "purchase_line_id", string="付款明细")
    deducted_amount = fields.Float(string="已优惠金额", digits='CGJE', compute="_compute_pay_amount")
    paid_amount = fields.Float(string="已付金额", digits='CGJE', compute="_compute_pay_amount", store=True)
    # 税率相关
    tax_rate = fields.Float('税率')
    unit_price_excl_tax = fields.Float('不含税单价', digits='CGDJ')
    amount_excl_tax = fields.Float('不含税金额', digits='CGJE', compute='_compute_amount_excl_tax', readonly=True)
    tax_amount = fields.Float('税额', digits='CGJE', compute='_compute_amount_excl_tax', readonly=True)
    discount_rate = fields.Float('折扣率')
    discount_amount = fields.Float('折扣额', digits='CGJE')
    after_discount_amount = fields.Float('折扣后金额', digits='CGJE', compute='_compute_after_discount_amount')
    whole_order_offer = fields.Float('整单优惠', digits='CGJE')
    amount_receivable = fields.Float('应付金额', digits='CGJE', compute='_compute_after_discount_amount')

    @api.onchange('product_id')
    def _onchange_tax_rate(self):
        if self.order_id.is_open_tax:
            self.tax_rate = self.product_id.tax_rate
        else:
            self.tax_rate = 0

    @api.onchange('unit_price_excl_tax')
    def _onchange_tax_rate_next(self):
        tax_rate = (self.unit_price - self.unit_price_excl_tax) / self.unit_price * 100 if self.unit_price and self.unit_price > 0 else 0
        if self.unit_price_excl_tax > self.unit_price:
            raise ValidationError('不含税单价大于产品单价!')
        if self.tax_rate != tax_rate:
            self.tax_rate = tax_rate

    @api.onchange('unit_price', 'tax_rate')
    def _onchange_unit_price_excl_tax(self):
        if self.tax_rate > 100:
            raise ValidationError('税率禁止大于100!')
        if self.tax_rate < 0:
            raise ValidationError('税率禁止为负数!')
        if self.order_id.is_open_tax:
            unit_price_excl_tax = self.unit_price - self.unit_price * self.tax_rate / 100
            self.unit_price_excl_tax = unit_price_excl_tax
        else:
            self.unit_price_excl_tax = 0

    @api.depends('unit_price_excl_tax', 'qty', 'amount_excl_tax', 'tax_amount')
    def _compute_amount_excl_tax(self):
        for rec in self:
            if rec.order_id.is_open_tax:
                rec.amount_excl_tax = rec.unit_price_excl_tax * rec.qty
                if rec.tax_rate:
                    rec.tax_amount = (rec.unit_price - rec.unit_price_excl_tax) * rec.qty
                else:
                    rec.tax_amount = 0
            else:
                rec.amount_excl_tax = 0
                rec.tax_amount = 0

    @api.onchange('discount_amount')
    def _onchange_discount_amount(self):
        self.discount_rate = (self.discount_amount / self.subtotal) * 100 if self.subtotal and self.subtotal > 0 else 0

    @api.onchange('discount_rate')
    def _onchange_discount_rate(self):
        self.discount_amount = self.subtotal * self.discount_rate / 100

    @api.onchange('unit_price', 'qty')
    def _onchange_account_qty_price(self):
        subtotal = self.unit_price * self.qty
        self.discount_amount = subtotal * self.discount_rate / 100

    @api.depends('subtotal', 'discount_amount', 'whole_order_offer')
    def _compute_after_discount_amount(self):
        """
        计算方式：折扣后金额=金额-折扣额-整单优惠
        当折扣率为0时，该字段默认不进行折扣，折扣后金额取小计字段数据
        """
        for rec in self:
            if rec.discount_rate > 0:
                rec.after_discount_amount = rec.subtotal - rec.discount_amount
                rec.amount_receivable = rec.subtotal - rec.discount_amount - rec.whole_order_offer
            else:
                rec.after_discount_amount = rec.subtotal
                rec.amount_receivable = rec.subtotal - rec.whole_order_offer

    @api.depends("payment_line_ids", "payment_line_ids.deducted_amount", "payment_line_ids.paid_amount")
    def _compute_pay_amount(self):
        for record in self:
            # active_payment_line_ids = record.payment_line_ids.filtered(lambda pl: pl.payment_id.state == "已过账")
            active_payment_line_ids = record.payment_line_ids
            record.deducted_amount = sum(active_payment_line_ids.mapped("deducted_amount"))
            record.paid_amount = sum(active_payment_line_ids.mapped("paid_amount"))


class InheritRokeMesPayment(models.Model):
    _inherit = 'roke.mes.payment'

    purchase_order_id = fields.Many2one("roke.purchase.order", string="采购订单")
    purchase_invoice_ids = fields.One2many('roke.purchase.invoice.verify', 'payment_id', string='核销记录')
    bank_purchase_account_id = fields.Many2one(related="purchase_order_id.bank_account_id", string="客户账户")

    @api.model
    def create(self, vals):
        res = super(InheritRokeMesPayment, self).create(vals)
        if vals.get("purchase_order_id", False):
            self.env['roke.purchase.order'].browse(vals.get("purchase_order_id", False)).write(
                {"payment_ids": [(4, res.id)]})
        return res

    # @api.model
    # def create(self, vals):
    #     if vals.get("payment_type") == "收款":
    #         sequence_code = "roke.mes.payment.receive.code"
    #     else:
    #         sequence_code = "roke.mes.payment.pay.code"
    #     vals["code"] = self.env['ir.sequence'].next_by_code(sequence_code)
    #     # 处理采购明细付款
    #     if self.env.context.get("pay_line_ids", []):
    #         vals["payment_line_ids"] = self.env.context.get("pay_line_ids", [])
    #     res = super(InheritRokeMesPayment, self).create(vals)
    #     # 关联发票付款、创建发票核销关系
    #     self.handle_invoice_and_payment(res)
    #     # 如果有采购订单，关联到采购订单上
    #     if vals.get("purchase_order_id", False):
    #         self.env['roke.purchase.order'].browse(vals.get("purchase_order_id", False)).write(
    #             {"payment_ids": [(4, res.id)]})
    #     return res

    def confirm(self):
        """
        确认收付款，创建记账明细
        :return:
        """
        # 收款单过账
        self.make_confirm()
        # 向关联销售单发送
        self.post_message_to_sale_order()

    def post_message_to_sale_order(self):
        # self.sale_order_id.message_post_with_view(
        #     'roke_mes_operation.roke_mes_backorder_message_origin_link',
        #     values={'self': self.sale_order_id, 'origin': self},
        #     subtype_id=self.env.ref('mail.mt_note').id
        # )
        pass

    def unlink(self):
        for v in self:
            if v.purchase_order_id:
                if v.purchase_order_id.amount_paid >= v.amount:
                    v.purchase_order_id.write({"amount_paid": v.purchase_order_id.amount_paid - v.amount})

        return super(InheritRokeMesPayment, self).unlink()


class InheritRokeMesPaymentLine(models.Model):
    _inherit = 'roke.mes.payment.line'

    purchase_line_id = fields.Many2one("roke.purchase.order.detail", string="采购明细")


class InheritRokeMesAccountMove(models.Model):
    _inherit = "roke.mes.account.move"

    purchase_id = fields.Many2one("roke.purchase.order", string="采购订单", index=True, ondelete='cascade')


class RokeMesPurchasePaymentPlan(models.Model):
    _name = "roke.mes.purchase.payment.plan"

    purchase_id = fields.Many2one("roke.purchase.order", string="采购订单", index=True, ondelete='cascade')
    payment_stage_type_id = fields.Many2one("roke.payment.stage.type", string="付款阶段")
    paid_amount = fields.Float(string="付款金额", digits='Account')
    estimated_payment_date = fields.Date(string="预计付款日期")

    @api.constrains('paid_amount', 'estimated_payment_date')
    def _check_paid_amount(self):
        for rec in self:
            if rec.paid_amount and rec.paid_amount < 0:
                raise ValidationError('付款金额不能小于0')
            if rec.estimated_payment_date and rec.estimated_payment_date < datetime.date.today():
                raise ValidationError('预计付款日期不能早于今天')
