# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class RokePurchaseInvoice(models.Model):
    _name = "roke.purchase.invoice"
    _inherit = ['mail.thread', 'mail.activity.mixin','roke.order.print.mixin']
    _description = "采购发票"
    _order = 'invoice_date desc'
    _rec_name = "code"

    order_id = fields.Many2one("roke.purchase.order", string="采购订单", ondelete='cascade')
    code = fields.Char(string="发票编号", required=True, index=True, tracking=True, copy=False, default="/")
    state = fields.Selection([
        ('草稿', '草稿'),
        ('确认', '确认'),
        ('取消', '取消')
    ], string="状态", required=True, default='草稿')
    supplier_id = fields.Many2one(
        "roke.partner", string="供应商", ondelete="restrict", domain=[("supplier", "=", True)]
    )
    payment_reference = fields.Char(string='付款参考', index=True, copy=False)
    invoice_date = fields.Date(
        string='结算日期', index=True, readonly=True, copy=False, states={'草稿': [('readonly', False)]})
    invoice_date_due = fields.Date(
        string='到期日期', index=True, readonly=True, copy=False, states={'草稿': [('readonly', False)]})
    invoice_line_ids = fields.One2many(
        "roke.purchase.invoice.line", "invoice_id", readonly=True, string="发票明细",
        states={'草稿': [('readonly', False)]}
    )
    payment_ids = fields.Many2many(
        'roke.mes.payment',
        'roke_purchase_invoice_payment_rel',
        'invoice_id', 'payment_id',
        string='发票付款单', readonly=True, copy=False
    )

    amount_total = fields.Float(string='总金额', store=True, readonly=True, digits='YSYFJE', compute='_compute_amount')
    amount_paid = fields.Float(string="已付", required=True, digits='YSYFJE', compute='_compute_amount')
    amount_unpaid = fields.Float(string='未付金额', store=True, digits='YSYFJE', compute='_compute_amount')

    verify_ids = fields.One2many("roke.purchase.invoice.verify", "invoice_id", string="核销明细")
    verify_count = fields.Integer(string="核销明细数量", compute="_compute_verify_count")
    is_show_invoice_pay = fields.Boolean(string="是否可以付款", compute="_compute_is_show_invoice_pay")

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def _get_is_open_tax(self):
        return self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)

    is_open_tax = fields.Boolean('启用税率', compute='_compute_is_open_tax', default=_get_is_open_tax)

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)',
         '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def _compute_is_open_tax(self):
        # 税率是否启用
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        for record in self:
            record.is_open_tax = is_tax

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.purchase.invoice.code')
        return super(RokePurchaseInvoice, self).create(vals)

    def unlink(self):
        if self.filtered(lambda o: o.state not in ["草稿", "取消"]):
            raise ValidationError("仅草稿和取消状态可以删除。")
        return super(RokePurchaseInvoice, self).unlink()

    def _compute_verify_count(self):
        """
        计算核销明细数量
        :return:
        """
        for record in self:
            record.verify_count = len(record.verify_ids)

    @api.depends("payment_ids", "invoice_line_ids")
    def _compute_amount(self):
        for record in self:
            record.amount_total = sum(record.invoice_line_ids.mapped("subtotal"))
            payment_ids = record.payment_ids.filtered(lambda p: p.state == '已过账').ids
            record.amount_paid = sum(
                self.env["roke.purchase.invoice.verify"].sudo().search([
                    ("invoice_line_id", "in", record.invoice_line_ids.ids),
                    ("payment_id", "in", payment_ids),
                ]).mapped("amount_verified")
            )
            record.amount_unpaid = record.amount_total - record.amount_paid

    @api.depends("verify_ids", "amount_total")
    def _compute_is_show_invoice_pay(self):
        for record in self:
            amount_total_verified = sum(record.verify_ids.mapped("amount_verified"))
            if record.amount_total > amount_total_verified:
                record.is_show_invoice_pay = True
            else:
                record.is_show_invoice_pay = False

    def action_view_verify(self):
        """
        查看核销明细
        :return:
        """
        return {
            'name': '付款记录',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.purchase.invoice.verify',
            'view_mode': 'tree,form',
            "views": [
                [self.env.ref('roke_mes_account_purchase.view_roke_purchase_invoice_verify_tree').id, "tree"],
                [self.env.ref('roke_mes_account_purchase.view_roke_purchase_invoice_verify_form').id, "form"]
            ],
            'target': 'current',
            'domain': [('id', 'in', self.verify_ids.ids)],
            'context': {'create': False, 'edit': False, 'delete': False}
        }

    def action_invoice_pay(self):
        """
        发票付款
        :return:
        """
        self.ensure_one()
        result = self.env["wizard.purchase.invoice.pay"].sudo().create({
            "invoice_id": self.id,
            "invoice_line_ids": [
                (0, 0, {"invoice_line_id": line.id})
                for line in self.invoice_line_ids.filtered(
                    lambda il: il.subtotal > sum(
                        self.env["roke.purchase.invoice.verify"].sudo().search([
                            ("invoice_line_id", "=", il.id)
                        ]).mapped("amount_verified")
                    )
                )
            ]
        })
        return {
            'name': '发票生成付款单',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.purchase.invoice.pay',
            'res_id': result.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'create': False, 'edit': True, 'delete': False}
        }

    def action_confirm(self):
        write_dict = {"state": "确认"}
        if not self.invoice_date:
            write_dict.update({"invoice_date": fields.Date.context_today(self)})
        self.write(write_dict)

    def action_draft(self):
        self.write({"state": "草稿"})

    def make_cancel(self):
        """
        发票取消
        :return:
        """
        for invoice in self:
            invoice.action_cancel()

    def action_cancel(self):
        write_dict = {"state": "取消"}
        if self.payment_ids:
            write_dict.update({"payment_ids": False})
        if self.verify_ids:
            self.verify_ids.unlink()
        self.write(write_dict)


class RokePurchaseInvoiceLine(models.Model):
    _name = "roke.purchase.invoice.line"
    _description = "采购发票明细"

    invoice_id = fields.Many2one("roke.purchase.invoice", string="采购发票", required=True, ondelete='cascade')
    product_id = fields.Many2one("roke.product", string="产品", required=True, index=True, ondelete='restrict')
    name = fields.Char(string="说明")
    quantity = fields.Float(string="数量", required=True, default=1, digits='YSYFSL')
    price_unit = fields.Float(string="单价", required=True, digits='YSYFDJ')
    preferential_subtotal = fields.Float(string="优惠金额", store=True, digits='YSYFJE')
    subtotal = fields.Float(string="金额", digits='YSYFJE', store=True)

    purchase_line_ids = fields.Many2many(
        'roke.purchase.order.detail',
        'roke_purchase_order_detail_invoice_rel',
        'invoice_line_id', 'order_line_id',
        string='采购单明细ID', readonly=True, copy=False
    )

    tax_rate = fields.Float('税率', store="True")
    unit_price_excl_tax = fields.Float('不含税单价', digits='XSDJ', store="True")
    amount_excl_tax = fields.Float('不含税金额', digits='XSJE', store="True", compute='_compute_amount_excl_tax', readonly=True)
    tax_amount = fields.Float('税额', digits='XSJE', store="True", compute='_compute_amount_excl_tax', readonly=True)


    @api.onchange('product_id')
    def _onchange_tax_rate(self):
        if self.invoice_id.is_open_tax:
            self.tax_rate = self.product_id.tax_rate
        else:
            self.tax_rate = 0

    @api.onchange('unit_price_excl_tax')
    def _onchange_tax_rate_next(self):
        tax_rate = (
                               self.price_unit - self.unit_price_excl_tax) / self.price_unit * 100 if self.price_unit and self.price_unit > 0 else 0
        if self.unit_price_excl_tax > self.price_unit:
            raise ValidationError('不含税单价大于产品单价!')
        if self.tax_rate != tax_rate:
            self.tax_rate = tax_rate

    @api.onchange('price_unit', 'tax_rate')
    def _onchange_unit_price_excl_tax(self):
        if self.tax_rate > 100:
            raise ValidationError('税率禁止大于100!')
        if self.tax_rate < 0:
            raise ValidationError('税率禁止为负数!')
        if self.invoice_id.is_open_tax:
            unit_price_excl_tax = self.price_unit - self.price_unit * self.tax_rate / 100
            self.unit_price_excl_tax = unit_price_excl_tax
        else:
            self.unit_price_excl_tax = 0

    @api.depends('unit_price_excl_tax', 'quantity', 'amount_excl_tax', 'tax_amount')
    def _compute_amount_excl_tax(self):
        for rec in self:
            if rec.invoice_id.is_open_tax:
                rec.amount_excl_tax = rec.unit_price_excl_tax * rec.quantity
                if rec.tax_rate:
                    rec.tax_amount = (rec.price_unit - rec.unit_price_excl_tax) * rec.quantity
                else:
                    rec.tax_amount = 0
            else:
                rec.amount_excl_tax = 0
                rec.tax_amount = 0

    @api.depends('quantity', 'price_unit')
    @api.onchange('quantity', 'price_unit')
    def _compute_subtotal(self):
        for line in self:
            if not line.quantity or not line.price_unit:
                line.subtotal = 0
            else:
                line.subtotal = line.quantity * line.price_unit

    @api.onchange("product_id")
    def _onchange_product_id(self):
        """
        获取产品说明
        :return:
        """
        value_dict = {}
        if self.product_id:
            value_dict["name"] = "%s（%s）" % (self.product_id.name, self.product_id.code)
        else:
            value_dict["name"] = ""
        return {"value": value_dict}


class RokePurchaseInvoiceVerify(models.Model):
    _name = "roke.purchase.invoice.verify"
    _description = "采购发票核销"
    _rec_name = "invoice_line_id"

    invoice_id = fields.Many2one("roke.purchase.invoice", string="采购发票", required=True, ondelete='cascade')
    invoice_line_id = fields.Many2one("roke.purchase.invoice.line", string="采购发票明细行", required=True,
                                      ondelete='cascade')
    payment_id = fields.Many2one("roke.mes.payment", string="付款单", required=True, ondelete='cascade')
    invoice_date = fields.Date(related="invoice_id.invoice_date", string="发票时间")  # , store=True
    verify_date = fields.Date(string="核销时间")
    amount_verified = fields.Float(string="核销金额", digits='YSYFJE')
    customer_id = fields.Many2one(related="invoice_id.supplier_id", string="供应商")

    quantity = fields.Float(related="invoice_line_id.quantity", string="数量", digits='YSYFSL')
    price_unit = fields.Float(related="invoice_line_id.price_unit", string="单价", digits='YSYFDJ')
    subtotal = fields.Float(related="invoice_line_id.price_unit", string="金额", digits='YSYFJE')
