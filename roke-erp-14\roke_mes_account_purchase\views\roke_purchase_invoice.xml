<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--采购应付单-->
    <!--search-->
    <record id="view_roke_purchase_invoice_search" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.search</field>
        <field name="model">roke.purchase.invoice</field>
        <field name="arch" type="xml">
            <search string="采购应付单">
                <field name="code"/>
                <field name="supplier_id"/>
                <field name="invoice_date"/>
                <field name="invoice_date_due"/>
                <field name="state"/>
                <field name="invoice_line_ids"/>
                <filter string="草稿" name="草稿" domain="[('state', '=', '草稿')]"/>
                <filter string="确认" name="确认" domain="[('state', '=', '确认')]"/>
                <filter string="取消" name="取消" domain="[('state', '=', '取消')]"/>
                <group expand="0" string="Group By">
                    <filter string="供应商" name="group_supplier_id" context="{'group_by': 'supplier_id'}"/>
                    <filter string="状态" name="group_state" context="{'group_by' : 'state'}"/>
                    <filter string="应付单日期" name="group_invoice_date" context="{'group_by': 'invoice_date'}"/>
                    <filter string="到期日期" name="group_invoice_date_due" context="{'group_by': 'invoice_date_due'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_purchase_invoice_tree" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.tree</field>
        <field name="model">roke.purchase.invoice</field>
        <field name="arch" type="xml">
            <tree string="采购应付单" decoration-info="state=='草稿'" decoration-muted="state=='取消'">
                <field name="code"/>
                <field name="supplier_id" optional="show"/>
                <field name="invoice_date" optional="show"/>
                <field name="invoice_date_due" optional="show"/>
                <!--                <field name="invoice_date_due" widget="remaining_days" optional="show"/>-->
                <field name="state" optional="show"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_purchase_invoice_form" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.form</field>
        <field name="model">roke.purchase.invoice</field>
        <field name="arch" type="xml">
            <form string="采购应付单" create="false">
                <header>
                    <field name="is_show_invoice_pay" invisible="1"/>
                    <button name="action_confirm" type="object" string="确认" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <button name="action_invoice_pay" type="object" string="应付单付款" class="oe_highlight"
                            attrs="{'invisible': ['|', ('state', '!=', '确认'), ('is_show_invoice_pay', '=', False)]}"/>
                    <button name="action_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '!=', '取消')]}"/>
                    <button name="action_cancel" type="object" string="取消应付单"
                            attrs="{'invisible': [('state', 'not in', ['草稿', '确认'])]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <div name="button_box" class="oe_button_box">
                    <button type="object"
                            name="action_view_verify"
                            class="oe_stat_button"
                            icon="fa-pencil-square-o" attrs="{'invisible':[('verify_ids','=',[])]}">
                        <field name="verify_count" widget="statinfo" string="付款记录" help="查看付款记录"/>
                        <field name="verify_ids" invisible="1"/>
                    </button>
                </div>
                <div class="oe_title">
<!--                    采购应付单-->
<!--                    <h1 class="d-flex">-->
<!--                        <field name="code" attrs="{'readonly': [('state', '!=', '草稿')]}"/>-->
<!--                    </h1>-->
                </div>
                <group name="header">
                    <group name="header_left">
                        <group>
                            <field name="supplier_id" required="1"/>
                        </group>
                        <group>
                            <field name="payment_reference"/>
                            <field name="is_open_tax" invisible="1"/>
                        </group>
                    </group>
                    <group>
                        <group name="header_right">
                            <field name="invoice_date"/>
                        </group>
                        <group>
                            <field name="invoice_date_due"/>
                        </group>
                    </group>
                </group>
                <notebook>
                    <page string="应付单明细" name="invoice_line_ids">
                        <field name="invoice_line_ids"/>
                        <group class="oe_subtotal_footer oe_right">
                            <field name="amount_total"/>
                            <field name="amount_paid"/>
                            <field name="amount_unpaid"/>
                        </group>
                    </page>
                </notebook>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_purchase_invoice_action" model="ir.actions.act_window">
        <field name="name">采购应付单</field>
        <field name="res_model">roke.purchase.invoice</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个采购应付单。
            </p>
        </field>
    </record>

    <!-- tree -->
    <record id="view_roke_purchase_invoice_line_tree" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.line.tree</field>
        <field name="model">roke.purchase.invoice.line</field>
        <field name="arch" type="xml">
            <tree string="应付单明细" editable="bottom">
                <field name="invoice_id" invisible="1"/>
                <field name="product_id"/>
                <field name="name"/>
                <field name="quantity"/>
                <field name="price_unit"/>
                <field name="preferential_subtotal"/>
                <field name="subtotal"/>
                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="amount_excl_tax" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="tax_amount" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
            </tree>
        </field>
    </record>

    <!-- form -->
    <record id="view_roke_purchase_invoice_line_form" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.line.form</field>
        <field name="model">roke.purchase.invoice.line</field>
        <field name="arch" type="xml">
            <form string="应付单明细">
                <group>
                    <group name="header_left">
                        <field name="product_id" widget="many2one_barcode"/>
                        <field name="price_unit"/>
                        <field name="quantity"/>
                    </group>
                    <group name="header_right">
                        <field name="preferential_subtotal" force_save="1"/>
                        <field name="subtotal" force_save="1"/>
                    </group>
                </group>
                <label for="name" string="说明"/>
                <field name="name" widget="text"/>
            </form>
        </field>
    </record>

    <!--  核销明细  -->
    <!-- tree -->
    <record id="view_roke_purchase_invoice_verify_tree" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.verify.tree</field>
        <field name="model">roke.purchase.invoice.verify</field>
        <field name="arch" type="xml">
            <tree string="核销明细">
                <field name="invoice_id"/>
                <field name="invoice_line_id"/>
                <field name="payment_id"/>
                <field name="invoice_date"/>
                <field name="verify_date"/>
                <field name="amount_verified"/>
            </tree>
        </field>
    </record>
    <!-- form -->
    <record id="view_roke_purchase_invoice_verify_form" model="ir.ui.view">
        <field name="name">roke.purchase.invoice.verify.form</field>
        <field name="model">roke.purchase.invoice.verify</field>
        <field name="arch" type="xml">
            <form string="核销明细">
                <sheet>
                    <group>
                        <group>
                            <field name="invoice_id"/>
                            <field name="invoice_line_id" options="{'no_create_edit':1,'no_create':1,'no_open':1}"/>
                            <field name="invoice_date"/>
                        </group>
                        <group>
                            <field name="payment_id"/>
                            <field name="verify_date"/>
                            <field name="amount_verified"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
</odoo>
