# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class InheritPurchaseBillingPeriodWarningWizard(models.TransientModel):
    _inherit = "purchase.billing.period.warning.wizard"
    _description = "采购账期预警"

    amount_paid = fields.Float(string="已付款金额", digits='CGJE')
    amount_unpaid = fields.Float(string="未付款金额", digits='CGJE')
