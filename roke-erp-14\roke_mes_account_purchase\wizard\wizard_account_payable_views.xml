<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="wizard_account_payable_views" model="ir.ui.view">
        <field name="name">roke.account.payable.form</field>
        <field name="model">roke.account.payable</field>
        <field name="arch" type="xml">
            <form string="应付账款查询">
                <group>
                    <group>
                        <field name="date_start"/>
                        <field name="date_end"/>
                        <field name="supplier_id"/>
                    </group>
                </group>
                <footer>
                    <button name="confirm" string="确认" type="object" class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="wizard_account_payable_views_action" model="ir.actions.server">
        <field name="name">应付账款汇总查询</field>
        <field name="model_id" ref="model_roke_account_payable"/>
        <field name="state">code</field>
        <field name="code">
            action = model.action_open_wizard()
        </field>
    </record>

    <record id="wizard_account_payable_result_views" model="ir.ui.view">
        <field name="name">roke.account.payable.form</field>
        <field name="model">roke.account.payable.result.order</field>
        <field name="arch" type="xml">
            <form string="应付账款查询">
                <group>
                    <field name="date_start" readonly="1"/>
                    <field name="date_end" readonly="1"/>
                    <field name="supplier_id" readonly="1"/>
                </group>
                <notebook>
                    <page>
                        <field name="line_ids">
                            <tree>
                                <field name="supplier_code"/>
                                <field name="supplier_id"/>
                                <field name="opening_balance"/>
                                <field name="current_payable"/>
                                <field name="current_payment"/>
                                <field name="closing_balance"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <!--                <footer>-->
                <!--                    <button name="confirm" string="确认" type="object" class="oe_highlight"/>-->
                <!--                    <button string="取消" class="oe_link" special="cancel"/>-->
                <!--                </footer>-->
            </form>
        </field>
    </record>
</odoo>
