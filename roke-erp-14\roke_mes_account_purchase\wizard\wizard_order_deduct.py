# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class WizardPurchaseOrderDeduct(models.TransientModel):
    _name = "wizard.purchase.order.deduct"
    _description = "采购订单付款"

    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('优惠金额', digits='YSYFJE')
    amount_after_discount = fields.Float('优惠后金额', digits='YSYFJE')
    deduct_line_ids = fields.One2many("wizard.purchase.order.deduct.line", "deduct_id", string="订单明细")

    def action_deduct_payment(self, result_id):
        """
        打开付款单
        :param result_id:付款单
        :return:
        """
        if len(result_id) > 1:
            return {
                'name': '本次创建的付款单',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': 'roke.mes.pay',
                'domain': [('id', 'in', result_id)],
                'views': [
                    (self.env.ref('roke_mes_account.view_roke_mes_pay_tree').id, 'tree'),
                    (self.env.ref('roke_mes_account.view_roke_mes_pay_form').id, 'form')
                ],
                'context': {'create': True, 'edit': True, 'delete': True}
            }
        else:
            return {
                'name': '本次创建的付款单',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': 'roke.mes.pay',
                'res_id': result_id[0],
                'context': {'create': True, 'edit': True, 'delete': True}
            }

    def action_confirm_deduct(self):
        """
        确认优惠，生成付款单
        :return:
        """
        result_ids = []
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部付款或添加明细数据。')
        # 归集采购订单
        purchase_order_ids = self.deduct_line_ids.mapped("order_line_id").mapped("order_id")
        for purchase in purchase_order_ids:
            # 供应商
            supplier_id = purchase.supplier_id
            # 取采购订单下明细
            line_dict_values = []
            # 过滤当前采购订单下的产品付款明细
            deduct_line_ids = self.deduct_line_ids.filtered(lambda r: r.order_line_id.order_id.id == purchase.id)
            for deduct in deduct_line_ids:
                line_dict_values.append((0, 0, {
                    "purchase_line_id": deduct.order_line_id.id,
                    "deducted_amount": 0,
                    "paid_amount": deduct.current_paid_amount
                }))
            # 付款单备注
            note = "来源单据:{}".format(purchase.code)
            # 查询预付款单id
            pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '预付款')])
            # 是否优惠
            is_deduct = True if purchase.amount_after_discount else False
            # 付款金额
            total_payment = sum(deduct_line_ids.mapped("current_paid_amount"))
            red_type = '付款' if purchase.purchase_type == '购货' or not purchase else '退款'
            banks = supplier_id.bank_ids if supplier_id else False
            bank_id = banks[0].id if banks else ''
            result_id = self.env["roke.mes.pay"].create({
                "state": "草稿",
                "payment_type": "付款",
                "partner_type": "供应商",
                "partner_id": supplier_id.id if supplier_id else False,
                "payment_date": fields.Date.context_today(self),
                "amount": total_payment,
                "pay_amount": total_payment,
                "is_edit": True,
                "is_deduct": is_deduct,
                "note": note,
                "payment_line_ids": line_dict_values,
                "is_payment": True,
                'order_type': pay_id.id,
                'is_advance_payment': True,
                'origin_order': purchase.code,
                'red_type': red_type,
                'bank_account_id': bank_id,
                "purchase_order_id": purchase.id
            })
            _amount_paid = purchase.amount_paid
            _amount_paid += total_payment
            # 采购订单关联付款单
            purchase.write({"payment_ids": [(4, result_id.payment_id.id)], "amount_paid": _amount_paid})
            result_ids.append(result_id.id)
        return self.action_deduct_payment(result_ids)

    def app_action_confirm_deduct(self,purchase_id):
        """
        app确认优惠，生成付款单
        :return:
        """
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部付款或添加明细数据。')
        total_payment = 0
        purchase_order_ids = self.deduct_line_ids.mapped("order_line_id").mapped("order_id")
        supplier_id = self.deduct_line_ids[0].order_line_id.order_id.supplier_id.id if self.deduct_line_ids else False
        line_dict_values = []
        for item in self.deduct_line_ids:
            total_payment += item.pay_amount
            line_dict_values.append((0, 0, {
                "purchase_line_id": item.order_line_id.id,
                "deducted_amount": 0, "paid_amount": item.current_paid_amount,
            }))
        # 创建付款单
        note = "来源单据:{}".format("\n".join(purchase_order_ids.mapped("code")))
        # 查询预付款单id
        is_deduct = True if self.amount_after_discount else False
        # 如果优惠后金额不为0说明有优惠
        # if self.amount_after_discount > 0:
        total_payment = sum(self.deduct_line_ids.mapped("current_paid_amount"))
        purchase = self.env['roke.purchase.order'].search([('id', '=', purchase_id)])
        red_type = '付款' if purchase.purchase_type == '购货' else '退款'
        pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '预付款')])
        result_id = self.env["roke.mes.pay"].create({
            "state": "草稿", "payment_type": "付款", "partner_type": "供应商", "partner_id": supplier_id,
            "payment_date": fields.Date.context_today(self), "amount": total_payment,
            "pay_amount": total_payment, "is_edit": True, "is_deduct": is_deduct,
            "note": note, "payment_line_ids": line_dict_values, "is_payment": True, 'order_type': pay_id.id,
            'is_advance_payment': True,
            'origin_order': purchase.code,
            'red_type': red_type
        })
        # 采购单关联付款单
        purchase_order_ids.write({"payment_ids": [(4, result_id.payment_id.id)]})


class WizardPurchaseOrderDeductLine(models.TransientModel):
    _name = "wizard.purchase.order.deduct.line"
    _description = "付款明细"

    deduct_id = fields.Many2one("wizard.purchase.order.deduct", string="优惠向导")
    order_line_id = fields.Many2one("roke.purchase.order.detail", string="产品", required=True)
    product_id = fields.Many2one(related="order_line_id.product_id", string="产品")
    unit_price = fields.Float(related="order_line_id.unit_price", digits='YSYFDJ')
    qty = fields.Float(related="order_line_id.qty", string="数量", digits='YSYFSL')
    subtotal = fields.Float(related="order_line_id.subtotal", string="金额", digits='YSYFJE')

    deducted_amount = fields.Float(related="order_line_id.deducted_amount", string="已优惠金额", digits='YSYFJE')
    paid_amount = fields.Float(related="order_line_id.paid_amount", string="已付款金额", digits='YSYFJE')

    unpaid_amount = fields.Float(string="待付款金额", digits='YSYFJE')
    deduct_amount = fields.Float(string="优惠金额", digits='YSYFJE')
    pay_amount = fields.Float(string="付款金额", digits='YSYFJE')

    after_discount_amount = fields.Float(related="order_line_id.after_discount_amount", string="折扣后金额", digits='YSYFJE')
    whole_order_offer = fields.Float(related="order_line_id.whole_order_offer", string="整单优惠", digits='YSYFJE')
    amount_receivable = fields.Float(related="order_line_id.amount_receivable", string="应付金额", digits='YSYFJE')
    current_paid_amount = fields.Float(string="本次付款金额", digits='YSYFJE')

    @api.onchange("deduct_amount")
    def onchange_deduct_amount(self):
        if self.deduct_amount > self.unpaid_amount:
            raise ValidationError("优惠金额不能大于待付款金额")
        self.pay_amount = round(self.unpaid_amount - self.deduct_amount, 2)

    @api.onchange("pay_amount")
    def onchange_pay_amount(self):
        if self.pay_amount > round(self.unpaid_amount - self.deduct_amount, 2):
            raise ValidationError("付款金额不能大于优惠后金额")
