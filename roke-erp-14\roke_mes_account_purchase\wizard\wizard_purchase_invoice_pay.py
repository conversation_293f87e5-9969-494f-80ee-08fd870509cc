# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class WizardPurchaseInvoicePay(models.TransientModel):
    _name = "wizard.purchase.invoice.pay"
    _description = "发票收款"

    invoice_id = fields.Many2one("roke.purchase.invoice", string="采购发票")
    supplier_id = fields.Many2one(related="invoice_id.supplier_id")
    order_id = fields.Many2one(related="invoice_id.order_id")
    invoice_date = fields.Date(related="invoice_id.invoice_date")
    invoice_date_due = fields.Date(related="invoice_id.invoice_date_due")
    amount_unpaid = fields.Float(related="invoice_id.amount_unpaid", digits='YSYFJE')
    amount_total = fields.Float(string="支付金额", compute="_compute_amount_total", digits='YSYFJE')
    invoice_line_ids = fields.One2many("wizard.purchase.invoice.pay.line", "wizard_id", string="发票付款明细")

    @api.depends("invoice_line_ids")
    def _compute_amount_total(self):
        for record in self:
            record.amount_total = sum(
                record.invoice_line_ids.filtered(lambda il: il.is_mark).mapped("amount_unverified")
            )

    def action_payment(self):
        marked_invoice_line_ids = self.invoice_line_ids.filtered(lambda item: item.is_mark)
        if not marked_invoice_line_ids:
            raise ValidationError("请先勾选要生成付款单的发票明细")
        line_dict_values = []
        for line in self.invoice_id.order_id.detail_ids:
            line_dict_values.append((0, 0, {
                "purchase_line_id": line.id,
                "deducted_amount": 0, "paid_amount": line.subtotal,
            }))
        print(line_dict_values)
        default_note = f"""来源单据:{self.invoice_id.code}"""
        data = {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'context': {
                "default_red_type": "付款",
                "default_order_type": self.env['roke.mes.pay.type.selection'].search([('value', '=', '付款')]).id,
                "default_origin_order": self.invoice_id.code,
                "default_payment_type": "付款",
                "default_partner_type": "供应商",
                "default_partner_id": self.supplier_id.id,
                "default_payment_date": self.invoice_date,
                "default_amount": self.amount_total,
                "default_bank_account_id": self.order_id.bank_account_id.id,
                "default_note": default_note,
                "default_is_edit": True,
                "default_purchase_order_id": self.invoice_id.order_id.id,
                "m2m_purchase_invoice_id": self.invoice_id.id,
                "invoice_line_info": [
                    {"invoice_line_id": item.invoice_line_id.id, "amount": item.amount_unverified}
                    for item in marked_invoice_line_ids
                ],
                "pay_line_ids": line_dict_values,
            },
            'res_model': 'roke.mes.pay',
        }
        return data

    def mark_all(self):
        self.invoice_line_ids.write({"is_mark": True})
        return {
            'name': '发票生成付款单',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.purchase.invoice.pay',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'create': False, 'edit': True, 'delete': False}
        }

    def clear_mark(self):
        self.invoice_line_ids.write({"is_mark": False})
        return {
            'name': '发票生成付款单',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.purchase.invoice.pay',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'create': False, 'edit': True, 'delete': False}
        }


class WizardPurchaseInvoicePayLine(models.TransientModel):
    _name = "wizard.purchase.invoice.pay.line"
    _description = "发票付款明细"

    wizard_id = fields.Many2one("wizard.purchase.invoice.pay", string="Primary")
    is_mark = fields.Boolean(string="选择")
    invoice_line_id = fields.Many2one("roke.purchase.invoice.line", string="采购发票明细", required=True,
                                      ondelete='cascade')
    product_id = fields.Many2one(related="invoice_line_id.product_id")
    name = fields.Char(related="invoice_line_id.name")
    quantity = fields.Float(related="invoice_line_id.quantity", digits='YSYFSL')
    price_unit = fields.Float(related="invoice_line_id.price_unit", digits='YSYFDJ')
    subtotal = fields.Float(related="invoice_line_id.subtotal", digits='YSYFJE')
    amount_verified = fields.Float(string="已核销金额", compute="_compute_amount", digits='YSYFJE')
    amount_unverified = fields.Float(string="未核销金额", compute="_compute_amount", digits='YSYFJE')

    @api.depends("invoice_line_id")
    def _compute_amount(self):
        for record in self:
            verified_records = self.env["roke.purchase.invoice.verify"].sudo().search([
                ("invoice_line_id", "=", record.invoice_line_id.id),
                ("payment_id.state", "=", "已过账"),
            ])
            record.amount_verified = sum(verified_records.mapped("amount_verified"))
            record.amount_unverified = record.subtotal - record.amount_verified
