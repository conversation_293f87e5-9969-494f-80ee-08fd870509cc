<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_wizard_purchase_invoice_verify_form" model="ir.ui.view">
        <field name="name">wizard.purchase.invoice.verify.form</field>
        <field name="model">wizard.purchase.invoice.verify</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="partner_id" string="核销对象" required="1"/>
                    </group>
                    <group>
                        <field name="start_date"/>
                        <field name="end_date"/>
                    </group>
                </group>

                <group string="付款单">
                    <group cols="4">
                        <field name="payment_id" attrs="{'readonly': [('partner_id', '=', False)]}"/>
                        <field name="sum_amount_verify" invisible="1"/>

                    </group>
                </group>
                <group>
                    <field name="verify_payment_ids" nolabel="1">
                        <tree editable="bottom" create="0">
                            <field name="verify_id" invisible="1"/>
                            <field name="payment_id" readonly="1" force_save="1"/>
                            <field name="payment_date"/>
                            <field name="partner_id"/>
                            <field name="user_id"/>
                            <field name="amount"/>
                            <field name="amount_verified"/>
                            <field name="amount_unverified"/>
                            <field name="amount_verify" readonly="1" force_save="1"/>
                        </tree>
                    </field>
                </group>
                <!--                <group>-->
                <!--                    <field name="verify_payment_ids" nolabel="1">-->
                <!--                        <tree editable="bottom" create="0">-->
                <!--                            <field name="verify_id" invisible="1"/>-->
                <!--                            <field name="payment_id" readonly="1" force_save="1"/>-->
                <!--                            <field name="payment_date"/>-->
                <!--                            <field name="partner_id"/>-->
                <!--                            <field name="user_id"/>-->
                <!--                            <field name="amount"/>-->
                <!--                            <field name="amount_verified"/>-->
                <!--                            <field name="amount_unverified"/>-->
                <!--                            <field name="amount_verify" readonly="1" force_save="1"/>-->
                <!--                        </tree>-->
                <!--                    </field>-->
                <!--                </group>-->
                <group string="发票明细">
                    <field name="verify_invoice_line_ids" nolabel="1">
                        <tree editable="bottom" create="false">
                            <field name="verify_id" invisible="1"/>
                            <field name="is_mark"/>
                            <field name="invoice_id"/>
                            <field name="invoice_date"/>
                            <field name="supplier_id"/>
                            <field name="invoice_line_id" readonly="1" force_save="1"/>
                            <field name="quantity"/>
                            <field name="price_unit"/>
                            <field name="subtotal"/>
                            <field name="amount_unverified" readonly="1" force_save="1"/>
                            <field name="amount_verify" force_save="1"/>
                        </tree>
                    </field>
                </group>
                <footer>
                    <button name="action_verify" string="核销" type="object" class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="action_wizard_purchase_invoice_verify" model="ir.actions.act_window">
        <field name="name">采购发票核销</field>
        <field name="res_model">wizard.purchase.invoice.verify</field>
        <field name="view_mode">form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="target">new</field>
    </record>
    <record id="action_open_wizard_purchase_invoice_verify" model="ir.actions.server">
        <field name="name">采购发票核销</field>
        <field name="model_id" ref="roke_mes_account_purchase.model_wizard_purchase_invoice_verify"/>
        <field name="state">code</field>
        <field name="code">
            action = model.action_open_wizard()
        </field>
    </record>

    <record id="view_wizard_invoice_return_form" model="ir.ui.view">
        <field name="name">wizard.invoice.return.form</field>
        <field name="model">wizard.invoice.return</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <span>核销成功</span>
                </group>
                <footer>
<!--                    <button name='confirm' string='确认' type='object' class='oe_highlight'/>-->
                    <button string="确认" class="oe_highlight" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
