# -*- coding: utf-8 -*-
from . import models
from . import wizard
from odoo import api, SUPERUSER_ID


def add_sql_funtion_data(cr):
	env = api.Environment(cr, SUPERUSER_ID, {})
	env.cr.execute('''CREATE OR REPLACE FUNCTION "public"."summary_receivable"("start_dt" date, "end_dt" date, "employee" varchar)
  RETURNS TABLE("客户编号" varchar, "客户名称" varchar, "期初余额" numeric, "本期应收" numeric, "已收款" numeric, "期末余额" numeric) AS $BODY$ BEGIN
	
	--判断客户名称是否为空
	--调用函数并将结果集存储在一个临时表中
	CREATE TEMPORARY TABLE temp_partner_names(rp_names varchar);
	    IF employee IS NOT NULL THEN
				INSERT INTO temp_partner_names(rp_names) 
        SELECT NAME
        FROM roke_partner
        WHERE NAME LIKE '%' || employee || '%';
    ELSE
        INSERT INTO temp_partner_names(rp_names)
        SELECT NAME
        FROM roke_partner
        WHERE NAME IS NOT NULL;
    END IF;
	
	--存储本期应收金额
		CREATE TEMPORARY TABLE temp_table_benqi_a ( rp_id NUMERIC, bqys NUMERIC );
	INSERT INTO temp_table_benqi_a ( rp_id, bqys ) SELECT
	* 
	FROM
		(
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.subtotal, 0 ) ) AS bqys 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
			ID LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL 
			AND roke_sale_order.order_date BETWEEN start_dt 
			AND end_dt 
-- 			AND
-- 				CASE
-- 					WHEN employee IS NOT NULL THEN roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
-- 				END
		
		GROUP BY
			GROUPING SETS ( roke_partner.ID) 
		) qq;
		
	--存储本期已收金额以及优惠额
		CREATE TEMPORARY TABLE temp_table_benqi_b (rp_id NUMERIC, yhje NUMERIC, bqyis NUMERIC);
		INSERT INTO temp_table_benqi_b (rp_id, yhje, bqyis) SELECT * FROM (
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.discount_amount, 0 ) ) + SUM ( COALESCE ( roke_sale_order_line.whole_order_offer, 0 ) ) AS yhje,
			SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS bqyis 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.ID
			LEFT JOIN roke_mes_payment ON roke_sale_order.id = roke_mes_payment.sale_order_id
			LEFT JOIN roke_mes_payment_line ON roke_mes_payment.ID = roke_mes_payment_line.payment_id
			LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL
			AND roke_mes_payment."state" = '已过账'
			AND roke_sale_order.order_date BETWEEN start_dt 
			AND end_dt 
-- 		AND
-- 		CASE	
-- 				WHEN employee IS NOT NULL THEN
-- 				roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
-- 			END 
			GROUP BY
				GROUPING SETS ( roke_partner.ID )
		) ww;
		
		--期初应收
	CREATE TEMPORARY TABLE temp_table_qichu_a ( rp_id NUMERIC, qcys NUMERIC );
	INSERT INTO temp_table_qichu_a ( rp_id, qcys ) SELECT
	* 
	FROM
		(
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.subtotal, 0 ) ) AS qcys 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
			ID LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL 
			AND roke_sale_order.order_date < start_dt
-- 		AND
-- 		CASE
-- 				WHEN employee IS NOT NULL THEN
-- 				roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
-- 			END 
			GROUP BY
				GROUPING SETS ( roke_partner.ID ) 
			) ee;
			
		--期初已收
CREATE TEMPORARY TABLE temp_table_qichu_b ( rp_id NUMERIC, yhje NUMERIC, qcyis NUMERIC );
INSERT INTO temp_table_qichu_b (rp_id, yhje, qcyis) SELECT * FROM (
SELECT
	roke_partner.ID AS rp_id,
	SUM ( COALESCE ( roke_sale_order_line.discount_amount, 0 ) ) + SUM ( COALESCE ( roke_sale_order_line.whole_order_offer, 0 ) ) AS yhje,
	SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS qcyis 
FROM
	roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.ID
			LEFT JOIN roke_mes_payment ON roke_sale_order.id = roke_mes_payment.sale_order_id
			LEFT JOIN roke_mes_payment_line ON roke_mes_payment.ID = roke_mes_payment_line.payment_id
			LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
WHERE
	roke_sale_order.customer_id IS NOT NULL
	AND roke_mes_payment."state" = '已过账'
	AND roke_sale_order.order_date < start_dt
-- 	AND
-- 		CASE
-- 			WHEN employee IS NOT NULL THEN roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
-- 		END
GROUP BY
	GROUPING SETS ( roke_partner.ID )
	) rr ;
	
	--存储临时数
	CREATE TEMPORARY TABLE temp_table_final ( rp_code VARCHAR, rp_name VARCHAR, f_qcye NUMERIC, f_bqys NUMERIC, f_ysk NUMERIC, f_qmye NUMERIC );
	INSERT INTO temp_table_final (rp_code, rp_name, f_qcye, f_bqys, f_ysk, f_qmye) SELECT * FROM (
	SELECT
	( roke_partner.code ) AS rp_code,
	( roke_partner.NAME ) AS rp_name,
	( COALESCE ( temp_table_qichu_a.qcys, 0 ) - COALESCE ( temp_table_qichu_b.yhje, 0 ) ) AS f_qcye,
	( COALESCE ( temp_table_benqi_a.bqys, 0 ) - COALESCE ( temp_table_benqi_b.yhje, 0 ) ) AS f_bqys,
	COALESCE ( temp_table_benqi_b.bqyis, 0 ) AS f_ysk,
	(
		COALESCE ( temp_table_qichu_a.qcys, 0 ) - COALESCE ( temp_table_qichu_b.yhje, 0 ) + COALESCE ( temp_table_benqi_a.bqys, 0 ) - COALESCE ( temp_table_benqi_b.yhje, 0 ) - COALESCE ( temp_table_benqi_b.bqyis, 0 ) 
	) AS f_qmye 
FROM
	roke_partner
	LEFT JOIN temp_table_benqi_a ON temp_table_benqi_a.rp_id = roke_partner.ID
	LEFT JOIN temp_table_benqi_b ON temp_table_benqi_a.rp_id = temp_table_benqi_b.rp_id
	LEFT JOIN temp_table_qichu_a ON temp_table_benqi_a.rp_id = temp_table_qichu_a.rp_id
	LEFT JOIN temp_table_qichu_b ON temp_table_benqi_a.rp_id = temp_table_qichu_b.rp_id
	WHERE roke_partner.name in (select rp_names from temp_partner_names)
	) tt;
	
-- Routine body goes here...
	RETURN QUERY SELECT
	temp_table_final.rp_code AS 供应商编号,
	temp_table_final.rp_name  AS 供应商名称,
	COALESCE(temp_table_final.f_qcye, 0) AS 期初余额,
	COALESCE(temp_table_final.f_bqys, 0) AS 本期应收,
	COALESCE(temp_table_final.f_ysk, 0) AS 已收款,
	COALESCE(temp_table_final.f_qmye, 0) AS 期末余额 
	FROM
		temp_table_final 
		where f_bqys != 0 and f_bqys != 0
		order by f_qmye desc;
		
	DROP TABLE
	IF
		EXISTS temp_table_benqi_a;
		
		DROP TABLE
	IF
		EXISTS temp_table_benqi_b;
		
		DROP TABLE
	IF
		EXISTS temp_table_qichu_a;
		DROP TABLE
	IF
		EXISTS temp_table_qichu_b;
		DROP TABLE
	IF
		EXISTS temp_table_final;
		DROP TABLE
	IF
		EXISTS temp_partner_names;
	
	
END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000''')
