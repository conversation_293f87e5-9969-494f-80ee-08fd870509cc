# -*- coding: utf-8 -*-
{
    'name': '应收-销售、库存桥接',
    'version': '1.4',
    'category': 'mes',
    'depends': ['roke_mes_account', 'roke_mes_sale'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        应收-销售、库存桥接
    """,
    'data': [
        'security/ir.model.access.csv',
        'data/order_data.xml',
        'data/account_data.xml',
        'data/roke_mes_account_sale_product.xml',
        'data/roke_mes_account_sale_supplier.xml',
        'data/roke_mes_account_sale_salesman.xml',
        'data/query_sale_report.xml',
        'data/query_collection_report.xml',
        'data/query_advance_collection_report.xml',
        'data/query_summary_receivable_report.xml',
        'data/mobile_custom_data.xml',
        # 'data/roke_query_sales_summary_by_salesperson.xml',
        'views/roke_sale_invoice.xml',
        'views/inherit_roke_sale_views.xml',
        'views/inherit_roke_mes_payment_views.xml',
        'wizard/wizard_sale_invoice_pay.xml',
        'wizard/wizard_sale_invoice_verify.xml',
        'wizard/wizard_order_deduct.xml',
        'wizard/wizard_account_payment_views.xml',
        'wizard/inherit_sale_billing_period_warning_wizard.xml',
        'views/menus.xml'
    ],
    'demo': [
    ],
    'pre_init_hook': 'add_sql_funtion_data',
    'application': True,
    'installable': True,
    'auto_install': False,
}
