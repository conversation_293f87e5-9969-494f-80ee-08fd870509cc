<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--默认值-类型-默认收款-->
        <record id="default_payment_type_collection" model="roke.app.default">
            <field name="type_name">selection</field>
            <field name="str_field">收款</field>
        </record>

        <!--销售-收款-->
        <record id="mobile_roke_order_collection" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_collection"/>
            <field name="model_name">收款</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_sale_mark"/>
            <field name="detail_field_id" ref="roke_mes_account.field_roke_mes_collection__payment_line_ids"/>
            <field name="detail_model_id" ref="roke_mes_account.model_roke_mes_collection"/>
            <field name="base_data">False</field>
            <field name="is_prefabrication">True</field>
            <field name="is_batch_add">False</field>
            <field name="default_open_state">默认新增状态</field>
        </record>

        <!--表头字段-->
        <record id="roke_order_collection_header_field_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__partner_id"/>
        </record>
        <record id="roke_order_collection_header_field_ids02-1" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_type"/>
            <field name="default_value" ref="default_payment_type_collection"/>
        </record>
        <record id="roke_order_collection_header_field_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__order_type"/>
            <field name="domain_field">[["code","ilike","收款"]]</field>
        </record>
        <record id="roke_order_collection_header_field_ids03" model="roke.app.general.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__red_type"/>
        </record>
        <record id="roke_order_collection_header_field_ids04" model="roke.app.general.order.fields.header">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_mes_collection__bank_account_id"/>
        </record>
        <record id="roke_order_collection_header_field_ids05" model="roke.app.general.order.fields.header">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_method_id"/>
        </record>
        <record id="roke_order_collection_header_field_ids06" model="roke.app.general.order.fields.header">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__amount"/>
        </record>
        <record id="roke_order_collection_header_field_ids07" model="roke.app.general.order.fields.header">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_date"/>
        </record>
        <record id="roke_order_collection_header_field_ids08" model="roke.app.general.order.fields.header">
            <field name="sequence">8</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__origin_order"/>
        </record>

        <!--列表字段-->
        <record id="roke_order_collection_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__partner_id"/>
            <field name="primary">True</field>
        </record>
        <record id="roke_order_collection_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_date"/>
        </record>
        <record id="roke_order_collection_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__order_type"/>
        </record>
        <record id="roke_order_collection_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__amount"/>
        </record>
        <record id="roke_order_collection_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_order_collection"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__state"/>
        </record>

        <!--销售-收款记录-->
        <record id="mobile_roke_order_collection_line" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_account.model_roke_mes_collection"/>
            <field name="model_name">收款记录</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_sale_mark"/>
            <field name="detail_field_id" ref="roke_mes_account.field_roke_mes_collection__payment_line_ids"/>
            <field name="detail_model_id" ref="roke_mes_account.model_roke_mes_collection"/>
            <field name="base_data">False</field>
            <field name="is_prefabrication">True</field>
            <field name="is_batch_add">False</field>
        </record>

        <!--列表字段-->
        <record id="roke_order_sale_record_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__partner_id"/>
            <field name="primary">True</field>
        </record>
        <record id="roke_order_sale_record_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_date"/>
        </record>
        <record id="roke_order_sale_record_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__order_type"/>
        </record>
        <record id="roke_order_sale_record_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__amount"/>
        </record>
        <record id="roke_order_sale_record_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__state"/>
        </record>
        
        <!--筛选字段-->
        <record id="roke_order_sale_record_list_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__partner_id"/>
        </record>
        <record id="roke_order_sale_record_list_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_date"/>
        </record>
        <record id="roke_order_sale_record_list_search_field_ids03" model="roke.app.general.order.fields.search">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__state"/>
        </record>
        <record id="roke_order_sale_record_list_search_field_ids04" model="roke.app.general.order.fields.search">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__amount"/>
        </record>
        <record id="roke_order_sale_record_list_search_field_ids05" model="roke.app.general.order.fields.search">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__order_type"/>
        </record>

        <!--表头字段-->
        <record id="roke_order_sale_record_list_header_field_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__partner_id"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids02-1" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_type"/>
            <field name="default_value" ref="default_payment_type_collection"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__order_type"/>
            <field name="domain_field">[["code","ilike","收款"]]</field>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids03" model="roke.app.general.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__red_type"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids04" model="roke.app.general.order.fields.header">
            <field name="sequence">4</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_mes_collection__bank_account_id"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids05" model="roke.app.general.order.fields.header">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_method_id"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids06" model="roke.app.general.order.fields.header">
            <field name="sequence">6</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__amount"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids07" model="roke.app.general.order.fields.header">
            <field name="sequence">7</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__payment_date"/>
        </record>
        <record id="roke_order_sale_record_list_header_field_ids08" model="roke.app.general.order.fields.header">
            <field name="sequence">8</field>
            <field name="order_id" ref="mobile_roke_order_collection_line"/>
            <field name="field_id" ref="roke_mes_account.field_roke_mes_collection__origin_order"/>
        </record>
    </data>
</odoo>
