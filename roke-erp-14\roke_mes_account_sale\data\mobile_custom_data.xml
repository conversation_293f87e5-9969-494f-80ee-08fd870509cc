<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!--====================================字段数据显示配置====================================-->

        <!--字段数据显示配置-百分数乘100-->
        <record id="roke_field_data_setting_sale_open_discount_rate" model="roke.field.data.setting">
            <field name="set_ttype">数据操作</field>
            <field name="symbol">*</field>
            <field name="float_data">100</field>
        </record>

        <!--====================================默认值====================================-->

        <!--默认值-计算公式-求折扣率-->
        <record id="default_roke_sale_order_line_discount_rate" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">discount_rate = discount_amount * 100 / (order_qty * price_unit)</field>
            <field name="enable_default_result">True</field>
            <field name="default_result">0</field>
        </record>

        <!--默认值-计算公式-求折扣额-->
        <record id="default_roke_sale_order_line_discount_amount" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">discount_amount = (order_qty * price_unit) * discount_rate / 100</field>
            <field name="enable_default_result">True</field>
            <field name="default_result">0</field>
        </record>

        <!--默认值-计算公式-求金额-->
        <record id="default_account_roke_sale_order_line_after_discount_amount" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">after_discount_amount = order_qty * price_unit - discount_amount</field>
        </record>

        <!--默认值-计算公式-求优惠率-->
        <record id="default_roke_sale_order_new_discount_rate" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">discount_rate = discount_amount * 100 / discount_amount_total</field>
            <field name="enable_default_result">True</field>
            <field name="default_result">0</field>
        </record>

        <!--默认值-计算公式-求优惠金额-->
        <record id="default_roke_sale_order_new_discount_amount" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">discount_amount = discount_amount_total * discount_rate / 100</field>
            <field name="enable_default_result">True</field>
            <field name="default_result">0</field>
        </record>

        <!--默认值-计算公式-求优惠后金额-->
        <record id="default_roke_sale_order_new_amount_after_discount" model="roke.app.default">
            <field name="type_name">calculation</field>
            <field name="field_model_id" ref="roke_mes_sale.model_roke_sale_order_line"/>
            <field name="calculation_type">公式表达式</field>
            <field name="calculation_formula">amount_after_discount = discount_amount_total - discount_amount</field>
            <field name="enable_default_result">True</field>
            <field name="default_result">0</field>
        </record>

        <!--====================================通用单据配置字段补充====================================-->

        <!--销售开单-表头字段-->
        <record id="mobile_custom_roke_sale_order_open_header_field_ids37" model="roke.mobile.fields.base.header">
            <field name="sequence">37</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_roke_sale_order_open"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order__discount_amount_total"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_y"/>
            <field name="default_value" ref="roke_mes_client_setting.default_sale_open_float"/>
            <field name="field_readonly">True</field>
            <field name="zdy_field_description">订单金额</field>
        </record>
        <record id="mobile_custom_roke_sale_order_open_header_field_ids40" model="roke.mobile.fields.base.header">
            <field name="sequence">40</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_roke_sale_order_open"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order__discount_amount"/>
            <field name="default_value" ref="roke_mes_account_sale.default_roke_sale_order_new_discount_amount"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_y"/>
        </record>
        <record id="mobile_custom_roke_sale_order_open_header_field_ids45" model="roke.mobile.fields.base.header">
            <field name="sequence">45</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_roke_sale_order_open"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order__discount_rate"/>
            <field name="default_value" ref="roke_mes_account_sale.default_roke_sale_order_new_discount_rate"/>
            <field name="field_setting" ref="roke_mes_account_sale.roke_field_data_setting_sale_open_discount_rate"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_bfh"/>
        </record>
        <record id="mobile_custom_roke_sale_order_open_header_field_ids50" model="roke.mobile.fields.base.header">
            <field name="sequence">50</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_roke_sale_order_open"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order__amount_after_discount"/>
            <field name="default_value" ref="roke_mes_account_sale.default_roke_sale_order_new_amount_after_discount"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_y"/>
            <field name="field_readonly">True</field>
        </record>

        <!--====================================单据表体配置字段补充====================================-->

        <!--订单明细-字段详情-->
        <record id="mobile_custom_body_roke_sale_order_line_detail_field_ids15" model="roke.mobile.fields.base.detail">
            <field name="sequence">15</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_body_roke_sale_order_line"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order_line__discount_amount"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_y"/>
            <field name="default_value" ref="roke_mes_account_sale.default_roke_sale_order_line_discount_amount"/>
        </record>
        <record id="mobile_custom_body_roke_sale_order_line_detail_field_ids20" model="roke.mobile.fields.base.detail">
            <field name="sequence">20</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_body_roke_sale_order_line"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order_line__discount_rate"/>
            <field name="default_value" ref="roke_mes_account_sale.default_roke_sale_order_line_discount_rate"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_bfh"/>
        </record>
        <record id="mobile_custom_body_roke_sale_order_line_detail_field_ids30" model="roke.mobile.fields.base.detail">
            <field name="sequence">30</field>
            <field name="order_id" ref="roke_mes_sale.mobile_custom_body_roke_sale_order_line"/>
            <field name="field_id" ref="roke_mes_account_sale.field_roke_sale_order_line__after_discount_amount"/>
            <field name="field_after_display" ref="roke_mes_client_setting.after_field_sale_open_y"/>
            <field name="default_value" ref="roke_mes_account_sale.default_account_roke_sale_order_line_after_discount_amount"/>
            <field name="sum_detail_field_id" ref="roke_mes_account_sale.field_roke_sale_order__discount_amount_total"/>
            <field name="zdy_field_description">金额</field>
        </record>
    </data>
    <!--替换金额字段-->
    <function model="roke.mobile.custom.base" name="update_subtotal" eval="[[]]"/>
    <!--重定向计算字段默认值-->
    <function model="roke.app.default" name="write_calculation" eval="[[]]"/>
</odoo>
