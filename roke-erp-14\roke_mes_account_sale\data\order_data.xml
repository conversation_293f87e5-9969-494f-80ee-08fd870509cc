<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="new_sale_order_function_ids05" model="roke.app.general.order.function">
            <field name="order_id" ref="roke_mes_sale.mobile_roke_open_order_sale"/>
            <field name="function_name">收款</field>
            <field name="function_index">app_action_collection</field>
            <field name="condition">[('state','=','确认')]</field>
        </record>
        <record id="new_sale_order_function_ids06" model="roke.app.general.order.function">
            <field name="order_id" ref="roke_mes_sale.mobile_roke_order_sale_record"/>
            <field name="function_name">收款</field>
            <field name="function_index">app_action_collection</field>
            <field name="condition">[('state','=','确认')]</field>
        </record>
    </data>
</odoo>