<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_advance_collection_select_report" model="roke.sql.model.component">
            <field name="name">应收账款明细表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="sql_statement">
                SELECT roke_sale_order.order_date AS "单据日期", roke_sale_order.code AS "单据编号", roke_partner.name AS "客户", roke_employee.name AS "业务员", roke_sale_order.sale_type AS "业务类型", SUM(COALESCE(roke_sale_order_line.subtotal,0) - COALESCE(roke_sale_order_line.discount_amount,0) - COALESCE(roke_sale_order_line.whole_order_offer,0)) AS "应收款金额", SUM(COALESCE(roke_sale_order_line.paid_amount,0)) AS "已收款金额", roke_sale_order_line.note AS "备注"
                FROM roke_sale_order_line
                LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.ID
                LEFT JOIN roke_employee ON roke_sale_order.sale_user_id = roke_employee.ID
                LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID
                WHERE roke_sale_order.order_date between :order_date and :order_date
                AND roke_partner.name = :roke_partner.name
                AND roke_employee.name = :roke_employee.name
                GROUP BY roke_sale_order.order_date, roke_sale_order.code, roke_partner.name, roke_employee.name, roke_sale_order.sale_type, roke_sale_order_line.note
            </field>
            <field name="top_menu_id" ref="roke_mes_account.roke_mes_account_query_menu"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '选择日期',
                    'field_id': ref('roke_mes_sale.field_roke_sale_order__order_date'),
                    'sql_decider': 'between',
                    'sql_data': ' roke_sale_order.order_date between :order_date and  :order_date ',
                    'sql_field_mark': ':order_date',
                    'sql_field_mark_type': 'date'
                }),
                (0, 0, {
                    'name': '客户',
                    'field_id': ref('roke_mes_base.field_roke_partner__name'),
                    'sql_inherit_field_id': ref('roke_mes_sale.field_roke_sale_order__customer_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_partner.name = :roke_partner.name ',
                    'sql_field_mark': ':roke_partner.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '业务员',
                    'field_id': ref('roke_mes_base.field_roke_employee__name'),
                    'sql_inherit_field_id': ref('roke_mes_sale.field_roke_sale_order__sale_user_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_partner.name = :roke_employee.name ',
                    'sql_field_mark': ':roke_employee.name',
                    'sql_field_mark_type': 'many2one'
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sequence": 1,
                    "sql_order_by_data": "roke_sale_order.order_date"
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__code"),
                    "sequence": 2,
                    "sql_order_by_data": "roke_sale_order.code"
                }),
                (0, 0, {
                    "name": "客户",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 3,
                    "sql_order_by_data": "roke_partner.name"
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 4,
                    "sql_order_by_data": "roke_employee.name"
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__sale_type"),
                    "sequence": 5,
                    "sql_order_by_data": "roke_sale_order.sale_type"
                }),
                (0, 0, {
                    "name": "应收款金额",
                    "sequence": 6,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer) AS 应收款金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer)"
                }),
                (0, 0, {
                    "name": "已收款金额",
                    "sequence": 7,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.paid_amount)) AS 已收款金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.paid_amount))"
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order_line__note"),
                    "sequence": 8,
                    "sql_order_by_data": "roke_sale_order_line.note"
                })
            ]'/>
            <field name="sql_group_way" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sequence": 1
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__code"),
                    "sequence": 2
                }),
                (0, 0, {
                    "name": "客户",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 3
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 4
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__sale_type"),
                    "sequence": 5
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order_line__note"),
                    "sequence": 6
                })
            ]'/>
        </record>
    </data>
</odoo>
