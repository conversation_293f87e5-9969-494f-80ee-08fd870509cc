<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_collection_order_select_report" model="roke.sql.model.component">
            <field name="name">销售收款一览表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="sql_statement">
                SELECT roke_sale_order.order_date AS "单据日期", roke_sale_order.code AS "单据编号", roke_partner.name AS "客户", roke_employee.name AS "业务员", roke_sale_order.sale_type AS "业务类型", SUM(COALESCE(roke_sale_order_line.subtotal,0)) AS "金额", SUM(COALESCE(roke_sale_order_line.discount_amount,0)) AS "折扣额", SUM((COALESCE(roke_sale_order_line.discount_amount,0) + COALESCE(roke_sale_order_line.whole_order_offer,0))) AS "优惠金额", SUM((COALESCE(roke_sale_order_line.subtotal,0) - COALESCE(roke_sale_order_line.discount_amount,0) - COALESCE(roke_sale_order_line.whole_order_offer,0))) AS "应收金额", SUM(COALESCE(roke_sale_order_line.paid_amount,0)) AS "已收款", SUM(COALESCE(roke_sale_order_line.subtotal,0) - COALESCE(roke_sale_order_line.discount_amount,0) - COALESCE(roke_sale_order_line.whole_order_offer,0)) - SUM(COALESCE(roke_sale_order_line.paid_amount,0)) AS "待收款金额", CASE (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) WHEN 0 THEN 1 ELSE CAST(SUM(COALESCE(roke_sale_order_line.paid_amount,0)) / (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) AS DECIMAL(13,2)) END AS "收款率", roke_sale_order.note AS "备注", SUM(COALESCE(roke_sale_order_line.whole_order_offer,0)) AS "整单优惠"
                FROM roke_sale_order_line
                LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.ID
                LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID
                LEFT JOIN roke_employee ON roke_sale_order.sale_user_id = roke_employee.ID
                WHERE roke_sale_order.order_date between :order_date and :order_date AND roke_partner.name = :roke_partner.name
                GROUP BY roke_sale_order.order_date, roke_sale_order.code, roke_partner.name, roke_employee.name, roke_sale_order.sale_type, roke_sale_order.note
            </field>
            <field name="top_menu_id" ref="roke_mes_sale.roke_mes_sale_query_root"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '选择日期',
                    'field_id': ref('roke_mes_sale.field_roke_sale_order__order_date'),
                    'sql_decider': 'between',
                    'sql_data': ' roke_sale_order.order_date between :order_date and  :order_date ',
                    'sql_field_mark': ':order_date',
                    'sql_field_mark_type': 'date'
                }),
                (0, 0, {
                    'name': '客户',
                    'field_id': ref('roke_mes_base.field_roke_partner__name'),
                    'sql_inherit_field_id': ref('roke_mes_sale.field_roke_sale_order__customer_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_partner.name = :roke_partner.name ',
                    'sql_field_mark': ':roke_partner.name',
                    'sql_field_mark_type': 'many2one'
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sequence": 1,
                    "sql_order_by_data": "roke_sale_order.order_date"
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__code"),
                    "sequence": 2,
                    "sql_order_by_data": "roke_sale_order.code"
                }),
                (0, 0, {
                    "name": "客户",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 3,
                    "sql_order_by_data": "roke_partner.name"
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 4,
                    "sql_order_by_data": "roke_employee.name"
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__sale_type"),
                    "sequence": 5,
                    "sql_order_by_data": "roke_sale_order.sale_type"
                }),
                (0, 0, {
                    "name": "金额",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order_line__subtotal"),
                    "sequence": 6,
                    "summary_method": "SUM",
                    "sql_order_by_data": "SUM(roke_sale_order_line.subtotal)"
                }),
                (0, 0, {
                    "name": "折扣额",
                    "field_id": ref("roke_mes_account_sale.field_roke_sale_order_line__discount_amount"),
                    "sequence": 7,
                    "summary_method": "SUM",
                    "sql_order_by_data": "SUM(roke_sale_order_line.discount_amount)"
                }),
                (0, 0, {
                    "name": "优惠金额",
                    "sequence": 8,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.discount_amount + roke_sale_order_line.whole_order_offer) AS 优惠金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.discount_amount + roke_sale_order_line.whole_order_offer)"
                }),
                (0, 0, {
                    "name": "应收金额",
                    "sequence": 9,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer) AS 应收金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer)"
                }),
                (0, 0, {
                    "name": "已收款",
                    "sequence": 10,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.paid_amount) AS 已收款金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.paid_amount)"
                }),
                (0, 0, {
                    "name": "待收款金额",
                    "sequence": 11,
                    "summary_method": "SUM",
                    "sql_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer - SUM(roke_sale_order_line.paid_amount)) AS 已收款金额",
                    "sql_order_by_data": "SUM(roke_sale_order_line.subtotal - roke_sale_order_line.discount_amount - roke_sale_order_line.whole_order_offer - SUM(roke_sale_order_line.paid_amount))"
                }),
                (0, 0, {
                    "name": "收款率",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order_line__deliver_qty"),
                    "sequence": 12,
                    "summary_method": "SUM",
                    "sql_data": "CASE (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) WHEN 0 THEN 1 ELSE CAST(SUM(COALESCE(roke_sale_order_line.paid_amount,0)) / (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) AS DECIMAL(13,2)) END AS 收款率",
                    "sql_order_by_data": "CASE (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) WHEN 0 THEN 1 ELSE CAST(SUM(COALESCE(roke_sale_order_line.paid_amount,0)) / (SUM(COALESCE(roke_sale_order_line.subtotal,0)) - SUM(COALESCE(roke_sale_order_line.discount_amount,0)) - SUM(COALESCE(roke_sale_order_line.whole_order_offer,0))) AS DECIMAL(13,2)) END"
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__note"),
                    "sequence": 13,
                    "sql_order_by_data": "roke_sale_order.note"
                }),
                (0, 0, {
                    "name": "整单优惠",
                    "field_id": ref("roke_mes_account_sale.field_roke_sale_order_line__whole_order_offer"),
                    "sequence": 13,
                    "summary_method": "SUM",
                    "sql_order_by_data": "SUM(roke_sale_order_line.whole_order_offer)",
                    "is_not_show": True
                })
            ]'/>
            <field name="sql_group_way" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sequence": 1
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__code"),
                    "sequence": 2
                }),
                (0, 0, {
                    "name": "客户",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 3
                }),
                (0, 0, {
                    "name": "业务员",
                    "field_id": ref("roke_mes_base.field_roke_employee__name"),
                    "sequence": 4
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__sale_type"),
                    "sequence": 5
                }),
                (0, 0, {
                    "name": "备注",
                    "field_id": ref("roke_mes_sale.field_roke_sale_order__note"),
                    "sequence": 9
                })
            ]'/>
        </record>
    </data>
</odoo>
