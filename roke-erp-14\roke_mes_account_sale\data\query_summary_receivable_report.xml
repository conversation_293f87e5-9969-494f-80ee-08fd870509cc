<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_summary_receivable_report" model="roke.sql.model.component">
            <field name="name">应收账款汇总表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="query_type">True</field>
            <field name="sql_procedure">summary_receivable</field>
            <field name="top_menu_id" ref="roke_mes_account.roke_mes_account_query_menu"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '开始时间',
                    'sql_field_mark': 'start_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '结束时间',
                    'sql_field_mark': 'end_dt',
                    'sql_field_mark_type': 'date',
                }),
                (0, 0, {
                    'name': '客户',
                    'field_id': ref('roke_mes_base.field_roke_employee__name'),
                    'sql_field_mark': 'employee',
                    'sql_field_mark_type': 'char',
                })]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "客户编号",
                    "sql_data": "(temp_table1.rp_c) as 供应商编号",
                    "sql_order_by_data": "(temp_table1.rp_c)"
                }),
                (0, 0, {
                    "name": "客户名称",
                    "sql_data": "(temp_table1.rp_n) as 供应商名称",
                    "sql_order_by_data": "(temp_table1.rp_n)"
                }),
                (0, 0, {
                    "name": "期初余额",
                    "sql_data": "COALESCE(temp_table1.qcye, 0) as 期初余额",
                    "sql_order_by_data": "COALESCE(temp_table1.qcye, 0)"
                }),
                (0, 0, {
                    "name": "本期应收",
                    "sql_data": "COALESCE(temp_table1.bqys, 0) as 本期应收",
                    "sql_order_by_data": "COALESCE(temp_table1.bqys, 0)"
                }),
                (0, 0, {
                    "name": "已收款",
                    "sql_data": "COALESCE(temp_table1.ysk, 0) as 已收款",
                    "sql_order_by_data": "COALESCE(temp_table1.ysk, 0)"
                }),
                (0, 0, {
                    "name": "期末余额",
                    "sql_data": "COALESCE(temp_table1.qmye, 0) as 期末余额",
                    "sql_order_by_data": "COALESCE(temp_table1.qmye, 0)"
                })]'/>
        </record>
    </data>
</odoo>
