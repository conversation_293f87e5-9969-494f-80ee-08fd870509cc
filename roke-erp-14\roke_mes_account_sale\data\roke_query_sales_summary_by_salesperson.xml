<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- 销售汇总表 - 按业务员 -->
        <record id="roke_mes_sale.roke_query_sales_summary_by_salesperson" model="roke.query.component.model">
            <field name="name">销售汇总表（按业务员）</field>
            <!-- 显示列 (继承) -->
            <field name="show_columns" eval="[
                (0, 0, {'name': '不含税单价', 'field_id': ref('roke_mes_account_sale.field_roke_sale_order_line__unit_price_excl_tax')}),
                (0, 0, {'name': '不含税金额', 'field_id': ref('roke_mes_account_sale.field_roke_sale_order_line__amount_excl_tax')}),
                (0, 0, {'name': '折扣额', 'field_id': ref('roke_mes_account_sale.field_roke_sale_order_line__discount_amount')}),
                (0, 0, {'name': '税额', 'field_id': ref('roke_mes_account_sale.field_roke_sale_order_line__tax_amount')}),
            ]"/>
        </record>
    </data>
</odoo>
