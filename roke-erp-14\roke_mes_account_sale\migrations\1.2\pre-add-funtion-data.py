from odoo import api, SUPERUSER_ID
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
	env = api.Environment(cr, SUPERUSER_ID, {})
	env.cr.execute('''
    CREATE OR REPLACE FUNCTION "public"."summary_receivable"("start_dt" date, "end_dt" date, "employee" varchar)
  RETURNS TABLE("客户编号" varchar, "客户名称" varchar, "期初余额" numeric, "本期应收" numeric, "已收款" numeric, "期末余额" numeric) AS $BODY$ BEGIN
    		CREATE TEMPORARY TABLE temp_table2 (
    			rp_c VARCHAR,
    			rp_n VARCHAR,
    			qcye NUMERIC,
    			bqys NUMERIC,
    			ysk NUMERIC,
    			qmye NUMERIC
    		);
    	INSERT INTO temp_table2 (
    		rp_c,
    		rp_n,
    		qcye,
    		bqys,
    		ysk,
    		qmye
    		) (
    		SELECT
    			current_period.rp_c AS 客户编号,
    			current_period.rp_n AS 客户名称,
    			COALESCE ( beginning_period.qcys, 0 ) AS 期初余额,
    			COALESCE ( current_period.bqys, 0 ) AS 本期应收,
    			COALESCE ( current_period.bqyis, 0 ) AS 已收款,
    			SUM (
    				COALESCE ( beginning_period.qcys, 0 ) + COALESCE ( current_period.bqys, 0 ) - COALESCE ( current_period.bqyis, 0 )
    			) AS 期末余额
    		FROM
    			(
    				(
    				SELECT
    					roke_partner.code AS rp_c,
    					roke_partner.ID AS rp_i,
    					roke_partner.NAME AS rp_n,
    					SUM (
    						COALESCE ( roke_sale_order_line.subtotal, 0 ) - COALESCE ( roke_sale_order_line.discount_amount, 0 ) - COALESCE ( roke_sale_order_line.whole_order_offer, 0 )
    					) AS bqys,
    					SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS bqyis
    				FROM
    					roke_sale_order_line
    					LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
    					ID LEFT JOIN roke_mes_payment_line ON roke_sale_order_line.ID = roke_mes_payment_line.sale_line_id
    					LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID
    				WHERE
    					roke_sale_order.customer_id IS NOT NULL
    					AND roke_sale_order.order_date BETWEEN start_dt
    					AND end_dt
    				AND
    				CASE
    						WHEN employee IS NOT NULL THEN
    						roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL
    					END
    					GROUP BY
    						GROUPING SETS ( roke_partner.ID )
    					) AS current_period
    					LEFT JOIN (
    					SELECT
    						roke_partner.ID AS rp_i,
    						SUM (
    							COALESCE ( roke_sale_order_line.subtotal, 0 ) - COALESCE ( roke_sale_order_line.discount_amount, 0 ) - COALESCE ( roke_sale_order_line.whole_order_offer, 0 )
    						) AS qcys,
    						SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS qcyis
    					FROM
    						roke_sale_order_line
    						LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
    						ID LEFT JOIN roke_mes_payment_line ON roke_sale_order_line.ID = roke_mes_payment_line.sale_line_id
    						LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID
    					WHERE
    						roke_sale_order.customer_id IS NOT NULL
    						AND roke_sale_order.order_date < start_dt
    					AND
    					CASE
    							WHEN employee IS NOT NULL THEN
    							roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL
    						END
    						GROUP BY
    							GROUPING SETS ( roke_partner.ID )
    						) AS beginning_period ON current_period.rp_i = beginning_period.rp_i
    					)
    				GROUP BY
    					current_period.rp_c,
    					current_period.rp_n,
    					beginning_period.qcys,
    					current_period.bqys,
    					current_period.bqyis
    				);
    -- Routine body goes here...
    			RETURN QUERY SELECT
    			( temp_table2.rp_c ) AS 供应商编号,
    			( temp_table2.rp_n ) AS 供应商名称,
    			COALESCE ( temp_table2.qcye, 0 ) AS 期初余额,
    			COALESCE ( temp_table2.bqys, 0 ) AS 本期应收,
    			COALESCE ( temp_table2.ysk, 0 ) AS 已收款,
    			COALESCE ( temp_table2.qmye, 0 ) AS 期末余额
    			FROM
    				temp_table2;
    			DROP TABLE
    			IF
    				EXISTS temp_table2;

    END $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000
    	''')
