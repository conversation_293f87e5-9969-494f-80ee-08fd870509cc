from odoo import api, SUPERUSER_ID
import logging

_logger = logging.getLogger(__name__)


def migrate(cr, version):
	env = api.Environment(cr, SUPERUSER_ID, {})
	env.cr.execute('''CREATE 
	OR REPLACE FUNCTION "public"."summary_receivable" ( "start_dt" DATE, "end_dt" DATE, "employee" VARCHAR ) RETURNS TABLE ( "客户编号" VARCHAR, "客户名称" VARCHAR, "期初余额" NUMERIC, "本期应收" NUMERIC, "已收款" NUMERIC, "期末余额" NUMERIC ) AS $BODY$ BEGIN
	--存储本期应收金额
		CREATE TEMPORARY TABLE temp_table_benqi_a ( rp_id NUMERIC, bqys NUMERIC );
	INSERT INTO temp_table_benqi_a ( rp_id, bqys ) SELECT
	* 
	FROM
		(
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.subtotal, 0 ) ) AS bqys 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
			ID LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL 
			AND roke_sale_order.order_date BETWEEN start_dt 
			AND end_dt 
			AND
				CASE
					WHEN employee IS NOT NULL THEN roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
				END
		GROUP BY
			GROUPING SETS ( roke_partner.ID ) 
		) qq;
		
	--存储本期已收金额以及优惠额
		CREATE TEMPORARY TABLE temp_table_benqi_b (rp_id NUMERIC, yhje NUMERIC, bqyis NUMERIC);
		INSERT INTO temp_table_benqi_b (rp_id, yhje, bqyis) SELECT * FROM (
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.discount_amount, 0 ) ) + SUM ( COALESCE ( roke_sale_order_line.whole_order_offer, 0 ) ) AS yhje,
			SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS bqyis 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
			ID LEFT JOIN roke_mes_payment_line ON roke_sale_order_line.ID = roke_mes_payment_line.sale_line_id
			LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL 
			AND roke_sale_order.order_date BETWEEN start_dt 
			AND end_dt 
		AND
		CASE	
				WHEN employee IS NOT NULL THEN
				roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
			END 
			GROUP BY
				GROUPING SETS ( roke_partner.ID )
		) ww;
		
		--期初应收
	CREATE TEMPORARY TABLE temp_table_qichu_a ( rp_id NUMERIC, qcys NUMERIC );
	INSERT INTO temp_table_qichu_a ( rp_id, qcys ) SELECT
	* 
	FROM
		(
		SELECT
			roke_partner.ID AS rp_id,
			SUM ( COALESCE ( roke_sale_order_line.subtotal, 0 ) ) AS qcys 
		FROM
			roke_sale_order_line
			LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
			ID LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
		WHERE
			roke_sale_order.customer_id IS NOT NULL 
			AND roke_sale_order.order_date < start_dt
		AND
		CASE
				
				WHEN employee IS NOT NULL THEN
				roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
			END 
			GROUP BY
				GROUPING SETS ( roke_partner.ID ) 
			) ee;
			
		--期初已收
CREATE TEMPORARY TABLE temp_table_qichu_b ( rp_id NUMERIC, yhje NUMERIC, qcyis NUMERIC );
INSERT INTO temp_table_qichu_b (rp_id, yhje, qcyis) SELECT * FROM (
SELECT
	roke_partner.ID AS rp_i,
	SUM ( COALESCE ( roke_sale_order_line.discount_amount, 0 ) ) + SUM ( COALESCE ( roke_sale_order_line.whole_order_offer, 0 ) ) AS yhje,
	SUM ( COALESCE ( roke_mes_payment_line.paid_amount, 0 ) ) AS qcyis 
FROM
	roke_sale_order_line
	LEFT JOIN roke_sale_order ON roke_sale_order_line.order_id = roke_sale_order.
	ID LEFT JOIN roke_mes_payment_line ON roke_sale_order_line.ID = roke_mes_payment_line.sale_line_id
	LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.ID 
WHERE
	roke_sale_order.customer_id IS NOT NULL
	AND roke_sale_order.order_date < start_dt
	AND
		CASE
			WHEN employee IS NOT NULL THEN roke_partner.NAME ILIKE employee ELSE roke_partner.NAME IS NOT NULL 
		END
GROUP BY
	GROUPING SETS ( roke_partner.ID )
	) rr ;
-- Routine body goes here...
	RETURN QUERY SELECT
	( roke_partner.code ) AS 供应商编号,
	( roke_partner.name ) AS 供应商名称,
	(COALESCE ( temp_table_qichu_a.qcys, 0 ) - COALESCE ( temp_table_qichu_b.yhje, 0 )) AS 期初余额,
	(COALESCE ( temp_table_benqi_a.bqys, 0 ) - COALESCE ( temp_table_benqi_b.yhje, 0 )) AS 本期应收,
	COALESCE ( temp_table_benqi_b.bqyis, 0 ) AS 已收款,
	(COALESCE ( temp_table_qichu_a.qcys, 0 ) - COALESCE ( temp_table_qichu_b.yhje, 0 ) + COALESCE ( temp_table_benqi_a.bqys, 0 ) - COALESCE ( temp_table_benqi_b.yhje, 0 ) -  COALESCE ( temp_table_benqi_b.bqyis, 0 )) AS 期末余额 
	FROM
		roke_partner 
		left join temp_table_benqi_a on roke_partner.id = temp_table_benqi_a.rp_id
		left join temp_table_benqi_b on roke_partner.id = temp_table_benqi_b.rp_id 
		left join temp_table_qichu_a on roke_partner.id = temp_table_qichu_a.rp_id
		left join temp_table_qichu_b on roke_partner.id = temp_table_qichu_b.rp_id;
		
	DROP TABLE
	IF
		EXISTS temp_table_benqi_a;
		
		DROP TABLE
	IF
		EXISTS temp_table_benqi_b;
		
		DROP TABLE
	IF
		EXISTS temp_table_qichu_a;
		DROP TABLE
	IF
		EXISTS temp_table_qichu_b;

END $BODY$ LANGUAGE plpgsql VOLATILE COST 100 ROWS 1000
    	''')
