# -*- coding: utf-8 -*-"""
# Description:
#     移动端单据配置（字典帮助定义为基础）
# Versions:
#     Created by www.rokedata.com
# """
from odoo import models, fields, api, _
import requests
import logging
import json,re
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)

class InheritRokeMobileCustomBase(models.Model):
    _inherit = "roke.mobile.custom.base"

    # 替换金额字段
    def update_subtotal(self):
        # 删除原来明细金额替换新金额
        record = self.env.ref('roke_mes_sale.mobile_custom_body_roke_sale_order_line_detail_field_ids25',
                              raise_if_not_found=False)
        if record:
            record.unlink()
        # 替换合计字段
        sale_line = self.env.ref('roke_mes_sale.mobile_custom_body_roke_sale_order_line',
                              raise_if_not_found=False)
        field_id = self.env["ir.model.fields"].search([("model_id.model", "=", "roke.sale.order.line"), ("name", "=", "after_discount_amount")], limit=1)
        if sale_line:
            sale_line.write({'sum_detail_field_id': field_id.id})
        # 删除原来表头金额替换新折扣后金额
        record = self.env.ref('roke_mes_sale.mobile_custom_roke_sale_order_open_header_field_ids35',
                              raise_if_not_found=False)
        if record:
            record.unlink()


class InheritRokeAppDefault(models.Model):
    _inherit = "roke.app.default"

    # 重定向计算字段默认值
    def write_calculation(self):
        default_discount_rate = self.env.ref('roke_mes_account_sale.default_roke_sale_order_line_discount_rate',
                                             raise_if_not_found=False)
        default_discount_amount = self.env.ref('roke_mes_account_sale.default_roke_sale_order_line_discount_amount',
                                             raise_if_not_found=False)
        if default_discount_rate:
            default_discount_rate.write({
                'enable_default_result': True,
                'default_result': 0
            })
        if default_discount_amount:
            default_discount_amount.write({
                'enable_default_result': True,
                'default_result': 0
            })
