# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import datetime

class InheritRokeMesPayment(models.Model):
    _inherit = 'roke.mes.payment'

    sale_order_id = fields.Many2one("roke.sale.order", string="销售订单")
    sale_invoice_ids = fields.One2many('roke.sale.invoice.verify', 'payment_id', string='核销记录')
    bank_account_id = fields.Many2one("roke.partner.bank.account", string="客户账户")

    def unlink(self):
        for record in self:
            if record.sale_order_id:
                if record.sale_order_id.amount_paid >= record.amount:
                    record.sale_order_id.write({"amount_paid": record.sale_order_id.amount_paid - record.amount})
                    # 将明细行中的sale_line_id 解绑
                    for line in record.payment_line_ids:
                        line.sale_line_id = False
                    record.sale_order_id.line_ids._compute_pay_amount()
        return super(InheritRokeMesPayment, self).unlink()

    @api.model
    def create(self, vals):
        res = super(InheritRokeMesPayment, self).create(vals)
        if vals.get("sale_order_id", False):
            self.env['roke.sale.order'].browse(vals.get("sale_order_id", False)).write({"payment_ids": [(4, res.id)]})
        return res

    def confirm(self):
        """
        确认收付款，创建记账明细
        :return:
        """
        # 收款单过账
        self.make_confirm()
        # 向关联销售单发送
        self.post_message_to_sale_order()

    def post_message_to_sale_order(self):
        self.sale_order_id.message_post_with_view(
            'roke_mes_sale.roke_mes_backorder_message_origin_link',
            values={'self': self.sale_order_id, 'origin': self},
            subtype_id=self.env.ref('mail.mt_note').id
        )


class InheritRokeSaleOrderDetail(models.Model):
    _inherit = 'roke.mes.payment.line'

    sale_line_id = fields.Many2one("roke.sale.order.line", string="销售明细")


class InheritRokeMesAccountMove(models.Model):
    _inherit = "roke.mes.account.move"

    sale_id = fields.Many2one("roke.sale.order", string="销售订单", index=True, ondelete='cascade')


class InheritRokeMesSaleOrder(models.Model):
    _inherit = 'roke.sale.order'

    def _get_is_open_tax(self):
        return self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)

    payment_ids = fields.Many2many(
        "roke.mes.payment", "roke_sale_payment_rel", "so_id", "pay_id", string="收款单"
    )
    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('优惠金额', digits='XSJE')
    amount_after_discount = fields.Float('优惠后金额', digits='XSJE', compute='_compute_discount_amount_total')
    is_open_tax = fields.Boolean('启用税率', compute='_compute_is_open_tax', default=_get_is_open_tax)
    payment_count = fields.Integer(string="收款单数量", compute="_compute_payment_count")
    pay_state = fields.Selection([('已收款', '已收款'), ('部分收款', '部分收款'), ('未收款', '未收款')],
                                 default='未收款', string='收款状态', compute='_compute_pay_state', store=True)
    amount_paid = fields.Float('已收金额', digits='XSJE')
    amount_unpaid = fields.Float('未收金额', compute='_compute_amount_unpaid', digits='XSJE')
    discount_amount_total = fields.Float(string='折扣后金额合计', digits='XSJE',
                                         compute='_compute_discount_amount_total')
    payment_plan_ids = fields.One2many('roke.mes.sale.payment.plan', 'sale_id', string="收款计划")

    @api.depends('line_ids', 'line_ids.after_discount_amount', 'line_ids.order_qty', 'line_ids.price_unit',
                 'discount_amount', 'line_ids.discount_rate', 'line_ids.discount_amount',
                 'discount_rate')
    def _compute_discount_amount_total(self):
        for rec in self:
            after_discount_amount = sum(rec.line_ids.mapped('after_discount_amount'))
            rec.discount_amount_total = after_discount_amount
            rec.amount_after_discount = after_discount_amount - rec.discount_amount

    @api.depends('total_price', 'amount_after_discount', 'amount_paid')
    def _compute_amount_unpaid(self):
        for record in self:
            record.amount_unpaid = 0
            if record.amount_after_discount:
                record.amount_unpaid = record.amount_after_discount - record.amount_paid or 0
            else:
                record.amount_unpaid = record.total_price - record.amount_paid or 0

    @api.depends('amount_paid', 'amount_unpaid', 'amount_after_discount')
    def _compute_pay_state(self):
        for record in self:
            if record.amount_after_discount:
                if record.amount_paid >= record.amount_after_discount:
                    record.pay_state = '已收款'
                elif record.amount_paid < record.amount_after_discount and record.amount_paid != 0:
                    record.pay_state = '部分收款'
                else:
                    record.pay_state = '未收款'
            else:
                if record.amount_paid >= record.total_price:
                    record.pay_state = '已收款'
                elif record.amount_paid < record.total_price and record.amount_paid != 0:
                    record.pay_state = '部分收款'
                else:
                    record.pay_state = '未收款'

    @api.constrains('payment_plan_ids')
    def _check_payment_plan(self):
        for rec in self:
            date_list = [line.estimated_payment_date for line in rec.payment_plan_ids]
            set_date_list = list(set(date_list)) if date_list else date_list
            if len(date_list) != len(set_date_list):
                raise ValidationError("收款计划中预计付款日期不能相同")

    def action_multi_order_deduct(self):
        """
        批量订单优惠
        :return:
        """
        active_order_ids = self.env["roke.sale.order"].search([
            ("id", "in", self._context.get('active_ids', [])), ("state", "!=", '取消')
        ])
        # return_sale_records = active_order_ids.filtered(lambda x: x.is_return_sale)
        # if return_sale_records:
        #     raise ValidationError('退销订单不可批量收款')
        # if len(active_order_ids.mapped("customer_id").ids) > 1:
        #     raise ValidationError("请选择同一客户的销售订单进行批量收款")
        deduct_line_ids = self.prepare_line_ids(active_order_ids.line_ids)
        return self.action_wizard_order_deduct(deduct_line_ids)

    def _compute_payment_count(self):
        for record in self:
            record.payment_count = len(record.payment_ids)

    def action_view_payment(self):
        result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_account.view_roke_mes_collection_action')
        result['context'] = {'default_partner_id': self.customer_id.id}
        payment_ids = self.mapped('payment_ids')
        result_ids = self.env["roke.mes.collection"].search([("payment_id", "in", payment_ids.ids)])
        if not result_ids or len(result_ids) > 1:
            result['domain'] = "[('id','in',%s)]" % (result_ids.ids)
        elif len(result_ids) == 1:
            res = self.env.ref('roke_mes_account.view_roke_mes_collection_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = result_ids.id
        return result

    @api.onchange('discount_amount')
    def _onchange_discount_amount(self):
        if not self.env.context.get('calculate_discount'):
            # 处理超过优惠金额
            after_discount_amount = sum(self.line_ids.mapped('after_discount_amount'))
            if after_discount_amount:
                self.discount_rate = round(self.discount_amount / after_discount_amount, 6)
            # else:
            #     raise ValidationError('折扣后金额总计为0，无法继续优惠！')
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('discount_rate')
    def _onchange_discount_rate(self):
        if not self.env.context.get('calculate_discount'):
            after_discount_amount = sum(self.line_ids.mapped('after_discount_amount'))
            self.discount_amount = self.discount_rate * after_discount_amount
            # 处理超过优惠金额
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('line_ids', 'line_ids.after_discount_amount', 'line_ids.order_qty', 'line_ids.price_unit',
                  'discount_amount', 'line_ids.discount_rate', 'line_ids.discount_amount',
                  'discount_rate'
                  )
    def _onchange_account_discount(self):
        # 计算方式：（折扣后金额合计-优惠金额）*该明细折扣后金额/折扣后总金额=整单优惠
        for line in self.line_ids:
            if self.discount_amount_total:
                whole_order_offer = self.discount_amount * (
                        line.after_discount_amount / self.discount_amount_total)
            else:
                whole_order_offer = 0
            line.whole_order_offer = whole_order_offer

    def _compute_is_open_tax(self):
        # 税率是否启用
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        for record in self:
            record.is_open_tax = is_tax

    @staticmethod
    def prepare_line_ids(line_ids):
        """
        归集明细行数据
        :param line_ids:
        :return:
        """
        deduct_line_ids = []
        for line in line_ids:
            if line.deducted_amount + line.paid_amount != line.subtotal:
                deduct_line_ids.append((0, 0, {
                    "order_line_id": line.id, "unpaid_amount": line.amount_receivable - line.paid_total,
                    "pay_amount": line.amount_receivable,
                    "current_paid_amount": line.amount_receivable - line.paid_total,
                    "deduct_amount": line.discount_amount + line.whole_order_offer
                }))
        return deduct_line_ids

    def action_wizard_order_deduct(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        :param deduct_line_ids:
        :param discount_rate:
        :param discount_amount:
        :param amount_after_discount:
        :return:
        """
        wizard_id = self.env["wizard.sale.order.deduct"].create({
            "discount_rate": discount_rate,
            "discount_amount": discount_amount,
            "amount_after_discount": amount_after_discount,
            "deduct_line_ids": deduct_line_ids
        })
        if len(self._context.get('active_ids', [])) > 1:
            return {
                'name': '批量收款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.sale.order.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_sale.view_wizard_sale_order_deduct_batch_form').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }
        else:
            return {
                'name': '收款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.sale.order.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_sale.view_wizard_sale_order_deduct_form').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }

    def action_order_deduct(self):
        """
        订单优惠
        :return:
        """
        deduct_line_ids = self.prepare_line_ids(self.line_ids)
        return self.action_wizard_order_deduct(deduct_line_ids, self.discount_rate, self.discount_amount,
                                               self.amount_after_discount)

    def app_action_collection(self):
        """
        app创建收款单
        :return:
        """
        deduct_line_ids = self.prepare_line_ids(self.line_ids)
        self.env["wizard.sale.order.deduct"].create({
            "discount_rate": self.discount_rate,
            "discount_amount": self.discount_amount,
            "amount_after_discount": self.amount_after_discount,
            "deduct_line_ids": deduct_line_ids
        }).app_action_confirm_deduct(self.id)

    def write(self, vals):
        # 保存的时候根据优惠金额优惠后金额计算优惠率

        res = super(InheritRokeMesSaleOrder, self).write(vals)
        if self._context.get('loopbreaker'):
            return True
        self = self.with_context(loopbreaker=True)
        for record in self:
            after_discount_amount = sum(record.line_ids.mapped('after_discount_amount'))
            discount_amount = record.discount_amount
            record._update_discount_rate(discount_amount, after_discount_amount)
        return res

    def _update_discount_rate(self, discount_amount, after_discount_amount):
        if after_discount_amount:
            self.discount_rate = round(discount_amount / after_discount_amount, 6)
        else:
            self.discount_rate = 0.0

    @api.model
    def create(self, vals):
        res = super(InheritRokeMesSaleOrder, self).create(vals)
        after_discount_amount = sum(res.line_ids.mapped('after_discount_amount'))
        if after_discount_amount:
            res.discount_rate = round(res.discount_amount / after_discount_amount, 6)
        else:
            res.discount_rate = 0.0
        return res


class InheritRokeMesSaleOrderLine(models.Model):
    _inherit = 'roke.sale.order.line'

    payment_line_ids = fields.One2many("roke.mes.payment.line", "sale_line_id", string="收款明细")
    deducted_amount = fields.Float(string="已优惠金额", digits='XSJE', compute="_compute_pay_amount")
    paid_amount = fields.Float(string="已收金额", digits='XSJE', compute="_compute_pay_amount", store=True)

    paid_total = fields.Float(string="已收款", digits='XSJE', compute="_compute_pay_amount")
    unpaid_amount = fields.Float(string="未收款", digits='XSJE', compute="_compute_pay_amount")

    # 税率相关
    tax_rate = fields.Float('税率')
    unit_price_excl_tax = fields.Float('不含税单价', digits='XSDJ')
    amount_excl_tax = fields.Float('不含税金额', digits='XSJE', store=True, compute='_compute_amount_excl_tax')
    tax_amount = fields.Float('税额', digits='XSJE', store=True, compute='_compute_amount_excl_tax')

    discount_rate = fields.Float('折扣率', store="True")
    discount_amount = fields.Float('折扣额', digits='XSJE', store="True")
    after_discount_amount = fields.Float('折扣后金额', digits='XSJE', compute='_compute_after_discount_amount', store="True")
    whole_order_offer = fields.Float('整单优惠', digits='XSJE', store="True")
    amount_receivable = fields.Float('应收金额', digits='XSJE', compute='_compute_after_discount_amount', store="True")

    @api.onchange('product_id')
    def _onchange_tax_rate(self):
        if self.order_id.is_open_tax:
            self.tax_rate = self.product_id.tax_rate
        else:
            self.tax_rate = 0

    @api.onchange('unit_price_excl_tax')
    def _onchange_tax_rate_next(self):
        tax_rate = (self.price_unit - self.unit_price_excl_tax) / self.price_unit * 100 if self.price_unit and self.price_unit > 0 else 0
        if self.unit_price_excl_tax > self.price_unit:
            raise ValidationError('不含税单价大于产品单价!')
        if self.tax_rate != tax_rate:
            self.tax_rate = tax_rate

    @api.onchange('price_unit', 'tax_rate')
    def _onchange_unit_price_excl_tax(self):
        if self.tax_rate > 100:
            raise ValidationError('税率禁止大于100!')
        if self.tax_rate < 0:
            raise ValidationError('税率禁止为负数!')
        if self.order_id.is_open_tax:
            unit_price_excl_tax = self.price_unit - self.price_unit * self.tax_rate / 100
            self.unit_price_excl_tax = unit_price_excl_tax
        else:
            self.unit_price_excl_tax = 0

    @api.depends('unit_price_excl_tax', 'order_qty', 'amount_excl_tax', 'tax_amount')
    def _compute_amount_excl_tax(self):
        for rec in self:
            if rec.order_id.is_open_tax:
                rec.amount_excl_tax = rec.unit_price_excl_tax * rec.order_qty
                if rec.tax_rate:
                    rec.tax_amount = (rec.price_unit - rec.unit_price_excl_tax) * rec.order_qty
                else:
                    rec.tax_amount = 0
            else:
                rec.amount_excl_tax = 0
                rec.tax_amount = 0

    @api.onchange('discount_amount')
    def _onchange_discount_amount(self):
        self.discount_rate = (self.discount_amount / self.subtotal) * 100 if self.subtotal and self.subtotal > 0 else 0

    @api.onchange('discount_rate')
    def _onchange_discount_rate(self):
        self.discount_amount = self.subtotal * self.discount_rate / 100
        # self.order_id._compute_discount_amount_total()

    @api.onchange('price_unit', 'order_qty')
    def _onchange_account_qty_price(self):
        subtotal = self.price_unit * self.order_qty
        self.discount_amount = subtotal * self.discount_rate / 100

    @api.depends("payment_line_ids", "payment_line_ids.deducted_amount", "payment_line_ids.paid_amount")
    def _compute_pay_amount(self):
        for record in self:
            # active_payment_line_ids = record.payment_line_ids.filtered(lambda pl: pl.payment_id.state == "已过账")
            active_payment_line_ids = record.payment_line_ids
            record.deducted_amount = sum(active_payment_line_ids.mapped("deducted_amount"))
            record.paid_amount = sum(active_payment_line_ids.mapped("paid_amount"))
            record.paid_total = record.paid_amount + record.deducted_amount
            record.unpaid_amount = record.subtotal - record.paid_total

    @api.depends('subtotal', 'discount_amount', 'whole_order_offer')
    def _compute_after_discount_amount(self):
        """
            计算方式：折扣后金额=金额-折扣额-整单优惠
            当折扣率为0时，该字段默认不进行折扣，折扣后金额取小计字段数据
	    """
        for rec in self:
            if rec.discount_rate > 0:
                rec.after_discount_amount = rec.subtotal - rec.discount_amount
                rec.amount_receivable = rec.subtotal - rec.discount_amount - rec.whole_order_offer
            else:
                rec.after_discount_amount = rec.subtotal
                rec.amount_receivable = rec.subtotal - rec.whole_order_offer

class InheritRokeMesCollection(models.Model):
    _inherit = 'roke.mes.collection'

    @api.onchange("partner_id")
    def _onchange_customer_id(self):
        ls = self.env["roke.partner.bank.account"].search([("partner_id.id", "=", self.partner_id.id)])
        if len(ls) <= 1:
            self.bank_account_id = ls
        else:
            self.bank_account_id = ls[0]


class InheritRokeMesPay(models.Model):
    _inherit = 'roke.mes.pay'

    @api.onchange("partner_id")
    def _onchange_customer_id(self):
        ls = self.env["roke.partner.bank.account"].search([("partner_id.id", "=", self.partner_id.id)])
        if len(ls) <= 1:
            self.bank_account_id = ls
        else:
            self.bank_account_id = ls[0]

class RokeMesSalePaymentPlan(models.Model):
    _name = "roke.mes.sale.payment.plan"

    sale_id = fields.Many2one("roke.sale.order", string="销售订单", index=True, ondelete='cascade')
    payment_stage_type_id = fields.Many2one("roke.pay.stage.type", string="收款阶段")
    paid_amount = fields.Float(string="收款金额", digits='Account')
    estimated_payment_date = fields.Date(string="预计收款日期")

    @api.constrains('paid_amount', 'estimated_payment_date')
    def _check_paid_amount(self):
        for rec in self:
            if rec.paid_amount and rec.paid_amount < 0:
                raise ValidationError('收款金额不能小于0')
            if rec.estimated_payment_date and rec.estimated_payment_date < datetime.date.today():
                raise ValidationError('预计收款日期不能早于今天')