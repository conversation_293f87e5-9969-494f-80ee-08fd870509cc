# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import datetime


class InheritRokeSaleQuotation(models.Model):
    _inherit = "roke.sale.quotation"

    def get_line_data(self, line):
        res = super(InheritRokeSaleQuotation, self).get_line_data(line)
        res.update({
            'tax_rate': line.product_id.tax_rate,
            'unit_price_excl_tax': line.sale_price - line.sale_price * line.product_id.tax_rate / 100
        })
        return res