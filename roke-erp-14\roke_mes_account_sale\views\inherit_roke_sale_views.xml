<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 采购收款按钮-->
    <record id="inherit_view_roke_sale_order_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_sale_order_form</field>
        <field name="model">roke.sale.order</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object" access="1" smart_button="查看收款单"
                            name="action_view_payment"
                            class="oe_stat_button"
                            icon="fa-money" attrs="{'invisible':[('payment_ids','=',[])]}">
                        <field name="payment_count" widget="statinfo" string="收款单" help="查看收款单"/>
                        <field name="payment_ids" invisible="1"/>
                    </button>
            </xpath>
            <xpath expr="//button[@name='button_purchase_stock']" position="after">
                <button name="action_order_deduct" string="收款" type="object"
                        attrs="{'invisible': ['|',('state', '!=',  '确认'),('sale_type','=','退货')]}"
                        class="oe_read_only"/>
            </xpath>
            <xpath expr="//group[@id='g2']" position="after">
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="discount_amount" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1"/>
                    </group>
                    <field name="is_open_tax" invisible="1"/>
                </group>
            </xpath>
            <xpath expr="//page[@name='line_ids']" position="after">
                <page string="收款计划" name="payment_plan_ids">
                    <field name="payment_plan_ids" attrs="{'readonly': [('state', '!=', '草稿')]}"
                           context="{'tree_view_ref': 'view_roke_sale_payment_plan_tree'}"/>
                </page>
            </xpath>
        </field>
    </record>

    <record id="view_roke_sale_payment_plan_tree" model="ir.ui.view">
        <field name="name">roke.mes.sale.payment.plan.tree</field>
        <field name="model">roke.mes.sale.payment.plan</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <tree editable="bottom">
                <field name="sale_id" invisible="1"/>
                <field name="payment_stage_type_id" required="1"/>
                <field name="paid_amount"/>
                <field name="estimated_payment_date" required="1"/>
            </tree>
        </field>
    </record>

    <record id="inherit_view_roke_sale_order_tree" model="ir.ui.view">
        <field name="name">inherit_view_roke_sale_order_tree</field>
        <field name="model">roke.sale.order</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="after">
                <field name="pay_state"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_sale_order_line_tree" model="ir.ui.view">
        <field name="name">inherit_view_roke_sale_order_line_tree</field>
        <field name="model">roke.sale.order.line</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_order_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='deliver_type']" position="after">
                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}" optional="show"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}" optional="show"/>
                <field name="amount_excl_tax" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}" optional="show"/>
                <field name="tax_amount" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='wait_deliver']" position="after">
                <field name="discount_rate"/>
                <field name="discount_amount" sum="折扣额合计"/>
                <field name="after_discount_amount" force_save="1" readonly="1" sum="折扣后金额合计"/>
                <field name="whole_order_offer" force_save="1" readonly="1" sum="整单优惠合计"/>
                <field name="amount_receivable" force_save="1" readonly="1" sum="应收金额合计"/>
            </xpath>
        </field>
    </record>

    <!-- 批量优惠收款 -->
    <record id="action_multi_sale_deduct" model="ir.actions.server">
        <field name="name">批量收款</field>
        <field name="model_id" ref="model_roke_sale_order"/>
        <field name="binding_model_id" ref="model_roke_sale_order"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="binding_view_types">list</field>
        <field name="code">
            action = model.action_multi_order_deduct()
        </field>
    </record>
</odoo>
