# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class RokeAccountPayment(models.TransientModel):
    _name = 'roke.account.payment'
    _rec_name = 'supplier_id'

    date_start = fields.Date('开始日期')
    date_end = fields.Date('结束日期')
    supplier_id = fields.Many2one('roke.partner', string='客户', domain=[("customer", "=", True)])

    def confirm(self):
        if self.supplier_id:
            # 查询这个供应商这个日期内的所有发票和付款单
            invoice = self.env['roke.sale.invoice'].search(
                [('customer_id', '=', self.supplier_id.id),
                 ('invoice_date', '>=', self.date_start),
                 ('invoice_date', '<=', self.date_end)]).mapped('amount_total')
            # 付款单
            payment = self.env['roke.mes.payment'].search(
                [('partner_id', '=', self.supplier_id.id),
                 ('payment_date', '>=', self.date_start),
                 ('payment_date', '<=', self.date_end),
                 ('payment_type', '=', '收款')]).mapped('amount')
            data = {"supplier_id": self.supplier_id.id,
                    "date_start": self.date_start,
                    "date_end": self.date_end,
                    "line_ids": [(0, 0, {
                        "supplier_id": self.supplier_id.id,
                        "date_start": self.date_start,
                        "date_end": self.date_end,
                        "current_payable": sum(invoice),
                        "current_payment": sum(payment),
                        "opening_balance": 0,
                        "closing_balance": 0,
                    })]}
            res = self.env['roke.account.payment.result.order'].create(data)
        else:
            # 查询所有供应商
            supplier_res = self.env['roke.partner'].search([('supplier', '=', True)])
            data_list = []
            for supplier in supplier_res:
                invoice = self.env['roke.sale.invoice'].search(
                    [('customer_id', '=', supplier.id),
                     ('invoice_date', '>=', self.date_start),
                     ('invoice_date', '<=', self.date_end)]).mapped('amount_total')
                payment = self.env['roke.mes.payment'].search(
                    [('partner_id', '=', supplier.id),
                     ('payment_date', '>=', self.date_start),
                     ('payment_date', '<=', self.date_end),
                     ('payment_type', '=', '付款')]).mapped('amount')
                data_list.append((0, 0, {
                    "supplier_id": supplier.id,
                    "date_start": self.date_start,
                    "date_end": self.date_end,
                    "current_payable": sum(invoice),
                    "current_payment": sum(payment),
                    "opening_balance": 0,
                    "closing_balance": 0,
                }))
            data = {
                "date_start": self.date_start,
                "date_end": self.date_end,
                "line_ids": data_list}
            res = self.env['roke.account.payment.result.order'].create(data)

        return {
            'type': 'ir.actions.act_window',
            'name': "应收账款汇总查询",
            'res_model': 'roke.account.payment.result.order',
            'res_id': res.id,
            'view_mode': 'form',
            'target': 'current',
            'views': [[self.env.ref('roke_mes_account_sale.wizard_account_payment_result_views').id, 'form']]
        }

    def action_open_wizard(self):
        return {
            'type': 'ir.actions.act_window',
            'name': "应收账款汇总查询",
            'res_model': 'roke.account.payment',
            # 'res_id': verify_id.id,
            'view_mode': 'form',
            'target': 'new',
            # 'context': {'default_verify_id': verify_id.id, "create": False},
            'views': [[self.env.ref('roke_mes_account_sale.wizard_account_payment_views').id, 'form']]
        }


class RokeAccountPaymentResultOrder(models.TransientModel):
    _name = 'roke.account.payment.result.order'
    _rec_name = 'supplier_id'

    date_start = fields.Date('开始日期')
    date_end = fields.Date('结束日期')
    supplier_id = fields.Many2one('roke.partner', string='客户')
    line_ids = fields.One2many('roke.account.payment.result', 'order_id', string='明细')


class RokeAccountPaymentResult(models.TransientModel):
    _name = 'roke.account.payment.result'

    order_id = fields.Many2one('roke.account.payment.result.order')
    supplier_id = fields.Many2one('roke.partner', string='客户')
    supplier_code = fields.Char(related='supplier_id.code', string='供应商编号')
    date_start = fields.Date('开始日期')
    date_end = fields.Date('结束日期')
    current_payable = fields.Float('本期收付', digits='YSYFJE')
    current_payment = fields.Float('本期收款', digits='YSYFJE')
    opening_balance = fields.Float('期初余额', digits='YSYFJE')
    closing_balance = fields.Float('期末余额', digits='YSYFJE')
