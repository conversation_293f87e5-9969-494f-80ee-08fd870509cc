# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class WizardSaleOrderDeduct(models.TransientModel):
    _name = "wizard.sale.order.deduct"
    _description = "销售订单收款"

    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('优惠金额', digits='YSYFJE')
    amount_after_discount = fields.Float('优惠后金额', digits='YSYFJE')
    deduct_line_ids = fields.One2many("wizard.sale.order.deduct.line", "deduct_id", string="订单明细")

    def action_deduct_payment(self, result_id):
        """
        打开收款单
        :param result_id: 收款单
        :return:
        """
        if len(result_id) > 1:
            return {
                'name': '本次创建的收款单',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': 'roke.mes.collection',
                'domain': [('id', 'in', result_id)],
                'views': [
                    (self.env.ref('roke_mes_account.view_roke_mes_collection_tree').id, 'tree'),
                    (self.env.ref('roke_mes_account.view_roke_mes_collection_form').id, 'form')
                ],
                'context': {'create': True, 'edit': True, 'delete': True}
            }
        else:
            return {
                'name': '本次创建的收款单',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': 'roke.mes.collection',
                'res_id': result_id[0],
                'context': {'create': True, 'edit': True, 'delete': True}
            }

    def action_confirm_deduct(self):
        """
        确认优惠，生成付款单
        :return:
        """
        result_ids = []
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部收款或添加明细数据。')
        # 归集销售订单
        sale_order_ids = self.deduct_line_ids.mapped("order_line_id").mapped("order_id")
        for sale in sale_order_ids:
            # 客户
            customer_id = sale.customer_id
            # 取销售订单下明细
            line_dict_values = []
            # 过滤当前销售订单下的产品收款明细
            deduct_line_ids = self.deduct_line_ids.filtered(lambda r: r.order_line_id.order_id.id == sale.id)
            for deduct in deduct_line_ids:
                line_dict_values.append((0, 0, {
                    "sale_line_id": deduct.order_line_id.id,
                    "deducted_amount": 0,
                    "paid_amount": deduct.current_paid_amount
                }))
            # 收款单备注
            note = "来源单据:{}".format(sale.code)
            # 查询预收款单id
            pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '预收款')])
            # 是否优惠
            is_deduct = True if sale.amount_after_discount else False
            # 收款金额
            total_payment = sum(deduct_line_ids.mapped("current_paid_amount"))
            red_type = '收款' if sale.sale_type == '销货' else '退款'
            banks = customer_id.bank_ids if customer_id else False
            bank_id = banks[0].id if banks else ''
            result_id = self.env["roke.mes.collection"].create({
                "state": "草稿",
                "payment_type": "收款",
                "partner_type": "客户",
                "partner_id": customer_id.id if customer_id else False,
                "payment_date": fields.Date.context_today(self),
                "amount": total_payment,
                "pay_amount": total_payment,
                "is_edit": True,
                "is_deduct": is_deduct,
                "note": note,
                "payment_line_ids": line_dict_values,
                "is_payment": True,
                'order_type': pay_id.id,
                'is_advance_payment': True,
                'origin_order': sale.code,
                'red_type': red_type,
                'bank_account_id': bank_id,
                "sale_order_id": sale.id
            })
            _amount_paid = sale.amount_paid
            _amount_paid += total_payment
            # 销售订单关联收款单
            sale.write({"payment_ids": [(4, result_id.payment_id.id)], "amount_paid": _amount_paid})
            result_ids.append(result_id.id)
        return self.action_deduct_payment(result_ids)

    def app_action_confirm_deduct(self,sale_id):
        """
        app确认优惠，生成付款单
        :return:
        """
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部收款或添加明细数据。')
        total_payment = 0
        sale_order_ids = self.deduct_line_ids.mapped("order_line_id").mapped("order_id")
        customer_id = self.deduct_line_ids[0].order_line_id.order_id.customer_id.id if self.deduct_line_ids else False
        line_dict_values = []
        for item in self.deduct_line_ids:
            total_payment += item.pay_amount
            line_dict_values.append((0, 0, {
                "sale_line_id": item.order_line_id.id,
                "deducted_amount": 0, "paid_amount": item.current_paid_amount,
            }))
        # 创建收款单
        note = "来源单据:{}".format("\n".join(sale_order_ids.mapped("code")))
        # 查询预收款单id
        pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '预收款')])
        # 是否优惠
        is_deduct = True if self.amount_after_discount else False
        # 如果优惠后金额不为0说明有优惠
        # if self.amount_after_discount > 0:
        total_payment = sum(self.deduct_line_ids.mapped("current_paid_amount"))
        sale_order = self.env['roke.sale.order'].search([('id', '=', sale_id)])
        red_type = '收款' if sale_order.sale_type == '销货' else '退款'
        result_id = self.env["roke.mes.collection"].create({
            "state": "草稿",
            "payment_type": "收款",
            "partner_type": "客户",
            "partner_id": customer_id,
            "payment_date": fields.Date.context_today(self),
            "amount": total_payment,
            "pay_amount": total_payment,
            "is_edit": True,
            "is_deduct": is_deduct,
            "note": note,
            "payment_line_ids": line_dict_values,
            "is_payment": True,
            'order_type': pay_id.id,
            'is_advance_payment': True,
            'origin_order': sale_order.code,
            'red_type': red_type
        })
        # 采购单关联付款单
        sale_order_ids.write({"payment_ids": [(4, result_id.payment_id.id)]})


class WizardSaleOrderDeductLine(models.TransientModel):
    _name = "wizard.sale.order.deduct.line"
    _description = "收款明细"

    deduct_id = fields.Many2one("wizard.sale.order.deduct", string="优惠向导")
    order_line_id = fields.Many2one("roke.sale.order.line", string="产品", required=True)
    product_id = fields.Many2one(related="order_line_id.product_id", string="产品")
    unit_price = fields.Float(related="order_line_id.price_unit", digits='YSYFDJ')
    qty = fields.Float(related="order_line_id.order_qty", string="数量", digits='YSYFSL')
    subtotal = fields.Float(related="order_line_id.subtotal", string="金额", digits='YSYFJE')
    deducted_amount = fields.Float(related="order_line_id.deducted_amount", string="已优惠金额", digits='YSYFJE')
    paid_amount = fields.Float(related="order_line_id.paid_amount", string="已收款金额", digits='YSYFJE')

    unpaid_amount = fields.Float(string="待收款金额", digits='XSJE')
    deduct_amount = fields.Float(string="优惠金额", digits='XSJE')
    pay_amount = fields.Float(string="本次收款金额", digits='XSJE')

    after_discount_amount = fields.Float(related="order_line_id.after_discount_amount", string="折扣后金额", digits='XSJE')
    whole_order_offer = fields.Float(related="order_line_id.whole_order_offer", string="整单优惠", digits='XSJE')
    amount_receivable = fields.Float(related="order_line_id.amount_receivable", string="应收金额", digits='XSJE')
    current_paid_amount = fields.Float(string="本次收款金额", digits='XSJE')

    @api.onchange("deduct_amount")
    def onchange_deduct_amount(self):
        if self.deduct_amount > self.unpaid_amount:
            raise ValidationError("优惠金额不能大于待收款金额")
        self.pay_amount = round(self.unpaid_amount - self.deduct_amount, 2)

    @api.onchange("pay_amount")
    def onchange_pay_amount(self):
        if self.pay_amount > round(self.unpaid_amount - self.deduct_amount, 2):
            raise ValidationError("收款金额不能大于优惠后金额")
