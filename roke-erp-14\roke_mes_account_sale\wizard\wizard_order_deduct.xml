<?xml version="1.0" encoding="UTF-8" ?>
<odoo>

    <record id="view_wizard_sale_order_deduct_form" model="ir.ui.view">
        <field name="name">wizard.sale.order.deduct.form</field>
        <field name="model">wizard.sale.order.deduct</field>
        <field name="arch" type="xml">
            <form string="收款">
                <field name="deduct_line_ids" nolabel="1">
                    <tree editable="bottom" create="false" delete="false">
                        <field name="deduct_id" invisible="1"/>
                        <field name="order_line_id" readonly="1" invisible="1"/>
                        <field name="product_id" readonly="1"/>
                        <field name="unit_price" readonly="1"/>
                        <field name="qty" readonly="1"/>
                        <field name="subtotal" sum="合计" readonly="1"/>
                        <field name="after_discount_amount" sum="合计" readonly="1"/>
                        <field name="whole_order_offer" sum="合计" readonly="1"/>
                        <field name="amount_receivable" sum="合计" readonly="1"/>
                        <field name="unpaid_amount" sum="合计" readonly="1"/>
                        <field name="current_paid_amount"/>
                    </tree>
                </field>
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" readonly="1"/>
                    </group>
                    <group>
                        <field name="discount_amount" readonly="1"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1"/>
                    </group>
                </group>
                <footer>
                    <button name="action_confirm_deduct" string="确认" type="object" class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <record id="view_wizard_sale_order_deduct_batch_form" model="ir.ui.view">
        <field name="name">wizard.sale.order.deduct.batch.form</field>
        <field name="model">wizard.sale.order.deduct</field>
        <field name="arch" type="xml">
            <form string="批量收款">
                <field name="deduct_line_ids" nolabel="1">
                    <tree editable="bottom" create="false" delete="false">
                        <field name="deduct_id" invisible="1"/>
                        <field name="order_line_id" readonly="1" invisible="1"/>
                        <field name="product_id" readonly="1"/>
                        <field name="unit_price" readonly="1"/>
                        <field name="qty" readonly="1"/>
                        <field name="subtotal" sum="合计" readonly="1"/>
                        <field name="after_discount_amount" sum="合计" readonly="1"/>
                        <field name="whole_order_offer" sum="合计" readonly="1"/>
                        <field name="amount_receivable" sum="合计" readonly="1"/>
                        <field name="unpaid_amount" sum="合计" readonly="1"/>
                        <field name="current_paid_amount"/>
                    </tree>
                </field>
                <footer>
                    <button name="action_confirm_deduct" string="确认" type="object" class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

</odoo>
