<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="view_wizard_sale_invoice_pay_form" model="ir.ui.view">
        <field name="name">wizard.sale.invoice.pay.form</field>
        <field name="model">wizard.sale.invoice.pay</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="invoice_id" readonly="1"/>
                        <field name="customer_id"/>
                        <field name="amount_unpaid"/>
                        <field name="amount_total" force_save="1"/>
                    </group>
                    <group>
                        <field name="invoice_date"/>
                        <field name="invoice_date_due"/>
                    </group>
                </group>
                <group string="发票明细">
                    <group cols="4">
                        <div>
                            <button name='mark_all' string='全选' type='object' class='oe_highlight'/>
                            <button name='clear_mark' string='清除选择' type='object'/>
                        </div>
                    </group>
                </group>
                <group>
                    <field name="invoice_line_ids" nolabel="1">
                        <tree editable="bottom" create="false" delete="false">
                            <field name="wizard_id" invisible="1"/>
                            <field name="is_mark"/>
                            <field name="invoice_line_id" invisible="1"/>
                            <field name="product_id"/>
                            <field name="name"/>
                            <field name="quantity"/>
                            <field name="price_unit"/>
                            <field name="subtotal"/>
                            <field name="amount_verified"/>
                            <field name="amount_unverified"/>
                        </tree>
                    </field>
                </group>
                <footer>
                    <button name="action_payment" string="收款" type="object" class="oe_highlight"/>
                    <button string="取消" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record id="action_wizard_sale_invoice_pay" model="ir.actions.act_window">
        <field name="name">销售发票收款</field>
        <field name="res_model">wizard.sale.invoice.pay</field>
        <field name="view_mode">form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="target">current</field>
    </record>

</odoo>
