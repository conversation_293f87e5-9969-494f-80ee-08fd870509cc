# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class WizardSaleInvoiceVerify(models.TransientModel):
    _name = "wizard.sale.invoice.verify"
    _description = "发票核销"

    partner_id = fields.Many2one("roke.partner", string="业务伙伴", domain=[('customer', '=', True)])
    start_date = fields.Date(string="起始日期")
    end_date = fields.Date(string="截止日期")
    payment_id = fields.Many2one("roke.mes.payment", string="选择收款单")

    sum_amount_verify = fields.Float("本次核销金额", digits='YSYFJE', compute="_compute_sum_amount_verify")

    verify_payment_ids = fields.One2many("wizard.sale.invoice.verify.payment", "verify_id", string="收款单")
    verify_invoice_line_ids = fields.One2many("wizard.sale.invoice.verify.invoice", "verify_id", string="发票")

    @api.depends("verify_invoice_line_ids.amount_verify", "verify_invoice_line_ids.is_mark")
    def _compute_sum_amount_verify(self):
        for record in self:
            sum_amount_verify = record.verify_payment_ids.amount_verify
            mark_line = record.verify_invoice_line_ids.filtered(lambda a: a.is_mark)
            record.sum_amount_verify = sum(mark_line.mapped("amount_verify"))
            if record.verify_payment_ids.amount_unverified >= record.sum_amount_verify:
                record.verify_payment_ids.amount_verify = record.sum_amount_verify
            else:
                record.verify_payment_ids.amount_verify = sum_amount_verify

    @api.onchange("partner_id", "start_date", "end_date")
    def _change_payment_id_domain(self):
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                return {
                    'warning': {'message': '起始日期不可大于截止日期'},
                    'value': {'start_date': False, 'end_date': False}
                }
        if not self.partner_id:
            self.payment_id = False
            payment_ids = []
        else:
            self.payment_id = False
            domain = [("partner_id", "=", self.partner_id.id), ("partner_type", "=", "客户")]
            if self.start_date:
                domain.append(("payment_date", ">=", self.start_date))
            if self.end_date:
                domain.append(("payment_date", "<=", self.end_date))
            payment_records = self.env["roke.mes.payment"].search(domain)
            # 过滤出未核销的收款单
            payment_ids = payment_records.filtered(
                lambda pr: pr.amount > sum(
                    self.env["roke.sale.invoice.verify"].search([
                        ("payment_id", "=", pr.id)
                    ]).mapped("amount_verified")
                )
            ).ids
        order_domain = [('id', 'in', payment_ids)]
        domain = {'payment_id': order_domain}
        return {'domain': domain}

    @api.onchange("payment_id", "partner_id")
    def _change_fields(self):
        partner = self.partner_id
        payment = self.payment_id
        start_date = self.start_date
        end_date = self.end_date
        self.verify_payment_ids.unlink()
        self.verify_invoice_line_ids.unlink()
        records = self.env["roke.sale.invoice"].search([("customer_id", "=", partner.id)])
        verify_invoice_line_ids = []
        verify_payment_ids = []
        unpaid_invoices = records.filtered(lambda r: r.amount_unpaid and r.state == "确认")
        unverified_lines = unpaid_invoices.invoice_line_ids.filtered(
            lambda il: il.subtotal > sum(
                self.env["roke.sale.invoice.verify"].sudo().search([
                    ("invoice_line_id", "=", il.id),
                    # ("payment_id.state", "=", "已过账")
                ]).mapped("amount_verified")
            )
        )
        for unverified_line in unverified_lines:
            verify_invoice_line_ids.append((0, 0, {"invoice_line_id": unverified_line.id}))
        if payment:
            verify_payment_ids.append((0, 0, {"payment_id": payment.id}))
        return {
            "value": {
                "verify_invoice_line_ids": verify_invoice_line_ids, "verify_payment_ids": verify_payment_ids,
                "partner_id": partner.id, "payment_id": payment.id, "start_date": start_date, "end_date": end_date
            }
        }

    def action_open_wizard(self):
        verify_id = self.sudo().create({"partner_id": False})
        return {
            'type': 'ir.actions.act_window', 'name': "发票核销",
            'res_model': 'wizard.sale.invoice.verify', 'res_id': verify_id.id,
            'view_mode': 'form', 'target': 'new', 'context': {'default_verify_id': verify_id.id, "create": False},
            'views': [[self.env.ref('roke_mes_account_sale.view_wizard_sale_invoice_verify_form').id, 'form']]
        }

    def action_verify(self):
        if not self.verify_payment_ids:
            raise ValidationError("请选择收款单")
        verify_line_ids = self.verify_invoice_line_ids.filtered(lambda vil: vil.is_mark)
        if not verify_line_ids:
            raise ValidationError("请勾选发票明细")
        if self.verify_payment_ids.amount_unverified < self.sum_amount_verify:
            raise ValidationError("发票核销金额不可大于所选收款单未核销金额")
        for verify_line_id in verify_line_ids:
            self.env["roke.sale.invoice.verify"].sudo().create({
                "invoice_id": verify_line_id.invoice_id.id,
                "invoice_line_id": verify_line_id.invoice_line_id.id,
                "verify_date": fields.Date.context_today(self),
                "payment_id": self.verify_payment_ids[0].payment_id.id,  # 只有一个
                "amount_verified": verify_line_id.amount_verify
            })
            # 收款单关联到发票
            verify_line_id.invoice_id.write({"payment_ids": [(4, self.verify_payment_ids[0].payment_id.id)]})

        return {'name': '核销成功',
                'view_type': 'form',
                'view_mode': 'form',
                'res_model': 'wizard.invoice.return.sale',
                'view_id': False,
                'type': 'ir.actions.act_window',
                'target': 'new',
                }
    # def mark_all(self):
    #     self.verify_invoice_line_ids.write({"is_mark": True})
    #     return {
    #         'type': 'ir.actions.act_window', 'name': "发票核销",
    #         'res_model': 'wizard.sale.invoice.verify', 'res_id': self.id,
    #         'view_mode': 'form', 'target': 'new', 'context': {'default_verify_id': self.id, "create": False},
    #         'views': [[self.env.ref('roke_mes_operation.view_wizard_sale_invoice_verify_form').id, 'form']]
    #     }
    #
    # def clear_mark(self):
    #     self.verify_invoice_line_ids.write({"is_mark": False})
    #     return {
    #         'type': 'ir.actions.act_window', 'name': "发票核销",
    #         'res_model': 'wizard.sale.invoice.verify', 'res_id': self.id,
    #         'view_mode': 'form', 'target': 'new', 'context': {'default_verify_id': self.id, "create": False},
    #         'views': [[self.env.ref('roke_mes_operation.view_wizard_sale_invoice_verify_form').id, 'form']]
    #     }


class WizardSaleInvoiceVerifyPayment(models.TransientModel):
    _name = "wizard.sale.invoice.verify.payment"
    _description = "收款单"

    verify_id = fields.Many2one("wizard.sale.invoice.verify", string="Primary")
    payment_id = fields.Many2one("roke.mes.payment", string="收款单")  # domain=_current_user_domain
    payment_date = fields.Date(related="payment_id.payment_date")
    partner_id = fields.Many2one(related="payment_id.partner_id")
    user_id = fields.Many2one(related="payment_id.create_uid", string="业务员")
    amount = fields.Float(related="payment_id.amount", digits='YSYFJE')
    amount_verified = fields.Float("已核销金额", digits='YSYFJE', compute="_compute_amount")
    amount_unverified = fields.Float("未核销金额", digits='YSYFJE', compute="_compute_amount")
    amount_verify = fields.Float("本次核销金额", digits='YSYFJE')

    @api.onchange("payment_id")
    @api.depends("payment_id", "amount")
    def _compute_amount(self):
        for record in self:
            verify_records = self.env["roke.sale.invoice.verify"].sudo().search([
                ("payment_id", "=", record.payment_id.id),
                # ("payment_id.state", "=", "已过账")
            ])
            record.amount_verified = sum(verify_records.mapped("amount_verified"))
            record.amount_unverified = record.amount - record.amount_verified


class WizardSaleInvoiceVerifyInvoice(models.TransientModel):
    _name = "wizard.sale.invoice.verify.invoice"
    _description = "发票"

    verify_id = fields.Many2one("wizard.sale.invoice.verify", string="Primary")
    is_mark = fields.Boolean(string="勾选")
    invoice_line_id = fields.Many2one("roke.sale.invoice.line", string="发票明细（产品）")
    invoice_id = fields.Many2one(related="invoice_line_id.invoice_id")
    invoice_date = fields.Date(related="invoice_id.invoice_date")
    customer_id = fields.Many2one(related="invoice_id.customer_id")
    quantity = fields.Float(related="invoice_line_id.quantity", digits='YSYFSL')
    price_unit = fields.Float(related="invoice_line_id.price_unit", digits='YSYFDJ')
    subtotal = fields.Float(related="invoice_line_id.subtotal", digits='YSYFJE')

    amount_unverified = fields.Float("未核销金额", digits='YSYFJE', compute="_compute_amount_unverified")
    amount_verify = fields.Float("本次核销金额", digits='YSYFJE', default=lambda self: self.amount_unverified)

    @api.depends("invoice_line_id", "subtotal")
    def _compute_amount_unverified(self):
        """
        计算未核销金额
        :return:
        """
        for record in self:
            verify_records = self.env["roke.sale.invoice.verify"].sudo().search([
                ("invoice_line_id", "=", record.invoice_line_id.id)
            ])
            amount_verified = sum(verify_records.mapped("amount_verified"))
            record.amount_unverified = record.subtotal - amount_verified

    @api.onchange("amount_verify")
    def _change_amount_verify(self):
        if self.amount_verify > self.amount_unverified:
            raise ValidationError("发票本次核销金额不可大于未核销金额")


class WizardInvoiceSuccessSale(models.TransientModel):
    _name = 'wizard.invoice.return.sale'

    def confirm(self):
        pass
