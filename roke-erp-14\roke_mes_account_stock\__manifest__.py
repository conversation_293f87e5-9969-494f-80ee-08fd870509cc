# -*- coding: utf-8 -*-
{
    'name': '应收应付库存桥接',
    'version': '1.1',
    'category': 'mes',
    'depends': ['roke_mes_account_purchase', 'roke_mes_account_sale'
                ],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        应收应付库存桥接
    """,
    'data': [
        'security/ir.model.access.csv',
        'data/roke_sale_income_statement.xml',
        'data/stock_picking_line_report.xml',
        'views/inherit_roke_stock_views.xml',
        'views/inherit_res_config_settings.xml',
        'wizard/wizard_stock_deduct.xml',
        'wizard/inherit_roke_sale_delivery_wizard.xml',
        'wizard/inherit_wizard_purchase_receiving.xml'
    ],
    'demo': [
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
}
