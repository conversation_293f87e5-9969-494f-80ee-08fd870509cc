<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_mes_material_cost_statistics" model="roke.sql.model.component">
            <field name="name">销售利润表</field>
            <field name="sql_statement">SELECT roke_sale_order.order_date as "单据日期",
	roke_sale_order.code as "单据编号",
	(case roke_mes_stock_picking_type.type WHEN '出库' THEN '销售出库单' ELSE '销售退货单' END) as "单据类型",
	roke_partner.name as "客户名称",
	(
		SELECT COALESCE(SUM(tax_amount), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as "税额",
	roke_mes_stock_picking.total_price as "价税合计",
	(
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as "实际不含税金额",
	(
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as "成本",
	((
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) - (
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	)) as "销售毛利",
	(
		CASE COALESCE(roke_mes_stock_picking.total_price,0) WHEN 0 THEN 0 ELSE ROUND((((
			SELECT COALESCE(SUM(amount_excl_tax), 0)
			FROM roke_mes_stock_move_line
			WHERE picking_id = roke_mes_stock_picking.id
		) - (
			SELECT (
				SELECT COALESCE(direct_material_price + direct_labor_price)
				FROM roke_product
				WHERE id = move_line.product_id
			)
			FROM roke_mes_stock_move_line as move_line
			WHERE picking_id = roke_mes_stock_picking.id
		)) / COALESCE(roke_mes_stock_picking.total_price,0) * 100), 2) END
	) as "毛利率(百分比)"
FROM roke_mes_stock_picking
	LEFT JOIN roke_mes_stock_picking_type ON roke_mes_stock_picking.picking_type_id = roke_mes_stock_picking_type.id
	LEFT JOIN roke_mes_stock_move_line ON roke_mes_stock_picking.id = roke_mes_stock_move_line.picking_id
	LEFT JOIN roke_sale_order ON roke_sale_order.id = roke_mes_stock_picking.sale_id
	LEFT JOIN roke_partner ON roke_sale_order.customer_id = roke_partner.id
	LEFT JOIN roke_mes_payment_line ON roke_mes_payment_line.stock_move_line_id = roke_mes_stock_move_line.id
	LEFT JOIN roke_mes_payment ON roke_mes_payment.id = roke_mes_payment_line.payment_id
WHERE roke_mes_stock_picking.state = '完成'
	and roke_mes_stock_picking_type.picking_logotype = 'XSCKD'
	and roke_mes_stock_picking_type.type = :roke_mes_stock_picking_type.type
	and roke_sale_order.order_date BETWEEN :order_date AND :order_date
	and roke_mes_payment.payment_date BETWEEN :payment_date AND :payment_date
	and roke_mes_payment.state = :roke_mes_payment.state</field>
            <field name="sql_search_criteria" eval='[(5, 0, 0),
                (0, 0, {"name": "状态", "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__state"),
                    "field_default": "&apos;完成&apos;", "sql_decider": "="}),
                (0, 0, {"name": "业务标识", "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking_type__picking_logotype"),
                    "field_default": "&apos;XSCKD&apos;", "sql_decider": "="}),
				(0, 0, {"name": "单据类型", "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking_type__type"),
                    "sql_data": " roke_mes_stock_picking_type.type = :roke_mes_stock_picking_type.type ",
                    "sql_field_mark": ":roke_mes_stock_picking_type.type", "sql_field_mark_type": "selection", "sql_decider": "="}),
				(0, 0, {"name": "单据日期", "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sql_data": " roke_sale_order.order_date BETWEEN :order_date AND :order_date ",
                    "sql_field_mark": ":order_date", "sql_field_mark_type": "date", "sql_decider": "between"}),
				(0, 0, {"name": "最后收(退)款日期", "field_id": ref("roke_mes_account.field_roke_mes_payment__payment_date"),
                    "sql_data": " roke_mes_payment.payment_date BETWEEN :payment_date AND :payment_date ",
                    "sql_field_mark": ":payment_date", "sql_field_mark_type": "date", "sql_decider": "between"}),
				(0, 0, {"name": "收款状态", "field_id": ref("roke_mes_account.field_roke_mes_payment__state"),
                    "sql_data": " roke_mes_payment.state = :roke_mes_payment.state ",
                    "sql_field_mark": ":roke_mes_payment.state", "sql_field_mark_type": "selection", "sql_decider": "="}),
            ]'/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {"name": "单据日期", "field_id": ref("roke_mes_sale.field_roke_sale_order__order_date"),
                    "sql_order_by_data": "roke_sale_order.order_date", "sequence": 1}),

                (0, 0, {"name": "单据编号", "field_id": ref("roke_mes_sale.field_roke_sale_order__code"),
                    "sql_order_by_data": "roke_sale_order.code","sequence": 2}),

                (0, 0, {"name": "单据类型", "sql_data": "   (case roke_mes_stock_picking_type.type WHEN &apos;出库&apos; THEN &apos;销售出库单&apos; ELSE &apos;销售退货单&apos; END) as 单据类型 ",
                    "sql_order_by_data": "  (case roke_mes_stock_picking_type.type WHEN &apos;出库&apos; THEN &apos;销售出库单&apos; ELSE &apos;销售退货单&apos; END)", "sequence": 3}),

                (0, 0, {"name": "客户名称", "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sql_order_by_data": "roke_partner.name", "sequence": 4}),

                (0, 0, {"name": "税额", "sql_data": "	(
		SELECT COALESCE(SUM(tax_amount), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as 税额",
                    "sql_order_by_data": "(
		SELECT COALESCE(SUM(tax_amount), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	)", "sequence": 5}),

                (0, 0, {"name": "价税合计", "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__total_price"),
                    "sql_order_by_data": "roke_mes_stock_picking.total_price", "sequence": 6}),

                (0, 0, {"name": "实际不含税金额", "sql_data": "	(
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as 实际不含税金额",
                    "sql_order_by_data": "(
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	)", "sequence": 7}),

                (0, 0, {"name": "成本", "sql_data": "	(
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) as 成本",
                    "sql_order_by_data": "(
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	)", "sequence": 8}),

                (0, 0, {"name": "销售毛利", "sql_data": "	((
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) - (
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	)) as 销售毛利",
                    "sql_order_by_data": "((
		SELECT COALESCE(SUM(amount_excl_tax), 0)
		FROM roke_mes_stock_move_line
		WHERE picking_id = roke_mes_stock_picking.id
	) - (
		SELECT (
			SELECT COALESCE(direct_material_price + direct_labor_price)
			FROM roke_product
			WHERE id = move_line.product_id
		)
		FROM roke_mes_stock_move_line as move_line
		WHERE picking_id = roke_mes_stock_picking.id
	))", "sequence": 9}),

                (0, 0, {"name": "毛利率(%)", "sql_data": "	(
		CASE COALESCE(roke_mes_stock_picking.total_price,0) WHEN 0 THEN 0 ELSE ROUND((((
			SELECT COALESCE(SUM(amount_excl_tax), 0)
			FROM roke_mes_stock_move_line
			WHERE picking_id = roke_mes_stock_picking.id
		) - (
			SELECT (
				SELECT COALESCE(direct_material_price + direct_labor_price)
				FROM roke_product
				WHERE id = move_line.product_id
			)
			FROM roke_mes_stock_move_line as move_line
			WHERE picking_id = roke_mes_stock_picking.id
		)) / COALESCE(roke_mes_stock_picking.total_price,0) * 100), 2) END
	) as 毛利率(百分比)",
                    "sql_order_by_data": "(
		CASE COALESCE(roke_mes_stock_picking.total_price,0) WHEN 0 THEN 0 ELSE ROUND((((
			SELECT COALESCE(SUM(amount_excl_tax), 0)
			FROM roke_mes_stock_move_line
			WHERE picking_id = roke_mes_stock_picking.id
		) - (
			SELECT (
				SELECT COALESCE(direct_material_price + direct_labor_price)
				FROM roke_product
				WHERE id = move_line.product_id
			)
			FROM roke_mes_stock_move_line as move_line
			WHERE picking_id = roke_mes_stock_picking.id
		)) / COALESCE(roke_mes_stock_picking.total_price,0) * 100), 2) END
	)", "sequence": 10}),
            ]'/>
            <field name="top_menu_id" ref="roke_mes_sale.roke_mes_sale_query_root"/>
        </record>
    </data>
</odoo>