<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_query_stock_picking_line_report" model="roke.sql.model.component">
            <field name="name">库存收发明细表</field>
            <field name="journaling_type">客户端报表</field>
            <field name="sql_statement">
                SELECT
                roke_mes_stock_picking.picking_date AS "单据日期",
                roke_mes_stock_picking.code AS "单据编号",
                roke_mes_stock_picking_type.name AS "业务类型",
                roke_partner.name AS "业务伙伴",
                roke_product_category.name AS "产品类别",
                roke_product.name AS "产品名称",
                roke_product.specification AS "规格",
                roke_product.model AS "型号",
                roke_mes_stock_location.name AS "仓库",
                roke_mes_stock_lot.name AS "单件/批次号",
                COALESCE(roke_mes_stock_move_line.qty,0) AS "数量",
                roke_uom.name AS "单位",
                COALESCE(received_amount,0) AS "金额"
                FROM roke_mes_stock_move_line
                LEFT JOIN roke_mes_stock_picking ON roke_mes_stock_move_line.picking_id = roke_mes_stock_picking.ID
                LEFT JOIN roke_mes_stock_move ON roke_mes_stock_move_line.move_id = roke_mes_stock_move.ID
                LEFT JOIN roke_mes_stock_picking_type ON roke_mes_stock_picking.picking_type_id = roke_mes_stock_picking_type.ID
                LEFT JOIN roke_partner ON roke_mes_stock_picking.partner_id = roke_partner.ID
                LEFT JOIN roke_product ON roke_mes_stock_move_line.product_id = roke_product.ID
                LEFT JOIN roke_product_category ON roke_product.category_id = roke_product_category.ID
                LEFT JOIN roke_mes_stock_lot ON roke_mes_stock_move_line.lot_id = roke_mes_stock_lot.ID
                LEFT JOIN roke_uom ON roke_product.uom_id = roke_uom.ID
                LEFT JOIN roke_mes_stock_location AS roke_mes_stock_location ON roke_mes_stock_move.location_of_in_out = roke_mes_stock_location.ID
                WHERE
                    roke_mes_stock_picking.picking_date between :picking_date and :picking_date
                    AND roke_product_category.name = :roke_product_category.name
                    AND roke_product.name = :roke_product.name
                    AND roke_mes_stock_location.name = :roke_mes_stock_location.name
                    AND roke_mes_stock_picking_type.name = :roke_mes_stock_picking_type.name
                    AND roke_partner.name = :roke_partner.name
                    AND roke_mes_stock_picking_type.name != '内部调拨'
            </field>
            <field name="top_menu_id" ref="roke_mes_stock.roke_stock_report_menu"/>
            <field name="sql_search_criteria" eval="[(5, 0, 0),
                (0, 0, {
                    'name': '日期选择',
                    'field_id': ref('roke_mes_stock.field_roke_mes_stock_picking__picking_date'),
                    'sql_decider': 'between',
                    'sql_data': ' roke_mes_stock.picking_date between :picking_date and  :picking_date ',
                    'sql_field_mark': ':picking_date',
                    'sql_field_mark_type': 'date'
                }),
                (0, 0, {
                    'name': '产品类别',
                    'field_id': ref('roke_mes_base.field_roke_product_category__name'),
                    'sql_inherit_field_id': ref('roke_mes_base.field_roke_product__category_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_product_category.name = :roke_product_category.name ',
                    'sql_field_mark': ':roke_product_category.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '产品名称',
                    'field_id': ref('roke_mes_base.field_roke_product__name'),
                    'sql_inherit_field_id': ref('roke_mes_stock.field_roke_mes_stock_move_line__product_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_product.name = :roke_product.name ',
                    'sql_field_mark': ':roke_product.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '仓库',
                    'field_id': ref('roke_mes_stock.field_roke_mes_stock_location__name'),
                    'sql_inherit_field_id': ref('roke_mes_stock.field_roke_mes_stock_move__location_of_in_out'),
                    'sql_decider': '=',
                    'sql_data': ' roke_mes_stock_location.name = :roke_mes_stock_location.name ',
                    'sql_field_mark': ':roke_mes_stock_location.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '业务类型',
                    'field_id': ref('roke_mes_stock.field_roke_mes_stock_picking_type__name'),
                    'sql_inherit_field_id': ref('roke_mes_stock.field_roke_mes_stock_picking__picking_type_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_mes_stock_picking_type.name = :roke_mes_stock_picking_type.name ',
                    'sql_field_mark': ':roke_mes_stock_picking_type.name',
                    'sql_field_mark_type': 'many2one'
                }),
                (0, 0, {
                    'name': '业务伙伴',
                    'field_id': ref('roke_mes_base.field_roke_partner__name'),
                    'sql_inherit_field_id': ref('roke_mes_stock.field_roke_mes_stock_picking__partner_id'),
                    'sql_decider': '=',
                    'sql_data': ' roke_partner.name = :roke_partner.name ',
                    'sql_field_mark': ':roke_partner.name',
                    'sql_field_mark_type': 'many2one'
                })
            ]"/>
            <field name="sql_show_columns" eval='[(5, 0, 0),
                (0, 0, {
                    "name": "单据日期",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__picking_date"),
                    "sequence": 1,
                    "sql_order_by_data": "roke_mes_stock_picking.picking_date"
                }),
                (0, 0, {
                    "name": "单据编号",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__code"),
                    "sequence": 2,
                    "sql_order_by_data": "roke_mes_stock_picking.code"
                }),
                (0, 0, {
                    "name": "业务类型",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking_type__name"),
                    "sequence": 3,
                    "sql_order_by_data": "roke_mes_stock_picking_type.name"
                }),
                (0, 0, {
                    "name": "业务伙伴",
                    "field_id": ref("roke_mes_base.field_roke_partner__name"),
                    "sequence": 4,
                    "sql_order_by_data": "roke_partner.name"
                }),
                (0, 0, {
                    "name": "产品类别",
                    "field_id": ref("roke_mes_base.field_roke_product_category__name"),
                    "sequence": 5,
                    "sql_order_by_data": "roke_product_category.name"
                }),
                (0, 0, {
                    "name": "产品名称",
                    "field_id": ref("roke_mes_base.field_roke_product__name"),
                    "sequence": 6,
                    "sql_order_by_data": "roke_product.name"
                }),
                (0, 0, {
                    "name": "规格",
                    "field_id": ref("roke_mes_base.field_roke_product__specification"),
                    "sequence": 7,
                    "sql_order_by_data": "roke_product.specification"
                }),
                (0, 0, {
                    "name": "型号",
                    "field_id": ref("roke_mes_base.field_roke_product__model"),
                    "sequence": 8,
                    "sql_order_by_data": "roke_product.model"
                }),
                (0, 0, {
                    "name": "仓库",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_location__name"),
                    "sequence": 9,
                    "sql_order_by_data": "roke_mes_stock_location.name"
                }),
                (0, 0, {
                    "name": "单件/批次号",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_lot__name"),
                    "sequence": 10,
                    "sql_order_by_data": "roke_mes_stock_lot.name"
                }),
                (0, 0, {
                    "name": "数量",
                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_move_line__qty"),
                    "sequence": 11,
                    "sql_order_by_data": "roke_mes_stock_move_line.qty"
                }),
                (0, 0, {
                    "name": "单位",
                    "field_id": ref("roke_mes_base.field_roke_uom__name"),
                    "sequence": 12,
                    "sql_order_by_data": "roke_uom.name"
                }),
                (0, 0, {
                    "name": "金额",
                    "field_id": ref("roke_mes_account_stock.field_roke_mes_stock_move_line__received_amount"),
                    "sequence": 13,
                    "sql_order_by_data": "roke_mes_stock_move_line.received_amount"
                })
                ]'/>
<!--            <field name="sql_group_way" eval='[(5, 0, 0),-->
<!--                (0, 0, {-->
<!--                    "name": "单据日期",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__picking_date"),-->
<!--                    "sequence": 1-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "单据编号",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking__code"),-->
<!--                    "sequence": 2-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "业务类型",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_picking_type__name"),-->
<!--                    "sequence": 3-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "业务伙伴",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_partner__name"),-->
<!--                    "sequence": 4-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "产品类别",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_product_category__name"),-->
<!--                    "sequence": 5-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "产品名称",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_product__name"),-->
<!--                    "sequence": 6-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "规格",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_product__specification"),-->
<!--                    "sequence": 7-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "型号",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_product__model"),-->
<!--                    "sequence": 8-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "仓库",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_location__name"),-->
<!--                    "sequence": 9-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "单件/批次号",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_lot__name"),-->
<!--                    "sequence": 10-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "数量",-->
<!--                    "field_id": ref("roke_mes_stock.field_roke_mes_stock_move_line__qty"),-->
<!--                    "sequence": 11-->
<!--                }),-->
<!--                (0, 0, {-->
<!--                    "name": "单位",-->
<!--                    "field_id": ref("roke_mes_base.field_roke_uom__name"),-->
<!--                    "sequence": 12-->
<!--                })-->
<!--            ]'/>-->
        </record>
    </data>
</odoo>
