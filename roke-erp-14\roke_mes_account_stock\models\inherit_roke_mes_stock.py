# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class InheritRokeMesPaymentLine(models.Model):
    _inherit = "roke.mes.payment.line"

    stock_move_id = fields.Many2one('roke.mes.stock.move', string='库存需求')
    stock_move_line_id = fields.Many2one('roke.mes.stock.move.line', string='库存明细需求')


class InheritRokeMesPayment(models.Model):
    _inherit = "roke.mes.payment"

    stock_move_id = fields.Many2one('roke.mes.stock.picking', string='库存需求')


class InheritRokeMesStockMove(models.Model):
    _inherit = "roke.mes.stock.move"

    payment_line_ids = fields.One2many("roke.mes.payment.line", "stock_move_id", string="收款明细")
    deducted_amount = fields.Float(string="已优惠金额", digits='KCJE', compute="_compute_pay_amount")
    paid_amount = fields.Float(string="已收金额", digits='KCJE', compute="_compute_pay_amount")
    paid_total = fields.Float(string="已收款", digits='KCJE', compute="_compute_pay_amount")
    unpaid_amount = fields.Float(string="未收款", digits='KCJE', compute="_compute_pay_amount")
    # 税率相关
    tax_rate = fields.Float('税率')
    unit_price_excl_tax = fields.Float('不含税单价', digits='KCDJ')
    amount_excl_tax = fields.Float('不含税金额', digits='KCJE', compute='_compute_amount_excl_tax', store=True)
    tax_amount = fields.Float('税额', digits='KCJE', compute='_compute_amount_excl_tax', store=True)

    discount_rate = fields.Float('折扣率')
    discount_amount = fields.Float('折扣额', digits='KCJE')
    after_discount_amount = fields.Float('折扣后金额', digits='KCJE', compute='_compute_after_discount_amount')
    whole_order_offer = fields.Float('整单优惠', digits='KCJE')
    amount_receivable = fields.Float('应收应付金额', digits='KCJE', compute='_compute_after_discount_amount')

    is_open_tax = fields.Boolean('启用税率', compute='_compute_is_open_tax', store=True)

    @api.depends('product_id')
    def _compute_is_open_tax(self):
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        # 税率是否启用
        for record in self:
            record.is_open_tax = is_tax

    @api.onchange('product_id')
    def _onchange_tax_rate(self):
        if self.picking_id.is_open_tax:
            self.tax_rate = self.product_id.tax_rate
        else:
            self.tax_rate = 0

    @api.onchange('unit_price_excl_tax')
    def _onchange_tax_rate_next(self):
        tax_rate = (self.unit_price - self.unit_price_excl_tax) / self.unit_price * 100 if self.unit_price and self.unit_price > 0 else 0
        if self.unit_price_excl_tax > self.unit_price:
            raise ValidationError('不含税单价大于产品单价!')
        if self.tax_rate != tax_rate:
            self.tax_rate = tax_rate

    @api.onchange('unit_price','tax_rate')
    def _onchange_unit_price_excl_tax(self):
        if self.tax_rate > 100:
            raise ValidationError('税率禁止大于100!')
        if self.tax_rate < 0:
            raise ValidationError('税率禁止为负数!')
        if self.picking_id.is_open_tax:
            unit_price_excl_tax = self.unit_price - self.unit_price * self.tax_rate / 100
            self.unit_price_excl_tax = unit_price_excl_tax
        else:
            self.unit_price_excl_tax = 0

    @api.depends('unit_price_excl_tax', 'qty', 'amount_excl_tax', 'tax_amount')
    def _compute_amount_excl_tax(self):
        for rec in self:
            if rec.picking_id.is_open_tax:
                rec.amount_excl_tax = rec.unit_price_excl_tax * rec.qty
                if rec.tax_rate:
                    rec.tax_amount = (rec.unit_price - rec.unit_price_excl_tax) * rec.qty
                else:
                    rec.tax_amount = 0
            else:
                rec.amount_excl_tax = 0
                rec.tax_amount = 0

    @api.depends("payment_line_ids", "payment_line_ids.deducted_amount", "payment_line_ids.paid_amount")
    def _compute_pay_amount(self):
        for record in self:
            # active_payment_line_ids = record.payment_line_ids.filtered(lambda pl: pl.payment_id.state == "已过账")
            active_payment_line_ids = record.payment_line_ids
            record.deducted_amount = sum(active_payment_line_ids.mapped("deducted_amount"))
            record.paid_amount = sum(active_payment_line_ids.mapped("paid_amount"))
            record.paid_total = record.paid_amount + record.deducted_amount
            record.unpaid_amount = record.subtotal - record.paid_total

    @api.onchange('discount_amount')
    def _onchange_discount_amount(self):
        self.discount_rate = (self.discount_amount / self.subtotal) * 100 if self.subtotal and self.subtotal > 0 else 0

    @api.onchange('discount_rate')
    def _onchange_discount_rate(self):
        self.discount_amount = self.subtotal * self.discount_rate / 100

    @api.onchange('unit_price', 'qty')
    def _onchange_account_qty_price(self):
        subtotal = self.unit_price * self.qty
        self.discount_amount = subtotal * self.discount_rate / 100

    @api.depends('subtotal', 'discount_amount', 'whole_order_offer','after_discount_amount')
    def _compute_after_discount_amount(self):
        """
			计算方式：折扣后金额=金额-折扣额-整单优惠
			当折扣率为0时，该字段默认不进行折扣，折扣后金额取小计字段数据
		"""
        for rec in self:
            if rec.subtotal - rec.discount_amount:
                discount_rate = rec.picking_id.discount_amount / (rec.subtotal - rec.discount_amount)
            else:
                discount_rate = 0
            if rec.discount_rate > 0:
                rec.discount_amount = rec.subtotal * rec.discount_rate / 100
                # if rec.subtotal:
                #     rec.whole_order_offer = (rec.subtotal - rec.subtotal * rec.discount_rate / 100) * discount_rate
                # else:
                #     rec.whole_order_offer = 0
                rec.after_discount_amount = rec.subtotal - rec.subtotal * rec.discount_rate / 100
                rec.amount_receivable = rec.after_discount_amount - rec.whole_order_offer
            else:
                # if rec.subtotal:
                #     rec.whole_order_offer = rec.subtotal * discount_rate
                # else:
                #     rec.whole_order_offer = 0
                rec.after_discount_amount = rec.subtotal
                rec.amount_receivable = rec.subtotal - rec.whole_order_offer


class InheritRokeMesStockMoveLine(models.Model):
    _inherit = "roke.mes.stock.move.line"

    payment_line_ids = fields.One2many("roke.mes.payment.line", "stock_move_line_id", string="收款明细")
    deducted_amount = fields.Float(string="已优惠金额", digits='KCJE', compute="_compute_pay_amount")
    paid_amount = fields.Float(string="已收金额", digits='KCJE', compute="_compute_pay_amount")
    paid_total = fields.Float(string="已收款", digits='KCJE', compute="_compute_pay_amount")
    unpaid_amount = fields.Float(string="未收款", digits='KCJE', compute="_compute_pay_amount")
    # 税率相关
    tax_rate = fields.Float('税率')
    unit_price_excl_tax = fields.Float('不含税单价', digits='KCDJ')
    amount_excl_tax = fields.Float('不含税金额', digits='KCJE')
    tax_amount = fields.Float('税额', digits='KCJE')
    discount_rate = fields.Float('折扣率')
    discount_amount = fields.Float('折扣额', digits='KCJE')

    received_amount = fields.Float('已收/付金额', digits='KCJE')

    @api.depends("payment_line_ids", "payment_line_ids.deducted_amount", "payment_line_ids.paid_amount")
    def _compute_pay_amount(self):
        for record in self:
            # active_payment_line_ids = record.payment_line_ids.filtered(lambda pl: pl.payment_id.state == "已过账")
            active_payment_line_ids = record.payment_line_ids
            record.deducted_amount = sum(active_payment_line_ids.mapped("deducted_amount"))
            record.paid_amount = sum(active_payment_line_ids.mapped("paid_amount"))
            record.paid_total = record.paid_amount + record.deducted_amount
            record.unpaid_amount = record.subtotal - record.paid_total


class InheritRokeMesStockPicking(models.Model):
    _inherit = "roke.mes.stock.picking"

    def _get_is_open_tax(self):
        return self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)

    payment_ids = fields.Many2many("roke.mes.payment", "roke_stock_picking_payment_rel", "pick_id", "pay_id",
                                   string="收款单")
    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('付款优惠', digits='KCJE')
    amount_after_discount = fields.Float('优惠后金额', digits='KCJE', compute='_compute_discount_amount_total',
                                         store=True)
    is_open_tax = fields.Boolean('启用税率', compute='_compute_is_open_tax', default=_get_is_open_tax)
    # 发票
    purchase_invoice_ids = fields.Many2many('roke.purchase.invoice', string='采购发票')
    sale_invoice_ids = fields.Many2many('roke.sale.invoice', string='销售发票')
    payment_count = fields.Integer(string="付款单数量", compute="_compute_payment_count")
    invoice_count = fields.Integer(string="销售发票数量", compute="_compute_payment_count")
    purchase_invoice_count = fields.Integer(string="采购发票数量", compute="_compute_payment_count")
    discount_amount_total = fields.Float(string='折扣后金额合计', digits='XSJE',
                                         compute='_compute_discount_amount_total')

    @api.depends('move_line_ids', 'move_line_ids.after_discount_amount', 'move_line_ids.qty',
                 'move_line_ids.unit_price',
                 'discount_amount', 'move_line_ids.discount_rate', 'move_line_ids.discount_amount',
                 'discount_rate')
    def _compute_discount_amount_total(self):
        for rec in self:
            # 产品总折扣后金额
            after_discount_amount = sum(rec.move_line_ids.mapped('after_discount_amount'))
            if not after_discount_amount:
                rec.discount_amount = 0
            rec.discount_amount_total = after_discount_amount
            rec.amount_after_discount = after_discount_amount - rec.discount_amount if after_discount_amount - rec.discount_amount >= 0 else 0

    def action_view_invoice(self):
        # result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_account_sale.view_roke_sale_invoice_action')
        # result['context'] = {'default_partner_id': self.partner_id.id}
        # sale_invoice_ids = self.mapped('sale_invoice_ids')
        # result_ids = self.env["roke.sale.invoice"].search([("id", "in", sale_invoice_ids.ids)])
        # if not result_ids or len(result_ids) > 1:
        #     result['domain'] = "[('id','in',%s)]" % (result_ids.ids)
        # elif len(result_ids) == 1:
        #     res = self.env.ref('roke_mes_account_sale.view_roke_sale_invoice_form', False)
        #     form_view = [(res and res.id or False, 'form')]
        #     if 'views' in result:
        #         result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
        #     else:
        #         result['views'] = form_view
        #     result['res_id'] = result_ids.id
        # return result
        if self.picking_type_id.type == '入库':
            data = {
                'name': '采购发票',
                'type': 'ir.actions.act_window',
                'res_model': 'roke.purchase.invoice',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [('id', 'in', self.purchase_invoice_ids.ids)],
                'context': {'create': True, 'edit': True, 'delete': True}
            }
        else:
            data = {
                'name': '销售发票',
                'type': 'ir.actions.act_window',
                'res_model': 'roke.sale.invoice',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [('id', 'in', self.sale_invoice_ids.ids)],
                'context': {'create': True, 'edit': True, 'delete': True}
            }
        return data

    # def action_view_invoice_purchase(self):
    #     return {
    #         'name': '采购发票',
    #         'type': 'ir.actions.act_window',
    #         'res_model': 'roke.purchase.invoice',
    #         'view_mode': 'tree,form',
    #         'target': 'current',
    #         'domain': [('id', 'in', self.purchase_invoice_ids.ids)],
    #         'context': {'create': True, 'edit': True, 'delete': True}
    #     }

    def button_create_invoice(self):
        sale_invoice = self.env['roke.sale.invoice']
        purchase_invoice = self.env['roke.purchase.invoice']
        _list = []
        for move in self.ml_ids:
            name = "%s（%s）" % (move.product_id.name, move.product_id.code) or ""
            if move.move_id.qty:
                radio = move.qty / move.move_id.qty
            else:
                raise ValidationError('需求数量为0，无法生成发票！')
            _list.append((0, 0, {
                "product_id": move.product_id.id,
                "quantity": move.qty,
                "price_unit": move.unit_price,
                "name": name,
                "preferential_subtotal": move.move_id.subtotal * radio - move.move_id.amount_receivable * radio,
                "subtotal": move.move_id.amount_receivable * radio,
                "tax_rate": move.move_id.tax_rate,
                "unit_price_excl_tax": move.move_id.unit_price_excl_tax
            }))
        data = {
            "invoice_date": fields.date.today(),
            "invoice_date_due": fields.date.today(),
            "invoice_line_ids": _list
        }
        if self.picking_type_id.type == '出库':
            data.update({"customer_id": self.partner_id.id})
            # data.update({"employee_id": self.employee_id.id})
            res_model = 'roke.sale.invoice'
            invoice = sale_invoice.create(data)
            self.sale_invoice_ids += invoice
        else:
            data.update({"supplier_id": self.partner_id.id})
            res_model = 'roke.purchase.invoice'
            invoice = purchase_invoice.create(data)
            self.purchase_invoice_ids += invoice
        return {
            'name': '发票',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': res_model,
            'res_id': invoice.id,
        }

    def _compute_payment_count(self):
        for record in self:
            record.payment_count = len(record.payment_ids)
            record.invoice_count = len(record.sale_invoice_ids)
            record.purchase_invoice_count = len(record.purchase_invoice_ids)

    def action_view_payment(self):
        if self.type == '出库':
            result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_account.view_roke_mes_collection_action')
            result['context'] = {'default_partner_id': self.partner_id.id}
            payment_ids = self.mapped('payment_ids')
            result_ids = self.env["roke.mes.collection"].search([("payment_id", "in", payment_ids.ids)])
            if not result_ids or len(result_ids) > 1:
                result['domain'] = "[('id','in',%s)]" % (result_ids.ids)
            elif len(result_ids) == 1:
                res = self.env.ref('roke_mes_account.view_roke_mes_collection_form', False)
                form_view = [(res and res.id or False, 'form')]
                if 'views' in result:
                    result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
                else:
                    result['views'] = form_view
                result['res_id'] = result_ids.id
            return result
        else:
            result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_account.view_roke_mes_pay_action')
            result['context'] = {'default_partner_id': self.partner_id.id}
            payment_ids = self.mapped('payment_ids')
            result_ids = self.env["roke.mes.pay"].search([("payment_id", "in", payment_ids.ids)])
            if not result_ids or len(result_ids) > 1:
                result['domain'] = "[('id','in',%s)]" % (result_ids.ids)
            elif len(result_ids) == 1:
                res = self.env.ref('roke_mes_account.view_roke_mes_pay_form', False)
                form_view = [(res and res.id or False, 'form')]
                if 'views' in result:
                    result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
                else:
                    result['views'] = form_view
                result['res_id'] = result_ids.id
            return result

    def make_revoke(self):
        res = super(InheritRokeMesStockPicking, self).make_revoke()
        for rec in self:
            if rec.type == '出库':
                for sale in rec.sale_invoice_ids:
                    sale.action_cancel()
            else:
                for purchase in rec.purchase_invoice_ids:
                    purchase.action_cancel()
        return res

    @api.onchange('discount_amount')
    def _onchange_amount(self):
        if not self.env.context.get('calculate_discount'):
            after_discount_amount = sum(self.move_line_ids.mapped('after_discount_amount'))
            if after_discount_amount:
                self.discount_rate = round(self.discount_amount / after_discount_amount, 6)
            else:
                raise ValidationError('折扣后金额总计为0，无法继续优惠！')
            # 处理超过优惠金额
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('discount_rate')
    def _onchange_discount(self):
        if not self.env.context.get('calculate_discount'):
            after_discount_amount = sum(self.move_line_ids.mapped('after_discount_amount'))
            self.discount_amount = self.discount_rate * after_discount_amount
            # 处理超过优惠金额
            if self.amount_after_discount < 0 or self.discount_amount < 0 or self.discount_rate > 1:
                self.discount_rate, self.discount_amount = 0, 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('discount_amount', 'discount_rate', 'move_line_ids', 'move_line_ids.after_discount_amount',
                  'move_line_ids.qty',
                  'move_line_ids.unit_price',
                  'move_line_ids.discount_rate', 'move_line_ids.discount_amount',
                  )
    def _onchange_account_discount(self):
        # 计算方式：（折扣后金额合计-优惠金额）*该明细折扣后金额/折扣后总金额=整单优惠
        for line in self.move_line_ids:
            if self.discount_amount_total:
                whole_order_offer = self.discount_amount * (
                        line.after_discount_amount / self.discount_amount_total)
            else:
                whole_order_offer = 0
            line.whole_order_offer = whole_order_offer
        # 处理精度差
        if self.discount_amount != sum(self.move_line_ids.mapped('whole_order_offer')):
            difference_amount = self.discount_amount - sum(self.move_line_ids.mapped('whole_order_offer'))
            self.move_line_ids[-1].whole_order_offer += difference_amount

    def _compute_is_open_tax(self):
        # 税率是否启用
        is_tax = self.env['ir.config_parameter'].sudo().get_param('is.open.tax', default=False)
        for record in self:
            record.is_open_tax = is_tax

    def button_finish(self):
        res = super(InheritRokeMesStockPicking, self).button_finish()
        is_auto_invoicing = self.env['ir.config_parameter'].sudo().get_param('is.auto.invoicing', default=False)
        # 财务员发货自动生成发票
        if is_auto_invoicing and self.picking_type_id.picking_logotype in ['CGRKD', 'XSCKD']:
            self.make_invoice()
        return res

    def make_invoice(self):
        """自动生成发票"""
        # pass
        _list = []
        for ml in self.ml_ids:
            name = "%s（%s）" % (ml.product_id.name, ml.product_id.code) or ""
            if ml.move_id.qty:
                radio = ml.qty / ml.move_id.qty
            else:
                raise ValidationError('需求数量为0，无法生成发票！')
            _list.append((0, 0, {"product_id": ml.product_id.id,
                                 "quantity": ml.qty,
                                 "price_unit": ml.unit_price,
                                 "name": name,
                                 "preferential_subtotal": ml.move_id.subtotal * radio - ml.move_id.amount_receivable * radio,
                                 "subtotal": ml.move_id.amount_receivable * radio,
                                 "tax_rate": ml.move_id.tax_rate,
                                 "unit_price_excl_tax": ml.move_id.unit_price_excl_tax}))
        if self.type == '出库':
            data = {
                "customer_id": self.partner_id.id,
                "invoice_line_ids": _list
            }
            res = self.env['roke.sale.invoice'].create(data)
            self.sale_invoice_ids = res.ids

        if self.type == '入库' and self.ml_ids:
            data = {
                "supplier_id": self.partner_id.id,
                "invoice_line_ids": _list
            }
            res = self.env['roke.purchase.invoice'].create(data)
            self.purchase_invoice_ids = res.ids

    @staticmethod
    def prepare_line_ids(line_ids):
        """
        归集明细行数据
        :param line_ids:
        :return:
        """
        deduct_line_ids = []
        for line in line_ids:
            if line.deducted_amount + line.paid_amount != line.subtotal:
                deduct_line_ids.append((0, 0, {
                    "order_line_id": line.id, "unpaid_amount": line.subtotal - line.deducted_amount - line.paid_amount,
                    "pay_amount": line.subtotal - line.deducted_amount - line.paid_amount
                }))
        return deduct_line_ids

    @staticmethod
    def prepare_move_line_ids(line_ids):
        """
        归集明细行数据
        :param line_ids:
        :return:
        """
        deduct_line_ids,sum_pay_amount,sum_discount_amount = [],0,0
        for line in line_ids:
            if line.move_id.qty:
                ratio = line.qty / line.move_id.qty
            else:
                raise ValidationError('需求数量为0，请检查数据准确性！')
            if line.deducted_amount + line.paid_amount != line.subtotal:
                deduct_line_ids.append((0, 0, {
                    "order_line_id": line.move_id.id,
                    "order_move_line_id": line.id,
                    "qty": line.qty,
                    "unpaid_amount": line.move_id.amount_receivable * ratio - line.paid_amount,
                    "pay_amount": line.move_id.amount_receivable * ratio,
                    "after_discount_amount": line.move_id.after_discount_amount * ratio,
                    "whole_order_offer": line.move_id.whole_order_offer * ratio,
                    "amount_receivable": line.move_id.amount_receivable * ratio,
                    "current_paid_amount": line.move_id.amount_receivable * ratio - line.paid_amount
                }))
                sum_discount_amount = sum_discount_amount + line.move_id.whole_order_offer * ratio
                sum_pay_amount = sum_pay_amount + line.move_id.amount_receivable * ratio
        return deduct_line_ids,sum_discount_amount,sum_pay_amount

    def button_pay(self):
        """
        :return:
        """
        if self.state in ('取消', '草稿'):
            raise UserError('该状态不可付款')

        deduct_line_ids,sum_discount_amount,sum_pay_amount = self.prepare_move_line_ids(self.ml_ids)
        # 获取销售、采购订单对应的首付款单金额
        if self.sale_id:
            _amount = sum(self.env['roke.mes.payment'].search([('sale_order_id', '=', self.sale_id.id)]).mapped('amount'))
        elif self.purchase_id:
            _amount = sum(
                self.env['roke.mes.payment'].search([('purchase_order_id', '=', self.purchase_id.id)]).mapped('amount'))
        else:
            _amount = 0
        if deduct_line_ids:
            _pay_amount = round(_amount / len(deduct_line_ids), 2)
            for i in deduct_line_ids:
                if i[2]['pay_amount'] > _pay_amount:
                    i[2]['pay_amount'] = i[2]['pay_amount'] - _pay_amount
                    i[2]['paid_amount'] = _pay_amount
        if self.type == '出库':
            res = self.action_wizard_order_deduct(deduct_line_ids, self.discount_rate, sum_discount_amount,sum_pay_amount)
        else:
            res = self.action_wizard_order_deduct_pay(deduct_line_ids, self.discount_rate, sum_discount_amount,sum_pay_amount)
        return res

    def multi_button_pay(self):
        """
        :return:
        """
        total_deduct_line_ids, type = [], ''
        active_order_ids = self.env["roke.mes.stock.picking"].search([
            ("id", "in", self._context.get('active_ids', []))
        ])
        if active_order_ids.filtered(lambda v: v.state in ('取消', '草稿')):
            raise UserError('所选订单状态，存在取消或者草稿，无法批量收付款!')
        if active_order_ids.filtered(lambda v: v.picking_type_id.picking_logotype not in ('CGRKD', 'XSCKD')):
            raise UserError('只有采购入库与销售出库类型可进行批量收付款操作！')
        # 总的明细
        for rec in active_order_ids:
            type = rec.type
            deduct_line_ids,sum_discount_amount,sum_pay_amount = rec.prepare_move_line_ids(rec.ml_ids)
            # 获取销售、采购订单对应的首付款单金额
            if rec.sale_id:
                _amount = sum(self.env['roke.mes.payment'].search([('sale_order_id', '=', rec.sale_id.id)]).mapped('amount'))
            elif rec.purchase_id:
                _amount = sum(
                    self.env['roke.mes.payment'].search([('purchase_order_id', '=', rec.purchase_id.id)]).mapped('amount'))
            else:
                _amount = 0
            if deduct_line_ids:
                _pay_amount = round(_amount / len(deduct_line_ids), 2)
                for i in deduct_line_ids:
                    if i[2]['pay_amount'] > _pay_amount:
                        i[2]['pay_amount'] = i[2]['pay_amount'] - _pay_amount
                        i[2]['paid_amount'] = _pay_amount

            total_deduct_line_ids = total_deduct_line_ids + deduct_line_ids

        if type == '出库':
            res = self.multi_action_wizard_order_deduct(total_deduct_line_ids, 0, 0, 0)
        else:
            res = self.multi_action_wizard_order_deduct_pay(total_deduct_line_ids, 0, 0, 0)
        return res

    def multi_action_wizard_order_deduct_pay(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        """
        wizard_id = self.env["wizard.stock.picking.deduct"].create({
            "discount_rate": discount_rate if deduct_line_ids else 0,
            "discount_amount": discount_amount if deduct_line_ids else 0,
            "amount_after_discount": amount_after_discount if deduct_line_ids else 0,
            "deduct_line_ids": deduct_line_ids
        })
        if len(self._context.get('active_ids', [])) > 1:
            return {
                'name': '批量付款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.stock.picking.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_stock.new_view_wizard_stock_picking_order_deduct_form_pay').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }
        else:
            return {
                'name': '付款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.stock.picking.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form_pay').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }

    def multi_action_wizard_order_deduct(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        """
        wizard_id = self.env["wizard.stock.picking.deduct"].create({
            "discount_rate": discount_rate if deduct_line_ids else 0,
            "discount_amount": discount_amount if deduct_line_ids else 0,
            "amount_after_discount": amount_after_discount if deduct_line_ids else 0,
            "deduct_line_ids": deduct_line_ids
        })
        if len(self._context.get('active_ids', [])) > 1:
            return {
                'name': '批量收款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.stock.picking.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_stock.new_view_wizard_stock_picking_order_deduct_form').id,
                     'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }
        else:
            return {
                'name': '收款',
                'type': 'ir.actions.act_window',
                'res_model': 'wizard.stock.picking.deduct',
                'view_mode': 'form',
                'target': 'new',
                'views': [
                    (self.env.ref('roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form').id, 'form')
                ],
                'res_id': wizard_id.id,
                'context': {'create': False, 'edit': False, 'delete': False}
            }

    def action_wizard_order_deduct_pay(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        """
        wizard_id = self.env["wizard.stock.picking.deduct"].create({
            "discount_rate": discount_rate if deduct_line_ids else 0,
            "discount_amount": discount_amount if deduct_line_ids else 0,
            "amount_after_discount": amount_after_discount if deduct_line_ids else 0,
            "deduct_line_ids": deduct_line_ids
        })
        view_id = self.env.ref('roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form_pay').id
        return {
            'name': '付款',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.stock.picking.deduct',
            'view_mode': 'form',
            'target': 'new',
            'res_id': wizard_id.id,
            'context': {'create': False, 'edit': False, 'delete': False},
            'views': [[view_id, 'form']]
        }

    def action_wizard_order_deduct(self, deduct_line_ids, discount_rate=0, discount_amount=0, amount_after_discount=0):
        """
        订单优惠向导
        """
        wizard_id = self.env["wizard.stock.picking.deduct"].create({
            "discount_rate": discount_rate if deduct_line_ids else 0,
            "discount_amount": discount_amount if deduct_line_ids else 0,
            "amount_after_discount": amount_after_discount if deduct_line_ids else 0,
            "deduct_line_ids": deduct_line_ids
        })
        view_id = self.env.ref('roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form').id
        return {
            'name': '收款',
            'type': 'ir.actions.act_window',
            'res_model': 'wizard.stock.picking.deduct',
            'view_mode': 'form',
            'target': 'new',
            'res_id': wizard_id.id,
            'context': {'create': False, 'edit': False, 'delete': False},
            'views': [[view_id, 'form']]
        }
