# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class InheritRokeMesSale(models.Model):

    _inherit = "roke.sale.order"

    def _get_sale_return_delivery_line_vals(self, sale_line, src_location, dest_location):
        res = super(InheritRokeMesSale, self)._get_sale_return_delivery_line_vals(sale_line, src_location, dest_location)
        res.update({
            "discount_rate": sale_line.discount_rate,
            "discount_amount": sale_line.discount_amount,
            "after_discount_amount": sale_line.after_discount_amount,
            "whole_order_offer": sale_line.whole_order_offer,
            "amount_receivable": sale_line.amount_receivable,
        })
        return res

    def _get_sale_delivery_line_vals(self, sale_line, src_location, dest_location):
        res = super(InheritRokeMesSale, self)._get_sale_delivery_line_vals(sale_line, src_location, dest_location)
        res.update({
            "discount_rate": sale_line.discount_rate,
            "discount_amount": sale_line.discount_amount,
            "after_discount_amount": sale_line.after_discount_amount,
            "whole_order_offer": sale_line.whole_order_offer,
            "amount_receivable": sale_line.amount_receivable,
        })
        return res

    def sale_return_delivery(self):
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.line_ids:
                if not line.order_qty:
                    continue
                if line.deliver_type == "按产品BOM发货" and line.e_bom_id:
                    base_qty = line.order_qty / line.e_bom_id.qty
                    picking_line_vals += self._get_sale_bom_delivery_vals(line, line.e_bom_id, base_qty, src_location,
                                                                          dest_location)
                else:
                    picking_line_vals.append(
                        (0, 0, self._get_sale_return_delivery_line_vals(line, src_location, dest_location)))
            picking_record = self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "sale_id": record.id,
                "is_red_order": True,
                "partner_id": record.customer_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "move_line_ids": picking_line_vals,
                "discount_rate": record.discount_rate,
                "discount_amount": record.discount_amount,
                "amount_after_discount": record.amount_after_discount
            })
            picking_record.make_confirm()
            is_auto_reserve = self.env['ir.config_parameter'].sudo().get_param('is.auto.reserve',
                                                                               default=False)
            if is_auto_reserve:
                picking_record.action_assign()

    def sale_delivery(self):
        """
            交货，创造出交货单; 统一发货
            :return:
        """
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.line_ids:
                if not line.order_qty:
                    continue
                if line.deliver_type == "按产品BOM发货" and line.e_bom_id:
                    base_qty = line.order_qty / line.e_bom_id.qty
                    picking_line_vals += self._get_sale_bom_delivery_vals(line, line.e_bom_id, base_qty, src_location,
                                                                          dest_location)
                else:
                    picking_line_vals.append(
                        (0, 0, self._get_sale_delivery_line_vals(line, src_location, dest_location)))
            picking_record = self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "sale_id": record.id,
                "partner_id": record.customer_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "move_line_ids": picking_line_vals,
                "discount_rate": record.discount_rate,
                "discount_amount": record.discount_amount,
                "amount_after_discount": record.amount_after_discount,
                "employee_id": record.sale_user_id.id
            })
            picking_record.make_confirm()
            is_auto_reserve = self.env['ir.config_parameter'].sudo().get_param('is.auto.reserve',
                                                                               default=False)
            if is_auto_reserve:
                picking_record.action_assign()

class InheritRokeMesPurchase(models.Model):

    _inherit = "roke.purchase.order"

    def purchase_return_receiving(self):
        """
        退货
        :return:
        """
        # 创建收货单
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.detail_ids:
                if not line.qty:
                    continue
                picking_line_vals.append((0, 0, {
                    "purchase_line_id": line.id,
                    "src_location_id": src_location.id,
                    "dest_location_id": dest_location.id,
                    "product_id": line.product_id.id,
                    "qty": -1 * line.qty,
                    "origin": record.code,
                    "unit_price": line.unit_price,
                    "subtotal": -1 * line.subtotal,
                    "discount_rate": line.discount_rate,
                    "discount_amount": line.discount_amount,
                    "after_discount_amount": line.after_discount_amount,
                    "whole_order_offer": line.whole_order_offer,
                    "amount_receivable": line.amount_receivable,
                }))
            self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "purchase_id": record.id,
                "partner_id": record.supplier_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "employee_id": record.employee_id.id,
                "move_line_ids": picking_line_vals,
                "is_red_order": True,
                "discount_rate": record.discount_rate,
                "discount_amount": record.discount_amount,
                "amount_after_discount": record.amount_after_discount
            })

    def purchase_receiving(self):
        """
        收货
        :return:
        """
        # 创建收货单
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.detail_ids:
                if not line.qty:
                    continue
                picking_line_vals.append((0, 0, {
                    "purchase_line_id": line.id,
                    "src_location_id": src_location.id,
                    "dest_location_id": dest_location.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "origin": record.code,
                    "unit_price": line.unit_price,
                    "subtotal": line.subtotal,
                    "discount_rate": line.discount_rate,
                    "discount_amount": line.discount_amount,
                    "after_discount_amount": line.after_discount_amount,
                    "whole_order_offer": line.whole_order_offer,
                    "amount_receivable": line.amount_receivable,
                }))
            self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "purchase_id": record.id,
                "partner_id": record.supplier_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "employee_id": record.employee_id.id,
                "move_line_ids": picking_line_vals,
                "discount_rate": record.discount_rate,
                "discount_amount": record.discount_amount,
                "amount_after_discount": record.amount_after_discount
            })