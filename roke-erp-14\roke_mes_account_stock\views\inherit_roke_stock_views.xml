<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 采购付款按钮-->
    <record id="inherit_view_roke_mes_purchase_red_stock_picking_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_purchase_red_stock_picking_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='make_revoke']" position="after">
                                <button name="button_pay" string="付款" type="object"
                                        attrs="{'invisible': ['|', ('state', 'in' ,['取消', '草稿']),('type', '!=', '入库')]}"
                                        class="oe_read_only"/>
                <field name="type" invisible="1"/>
                <button name="button_create_invoice" string="创建发票" type="object"
                        attrs="{'invisible': ['|', '|', ('purchase_invoice_ids', '>', 0), ('state', '!=' ,'完成'), ('type', '!=', '入库')]}"
                        class="oe_read_only"/>
                <field name="type" invisible="1"/>
                <field name="purchase_invoice_ids" invisible="1"/>
            </xpath>
            <xpath expr="//group[@id='g2']" position="after">
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="discount_amount" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1" force_save="1"/>
                    </group>
                    <field name="is_open_tax" invisible="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_mes_purchase_receiving_move_tree" model="ir.ui.view">
        <field name="name">inherit_roke.mes.stock.purchase.receiving.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='subtotal']" position="after">

                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="amount_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="tax_amount" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>

                <field name="discount_rate"/>
                <field name="discount_amount"
                       sum="折扣额合计"/>
                <field name="after_discount_amount"
                       sum="折扣后金额合计"/>
                <field name="whole_order_offer"
                       readonly="1" force_save="1" sum="整单优惠合计"/>
                <field name="amount_receivable"
                       readonly="1" force_save="1" string="应付金额" sum="应付金额合计"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_mes_purchase_return_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_purchase_return_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_return_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@id='g2']" position="after">
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="discount_amount" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1" force_save="1"/>
                    </group>
                    <field name="is_open_tax" invisible="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_mes_purchase_return_move_tree" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_purchase_return_move_tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_return_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='subtotal']" position="after">

                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="amount_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="tax_amount" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>

                <field name="discount_rate"/>
                <field name="discount_amount"
                       sum="折扣额合计"/>
                <field name="after_discount_amount"
                       sum="折扣后金额合计"/>
                <field name="whole_order_offer"
                       readonly="1" force_save="1" sum="整单优惠合计"/>
                <field name="amount_receivable"
                       readonly="1" force_save="1" string="应付金额" sum="应付金额合计"/>
            </xpath>
        </field>
    </record>
    <!-- 销售收款按钮-->
    <record id="inherit_view_roke_mes_sale_red_stock_picking_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_purchase_red_stock_picking_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='make_revoke']" position="after">
                                <button name="button_pay" string="收款" type="object"
                                        attrs="{'invisible': ['|', ('state', 'in' ,['取消', '草稿']), ('type', '!=', '出库')]}"
                                        class="oe_read_only"/>
                <field name="type" invisible="1"/>
                <button name="button_create_invoice" string="创建发票" type="object"
                        attrs="{'invisible': ['|', '|', ('sale_invoice_ids', '>', 0), ('state', '!=' ,'完成'), ('type', '!=', '出库')]}"
                        class="oe_read_only"/>
                <field name="type" invisible="1"/>
                <field name="sale_invoice_ids" invisible="1"/>
            </xpath>
            <xpath expr="//group[@id='g2']" position="after">
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="discount_amount" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1" force_save="1"/>
                    </group>
                    <field name="is_open_tax" invisible="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_mes_sale_deliver_move_tree" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_sale_deliver_move_tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='subtotal']" position="after">

                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="amount_excl_tax" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="tax_amount" readonly="1" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>

                <field name="discount_rate"/>
                <field name="discount_amount"
                       sum="折扣额合计"/>
                <field name="after_discount_amount"
                       sum="折扣后金额合计"/>
                <field name="whole_order_offer"
                       readonly="1" force_save="1" sum="整单优惠合计"/>
                <field name="amount_receivable"
                       readonly="1" force_save="1" string="应收金额" sum="应收金额合计"/>
            </xpath>
        </field>
    </record>


    <record id="inherit_view_roke_mes_sale_return_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_sale_return_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_return_form"/>
        <field name="arch" type="xml">
            <xpath expr="//group[@id='g2']" position="after">
                <group col='4'>
                    <group>
                        <field name="discount_rate" widget="percentage" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="discount_amount" attrs="{'readonly': [('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="amount_after_discount" readonly="1" force_save="1"/>
                    </group>
                    <field name="is_open_tax" invisible="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_mes_sale_return_move_tree" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_sale_return_move_tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_return_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='subtotal']" position="after">

                <field name="tax_rate" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="unit_price_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="amount_excl_tax" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>
                <field name="tax_amount" attrs="{'column_invisible': [('parent.is_open_tax', '!=', True)]}"/>

                <field name="discount_rate" optional="hide"/>
                <field name="discount_amount"
                       sum="折扣额合计" optional="hide"/>
                <field name="after_discount_amount"
                       sum="折扣后金额合计" optional="hide"/>
                <field name="whole_order_offer"
                       readonly="1" force_save="1" sum="整单优惠合计" optional="hide"/>
                <field name="amount_receivable"
                       readonly="1" force_save="1" string="应收金额" sum="应收金额合计" optional="hide"/>
            </xpath>
        </field>
    </record>
    <!-- 发货单联查 -->
    <record id="inherit_view_roke_mes_sale_deliver_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_sale_deliver_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object" access="1" smart_button="查看收款单"
                        name="action_view_payment"
                        class="oe_stat_button"
                        icon="fa-money" attrs="{'invisible':[('payment_ids','=',[])]}">
                    <field name="payment_count" widget="statinfo" string="收款单" help="查看收款单"/>
                    <field name="payment_ids" invisible="1"/>
                </button>

                <button type="object" access="1" smart_button="查看发票"
                        name="action_view_invoice"
                        class="oe_stat_button"
                        icon="fa-money" attrs="{'invisible':[('sale_invoice_ids','=',[])]}">
                    <field string="发票" name="invoice_count" widget="statinfo"  help="查看发票"/>
                    <field name="sale_invoice_ids" invisible="1"/>
                </button>
            </xpath>
        </field>
    </record>
    <!--收货单联查-->
    <record id="inherit_view_roke_mes_purchase_receiving_form" model="ir.ui.view">
        <field name="name">inherit_view_roke_mes_purchase_receiving_form</field>
        <field name="model">roke.mes.stock.picking</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='button_box']" position="inside">
                <button type="object"
                        name="action_view_payment"
                        class="oe_stat_button"
                        icon="fa-money" attrs="{'invisible':[('payment_ids','=',[])]}">
                    <field name="payment_count" widget="statinfo" string="付款单" help="查看付款单"/>
                    <field name="payment_ids" invisible="1"/>
                </button>
                <button type="object" access="1" smart_button="查看发票"
                        name="action_view_invoice"
                        class="oe_stat_button"
                        icon="fa-money" attrs="{'invisible':[('purchase_invoice_ids','=',[])]}">
                    <field string="发票" name="invoice_count" widget="statinfo"  help="查看发票"/>
                    <field name="purchase_invoice_ids" invisible="1"/>
                </button>
            </xpath>
        </field>
    </record>

    <!-- 批量优惠收款 -->
    <record id="action_multi_sale_stock" model="ir.actions.server">
        <field name="name">批量收付款</field>
        <field name="model_id" ref="model_roke_mes_stock_picking"/>
        <field name="binding_model_id" ref="model_roke_mes_stock_picking"/>
        <field name="type">ir.actions.server</field>
        <field name="state">code</field>
        <field name="binding_view_types">list</field>
        <field name="code">
            action = model.multi_button_pay()
        </field>
    </record>
</odoo>