# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)


class InheritRokeWizardMesPickingAllMove(models.TransientModel):
    _inherit = "roke.wizard.mes.picking.all.move"

    def confirm(self):
        res = super(InheritRokeWizardMesPickingAllMove, self).confirm()
        is_auto_invoicing = self.env['ir.config_parameter'].sudo().get_param('is.auto.invoicing', default=False)
        # 自动生成发票
        if is_auto_invoicing and self.picking_id.picking_type_id.picking_logotype in ['CGRKD', 'XSCKD']:
            self.picking_id.make_invoice()
        return res