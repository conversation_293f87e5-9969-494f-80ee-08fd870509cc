# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class InheritPurchaseReturnStockWizard(models.TransientModel):
    _inherit = "roke.purchase.return.stock.wizard"

    def prepare_move_dict(self, line):
        res = super(InheritPurchaseReturnStockWizard, self).prepare_move_dict(line)
        res.update({
            # 税率
            "tax_rate": line.purchase_line_id.tax_rate,
            "unit_price_excl_tax": line.purchase_line_id.unit_price_excl_tax
        })
        return res