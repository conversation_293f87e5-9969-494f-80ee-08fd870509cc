# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class InheritSaleDeliveryWizard(models.TransientModel):

    _inherit = "roke.sale.delivery.wizard"

    def _get_line_values(self, sale_line):
        res = super(InheritSaleDeliveryWizard, self)._get_line_values(sale_line)
        if sale_line.order_qty:
            ratio = (sale_line.order_qty - sale_line.deliver_qty) / sale_line.order_qty
        else:
            ratio = 0
        res.update({
            "discount_rate": sale_line.discount_rate,
            "discount_amount": sale_line.discount_amount * ratio,
            "after_discount_amount": sale_line.after_discount_amount * ratio,
            "whole_order_offer": sale_line.whole_order_offer * ratio,
            "amount_receivable": sale_line.amount_receivable * ratio,
        })
        return res

    def _get_sale_delivery_line_vals(self, sale_line, src_location, dest_location, wizard_line):
        res = super(InheritSaleDeliveryWizard, self)._get_sale_delivery_line_vals(sale_line, src_location, dest_location, wizard_line)
        res.update({
            "discount_rate": wizard_line.discount_rate,
            "discount_amount": wizard_line.discount_amount,
            "after_discount_amount": wizard_line.after_discount_amount,
            "whole_order_offer": wizard_line.whole_order_offer,
            "amount_receivable": wizard_line.amount_receivable,
            # 税率
            "tax_rate": wizard_line.order_line_id.tax_rate,
            "unit_price_excl_tax": wizard_line.order_line_id.unit_price_excl_tax
        })
        return res

    def get_picking_val_value(self, picking_type, src_location, dest_location, picking_line_vals):
        res = super(InheritSaleDeliveryWizard, self).get_picking_val_value(picking_type, src_location, dest_location, picking_line_vals)
        res.update({
            "discount_rate": self.sale_id.discount_rate,
            "discount_amount": sum(self.line_ids.mapped('whole_order_offer')),
            "amount_after_discount": sum(self.line_ids.mapped('amount_receivable'))
        })
        return res

class InheritSaleDeliveryLineWizard(models.TransientModel):

    _inherit = "roke.sale.delivery.line.wizard"

    discount_rate = fields.Float('折扣率')
    discount_amount = fields.Float('折扣额', digits='KCJE')
    after_discount_amount = fields.Float('折扣后金额', digits='KCJE')
    whole_order_offer = fields.Float('整单优惠', digits='KCJE')
    amount_receivable = fields.Float('应收金额', digits='KCJE')

    @api.onchange('current_stock_qty')
    def _onchange_current_stock_qty(self):
        if self.current_stock_qty:
            if self.order_line_id.order_qty - self.order_line_id.deliver_qty == 0:
                ratio = 0
            else:
                ratio = self.current_stock_qty / (self.order_line_id.order_qty - self.order_line_id.deliver_qty)
            self.discount_amount = self.discount_amount * ratio
            self.after_discount_amount = self.after_discount_amount * ratio
            self.whole_order_offer = self.whole_order_offer * ratio
            self.amount_receivable = self.amount_receivable * ratio
        else:
            raise ValidationError('本次发货数不能为零')

