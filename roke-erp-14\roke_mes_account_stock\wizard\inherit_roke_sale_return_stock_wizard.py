# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class InheritSaleReturnStockWizard(models.TransientModel):

    _inherit = "roke.sale.return.stock.wizard"

    def prepare_move_dict(self, line):
        res = super(InheritSaleReturnStockWizard, self).prepare_move_dict(line)
        res.update({
            # 税率
            "tax_rate": line.order_line_id.tax_rate,
            "unit_price_excl_tax": line.order_line_id.unit_price_excl_tax
        })
        return res
