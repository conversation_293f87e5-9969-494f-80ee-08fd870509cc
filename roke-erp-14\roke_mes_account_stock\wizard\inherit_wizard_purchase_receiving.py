# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class InheritRokePurchaseReceivingWizard(models.TransientModel):
    _inherit = "roke.purchase.receiving.wizard"

    def prepare_line_value(self, line):
        res = super(InheritRokePurchaseReceivingWizard, self).prepare_line_value(line)
        if line.qty:
            ratio = (line.qty - line.receiving_qty) / line.qty
        else:
            ratio = 0
        res.update({
            "discount_rate": line.discount_rate,
            "discount_amount": line.discount_amount * ratio,
            "after_discount_amount": line.after_discount_amount * ratio,
            "whole_order_offer": line.whole_order_offer * ratio,
            "amount_receivable": line.amount_receivable * ratio,
        })
        return res

    def prepare_move_dict(self, line, src_location_id, dest_location_id):
        res = super(InheritRokePurchaseReceivingWizard, self).prepare_move_dict(line, src_location_id, dest_location_id)
        res.update({
            "discount_rate": line.discount_rate,
            "discount_amount": line.discount_amount,
            "after_discount_amount": line.after_discount_amount,
            "whole_order_offer": line.whole_order_offer,
            "amount_receivable": line.amount_receivable,
            # 税率
            "tax_rate": line.purchase_line_id.tax_rate,
            "unit_price_excl_tax": line.purchase_line_id.unit_price_excl_tax
        })
        return res

    def prepare_picking_dict(self, picking_type, src_location_id, dest_location_id, move_values):
        res = super(InheritRokePurchaseReceivingWizard, self).prepare_picking_dict(picking_type, src_location_id, dest_location_id, move_values)
        purchase_order_id = self.get_purchase_order_id()
        if purchase_order_id:
            purchase_order_rec = self.env["roke.purchase.order"].search([("id", "=", purchase_order_id)])
            res.update({
                "discount_rate": purchase_order_rec.discount_rate,
                "discount_amount": sum(self.line_ids.mapped('whole_order_offer')),
                "amount_after_discount": sum(self.line_ids.mapped('amount_receivable'))
            })
        return res

class InheritRokePurchaseReceivingWizardLine(models.TransientModel):
    _inherit = "roke.purchase.receiving.wizard.line"

    discount_rate = fields.Float('折扣率')
    discount_amount = fields.Float('折扣额', digits='KCJE')
    after_discount_amount = fields.Float('折扣后金额', digits='KCJE')
    whole_order_offer = fields.Float('整单优惠', digits='KCJE')
    amount_receivable = fields.Float('应收金额', digits='KCJE')

    @api.onchange('receive_qty')
    def _onchange_receive_qty(self):
        if self.receive_qty:
            if self.purchase_line_id.qty - self.purchase_line_id.receiving_qty == 0:
                ratio = 0
            else:
                ratio = self.receive_qty / (self.purchase_line_id.qty - self.purchase_line_id.receiving_qty)
            self.discount_amount = self.discount_amount * ratio
            self.after_discount_amount = self.after_discount_amount * ratio
            self.whole_order_offer = self.whole_order_offer * ratio
            self.amount_receivable = self.amount_receivable * ratio
        else:
            raise ValidationError('本次收货数不能为零')
