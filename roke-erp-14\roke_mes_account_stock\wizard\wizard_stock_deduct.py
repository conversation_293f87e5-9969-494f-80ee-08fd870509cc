# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class WizardStockPickingDeduct(models.TransientModel):
    _name = "wizard.stock.picking.deduct"
    _description = "库存单据收付款"

    discount_rate = fields.Float('优惠率')
    discount_amount = fields.Float('优惠金额', digits='KCJE')
    amount_after_discount = fields.Float('优惠后金额', digits='KCJE')
    deduct_line_ids = fields.One2many("wizard.stock.picking.deduct.line", "deduct_id", string="订单明细")

    def action_deduct_payment(self, res_model, result_id):
        """
        打开付款单
        :param res_model: 收付款模型
        :param result_id: 收款
        :return:
        """
        return {
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'current',
            'res_model': res_model,
            'res_id': result_id.id,
            'context': {'create': True, 'edit': True, 'delete': True}
        }

    def action_confirm_deduct(self):
        """
        确认优惠，生成付款单
        :return:
        """
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部完成或添加明细数据。')
        total_payment = 0
        picking = self.deduct_line_ids.mapped("order_line_id").mapped("picking_id")
        customer_id = self.deduct_line_ids[0].order_line_id.picking_id.partner_id.id if self.deduct_line_ids else False
        if not customer_id:
            raise UserError('合作伙伴/客户不能为空')
        line_dict_values = []
        d_price = 0

        if picking.sale_id:
            _amount = sum(self.env['roke.mes.payment'].search([('sale_order_id', '=', picking.sale_id.id)]).mapped('amount'))
        elif picking.purchase_id:
            _amount = sum(
                self.env['roke.mes.payment'].search([('purchase_order_id', '=', picking.purchase_id.id)]).mapped('amount'))
        else:
            _amount = 0
        _pay_amount = round(_amount / len(self.deduct_line_ids), 2)
        for item in self.deduct_line_ids:
            d_price += item.deduct_amount
            total_payment += item.pay_amount
            _paid_amount = item.pay_amount + _pay_amount
            line_dict_values.append((0, 0, {
                "stock_move_id": item.order_line_id.id,
                "stock_move_line_id": item.order_move_line_id.id,
                "deducted_amount": 0,
                # "paid_amount": item.pay_amount,
                "paid_amount": item.current_paid_amount,
            }))
            print(item.order_move_line_id.received_amount,item.current_paid_amount)
            item.order_move_line_id.write({'received_amount': item.order_move_line_id.received_amount + item.current_paid_amount})
        # 创建收款单
        note = "来源单据:{}".format("\n".join(picking.mapped("code")))
        picking_type = picking[0].picking_type_id.type  # 临时修改，多调拨单时报错
        if picking_type == "出库":
            partner_type = '客户'
            payment_type = "收款"
            red_type = "收款"
            res_model = "roke.mes.collection"
            pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '收款')])
        elif picking_type == "入库":
            partner_type = '供应商'
            payment_type = "付款"
            red_type = "付款"
            res_model = "roke.mes.pay"
            pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '付款')])
        else:
            raise UserError("内部调拨业务的单据不支持收付款")

        is_deduct = True if sum(self.deduct_line_ids.mapped("current_paid_amount")) else False
        # 如果优惠后金额不为0说明有优惠
        if self.amount_after_discount > 0:
            total_payment = sum(self.deduct_line_ids.mapped("current_paid_amount"))
        picking_order = self.env['roke.mes.stock.picking'].search([('id', '=', self.env.context.get('active_id'))])
        # 判断是否退货
        if picking_order.is_red_order:
            red_type = '退款'

        banks = self.deduct_line_ids[0].order_line_id.picking_id.partner_id.bank_ids
        bank_id = banks[0].id if banks else ''
        data = {
            "state": "草稿",
            "payment_type": payment_type,
            "partner_type": partner_type,
            "partner_id": customer_id,
            "payment_date": fields.Date.context_today(self),
            "amount": sum(self.deduct_line_ids.mapped("current_paid_amount")),
            "pay_amount": sum(self.deduct_line_ids.mapped("current_paid_amount")),
            "is_edit": True,
            "is_deduct": True,
            "note": note,
            "payment_line_ids": line_dict_values,
            "deduct_amount": d_price,
            'is_advance_payment': True,
            'origin_order': picking_order.code,
            'red_type': red_type,
            'order_type': pay_id.id,
            'bank_account_id': bank_id
        }
        if picking.purchase_id and picking_type == '入库':
            _amount_paid = picking.purchase_id.amount_paid
            _amount_paid += total_payment
            picking.purchase_id.write({"amount_paid": _amount_paid})
            data.update({"purchase_order_id": picking.purchase_id.id})
        if picking.sale_id and picking_type == '出库':
            _amount_paid = picking.sale_id.amount_paid
            _amount_paid += total_payment
            picking.sale_id.write({"amount_paid": _amount_paid})
            data.update({"sale_order_id": picking.sale_id.id})
        result_id = self.env[res_model].create(data)
        # 采购单关联付款单
        picking.write({"payment_ids": [(4, result_id.payment_id.id)]})
        return self.action_deduct_payment(res_model, result_id)

    def multi_action_deduct_payment(self, res_model, result_id):
        """
        打开付款单
        :param res_model: 收付款模型
        :param result_id: 收款
        :return:
        """
        show_title = '本次创建的收款单' if res_model == 'roke.mes.collection' else '本次创建的付款单'
        if len(result_id) > 1:
            return {
                'name': show_title,
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': res_model,
                'domain': [('id', 'in', result_id)],
                'views': [
                    (self.env.ref('roke_mes_account.view_roke_mes_collection_tree').id, 'tree'),
                    (self.env.ref('roke_mes_account.view_roke_mes_collection_form').id, 'form')
                ],
                'context': {'create': True, 'edit': True, 'delete': True}
            }
        else:
            return {
                'name': show_title,
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_model': res_model,
                'res_id': result_id[0],
                'context': {'create': True, 'edit': True, 'delete': True}
            }

    def multi_action_confirm_deduct(self):
        """
        确认优惠，生成付款单
        :return:
        """
        result_ids,res_model = [],''
        if not self.deduct_line_ids:
            raise UserError('无明细，已全部收款或添加明细数据。')
        # 归集仓库调拨单
        picking_order_ids = self.deduct_line_ids.mapped("order_line_id").mapped("picking_id")
        # 处理业务伙伴为空问题
        not_partner_ids = picking_order_ids.filtered(lambda p: not p.partner_id)
        if len(not_partner_ids):
            raise UserError('单据编号%s下的单据业务伙伴为空！' % not_partner_ids.mapped("code"))
        # 遍历
        for pick in picking_order_ids:
            # 业务伙伴
            partner_id = pick.partner_id
            # 取调拨单下明细
            line_dict_values, d_price, total_payment = [], 0, 0
            if pick.sale_id:
                _amount = sum(
                    self.env['roke.mes.payment'].search([('sale_order_id', '=', pick.sale_id.id)]).mapped('amount'))
            elif pick.purchase_id:
                _amount = sum(
                    self.env['roke.mes.payment'].search([('purchase_order_id', '=', pick.purchase_id.id)]).mapped(
                        'amount'))
            else:
                _amount = 0
            # 过滤当前调拨单下的产品明细
            deduct_line_ids = self.deduct_line_ids.filtered(lambda r: r.order_line_id.picking_id.id == pick.id)
            _pay_amount = round(_amount / len(deduct_line_ids), 2)
            for deduct in deduct_line_ids:
                d_price += deduct.deduct_amount
                total_payment += deduct.pay_amount
                _paid_amount = deduct.pay_amount + _pay_amount
                line_dict_values.append((0, 0, {
                    "stock_move_id": deduct.order_line_id.id,
                    "stock_move_line_id": deduct.order_move_line_id.id,
                    "deducted_amount": 0,
                    "paid_amount": deduct.current_paid_amount,
                }))
                deduct.order_move_line_id.write(
                    {'received_amount': deduct.order_move_line_id.received_amount + deduct.current_paid_amount})
            # 创建收付款单
            note = "来源单据:{}".format(pick.code)
            picking_type = pick.picking_type_id.type  # 临时修改，多调拨单时报错
            if picking_type == "出库":
                partner_type = '客户'
                payment_type = "收款"
                red_type = "收款"
                res_model = "roke.mes.collection"
                pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '收款')])
            elif picking_type == "入库":
                partner_type = '供应商'
                payment_type = "付款"
                red_type = "付款"
                res_model = "roke.mes.pay"
                pay_id = self.env['roke.mes.pay.type.selection'].search([('value', '=', '付款')])
            else:
                raise UserError("内部调拨业务的单据不支持收付款")
            # 如果优惠后金额不为0说明有优惠
            total_payment = sum(deduct_line_ids.mapped("current_paid_amount"))
            # 判断是否退货
            if pick.is_red_order:
                red_type = '退款'
            banks = partner_id.bank_ids if partner_id else False
            bank_id = banks[0].id if banks else ''
            data = {
                "state": "草稿",
                "payment_type": payment_type,
                "partner_type": partner_type,
                "partner_id": partner_id.id if partner_id else False,
                "payment_date": fields.Date.context_today(self),
                "amount": sum(deduct_line_ids.mapped("current_paid_amount")),
                "pay_amount": sum(deduct_line_ids.mapped("current_paid_amount")),
                "is_edit": True,
                "is_deduct": True,
                "note": note,
                "payment_line_ids": line_dict_values,
                "deduct_amount": d_price,
                'is_advance_payment': True,
                'origin_order': pick.code,
                'red_type': red_type,
                'order_type': pay_id.id,
                'bank_account_id': bank_id
            }
            if pick.purchase_id and picking_type == '入库':
                _amount_paid = pick.purchase_id.amount_paid
                _amount_paid += total_payment
                pick.purchase_id.write({"amount_paid": _amount_paid})
                data.update({"purchase_order_id": pick.purchase_id.id})
            if pick.sale_id and picking_type == '出库':
                _amount_paid = pick.sale_id.amount_paid
                _amount_paid += total_payment
                pick.sale_id.write({"amount_paid": _amount_paid})
                data.update({"sale_order_id": pick.sale_id.id})
            result_id = self.env[res_model].create(data)
            # 关联收付款单
            pick.write({"payment_ids": [(4, result_id.payment_id.id)]})
            result_ids.append(result_id.id)
        return self.multi_action_deduct_payment(res_model, result_ids)


class WizardStockPickingDeductLine(models.TransientModel):
    _name = "wizard.stock.picking.deduct.line"
    _description = "收款明细"

    deduct_id = fields.Many2one("wizard.stock.picking.deduct", string="优惠向导")
    order_line_id = fields.Many2one("roke.mes.stock.move", string="产品", required=True)
    order_move_line_id = fields.Many2one("roke.mes.stock.move.line", string="产品", required=True)
    product_id = fields.Many2one(related="order_line_id.product_id", string="产品")
    unit_price = fields.Float(related="order_line_id.unit_price", digits='KCDJ')
    qty = fields.Float(string="数量", digits='KCSL')
    subtotal = fields.Float(related="order_move_line_id.subtotal", string="金额", digits='KCJE')
    deducted_amount = fields.Float(related="order_line_id.deducted_amount", string="已优惠金额", digits='KCJE')
    paid_amount = fields.Float(string="已收/付款金额", digits='KCJE')

    unpaid_amount = fields.Float(string="待收/付款金额", digits='KCJE')
    deduct_amount = fields.Float(string="优惠金额", digits='KCJE')
    pay_amount = fields.Float(string="收/付款金额", digits='KCJE')

    after_discount_amount = fields.Float(string="折扣后金额", digits='KCJE')
    whole_order_offer = fields.Float(string="整单优惠", digits='KCJE')
    amount_receivable = fields.Float(string="应收/付金额", digits='KCJE')
    current_paid_amount = fields.Float(string="本次收/付款金额", digits='KCJE')

    @api.onchange("deduct_amount")
    def onchange_deduct_amount(self):
        if self.deduct_amount > self.unpaid_amount:
            raise ValidationError("优惠金额不能大于待收/付款金额")
        self.pay_amount = round(self.unpaid_amount - self.deduct_amount, 2)

    @api.onchange("pay_amount")
    def onchange_pay_amount(self):
        if self.pay_amount > round(self.unpaid_amount - self.deduct_amount, 2):
            raise ValidationError("收/付款金额不能大于优惠后金额")
