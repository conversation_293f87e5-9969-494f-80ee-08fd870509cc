# -*- coding: utf-8 -*-
{
    'name': '融科MES 待办协同',
    'version': '********',
    'category': 'mes',
    'depends': ['web', 'mail','roke_mes_base'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
    """,
    'data': [
        'security/ir.model.access.csv',
        'security/security_groups.xml',
        'data/data_mail_activity_label.xml',
        'data/data_mail_activity_state.xml',
        'data/data_message_template.xml',
        'data/app_function_data.xml',
        'views/assets.xml',
        'views/menu_root.xml',
        'views/inherit_mail_activity.xml',
        # 'views/roke_activity.xml', // task - 7275
    ],
    'qweb': [
        'static/src/xml/feedback_popup.xml',
        'static/src/xml/action_activities.xml',
    ],
    'application': False,
    'installable': True,
    'auto_install': ['web'],
}
