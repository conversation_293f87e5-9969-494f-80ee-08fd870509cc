# -*- coding: utf-8 -*-

import logging
import math
import os
from datetime import timedelta, datetime
from dateutil.parser import parse
from odoo.addons.roke_mes_client.controller import app_general_order as app
from odoo import http, SUPERUSER_ID

BASE_DIR = os.path.dirname(os.path.dirname(__file__))

try:
    import simplejson as json
except ImportError:
    import json
_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]
methods = ["POST", "OPTIONS"]


class MailActivity(http.Controller):

    @staticmethod
    def _get_activity_list(
            record_type, page_size, page_no, res_model, res_id, state_id, type_id, user_id, deadline, state_done):
        """
        组装活动列表数据
        :param record_type: 请求类型。launch：我发起的，dealt: 待办的
        :param page_size: 每页显示数量
        :param page_no: 请求页码
        :return:
        """
        domain = []
        if res_model and res_id:
            domain.append(("res_model", "=", res_model))
            domain.append(("res_id", "=", res_id))

        if record_type == "launch":  # 我发起的*(状态、类型、分配给、截止时间筛选)
            if state_id:
                domain.append(("state_id", "=", state_id))
            if type_id:
                domain.append(("activity_type_id", "=", type_id))
            if user_id:
                domain.append(("user_id", "=", user_id))
            if deadline:
                domain.append(("date_deadline", "=", deadline))
            domain.append(("create_uid", "=", http.request.env.user.id))
        else:  # 我待办的*（已完成、未完成）取消的不在查询范围内
            done_state_ids = http.request.env["roke.mail.activity.state"].search([("is_done", "=", True)])
            if state_done:  # 已完成
                domain.append(("state_id", "in", done_state_ids.ids))
            else:  # 未完成
                cancel_state_id = http.request.env.ref("roke_mes_activity.mail_activity_state_cancel")
                exclude_state_ids = done_state_ids.ids + cancel_state_id.ids
                domain.append(("state_id", "not in", exclude_state_ids))  # 非完成的、非取消的
            domain.append(("user_id", "=", http.request.env.user.id))

        search_results = http.request.env['mail.activity'].with_context(lang='zh_CN').search(domain, order="id desc")
        if page_size:
            total_page = math.ceil(len(search_results) / page_size)  # 总页数
            page_records = search_results[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
            page_records = search_results

        query_results = [
            # 处理其他关联字段对应模型的多公司权限问题，使用sudo提权组织数据
            result.sudo().api_generate_activity_data()
            # result.api_generate_activity_data()
            for result in page_records
        ]
        return_data = {
            "state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
            "total_number": len(search_results), "result": query_results
        }
        return return_data

    @http.route('/roke/get_activity_list', type='json', methods=methods, auth='user', csrf=False, cors='*')
    def get_activity_list(self):
        """
        获取活动列表
        :return:
        """
        record_type = http.request.jsonrequest.get("record_type")  # 类型
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        res_model = http.request.jsonrequest.get('res_model', False)
        res_id = http.request.jsonrequest.get('res_id', False)

        state_id = http.request.jsonrequest.get('state_id', False)  # 活动状态ID
        type_id = http.request.jsonrequest.get('type_id', False)  # 活动类型ID
        user_id = http.request.jsonrequest.get('user_id', False)  # 指派给
        date_deadline = http.request.jsonrequest.get('date_deadline', False)  # 截止时间
        state_done = http.request.jsonrequest.get('state_done', False)  # 截止时间

        request_data = self._get_activity_list(
            record_type, page_size, page_no, res_model, res_id, state_id, type_id, user_id, date_deadline, state_done
        )
        return request_data

    @http.route('/roke/deal_activity', type='json', methods=methods, auth='user', csrf=False, cors='*')
    def deal_activity(self):
        state_id = http.request.jsonrequest.get("state_id")  # 状态ID
        activity_id = http.request.jsonrequest.get('activity_id')  # 活动ID
        note = http.request.jsonrequest.get('note', "")  # 备注
        state_res = http.request.env["roke.mail.activity.state"].search([("id", "=", state_id)])
        if not state_res:
            return {
                "state": "error", "msgs": "活动状态数据异常，请返回重试", "result": None
            }
        activity_res = http.request.env["mail.activity"].with_context(lang='zh_CN').search([("id", "=", activity_id)])
        if not activity_res:
            return {
                "state": "error", "msgs": f"待办活动数据异常，活动不存在。", "result": None
            }
        if state_res.id == activity_res.state_id.id:
            return {
                "state": "error", "msgs": f"当前待办活动状态已经是[{activity_res.state_id.name}]，请勿重复操作。",
                "result": None
            }
        if state_res.is_done:
            new_context = http.request.env.context.copy()
            new_context['state_id'] = state_id
            if note:
                # 处理其他关联字段对应模型的多公司权限问题，使用sudo提权处理数据
                activity_res.sudo().with_context(new_context).action_feedback(feedback=note)
            else:
                # 处理其他关联字段对应模型的多公司权限问题，使用sudo提权处理数据
                activity_res.sudo().with_context(new_context).action_done()
            message = "活动已完成"
        elif state_res.id == http.request.env.ref("roke_mes_activity.mail_activity_state_cancel").id:
            activity_res.write({"state_id": state_id})
            # 处理其他关联字段对应模型的多公司权限问题，使用sudo提权处理数据
            activity_res.sudo().action_activity_operate(operate_type="cancel", feedback=note)
            message = "活动已取消"
        else:
            activity_res.write({"state_id": state_id})
            # 处理其他关联字段对应模型的多公司权限问题，使用sudo提权处理数据
            activity_res.sudo().action_activity_operate(operate_type="other", feedback=note)
            message = "活动已处理"
        return {
            "state": "success", "msgs": message, "result": activity_res.id
        }

    @http.route('/roke/get_activity_state_list', type='json', methods=methods, auth='user', csrf=False, cors='*')
    def get_activity_state_list(self):
        activity_state_ids = http.request.env["roke.mail.activity.state"].search([])
        state_list = [
            {
                "value": activity_state.id,
                "text": activity_state.name,
            } for activity_state in activity_state_ids
        ]
        return {
            "state": "success", "msgs": "获取成功", "result": state_list
        }

    @staticmethod
    def _get_activity_log_list(activity_id, page_size, page_no):
        domain = [
            ["message_type", "!=", "user_notification"],
            ["model", "=", "mail.activity"],
            ["res_id", "=", activity_id]
        ]
        message_ids = http.request.env["mail.message"].message_fetch(domain, limit=10000)
        if page_size:
            total_page = math.ceil(len(message_ids) / page_size)  # 总页数
            page_records = message_ids[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
            page_records = message_ids

        def format_date(origin):
            # 格式化日志时间
            time = (origin + timedelta(hours=8)).timetuple()
            return f"{time.tm_mon}月{time.tm_mday}日 {time.tm_hour}:{time.tm_min}:{time.tm_sec}"

        def format_date_string(string):
            if not string:
                return "False"
            else:
                time = parse(string)
                # time = datetime.strptime(string, "%Y-%m-%d %H:%M:%S%z")
                date_format = "%Y年%m月%d日 %p%-I点%M分"
                formatted_string = (time + timedelta(hours=8)).strftime(date_format)
                return formatted_string

        def get_log_item(item_obj):
            if item_obj["field_type"] == "datetime":
                old = format_date_string(item_obj["old_value"])
                new = format_date_string(item_obj["new_value"])
            else:
                old = item_obj.get("old_value", "False")
                new = item_obj.get("new_value", "False")
            return f"""{item_obj["changed_field"]}: {old} - {new}"""

        query_results = [
            {
                "author_avatar": f"/web/image/res.partner/{page_record['author_id'][0]}/image_128",
                "author_name": page_record["author_id"][1],
                "date": format_date(page_record["date"]),
                "tracking_value": [
                    get_log_item(item)
                    for item in page_record["tracking_value_ids"]],
                "body": page_record["body"],
            }
            for page_record in page_records
        ]
        return_data = {
            "state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
            "total_number": len(message_ids), "result": query_results
        }
        return return_data

    @http.route('/roke/get_activity_log_list', type='json', methods=methods, auth='user', csrf=False, cors='*')
    def get_activity_log_list(self):
        activity_id = http.request.jsonrequest.get('activity_id')
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        request_data = self._get_activity_log_list(activity_id, page_size, page_no)
        return request_data

    @http.route('/roke/create_other_activity', type='json', method=methods, auth='user', cors='*', csrf='*')
    def create_other_activity(self):
        """
        创建不依赖单据的活动
        """
        type_id = http.request.jsonrequest.get('type_id')
        date_deadline = http.request.jsonrequest.get('date_deadline')
        summary = http.request.jsonrequest.get('summary', "")
        user_id = http.request.jsonrequest.get('user_id')
        note = http.request.jsonrequest.get('note', "")
        photo_ids = http.request.jsonrequest.get('file_list', None)  # 图片ID列表
        file_ids = http.request.jsonrequest.get('file_ids', None)  # 文件ID列表
        partner_id = http.request.jsonrequest.get('partner_id', None)
        priority = http.request.jsonrequest.get('priority', None)

        create_user_id = http.request.env.user.id
        if type_id and date_deadline and user_id:
            res_id = http.request.env["roke.activity"].with_context({"stop_create": True}).create({
                "summary": summary, "note": note, "partner_id": partner_id, "priority": priority,
            })
            activity_id = http.request.env(user=create_user_id)['mail.activity'].create({
                "res_model_id": http.request.env['ir.model'].search([("model", "=", "roke.activity")], limit=1).id,
                "res_model": "roke.activity", "res_id": res_id.id,
                'activity_type_id': type_id, 'date_deadline': date_deadline,
                'summary': summary, 'user_id': user_id, 'note': note,
                "partner_id": partner_id, "priority": priority,
                "photo_ids": photo_ids if photo_ids else None,
                "file_ids": file_ids if file_ids else None
            })
            if activity_id:
                result = {'state': 'success', 'msgs': '创建成功'}
            else:
                result = {'state': 'error', 'msgs': '创建失败'}
        else:
            result = {'state': 'error', 'msgs': '缺少必要参数'}
        return result

    @http.route('/roke/activity/get_customer_list', type='json', methods=methods, auth='user', csrf=False, cors='*')
    def activity_get_customer_list(self):
        customer_records = http.request.env["roke.partner"].with_context(lang='zh_CN').search([('customer', '=', True)])
        customer_list = [
            {"id": customer.id, "name": customer.name}
            for customer in customer_records
        ]
        return_data = {
            "state": "success", "msgs": "获取成功", "result": customer_list
        }
        return return_data


class Mobile(app.Mobile):

    @http.route('/get/activity/type', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def get_activity_type(self):
        """
        获取活动类型
        :parameter: {}
        :return:{status: True/False, msg: 提示信息, info: 数据}
        """
        try:
            type_list = []
            type_ids = http.request.env['mail.activity.type'].with_context(lang='zh_CN').search([])
            for activity_type_id in type_ids:
                type_list.append(activity_type_id.sudo().api_generate_activity_type_data())
            result = {'state': 'success', 'msgs': '获取成功', 'info': type_list}
        except Exception as e:
            result = {"state": "error", "msgs": e}
        return result
