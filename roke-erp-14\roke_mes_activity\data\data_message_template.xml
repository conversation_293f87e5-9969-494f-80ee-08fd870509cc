<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <template id="message_activity_done">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                    <span t-if="activity.summary">:</span>
                    <span t-if="activity.summary" t-field="activity.summary"/>
                </p>
                <div t-if="feedback">
                    <div class="o_mail_note_title">
                        <strong>审批意见:</strong>
                    </div>
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>
        <template id="message_activity_cancel">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                    <span t-if="activity.summary">:</span>
                    <span t-if="activity.summary" t-field="activity.summary"/>
                </p>
                <div t-if="feedback">
                    <div class="o_mail_note_title">
                        <strong>审批意见:</strong>
                    </div>
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>
        <template id="message_activity_other">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                    <span t-if="activity.summary">:</span>
                    <span t-if="activity.summary" t-field="activity.summary"/>
                </p>
                <div t-if="feedback">
                    <div class="o_mail_note_title">
                        <strong>审批意见:</strong>
                    </div>
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>

        <template id="message_activity_done_log">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                </p>
                <div t-if="feedback">
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>
        <template id="message_activity_cancel_log">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                </p>
                <div t-if="feedback">
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>
        <template id="message_activity_other_log">
            <div>
                <p>
                    <span t-attf-class="fa #{activity.activity_type_id.icon} fa-fw"/>
                    <span t-field="activity.activity_type_id.name"/>
                    <span t-field="activity.state_id.name"/>
                    <t t-if="display_assignee">(最初分配给：<span t-field="activity.user_id.name"/>)
                    </t>
                </p>
                <div t-if="feedback">
                    <t t-foreach="feedback.split('\n')" t-as="feedback_line">
                        <t t-esc="feedback_line"/>
                        <br t-if="not feedback_line_last"/>
                    </t>
                </div>
                <t t-if="activity.note and activity.note != '&lt;p&gt;&lt;br&gt;&lt;/p&gt;'">
                    <div class="o_mail_note_title">
                        <strong>原始说明:</strong>
                    </div>
                    <div t-field="activity.note"/>
                </t>
            </div>
        </template>
    </data>
</odoo>
