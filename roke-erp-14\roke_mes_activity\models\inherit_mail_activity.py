# -*- coding: utf-8 -*-
from datetime import timedelta, datetime

from odoo import fields, models, modules, api, SUPERUSER_ID
from odoo.exceptions import AccessDenied, ValidationError
from collections import defaultdict
from urllib import parse


class MailActivity(models.Model):
    _name = 'mail.activity'
    _inherit = ['mail.activity', 'mail.thread']

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def _default_state_id(self):
        return self.env.ref("roke_mes_activity.mail_activity_state_pending").id

    state_id = fields.Many2one(
        "roke.mail.activity.state",
        string="活动状态", group_expand='_read_group_stage_ids', track_visibility='always',
        default=_default_state_id
    )

    photo_ids = fields.Many2many("ir.attachment", "activity_photo_attachments_rel", string="图片")
    file_ids = fields.Many2many("ir.attachment", "activity_files_attachments_rel", string="文件")

    top_left = fields.Char("TopLeft", compute="_compute_dynamic_value")
    top_right = fields.Char("TopRight", compute="_compute_dynamic_value")

    priority = fields.Selection([('正常', '正常'), ('紧急', '紧急'), ('非常紧急', '非常紧急')], string="优先级", default='正常')
    partner_id = fields.Many2one("roke.partner", string="客户")

    res_model_id = fields.Many2one(required=False)

    confirmed_datetime = fields.Datetime(string="确认时间", track_visibility='always')
    planned_datetime = fields.Datetime(string="计划完成时间", track_visibility='always')
    finished_datetime = fields.Datetime(string="实际完成时间", track_visibility='always')

    def _onchange_activity_type_id(self):
        """
        重写此方法，变更活动类型，不重新设置任何项
        :return:
        """
        pass
        # if self.activity_type_id:
        #     if self.activity_type_id.summary:
        #         self.summary = self.activity_type_id.summary
        #     self.date_deadline = self._calculate_date_deadline(self.activity_type_id)
        #     self.user_id = self.activity_type_id.default_user_id or self.env.user
        #     if self.activity_type_id.default_description:
        #         self.note = self.activity_type_id.default_description

    @api.depends('res_model', 'res_id')
    def _compute_dynamic_value(self):
        for activity in self:
            activity.top_left = activity.get_dynamic_value("left_top")
            activity.top_right = activity.get_dynamic_value("right_top")

    def action_view_order(self):
        """
        跳转到相应单据
        :return:
        """
        return {
            'name': self.res_model_id.name,
            'type': 'ir.actions.act_window',
            'res_model': self.res_model,
            'res_id': self.res_id,
            'view_mode': 'form',
            'target': 'current',
        }

    def get_dynamic_value(self, label_name):
        """
        获取动态字段（APP右上角显示的数据）
        :param label_name: 活动关联模型名称
        :return:
        """
        if not self.res_model or not self.res_id:
            return ""

        def ellipsis_label(label):
            if len(label) > 15:
                return label[0: 15] + '...'
            else:
                return label

        label_result = self.env["roke.mail.activity.label"].sudo().search([
            ("model_id.model", "=", self.res_model), ("name", "=", label_name)
        ], order="id desc", limit=1)
        if not label_result:
            return ""

        if label_result.field_id:
            record = self.env[self.res_model].sudo().browse(self.res_id)
            exist_value = getattr(record, label_result.field_id.name)
            if not exist_value:
                return ""
            if label_result.field_id.ttype == "char":
                value = record.read([label_result.field_id.name])[0][label_result.field_id.name]
                return ellipsis_label(value)
            elif label_result.field_id.ttype == "many2one":
                value = record.read([label_result.field_id.name])[0][label_result.field_id.name][1]
                return ellipsis_label(value)
            elif label_result.field_id.ttype == "selection":
                value = record.read([label_result.field_id.name])[0][label_result.field_id.name]
                return value
            elif label_result.field_id.ttype == "date":
                value = record.read([label_result.field_id.name])[0][label_result.field_id.name]
                value = (value + timedelta(hours=8)).strftime("%Y/%m/%d")
                return value
            elif label_result.field_id.ttype == "datetime":
                value = record.read([label_result.field_id.name])[0][label_result.field_id.name]
                value = (value + timedelta(hours=8)).strftime("%Y/%m/%d %H:%M:%S")
                return value
            return ""
        else:
            return ""

    def _action_done(self, feedback=False, attachment_ids=None):
        """
        @Override
        覆盖原有方法，不再执行删除记录操作，将删除操作替换为标记称完成状态。
        """
        state_id = self.env.context.get("state_id", None)
        if not state_id:
            self.state_id = self.env.ref("roke_mes_activity.mail_activity_state_done").id
        else:
            self.state_id = state_id

        messages = self.env['mail.message']
        next_activities_values = []

        # Search for all attachments linked to the activities we are about to unlink. This way, we
        # can link them to the message posted and prevent their deletion.
        attachments = self.env['ir.attachment'].search_read([
            ('res_model', '=', self._name),
            ('res_id', 'in', self.ids),
        ], ['id', 'res_id'])

        activity_attachments = defaultdict(list)
        for attachment in attachments:
            activity_id = attachment['res_id']
            activity_attachments[activity_id].append(attachment['id'])

        for activity in self:
            # extract value to generate next activities
            if activity.force_next:
                Activity = self.env['mail.activity'].with_context(
                    activity_previous_deadline=activity.date_deadline)  # context key is required in the onchange to set deadline
                vals = Activity.default_get(Activity.fields_get())

                vals.update({
                    'previous_activity_type_id': activity.activity_type_id.id,
                    'res_id': activity.res_id,
                    'res_model': activity.res_model,
                    'res_model_id': self.env['ir.model']._get(activity.res_model).id,
                })
                virtual_activity = Activity.new(vals)
                virtual_activity._onchange_previous_activity_type_id()
                virtual_activity._onchange_activity_type_id()
                next_activities_values.append(virtual_activity._convert_to_write(virtual_activity._cache))

            # post message on activity, before deleting it
            record = self.env[activity.res_model].browse(activity.res_id)
            record.message_post_with_view(
                'roke_mes_activity.message_activity_done',
                values={
                    'activity': activity,
                    'feedback': feedback,
                    'display_assignee': activity.user_id != self.env.user
                },
                subtype_id=self.env['ir.model.data'].xmlid_to_res_id('mail.mt_activities'),
                mail_activity_type_id=activity.activity_type_id.id,
                attachment_ids=[(4, attachment_id) for attachment_id in attachment_ids] if attachment_ids else [],
            )
            if feedback:
                activity.message_post(body=feedback)

            # Moving the attachments in the message
            # TODO: Fix void res_id on attachment when you create an activity with an image
            # directly, see route /web_editor/attachment/add
            activity_message = record.message_ids[0]
            message_attachments = self.env['ir.attachment'].browse(activity_attachments[activity.id])
            if message_attachments:
                message_attachments.write({
                    'res_id': activity_message.id,
                    'res_model': activity_message._name,
                })
                activity_message.attachment_ids = message_attachments
            messages |= activity_message

        next_activities = self.env['mail.activity'].create(next_activities_values)
        # self.unlink()  # will unlink activity, dont access `self` after that
        return messages, next_activities

    def action_activity_operate(self, operate_type, feedback):
        if operate_type == "cancel":
            template = "roke_mes_activity.message_activity_cancel"
        else:
            template = "roke_mes_activity.message_activity_other"
        for activity in self:
            record = self.env[activity.res_model].browse(activity.res_id)
            record.message_post_with_view(
                template,
                values={
                    'activity': activity,
                    'feedback': feedback,
                    'display_assignee': activity.user_id != self.env.user
                },
                subtype_id=self.env['ir.model.data'].xmlid_to_res_id('mail.mt_activities'),
                mail_activity_type_id=activity.activity_type_id.id,
                attachment_ids=[]
            )
            if feedback:
                activity.message_post(body=feedback)

    @api.model
    def create(self, values):
        """
        @Override
        新建活动，给定初始状态
        """

        if 'res_id' in values.keys() and values["res_id"] == 0:  # 直接创建活动的时候res_id=0, 从单据上创建活动!=0
            res_id = self.env["roke.activity"].with_context({"stop_create": True}).create({
                "summary": values.get("summary", ""),
                "note": values.get("note", ""),
                "partner_id": values.get("partner_id", None),
                "priority": values.get("priority", "正常"),
            })
            values["res_model_id"] = self.env['ir.model'].sudo().search([("model", "=", "roke.activity")], limit=1).id
            values["res_model"] = "roke.activity"
            values["res_id"] = res_id.id

        if 'state_id' not in values.keys():
            values["state_id"] = self.env.ref("roke_mes_activity.mail_activity_state_pending").id
        return super(MailActivity, self).create(values)

    def unlink(self):
        """
        @Override
        重写删除方法，不给予删除，将状态设置为取消，但仍返回True
        """
        for activity in self:
            activity.state_id = self.env.ref("roke_mes_activity.mail_activity_state_cancel").id
            activity.action_activity_operate(operate_type="cancel", feedback="")
            date_ju = isinstance(activity.date_deadline, datetime)
            if activity.date_deadline <= getattr(getattr(fields, 'Date' if not date_ju else 'Datetime'),
                                                 "today" if not date_ju else 'now')():
                self.env['bus.bus'].sendone(
                    (self._cr.dbname, 'res.partner', activity.user_id.partner_id.id),
                    {'type': 'activity_updated', 'activity_deleted': True}
                )
        # result = super(MailActivity, self).unlink()
        return True

    def write(self, vals):
        if "state_id" in vals.keys():
            state = self.env["roke.mail.activity.state"].sudo().search([("id", "=", vals["state_id"])])
            if state.is_done:
                vals["finished_datetime"] = fields.Datetime.now()
            if state.is_confirm:
                vals["confirmed_datetime"] = fields.Datetime.now()
        return super(MailActivity, self).write(vals)

    def activity_format(self):
        """
        @Override
        重写此方法，返回状态不是完成或者取消的记录列表
        :return:
        """
        filtered_domain = [
            self.env.ref("roke_mes_activity.mail_activity_state_cancel").id,
            self.env.ref("roke_mes_activity.mail_activity_state_done").id
        ]
        done_state_ids = self.env["roke.mail.activity.state"].search([("is_done", "=", True)])
        filtered_domain.extend(done_state_ids.ids)
        activities = self.filtered(lambda a: a.state_id.id not in filtered_domain).read()
        mail_template_ids = set(
            [template_id for activity in activities for template_id in activity["mail_template_ids"]])
        mail_template_info = self.env["mail.template"].browse(mail_template_ids).read(['id', 'name'])
        mail_template_dict = dict([(mail_template['id'], mail_template) for mail_template in mail_template_info])
        for activity in activities:
            activity['mail_template_ids'] = [
                mail_template_dict[mail_template_id]
                for mail_template_id in activity['mail_template_ids']
            ]
        return activities

    @api.model
    def _read_group_stage_ids(self, stages, domain, order):
        """
        @Override
        重写expand，获取所有状态（不管状态下收否有记录）
        全部显示，如不需要全部显示（注释掉字段上的 group_expand 方法）
        """
        stages = self.env["roke.mail.activity.state"].sudo().search([])
        search_domain = [('id', 'in', stages.ids)]
        stage_ids = stages._search(search_domain, order=order, access_rights_uid=2)
        return stages.browse(stage_ids)

    def get_res_model_name(self):
        """
        获取单据来源
        :return: 例如：返回 "销售订单"
        """
        if self.res_model_id:
            res_model_name = self.res_model_id.name
        else:
            if not self.res_model:
                res_model_name = ""
            else:
                result = self.env["ir.model"].sudo().search([("model", "=", self.res_model)], limit=1)
                res_model_name = result.name
        return res_model_name or ""

    def get_activity_attachments(self, attachment_ids):
        """
        获取附件列表
        :param attachment_ids:
        :return:
        """
        attachment_list = []
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        for attachment in attachment_ids:
            if attachment.type == "base64":
                attachment_list.append({
                    "name": attachment.name,
                    "type": "base64",
                    "data": attachment.datas
                })
                continue

            if not attachment.access_token:
                attachment.generate_access_token()

            if attachment.mimetype == "application/pdf":
                # pdf 预览
                content_url = parse.quote(
                    "/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
                url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
                attachment_list.append({
                    "name": attachment.name, "type": "url", "data": url
                })
            else:
                # 图片 预览
                url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
                attachment_list.append({
                    "name": attachment.name, "type": "url", "data": url
                })
        return attachment_list

    @api.model
    def action_mail_feedback(self, record_id, state_id, feedback):
        state_res = self.env["roke.mail.activity.state"].search([("id", "=", state_id)])
        activity_res = self.sudo().search([("id", "=", record_id)])
        if feedback and state_id:
            if state_res.is_done and feedback:
                activity_res.action_feedback(feedback=feedback)
            elif state_res.id == self.env.ref("roke_mes_activity.mail_activity_state_cancel").id:
                activity_res.action_activity_operate(operate_type="cancel", feedback=feedback)
            else:
                activity_res.action_activity_operate(operate_type="other", feedback=feedback)

    def show_origin(self):
        if self.res_model == 'roke.activity':
            return False
        return True if self.res_model and self.res_id else False

    def api_generate_activity_data(self):
        """
        组装活动数据
        :return:
        """
        activity_data = {
            "activity_id": self.id, "activity_type_id": self.activity_type_id.name,
            "show_origin_button": self.show_origin(),
            "res_model": self.res_model, "res_id": self.res_id,
            "record_from": self.get_res_model_name(),
            "dynamic_label_a": self.get_dynamic_value("left_top"),
            "dynamic_label_b": self.get_dynamic_value("right_top"),
            "summary": self.summary or "未填写摘要", "note": self.note or "",
            "state": self.state_id.name, "is_done": self.state_id.is_done,
            "create_user": self.create_uid.name, "create_date": self.create_date.strftime('%Y-%m-%d'),
            "user_name": self.user_id.name, "date_deadline": self.date_deadline.strftime('%Y-%m-%d'),
            "photo_ids": self.get_activity_attachments(self.photo_ids),
            "file_ids": self.get_activity_attachments(self.file_ids),
            "partner_name": self.partner_id.name or "",
            "priority": self.priority
        }
        return activity_data


class MailActivityState(models.Model):
    _name = 'roke.mail.activity.state'
    _description = "活动状态"
    _order = 'sequence, id'

    name = fields.Char(string="状态名称")
    sequence = fields.Integer(default=1)
    is_done = fields.Boolean(string="是否为完成状态", default=False)
    is_confirm = fields.Boolean(string="是否为确认状态", default=False)

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def unlink(self):
        """
        @Override
        重写删除方法，预置数据不给予删除
        """
        exclude_state_ids = [
            self.env.ref("roke_mes_activity.mail_activity_state_cancel"),
            self.env.ref("roke_mes_activity.mail_activity_state_done"),
            self.env.ref("roke_mes_activity.mail_activity_state_pending"),
            self.env.ref("roke_mes_activity.mail_activity_state_unstart"),
        ]

        if any(item in exclude_state_ids for item in self):
            raise ValidationError(f"{''.join([f'【{rec.name}】' for rec in exclude_state_ids])}状态禁止删除，但您可以通过修改名称满足您的需求。")

        validation_result = {}
        for record in self:
            search_count = self.env["mail.activity"].sudo().search_count([("state_id", "=", record.id)])
            if search_count:
                validation_result[record.name] = search_count
        if validation_result:
            message = ""
            for key, value in validation_result.items():
                message += f"\t{key} ({value}条记录)\n"
            raise ValidationError(f"以下状态下存在活动记录， 不允许删除：\n{message}")
        result = super(MailActivityState, self).unlink()
        return result

    def write(self, vals):
        if "is_done" in vals.keys() and vals["is_done"]:
            if self.id == self.env.ref("roke_mes_activity.mail_activity_state_cancel").id:
                raise ValidationError(f"【{self.name}】不可标记为完成状态。")
        if "is_done" in vals.keys() and not vals["is_done"]:
            if self.id == self.env.ref("roke_mes_activity.mail_activity_state_done").id:
                raise ValidationError(f"【{self.name}】不可标记为非完成状态。")
        return super(MailActivityState, self).write(vals)


class MailActivityLabel(models.Model):
    _name = "roke.mail.activity.label"
    _description = "APP显示标签"

    name = fields.Selection([("left_top", "左上"), ("right_top", "右上")], string="标记显示位置", default="right_top")
    model_id = fields.Many2one('ir.model', '模型', ondelete='cascade')
    model_inherited_ids = fields.Many2many('ir.model', related='model_id.inherited_model_ids')
    field_id = fields.Many2one(
        'ir.model.fields', string='字段',
        domain="[('ttype', 'in', ['char','date', 'datetime', 'many2one', 'selection']), ('store', '=', True), '|', ('model_id', '=', model_id), ('model_id', 'in', model_inherited_ids)]"
    )

    def create_label_for_model(self, model_name, field_name, label_name):
        model_id = self.env["ir.model"].sudo().search([
            ("model", "=", model_name)
        ], limit=1)
        field_id = self.env["ir.model.fields"].sudo().search([
            ("model_id", "=", model_id.id), ("name", "=", field_name)
        ], limit=1)
        exist = self.env["roke.mail.activity.label"].sudo().search_count([
            ("model_id", "=", model_id.id), ("field_id", "=", field_id.id), ("name", "=", label_name)
        ])
        if not exist:
            self.env["roke.mail.activity.label"].sudo().create({
                "model_id": model_id.id, "field_id": field_id.id,
                "name": label_name
            })

    def initial_data(self):
        self.create_label_for_model("roke.sale.order", "code", "left_top")  # 销售订单左上
        self.create_label_for_model("roke.sale.order", "customer_id", "right_top")  # 销售订单右上


class MailActivityType(models.Model):
    _inherit = "mail.activity.type"

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    def api_generate_activity_type_data(self):
        """
        返回活动类型数据字典
        :return:
        """
        return {
            "type_id": self.id, "type_name": self.name
        }
