# -*- coding: utf-8 -*-


from odoo import fields, models


class RokeActivity(models.Model):
    _name = "roke.activity"
    _description = "其他活动"
    _rec_name = 'summary'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    priority = fields.Selection([('正常', '正常'), ('紧急', '紧急'), ('非常紧急', '非常紧急')], string="优先级", default='正常')
    partner_id = fields.Many2one("roke.partner", string="客户")
    summary = fields.Char(string="摘要")
    note = fields.Html('备注', sanitize_style=True)

