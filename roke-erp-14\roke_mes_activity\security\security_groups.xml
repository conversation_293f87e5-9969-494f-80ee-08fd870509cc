<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 多公司: 活动-->
        <record id="mail_activity_multi_company_rule" model="ir.rule">
            <field name="name">活动多公司记录规则</field>
            <field name="model_id" ref="model_mail_activity"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!-- 多公司: 活动状态-->
        <record id="roke_mail_activity_state_multi_company_rule" model="ir.rule">
            <field name="name">活动状态多公司记录规则</field>
            <field name="model_id" ref="model_roke_mail_activity_state"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!-- 多公司: 活动类型-->
        <record id="mail_activity_type_multi_company_rule" model="ir.rule">
            <field name="name">活动类型多公司记录规则</field>
            <field name="model_id" ref="model_mail_activity_type"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>