odoo.define('roke_mes_activity.ActionMenus', function (require) {
    "use strict";

    const ActionMenus = require('web.ActionMenus');
    const components = {ActionMenus: require("web.ActionMenus")};
    const {patch} = require("web.utils");

    patch(components.ActionMenus, "roke_mes_activity.ActionMenus", {
        async willStart() {
            await this._super(...arguments);
            this.activites_records = await this._setActivityButton(this.props);
        },

        async willUpdateProps(nextProps) {
            await this._super(...arguments);
            this.activites_records = await this._setActivityButton(nextProps);
        },

        async _setActivityButton(props) {
            // console.log("props", props);
            // console.log("this.env", this.env);
            let res_model = props.context.params && props.context.params.model || this.env.action.res_model;
            let res_id = props.context.params && props.context.params.id || props.activeIds ? props.activeIds[0] : false;

            if (!res_model || !res_id){
                return [];
            }

            // console.log("res_model", res_model);
            // console.log("res_id", res_id);

            const result = await this.rpc({
                model: 'mail.activity',
                method: 'search',
                args: [[['res_model', '=', res_model], ['res_id', '=', res_id]]],
            });
            // console.log("result", result);
            return [...result];
        },

        doActionActivites(){
            console.log("doActionActivites");
            if (this.activites_records.length == 1){
                var action = {
                    type: 'ir.actions.act_window',
                    name: "待办",
                    res_model: 'mail.activity',
                    view_mode: 'form',
                    views: [[false, 'form']],
                    target: 'current',
                    context: {
                        form_view_ref: 'roke_mes_activity.mail_activity_view_form',
                    },
                    res_id: this.activites_records[0],
                };
            }else{
                var action = {
                    type: 'ir.actions.act_window',
                    name: "待办",
                    res_model: 'mail.activity',
                    view_mode: 'kanban,list,form',
                    views: [[false, 'kanban'], [false, 'list'], [false, 'form']],
                    target: 'current',
                    domain: [['id', 'in', this.activites_records]],
                    context: {
                        kanban_view_ref: 'roke_mes_activity.mail_activity_view_kanban',
                        tree_view_ref: 'mail.mail_activity_view_tree',
                        form_view_ref: 'roke_mes_activity.mail_activity_view_form',
                    },
                };
            }
            return this.env.bus.trigger('do-action', {
                action,
                options: {
                    on_close: () => {
                        this.trigger('reload', {keepChanges: true});
                    },
                },
            });
        },

    });

    ActionMenus.props = {
        activeIds: { type: Array, element: [Number, String] },
        context: Object,
        domain: { type: Array, optional: 1 },
        isDomainSelected: { type: Boolean, optional: 1 },
        items: {
            type: Object,
            shape: {
                action: { type: Array, optional: 1 },
                print: { type: Array, optional: 1 },
                other: { type: Array, optional: 1 },
                buttonBox: { type: Array, optional: 1 },
            },
        },
    };

});
