odoo.define('roke_mes_activity.feedback_popup', function (require) {
    'use strict';

    const core = require('web.core');
    const viewRegistry = require('web.view_registry');

    const KanbanView = require('web.KanbanView');
    const KanbanController = require('web.KanbanController');
    const Dialog = require('web.Dialog');
    const QWeb = core.qweb;

    const FeedbackKanbanController = KanbanController.extend({
        _onAddRecordToColumn: function (ev) {
            var record = ev.data.record;
            var column = ev.target;
            this.openDialog(column, record, ev)
        },

        openDialog: function (column, record, ev) {
            let self = this
            let dialog = new Dialog(this, {
                title: '反馈',
                size: 'medium',
                buttons: [
                    {
                        text: '确认', classes: 'btn-primary', click: function () {
                            self._rpc({
                                model: 'mail.activity',
                                method: 'action_mail_feedback',
                                args: [record.id, column.data.res_id || false, $('#feedback').val()],
                            }).then(function () {
                                self.alive(self.model.moveRecord(record.db_id, column.db_id, self.handle))
                                    .then(function (column_db_ids) {
                                        return self._resequenceRecords(column.db_id, ev.data.ids)
                                            .then(function () {
                                                _.each(column_db_ids, function (db_id) {
                                                    var data = self.model.get(db_id);
                                                    self.renderer.updateColumn(db_id, data);
                                                });
                                            });
                                    }).guardedCatch(self.reload.bind(self));
                            })
                            dialog.close();
                        }
                    },
                    {
                        text: '取消', click: function () {
                            self.reload();
                            dialog.close();
                        }
                    },
                ],
                $content: QWeb.render('MailActivityFeedback', {})
            });
            dialog.open();
        }
    })

    const FeedbackKanbanView = KanbanView.extend({
        config: _.extend({}, KanbanView.prototype.config, {
            Controller: FeedbackKanbanController,
        }),
    });

    viewRegistry.add('feedback_popup', FeedbackKanbanView);
    return FeedbackKanbanView;
});
