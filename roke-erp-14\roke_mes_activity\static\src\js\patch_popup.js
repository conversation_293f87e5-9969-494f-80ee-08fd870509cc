odoo.define("roke_mes_activity.patch_popup", function (require) {
        "use strict";

        const {patch} = require("web.utils");

        patch(
            require("mail/static/src/components/chatter_topbar/chatter_topbar.js"),
            "roke_mes_activity.patch_popup",
            {
                _onClickScheduleActivity(ev) {
                    const action = {
                        type: 'ir.actions.act_window',
                        name: this.env._t("Schedule Activity"),
                        res_model: 'mail.activity',
                        view_mode: 'form',
                        views: [[false, 'form']],
                        target: 'new',
                        context: {
                            default_res_id: this.chatter.thread.id,
                            default_res_model: this.chatter.thread.model,
                            form_view_ref: 'roke_mes_activity.mail_state_inherit_activity_view_form_popup',

                        },
                        res_id: false,
                    };
                    return this.env.bus.trigger('do-action', {
                        action,
                        options: {
                            on_close: () => {
                                this.trigger('reload', {keepChanges: true});
                            },
                        },
                    });
                }
            }
        );
    }
);