odoo.define('roke_mes_activity.statusbar_popup', function (require) {
    'use strict';

    const FieldStatus = require('web.relational_fields').FieldStatus;
    const core = require('web.core');
    const field_registry = require('web.field_registry');
    const Dialog = require("web.Dialog");
    const qweb = core.qweb;

    const FieldStatusPopup = FieldStatus.extend({
        _onClickStage: function (e) {
            this.openDialog($(e.currentTarget).data("value"), this.res_id)
        },
        openDialog: function (state_id, res_id) {
            let self = this
            let dialog = new Dialog(this, {
                title: '反馈',
                size: 'medium',
                buttons: [
                    {
                        text: '确认', classes: 'btn-primary', click: function () {
                            self._rpc({
                                model: 'mail.activity',
                                method: 'action_mail_feedback',
                                args: [res_id, state_id, $('#feedback').val()],
                            }).then(function () {
                                self._setValue(state_id);
                            })
                            dialog.close();
                        }
                    },
                    {text: '取消', close: true}
                ],
                $content: qweb.render('MailActivityFeedback', {})
            });
            dialog.open();
        }
    });
    field_registry.add('statusbar_popup', FieldStatusPopup)
    return FieldStatusPopup;
});


