<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <!--添加查看活动按钮-->
    <div t-name="roke.ActionMenus" t-inherit="web.ActionMenus" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('o_cp_action_menus')]" position="inside">
            <button t-if="activites_records.length" type="button"
            t-on-click="doActionActivites"
                class="btn btn-secondary o_form_button_activites fa fa-clock-o" style="margin-left: 5px;">
                查看待办
            </button>
        </xpath>
    </div>

</templates>
