<?xml version="1.0"?>
<odoo>
    <record id="roke_mail_activity_state_action" model="ir.actions.act_window">
        <field name="name">活动状态</field>
        <field name="res_model">roke.mail.activity.state</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个活动状态。
          </p>
        </field>
    </record>
    <record id="roke_mail_activity_label_action" model="ir.actions.act_window">
        <field name="name">APP显示标签</field>
        <field name="res_model">roke.mail.activity.label</field>
        <field name="view_mode">tree</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个APP显示标签。
          </p>
        </field>
    </record>

    <menuitem
            id="menu_roke_mail_activity"
            name="待办协同"
            action="mail.mail_activity_action"
            parent="roke_mes_activity.menu_roke_activity_level1"
            sequence="10"
            groups="base.group_system"
    />

    <menuitem id="menu_roke_mail_activity_settings"
              parent="roke_mes_activity.menu_root_activity_management"
              name="设置" sequence="30"/>
    <menuitem
            id="menu_roke_mail_activity_state"
            name="活动状态"
            action="roke_mail_activity_state_action"
            parent="roke_mes_activity.menu_roke_mail_activity_settings"
            sequence="10"/>
    <menuitem
            id="menu_roke_mail_activity_type"
            name="活动类型"
            action="mail.mail_activity_type_action"
            parent="roke_mes_activity.menu_roke_mail_activity_settings"
            sequence="20"/>
    <menuitem
            id="menu_roke_mail_activity_label"
            name="APP显示标签"
            action="roke_mail_activity_label_action"
            parent="roke_mes_activity.menu_roke_mail_activity_settings"
            sequence="30"/>

    <record id="roke_mail_activity_label_view_tree" model="ir.ui.view">
        <field name="name">roke.mail.activity.label.view.tree</field>
        <field name="model">roke.mail.activity.label</field>
        <field name="arch" type="xml">
            <tree string="活动标签" editable="bottom">
                <field name="name"/>
                <field name="model_id"/>
                <field name="model_inherited_ids" invisible="1"/>
                <field name="field_id"/>
            </tree>
        </field>
    </record>

    <!-- 活动状态 -->
    <record id="roke_mail_activity_state_view_search" model="ir.ui.view">
        <field name="name">roke.mail.activity.state.view.search</field>
        <field name="model">roke.mail.activity.state</field>
        <field name="arch" type="xml">
            <search string="Activity">
                <field name="name"/>
                <field name="is_done"/>
                <field name="is_confirm"/>
                <field name="company_id"/>
                <group expand="0" string="Group By">
                    <filter string="完成状态" name="is_done" context="{'group_by': 'is_done'}"/>
                    <filter string="确认状态" name="is_confirm" context="{'group_by': 'is_confirm'}"/>
                </group>
            </search>
        </field>
    </record>
    <record id="roke_mail_activity_state_view_tree" model="ir.ui.view">
        <field name="name">roke.mail.activity.state.view.tree</field>
        <field name="model">roke.mail.activity.state</field>
        <field name="arch" type="xml">
            <tree string="活动状态" create="0" duplicate="0">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="is_done"/>
                <field name="is_confirm"/>
                <field name="company_id"/>
            </tree>
        </field>
    </record>
    <record id="roke_mail_activity_state_view_form" model="ir.ui.view">
        <field name="name">roke.mail.activity.state.view.form</field>
        <field name="model">roke.mail.activity.state</field>
        <field name="arch" type="xml">
            <form string="活动状态" create="0" duplicate="0">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="is_done"/>
                        <field name="is_confirm"/>
                        <field name="company_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!--  活动类型  -->
    <record id="company_inherit_mail_activity_type_view_tree" model="ir.ui.view">
        <field name="name">company.inherit.mail.activity.type.view.tree</field>
        <field name="model">mail.activity.type</field>
        <field name="inherit_id" ref="mail.mail_activity_type_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='icon']" position="after">
                <field name="company_id"/>
            </xpath>
        </field>
    </record>
    <record id="company_inherit_mail_activity_type_view_form" model="ir.ui.view">
        <field name="name">company.inherit.mail.activity.type.view.form</field>
        <field name="model">mail.activity.type</field>
        <field name="inherit_id" ref="mail.mail_activity_type_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='decoration_type']" position="after">
                <field name="company_id"/>
            </xpath>
        </field>
    </record>

    <!-- Mail Activity -->
    <record id="mail_state_inherit_activity_view_search" model="ir.ui.view">
        <field name="name">mail.state.inherit.activity.view.search</field>
        <field name="model">mail.activity</field>
        <field name="inherit_id" ref="mail.mail_activity_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='activity_type_id']" position="after">
                <field name="state_id"/>
                <field name="date_deadline"/>
                <field name="user_id"/>
                <field name="partner_id"/>
            </xpath>
            <xpath expr="//filter[@name='activities_overdue']" position="before">
                <filter string="我发起的" name="activities_launched" domain="[('create_uid', '=', uid)]"/>
                <filter string="分派给我的" name="activities_assigned" domain="[('user_id', '=', uid)]"/>
            </xpath>
            <xpath expr="//filter[@name='activittype']" position="after">
                <filter string="活动状态" name="state_id" context="{'group_by': 'state_id'}"/>
                <filter string="客户" name="state_id" context="{'group_by': 'partner_id'}"/>
            </xpath>
        </field>
    </record>
    <record id="mail_state_inherit_activity_view_form_popup" model="ir.ui.view">
        <field name="name">mail.state.inherit.activity.view.form.popup</field>
        <field name="model">mail.activity</field>
        <field name="inherit_id" ref="mail.mail_activity_view_form_popup"/>
        <field name="arch" type="xml">
            <form position="attributes">
                <attribute name="delete">false</attribute>
                <attribute name="create">true</attribute>
            </form>
            <xpath expr="//sheet" position="after">
                <div class="oe_chatter">
                    <field name="message_ids"/>
                </div>
            </xpath>
            <xpath expr="//sheet" position="inside">
                <notebook>
                    <page string="图片">
                        <!-- <field name="photo_ids" mode="kanban"/> -->
                        <field name="photo_ids" widget="many2many_pic_preview"/>
                    </page>
                    <page string="附件">
                        <!-- <field name="file_ids" mode="kanban"/> -->
                        <field name="file_ids" widget="many2many_pic_preview"/>
                    </page>
                </notebook>
            </xpath>
            <xpath expr="//field[@name='activity_type_id']" position="before">
                <label for="res_model_id" string="业务信息"/>
                <div name="res_model_id">
                    <field name="res_model_id" readonly="1" options="{'no_create_edit':1,'no_create':1,'no_open':1}"/>
                    <br/>
                    <field name="top_left" nolabel="1"/>
                    <br/>
                    <field name="top_right" nolabel="1"/>
                    <!-- 跳转到详情 -->
                    <!--                    <br/>-->
                    <!--                    <button class="btn btn-sm btn-primary"-->
                    <!--                            attrs="{'invisible':['|', '|', ('res_model_id','=',False),('res_model','=',False), ('res_id','=',False)]}"-->
                    <!--                            name="action_view_order" string="查看详情" type="object"/>-->
                </div>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="before">
                <field name="create_uid"/>
            </xpath>
            <xpath expr="//field[@name='user_id']" position="after">
                <field name="priority"/>
                <field name="partner_id" options="{'no_create_edit':1,'no_create':1,'no_open':1}"/>
            </xpath>
            <xpath expr="//field[@name='summary']" position="after">
                <field name="company_id"/>
            </xpath>
            <xpath expr="//field[@name='date_deadline']" position="before">
                <field name="create_date"/>
            </xpath>
            <xpath expr="//field[@name='note']" position="before">
                <group name="activity_develop">
                    <group name="activity_develop_datetime">
                        <!--                        <field name="confirmed_datetime" readonly="1"/>-->
                        <!--                        <field name="planned_datetime"/>-->
                        <!--                        <field name="finished_datetime" readonly="1"/>-->
                    </group>
                </group>
            </xpath>
        </field>
    </record>
    <record id="mail_state_inherit_activity_view_tree" model="ir.ui.view">
        <field name="name">mail.state.inherit.activity.view.tree</field>
        <field name="model">mail.activity</field>
        <field name="inherit_id" ref="mail.mail_activity_view_tree"/>
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="delete">false</attribute>
                <attribute name="create">true</attribute>
            </tree>
            <xpath expr="//field[@name='res_name']" position="before">
                <field name="priority"/>
            </xpath>
            <xpath expr="//field[@name='date_deadline']" position="after">
                <field name="state_id"/>
            </xpath>
        </field>
    </record>
    <record id="mail_activity_view_kanban" model="ir.ui.view">
        <field name="name">mail.activity.view.kanban</field>
        <field name="model">mail.activity</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state_id" class="o_kanban_small_column" create="true" js_class="feedback_popup" limit="40">
                <field name="state_id"/>
                <field name="id"/>
                <field name="res_name"/>
                <field name="summary"/>
                <field name="date_deadline"/>
                <field name="res_model_id"/>
                <field name="priority"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_hr_kanban_record">
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle btn"
                                   data-toggle="dropdown" role="button"
                                   aria-label="Dropdown menu"
                                   title="Dropdown menu"
                                   href="#">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable">
                                        <a role="menuitem" type="edit" class="dropdown-item">编辑</a>
                                    </t>
                                </div>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <span t-if="record.priority.raw_value=='非常紧急'"
                                              style="background: #e8cdcd; color: #A61300; padding: 0 5px;">
                                            <field name="priority"/>
                                        </span>
                                        <span t-if="record.priority.raw_value=='紧急'"
                                              style="background: #f4daae; color: #bb5a16; padding: 0 5px;">
                                            <field name="priority"/>
                                        </span>
                                        <span t-if="record.priority.raw_value=='正常'"
                                              style="background: #c5ebc1; color: #476936; padding: 0 5px;">
                                            <field name="priority"/>
                                        </span>
                                        <div class="o_kanban_record_title"
                                             style="display: flex; justify-content: space-between; margin-top: 15px;">
                                            <div>
                                                <strong>
                                                    <field name="res_model_id"/>
                                                    <t t-if="record.top_left.value">
                                                        /
                                                        <field name="top_left"/>
                                                    </t>
                                                </strong>
                                            </div>
                                            <div>
                                                <field name="top_right"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="oe_kanban_content">
                                    <div class="oe_kanban_body">
                                        <div style="margin: 5px;">
                                            <field name="summary"/>
                                        </div>
                                    </div>
                                </div>
                                <div class=" float-right">
                                    <div>
                                        结束时间
                                        <span style="border-radius: 2px; background: aliceblue; color: cornflowerblue; padding: 0 5px;">
                                            <field name="date_deadline" t-options='{"widget": "datetime"}'/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="mail_activity_view_form" model="ir.ui.view">
        <field name="name">mail.activity.view.form</field>
        <field name="model">mail.activity</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="id" invisible="1"/>
                    <field name="state_id" widget="statusbar_popup" options="{'clickable': '1'}"
                           attrs="{'invisible': [['id', '=', False]]}"/>
                </header>
                <sheet string="待办协同">
                    <div class="oe_button_box" name="button_box"/>
                    <group invisible="1">
                        <field name="activity_category" invisible="1"/>
                        <field name="res_model" invisible="1"/>
                        <field name="res_model_id" invisible="1"/>
                        <field name="res_id" invisible="1" widget="integer"/>
                        <field name="force_next" invisible="1"/>
                        <field name="previous_activity_type_id"/>
                        <field name="has_recommended_activities"/>
                    </group>
                    <group attrs="{'invisible': [('has_recommended_activities','=',False)]}">
                        <div class="o_row">
                            <strong>Recommended Activities</strong>
                            <field name="recommended_activity_type_id" widget="selection_badge"
                                   domain="[('previous_type_ids', '=', previous_activity_type_id)]"
                                   nolabel="1"/>
                        </div>
                    </group>
                    <group>
                        <group>
                            <group>
                                <field name="res_model_id" readonly="1" options="{'no_create_edit':1,'no_create':1,'no_open':1}" string="业务信息"/>
                                <field name="top_left" string=""/>
                                <field name="top_right" string=""/>
                            </group>
                            <group>
                                <field name="summary" string="摘要" placeholder="例如：讨论生产排期"/>
                                <field name="priority"/>
                                <field name="company_id" options="{'no_create': True, 'no_open': True}"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="activity_type_id" required="1" options="{'no_create': True, 'no_open': True}"/>
                                <field name="create_uid"/>
                                <field name="user_id" options="{'no_create': True, 'no_open': True}"/>
                            </group>
                            <group>
                                <field name="partner_id" options="{'no_create_edit':1,'no_create':1,'no_open':1}"/>
                                <field name="create_date"/>
                                <field name="date_deadline"/>
                            </group>
                        </group>
                    </group>

                    <xpath expr="//field[@name='note']" position="before">
                        <group name="activity_develop">
                            <group name="activity_develop_datetime"/>
                        </group>
                    </xpath>
                    <field name="note" placeholder="Log a note..."/>
                    <notebook>
                        <page string="图片">
                            <field name="photo_ids" widget="many2many_pic_preview"/>
                        </page>
                        <page string="附件">
                            <field name="file_ids" widget="many2many_pic_preview"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- mail_activity_action -->
    <record id="mail.mail_activity_action" model="ir.actions.act_window">
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('roke_mes_activity.mail_activity_view_kanban')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('roke_mes_activity.mail_activity_view_form')})]"/>
    </record>
</odoo>
