<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--位置-->
        <record id="stock_location_locations_assemble_disassemble" model="roke.mes.stock.location">
            <field name="name">组装拆卸位置</field>
            <field name="location_type">虚拟位置</field>
        </record>
        <record id="stock_location_locations_other" model="roke.mes.stock.location">
            <field name="name">其他位置</field>
            <field name="location_type">虚拟位置</field>
        </record>
        <!--作业类型-->
        <record id="stock_picking_type_other_out" model="roke.mes.stock.picking.type">
            <field name="name">其他出库</field>
            <field name="index">OT/OUT/</field>
            <field name="type">出库</field>
            <field name="src_location_id" ref="roke_mes_stock.stock_location_locations"/>
            <field name="dest_location_id" ref="stock_location_locations_other"/>
        </record>
        <record id="stock_picking_type_other_in" model="roke.mes.stock.picking.type">
            <field name="name">其他入库</field>
            <field name="index">OT/IN/</field>
            <field name="type">入库</field>
            <field name="src_location_id" ref="stock_location_locations_other"/>
            <field name="dest_location_id" ref="roke_mes_stock.stock_location_locations"/>
        </record>
    </data>
</odoo>
