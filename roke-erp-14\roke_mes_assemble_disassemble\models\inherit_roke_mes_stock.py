#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_roke_mes_stock.py
@Time:
    2022/10/10 15:11
@Site: 
    
@Desc:
    
"""

from odoo import models, fields, api, _


class InheritRokeMesStockPicking(models.Model):
    _inherit = "roke.mes.stock.picking"

    assemble_disassemble_id = fields.Many2one("roke.mes.assemble.disassemble", string="组装拆卸单")

    def make_finish(self):
        obj = super(InheritRokeMesStockPicking, self).make_finish()
        print(list(set(self.assemble_disassemble_id.picking_ids.mapped("state"))))
        states = list(set(self.assemble_disassemble_id.picking_ids.mapped("state")))
        if len(states) == 1:
            if states[0] == "完成":
                self.assemble_disassemble_id.write({
                    "state": "完成"
                })
        return obj
