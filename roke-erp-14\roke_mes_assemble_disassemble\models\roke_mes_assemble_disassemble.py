#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    roke_mes_assemble_disassemble.py
@Time:
    2022/10/10 10:42
@Site: 
    
@Desc:
    
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import datetime


class RokeMesAssembleDisassemble(models.Model):
    _name = "roke.mes.assemble.disassemble"
    _inherit = ['mail.thread']
    _description = "组装拆卸单"
    _rec_name = "code"
    _order = "business_date desc"

    code = fields.Char(string="单号", index=True, tracking=True, copy=False, default="/")
    business_date = fields.Date(string="业务日期", default=fields.Date.context_today, required=True, tracking=True)
    business_type = fields.Selection([("组装", "组装"), ("拆卸", "拆卸")], string="业务类型", required=True, tracking=True, default="组装")
    partner_id = fields.Many2one("roke.partner", string="业务伙伴", ondelete='restrict', tracking=True)
    location_id = fields.Many2one("roke.mes.stock.location", string="仓库", required=True, tracking=True, ondelete='restrict', domain="[('location_type', '=', '内部位置')]")
    product_id = fields.Many2one("roke.product", string="产品", required=True, tracking=True, ondelete='restrict')
    lot_code = fields.Char(string="单件/批次号", tracking=True)
    qty = fields.Float(string="数量", required=True, tracking=True, digits='Stock')
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位", store=True, tracking=True)
    note = fields.Text(string="备注说明", tracking=True)
    state = fields.Selection([("草稿", "草稿"), ("确认", "确认"), ("完成", "完成"), ("取消", "取消")], string="状态", required=True, tracking=True, copy=False, default="草稿")
    line_ids = fields.One2many("roke.mes.assemble.disassemble.line", "assemble_disassemble_id", string="组装拆卸明细", copy=False)
    picking_ids = fields.One2many("roke.mes.stock.picking", "assemble_disassemble_id", string="出入库单")
    picking_count = fields.Integer(string="出入库单", compute="_compute_picking_count")
    e_bom_id = fields.Many2one("roke.mes.e_bom", string="产品BOM")
    move_id = fields.Many2one("roke.mes.stock.move", index=True, string="库存移动")

    def make_picking(self):
        # 生成库存单据
        # 判断是组装还是拆卸
        # 组装时：表头产品其他入库、表体物料其他出库
        # 拆卸时：表头产品其他出库、表体物料其他入库
        picking = self.env["roke.mes.stock.picking"]
        move = self.env["roke.mes.stock.move"]
        # 其他出库作业类型
        picking_type_out = self.env.ref("roke_mes_assemble_disassemble.stock_picking_type_other_out")
        # 其他入库作业类型
        picking_type_in = self.env.ref("roke_mes_assemble_disassemble.stock_picking_type_other_in")
        if self.business_type == "组装":
            # 创建出库单==表体出
            pickingObj = picking.create({
                "picking_type_id": picking_type_out.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_out.src_location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "origin": self.code,
            })
            for line in self.line_ids:
                moveObj = move.create({
                    "picking_id": pickingObj.id,
                    "src_location_id": line.location_id.id,
                    "dest_location_id": picking_type_out.dest_location_id.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "origin": self.code,
                })
                line.move_id = moveObj.id
            # 创建入库单==表头入
            pickingObj = picking.create({
                "picking_type_id": picking_type_in.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": self.location_id.id,
                "origin": self.code,
            })
            moveObj = move.create({
                "picking_id": pickingObj.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": self.location_id.id,
                "product_id": self.product_id.id,
                "qty": self.qty,
                "origin": self.code,
            })
            self.move_id = moveObj.id
        elif self.business_type == "拆卸":
            # 创建出库单==表头出
            pickingObj = picking.create({
                "picking_type_id": picking_type_out.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": self.location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "origin": self.code,
            })
            moveObj = move.create({
                "picking_id": pickingObj.id,
                "src_location_id": self.location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "product_id": self.product_id.id,
                "qty": self.qty,
                "origin": self.code,
            })
            self.move_id = moveObj.id
            # 创建入库单==表体入
            pickingObj = picking.create({
                "picking_type_id": picking_type_in.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": picking_type_in.dest_location_id.id,
                "origin": self.code,
            })
            for line in self.line_ids:
                moveObj = move.create({
                    "picking_id": pickingObj.id,
                    "src_location_id": picking_type_in.src_location_id.id,
                    "dest_location_id": line.location_id.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "origin": self.code,
                })
                line.move_id = moveObj.id

    def make_confirm(self):
        if self.qty <= 0:
            raise UserError("数量必须大于0!")
        for line in self.line_ids:
            if not line.location_id:
                raise UserError("明细行仓库字段存在空的数据,请检查!")
            if line.qty <= 0:
                raise UserError("明细行数量必须大于0!")
        self.make_picking()
        self.write({"state": "确认"})

    def button_finish(self):
        self.make_move_line()
        self.picking_ids.make_finish()

    def make_move_line(self):
        move_line = self.env["roke.mes.stock.move.line"]
        move_line_vals = []
        # 先生成表头对应的move_line
        move_line_vals.append({
            "move_id": self.move_id.id,
            "lot_id": self.make_lot(self.lot_code),
            "src_location_id": self.move_id.src_location_id.id,
            "dest_location_id": self.move_id.dest_location_id.id,
            "qty": self.qty
        })
        # 再生成表体对应的moven_line
        for line in self.line_ids:
            move_line_vals.append({
            "move_id": line.move_id.id,
            "lot_id": line.make_lot(line.lot_code),
            "src_location_id": line.move_id.src_location_id.id,
            "dest_location_id": line.move_id.dest_location_id.id,
            "qty": line.qty
            })
        move_line.create(move_line_vals)

    def make_lot(self, lot_code):
        lot = self.env["roke.mes.stock.lot"]
        if lot_code:
            lotObj = lot.search([("name", "=", lot_code), ("product_id", "=", self.product_id.id)], limit=1)
            if not lotObj:
                lotObj = lot.create({
                    "name": lot_code,
                    "product_id": self.product_id.id
                })
            lot_id = lotObj.id
            return lot_id
        else:
            return False

    def cancel_picking(self):
        for picking in self.picking_ids:
            if picking.state != "草稿":
                raise UserError("库存单据不是草稿状态,禁止此单据重置为草稿状态!")
        self.picking_ids.unlink()

    def make_draft(self):
        # 判断库存单据状态并删除
        self.cancel_picking()
        self.write({"state": "草稿"})

    def make_cancel(self):
        # 撤回库存单据
        self.picking_ids.unlink()
        self.write({"state": "取消"})

    @api.onchange("e_bom_id")
    def _onchange_e_bom_id(self):
        # 算出bom物料比例
        if self.e_bom_id:
            ratio = self.qty / self.e_bom_id.qty
            for line in self.line_ids:
                if line.e_bom_line_id:
                    self.line_ids = [(2, line.id)]
            assembleDisassembleLine = self.env["roke.mes.assemble.disassemble.line"]
            for bom_line in self.e_bom_id.bom_line_ids:
                self.line_ids = [(0, 0, {
                    "product_id": bom_line.product_id.id,
                    "qty": bom_line.qty * (1 if ratio == 0 else ratio),
                    "e_bom_line_id": bom_line.id,
                })]
        else:
            for line in self.line_ids:
                if line.e_bom_line_id:
                    self.line_ids = [(2, line.id)]

    @api.onchange("qty")
    def _onchange_qty(self):
        if self.product_id and self.e_bom_id:
            for line in self.line_ids:
                if line.e_bom_line_id:
                    line.qty = line.e_bom_line_id.qty / self.e_bom_id.qty * self.qty


    @api.onchange("product_id")
    def _onchange_product_id(self):
        self.e_bom_id = self.product_id.bom_ids[:1].id

    def action_view_picking(self):
        result = self.env["ir.actions.actions"]._for_xml_id('roke_mes_stock.action_picking_tree_all')
        result['context'] = {'default_origin': self.code}
        pick_ids = self.mapped('picking_ids')
        if not pick_ids or len(pick_ids) > 1:
            result['domain'] = "[('id','in',%s)]" % (pick_ids.ids)
        elif len(pick_ids) == 1:
            res = self.env.ref('roke_mes_stock.view_roke_mes_stock_picking_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + [(state, view) for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = pick_ids.id
        return result


    def _compute_picking_count(self):
        for record in self:
            record.picking_count = len(record.picking_ids)

    @api.model
    def create(self, vals):
        res = super(RokeMesAssembleDisassemble, self).create(vals)
        if res.qty <= 0:
            raise UserError("数量必须大于0!")
        if res.business_type == "组装":
            res.code = self.env["ir.sequence"].next_by_code('roke.mes.assemble.code')
        elif res.business_type == "拆卸":
            res.code = self.env["ir.sequence"].next_by_code('roke.mes.disassemble.code')
        return res

    def write(self, values):
        res = super(RokeMesAssembleDisassemble, self).write(values)
        if self.qty <= 0:
            raise UserError("数量必须大于0!")
        return res

    def unlink(self):
        for res in self:
            if res.state not in ["草稿", "取消"]:
                raise UserError("仅草稿或取消状态的单据可以删除")
        res = super(RokeMesAssembleDisassemble, self).unlink()
        return res


class RokeMesAssembleDisassembleLine(models.Model):
    _name = "roke.mes.assemble.disassemble.line"
    _inherit = ['mail.thread']
    _description = "组装拆卸明细"
    _order = "id asc"
    assemble_disassemble_id = fields.Many2one("roke.mes.assemble.disassemble", string="组装拆卸单", ondelete='cascade')
    product_id = fields.Many2one("roke.product", string="物料", required=True, tracking=True, ondelete='restrict')
    lot_code = fields.Char(string="单件/批次号", tracking=True)
    location_id = fields.Many2one("roke.mes.stock.location", string="关联仓库", tracking=True, ondelete='restrict', domain="[('location_type', '=', '内部位置')]")
    qty = fields.Float(string="数量", required=True, tracking=True, digits='Stock')
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位", store=True, tracking=True)
    note = fields.Text(string="备注说明", tracking=True)
    e_bom_line_id = fields.Many2one("roke.mes.e_bom.line", string="BOM明细")
    move_id = fields.Many2one("roke.mes.stock.move", index=True, string="库存移动")


    def make_lot(self, lot_code):
        lot = self.env["roke.mes.stock.lot"]
        if lot_code:
            lotObj = lot.search([("name", "=", lot_code), ("product_id", "=", self.product_id.id)], limit=1)
            if not lotObj:
                lotObj = lot.create({
                    "name": lot_code,
                    "product_id": self.product_id.id
                })
            lot_id = lotObj.id
            return lot_id
        else:
            return False


    @api.model
    def create(self, vals):
        res = super(RokeMesAssembleDisassembleLine, self).create(vals)
        if res.qty <= 0:
            raise UserError("明细行数量必须大于0!")
        return res

    def write(self, values):
        res = super(RokeMesAssembleDisassembleLine, self).write(values)
        if self.qty <= 0:
            raise UserError("明细行数量必须大于0!")
        return res


