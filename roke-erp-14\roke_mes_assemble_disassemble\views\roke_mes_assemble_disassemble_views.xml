<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--组装拆卸单-->
    <!--search-->
    <record id="view_roke_mes_assemble_disassemble_search" model="ir.ui.view">
        <field name="name">roke.mes.assemble.disassemble.search</field>
        <field name="model">roke.mes.assemble.disassemble</field>
        <field name="arch" type="xml">
            <search string="组装拆卸单">
                <field name="code"/>
                <separator/>
                <filter name="draft" string="草稿" domain="[('state', '=', '草稿')]"/>
                <filter name="confirm" string="确认" domain="[('state', '=', '确认')]"/>
                <filter name="finish" string="完成" domain="[('state', '=', '完成')]"/>
                <filter name="cancel" string="取消" domain="[('state', '=', '取消')]"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="仓库" name="groupby_location_id" context="{'group_by':'location_id'}"/>
                    <filter string="业务类型" name="groupby_business_type" context="{'group_by':'business_type'}"/>
                    <filter string="业务日期" name="groupby_business_date" context="{'group_by':'business_date'}"/>
                    <filter string="产品" name="groupby_product_id" context="{'group_by':'product_id'}"/>
                    <filter string="状态" name="groupby_state" context="{'group_by':'state'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_assemble_disassemble_tree" model="ir.ui.view">
        <field name="name">roke.mes.assemble.disassemble.tree</field>
        <field name="model">roke.mes.assemble.disassemble</field>
        <field name="arch" type="xml">
            <tree string="组装拆卸单"
                  decoration-info="state=='草稿'"
                  decoration-muted="state=='取消'"
                  decoration-success="state=='完成'">
                <field name="code"/>
                <field name="business_date"/>
                <field name="business_type"/>
                <field name="partner_id"/>
                <field name="location_id"/>
                <field name="product_id"/>
                <field name="lot_code"/>
                <field name="qty"/>
                <field name="uom_id"/>
                <field name="state"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_assemble_disassemble_form" model="ir.ui.view">
        <field name="name">roke.mes.assemble.disassemble.form</field>
        <field name="model">roke.mes.assemble.disassemble</field>
        <field name="arch" type="xml">
            <form string="组装拆卸单">
                <header>
                    <button name="make_confirm" type="object" string="确认" class='oe_highlight'
                            attrs="{'invisible':[('state', '!=', '草稿')]}" confirm="确认并生成库存单据？"/>
                    <button name="button_finish" type="object" string="完成" class='oe_highlight'
                            attrs="{'invisible':[('state', '!=', '确认')]}" confirm="确认并同时将库存单据全部执行吗？"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible':[('state', 'not in', ['确认', '取消'])]}"/>
                    <button name="make_cancel" type="object" string="取消"
                            attrs="{'invisible':[('state', '!=', '草稿')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                <div name="button_box" class="oe_button_box">
                    <button type="object"
                        name="action_view_picking"
                        class="oe_stat_button"
                        icon="fa-truck" attrs="{'invisible':[('picking_ids','=',[])]}">
                        <field name="picking_count" widget="statinfo" string="出入库单" help="查看出入库单"/>
                        <field name="picking_ids" invisible="1"/>
                    </button>
                </div>
                <div class="oe_title">
                    <h1 class="d-flex">
                        <field name="code" readonly="1"/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="business_type" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="business_date" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="partner_id" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="location_id" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                    </group>
                    <group>
                        <field name="product_id" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="lot_code" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="qty" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                        <field name="uom_id" readonly="1"/>
                        <field name="e_bom_id" invisible="1"/>
                    </group>
                </group>
                <group>
                    <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" attrs="{'readonly':[('state', '!=', '草稿')]}"/>
                </group>
                <notebook>
                    <page string="组装拆卸明细" name="line_ids">
                        <field name="line_ids" attrs="{'readonly':[('state', '!=', '草稿')]}">
                            <tree editable="bottom">
                                <field name="product_id"/>
                                <field name="lot_code"/>
                                <field name="location_id" required="1"/>
                                <field name="qty"/>
                                <field name="uom_id"/>
                                <field name="note"/>
                                <field name="e_bom_line_id" invisible="1"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--pivot-->
    <record model="ir.ui.view" id="view_roke_mes_assemble_disassemble_pivot">
        <field name="name">roke.mes.assemble.disassemble.pivot</field>
        <field name="model">roke.mes.assemble.disassemble</field>
        <field name="arch" type="xml">
            <pivot string="组装拆卸单" display_quantity="True" sample="1">
                <field name="business_type" type="row"/>
                <field name="state" type="row"/>
                <field name="location_id" type="row"/>
                <field name="product_id" type="row"/>
                <field name="lot_code" type="row"/>
                <field name="business_date" type="row"/>
                <field name="qty" type="measure"/>
            </pivot>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mes_assemble_disassemble_action" model="ir.actions.act_window">
        <field name="name">组装拆卸单</field>
        <field name="res_model">roke.mes.assemble.disassemble</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_mes_assemble_disassemble_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个组装拆卸单。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个组装拆卸单。
          </p>
        </field>
    </record>

</odoo>
