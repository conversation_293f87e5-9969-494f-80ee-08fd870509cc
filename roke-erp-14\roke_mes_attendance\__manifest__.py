# -*- coding: utf-8 -*-
{
    'name': '融科MES 考勤管理',
    'version': '1.0',
    'category': 'mes',
    'depends': ['roke_mes_base', 'roke_mes_salary'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        
    """,
    # always loaded
    'data': [
        'security/security_groups.xml',
        'security/security_rule.xml',
        'security/ir.model.access.csv',
        'data/sequence_data.xml',
        'data/cron_data.xml',
        'views/roke_salary_mode.xml',
        'views/roke_attendance_device_interaction.xml',
        'views/roke_attendance_device.xml',
        'views/roke_attendance_record_detail.xml',
        'views/roke_attendance_salary_confirm_order_views.xml',
        'views/roke_attendance_record.xml',
        'views/inherit_employee_views.xml',
        'views/inherit_salary_order_views.xml',
        'views/list_btn_js.xml',
        'wizard/create_attendance_sco_wizard_views.xml',
        'report/inherit_roke_salary_report_views.xml',
        'wizard/work_attendance_record_import_wizard_views.xml',
        'views/menus.xml',
    ],
    'qweb': [
        'static/src/xml/create_attendance_sco_btn.xml',
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
}
