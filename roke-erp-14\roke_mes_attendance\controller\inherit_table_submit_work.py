"""
Description:
    表格报工接口入参工作时长处理
    根据工作时长生成考勤记录
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_production.controller.table_submit_work import TableSubmitWork
import datetime
import logging

_logger = logging.getLogger(__name__)


class InheritTableSubmitWork(TableSubmitWork):

    @http.route('/roke/fz_table_detail', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def fz_table_detail(self):
        res = super(InheritTableSubmitWork, self).fz_table_detail()
        res.update({
            "allow_gzsc": True
        })
        return res

    @http.route('/roke/fz_table_save', type='json', method=["POST", "OPTIONS"], auth='user', cors='*', csrf=False)
    def fz_table_save(self):
        res = super(InheritTableSubmitWork, self).fz_table_save()
        values = http.request.jsonrequest
        confirm = values.get('confirm') or False
        gzsc = values.get("gzsc") or 0  # 新入参工作时长：本此报工的所有人员按此时长生成考勤记录
        cell_list = values.get('cell_list') or []
        if confirm and gzsc:
            employee_ids = []
            now_time = datetime.datetime.utcnow()
            today = (now_time + datetime.timedelta(hours=8)).date()
            clock_in_time = now_time - datetime.timedelta(seconds=gzsc * 3600)
            for cell in cell_list:
                cell_details = cell.get("cell_detail") or []
                for cell_detail in cell_details:
                    employee_id = cell_detail.get("employee_id")
                    if employee_id in employee_ids:
                        # 不重复生成
                        continue
                    # 根据工作时长生成考勤记录
                    http.request.env["roke.attendance.record"].create({
                        "attendance_date": today,
                        "employee_id": employee_id,
                        "clock_in_time": clock_in_time,
                        "clock_out_time": now_time,
                        "work_hours": gzsc
                    })
                    employee_ids.append(employee_id)
        return res
