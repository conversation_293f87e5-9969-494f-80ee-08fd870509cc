<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<!--        <record id="ir_cron_check_salary_mode_valid" model="ir.cron">-->
<!--            <field name="name">校验计薪模式</field>-->
<!--            <field name="model_id" ref="model_roke_salary_mode"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">model.check_valid()</field>-->
<!--            <field name="interval_number">1</field>-->
<!--            <field name="interval_type">days</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="doall" eval="True"/>-->
<!--            <field name="active" eval="True" />-->
<!--        </record>-->
    </data>
</odoo>