# -*- coding: utf-8 -*-
"""
Description:
    员工操作
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import nanoid


class InheritRokeEmployee(models.Model):
    _inherit = "roke.employee"

    attendance_device_ids = fields.Many2many("roke.attendance.device", string="可考勤设备")

    def send_device(self):
        """下发用户至考勤机"""
        return
