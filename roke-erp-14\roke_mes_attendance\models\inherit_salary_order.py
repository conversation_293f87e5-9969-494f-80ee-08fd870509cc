# -*- coding: utf-8 -*-
"""
Description:
    员工操作
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import nanoid


class InheritSalaryOrder(models.Model):
    _inherit = "roke.salary.order"

    attendance_salary_ids = fields.One2many("roke.attendance.salary.confirm.order.line", "salary_id", string="考勤记录")

    def unlink(self):
        self.attendance_salary_ids.write({"state": "确认"})
        return super(InheritSalaryOrder, self).unlink()


class InheritSalaryOrderLine(models.Model):
    _inherit = "roke.salary.order.line"

    attendance_salary = fields.Float(string="考勤工资", digits='Salary')
    attendance_salary_ids = fields.Many2many("roke.attendance.salary.confirm.order.line", "roke_attendance_salary_order_rel", string="考勤记录")

    @api.onchange("work_salary", "extra_salary", "attendance_salary")
    def _onchange_salary(self):
        total = self.work_salary + self.extra_salary + self.attendance_salary
        return {"value": {
            "total": total,
            "confirm_total": total
        }}
