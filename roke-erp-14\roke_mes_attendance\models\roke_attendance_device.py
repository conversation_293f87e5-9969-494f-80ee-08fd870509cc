# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeAttendanceDevice(models.Model):
    _name = "roke.attendance.device"
    _description = "设备信息"
    _rec_name = "sn"

    sn = fields.Char(string='序列号', required=True)
    brand = fields.Selection([("ZKTeco", "ZKTeco")], string='设备品牌', required=True, default="ZKTeco")
    upload_type = fields.Selection([
        ("不上传", "不上传"),
        ("每日定时上传", "每日定时上传"),
        ("间隔时间上传", "间隔时间上传"),
        ("实时上传", "实时上传")
    ], string="考勤数据上传方式", required=True, default="每日定时上传")
    ping_time = fields.Integer(string='心跳时间间隔(秒)', default="60", required=True)
    reconn_time = fields.Integer(string='重连时间间隔(秒)', default="30", required=True)
    upload_time_hour = fields.Selection([
        ("0", "0"), ("1", "1"), ("2", "2"), ("3", "3"), ("4", "4"), ("5", "5"),
        ("6", "6"), ("7", "7"), ("8", "8"), ("9", "9"), ("10", "10"), ("11", "11"),
        ("12", "12"), ("13", "13"), ("14", "14"), ("15", "15"), ("16", "16"), ("17", "17"),
        ("18", "18"), ("19", "19"), ("20", "20"), ("21", "21"), ("22", "22"), ("23", "23")
    ], string='上传数据时间', default="0")
    upload_time_minute = fields.Selection([
        ("0", "0"), ("10", "10"), ("20", "20"), ("30", "30"), ("40", "40"), ("50", "50")
    ], string='上传数据时间', default="0")
    interval_time = fields.Integer(string='间隔时间(分)')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


