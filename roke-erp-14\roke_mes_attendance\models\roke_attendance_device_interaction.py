# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeAttendanceDeviceInteraction(models.Model):
    _name = "roke.attendance.device.interaction"
    _description = "设备交互记录"
    _rec_name = "index"

    index = fields.Char(string='记录标识')
    device_id = fields.Many2one('roke.attendance.device', string='设备', required=True)
    state = fields.Selection([("等待", "等待"), ("完成", "完成")], string='状态', default="等待")
    type = fields.Selection([("发送", "发送"), ("接收", "接收")], string='类型')
    content = fields.Text(string='内容')
    result = fields.Text(string='结果')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

