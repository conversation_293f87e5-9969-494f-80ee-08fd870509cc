# -*- coding: utf-8 -*-
from odoo import models, fields, api, _


class RokeAttendanceRecord(models.Model):
    _name = "roke.attendance.record"
    _description = "考勤记录"
    _rec_name = "attendance_date"
    _order = "attendance_date"

    attendance_date = fields.Date(string='考勤日期', default=fields.Date.context_today)
    employee_id = fields.Many2one('roke.employee', string='员工')
    clock_in_time = fields.Datetime(string='上班打卡时间')
    clock_out_time = fields.Datetime(string='下班打卡时间')
    work_hours = fields.Float(string='工作时长（小时）', compute='_compute_work_hours', store=True)
    detail_ids = fields.One2many('roke.attendance.record.detail', 'attendance_id', string='当日打卡记录')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    @api.depends("clock_in_time", "clock_out_time")
    def _compute_work_hours(self):
        for record in self:
            if not record.clock_in_time or not record.clock_out_time:
                record.work_hours = 0
            else:
                record.work_hours = (record.clock_out_time - record.clock_in_time).seconds / 3600
