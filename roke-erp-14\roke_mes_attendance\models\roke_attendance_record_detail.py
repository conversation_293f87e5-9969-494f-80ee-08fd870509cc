# -*- coding: utf-8 -*-
import datetime
from datetime import datetime, timedelta

from odoo import models, fields, api, _


class RokeAttendanceRecordDetail(models.Model):
    _name = "roke.attendance.record.detail"
    _description = "打卡记录"
    _rec_name = "employee_id"

    device_id = fields.Many2one('roke.attendance.device', string='设备')
    employee_id = fields.Many2one('roke.employee', string='员工')
    attendance_id = fields.Many2one('roke.attendance.record', string='考勤记录')
    clock_time = fields.Datetime(string="打卡时间",default=fields.Datetime.now())
    clock_type = fields.Selection([('人脸', '人脸'), ('指纹', '指纹'), ('其他', '其他')], string='打卡方式')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    @api.model
    def create(self, vals):
        employee_id = vals.get("employee_id")
        attendance_time = datetime.strptime(vals.get("clock_time"), "%Y-%m-%d %H:%M:%S")
        attendance_date = (attendance_time + timedelta(hours=8)).date()
        attendanceObj = self.env['roke.attendance.record']
        attendance_record = attendanceObj.search(
            [("attendance_date", "=", attendance_date), ("employee_id", "=", employee_id)]
        )
        if attendance_record:
            attendance_record.write({
                'clock_out_time': attendance_time
            })
        else:
            attendance_record = attendanceObj.create({
                'attendance_date': attendance_date,
                'employee_id': employee_id,
                'clock_in_time': attendance_time
            })
        vals["attendance_id"] = attendance_record.id
        res = super(RokeAttendanceRecordDetail, self).create(vals)
        return res


