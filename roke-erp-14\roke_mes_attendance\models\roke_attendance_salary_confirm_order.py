# -*- coding: utf-8 -*-
"""
Description:
    TODO 考勤计薪确认单
        考勤记录、计薪模式、日薪规则
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class RokeAttendanceSalaryConfirmOrder(models.Model):
    _name = "roke.attendance.salary.confirm.order"
    _inherit = ['mail.thread']
    _description = "考勤工资确认单"
    _order = "id desc"
    _rec_name = "code"

    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.attendance.salary.confirm.order.code'))
    start_date = fields.Date(string="开始日期")
    end_date = fields.Date(string="结束日期")
    state = fields.Selection([("草稿", "草稿"), ("确认", "确认"), ("已统计", "已统计")], string="状态", default="草稿")
    total = fields.Float(string="合计", compute="_compute_total", store=True, digits='Salary')
    confirm_uid = fields.Many2one("res.users", string="审批确认人")
    confirm_time = fields.Datetime(string="审批确认时间")
    note = fields.Text(string="备注")

    line_ids = fields.One2many("roke.attendance.salary.confirm.order.line", "order_id", string="报工明细")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.depends("line_ids", "line_ids.subtotal")
    def _compute_total(self):
        for record in self:
            record.total = sum(self.line_ids.mapped("subtotal"))

    def confirm(self):
        """
        确认考勤工资单
        :return:
        """
        self.write({"state": "确认", "confirm_uid": self.env.user.id, "confirm_time": fields.Datetime.now()})

    def make_draft(self):
        """
        置为草稿
        :return:
        """
        self.write({"state": "草稿", "confirm_uid": False, "confirm_time": False})

    def unlink(self):
        if self.filtered(lambda o: o.state != "草稿"):
            raise ValidationError("非草稿状态的计价确认单禁止删除！如果您确认要删除，请先将单据“置为草稿”。")
        res = super(RokeAttendanceSalaryConfirmOrder, self).unlink()
        return res

    def create_order_entrance(self):
        # if not self.env.user.has_group(''):
        #     raise ValidationError("没有创建考勤确认单的权限，请联系系统管理员。")
        view = self.env.ref('roke_mes_attendance.roke_create_attendance_sco_wizard_view')
        return {
            'name': '创建考勤工资确认单',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'view_id': view.id,
            'views': [(view.id, 'form')],
            'target': 'new',
            'res_model': 'roke.create.attendance.sco.wizard'
        }


class RokeAttendanceSalaryConfirmOrderLine(models.Model):
    _name = "roke.attendance.salary.confirm.order.line"
    _description = "考勤工资明细"

    order_id = fields.Many2one("roke.attendance.salary.confirm.order", string="考勤确认单", required=True, ondelete="cascade")
    attendance_record_id = fields.Many2one("roke.attendance.record", string="考勤记录", required=True, ondelete="restrict")
    employee_id = fields.Many2one('roke.employee', string='员工', required=True, ondelete="cascade")
    attendance_date = fields.Date(string='考勤日期')
    work_hours = fields.Float(string='考勤时长（小时）')
    confirm_work_hours = fields.Float(string="确认时长（小时）")
    salary_mode_id = fields.Many2one("roke.salary.mode", string="计薪模式")
    salary_item_id = fields.Many2one("roke.salary.item", string="工资项")
    salary_type = fields.Selection([("时薪", "时薪"), ("日薪", "日薪")], string='计薪单位', required=True, default="日薪")
    salary = fields.Integer(string='单位工资', required=True)
    state = fields.Selection([("草稿", "草稿"), ("确认", "确认"), ("已统计", "已统计")], related="order_id.state", string="状态", store=True)

    subtotal = fields.Float(string="应发小计", compute="_compute_subtotal", store=True, digits='Salary')
    note = fields.Char(string="备注")
    salary_id = fields.Many2one("roke.salary.order", string="工资单")

    @api.depends("confirm_work_hours", "salary")
    @api.onchange("confirm_work_hours", "salary")
    def _compute_subtotal(self):
        # 应发小计
        for record in self:
            if record.salary_type == "时薪":
                record.subtotal = round(record.confirm_work_hours * record.salary, _get_pd(self.env, "Salary"))
            else:
                if record.confirm_work_hours >= record.salary_mode_id.all_day:
                    record.subtotal = round(record.salary, _get_pd(self.env, "Salary"))
                elif record.confirm_work_hours >= record.salary_mode_id.half_day:
                    record.subtotal = round(record.salary * 0.5, _get_pd(self.env, "Salary"))
                else:
                    record.subtotal = 0

    def write(self, vals):
        res = super(RokeAttendanceSalaryConfirmOrderLine, self).write(vals)
        if vals.get("state", "") == "已统计":  # 明细修改为已统计时，修改单据状态为已统计
            orders = list(set(self.mapped("order_id")))
            for order in orders:
                if not order.line_ids.filtered(lambda l: l.state != "已统计"):
                    order.write({"state": "已统计"})
        elif vals.get("state", "") == "确认":  # 明细修改为确认时，修改单据状态为确认
            self.mapped("order_id").write({"state": "确认"})
        return res

    def unlink(self):
        # 删除时更新报工记录统计数
        res = super(RokeAttendanceSalaryConfirmOrderLine, self).unlink()
        return res


