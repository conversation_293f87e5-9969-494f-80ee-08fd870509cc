# -*- coding: utf-8 -*-
from odoo import models, fields, api, _

class RokeSaleryMode(models.Model):
    _name = "roke.salary.mode"
    _description = "计薪模式"
    _rec_name = "code"

    code = fields.Char(string='编号', required=True, copy=False, readonly=True, store=True, default="保存自动生成编号")
    salary_mode = fields.Selection([("报工", "报工"), ("考勤", "考勤")], string='计薪模式', default='考勤', required=True)
    active_type = fields.Selection([("长期", "长期"), ("临时", "临时")], string='时效', default='临时', required=True)
    valid_date = fields.Date(string="生效日期", default=fields.Date.context_today)
    invalid_date = fields.Date(string="失效日期", default=fields.Date.context_today)
    salary_type = fields.Selection([("时薪", "时薪"), ("日薪", "日薪")], string='计薪单位', required=True, default="日薪")
    half_day = fields.Integer(string="多少小时记半日工资", default=4)
    all_day = fields.Integer(string="多少小时记全天工资", default=8)
    salary = fields.Integer(string='单位工资', required=True)
    salary_item_id = fields.Many2one("roke.salary.item", string="归属工资项")
    employee_ids = fields.Many2many('roke.employee', string='相关人员')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    @api.model
    def create(self, vals):
        vals['code'] = self.env['ir.sequence'].next_by_code('roke.salary.mode.code') or '/'
        return super(RokeSaleryMode, self).create(vals)







