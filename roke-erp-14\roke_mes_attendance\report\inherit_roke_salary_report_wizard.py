# -*- coding: utf-8 -*-
"""
Description:
    工资报表添加考勤工资
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from datetime import datetime, timedelta


class AttendanceInheritRokeSalaryReport(models.Model):
    _inherit = "roke.salary.report"

    attendance_salary = fields.Float(string="考勤工资", digits='Salary')

    def _get_select(self):
        res = super(AttendanceInheritRokeSalaryReport, self)._get_select()
        res += """,ol.attendance_salary as attendance_salary
        """
        return res

