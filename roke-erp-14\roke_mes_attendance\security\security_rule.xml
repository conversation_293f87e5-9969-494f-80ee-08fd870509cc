<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--多公司-->
        <!--考勤设备-->
        <record id="roke_attendance_device_company_rule" model="ir.rule">
            <field name="name">考勤设备多公司记录规则</field>
            <field name="model_id" ref="model_roke_attendance_device"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--设备交互记录-->
        <record id="roke_attendance_di_multi_company_rule" model="ir.rule">
            <field name="name">设备交互记录多公司记录规则</field>
            <field name="model_id" ref="model_roke_attendance_device_interaction"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--考勤记录-->
        <record id="roke_attendance_record_multi_company_rule" model="ir.rule">
            <field name="name">考勤记录多公司记录规则</field>
            <field name="model_id" ref="model_roke_attendance_record"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--打卡记录-->
        <record id="roke_attendance_record_detail_multi_company_rule" model="ir.rule">
            <field name="name">打卡记录多公司记录规则</field>
            <field name="model_id" ref="model_roke_attendance_record_detail"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--考勤工资确认单-->
        <record id="roke_attendance_sco_multi_company_rule" model="ir.rule">
            <field name="name">考勤工资确认单多公司记录规则</field>
            <field name="model_id" ref="model_roke_attendance_salary_confirm_order"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--计薪模式-->
        <record id="roke_salary_mode_multi_company_rule" model="ir.rule">
            <field name="name">计薪模式多公司记录规则</field>
            <field name="model_id" ref="model_roke_salary_mode"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>