odoo.define('roke_create_attendance_sco.button', function (require){
    "use strict";
    var rpc = require("web.rpc");
    var ListController = require('web.ListController');
    var ListView = require('web.ListView');


    var ImportViewMixin = {
        /**
         * @override
         */
        init: function (viewInfo, params) {
            var importEnabled = 'import_enabled' in params ? params.import_enabled : true;
            this.controllerParams.importEnabled = importEnabled;
        },
    };
    var ImportControllerMixin = {
        /**
         * @override
         */
        init: function (parent, model, renderer, params) {
            this.importEnabled = params.importEnabled;
        },
        //--------------------------------------------------------------------------
        // Private
        //--------------------------------------------------------------------------
        /**
         * Adds an event listener on the import button.
         *
         * @private
         */
        _bindImport: function () {
            if (!this.$buttons) {
                return;
            }
            var self = this;
            /*计价确认单*/
            this.$buttons.on('click', '.o_button_create_attendance_sco', function () {
                rpc.query({
                    model: 'roke.attendance.salary.confirm.order',
                    method: 'create_order_entrance',
                    args: [""]
                }).then(function (action_dict) {
                   self.do_action(
                       action_dict
                   )
                });
            });
        }
    };
    ListView.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportViewMixin.init.apply(this, arguments);
        },
    });
    ListController.include({
        init: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin.init.apply(this, arguments);
        },
        renderButtons: function () {
            this._super.apply(this, arguments);
            ImportControllerMixin._bindImport.call(this);
            var self = this;
            /*计价确认单*/
            if (this.modelName === "roke.attendance.salary.confirm.order"){
                self.$buttons.find(".o_button_create_attendance_sco").css("display","inline-block");
            }
        }
    });
}
);