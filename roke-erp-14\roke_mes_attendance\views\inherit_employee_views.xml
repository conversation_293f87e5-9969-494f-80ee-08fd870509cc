<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--人员信息-->
    <!--form-->
    <record id="view_roke_attendance_inherit_employee_form_view" model="ir.ui.view">
        <field name="name">roke.attendance.inherit.employee.form</field>
        <field name="model">roke.employee</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='job_number']" position="after">
                <field name="attendance_device_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
            </xpath>
            <xpath expr="//header" position="inside">
                    <button name="send_device" type="object" string="员工信息下发至考勤机" class="oe_highlight"
                            attrs="{'invisible': [('attendance_device_ids', '=', [])]}"
                            help="将使用员工工号或名称创建系统用户，默认密码000000，请确认手机号和已录入。"/>
            </xpath>
        </field>
    </record>
</odoo>