<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工资单-->
    <!--form-->
    <record id="view_roke_attendance_inherit_salary_order_form_view" model="ir.ui.view">
        <field name="name">roke.attendance.inherit.salary.order.form</field>
        <field name="model">roke.salary.order</field>
        <field name="inherit_id" ref="roke_mes_salary.view_roke_salary_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']/tree//field[@name='total']" position="before">
                <field name="attendance_salary" optional="show" attrs="{'readonly': [('parent.manual_order', '!=', True)]}" sum="考勤工资合计"/>
            </xpath>
        </field>
    </record>
    <!--工资单明细-->
    <!--tree-->
    <record id="view_roke_attendance_inherit_salary_order_line_tree_view" model="ir.ui.view">
        <field name="name">roke.attendance.inherit.salary.order.line.tree</field>
        <field name="model">roke.salary.order.line</field>
        <field name="inherit_id" ref="roke_mes_salary.view_roke_salary_order_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='total']" position="before">
                <field name="attendance_salary" optional="show"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_inherit_salary_order_line_form_view" model="ir.ui.view">
        <field name="name">roke.attendance.inherit.salary.order.line.form</field>
        <field name="model">roke.salary.order.line</field>
        <field name="inherit_id" ref="roke_mes_salary.view_roke_salary_order_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='total']" position="before">
                <field name="attendance_salary" optional="show"/>
            </xpath>
            <xpath expr="//group[@name='extra_salary_ids']" position="after">
                <group string="考勤工资" name="attendance_salary_ids">
                    <field name="attendance_salary_ids" nolabel="1"/>
                </group>
            </xpath>
        </field>
    </record>
</odoo>