<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_attendance_device_search" model="ir.ui.view">
        <field name="name">roke.attendance.device.search</field>
        <field name="model">roke.attendance.device</field>
        <field name="arch" type="xml">
            <search string="考勤设备信息">
                <field name="sn" string="序列号"/>
                <field name="brand" string="设备品牌"/>
                <filter name="upload_type" string="状态"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_attendance_device_tree" model="ir.ui.view">
        <field name="name">roke.attendance.device.tree</field>
        <field name="model">roke.attendance.device</field>
        <field name="arch" type="xml">
            <tree string="考勤设备信息">
                <field name="sn" optional="show"/>
                <field name="brand" optional="show"/>
                <field name="upload_type" optional="show"/>
                <field name="ping_time" optional="show"/>
                <field name="reconn_time" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_device_form" model="ir.ui.view">
        <field name="name">roke.attendance.device.form</field>
        <field name="model">roke.attendance.device</field>
        <field name="arch" type="xml">
            <form string="考勤设备信息">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="sn" invisible="0"/>
                            <field name="brand"/>
                            <field name="ping_time"/>
                            <field name="reconn_time"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="upload_type"/>
                            <field name="interval_time"
                                   attrs="{'invisible': [('upload_type', '!=', '间隔时间上传')],'required': [('upload_type', '=', '间隔时间上传')]}"/>

                            <label for="upload_time_hour" attrs="{'invisible': [('upload_type', '!=', '每日定时上传')]}"/>
                            <div name="upload_time" class="o_row" attrs="{'invisible': [('upload_type', '!=', '每日定时上传')]}">
                                <field name="upload_time_hour" attrs="{'required': [('upload_type', '=', '每日定时上传')]}"/> :
                                <field name="upload_time_minute" attrs="{'required': [('upload_type', '=', '每日定时上传')]}"/>
                            </div>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_roke_attendance_device_action" model="ir.actions.act_window">
        <field name="name">考勤设备信息</field>
        <field name="res_model">roke.attendance.device</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_attendance_device_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个考勤设备。
          </p>
        </field>
    </record>
</odoo>
