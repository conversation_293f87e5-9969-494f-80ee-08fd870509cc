<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_attendance_device_interaction_search" model="ir.ui.view">
        <field name="name">roke.attendance.device.interaction.search</field>
        <field name="model">roke.attendance.device.interaction</field>
        <field name="arch" type="xml">
            <search string="考勤设备交互记录">
                <field name="device_id" string="设备"/>
                <filter name="index" string="记录标识"/>
                <filter name="state_wait" string="等待" domain="[('state', '=', '等待')]"/>
                <filter name="state_finish" string="完成" domain="[('state', '=', '完成')]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_attendance_device_interaction_tree" model="ir.ui.view">
        <field name="name">roke.attendance.device.interaction.tree</field>
        <field name="model">roke.attendance.device.interaction</field>
        <field name="arch" type="xml">
            <tree string="考勤设备交互记录">
                <field name="device_id" optional="show"/>
                <field name="index" optional="show"/>
                <field name="type" optional="show"/>
                <field name="content" optional="show"/>
                <field name="result" optional="show"/>
                <field name="state" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_device_interaction_form" model="ir.ui.view">
        <field name="name">roke.attendance.device.interaction.from</field>
        <field name="model">roke.attendance.device.interaction</field>
        <field name="arch" type="xml">
            <form string="考勤设备交互记录">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="index"/>
                            <field name="device_id"/>
                            <field name="type"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="content"/>
                            <field name="result"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_roke_attendance_device_interaction_action" model="ir.actions.act_window">
        <field name="name">考勤设备交互记录</field>
        <field name="res_model">roke.attendance.device.interaction</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{'create': False, 'edit': False, 'delete': False}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            没有考勤设备交互记录。
          </p>
        </field>
    </record>
</odoo>