<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_attendance_record_search" model="ir.ui.view">
        <field name="name">roke.attendance.record.search</field>
        <field name="model">roke.attendance.record</field>
        <field name="arch" type="xml">
            <search string="考勤记录">
                <field name="attendance_date" string="考勤日期"/>
                <field name="employee_id" string="员工"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_attendance_record_tree" model="ir.ui.view">
        <field name="name">roke.attendance.record.tree</field>
        <field name="model">roke.attendance.record</field>
        <field name="arch" type="xml">
            <tree string="考勤记录">
                <field name="attendance_date" optional="show"/>
                <field name="employee_id" optional="show"/>
                <field name="clock_in_time" optional="show"/>
                <field name="clock_out_time" optional="show"/>
                <field name="work_hours" optional="show"/>
                <field name="detail_ids" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_record_form" model="ir.ui.view">
        <field name="name">roke.attendance.recordform</field>
        <field name="model">roke.attendance.record</field>
        <field name="arch" type="xml">
            <form string="考勤记录">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="attendance_date"/>
                            <field name="employee_id"/>
                            <field name="work_hours"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="clock_in_time"/>
                            <field name="clock_out_time"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="当日打卡记录明细">
                            <field name="detail_ids">
                                <tree>
                                    <field name="device_id" optional="show"/>
                                    <field name="employee_id" optional="show"/>
                                    <field name="clock_time" optional="show"/>
                                    <field name="clock_type" optional="show"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>

                </sheet>
            </form>
        </field>
    </record>
    <record id="view_roke_attendance_record_action" model="ir.actions.act_window">
        <field name="name">考勤记录</field>
        <field name="res_model">roke.attendance.record</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
<!--        <field name="context">{'create': False, 'edit': False, 'delete': False}</field>-->
        <field name="form_view_id" ref="view_roke_attendance_record_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            没有考勤记录。
          </p>
        </field>
    </record>
</odoo>
