<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_attendance_record_detail_search" model="ir.ui.view">
        <field name="name">roke.attendance.record.detail.search</field>
        <field name="model">roke.attendance.record.detail</field>
        <field name="arch" type="xml">
            <search string="考勤记录">
                <field name="employee_id" string="员工"/>
                <filter name="clock_type" string="打卡方式" domain="[('clock_type', '=','人脸')]"/>
                <filter name="clock_type" string="打卡方式" domain="[('clock_type', '=','指纹')]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_attendance_record_detail_tree" model="ir.ui.view">
        <field name="name">roke.attendance.record.detail.tree</field>
        <field name="model">roke.attendance.record.detail</field>
        <field name="arch" type="xml">
            <tree string="打卡记录">
                <field name="employee_id" optional="show"/>
                <field name="clock_time" optional="show"/>
                <field name="clock_type" optional="show"/>
                <field name="device_id" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_record_detail_form" model="ir.ui.view">
        <field name="name">roke.attendance.record.detail.form</field>
        <field name="model">roke.attendance.record.detail</field>
        <field name="arch" type="xml">
            <form string="打卡记录">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="employee_id" invisible="0"/>
                            <field name="device_id"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="clock_time"/>
                            <field name="clock_type"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_roke_attendance_record_detail_action" model="ir.actions.act_window">
        <field name="name">打卡记录</field>
        <field name="res_model">roke.attendance.record.detail</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
<!--        <field name="context">{'create': False, 'edit': False, 'delete': False}</field>-->
        <field name="form_view_id" ref="view_roke_attendance_record_detail_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            没有考勤记录。
          </p>
        </field>
    </record>
</odoo>
