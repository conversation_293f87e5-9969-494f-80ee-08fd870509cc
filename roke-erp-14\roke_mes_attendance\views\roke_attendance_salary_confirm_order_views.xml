<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--考勤确认单-->
    <!--search-->
    <record id="view_roke_attendance_salary_confirm_order_search" model="ir.ui.view">
        <field name="name">roke.attendance.salary.confirm.order.search</field>
        <field name="model">roke.attendance.salary.confirm.order</field>
        <field name="arch" type="xml">
            <search string="考勤确认单">
                <field name="code"/>
                <filter string="草稿" name="草稿" domain="[('state', '=', '草稿')]"/>
                <filter string="确认" name="确认" domain="[('state', '=', '确认')]"/>
                <filter string="已统计" name="已统计" domain="[('state', '=', '已统计')]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_attendance_salary_confirm_order_tree" model="ir.ui.view">
        <field name="name">roke.attendance.salary.confirm.order.tree</field>
        <field name="model">roke.attendance.salary.confirm.order</field>
        <field name="arch" type="xml">
            <tree string="考勤确认单" decoration-info="state=='草稿'">
                <field name="code"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="total" sum="合计"/>
                <field name="note" optional="show"/>
                <field name="state"/>
                <field name="confirm_uid" optional="show"/>
                <field name="confirm_time" optional="hide"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_salary_confirm_order_form" model="ir.ui.view">
        <field name="name">roke.attendance.salary.confirm.order.form</field>
        <field name="model">roke.attendance.salary.confirm.order</field>
        <field name="arch" type="xml">
            <form string="考勤确认单">
                <header>
                    <button name="confirm" type="object" string="确认" class="oe_highlight"
                            attrs="{'invisible': [('state', '!=', '草稿')]}"/>
                    <button name="make_draft" type="object" string="置为草稿"
                            attrs="{'invisible': [('state', '!=', '确认')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                    <group id="g1" col="3">
                        <group>
                            <field name="start_date" readonly="1"/>
                            <field name="end_date" readonly="1"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="code" readonly="1"/>
                            <field name="total"/>
                        </group>
                        <group>
                            <field name="create_date" string="创建时间"/>
                            <field name="create_uid" string="创建人"/>
                            <field name="confirm_uid" readonly="1"/>
                            <field name="confirm_time" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="考勤明细">
                            <field name="line_ids" attrs="{'readonly':[('state', '!=', '草稿')]}">
                                <tree editable="bottom" create="0">
                                    <field name="order_id" invisible="1"/>
                                    <field name="attendance_record_id" optional="show" readonly="1"/>
                                    <field name="employee_id" readonly="1" optional="show"/>
                                    <field name="work_hours" optional="show" readonly="1"/>
                                    <field name="confirm_work_hours" optional="show"/>
                                    <field name="salary_mode_id" optional="hide"/>
                                    <field name="salary_item_id" optional="show"/>
                                    <field name="salary_type" optional="show"/>
                                    <field name="salary" optional="show"/>
                                    <field name="subtotal" sum="合计" optional="show"/>
                                    <field name="attendance_date" optional="show"/>
                                    <field name="note" optional="show"/>
                                    <field name="create_uid" string="创建人" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_attendance_salary_confirm_order_action" model="ir.actions.act_window">
        <field name="name">考勤确认单</field>
        <field name="res_model">roke.attendance.salary.confirm.order</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{'create': False}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个考勤确认单。
          </p>
        </field>
    </record>
    <!--报工工资明细-->
    <!--tree-->
    <record id="view_roke_attendance_salary_confirm_order_line_tree" model="ir.ui.view">
        <field name="name">roke.attendance.salary.confirm.order.line.tree</field>
        <field name="model">roke.attendance.salary.confirm.order.line</field>
        <field name="arch" type="xml">
            <tree string="报工工资明细">
                <field name="order_id" optional="show"/>
                <field name="attendance_record_id" optional="show" readonly="1"/>
                <field name="employee_id"/>
                <field name="work_hours" optional="show" readonly="1"/>
                <field name="confirm_work_hours" optional="show"/>
                <field name="salary_mode_id" optional="hide"/>
                <field name="salary_item_id" optional="show"/>
                <field name="salary_type" optional="show"/>
                <field name="salary" optional="show"/>
                <field name="subtotal" sum="合计" optional="show"/>
                <field name="attendance_date" optional="show"/>
                <field name="note" optional="show"/>
                <field name="create_uid" string="创建人" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_attendance_salary_confirm_order_line_form" model="ir.ui.view">
        <field name="name">roke.attendance.salary.confirm.order.line.form</field>
        <field name="model">roke.attendance.salary.confirm.order.line</field>
        <field name="arch" type="xml">
            <form string="报工工资明细">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="attendance_record_id"/>
                            <field name="employee_id"/>
                            <field name="work_hours"/>
                            <field name="confirm_work_hours"/>
                            <field name="attendance_date"/>
                        </group>
                        <group>
                            <field name="salary_mode_id"/>
                            <field name="salary_item_id"/>
                            <field name="salary_type"/>
                            <field name="salary"/>
                            <field name="subtotal" sum="合计"/>
                        </group>
                    </group>
                    <notebook/>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                </sheet>
            </form>
        </field>
    </record>


</odoo>
