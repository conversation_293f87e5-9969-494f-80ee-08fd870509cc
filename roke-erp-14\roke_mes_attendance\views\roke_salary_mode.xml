<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="view_roke_salary_mode_search" model="ir.ui.view">
        <field name="name">roke.salary.mode.search</field>
        <field name="model">roke.salary.mode</field>
        <field name="arch" type="xml">
            <search string="计薪模式">
                <field name="code" string="编号"/>
                <filter name="salary_mode_bg" string="按报工计薪" domain="[('salary_mode', '=', '报工')]"/>
                <filter name="salary_mode_kq" string="按考勤计薪" domain="[('salary_mode', '=', '考勤')]"/>
                <separator/>
                <filter name="active_type" string="长期有效" domain="[('active_type', '=', '长期')]"/>
                <filter name="active_type" string="临时有效" domain="[('active_type', '=', '临时')]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_salary_mode_tree" model="ir.ui.view">
        <field name="name">roke.salary.mode.tree</field>
        <field name="model">roke.salary.mode</field>
        <field name="arch" type="xml">
            <tree string="计薪模式">
                <field name="code" optional="show"/>
                <field name="salary_mode" optional="show"/>
                <field name="active_type" optional="show"/>
                <field name="valid_date" optional="show"/>
                <field name="invalid_date" optional="show"/>
                <field name="salary_type" optional="show"/>
                <field name="salary" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_salary_mode_form" model="ir.ui.view">
        <field name="name">roke.salary.mode.form</field>
        <field name="model">roke.salary.mode</field>
        <field name="arch" type="xml">
            <form string="计薪模式">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="code" readonly="1"/>
                            <field name="salary_mode"/>
                            <field name="active_type"/>
                            <field name="salary_item_id" attrs="{'invisible': [('salary_mode', '!=', '考勤')],'required': [('salary_mode', '=', '考勤')]}"/>
                            <field name="valid_date" attrs="{'invisible': [('active_type', '!=', '临时')],'required': [('active_type', '=', '临时')]}"/>
                            <field name="invalid_date" attrs="{'invisible': [('active_type', '!=', '临时')],'required': [('active_type', '=', '临时')]}"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                            <field name="employee_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
                            <field name="salary_type" attrs="{'invisible': [('salary_mode', '=', '报工')]}"/>
                            <field name="salary" attrs="{'invisible': [('salary_mode', '=', '报工')]}"/>
                            <field name="half_day"
                                   attrs="{
                                   'invisible': ['|',('salary_mode', '=', '报工'), ('salary_type', '=', '时薪')],
                                   'required': [('salary_mode', '=', '考勤'), ('salary_type', '=', '日薪')]
                                   }"/>
                            <field name="all_day"
                                   attrs="{
                                   'invisible': ['|',('salary_mode', '=', '报工'), ('salary_type', '=', '时薪')],
                                   'required': [('salary_mode', '=', '考勤'), ('salary_type', '=', '日薪')]
                                   }"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_roke_salary_mode_action" model="ir.actions.act_window">
        <field name="name">计薪模式</field>
        <field name="res_model">roke.salary.mode</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_salary_mode_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个考勤计薪模式。
          </p>
        </field>
    </record>
</odoo>
