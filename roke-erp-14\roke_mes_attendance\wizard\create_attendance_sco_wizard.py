# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class CreateAttendanceSalaryConfirmWizard(models.TransientModel):
    _name = "roke.create.attendance.sco.wizard"

    select_range = fields.Selection(
        [("当日", "当日"), ("本周", "本周"), ("本月", "本月"), ("昨日", "昨日"), ("上周", "上周"), ("上月", "上月")],
        string="报工日期", default="本月"
    )
    start_date = fields.Date(string="开始日期")
    end_date = fields.Date(string="结束日期")

    def _get_line_dict(self, attendance_record):
        """
        获取工资明细字典
        """
        salary_modes = self.env["roke.salary.mode"].search([
            ("employee_ids", "in", attendance_record.employee_id.id),
            ("salary_mode", "=", "考勤"),
        ]).filtered(lambda sm: sm.active_type == "长期" or sm.valid_date <= attendance_record.attendance_date <= sm.invalid_date)
        if salary_modes:
            salary_mode = salary_modes[0]
            return {
                "attendance_record_id": attendance_record.id,
                "employee_id": attendance_record.employee_id.id,
                "attendance_date": attendance_record.attendance_date,
                "work_hours": attendance_record.work_hours,
                "confirm_work_hours": attendance_record.work_hours,
                "salary_mode_id": salary_mode.id,
                "salary_item_id": salary_mode.salary_item_id.id,
                "salary_type": salary_mode.salary_type,
                "salary": salary_mode.salary
            }
        else:
            return False

    def _get_line_values(self, attendance_record):
        """
        获取确认单明细内容
        :param work_record:
        :param confirm_qty:
        :return:
        """
        line_value = self._get_line_dict(attendance_record)
        if line_value:
            return (0, 0, self._get_line_dict(attendance_record))
        else:
            return False

    def _get_attendance_records(self):
        """
        获取报工记录
        :return:
        """
        attendance_records = self.env["roke.attendance.record"].search([
            ("attendance_date", "<", self.end_date),
            ("attendance_date", ">=", self.start_date)
        ])
        return attendance_records

    def confirm(self):
        attendance_records = self._get_attendance_records()
        lines = []
        for attendance_record in attendance_records:
            # 根据计薪规则计算计薪数量
            line_value = self._get_line_values(attendance_record)
            if line_value:
                lines.append(line_value)
        code = self.env['ir.sequence'].next_by_code('roke.attendance.salary.confirm.order.code')
        create_dict = {
            "code": code,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "line_ids": lines
        }
        res = self.env["roke.attendance.salary.confirm.order"].create(create_dict)
        return {
            'name': code,
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_id': res.id,
            'target': 'self',
            'context': {'create': False},
            # 'context': {'create': False, 'form_view_initial_mode': 'edit'},
            'res_model': 'roke.attendance.salary.confirm.order'
        }

    @api.onchange("select_range")
    def _onchange_select_range(self):
        if self.select_range:
            start_date, end_date = False, False
            today = fields.Date.context_today(self)
            if self.select_range == "当日":
                start_date, end_date = today, today
            elif self.select_range == "本周":
                start_date, end_date = today-timedelta(days=today.weekday()), today-timedelta(days=today.weekday())+timedelta(days=6)
            elif self.select_range == "本月":
                start_date = today.replace(day=1)
                if today.month != 12:
                    end_date = today.replace(day=1, month=today.month + 1)-timedelta(days=1)
                else:
                    end_date = today.replace(year=today.year + 1, day=1, month=1)
            elif self.select_range == "昨日":
                start_date, end_date = today-timedelta(days=1), today-timedelta(days=1)
            elif self.select_range == "上周":
                start_date, end_date = today-timedelta(days=today.weekday()+7), today-timedelta(days=today.weekday()+1)
            elif self.select_range == "上月":
                end_date = today.replace(day=1) - timedelta(days=1)
                if today.month != 1:
                    start_date = today.replace(day=1, month=today.month - 1)
                else:
                    start_date = today.replace(year=today.year - 1, day=1, month=12)
            return {"value": {"start_date": start_date, "end_date": end_date if end_date <= today else today}}
        else:
            return {}


