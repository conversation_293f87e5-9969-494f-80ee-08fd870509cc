<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="roke_create_attendance_sco_wizard_view" model="ir.ui.view">
        <field name="name">roke.create.attendance.sco.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.create.attendance.sco.wizard</field>
        <field name="priority" eval="20"/>
        <field name="arch" type="xml">
            <form>
                <div name="message" class="alert alert-info" role="alert" style="margin-bottom:0px;">
                    根据起止日期范围内的考勤记录生成考勤确认单。您可以在生成后对考勤明细进行微调。
                </div>
                <group>
                    <group>
                        <field name="select_range"/>
                        <label for="start_date" string="日期起止"/>
                        <div name="date_range" class="o_row">
                            <field name="start_date" required="1"/>-
                            <field name="end_date" required="1"/>
                        </div>
                    </group>
                </group>
                <footer>
                    <button name='confirm' string='确认创建' type='object' class='oe_highlight'/>
                    <button string="关闭" class="oe_link" special="cancel" />
                </footer>
            </form>
        </field>
    </record>

</odoo>