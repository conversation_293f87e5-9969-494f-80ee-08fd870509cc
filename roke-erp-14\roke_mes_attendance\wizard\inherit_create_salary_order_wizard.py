# -*- coding: utf-8 -*-
"""
Description:
    工资单添加考勤工资内容
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from datetime import datetime, timedelta


class InheritCreateSalaryOrderWizard(models.TransientModel):
    _inherit = "roke.create.salary.order.wizard"

    def _get_attendance_dict(self, attendance_lines):
        """获取考勤明细"""
        attendance_dict = {}
        for attendance_line in attendance_lines:
            employee = attendance_line.employee_id
            if employee in attendance_dict:
                attendance = attendance_dict.get(employee)
                attendance.append([attendance_line, attendance_line.subtotal])
                attendance_dict[employee] = attendance
            else:
                attendance_dict[employee] = [[attendance_line, attendance_line.subtotal]]
        return attendance_dict

    def _create_salary_order(self):
        """创建工资单"""
        res = super(InheritCreateSalaryOrderWizard, self)._create_salary_order()
        # 创建考勤工资
        attendance_lines = self.env["roke.attendance.salary.confirm.order.line"].search([
            ("attendance_date", ">=", self.start_date),
            ("attendance_date", "<", self.end_date),
            ("salary_id", "=", False),
            ("state", "=", "确认")
        ])
        if self.position_ids:
            attendance_lines = attendance_lines.filtered(lambda al: al.employee_id.position_id in self.position_ids)
        attendance_dict = self._get_attendance_dict(attendance_lines)
        AttendanceLineObj = self.env["roke.attendance.salary.confirm.order.line"]
        for line in res.line_ids:
            employee = line.employee_id
            e_attendance_list = attendance_dict.get(employee, [])
            attendance_line = AttendanceLineObj
            attendance_subtotal = 0
            for e_attendance in e_attendance_list:
                attendance_line += e_attendance[0]
                attendance_subtotal += e_attendance[1]
            if attendance_line:
                line.write({
                    "total": line.total + attendance_subtotal,
                    "confirm_total": line.confirm_total + attendance_subtotal,
                    "attendance_salary": attendance_subtotal,
                    "attendance_salary_ids": [(6, 0, attendance_line.ids)]
                })
                if attendance_dict.__contains__(employee):
                    attendance_dict.pop(employee)  # 删除已统计的考勤项
        lines = []
        for employee in attendance_dict:
            attendance_line = AttendanceLineObj
            attendance_subtotal = 0
            e_attendance_list = attendance_dict.get(employee)
            for e_attendance in e_attendance_list:
                attendance_line += e_attendance[0]
                attendance_subtotal += e_attendance[1]
            lines.append({
                "order_id": res.id,
                "employee_id": employee.id,
                "work_salary": 0,
                "extra_salary": 0,
                "attendance_salary": attendance_subtotal,
                "total": attendance_subtotal,
                "confirm_total": attendance_subtotal,
                "work_record_ids": [(6, 0, [])],
                "extra_salary_ids": [(6, 0, [])],
                "confirm_order_line_ids": [(6, 0, [])],
                "attendance_salary_ids": [(6, 0, attendance_line.ids)]
            })
        self.env["roke.salary.order.line"].create(lines)
        attendance_lines.write({"salary_id": res.id, "state": "已统计"})
        return res
