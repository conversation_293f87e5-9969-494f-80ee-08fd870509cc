# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from datetime import datetime, timedelta


class InheritCreateSalaryConfirmWizard(models.TransientModel):
    _inherit = "roke.create.salary.confirm.order.wizard"

    def _get_work_records(self):
        """
        获取报工记录
        :return:
        """
        work_records = super(InheritCreateSalaryConfirmWizard, self)._get_work_records()
        # 筛选不使用报工的人员
        SalaryMode = self.env["roke.salary.mode"]
        extra_wrs = self.env["roke.work.record"]
        for wr in work_records:
            work_date = (wr.work_time + timedelta(hours=8)).date()
            if len(wr.employee_ids) == 1:
                salary_modes = SalaryMode.search([
                    ("employee_ids", "in", wr.employee_ids[0].id),
                    ("salary_mode", "=", "考勤")
                ]).filtered(lambda sm: sm.active_type == "长期" or sm.valid_date <= work_date <= sm.invalid_date)
                if salary_modes:
                    extra_wrs += wr
        work_records -= extra_wrs
        return work_records
