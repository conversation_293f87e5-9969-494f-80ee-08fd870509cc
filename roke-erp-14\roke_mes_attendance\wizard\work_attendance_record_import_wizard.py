# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError
import xlwt
import xlrd
from io import BytesIO
import base64
import logging
import datetime

_logger = logging.getLogger(__name__)
_s_date = datetime.date(1899, 12, 31).toordinal() - 1


class ImportWorkAttendanceRecord(models.TransientModel):
    _name = "outside.attendance.record.import.wizard"
    _description = "批量导入外部考勤记录"

    file = fields.Binary('文件')

    def generate_excel(self):
        """
            导出盘点明细
        """
        result = [
            ['考勤日期', '员工名称或编号', '上班时间', '下班时间',],
            ['例：2022-04-28', '例：员工A,员工B,员工C或001,002,003', '例：2022-04-28 08:20:10', '例：2022-04-28 18:20:10',
            '本行删掉。其中产品、工序、人员、班组、工作中心的名称和编号填一个就可以，也可以即填编号也填名称'],
        ]
        wbk = xlwt.Workbook()
        sheet = wbk.add_sheet('Sheet1', cell_overwrite_ok=True)
        for i in range(len(result)):
            for j in range(len(result[i])):
                sheet.write(i, j, result[i][j])
        buffer = BytesIO()
        wbk.save(buffer)
        data = base64.encodebytes(buffer.getvalue())
        return data

    def action_export_data(self):
        # 调用自定义excel模板
        res = self.env["export.excel.template"].create({'file': self.generate_excel()})
        excel_url = '/web/content?model=%s&id=%s&field=file&download=true&filename=%s.xls' % (
        "export.excel.template", res.id, "外部考勤数据导入模板")

        value = dict(
            type='ir.actions.act_url',
            target='self',
            url=excel_url
        )
        return value

    def _get_date(self, cheange_date):
        if isinstance(cheange_date, float):
            cheange_date = int(cheange_date)
            d = datetime.date.fromordinal(_s_date + cheange_date)
            return d.strftime("%Y-%m-%d")
        return cheange_date

    def check_and_save(self, row, rx):
        """
        校验并保存
        :param row: excel行
        :return: 任务对象id
        """
        # 考勤日期
        attendance_date_str = row[0].value or ""
        attendance_date = self._get_date(attendance_date_str) or False
        record = self.env["roke.attendance.record"]
        # if attendance_date:
        #     record = record.search([("attendance_date", "=", attendance_date)])
        if not attendance_date:
            raise ValidationError("未找到考勤日期，请先创建考勤日期。第%s行" % str(rx + 1))
        # 人员
        clock_employee_str = row[1].value or ""
        EmployeeObj = self.env["roke.employee"]
        if clock_employee_str:
            EmployeeObj = EmployeeObj.search(["|", ("name", "=", clock_employee_str), ("code", "=", str(clock_employee_str))])

        if not EmployeeObj:
            raise ValidationError("未找到考勤人员，请先确认是否有此员工。第%s行" % str(rx + 1))
        # 上班时间
        clock_in_time_str = row[2].value or ""
        if type(clock_in_time_str) == str:
            clock_in_time = datetime.datetime.strptime(clock_in_time_str, "%Y-%m-%d %H:%M:%S")
        else:
            try:
                clock_in_time = xlrd.xldate.xldate_as_datetime(clock_in_time_str,0)
            except:
                raise ValidationError("未找到上班打卡时间，请先确认是否上班打卡。第%s行" % str(rx + 1))
        # 下班时间
        clock_out_time_str = row[3].value or ""
        if type(clock_out_time_str) == str:
            clock_out_time = datetime.datetime.strptime(clock_out_time_str, "%Y-%m-%d %H:%M:%S")
        else:
            try:
                clock_out_time = xlrd.xldate.xldate_as_datetime(clock_out_time_str, 0)
            except:
                raise ValidationError("未找到下班打卡时间，请先确认是否下班打卡。第%s行" % str(rx + 1))

        # 考勤
        create_dict = {
            "attendance_date": attendance_date,
            "employee_id": EmployeeObj.id,
            "clock_in_time": clock_in_time-datetime.timedelta(hours=8),
            "clock_out_time": clock_out_time-datetime.timedelta(hours=8),
        }
        record = self.env['roke.attendance.record'].create(create_dict)  # 确认执行导入
        return record

    def action_import(self):
        """
        导入excel文件,按行读取数据并处理
        :return: 导入结果视图
        """
        if not self.file:
            return
        book = xlrd.open_workbook(file_contents=base64.decodebytes(self.file))
        sh = book.sheet_by_index(0)
        wo_ids = []
        for rx in range(1, sh.nrows):
            row = sh.row(rx)
            wo_id = self.check_and_save(row, rx)
            wo_ids.append(wo_id)

