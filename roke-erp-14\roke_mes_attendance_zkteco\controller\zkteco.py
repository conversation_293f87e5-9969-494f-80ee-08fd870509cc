# -*- coding: utf-8 -*-
"""
Description:
    考勤机对接
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
import json
import logging
import uuid
from datetime import datetime, timedelta
import pytz

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class ZKTeco(http.Controller):

    def to_utc_zero_datetime(self, time_string):
        # time_format = "%Y-%m-%d %H:%M:%S"
        # local_time = datetime.strptime(time_string, time_format)
        # local_timezone = pytz.timezone('Asia/Shanghai')
        # local_time = local_timezone.localize(local_time)
        # utc_time = local_time.astimezone(pytz.utc)
        # utc_time_string = utc_time.strftime(time_format)
        # return utc_time_string
        # time_string = "2023-08-26 12:00:00"
        time_format = "%Y-%m-%d %H:%M:%S"
        original_time = datetime.strptime(time_string, time_format)
        new_time = original_time - timedelta(hours=8)
        # 将新时间对象格式化为字符串
        new_time_string = new_time.strftime(time_format)
        return new_time_string

    @http.route('/iclock/cdata', type='http', methods=['GET', 'POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def iclock_cdat(self, **kwargs):
        """
        :return:
        """
        _logger.info("初始数据交互")
        _logger.info(http.request.httprequest.data)
        SN = kwargs.get("SN")
        device = http.request.env(user=SUPERUSER_ID)['roke.attendance.device'].search([("sn", "=", SN)])
        table = kwargs.get("table")
        if table == "options":
            return http.Response("OK", headers=[('Content-Type', 'text/plain; charset=utf-8')])
        elif table == "ATTLOG":
            # 考勤记录
            recordObj = http.request.env(user=SUPERUSER_ID)['roke.attendance.record.detail']
            employeeObj = http.request.env(user=SUPERUSER_ID)['roke.employee']
            datas = http.request.httprequest.data.decode().split("\n")
            success_qty = 0
            for data in datas:
                if not data:
                    continue
                success_qty += 1
                record_datas = data.split("\t")
                employee_code = record_datas[0]
                attendance_time = record_datas[1]
                verify = record_datas[3]
                employee = employeeObj.search(["|", ("job_number", "=", employee_code), ("code", "=", employee_code)], limit=1)
                if not employee:
                    continue
                recordObj.create({
                    "device_id": device.id,
                    "employee_id": employee.id,
                    "clock_time": self.to_utc_zero_datetime(attendance_time),
                    "clock_type": "人脸" if verify in ["15", "16", "17", "18", "19", "20"] else "指纹" if verify == "1" else "其他"
                })
            return http.Response("OK:%s" % str(success_qty), headers=[('Content-Type', 'text/plain; charset=utf-8')])
        elif table == "OPERLOG":
            # 操作记录
            return http.Response("OK:1", headers=[('Content-Type', 'text/plain; charset=utf-8')])
        elif table == "BIODATA":
            # 操作记录
            return http.Response("OK:", headers=[('Content-Type', 'text/plain; charset=utf-8')])
        # 初始数据交互
        if device.upload_type == "每日定时上传":
            upload_time = "%s:%s" % (device.upload_time_hour, device.upload_time_minute)
            Realtime = "0"
            TransInterval = "0"
        elif device.upload_type == "实时上传":
            upload_time = ""
            Realtime = "1"
            TransInterval = "0"
        elif device.upload_type == "间隔时间上传":
            upload_time = ""
            Realtime = "0"
            TransInterval = str(device.interval_time)
        else:
            upload_time = ""
            Realtime = "0"
            TransInterval = "0"
        strs = "%s\n%sStamp=%s\nErrorDelay=%s\nDelay=%s\nTransTimes=%s\nTransInterval=%s\nTransFlag=%s\nTimeZone=%s\nRealtime=%s\nEncrypt=%s\nServerVer=%s\nPushProtVer=%s\nPushOptionsFlag=%s\nPushOptions=%s"\
               % (SN, "ATTLOG", "None", str(device.reconn_time), str(device.ping_time), upload_time, TransInterval, "000000000000", "8", Realtime, "0", "None", "", "", "")
        # 设备编号、数据类型ATTLOG考勤记录、时间戳无、联网失败后重连时间30、客户端请求“获取命令时间间隔60、固定传送新数据时间00：00；14:05、数据类型自动上传000000000000、
        # 时区8、客户端是否实时传送新记录0、是否加密0、服务器支持的协议版本号及时间None
        result = {
            "GET OPTION FROM": strs
        }
        _logger.info(result)
        _logger.info("初始数据交互完成")
        return http.Response(json.dumps(result), headers=headers)

    @http.route('/iclock/getrequest', type='http', methods=['GET', 'POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def iclock_getrequest(self, **kwargs):
        _logger.info("请求命令")
        wait_cmds = http.request.env(user=SUPERUSER_ID)['roke.attendance.device.interaction'].search([
            ("state", "=", "等待"), ("type", "=", "发送")
        ])
        response_cmd = ""
        for wait_cmd in wait_cmds:
            response_cmd += wait_cmd.content + ""
        response_cmd = response_cmd.encode("gb2312")
        _logger.info(response_cmd)
        _logger.info("请求命令完成")
        return http.Response(response_cmd, headers=[('Content-Type', 'text/plain; charset=gb2312')])

    @http.route('/iclock/devicecmd', type='http', methods=['GET', 'POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def iclock_devicecmd(self, **kwargs):
        _logger.info("---------------")
        _logger.info(http.request.httprequest.data)
        data = http.request.httprequest.data
        _logger.info("---------------")
        datas = data.decode().split("\n")
        interactionObj = http.request.env(user=SUPERUSER_ID)['roke.attendance.device.interaction']
        for data in datas:
            if not data:
                continue
            index = data.split("&")[0].split("=")[1]
            result = data.split("&")[1].split("=")[1]
            interaction = interactionObj.search([("index", "=", index)])
            if result == "0":
                interaction.write({"state": "完成", "result": data})
        return http.Response("OK", headers=[('Content-Type', 'text/plain; charset=utf-8')])
