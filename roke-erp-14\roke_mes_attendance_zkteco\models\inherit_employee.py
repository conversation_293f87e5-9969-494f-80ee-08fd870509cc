# -*- coding: utf-8 -*-
"""
Description:
    员工操作
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import nanoid


class InheritRokeEmployee(models.Model):
    _inherit = "roke.employee"

    def send_device(self):
        """下发用户至考勤机"""
        res = super(InheritRokeEmployee, self).send_device()
        interactionObj = self.env["roke.attendance.device.interaction"]
        for record in self:
            for device in record.attendance_device_ids:
                cmd_index = nanoid.non_secure_generate(size=10)
                cmd = "C:%s:%s %s" % (cmd_index, "DATA", "UPDATE USERINFO PIN=%s\tName=%s" % (
                record.job_number or record.code or str(record.id), record.name))
                interactionObj.create({
                    "index": cmd_index,
                    "device_id": device.id,
                    "type": "发送",
                    "content": cmd
                })
        return res
