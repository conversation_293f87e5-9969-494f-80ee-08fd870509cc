# -*- coding: utf-8 -*-
{
    'name': '融科MES 辅计量单位',
    'version': '14.0',
    'category': 'mes',
    'depends': ['roke_mes_workshop_material', 'roke_mes_entrust_order', 'roke_mes_salary',
                'roke_mes_assemble_disassemble', 'roke_mes_account_stock'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """
        MES 辅计量单位
    """,
    'data': [
        # 'security/security_groups.xml',
        'security/ir.model.access.csv',
        'data/sequence_data.xml',
        'views/assets.xml',
        'views/inherit_product_views.xml',
        'views/inherit_work_order_views.xml',
        'views/inherit_work_record_views.xml',
        'views/roke_uom_groups_views.xml',
        'views/inherit_roke_sale_order_views.xml',
        'views/inherit_roke_purchase_order_views.xml',
        'views/inherit_roke_mes_stock_picking_views.xml',
        'views/inherit_roke_mes_stock_quant_views.xml',
        'views/roke_stock_auxiliary_quant_views.xml',
        'views/inherit_roke_mes_assemble_disassemble_views.xml',
        'views/inherit_roke_mes_stock_inventory_views.xml',
        'views/inherit_roke_purchase_requisition_views.xml',
        'views/inherit_roke_production_order.xml',
        'views/inherit_roke_production_task.xml',
        'views/inherit_roke_production_result.xml',
        'views/inherit_e_bom_views.xml',
        'views/inherit_entrust_order_views.xml',
        'views/inherit_pt_material_demand_views.xml',
        'views/inherit_wo_material_demand_views.xml',
        'views/inherit_wo_material_record_views.xml',
        'views/inherit_table_submit_work_views.xml',
        'views/inherit_roke_sale_quotation_views.xml',
        'wizard/inherit_wizard_purchase_receiving.xml',
        'wizard/inherit_wizard_generate_red_picking.xml',
        'wizard/inherit_create_work_record_wizard_views.xml',
        'wizard/inherit_picking_move_qty_views.xml',
        'wizard/inherit_stock_query_census_views.xml',
        'wizard/inherit_stock_rds_quant_views.xml',
        'wizard/inherit_stock_quant_action_wizard_views.xml',
        'wizard/inhert_wizard_generate_purchase_order_views.xml',
        'wizard/inherit_bom_create_picking_wizard_views.xml',
        'wizard/inherit_roke_purchase_return_stock_wizard_views.xml',
        'wizard/inherit_roke_sale_return_stock_wizard_views.xml',
        'wizard/inherit_sale_delivery_wizard_views.xml',
        'wizard/inherit_sale_production_wizard_views.xml',
        'wizard/inherit_wizard_order_deduct_view.xml',
        'wizard/inherit_roke_order_create_purchase_wizard_views.xml',
        'wizard/inherit_purchase_wizard_order_deduct.xml',
        'wizard/inherit_wizard_stock_deduct.xml',
        'wizard/inherit_split_task_wizard_views.xml',
        'wizard/inherit_split_work_order_wizard_views.xml',
        'wizard/inherit_transfer_work_order_wizard_views.xml',
        'wizard/inherit_result_put_warehouse_wizard_views.xml',
        'wizard/inherit_ptm_create_purchase_wizard_views.xml',
        'wizard/inherit_wo_create_picking_wizard_views.xml',
        'wizard/inherit_material_input_wizard_views.xml',
        'wizard/inherit_transportation_product_wizard_views.xml',
        'wizard/inherit_assign_customer_wizard_views.xml',
        'wizard/inherit_so_create_bom_pocking_wizard_views.xml',
        'views/menus.xml'
    ],
    'qweb': [
        'static/src/xml/widget_auxiliary.xml',
    ],
    'application': True,
    'installable': True,
}
