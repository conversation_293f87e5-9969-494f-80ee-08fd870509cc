# -*- coding: utf-8 -*-
"""
Description:
    通用单据配置接口
Versions:
    Created by www.rokedata.com
"""
import math
from datetime import timedelta
from odoo import models, fields, http, SUPERUSER_ID, api, _
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)
import requests, json
from datetime import date, datetime
import time, re, sys, os
import jinja2
import qrcode
import io
import base64
from odoo.addons.roke_mes_documents.controllers.inherit_app_general_order import InheritMobile
from odoo.addons.documents.controllers.ris_controller import RisDocuments

if hasattr(sys, 'frozen'):
    # When running on compiled windows binary, we don't have access to package loader.
    path = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'views'))
    loader = jinja2.FileSystemLoader(path)
else:
    loader = jinja2.PackageLoader('odoo.addons.roke_mes_client', "views")

env = jinja2.Environment(loader=loader, autoescape=True)
env.filters["json"] = json.dumps


class InheritAuxMobile(InheritMobile):

    def _get_app_bom_line_value(self, document, bom):
        bom_line_value = super(InheritAuxMobile, self)._get_app_bom_line_value(document, bom)
        aux_list = [{
            "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
            "aux_conversion": rec.conversion,
            "aux_uom_name": rec.uom_id.name,
        } for rec in bom.product_id.uom_groups_id.uom_line_ids]
        aux_compute_type = ''
        if bom.product_id.uom_groups_id:
            if not bom.product_id.uom_groups_id.is_free_conversion:
                aux_compute_type = 'normal'
            # 自由
            else:
                aux_compute_type = 'free'
        bom_line_value.update({
            "auxiliary1_qty": bom.auxiliary1_qty,
            "auxiliary2_qty": bom.auxiliary2_qty,
            "aux_list": aux_list,
            "aux_compute_type": aux_compute_type,
            "uom_name": bom.product_id.uom_id.name,
        })
        return bom_line_value
