# -*- coding: utf-8 -*-
"""
Description:
    bom视图接口添加计量类型
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json,math
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_material_enterprise.controller.bom_tree import BomTree
import logging

_logger = logging.getLogger(__name__)


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritBomTree(BomTree):

    def bom_card_get_product_info(self, product):
        """
        获取bom卡片上的物料信息：添加计量类型
        :return:
        """
        res = super(InheritBomTree, self).bom_card_get_product_info(product)
        if product.uom_type == '多计量':
            res.update({
                'uom_type': '多计量',
                'uom': product.uom_groups_id.display_name or ''
            })
        return res
