# -*- coding: utf-8 -*-
"""
Description:
    bom视图接口添加计量类型
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json, math
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_entrust_order.controller.main import Main
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritEntrustMain(Main):

    def _prepare_material_ids_vals(self, product_list, work_id):
        """
			组装物料明细数据
		"""
        product_info = []
        for i in product_list:
            main_qty = float(i['qty'])
            auxiliary1_qty = float(i.get('auxiliary1_qty', 0))
            auxiliary2_qty = float(i.get('auxiliary2_qty', 0))
            product_info.append((0, 0, {
                'material_id': int(work_id),
                'product_id': int(i['product_id']),
                'note': i['note'],
                'qty': main_qty,
                'auxiliary1_qty': auxiliary1_qty,
                'auxiliary2_qty': auxiliary2_qty,
            }))
        return product_info

    def _prepare_entrust_info_data(self, entrust_orders):
        info = []
        for i in entrust_orders:
            aux_list = [{
                "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                "aux_conversion": rec.conversion,
                "aux_uom_name": rec.uom_id.name,
            } for rec in i.product_id.uom_groups_id.uom_line_ids]
            info.append({
                'work_id': i.id,
                'product': i.product_id.name,
                'product_id': i.product_id.id,
                'plan_qty': i.plan_qty,
                'plan_auxiliary1_qty': i.plan_auxiliary1_qty,
                'plan_auxiliary2_qty': i.plan_auxiliary2_qty,
                'process': i.process_id.name,
                'entrust_customer': i.entrust_customer.name,
                'plan_date': i.plan_date,
                'entrust_state': i.entrust_state,
                'finish_qty': i.finish_qty,
                'finish_auxiliary1_qty': i.finish_auxiliary1_qty,
                'finish_auxiliary2_qty': i.finish_auxiliary2_qty,
                'unqualified_qty': i.unqualified_qty,
                'unqualified_auxiliary1_qty': i.unqualified_auxiliary1_qty,
                'unqualified_auxiliary2_qty': i.unqualified_auxiliary2_qty,
                'note': i.note if i.note else '',
                'aux_list': aux_list,
            })
        return info

    def _prepare_entrust_send_info_data(self, line):
        """
        准备/entrust/send/index接口的info数据
        """
        aux_list = [{
            "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
            "aux_conversion": rec.conversion,
            "aux_uom_name": rec.uom_id.name,
        } for rec in line.product_id.uom_groups_id.uom_line_ids]
        info_dict = {
            'work_id': line.id,
            'product': line.product_id.name,
            'plan_qty': line.plan_qty,
            'plan_auxiliary1_qty': line.plan_auxiliary1_qty,
            'plan_auxiliary2_qty': line.plan_auxiliary2_qty,
            'process': line.process_id.name,
            'entrust_customer': line.entrust_customer.name,
            'plan_date': line.plan_date,
            'entrust_state': line.entrust_state,
            'finish_qty': line.finish_qty,
            'finish_auxiliary1_qty': line.finish_auxiliary1_qty,
            'finish_auxiliary2_qty': line.finish_auxiliary2_qty,
            'aux_list': aux_list,
        }
        return info_dict

    def _prepare_entrust_send_product_list(self, e_bom, entrust_id):
        """准备接口/entrust/send/product中的product_list数据"""
        product_list = []
        for i in e_bom.bom_line_ids:
            # 处理辅计量数据，只处理取余和非自由非取余，自由换算的计量不处理
            main_qty = i.qty * entrust_id.plan_qty
            auxiliary1_qty = 0
            auxiliary2_qty = 0
            # 非自由非取余
            if not i.product_id.is_free_conversion:
                value = http.request.env['roke.uom.groups'].main_auxiliary_conversion(i.product_id, 'main', main_qty)
                auxiliary1_qty = value.get('aux1_qty', 0)
                auxiliary2_qty = value.get('aux2_qty', 0)
            aux_list = [{
                "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                "aux_conversion": rec.conversion,
                "aux_uom_name": rec.uom_id.name,
            } for rec in i.product_id.uom_groups_id.uom_line_ids]
            product_list.append({
                "product_id": i.product_id.id,
                "product_name": i.product_id.name,
                "qty": main_qty,
                "auxiliary1_qty": auxiliary1_qty,
                "auxiliary2_qty": auxiliary2_qty,
                "note": i.note if i.note != False else '',
                "aux_list": aux_list,
            })
        return product_list

    def _prepare_sure_back_product_record_data(self, work_id, entrust_id, qty_dict,
                                               unqualified_qty_dict):
        """
        准备/entrust/sure/back_product接口的record数据
        """
        # 计算辅计量json
        qty = qty_dict['qty']
        auxiliary1_qty = qty_dict['auxiliary1_qty']
        auxiliary2_qty = qty_dict['auxiliary2_qty']
        unqualified_qty = unqualified_qty_dict['qty']
        unqualified_auxiliary1_qty = unqualified_qty_dict['auxiliary1_qty']
        unqualified_auxiliary2_qty = unqualified_qty_dict['auxiliary2_qty']
        record = {
            'work_order_id': int(work_id),
            'product_id': entrust_id.product_id.id,
            'process_id': entrust_id.process_id.id,
            'uom_id': entrust_id.uom_id.id,
            'work_center_id': entrust_id.work_center_id.id,
            'finish_qty': float(qty),
            'finish_auxiliary1_qty': float(auxiliary1_qty),
            'finish_auxiliary2_qty': float(auxiliary2_qty),
            'unqualified_qty': float(unqualified_qty),
            'unqualified_auxiliary1_qty': float(unqualified_auxiliary1_qty),
            'unqualified_auxiliary2_qty': float(unqualified_auxiliary2_qty),
            'customer_id': entrust_id.entrust_customer.id,
            'state': '已确认'
        }
        return record

    def _process_product_finish_qty(self, entrust_id, auxiliary1_qty=0, auxiliary2_qty=0, unqualified_auxiliary1_qty=0,
                                    unqualified_auxiliary2_qty=0):
        """
        准备/entrust/sure/back_product接口的finish_data数据
        """
        # 计算辅计量json
        finish_data = {
            'finish_qty': sum(entrust_id.record_ids.mapped('finish_qty')),
            'finish_auxiliary1_qty': float(auxiliary1_qty),
            'finish_auxiliary2_qty': float(auxiliary2_qty),
            'unqualified_qty': sum(entrust_id.record_ids.mapped('unqualified_qty')),
            'unqualified_auxiliary1_qty': float(unqualified_auxiliary1_qty),
            'unqualified_auxiliary2_qty': float(unqualified_auxiliary2_qty),
        }
        return finish_data