# -*- coding: utf-8 -*-
"""
Description:
    生产相关接口添加辅计量出入参
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json,math
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_production.controller.production import Production
from odoo.addons.roke_mes_production.controller.table_submit_work import TableSubmitWork
from odoo.addons.roke_mes_documents.controllers.document_production import InheritRisDocument
import logging

_logger = logging.getLogger(__name__)


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritAuxRisDocument(InheritRisDocument):
    def _get_document_bom_line_val(self, bom_line, bom_id=False):
        """获取文档bom行列表"""
        res = super(InheritAuxRisDocument, self)._get_document_bom_line_val(bom_line, bom_id=bom_id)
        qty = bom_line.get('qty', 0)
        auxiliary1_qty = bom_line.get('auxiliary1_qty', 0)
        auxiliary2_qty = bom_line.get('auxiliary2_qty', 0)
        product_id = bom_line.get('product_id', False)
        product_obj = http.request.env['roke.product']
        product_record = product_obj.sudo().browse(int(product_id))
        if product_record.uom_groups_id and product_record.uom_type == '多计量':
            # 双非&自由
            aux_val = {'auxiliary1_qty': auxiliary1_qty, 'auxiliary2_qty': auxiliary2_qty}
            res.update(aux_val)
        return res

class InheritProduction(Production):

    def _get_production_info(self, product, detail=False):
        res = super(InheritProduction, self)._get_production_info(product, detail=detail)
        aux_list = []
        aux_compute_type = ""
        if product.uom_type == '多计量' and product.uom_groups_id:
            if not product.uom_groups_id.is_free_conversion:
                aux_compute_type = 'normal'
            # 自由
            else:
                aux_compute_type = "free"
            aux_list = [{
                "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                "aux_conversion": rec.conversion,
                "aux_uom_name": rec.uom_id.name
            } for rec in product.uom_groups_id.uom_line_ids]
        res['aux_list'] = aux_list
        res['aux_compute_type'] = aux_compute_type
        return res

    def _get_work_order(self, work_order, detail=False, file_type=None):
        """
        获取工单信息,添加辅计量单位
        :param work_order:
        :return:
        """
        res = super(InheritProduction, self)._get_work_order(work_order, detail=detail, file_type=file_type)
        res.update({
            "auxiliary_uom1": work_order.auxiliary_uom1_id.name or "",
            "auxiliary_uom2": work_order.auxiliary_uom2_id.name or ""})
        allow_qty, default_qty = work_order._get_wo_allow_qty()
        # 根据成品的辅计量返回辅计量(如果是取余计算则重新计算之后返回）
        uom_groups_obj = http.request.env['roke.uom.groups']
        product_id = work_order.product_id
        if work_order.product_id.uom_type == '多计量':
            res['plan_qty'] = '%s%s%s%s%s%s' % (
                round(work_order.plan_qty, _get_pd(http.request.env())), work_order.uom_id.name or "",
                round(work_order.plan_auxiliary1_qty, _get_pd(http.request.env())),
                work_order.auxiliary_uom1_id.name or "",
                round(work_order.plan_auxiliary2_qty, _get_pd(http.request.env())),
                work_order.auxiliary_uom2_id.name or "")
            res['finish_qty'] = '%s%s%s%s%s%s' % (
                work_order.finish_qty, work_order.uom_id.name or "",
                round(work_order.finish_auxiliary1_qty, _get_pd(http.request.env())),
                work_order.auxiliary_uom1_id.name or "",
                round(work_order.finish_auxiliary2_qty, _get_pd(http.request.env())),
                work_order.auxiliary_uom2_id.name or "")
            aux_info = self.get_auxiliary_info(work_order.product_id, work_order=work_order, allow_qty=allow_qty)
            aux_list = aux_info.get("aux_list", [])
            uom_name = aux_info.get("uom_name", "")
            aux_compute_type = aux_info.get("aux_compute_type", "")
            res['aux_list'] = aux_list
            res['uom_name'] = uom_name
            res['aux_compute_type'] = aux_compute_type
        return res

    def _get_work_record(self, work_record, detail=False):
        """
        获取报工记录信息，添加辅计量
        :param work_record:
        :return:
        """
        res = super(InheritProduction, self)._get_work_record(work_record, detail=detail)
        res.update({
            "auxiliary_uom1": work_record.auxiliary_uom1_id.name or "",
            "auxiliary_uom2": work_record.auxiliary_uom2_id.name or "",
            "finish_auxiliary1_qty": work_record.finish_auxiliary1_qty,
            "finish_auxiliary2_qty": work_record.finish_auxiliary2_qty,
            "unqualified_auxiliary1_qty": work_record.unqualified_auxiliary1_qty,
            "unqualified_auxiliary2_qty": work_record.unqualified_auxiliary2_qty
        })
        return res

    def _get_work_record_values(self, wo, values):
        """
        获取创建工单需要的数据：合格辅数量、不合格辅数量
        :return:
        """
        res = super(InheritProduction, self)._get_work_record_values(wo, values)
        # 合格
        finish_qty = values.get("finish_qty", 0)
        aux_list = values.get("aux_list", [])
        aux_conversion = aux_list[0].get("aux_conversion", 1) if aux_list else 1
        finish_auxiliary1_qty = 0
        finish_auxiliary2_qty = 0
        for rec in aux_list:
            if rec.get('aux_type', False) == 1:
                finish_auxiliary1_qty = rec.get("aux_qty", 0)
            else:
                finish_auxiliary2_qty = rec.get("aux_qty", 0)
        # 不合格
        unqualified_qty = values.get("unqualified_qty")
        unaux_list = values.get("unaux_list", [])
        unqualified_auxiliary1_qty = 0
        unqualified_auxiliary2_qty = 0
        for line in unaux_list:
            if line.get('aux_type', False) == 1:
                unqualified_auxiliary1_qty = line.get("aux_qty", 0)
            else:
                unqualified_auxiliary2_qty = line.get("aux_qty", 0)
        res.update({
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "finish_auxiliary1_qty": finish_auxiliary1_qty,
            "finish_auxiliary2_qty": finish_auxiliary2_qty,
            "unqualified_auxiliary1_qty": unqualified_auxiliary1_qty,
            "unqualified_auxiliary2_qty": unqualified_auxiliary2_qty,
        })
        return res

    def _without_order_get_order_value(self, product_id, process_id, finish_qty, collection_list, values):
        """
        无工单报工获取新建工单数据
        """
        res = super(InheritProduction, self)._without_order_get_order_value(product_id, process_id, finish_qty, collection_list, values)
        aux_compute_type = values.get("aux_compute_type", False)
        product_record = http.request.env['roke.product'].browse(int(product_id))
        # 计划数
        aux_list = values.get("aux_list", [])
        aux_conversion = aux_list[0].get("aux_conversion", 1) if aux_list else 1
        auxiliary1_qty = 0
        auxiliary2_qty = 0
        for rec in aux_list:
            if rec.get('aux_type', False) == 1:
                auxiliary1_qty = rec.get("aux_qty", 0)
            else:
                auxiliary2_qty = rec.get("aux_qty", 0)
        res.update({
            "plan_qty": finish_qty,
            "plan_auxiliary1_qty": auxiliary1_qty,
            "plan_auxiliary2_qty": auxiliary2_qty,
        })
        return res

    def get_input_material_vals(self, work_order_id, material_id, material_info, demand_id):
        """
        投料接口获取投料记录内容,添加辅计量
        :return:
        """
        res = super(InheritProduction, self).get_input_material_vals(work_order_id, material_id, material_info, demand_id)
        auxiliary1_qty = material_info.get("auxiliary1_qty")
        auxiliary2_qty = material_info.get("auxiliary2_qty")
        res.update({
            "auxiliary1_qty": auxiliary1_qty,
            "auxiliary2_qty": auxiliary2_qty,
        })
        return res

    @http.route('/roke/get_product_auxiliary_info', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_product_auxiliary_info(self):
        product_id = http.request.jsonrequest.get('product_id', False)
        product_obj = http.request.env['roke.product']
        aux_list = []
        if product_id:
            product_record = product_obj.browse(int(product_id))
            aux_compute_type = ''
            if product_record.uom_groups_id:
                if not product_record.uom_groups_id.is_free_conversion:
                    aux_compute_type = 'normal'
                # 自由
                else:
                    aux_compute_type = 'free'
                aux_list = [{
                    "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                    "aux_conversion": rec.conversion,
                    "aux_uom_name": rec.uom_id.name
                } for rec in product_record.uom_groups_id.uom_line_ids]
            return {"state": "success",
                    "msgs": "查询成功!",
                    "aux_list": aux_list,
                    "aux_compute_type": aux_compute_type,
                    "uom_name": product_record.uom_id.name or ""
                    }
        else:
            return {"state": "error", "msgs": "产品ID未找到!"}

    def get_auxiliary_info(self, product, work_order=None, allow_qty=0):
        """
        获取工单可报工数量
        :param product:
        :param work_order:
        :return:
        """
        aux_list = []
        if product:
            aux1_qty, aux2_qty, aux_compute_type = 0, 0, ''
            if product.uom_groups_id:
                if not product.uom_groups_id.is_free_conversion:
                    aux_compute_type = 'normal'
                    wait_auxiliary = http.request.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', allow_qty)
                    aux1_qty = wait_auxiliary.get("aux1_qty", 0)
                    aux2_qty = wait_auxiliary.get("aux2_qty", 0)
                # 自由
                else:
                    aux_compute_type = 'free'
                    aux1_qty = 0
                    aux2_qty = 0
                aux_list = [{
                    "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                    "aux_conversion": rec.conversion,
                    "aux_uom_name": rec.uom_id.name,
                    "aux_qty": round(aux1_qty, _get_pd(http.request.env())) if rec.uom_grade == "辅计量1" else round(aux2_qty, _get_pd(http.request.env()))
                } for rec in product.uom_groups_id.uom_line_ids]
            return {
                "aux_list": aux_list,
                "aux_compute_type": aux_compute_type,
                "uom_name": product.uom_id.name or ""
            }
        else:
            return {}

    def pt_get_wo_vals(self, wo, default_qty, print_code):
        res = super(InheritProduction, self).pt_get_wo_vals(wo, default_qty, print_code)
        allow_qty = res.get("allow_qty", 0)
        aux_info = self.get_auxiliary_info(wo.product_id, work_order=wo, allow_qty=allow_qty)
        aux_list = aux_info.get("aux_list", [])
        uom_name = aux_info.get("uom_name", "")
        aux_compute_type = aux_info.get("aux_compute_type", "")
        res['aux_list'] = aux_list
        res['uom_name'] = uom_name
        res['aux_compute_type'] = aux_compute_type
        return res

    def pt_get_wo_vals_new(self, wo, allow_qty, default_qty, print_code):
        res = super(InheritProduction, self).pt_get_wo_vals_new(wo, allow_qty, default_qty, print_code)
        default_qty = res.get("default_qty", 0)
        aux_info = self.get_auxiliary_info(wo.product_id, work_order=wo, allow_qty=default_qty)
        aux_list = aux_info.get("aux_list", [])
        uom_name = aux_info.get("uom_name", "")
        aux_compute_type = aux_info.get("aux_compute_type", "")
        res['aux_list'] = aux_list
        res['uom_name'] = uom_name
        res['aux_compute_type'] = aux_compute_type
        return res


class InheritTableSubmitWork(TableSubmitWork):

    def format_aux_uom_data(self, data, product_id):
        """
        表格报工提交时处理辅计量数据
        :return:
        """
        product = http.request.env["roke.product"].browse(int(product_id))
        aux_compute_type = data.get("aux_compute_type", False)
        aux_list = data.get("aux_list", [])
        unaux_list = data.get("unaux_list", [])
        if not aux_list and not unaux_list:
            return data
        # 辅计量合格数
        finish_qty = data.get("finish_qty", 0)
        finish_auxiliary1_qty = 0
        finish_auxiliary2_qty = 0
        for aux in aux_list:
            if aux.get('aux_type', False) == 1:
                finish_auxiliary1_qty = aux.get("aux_qty", 0)
            else:
                finish_auxiliary2_qty = aux.get("aux_qty", 0)
        data["finish_qty"] = finish_qty
        data["finish_auxiliary1_qty"] = finish_auxiliary1_qty
        data["finish_auxiliary2_qty"] = finish_auxiliary2_qty
        # 辅计量不合格数
        unqualified_qty = data.get("unqualified_qty", 0)
        unqualified_auxiliary1_qty = 0
        unqualified_auxiliary2_qty = 0
        for unaux in unaux_list:
            if unaux.get('aux_type', False) == 1:
                unqualified_auxiliary1_qty = unaux.get("aux_qty", 0)
            else:
                unqualified_auxiliary2_qty = unaux.get("aux_qty", 0)
        data["unqualified_qty"] = unqualified_qty
        data["unqualified_auxiliary1_qty"] = unqualified_auxiliary1_qty
        data["unqualified_auxiliary2_qty"] = unqualified_auxiliary2_qty
        return data
