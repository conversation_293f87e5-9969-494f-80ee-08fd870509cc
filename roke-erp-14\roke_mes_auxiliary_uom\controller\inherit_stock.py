# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError
import json
import logging
import base64
from datetime import datetime, date
import time
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
import re
import math
from odoo.addons.roke_mes_stock.controller.main import Main

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


class InheritAuxiliaryStock(Main):

    def _get_picking_move_val(self, detail, picking, lot_id):
        res = super(InheritAuxiliaryStock, self)._get_picking_move_val(detail, picking, lot_id)
        product_obj = http.request.env['roke.product']
        product_record = product_obj.sudo().browse(int(detail['d_id']))
        auxiliary1_qty = 0
        auxiliary2_qty = 0
        if product_record.uom_type == '多计量':
            product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
            product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
            if not product_record.is_free_conversion:
                qty = float(detail['d_qty'])
                auxiliary1_qty = qty * product_uom_line1.conversion
                auxiliary2_qty = qty * product_uom_line2.conversion
            else:
                auxiliary1_qty = float(detail['auxiliary1_qty'])
                auxiliary2_qty = float(detail['auxiliary2_qty'])
            res.update({
                'auxiliary1_qty': auxiliary1_qty,
                'auxiliary2_qty': auxiliary2_qty,
                'finish_auxiliary1_qty': auxiliary1_qty,
                'finish_auxiliary2_qty': auxiliary2_qty,
            })
        return res

    def _get_in_move_line_vals(self, i, picking_id, move_id, lot_id):
        res = super(InheritAuxiliaryStock, self)._get_in_move_line_vals(i, picking_id, move_id, lot_id)
        product_obj = http.request.env['roke.product']
        product_record = product_obj.sudo().browse(int(i['d_id']))
        if product_record.uom_type == '多计量':
            product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
            product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
            if not product_record.is_free_conversion:
                qty = float(i['d_qty'])
                auxiliary1_qty = qty * product_uom_line1.conversion
                auxiliary2_qty = qty * product_uom_line2.conversion
            else:
                auxiliary1_qty = float(i['auxiliary1_qty'])
                auxiliary2_qty = float(i['auxiliary2_qty'])
            res.update({
                'auxiliary1_qty': auxiliary1_qty,
                'auxiliary2_qty': auxiliary2_qty,
            })
        return res

    def _get_out_move_line_vals(self, i, picking_id, move_id, lot_id):
        res = super(InheritAuxiliaryStock, self)._get_out_move_line_vals(i, picking_id, move_id, lot_id)
        product_obj = http.request.env['roke.product']
        product_record = product_obj.sudo().browse(int(i['d_id']))
        if product_record.uom_type == '多计量':
            product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
            product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
            if not product_record.is_free_conversion:
                qty = float(i['d_qty'])
                auxiliary1_qty = qty * product_uom_line1.conversion
                auxiliary2_qty = qty * product_uom_line2.conversion
            else:
                auxiliary1_qty = float(i['auxiliary1_qty'])
                auxiliary2_qty = float(i['auxiliary2_qty'])
            res.update({
                'auxiliary1_qty': auxiliary1_qty,
                'auxiliary2_qty': auxiliary2_qty,
            })
        return res
