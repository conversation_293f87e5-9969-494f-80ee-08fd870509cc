# -*- coding: utf-8 -*-
"""
Description:
    生产相关接口添加辅计量出入参
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json,math
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_production.controller.table_submit_work import TableSubmitWork
import logging

_logger = logging.getLogger(__name__)


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritProduction(TableSubmitWork):

    def st_table_detail_get_cell(self, wo, employee):
        """
        任务表格报工获取单元格内容，添加辅计量
        :return:
        """
        res = super(InheritProduction, self).st_table_detail_get_cell(wo, employee)
        allow_qty = res.get("allow_qty", 0)
        aux_info = self.get_auxiliary_info(wo.product_id, work_order=wo, allow_qty=allow_qty)
        aux_list = aux_info.get("aux_list", [])
        uom_name = aux_info.get("uom_name", "")
        aux_compute_type = aux_info.get("aux_compute_type", "")
        res['aux_list'] = aux_list
        res['uom_name'] = uom_name
        res['aux_compute_type'] = aux_compute_type
        return res

    def get_st_table_confirm_vals(self, cell):
        """
        获取任务表格报工确认报工值
        :return:
        """
        res = super(InheritProduction, self).get_st_table_confirm_vals(cell)
        wo = http.request.env["roke.work.order"].browse(int(cell.get("wo_id")))
        # 处理合格数辅计量
        finish_qty = cell.get("qty", 0)
        aux_list = cell.get("aux_list", [])
        finish_auxiliary1_qty = 0
        finish_auxiliary2_qty = 0
        for rec in aux_list:
            if rec.get('aux_type', False) == 1:
                finish_auxiliary1_qty = rec.get("aux_qty", 0)
            else:
                finish_auxiliary2_qty = rec.get("aux_qty", 0)
        res.update({
            "finish_qty": finish_qty,
            "finish_auxiliary1_qty": finish_auxiliary1_qty,
            "finish_auxiliary2_qty": finish_auxiliary2_qty
        })
        return res

    def get_st_table_save_employee_vals(self, cell):
        """
        获取任务表格报工保存时，员工对应的临时值
        :return:
        """
        res = super(InheritProduction, self).get_st_table_save_employee_vals(cell)
        qty = res.get("qty", 0)
        wo = http.request.env["roke.work.order"].browse(int(cell.get("wo_id")))
        aux_list = cell.get("aux_list", [])
        res["aux_list"] = aux_list
        res["aux_compute_type"] = cell.get("aux_compute_type", "")
        if wo.product_id.uom_type == '多计量':
            uom_groups_obj = http.request.env['roke.uom.groups']
            qty_auxiliary1_qty = 0
            qty_auxiliary2_qty = 0
            for aux in aux_list:
                if aux.get('aux_type', False) == 1:
                    qty_auxiliary1_qty = aux.get("aux_qty", 0)
                else:
                    qty_auxiliary2_qty = aux.get("aux_qty", 0)
            res['qty'] = '%s%s%s%s%s%s' % (
                round(qty, _get_pd(http.request.env())), wo.uom_id.name or "",
                round(qty_auxiliary1_qty, _get_pd(http.request.env())), wo.auxiliary_uom1_id.name or "",
                round(qty_auxiliary2_qty, _get_pd(http.request.env())), wo.auxiliary_uom2_id.name or "")
        return res
