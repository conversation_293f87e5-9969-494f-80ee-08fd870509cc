# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import math
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)
class InheritBom(models.Model):
    _inherit = "roke.mes.e_bom"

    def get_can_purchase_material(self, qty=1, level=1):
        """
        获取可采购物料及系数
        :param bom_line:
        :return:
        """
        rounding_type = self.env['ir.config_parameter'].sudo().get_param('e_bom.material.demand.rounding',
                                                                         default="精确计算")
        material_list = []
        for line in self.bom_line_ids:
            purchase_qty = (line.qty / (self.qty or 1)) * qty * (1 + line.loss_rate / 100)
            if not line.product_id.is_free_conversion:
                auxiliary1_qty = (line.auxiliary1_qty / (self.qty or 1)) * qty * (1 + line.loss_rate / 100)
                auxiliary2_qty = (line.auxiliary2_qty / (self.qty or 1)) * qty * (1 + line.loss_rate / 100)
            else:
                auxiliary1_qty = 0
                auxiliary2_qty = 0
            if rounding_type == "向下取整":
                purchase_qty = int(purchase_qty)
                auxiliary1_qty = int(auxiliary1_qty)
                auxiliary2_qty = int(auxiliary2_qty)
            elif rounding_type == "向上取整":
                purchase_qty = math.ceil(purchase_qty)
                auxiliary1_qty = math.ceil(auxiliary1_qty)
                auxiliary2_qty = math.ceil(auxiliary2_qty)
            if line.product_id.purchase:
                material_list.append({
                    "parent_material_id": self.product_id,
                    "material": line.product_id,
                    "qty": purchase_qty,
                    "auxiliary1_qty": auxiliary1_qty,
                    "auxiliary2_qty": auxiliary2_qty,
                    "level": level
                })
            if line.child_bom_id:
                material_list += line.child_bom_id.get_can_purchase_material(purchase_qty, level + 1)
        material_list = sorted(material_list, key=lambda v: v.get("level"))
        return material_list

class InheritEBomLine(models.Model):
    _inherit = "roke.mes.e_bom.line"

    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量2")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                self.qty = 1
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            else:
                self.qty = 1
                self.auxiliary1_qty = 0
                self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _order_bom_get_pt_vals(self, product, qty, bom_line, e_bom_id, po_line=None, sale_line=None):
        """
        BOM投产获取任务值
        :return:
        """
        res = super(InheritEBomLine, self)._order_bom_get_pt_vals(product, qty, bom_line, e_bom_id, po_line=po_line, sale_line=sale_line)
        value = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', qty)
        res.update({
            "plan_auxiliary1_qty": value.get('aux1_qty', 0),
            "plan_auxiliary2_qty": value.get('aux2_qty', 0)
        })
        return res
