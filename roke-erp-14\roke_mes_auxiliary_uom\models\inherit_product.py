# -*- coding: utf-8 -*-
"""
Description:
    产品添加辅计量单位
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import datetime
import json


class InheritProduct(models.Model):
    _inherit = "roke.product"

    auxiliary_uom1_id = fields.Many2one("roke.uom", string="辅计量单位1", compute='_compute_auxiliary_uom1_id',
                                        store=True)
    auxiliary_uom2_id = fields.Many2one("roke.uom", string="辅计量单位2", compute='_compute_auxiliary_uom1_id',
                                        store=True)
    uom_type = fields.Selection([("单计量", "单计量"), ("多计量", "多计量")], string="计量类型", default="单计量")
    uom_groups_id = fields.Many2one("roke.uom.groups", string="多计量组")
    # uom_line_ids = fields.One2many("roke.product.uom.line", "product_id", string="计量单位列表")
    is_free_conversion = fields.Bo<PERSON>an(string="是否自由换算", related='uom_groups_id.is_free_conversion')
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               compute="_compute_is_real_time_calculations")

    @api.constrains('uom_type', 'uom_groups_id')
    def _check_uom_groups_id(self):
        """校验计量类型从单计量转换成多计量时, 如果还有库存,则禁止"""
        for rec in self:
            if rec.uom_type == '多计量' and rec.current_qty != 0:
                raise ValidationError('该产品当前数量不为0,请先调账之后再更改为多计量')

    @api.onchange('uom_type')
    def _onchange_uom_type(self):
        if self.uom_type == "单计量":
            self.uom_groups_id = None

    @api.depends('uom_groups_id', 'uom_type')
    def _compute_auxiliary_uom1_id(self):
        for rec in self:
            if rec.uom_groups_id:
                # 清空辅计量
                rec.auxiliary_uom1_id = None
                rec.auxiliary_uom2_id = None
                # 处理主辅计量单位
                rec.uom_id = rec.uom_groups_id.main_uom_id.id
                not_main_uom_uom_lines = self.env['roke.uom.line'].search([
                    ('uom_groups_id', '=', rec.uom_groups_id.id),
                ])
                for line in not_main_uom_uom_lines:
                    if line.uom_grade == '辅计量1':
                        rec.auxiliary_uom1_id = line.uom_id.id
                    else:
                        rec.auxiliary_uom2_id = line.uom_id.id
            else:
                rec.auxiliary_uom1_id = None
                rec.auxiliary_uom2_id = None

        # productUomLineObj = self.env['roke.product.uom.line']
        # uom_line = []
        # for line in self.uom_groups_id.uom_line_ids:
        #     productUomLine = productUomLineObj.create({
        #         "uom_id": line.uom_id.id,
        #         "uom_grade": line.uom_grade,
        #         "conversion": line.conversion,
        #         "note": line.note,
        #         "unit_price": 0,
        #         "purchase_unit_price": 0,
        #         "sale_unit_price": 0,
        #     })
        #     uom_line.append(productUomLine.id)
        # return {"value": {"uom_line_ids": [(6, 0, uom_line)]}}

    @api.depends('is_free_conversion', 'uom_groups_id')
    def _compute_is_real_time_calculations(self):
        for rec in self:
            if not rec.is_free_conversion:
                rec.is_real_time_calculations = True
            else:
                rec.is_real_time_calculations = False

    @api.model
    def calculate_qty(self, data):
        product_obj = self.env['roke.product']
        product_record = product_obj.browse(int(data.get('product_id', 0)))
        product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
        product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
        if data.get('flag', '') == 'aux1_qty':
            aux1_qty = float(data.get('value', 0))
            if product_uom_line1 and product_uom_line1.conversion:
                main_qty = aux1_qty / product_uom_line1.conversion
                if product_uom_line2 and product_uom_line2.conversion:
                    aux2_qty = main_qty * product_uom_line2.conversion
                else:
                    aux2_qty = 0
            else:
                main_qty = 0
        elif data.get('flag', '') == 'aux2_qty':
            aux2_qty = float(data.get('value', 0))
            if product_uom_line2 and product_uom_line2.conversion:
                main_qty = aux2_qty / product_uom_line2.conversion
                if product_uom_line1 and product_uom_line1.conversion:
                    aux1_qty = main_qty * product_uom_line1.conversion
                else:
                    aux1_qty = 0
            else:
                main_qty = 0
        else:
            main_qty = float(data.get('value', 0))
            if product_uom_line1 and product_uom_line1.conversion:
                aux1_qty = main_qty * product_uom_line1.conversion
            else:
                aux1_qty = 0
            if product_uom_line2 and product_uom_line2.conversion:
                aux2_qty = main_qty * product_uom_line2.conversion
            else:
                aux2_qty = 0
        result = {"main_qty": main_qty, "aux1_qty": aux1_qty,
                  "aux2_qty": aux2_qty}
        return result

# class ProductUomLine(models.Model):
#     _name = "roke.product.uom.line"
#
#     product_id = fields.Many2one("roke.product", string="产品")
#     uom_id = fields.Many2one("roke.uom", string="计量单位", required=True)
#     uom_grade = fields.Selection([('主计量', '主计量'), ('辅计量1', '辅计量1'), ('辅计量2', '辅计量2')],
#                                  string="计量等级")
#     conversion = fields.Float(string="换算关系", digits='Stock')
#     note = fields.Char(string="换算说明")
#     unit_price = fields.Float(string="单价")
#     purchase_unit_price = fields.Float(string="采购单价")
#     sale_unit_price = fields.Float(string="销售单价")
