# -*- coding: utf-8 -*-
"""
Description:
    生产任务BOM领料时添加辅计量数量
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritProductionTask(models.Model):
    _inherit = "roke.production.task"

    def _get_demand_create_picking_line_vals(self, demand):
        """
        获取bom生成调拨单的数据,添加辅计量
        """
        res = super(InheritProductionTask, self)._get_demand_create_picking_line_vals(demand)
        material = demand.material_id
        qty = res.get("qty") or 0
        demand_qty = res.get("demand_qty") or 0
        if not material.is_free_conversion:
            value = self.env['roke.uom.groups'].main_auxiliary_conversion(material, 'main', qty)
            demand_value = self.env['roke.uom.groups'].main_auxiliary_conversion(material, 'main', demand_qty)
            res.update({
                "demand_auxiliary1_qty": demand_value.get('aux1_qty', 0),
                "demand_auxiliary2_qty": demand_value.get('aux2_qty', 0),
                "auxiliary1_qty": value.get('aux1_qty', 0),
                "auxiliary2_qty": value.get('aux2_qty', 0)
            })
        else:
            res.update({
                "demand_auxiliary1_qty": 0,
                "demand_auxiliary2_qty": 0,
                "auxiliary1_qty": 0,
                "auxiliary2_qty": 0,
            })
        return res
