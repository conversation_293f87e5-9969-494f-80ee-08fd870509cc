# -*- coding: utf-8 -*-
"""
Description:
    任务物料需求添加辅计量内容
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


class InheritPTMaterialDemand(models.Model):
    _inherit = "roke.pt.material.demand"

    uom_id = fields.Many2one(related="material_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one(related="material_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="material_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON>an(string="辅计量是否实时计算", related="material_id.is_real_time_calculations")
    # 需求数量
    demand_auxiliary_json = fields.Char(string="需求数量")
    demand_auxiliary1_qty = fields.Float(string="需求辅数量1", digits='SCSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅数量2", digits='SCSL')
    demand_uom_info = fields.Char(string="需求数量", compute="_compute_demand_uom_info")
    # 库存数量
    stock_auxiliary_json = fields.Char(string="库存数量", compute="_compute_stock_qty")
    stock_auxiliary1_qty = fields.Float(string="库存辅助数量1", compute="_compute_stock_qty", digits='KCSL')
    stock_auxiliary2_qty = fields.Float(string="库存辅助数量2", compute="_compute_stock_qty", digits='KCSL')

    @api.depends('demand_auxiliary1_qty', 'demand_auxiliary2_qty', 'demand_qty')
    def _compute_demand_uom_info(self):
        for rec in self:
            demand_uom_info = str(rec.demand_qty) + str(rec.uom_id.name)
            if rec.product_id and rec.product_id.uom_type == '多计量':
                if rec.auxiliary_uom1_id:
                    demand_uom_info += ' ' + str(rec.demand_auxiliary1_qty) + str(rec.auxiliary_uom1_id.name)
                if rec.auxiliary_uom2_id:
                    demand_uom_info += ' ' + str(rec.demand_auxiliary2_qty) + str(rec.auxiliary_uom2_id.name)
            rec.demand_uom_info = demand_uom_info

    def _compute_stock_qty(self):
        location_obj = self.env['roke.mes.stock.location']
        for record in self:
            location_records = location_obj.sudo().search([('location_type', 'in', ['内部位置', '中转位置'])])
            quants = self.env["roke.mes.stock.quant"].sudo().search_quants(products=record.material_id,
                                                                           locations=location_records)
            stock_qty = sum(quants.mapped("inventory_quantity"))
            stock_auxiliary1_qty = sum(quants.mapped("auxiliary1_qty"))
            stock_auxiliary2_qty = sum(quants.mapped("auxiliary2_qty"))
            record.stock_qty = stock_qty
            record.stock_auxiliary1_qty = stock_auxiliary1_qty
            record.stock_auxiliary2_qty = stock_auxiliary2_qty
            record.stock_auxiliary_json = json.dumps({
                'main_qty': stock_qty,
                'aux1_qty': stock_auxiliary1_qty,
                'aux2_qty': stock_auxiliary2_qty
            })

    def _get_ptm_create_purchase_vals(self, pt_demand, supplier_id, unit_price, qty):
        """
        获取采购向导内容
        :return:
        """
        res = super(InheritPTMaterialDemand, self)._get_ptm_create_purchase_vals(pt_demand, supplier_id, unit_price, qty)
        aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(pt_demand.material_id, 'main', qty)
        res.update({
            "auxiliary_json": json.dumps(aux_value),
            "auxiliary1_qty": aux_value.get('aux1_qty', 0),
            "auxiliary2_qty": aux_value.get('aux2_qty', 0)
        })
        return res
