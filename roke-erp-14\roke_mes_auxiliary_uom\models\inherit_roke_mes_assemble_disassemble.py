#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_roke_mes_assemble_disassemble.py.py
@Time:
    2022/10/12 09:44
@Site: 
    
@Desc:
    
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InheritRokeMesAssembleDisassemble(models.Model):
    _inherit = "roke.mes.assemble.disassemble"

    auxiliary1_qty = fields.Float(string="辅助数量1", digits='SCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='SCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Bo<PERSON>an(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    def make_picking(self):
        # 生成库存单据
        # 判断是组装还是拆卸
        # 组装时：表头产品其他入库、表体物料其他出库
        # 拆卸时：表头产品其他出库、表体物料其他入库
        picking = self.env["roke.mes.stock.picking"]
        move = self.env["roke.mes.stock.move"]
        # 其他出库作业类型
        picking_type_out = self.env.ref("roke_mes_assemble_disassemble.stock_picking_type_other_out")
        # 其他入库作业类型
        picking_type_in = self.env.ref("roke_mes_assemble_disassemble.stock_picking_type_other_in")
        if self.business_type == "组装":
            # 创建出库单==表体出
            pickingObj = picking.create({
                "picking_type_id": picking_type_out.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_out.src_location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "origin": self.code,
            })
            for line in self.line_ids:
                moveObj = move.create({
                    "picking_id": pickingObj.id,
                    "src_location_id": line.location_id.id,
                    "dest_location_id": picking_type_out.dest_location_id.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "auxiliary1_qty": line.auxiliary1_qty,
                    "auxiliary2_qty": line.auxiliary2_qty,
                    "origin": self.code,
                })
                line.move_id = moveObj.id
            # 创建入库单==表头入
            pickingObj = picking.create({
                "picking_type_id": picking_type_in.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": self.location_id.id,
                "origin": self.code,
            })
            moveObj = move.create({
                "picking_id": pickingObj.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": self.location_id.id,
                "product_id": self.product_id.id,
                "qty": self.qty,
                "auxiliary1_qty": self.auxiliary1_qty,
                "auxiliary2_qty": self.auxiliary2_qty,
                "origin": self.code,
            })
            self.move_id = moveObj.id
        elif self.business_type == "拆卸":
            # 创建出库单==表头出
            pickingObj = picking.create({
                "picking_type_id": picking_type_out.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": self.location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "origin": self.code,
            })
            moveObj = move.create({
                "picking_id": pickingObj.id,
                "src_location_id": self.location_id.id,
                "dest_location_id": picking_type_out.dest_location_id.id,
                "product_id": self.product_id.id,
                "qty": self.qty,
                "auxiliary1_qty": self.auxiliary1_qty,
                "auxiliary2_qty": self.auxiliary2_qty,
                "origin": self.code,
            })
            self.move_id = moveObj.id
            # 创建入库单==表体入
            pickingObj = picking.create({
                "picking_type_id": picking_type_in.id,
                "assemble_disassemble_id": self.id,
                "partner_id": self.partner_id.id,
                "src_location_id": picking_type_in.src_location_id.id,
                "dest_location_id": picking_type_in.dest_location_id.id,
                "origin": self.code,
            })
            for line in self.line_ids:
                moveObj = move.create({
                    "picking_id": pickingObj.id,
                    "src_location_id": picking_type_in.src_location_id.id,
                    "dest_location_id": line.location_id.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "auxiliary1_qty": self.auxiliary1_qty,
                    "auxiliary2_qty": self.auxiliary2_qty,
                    "origin": self.code,
                })
                line.move_id = moveObj.id

    def make_move_line(self):
        move_line = self.env["roke.mes.stock.move.line"]
        move_line_vals = []
        # 先生成表头对应的move_line
        move_line_vals.append({
            "move_id": self.move_id.id,
            "lot_id": self.make_lot(self.lot_code),
            "src_location_id": self.move_id.src_location_id.id,
            "dest_location_id": self.move_id.dest_location_id.id,
            "qty": self.qty,
            "auxiliary1_qty": self.auxiliary1_qty,
            "auxiliary2_qty": self.auxiliary2_qty
        })
        # 再生成表体对应的moven_line
        for line in self.line_ids:
            move_line_vals.append({
            "move_id": line.move_id.id,
            "lot_id": line.make_lot(line.lot_code),
            "src_location_id": line.move_id.src_location_id.id,
            "dest_location_id": line.move_id.dest_location_id.id,
            "qty": line.qty,
            "auxiliary1_qty": line.auxiliary1_qty,
            "auxiliary2_qty": line.auxiliary2_qty
            })
        move_line.create(move_line_vals)

    @api.onchange("e_bom_id")
    def _onchange_e_bom_id(self):
        # 算出bom物料比例
        if self.e_bom_id:
            ratio = self.qty / self.e_bom_id.qty
            for line in self.line_ids:
                if line.e_bom_line_id:
                    self.line_ids = [(2, line.id)]
            assembleDisassembleLine = self.env["roke.mes.assemble.disassemble.line"]
            for bom_line in self.e_bom_id.bom_line_ids:
                auxiliary1_qty = 0
                auxiliary2_qty = 0
                if not bom_line.product_id.is_free_conversion:
                    # 计算辅数量1
                    product_uom1_line = bom_line.product_id.uom_groups_id.uom_line_ids.filtered(
                        lambda a: a.uom_id.id == bom_line.product_id.auxiliary_uom1_id.id)
                    if not product_uom1_line:
                        auxiliary1_qty = 0
                    else:
                        auxiliary1_qty = bom_line.qty * product_uom1_line.conversion
                    # 计算辅数量2
                    product_uom2_line = bom_line.product_id.uom_groups_id.uom_line_ids.filtered(
                        lambda a: a.uom_id.id == bom_line.product_id.auxiliary_uom2_id.id)
                    if not product_uom2_line:
                        auxiliary2_qty = 0
                    else:
                        auxiliary2_qty = bom_line.qty * product_uom2_line.conversion
                self.line_ids = [(0, 0, {
                    "product_id": bom_line.product_id.id,
                    "qty": bom_line.qty * (1 if ratio == 0 else ratio),
                    "auxiliary1_qty": auxiliary1_qty * (1 if ratio == 0 else ratio),
                    "auxiliary2_qty": auxiliary2_qty * (1 if ratio == 0 else ratio),
                    "e_bom_line_id": bom_line.id,
                })]
        else:
            for line in self.line_ids:
                if line.e_bom_line_id:
                    self.line_ids = [(2, line.id)]

    # @api.onchange("auxiliary2_qty")
    # def _onchange_auxiliary2_qty(self):
    #     if self.auxiliary_uom2_id:
    #         if not self.product_id.is_free_conversion:
    #             # 先查询是否有辅计量单位2
    #             product_uom_line = self.env["roke.product.uom.line"].search([
    #                 ("product_id", "=", self.product_id.id),
    #                 ("uom_id", "=", self.product_id.auxiliary_uom2_id.id)
    #             ], limit=1)
    #             if not product_uom_line:
    #                 self.qty = self.auxiliary2_qty
    #             else:
    #                 self.qty = self.auxiliary2_qty / product_uom_line.conversion
    #
    # @api.onchange("auxiliary1_qty")
    # def _onchange_auxiliary1_qty(self):
    #     if self.auxiliary_uom1_id:
    #         if not self.product_id.is_free_conversion:
    #             # 先查询是否有辅计量单位1
    #             product_uom_line = self.env["roke.product.uom.line"].search([
    #                 ("product_id", "=", self.product_id.id),
    #                 ("uom_id", "=", self.product_id.auxiliary_uom1_id.id)
    #             ], limit=1)
    #             if not product_uom_line:
    #                 self.qty = self.auxiliary1_qty
    #             else:
    #                 self.qty = self.auxiliary1_qty / product_uom_line.conversion
    #
    # @api.onchange("qty")
    # def _onchange_qty(self):
    #     if self.product_id and self.e_bom_id:
    #         for line in self.line_ids:
    #             if line.e_bom_line_id:
    #                 line.qty = line.e_bom_line_id.qty / self.e_bom_id.qty * self.qty
    #                 line._onchange_qty()
    #     if not self.product_id.is_free_conversion:
    #         # 计算辅数量1
    #         product_uom_line = self.env["roke.product.uom.line"].search([
    #             ("product_id", "=", self.product_id.id),
    #             ("uom_id", "=", self.product_id.auxiliary_uom1_id.id)
    #         ], limit=1)
    #         if not product_uom_line:
    #             self.auxiliary1_qty = 0
    #         else:
    #             self.auxiliary1_qty = self.qty * product_uom_line.conversion
    #
    #         # 计算辅数量2
    #         product_uom_line = self.env["roke.product.uom.line"].search([
    #             ("product_id", "=", self.product_id.id),
    #             ("uom_id", "=", self.product_id.auxiliary_uom2_id.id)
    #         ], limit=1)
    #         if not product_uom_line:
    #             self.auxiliary2_qty = 0
    #         else:
    #             self.auxiliary2_qty = self.qty * product_uom_line.conversion


class InheritRokeMesAssembleDisassembleLine(models.Model):
    _inherit = "roke.mes.assemble.disassemble.line"

    auxiliary1_qty = fields.Float(string="辅助数量1", digits='SCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='SCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    # @api.onchange("auxiliary2_qty")
    # def _onchange_auxiliary2_qty(self):
    #     if self.auxiliary_uom2_id:
    #         if not self.product_id.is_free_conversion:
    #             # 先查询是否有辅计量单位2
    #             product_uom_line = self.env["roke.product.uom.line"].search([
    #                 ("product_id", "=", self.product_id.id),
    #                 ("uom_id", "=", self.product_id.auxiliary_uom2_id.id)
    #             ], limit=1)
    #             if not product_uom_line:
    #                 self.qty = self.auxiliary2_qty
    #             else:
    #                 self.qty = self.auxiliary2_qty / product_uom_line.conversion
    #
    # @api.onchange("auxiliary1_qty")
    # def _onchange_auxiliary1_qty(self):
    #     if self.auxiliary_uom1_id:
    #         if not self.product_id.is_free_conversion:
    #             # 先查询是否有辅计量单位1
    #             product_uom_line = self.env["roke.product.uom.line"].search([
    #                 ("product_id", "=", self.product_id.id),
    #                 ("uom_id", "=", self.product_id.auxiliary_uom1_id.id)
    #             ], limit=1)
    #             if not product_uom_line:
    #                 self.qty = self.auxiliary1_qty
    #             else:
    #                 self.qty = self.auxiliary1_qty / product_uom_line.conversion
    #
    # @api.onchange("qty")
    # def _onchange_qty(self):
    #     if not self.product_id.is_free_conversion:
    #         # 计算辅数量1
    #         product_uom_line = self.env["roke.product.uom.line"].search([
    #             ("product_id", "=", self.product_id.id),
    #             ("uom_id", "=", self.product_id.auxiliary_uom1_id.id)
    #         ], limit=1)
    #         if not product_uom_line:
    #             self.auxiliary1_qty = 0
    #         else:
    #             self.auxiliary1_qty = self.qty * product_uom_line.conversion
    #
    #         # 计算辅数量2
    #         product_uom_line = self.env["roke.product.uom.line"].search([
    #             ("product_id", "=", self.product_id.id),
    #             ("uom_id", "=", self.product_id.auxiliary_uom2_id.id)
    #         ], limit=1)
    #         if not product_uom_line:
    #             self.auxiliary2_qty = 0
    #         else:
    #             self.auxiliary2_qty = self.qty * product_uom_line.conversion
