#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_roke_mes_stock_inventory.py.py
@Time:
    2022/12/6 17:17
@Site: 
    
@Desc:
    
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.osv import expression
import json

class InheritRokeMesStockInventory(models.Model):
    _inherit = "roke.mes.stock.inventory"

    def action_validate(self):
        """确认盘点"""
        self.ensure_one()
        # 创建物料移动、明细
        MoveObj = self.env["roke.mes.stock.move"]
        inventory_location = self.env.ref('roke_mes_stock.stock_location_locations_inventory')
        for line in self.line_ids:
            if not line.difference:
                continue
            if line.difference > 0:
                src_location_id = inventory_location.id
                dest_location_id = line.location_id.id
            else:
                src_location_id = line.location_id.id,
                dest_location_id = inventory_location.id,
            MoveObj.create({
                "inventory_id": self.id,
                "src_location_id": src_location_id,
                "dest_location_id": dest_location_id,
                "product_id": line.product_id.id,
                "qty": abs(line.difference),
                "auxiliary1_qty": abs(line.auxiliary1_difference),
                "auxiliary2_qty": abs(line.auxiliary2_difference),
                "move_date": line.inventory_date,
                "origin": self.name,
                "line_ids": [(0, 0, {
                    "origin": self.name,
                    "src_location_id": src_location_id,
                    "dest_location_id": dest_location_id,
                    "product_id": line.product_id.id,
                    "qty": abs(line.difference),
                    "auxiliary1_qty": abs(line.auxiliary1_difference),
                    "auxiliary2_qty": abs(line.auxiliary2_difference),
                    "lot_id": line.lot_id and line.lot_id.id,
                    "lot_note": line.lot_note
                })]
            })
        self.move_ids.action_done()
        self.write({'state': '完成'})

    def _get_inventory_lines_values(self):
        self.ensure_one()
        quants_groups = self._get_quantities()
        vals = []
        for (product_id, location_id, lot_id), (quantity, auxiliary1_qty, auxiliary2_qty) in quants_groups.items():
            line_values = {
                'inventory_id': self.id,
                'sys_qty': quantity,
                'sys_auxiliary1_qty': auxiliary1_qty,
                'sys_auxiliary2_qty': auxiliary2_qty,
                'qty': quantity,
                'auxiliary1_qty': auxiliary1_qty,
                'auxiliary2_qty': auxiliary2_qty,
                'lot_id': lot_id,
                'product_id': product_id,
                'location_id': location_id,
            }
            vals.append(line_values)
        return vals

    def _get_quantities(self):
        self.ensure_one()
        if self.location_ids:
            domain_loc = [('id', 'child_of', self.location_ids.ids)]
        else:
            domain_loc = [('location_type', 'in', ['内部位置', '中转位置', '生产位置'])]
        locations_ids = [l['id'] for l in self.env['roke.mes.stock.location'].search_read(domain_loc, ['id'])]
        domain = [('qty', '!=', '0'),
                  ('location_id', 'in', locations_ids)]
        if self.product_ids:
            domain = expression.AND([domain, [('product_id', 'in', self.product_ids.ids)]])
        fields = ['product_id', 'location_id', 'lot_id', 'qty:sum', 'auxiliary1_qty:sum', 'auxiliary2_qty:sum']
        group_by = ['product_id', 'location_id', 'lot_id']
        quants = self.env['roke.mes.stock.quant'].read_group(domain, fields, group_by, lazy=False)
        return {(
                    quant['product_id'] and quant['product_id'][0] or False,
                    quant['location_id'] and quant['location_id'][0] or False,
                    quant['lot_id'] and quant['lot_id'][0] or False):
                    (quant['qty'], quant['auxiliary1_qty'], quant['auxiliary2_qty']) for quant in quants
                }

class InheritRokeMesStockInventoryLine(models.Model):
    _inherit = "roke.mes.stock.inventory.line"

    auxiliary1_qty = fields.Float(string="实际辅数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="实际辅数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    sys_auxiliary1_qty = fields.Float(string="系统辅数量1", digits='KCSL')
    sys_auxiliary2_qty = fields.Float(string="系统辅数量2", digits='KCSL')
    auxiliary1_difference = fields.Float(string="辅差额1", digits='KCSL', compute="_compute_difference", store=True)
    auxiliary2_difference = fields.Float(string="辅差额2", digits='KCSL', compute="_compute_difference", store=True)
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.depends("sys_qty", "qty", "sys_auxiliary1_qty", "auxiliary1_qty", "sys_auxiliary2_qty", "auxiliary2_qty")
    def _compute_difference(self):
        for record in self:
            record.difference = record.qty - record.sys_qty
            record.auxiliary1_difference = record.auxiliary1_qty - record.sys_auxiliary1_qty
            record.auxiliary2_difference = record.auxiliary2_qty - record.sys_auxiliary2_qty


class InheritRokeMesStockInventorySheet(models.Model):
    _inherit = 'roke.mes.stock.inventory.sheet'

    def action_validate(self):
        """确认盘点"""
        self.ensure_one()
        # 创建物料移动、明细
        MoveObj = self.env["roke.mes.stock.move"]
        inventory_location = self.env.ref('roke_mes_stock.stock_location_locations_inventory')
        for line in self.line_ids:
            if not line.difference:
                continue
            if line.difference > 0:
                src_location_id = inventory_location.id
                dest_location_id = line.location_id.id
            else:
                src_location_id = line.location_id.id,
                dest_location_id = inventory_location.id,
            lot = line.lot_id
            if lot and lot.partner_id:
                if line.partner_id and line.partner_id != lot.partner_id:
                    lot.write({"partner_id": line.partner_id.id})
            MoveObj.create({
                "inventory_sheet_id": self.id,
                "src_location_id": src_location_id,
                "dest_location_id": dest_location_id,
                "product_id": line.product_id.id,
                "qty": abs(line.difference),
                "auxiliary1_qty": abs(line.difference_auxiliary1_qty),
                "auxiliary2_qty": abs(line.difference_auxiliary2_qty),
                "move_date": line.inventory_date,
                "line_ids": [(0, 0, {
                    "src_location_id": src_location_id,
                    "dest_location_id": dest_location_id,
                    "product_id": line.product_id.id,
                    "qty": abs(line.difference),
                    "auxiliary1_qty": abs(line.difference_auxiliary1_qty),
                    "auxiliary2_qty": abs(line.difference_auxiliary2_qty),
                    "lot_id": lot.id,
                    "lot_note": line.lot_note
                })]
            })
        self.move_ids.action_done()
        self.write({'state': '完成'})

    def _get_inventory_lines_values(self):
        self.ensure_one()
        LotObj = self.env["roke.mes.stock.lot"]
        quants_groups = self._get_quantities()
        vals = []
        for (product_id, location_id, lot_note, lot_id), (
        quantity, auxiliary1_qty, auxiliary2_qty) in quants_groups.items():
            lot = LotObj.browse(lot_id)
            line_values = {
                'inventory_id': self.id,
                'sys_qty': quantity,
                'system_auxiliary1_qty': auxiliary1_qty,
                'system_auxiliary2_qty': auxiliary2_qty,
                'qty': quantity,
                'sheet_auxiliary1_qty': auxiliary1_qty,
                'sheet_auxiliary2_qty': auxiliary2_qty,
                'lot_id': lot_id,
                'partner_id': lot.partner_id.id,
                'product_id': product_id,
                'location_id': location_id,
                'lot_note': lot_note
            }
            vals.append(line_values)
        for record in vals:
            product = self.env['roke.product'].search([('id', '=', record.get('product_id'))])
            # 非自由非取余
            if not product.is_free_conversion:
                qty_json = product.uom_groups_id.main_auxiliary_conversion(product, 'main', record.get('qty'))
                sheet_auxiliary1_qty = qty_json.get('aux1_qty')
                sheet_auxiliary2_qty = qty_json.get('aux2_qty')
                sys_json = product.uom_groups_id.main_auxiliary_conversion(product, 'main', record.get('sys_qty'))
                system_auxiliary1_qty = sys_json.get('aux1_qty')
                system_auxiliary2_qty = sys_json.get('aux2_qty')
                record.update({"sheet_auxiliary1_qty": sheet_auxiliary1_qty,
                               "sheet_auxiliary2_qty": sheet_auxiliary2_qty,
                               "system_auxiliary1_qty": system_auxiliary1_qty,
                               "system_auxiliary2_qty": system_auxiliary2_qty
                               })
        return vals

    def _get_quantities(self):
        self.ensure_one()
        if self.location_ids:
            domain_loc = [('id', 'child_of', self.location_ids.ids)]
        else:
            domain_loc = [('location_type', 'in', ['内部位置', '中转位置', '生产位置'])]
        locations_ids = [l['id'] for l in self.env['roke.mes.stock.location'].search_read(domain_loc, ['id'])]
        domain = [('qty', '!=', '0'),
                  ('location_id', 'in', locations_ids),
                  ]
        if self.product_ids:
            domain = expression.AND([domain, [('product_id', 'in', self.product_ids.ids)]])
        fields = ['product_id', 'location_id', 'lot_id', 'lot_note', 'qty:sum', 'auxiliary1_qty:sum',
                  'auxiliary2_qty:sum']
        group_by = ['product_id', 'location_id', 'lot_id', 'lot_note']
        quants = self.env['roke.mes.stock.quant'].read_group(domain, fields, group_by, lazy=False)
        return {(
                    quant['product_id'] and quant['product_id'][0] or False,
                    quant['location_id'] and quant['location_id'][0] or False,
                    quant['lot_note'] and quant['lot_note'][0] or False,
                    quant['lot_id'] and quant['lot_id'][0] or False):
                    (quant['qty'], quant['auxiliary1_qty'], quant['auxiliary2_qty']) for quant in quants
                }

    @api.onchange('line_ids')
    def _onchange_line_ids(self):
        for line in self.line_ids:
            if not line.product_id.is_free_conversion:
                qty_json = line.product_id.uom_groups_id.main_auxiliary_conversion(line.product_id, 'main', line.qty)
                line.sheet_auxiliary1_qty = qty_json.get('aux1_qty')
                line.sheet_auxiliary2_qty = qty_json.get('aux2_qty')

                diff_json = line.product_id.uom_groups_id.main_auxiliary_conversion(line.product_id, 'main',
                                                                                    line.difference)
                line.difference_auxiliary1_qty = diff_json.get('aux1_qty')
                line.difference_auxiliary2_qty = diff_json.get('aux2_qty')
            # 自由
            else:
                line.difference = line.qty - line.sys_qty
                line.difference_auxiliary1_qty = line.sheet_auxiliary1_qty - line.system_auxiliary1_qty
                line.difference_auxiliary2_qty = line.sheet_auxiliary2_qty - line.system_auxiliary2_qty


class InheritRokeMesStockInventorySheetLine(models.Model):
    _inherit = "roke.mes.stock.inventory.sheet.line"

    system_auxiliary1_qty = fields.Float(string='系统辅1数量', store=True, digits='KCSL')
    system_auxiliary2_qty = fields.Float(string='系统辅2数量', store=True, digits='KCSL')
    system_auxiliary_json = fields.Char(string="系统数量")
    sheet_auxiliary1_qty = fields.Float(string='盘点辅1数量', store=True, digits='KCSL')
    sheet_auxiliary2_qty = fields.Float(string='盘点辅2数量', store=True, digits='KCSL')
    sheet_auxiliary_json = fields.Char(string="盘点数量")
    difference_auxiliary1_qty = fields.Float(string='差额辅1数量', store=True, digits='KCSL')
    difference_auxiliary2_qty = fields.Float(string='差额辅2数量', store=True, digits='KCSL')
    difference_auxiliary_json = fields.Char(string="差额数量")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    # is_balance_calculation = fields.Boolean(string="是否取余计算", related="product_id.is_balance_calculation")

    @api.onchange('sheet_auxiliary1_qty', 'sheet_auxiliary2_qty')
    def _onchange_aux_qty(self):
        if self.sheet_auxiliary1_qty < 0 or self.sheet_auxiliary2_qty:
            raise ValidationError(_("盘点数量不能为负数"))

    def _get_quants_value(self, quants):
        res = super(InheritRokeMesStockInventorySheetLine, self)._get_quants_value(quants)
        res.update({
            'system_auxiliary1_qty': sum(quants.mapped('auxiliary1_qty')),
            'system_auxiliary2_qty': sum(quants.mapped('auxiliary2_qty')),
        })
        return res

    @api.depends("sys_qty", "system_auxiliary1_qty", "system_auxiliary2_qty", "qty", "sheet_auxiliary1_qty",
                 "sheet_auxiliary2_qty")
    def _compute_difference(self):
        for record in self:
            record.difference = record.qty - record.sys_qty
            record.difference_auxiliary1_qty = record.sheet_auxiliary1_qty - record.system_auxiliary1_qty
            record.difference_auxiliary2_qty = record.sheet_auxiliary2_qty - record.system_auxiliary2_qty
