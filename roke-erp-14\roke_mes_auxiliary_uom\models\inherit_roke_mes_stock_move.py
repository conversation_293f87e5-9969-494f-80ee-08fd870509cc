#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2022-09-28 10:04
# <AUTHOR> 陈常磊
# @Site    :
# @File    : inherit_roke_mes_stock_move.py
# @Software: PyCharm

from odoo import models, fields, api, _
import math
import json
from odoo.exceptions import UserError
def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRokeMesStockMove(models.Model):
    _inherit = "roke.mes.stock.move"

    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    finish_auxiliary1_qty = fields.Float(string="完成辅助数量1", digits='KCSL',
                                         compute="_compute_finish_auxiliary_qty")
    finish_auxiliary2_qty = fields.Float(string="完成辅助数量2", digits='KCSL',
                                         compute="_compute_finish_auxiliary_qty")
    finish_auxiliary_json = fields.Char(string="完成数量")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.depends("line_ids", "line_ids.qty")
    def _compute_finish_auxiliary_qty(self):
        for record in self:
            line_auxiliary1_qty = sum(record.line_ids.mapped("auxiliary1_qty"))
            line_auxiliary2_qty = sum(record.line_ids.mapped("auxiliary2_qty"))
            record.finish_auxiliary1_qty = line_auxiliary1_qty
            record.finish_auxiliary2_qty = line_auxiliary2_qty

    def generate_move_line_dict(self, qty, quant_id, src_location_id, lot_id, product_state_id, auxiliary1_qty,
                                auxiliary2_qty):
        # 红单入库
        if self.picking_id.type == '入库':
            quant = False
            location_id = self.src_location_id.id
        else:
            quant = quant_id
            location_id = src_location_id
        # 根据产品和辅一数量和主数量计算json
        return {
            "move_id": self.id, "state": self.state, "product_id": self.product_id.id,
            "move_date": self.move_date, "picking_id": self.picking_id.id,
            "recipient_id": self.recipient_id.id,
            "src_location_id": location_id, "dest_location_id": self.dest_location_id.id,
            "auxiliary1_qty": auxiliary1_qty,
            "auxiliary2_qty": auxiliary2_qty,
            "qty": qty, "lot_id": lot_id, "reserved_quant_id": quant,
            "product_state_id": product_state_id
        }

    def _action_assign(self):
        """
        库存预占
        点击占用，自动获取有库存的quant,生成move line
        :return:
        """
        self.ensure_one()
        self.line_ids.unlink()

        move_line_model = self.env["roke.mes.stock.move.line"]
        stock_quant_model = self.env["roke.mes.stock.quant"]
        stock_negative_inventory = self.env['ir.config_parameter'].sudo().get_param(
            'stock.negative.inventory', default="允许"
        )
        inventory_picking_strict = self.env['ir.config_parameter'].sudo().get_param(
            'inventory.picking.strict'
        )
        # 获取给定位置及其子位置
        if self.picking_id.is_red_order and self.picking_id.type == '入库':
            src_location_id = self.dest_location_id
        else:
            src_location_id = self.src_location_id
        location_ids = self.get_locations(src_location_id)

        ml_dicts = []
        needs_qty = self.qty
        needs_auxiliary1_qty = self.auxiliary1_qty
        needs_auxiliary2_qty = self.auxiliary2_qty
        # is_free_conversion = True if self.product_id.uom_type == '多计量' and self.product_id.uom_groups_id.is_free_conversion else False
        # if not is_free_conversion:
        if stock_negative_inventory == "允许":
            create_dict = {
                'product_id': self.product_id.id,
                'location_id': self.src_location_id.id,
                "quant_date": fields.Date.context_today(self)
            }
            if self.lot_id:
                create_dict.update({'lot_id': self.lot_id.id if self.lot_id else False})
                record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                               locations=location_ids,
                                                                               lots=self.lot_id, limit=1)
                if not record:
                    record = stock_quant_model.get_quants(self.product_id, self.src_location_id, self.lot_id)
                ml_dicts.append(
                    self.generate_move_line_dict(
                        needs_qty, record.id, record.location_id.id, record.lot_id.id, record.product_state_id.id,
                        needs_auxiliary1_qty, needs_auxiliary2_qty
                    )
                )
            else:
                # 如果没有提供批次号，有以下两种情况：
                if self.product_id.track_type != "none":  # (2 该产品管理批次，但没有提供批次。需要通过业务伙伴查询批次，自动提供批次。
                    if inventory_picking_strict:  # 使用业务伙伴匹配批次
                        record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                       locations=location_ids,
                                                                                       limit=1)
                        record = record.filtered(lambda quant: quant.lot_id.partner_id.id == self.picking_id.partner_id.id)  # 添加过滤条件
                        if record:
                            ml_dicts.append(
                                self.generate_move_line_dict(
                                    needs_qty, record.id, record.location_id.id, record.lot_id.id,
                                    record.product_state_id.id,
                                    needs_auxiliary1_qty, needs_auxiliary2_qty
                                )
                            )
                    else:
                        record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                       locations=location_ids,
                                                                                       limit=1)
                        record = record.filtered(lambda quant: quant.lot_id != False)  # 添加过滤条件
                        if record:
                            ml_dicts.append(
                                self.generate_move_line_dict(
                                    needs_qty, record.id, record.location_id.id, record.lot_id.id,
                                    record.product_state_id.id,
                                    needs_auxiliary1_qty, needs_auxiliary2_qty
                                )
                            )
                else:  # (1 该产品不管理批次
                    record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                   locations=location_ids,
                                                                                   limit=1)
                    if not record:
                        record = stock_quant_model.get_quants(self.product_id, self.src_location_id, self.lot_id)
                    ml_dicts.append(
                        self.generate_move_line_dict(
                            needs_qty, record.id, record.location_id.id, record.lot_id.id,
                            record.product_state_id.id,
                            needs_auxiliary1_qty, needs_auxiliary2_qty
                        )
                    )
        else:
            if self.lot_id:
                record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                               locations=location_ids,
                                                                               lots=self.lot_id)
                records = record.filtered(lambda quant: quant.inventory_quantity > 0.00)  # 添加过滤条件
            else:
                # 如果没有提供批次号，有以下两种情况：
                if self.product_id.track_type != "none":  # (2 该产品管理批次，但没有提供批次。需要通过业务伙伴查询批次，自动提供批次。
                    if inventory_picking_strict:  # 使用业务伙伴匹配批次
                        record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                       locations=location_ids)
                        records = record.filtered(lambda quant: quant.inventory_quantity > 0.00)  # 添加过滤条件
                        records = records.filtered(
                            lambda quant: quant.lot_id.partner_id.id == self.picking_id.partner_id.id)
                    else:
                        record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                       locations=location_ids)
                        records = record.filtered(lambda quant: quant.inventory_quantity > 0.00)  # 添加过滤条件
                        records = records.filtered(lambda quant: quant.lot_id != False)  # 添加过滤条件

                else:  # (1 该产品不管理批次
                    record = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                   locations=location_ids)
                    records = record.filtered(lambda quant: quant.inventory_quantity > 0.00)  # 添加过滤条件

            # if not self.picking_id.is_red_order:  # 蓝单过滤出可用库存 > 0 的
            #     records = records.filter(lambda r: r.inventory_quantity > 0.00)

            for record in records:
                if math.fabs(needs_qty) <= 0:  # 判断是否还有需求数（红单情况下，需求数是负数，所以取绝对值）
                    break

                if self.picking_id.is_red_order:
                    assign_qty = -1.00 * min(record.inventory_quantity, math.fabs(needs_qty))
                    assign_auxiliary1_qty = -1 * min(record.inventory_auxiliary1_qty,
                                                     math.fabs(needs_auxiliary1_qty))
                    assign_auxiliary2_qty = -1 * min(record.inventory_auxiliary2_qty,
                                                     math.fabs(needs_auxiliary2_qty))
                else:
                    assign_qty = min(record.inventory_quantity, needs_qty)
                    assign_auxiliary1_qty = min(record.inventory_auxiliary1_qty, needs_auxiliary1_qty)
                    assign_auxiliary2_qty = min(record.inventory_auxiliary2_qty, needs_auxiliary2_qty)

                needs_qty -= assign_qty
                needs_auxiliary1_qty -= assign_auxiliary1_qty
                needs_auxiliary2_qty -= assign_auxiliary2_qty
                ml_dicts.append(
                    self.generate_move_line_dict(
                        assign_qty, record.id, record.location_id.id, record.lot_id.id, record.product_state_id.id,
                        assign_auxiliary1_qty, assign_auxiliary2_qty
                    )
                )

        if ml_dicts:
            self.is_show_button = False

        # 处理预留显示
        sum_qty = sum([i['qty'] for i in ml_dicts])

        if math.fabs(sum_qty) < math.fabs(self.qty):
            self.is_done = False
        # 如果当前产品是取余计算，更新json数据
        product_obj = self.env['roke.product']
        for ml in ml_dicts:
            prouduct_id = product_obj.browse(ml.get('product_id', False))
            aux11_qty = ml.get('auxiliary1_qty', 0)
            if self.picking_id.is_red_order:
                coefficient = -1
            else:
                coefficient = 1
        move_line_model.create(ml_dicts)

    # def get_aux_auxiliary_json(self, product_id, aux1_qty, coefficient):
    #     """
    #     取余计算，更新json数据
    #     :param line.product_id:
    #     :param aux1_qty:
    #     :param aux2_qty:
    #     """
    #     product_uom_line1 = product_id.uom_groups_id.uom_line_ids.filtered(
    #         lambda a: a.uom_id.id == product_id.auxiliary_uom1_id.id)
    #     json_main_qty = 0
    #     json_aux1_qty = 0
    #     if product_uom_line1 and product_uom_line1.conversion:
    #         json_aux1_qty = abs(aux1_qty) / product_uom_line1.conversion
    #         floor_aux1_qty = math.floor(json_aux1_qty)
    #         if floor_aux1_qty > 0:
    #             json_main_qty += math.floor(json_aux1_qty)
    #             json_aux1_qty = abs(aux1_qty) - (json_main_qty * product_uom_line1.conversion)
    #         else:
    #             json_aux1_qty = abs(aux1_qty)
    #     balance_calculation_auxiliary_json = {'main_qty': coefficient * round(json_main_qty, _get_pd(self.env)),
    #                                           'aux1_qty': coefficient * round(json_aux1_qty, _get_pd(self.env))}
    #     return balance_calculation_auxiliary_json

    def _get_show_details_lines_vals(self, ml):
        result = super(InheritRokeMesStockMove, self)._get_show_details_lines_vals(ml)
        result.update({
            'auxiliary1_qty': ml.auxiliary1_qty,
            'auxiliary2_qty': ml.auxiliary2_qty,
        })
        return result

    def action_done(self):
        res = super(InheritRokeMesStockMove, self).action_done()
        Quant = self.env["roke.mes.stock.quant"]
        stock_negative_inventory = self.env['ir.config_parameter'].sudo().get_param(
            'stock.negative.inventory', default="允许"
        )
        for record in self:
            for line in record.line_ids:
                if line.src_location_id.location_type in ["内部位置", "中转位置"]:
                    quant_id = self.env['roke.mes.stock.quant'].sudo().search_quants(
                        products=line.product_id, locations=line.src_location_id, lots=line.lot_id, limit=1)
                    if stock_negative_inventory == "禁止" and (
                            quant_id.inventory_auxiliary1_qty < 0 or quant_id.inventory_auxiliary2_qty < 0):
                        raise UserError("产品【%s】该批次号或该状态所选数量过大,不允许负库存调拨" % line.product_id.name)
        return res
