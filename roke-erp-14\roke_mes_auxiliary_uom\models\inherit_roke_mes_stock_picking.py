# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
import json
import math


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRokeMesStockPicking(models.Model):
    _inherit = "roke.mes.stock.picking"

    def prepare_move_dict(self, line):
        result = super(InheritRokeMesStockPicking, self).prepare_move_dict(line)
        if self.sale_id.sale_type == '退货':
            aux1_qty = -1 * (line.auxiliary1_qty - abs(line.deliver_auxiliary1_qty)) if line.auxiliary1_qty - abs(
                line.deliver_auxiliary1_qty) > 0 else 0
            aux2_qty = -1 * (line.auxiliary2_qty - abs(line.deliver_auxiliary2_qty)) if line.auxiliary2_qty - abs(
                line.deliver_auxiliary2_qty) > 0 else 0
        else:
            qty = -1 * line.deliver_qty if line.deliver_qty > 0 else 0
            aux1_qty = -1 * (line.deliver_auxiliary1_qty) if line.deliver_auxiliary1_qty > 0 else 0
            aux2_qty = -1 * (line.deliver_auxiliary2_qty) if line.deliver_auxiliary2_qty > 0 else 0
        result.update({'auxiliary1_qty': aux1_qty, 'auxiliary2_qty': aux2_qty})
        return result

    def prepare_move_line_dict(self, move, stock_lot_id=None):
        result = super(InheritRokeMesStockPicking, self).prepare_move_line_dict(move, stock_lot_id)
        # 跟踪单一批次号的情况
        if move.product_id.track_type == 'unique':
            product_uom_line1 = move.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == move.product_id.auxiliary_uom1_id.id)
            product_uom_line2 = move.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == move.product_id.auxiliary_uom2_id.id)
            # 三种换算关系---1、取余  2、自由  3、非自由-非取余
            # 非自由
            if not move.product_id.uom_groups_id.is_free_conversion:
                auxiliary1_qty = product_uom_line1.conversion if product_uom_line1 else 0
                auxiliary2_qty = product_uom_line2.conversion if product_uom_line2 else 0
            # 自由
            else:
                auxiliary1_qty = move.auxiliary1_qty / move.qty
                auxiliary2_qty = move.auxiliary2_qty / move.qty
        # 不是跟踪单一批次
        else:
            # 三种换算关系---1、取余  2、自由  3、非自由-非取余
            auxiliary1_qty = move.auxiliary1_qty
            auxiliary2_qty = move.auxiliary2_qty
        result.update({
            "auxiliary1_qty": auxiliary1_qty,
            "auxiliary2_qty": auxiliary2_qty})
        return result

    def prepare_purchase_move_dict(self, line):
        result = super(InheritRokeMesStockPicking, self).prepare_purchase_move_dict(line)
        if self.purchase_id.purchase_type == '退货':
            aux1_qty = -1 * (line.auxiliary1_qty - abs(line.receiving_auxiliary1_qty)) if line.auxiliary1_qty - abs(
                line.receiving_auxiliary1_qty) > 0 else 0
            aux2_qty = -1 * (line.auxiliary2_qty - abs(line.receiving_auxiliary2_qty)) if line.auxiliary2_qty - abs(
                line.receiving_auxiliary2_qty) > 0 else 0
        else:
            aux1_qty = -1 * (line.receiving_auxiliary1_qty) if line.receiving_auxiliary1_qty > 0 else 0
            aux2_qty = -1 * (line.receiving_auxiliary2_qty) if line.receiving_auxiliary2_qty > 0 else 0
        result.update({'auxiliary1_qty': aux1_qty, 'auxiliary2_qty': aux2_qty})
        return result

    def create_production_result_backorder(self):
        """创建产成品入库的欠单"""
        picking_type = self.picking_type_id
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_values = []
        result_move_obj = self.env["roke.production.result.move"]
        material_move_line_obj = self.env["roke.material.move.line"]
        backorder_move_lines = self.move_line_ids.filtered(lambda ml: ml.qty > ml.finish_qty)
        for stock_move in backorder_move_lines:
            qty = stock_move.qty - stock_move.finish_qty
            # 创建入库明细
            result_move = result_move_obj.create({
                "location_id": dest_location.id,
                "result_id": stock_move.result_move_id.result_id.id,
                "qty": qty
            })
            # 创建车间物料移动明细
            material_move_id = material_move_line_obj.create({
                "product_result_move_id": result_move.id,
                "product_id": stock_move.product_id.id,
                "process_id": stock_move.result_material_move_id.process_id.id,
                "src_process_id": stock_move.result_material_move_id.src_process_id.id,
                "move_date": fields.Date.context_today(self),
                "stock_qty": -qty
            })
            # 辅计量单位
            auxiliary1_qty, auxiliary2_qty = stock_move.auxiliary1_qty, stock_move.auxiliary2_qty
            if not stock_move.product_id.is_free_conversion:
                # 计算辅数量1
                product_uom1_line = stock_move.product_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == stock_move.product_id.auxiliary_uom1_id.id)
                if not product_uom1_line:
                    auxiliary1_qty = 0
                else:
                    auxiliary1_qty = stock_move.finish_qty * product_uom1_line.conversion

                # 计算辅数量2
                product_uom2_line = stock_move.product_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == stock_move.product_id.auxiliary_uom2_id.id)
                if not product_uom2_line:
                    auxiliary2_qty = 0
                else:
                    auxiliary2_qty = stock_move.finish_qty * product_uom2_line.conversion
            picking_line_values.append((0, 0, {
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "product_id": stock_move.result_move_id.product_id.id,
                "result_move_id": result_move.id,
                "result_material_move_id": material_move_id.id,
                "qty": qty,
                "auxiliary1_qty": auxiliary1_qty,
                "auxiliary2_qty": auxiliary2_qty,
                "line_ids": [(0, 0, {
                    "qty": qty,
                    "lot_id": self.get_lot_id(stock_move)
                })]
            }))  # 创建入库调拨

        new_picking = self.env["roke.mes.stock.picking"].create({
            "backorder_id": self.id,
            "state": "确认",
            "picking_type_id": picking_type.id,
            "src_location_id": src_location.id,
            "dest_location_id": dest_location.id,
            "production_result_ids": [(6, 0, backorder_move_lines.result_move_id.result_id.ids)],
            "move_line_ids": picking_line_values
        })
        return new_picking

    def picking_create_backorder(self):
        """
        创建欠单
        :return:
        """
        if self.production_result_ids:
            # 产成品入库的欠单，因为要与车间物料同步，所以单独处理
            new_picking = self.create_production_result_backorder()
        else:
            new_picking = self.copy({
                "backorder_id": self.id
            })
            for line in self.move_line_ids:
                auxiliary1_qty, auxiliary2_qty = line.auxiliary1_qty, line.auxiliary2_qty
                if not line.product_id.is_free_conversion:
                    # 计算辅数量1
                    product_uom1_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
                        lambda a: a.uom_id.id == line.product_id.auxiliary_uom1_id.id)
                    if not product_uom1_line:
                        auxiliary1_qty = 0
                    else:
                        auxiliary1_qty = line.finish_qty * product_uom1_line.conversion
                    # 计算辅数量2
                    product_uom2_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
                        lambda a: a.uom_id.id == line.product_id.auxiliary_uom2_id.id)
                    if not product_uom2_line:
                        auxiliary2_qty = 0
                    else:
                        auxiliary2_qty = line.finish_qty * product_uom2_line.conversion

                if line.qty > line.finish_qty:
                    line.copy({
                        "picking_id": new_picking.id,
                        "qty": line.qty - line.finish_qty,
                        "finish_qty": 0,
                        "auxiliary1_qty": line.auxiliary1_qty - auxiliary1_qty,
                        "auxiliary2_qty": line.auxiliary2_qty - auxiliary2_qty
                    })
                line.write({
                    "auxiliary1_qty": auxiliary1_qty,
                    "auxiliary2_qty": auxiliary2_qty
                })
        self.message_post_with_view(
            'roke_mes_stock.roke_mes_backorder_message_origin_link',
            values={'self': self, 'origin': new_picking},
            subtype_id=self.env.ref('mail.mt_note').id
        )
        self.make_finish()  # 完成操作

    # 更新调拨明细
    def picking_update_backorder(self):
        for line in self.move_line_ids:
            auxiliary1_qty, auxiliary2_qty = line.auxiliary1_qty, line.auxiliary2_qty
            if not line.product_id.uom_groups_id.is_free_conversion:
                # 计算辅数量1
                product_uom1_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == line.product_id.auxiliary_uom1_id.id)
                if not product_uom1_line:
                    auxiliary1_qty = 0
                else:
                    auxiliary1_qty = line.finish_auxiliary1_qty

                # 计算辅数量2
                product_uom2_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == line.product_id.auxiliary_uom2_id.id)
                if not product_uom2_line:
                    auxiliary2_qty = 0
                else:
                    auxiliary2_qty = line.finish_auxiliary2_qty
            line.write({
                "auxiliary1_qty": auxiliary1_qty,
                "auxiliary2_qty": auxiliary2_qty
            })
