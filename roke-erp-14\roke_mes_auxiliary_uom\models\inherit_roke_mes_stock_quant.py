#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2022-09-28 00:20
# <AUTHOR> 陈常磊
# @Site    :
# @File    : inherit_roke_mes_stock_quant.py
# @Software: PyCharm

from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import logging
_logger = logging.getLogger(__name__)


class InheritRokeMesStockQuant(models.Model):
    _inherit = "roke.mes.stock.quant"

    auxiliary1_qty = fields.Float(string="辅助数量1", compute='_compute_quantity', store=True, digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", compute='_compute_quantity', store=True, digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")

    reserved_auxiliary1_qty = fields.Float(string='已占用辅数量1', compute='_compute_quantity', store=True,
                                           digits='KCSL')
    reserved_auxiliary2_qty = fields.Float(string='已占用辅数量2', compute='_compute_quantity', store=True,
                                           digits='KCSL')

    inventory_auxiliary1_qty = fields.Float(string='可用库存辅数量1', compute='_compute_quantity', store=True,
                                            digits='KCSL')
    inventory_auxiliary2_qty = fields.Float(string='可用库存辅数量2', compute='_compute_quantity', store=True,
                                            digits='KCSL')
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    # auxiliary_quant_ids = fields.One2many("roke.stock.auxiliary.quant", "quant_id", string="多计量库存")

    @api.depends("in_move_line_ids", "out_move_line_ids", "in_move_line_ids.qty", "out_move_line_ids.qty",
                 "reserved_ids.qty", "in_move_line_ids.state", "out_move_line_ids.state")
    def _compute_quantity(self):
        for record in self:
            qty = sum(record.in_move_line_ids.mapped("qty")) - sum(record.out_move_line_ids.mapped("qty"))
            auxiliary1_qty = sum(record.in_move_line_ids.mapped("auxiliary1_qty")) - sum(
                record.out_move_line_ids.mapped("auxiliary1_qty"))
            auxiliary2_qty = sum(record.in_move_line_ids.mapped("auxiliary2_qty")) - sum(
                record.out_move_line_ids.mapped("auxiliary2_qty"))

            # reserved_quantity = sum(record.reserved_ids.mapped("qty"))
            reserved_quantity = sum(record.reserved_ids.filtered(lambda r: r.qty > 0).mapped("qty"))
            reserved_auxiliary1_qty = sum(record.reserved_ids.mapped("auxiliary1_qty"))
            reserved_auxiliary2_qty = sum(record.reserved_ids.mapped("auxiliary2_qty"))
            inventory_quantity = qty - reserved_quantity
            inventory_auxiliary1_qty = auxiliary1_qty - reserved_auxiliary1_qty
            inventory_auxiliary2_qty = auxiliary2_qty - reserved_auxiliary2_qty
            # if record.location_type == '内部位置':
            #     if qty < 0:
            #         raise UserError('库存数量小于0，不可操作。【%s】' % qty)
            record.qty = qty
            record.auxiliary1_qty = auxiliary1_qty
            record.auxiliary2_qty = auxiliary2_qty
            record.reserved_quantity = reserved_quantity
            record.reserved_auxiliary1_qty = reserved_auxiliary1_qty
            record.reserved_auxiliary2_qty = reserved_auxiliary2_qty

            record.inventory_quantity = inventory_quantity
            record.inventory_auxiliary1_qty = inventory_auxiliary1_qty
            record.inventory_auxiliary2_qty = inventory_auxiliary2_qty

#             # 先删除子表再重新创建
#             record.auxiliary_quant_ids.unlink()
#
#             # 存主数量--多计量仓库库存
#             self.env["roke.stock.auxiliary.quant"].create({
#                 "quant_id": record.id,
#                 "product_id": record.product_id.id,
#                 "location_id": record.location_id.id,
#                 "uom_id": record.product_id.uom_id.id,
#                 "qty": qty,
#                 "lot_id": record.lot_id.id,
#                 "quant_date": record.quant_date,
#             })
#             # 存辅数量1--多计量仓库库存
#             if record.product_id.auxiliary_uom1_id:
#                 self.env["roke.stock.auxiliary.quant"].create({
#                     "quant_id": record.id,
#                     "product_id": record.product_id.id,
#                     "location_id": record.location_id.id,
#                     "uom_id": record.product_id.auxiliary_uom1_id.id,
#                     "qty": auxiliary1_qty,
#                     "lot_id": record.lot_id.id,
#                     "quant_date": record.quant_date,
#                 })
#             # 存辅数量2--多计量仓库库存
#             if record.product_id.auxiliary_uom2_id:
#                 self.env["roke.stock.auxiliary.quant"].create({
#                     "quant_id": record.id,
#                     "product_id": record.product_id.id,
#                     "location_id": record.location_id.id,
#                     "uom_id": record.product_id.auxiliary_uom2_id.id,
#                     "qty": auxiliary2_qty,
#                     "lot_id": record.lot_id.id,
#                     "quant_date": record.quant_date,
#                 })
#
# class RokeStockAuxiliaryQuant(models.Model):
#     _name = "roke.stock.auxiliary.quant"
#     _description = "多计量仓库库存"
#     _rec_name = "product_id"
#
#     quant_id = fields.Many2one("roke.mes.stock.quant", string="仓库库存", ondelete='cascade')
#     product_id = fields.Many2one("roke.product", string="产品")
#     location_id = fields.Many2one("roke.mes.stock.location", index=True, string="位置", ondelete='restrict')
#     location_type = fields.Selection(related="location_id.location_type", index=True, store=True, string="位置类型")
#     qty = fields.Float(string="数量", digits='Stock')
#     uom_id = fields.Many2one("roke.uom", string="单位")
#     lot_id = fields.Many2one("roke.mes.stock.lot", index=True, string="单件/批次号码", ondelete='restrict')
#     reserved_quantity = fields.Float(string='已占用数量', digits='Stock')
#     inventory_quantity = fields.Float(string='可用库存', digits='Stock')
#     quant_date = fields.Date(string='日期')



