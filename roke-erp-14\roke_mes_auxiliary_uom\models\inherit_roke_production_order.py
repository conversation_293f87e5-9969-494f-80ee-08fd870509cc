# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime, timedelta
import math
import json


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRokeProductionOrderLineUom(models.Model):
    _inherit = "roke.production.order.line"

    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Bo<PERSON>an(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 计划数量
    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='SCSL')
    # 完工数
    finish_auxiliary_json = fields.Char(string="完工数量")
    finish_auxiliary1_qty = fields.Float(string="完工辅助数量1", digits='SCSL', compute="_compute_finish_qty")
    finish_auxiliary2_qty = fields.Float(string="完工辅助数量2", digits='SCSL', compute="_compute_finish_qty")

    @api.depends("task_ids", "task_ids.finish_qty", "task_ids.state")
    def _compute_finish_qty(self):
        """
            处理完成数量
            :return:
        """
        for record in self:
            task_list = record.get_pol_first_tasks()
            finish_qty = sum(task_list.mapped("finish_qty"))
            record.finish_qty = finish_qty
            # 处理辅数量
            finish_auxiliary1_qty = sum(task_list.mapped("finish_auxiliary1_qty"))
            finish_auxiliary2_qty = sum(task_list.mapped("finish_auxiliary2_qty"))
            record.finish_auxiliary1_qty = finish_auxiliary1_qty
            record.finish_auxiliary2_qty = finish_auxiliary2_qty
            state = "未完工"
            if record.task_ids:
                if not record.task_ids.filtered(lambda pt: pt.state == "未完工"):
                    if task_list.filtered(lambda pt: pt.state == "强制完工"):
                        # 有强制完工的任务，则当前明细状态为强制完工
                        state = "强制完工"
                    elif task_list.filtered(lambda pt: pt.state == "暂停"):
                        # 有暂停的任务，则当前明细状态为暂停
                        state = "暂停"
                    else:
                        state = "已完工"
            record.state = state

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def pol_create_material_purchase(self):
        """
        销售订单行创建物料采购订单
        :return:
        """
        wizard_line_vals = []
        wizard_material_vals = []
        multi_purchase_materials = []
        purchase_materials_list = []

        for record in self:
            if not record.e_bom_id:
                continue
            purchase_materials = record.e_bom_id.get_can_purchase_material(record.qty, 1)
            purchase_materials_list += purchase_materials
        # 将需要购买的明细合并
        # 获取所有物料
        material_list = [pur_line.get('material', False) for pur_line in purchase_materials_list]
        set_material_list = list(set(material_list)) if material_list else []
        for mate_line in set_material_list:
            material_qty = 0
            material_auxiliary1_qty = 0
            material_auxiliary2_qty = 0
            for pur_line in purchase_materials_list:
                if pur_line.get('material', False) == mate_line:
                    # 通过辅计量数量换算一下数量
                    qty_dict = self._get_aux_qty_dict(pur_line)
                    qty = qty_dict.get("main_qty", 0)
                    auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
                    auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
                    material_qty += qty
                    material_auxiliary1_qty += auxiliary1_qty
                    material_auxiliary2_qty += auxiliary2_qty

            multi_purchase_materials.append({
                'material': mate_line,
                'qty': material_qty,
                'aux1_qty': material_auxiliary1_qty,
                'aux2_qty': material_auxiliary2_qty,
            })
        # 获取sale_ids
        production_ids = [line.order_id.id for line in self]
        # 获取采购明细
        for purchase_material in multi_purchase_materials:
            supplier_id = False
            unit_price = 1
            material = purchase_material.get("material", False)
            qty = purchase_material.get("qty", 0)
            aux1_qty = purchase_material.get("aux1_qty", 0)
            aux2_qty = purchase_material.get("aux2_qty", 0)
            # 根据物料获取已生成的采购订单明细已购买了多少
            purchased_qty, purchased_aux1_qty, purchased_aux2_qty = self.get_purchased_qty(material,
                                                                                           list(set(production_ids)))
            # 根据物料单位类型判断需要辅计量需要购买多少
            # 非自由非取余
            purchase_qty, purchase_aux1_qty, purchase_aux2_qty = 0, 0, 0
            if not material.is_free_conversion:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
                purchase_aux1_qty = aux1_qty - purchased_aux1_qty if aux1_qty - purchased_aux1_qty > 0 else 0
                purchase_aux2_qty = aux2_qty - purchased_aux2_qty if aux2_qty - purchased_aux2_qty > 0 else 0
            # 自由
            else:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
            if purchase_qty and material and material.supplier_price_ids:
                # 获取供应商价格配置
                supplier_price = material.supplier_price_ids[0]
                supplier_id = supplier_price.supplier_id.id
                unit_price = supplier_price.purchase_price
                purchase_qty = supplier_price.min_purchase_num if purchase_qty < supplier_price.min_purchase_num else purchase_qty
            if purchase_qty > 0:
                wizard_line_vals.append((0, 0, {
                    # "sol": record.id,
                    "material_id": material.id,
                    "demand_qty": qty,
                    "demand_auxiliary1_qty": aux1_qty,
                    "demand_auxiliary2_qty": aux2_qty,
                    "supplier_id": supplier_id,
                    "purchased_qty": purchased_qty,
                    "unit_price": unit_price,
                    "qty": purchase_qty,
                    "auxiliary1_qty": purchase_aux1_qty,
                    "auxiliary2_qty": purchase_aux2_qty,
                    "purchased_aux1_qty": purchased_aux1_qty,
                    "purchased_aux2_qty": purchased_aux2_qty
                }))
        for pur_material in purchase_materials_list:
            # 获取详细物料明细
            qty_dict = self._get_aux_qty_dict(pur_material)
            parent_material = pur_material.get("parent_material_id", False)
            qty = qty_dict.get("main_qty", 0)
            demand_auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
            demand_auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
            material = pur_material.get("material", False)
            wizard_material_vals.append((0, 0, {
                "product_id": parent_material.id,
                "material_id": material.id,
                "demand_qty": qty,
                "demand_auxiliary1_qty": demand_auxiliary1_qty,
                "demand_auxiliary2_qty": demand_auxiliary2_qty,
            }))

        if not wizard_line_vals:
            raise ValidationError("没有可采购的原材料/半成品")
        res = self.env["roke.order.create.purchase.wizard"].create({
            "production_order_ids": [(6, 0, self.order_id.ids)],
            "line_ids": wizard_line_vals,
            "material_details_line_ids": wizard_material_vals
        })
        return {
            'name': '批量创建原材料采购订单',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.order.create.purchase.wizard'
        }

    def get_purchased_qty(self, material, production_ids):
        # 获取已生成的采购订单明细已购买了多少
        purchased_qty = 0
        purchased_aux1_qty = 0
        purchased_aux2_qty = 0
        purchase_order_obj = self.env['roke.purchase.order']
        production_purchase_order_records = purchase_order_obj.search([('production_order_ids', '!=', False)])
        purchase_order_list = [rec for rec in production_purchase_order_records if
                               rec.production_order_ids and list(
                                   set(rec.production_order_ids.ids) & set(production_ids))]

        requisition_order_obj = self.env['roke.purchase.requisition']
        production_requisition_order_records = requisition_order_obj.search(
            [('origin_production_order_ids', '!=', False)])
        requisition_order_list = [rec for rec in production_requisition_order_records if
                                  rec.origin_production_order_ids and list(
                                      set(rec.origin_production_order_ids.ids) & set(production_ids))]
        # 采购订单
        for rec in purchase_order_list:
            for line in rec.detail_ids:
                if line.product_id.id == material.id:
                    purchased_qty += line.qty
                    purchased_aux1_qty += line.auxiliary1_qty
                    purchased_aux2_qty += line.auxiliary2_qty
        # 请购单
        for rec in requisition_order_list:
            for line in rec.detail_ids:
                if line.product_id.id == material.id:
                    purchased_qty += line.demand_qty
                    purchased_aux1_qty += line.auxiliary1_qty
                    purchased_aux2_qty += line.auxiliary2_qty
        return purchased_qty, purchased_aux1_qty, purchased_aux2_qty

    def _get_aux_qty_dict(self, purchase_material):
        """
        通过辅计量计算各个数量
        """
        product = purchase_material.get('material', False)
        qty = purchase_material.get('qty', 0)
        aux1_qty = purchase_material.get('auxiliary1_qty', 0)
        aux2_qty = purchase_material.get('auxiliary2_qty', 0)
        qty_dict = self.env['roke.uom.groups'].get_aux_qty(product, qty, aux1_qty, aux2_qty)
        return qty_dict

class InheritRokeOrderCreateTaskWizardUom(models.TransientModel):
    _inherit = "roke.order.create.task.wizard"

    def _get_po_create_task_values(self, pol, line, createNum):
        """
        重写获取创建生产任务的值，添加返回值
        """
        res = super(InheritRokeOrderCreateTaskWizardUom, self)._get_po_create_task_values(pol, line, createNum)
        if pol.product_id.is_free_conversion:
            res.update({
                "plan_auxiliary1_qty": pol.auxiliary1_qty,
                "plan_auxiliary2_qty": pol.auxiliary2_qty
            })
        else:
            value = self.env['roke.uom.groups'].main_auxiliary_conversion(pol.product_id, 'main', createNum)
            res.update({
                "plan_auxiliary1_qty": value.get('aux1_qty', 0),
                "plan_auxiliary2_qty": value.get('aux2_qty', 0)
            })
        return res


class InheritRokePOAddPlanQtyWizard(models.TransientModel):
    # 增加计划数
    _inherit = "roke.po.add.plan.qty.wizard"

    def _get_pt_write_vals(self, task, new_qty, line):
        """
        获取生产任务编辑内容
        :return:
        """
        res = super(InheritRokePOAddPlanQtyWizard, self)._get_pt_write_vals(task, new_qty, line)
        plan_aux1_qty = task.plan_auxiliary1_qty + line.add_auxiliary1_qty
        plan_aux2_qty = task.plan_auxiliary2_qty + line.add_auxiliary1_qty
        res.update({
            "plan_auxiliary1_qty": plan_aux1_qty,
            "plan_auxiliary2_qty": plan_aux2_qty
        })
        return res

    def _get_pol_write_vals(self, line):
        """
        获取订单明细编辑内容
        :return:
        """
        res = super(InheritRokePOAddPlanQtyWizard, self)._get_pol_write_vals(line)
        res.update({
            "auxiliary1_qty": line.new_auxiliary1_qty,
            "auxiliary2_qty": line.new_auxiliary2_qty,
        })
        return res


class InheritRokePOAddPlanQtyWizardLine(models.TransientModel):
    # 增加计划数明细
    _inherit = "roke.po.add.plan.qty.wizard.line"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="主计量单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    # 原数量
    origin_auxiliary_json = fields.Char(string="原数量", related="pol_id.auxiliary_json")
    origin_auxiliary1_qty = fields.Float(string="原辅助数量1", digits='SCSL', related="pol_id.auxiliary1_qty")
    origin_auxiliary2_qty = fields.Float(string="原辅助数量2", digits='SCSL', related="pol_id.auxiliary2_qty")
    # 增加数量
    add_auxiliary_json = fields.Char(string="增加数量")
    add_auxiliary1_qty = fields.Float(string="增加辅助数量1", digits='SCSL')
    add_auxiliary2_qty = fields.Float(string="增加辅助数量2", digits='SCSL')
    # 增加后数量
    new_auxiliary_json = fields.Char(string="增加后数量", compute="_compute_new_qty")
    new_auxiliary1_qty = fields.Float(string="增加后辅助数量1", digits='SCSL', compute="_compute_new_qty")
    new_auxiliary2_qty = fields.Float(string="增加后辅助数量2", digits='SCSL', compute="_compute_new_qty")

    @api.onchange("add_qty")
    def _compute_new_qty(self):
        for record in self:
            new_qty = record.origin_qty + record.add_qty
            new_auxiliary1_qty = record.origin_auxiliary1_qty + record.add_auxiliary1_qty
            new_auxiliary2_qty = record.origin_auxiliary2_qty + record.add_auxiliary2_qty
            record.new_qty = new_qty
            record.new_auxiliary1_qty = new_auxiliary1_qty
            record.new_auxiliary2_qty = new_auxiliary2_qty

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.add_qty = 0
            self.add_auxiliary1_qty = 0
            self.add_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('add_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.add_qty)
                self.add_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.add_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('add_auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.add_auxiliary1_qty)
                self.add_qty = qty_json.get('main_qty', 0)
                self.add_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('add_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.add_auxiliary2_qty)
                self.add_qty = qty_json.get('main_qty', 0)
                self.add_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
