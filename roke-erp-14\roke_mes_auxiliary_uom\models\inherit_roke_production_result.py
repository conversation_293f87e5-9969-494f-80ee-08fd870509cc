# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import json


class InheritRokeProductionResult(models.Model):
    _inherit = "roke.production.result"

    # uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Bo<PERSON>an(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    qty_uom_info = fields.Char(string="数量", compute="_compute_qty_uom_info", store=True)
    # 产出数量
    auxiliary_json = fields.Char(string="产出数量")
    auxiliary1_qty = fields.Float(string="产出数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="产出数量2", digits='SCSL')
    # 剩余数量
    residue_json = fields.Char(string="剩余数量", compute="_compute_qty", store=True)
    residue_auxiliary1_qty = fields.Float(string="剩余数量1", compute="_compute_qty", digits='SCSL', store=True)
    residue_auxiliary2_qty = fields.Float(string="剩余数量2", compute="_compute_qty", digits='SCSL', store=True)
    residue_uom_info = fields.Char(string="剩余数量", compute="_compute_residue_uom_info", store=True)
    # 移动数量
    move_json = fields.Char(string="移动数量", compute="_compute_qty", store=True)
    move_auxiliary1_qty = fields.Float(string="移动数量1", compute="_compute_qty", digits='SCSL', store=True)
    move_auxiliary2_qty = fields.Float(string="移动数量2", compute="_compute_qty", digits='SCSL', store=True)
    move_uom_info = fields.Char(string="剩余数量", compute="_compute_move_uom_info", store=True)

    @api.depends('qty', 'auxiliary1_qty', 'auxiliary2_qty')
    def _compute_qty_uom_info(self):
        for result in self:
            qty_uom_info = str(result.qty) + str(result.uom_id.name)
            if result.product_id and result.product_id.uom_type == '多计量':
                if result.auxiliary_uom1_id:
                    qty_uom_info += str(result.auxiliary1_qty) + str(result.auxiliary_uom1_id.name)
                if result.auxiliary_uom2_id:
                    qty_uom_info += str(result.auxiliary2_qty) + str(result.auxiliary_uom2_id.name)
            result.qty_uom_info = qty_uom_info

    @api.depends('residue', 'residue_auxiliary1_qty', 'residue_auxiliary2_qty')
    def _compute_residue_uom_info(self):
        for result in self:
            residue_uom_info = str(result.residue) + str(result.uom_id.name)
            if result.product_id and result.product_id.uom_type == '多计量':
                if result.auxiliary_uom1_id:
                    residue_uom_info += str(result.residue_auxiliary1_qty) + str(result.auxiliary_uom1_id.name)
                if result.auxiliary_uom2_id:
                    residue_uom_info += str(result.residue_auxiliary2_qty) + str(result.auxiliary_uom2_id.name)
            result.residue_uom_info = residue_uom_info

    @api.depends('move_qty', 'move_auxiliary1_qty', 'move_auxiliary2_qty')
    def _compute_move_uom_info(self):
        for result in self:
            move_uom_info = str(result.move_qty) + str(result.uom_id.name)
            if result.product_id and result.product_id.uom_type == '多计量':
                if result.auxiliary_uom1_id:
                    move_uom_info += str(result.move_auxiliary1_qty) + str(result.auxiliary_uom1_id.name)
                if result.auxiliary_uom2_id:
                    move_uom_info += str(result.move_auxiliary2_qty) + str(result.auxiliary_uom2_id.name)
            result.move_uom_info = move_uom_info

    @api.depends("line_ids", "line_ids.qty")
    def _compute_qty(self):
        UomGroups = self.env['roke.uom.groups']
        for record in self:
            # 移动数量
            move_qty = sum(record.line_ids.mapped("qty"))
            move_auxiliary1_qty = sum(record.line_ids.mapped("auxiliary1_qty"))
            move_auxiliary2_qty = sum(record.line_ids.mapped("auxiliary2_qty"))

            record.move_qty = move_qty
            record.move_auxiliary1_qty = move_auxiliary1_qty
            record.move_auxiliary2_qty = move_auxiliary2_qty
            # 剩余数量
            residue = record.qty - move_qty
            residue_auxiliary1_qty = record.auxiliary1_qty - move_auxiliary1_qty
            residue_auxiliary2_qty = record.auxiliary2_qty - move_auxiliary2_qty

            record.residue = residue
            record.residue_auxiliary1_qty = residue_auxiliary1_qty
            record.residue_auxiliary2_qty = residue_auxiliary2_qty

    def _auto_stock_get_lines(self, result_res):
        """
        自动入库获取入库明细
        :return:
        """
        res = super(InheritRokeProductionResult, self)._auto_stock_get_lines(result_res)
        res.update({
            "stock_auxiliary1_qty": result_res.residue_auxiliary1_qty,
            "stock_auxiliary2_qty": result_res.residue_auxiliary2_qty
        })
        return res

    def generate_pr_stock_move_line_vals(self, dest_location_id, lot_id):
        """
        获取产出物生成时的stock_move_line
        :return:
        """
        res = super(InheritRokeProductionResult, self).generate_pr_stock_move_line_vals(dest_location_id, lot_id)
        res.update({
            "auxiliary1_qty": self.auxiliary1_qty,
            "auxiliary2_qty": self.auxiliary2_qty,
        })
        return res

    def generate_pr_stock_move_vals(self, dest_location, lot_id):
        """
        获取产出物生成时的stock_move
        :return:
        """
        res = super(InheritRokeProductionResult, self).generate_pr_stock_move_vals(dest_location, lot_id)
        res.update({
            "auxiliary1_qty": self.auxiliary1_qty,
            "auxiliary2_qty": self.auxiliary2_qty,
        })
        return res


class InheritRokeProductionResultMove(models.Model):
    _inherit = "roke.production.result.move"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 移动数量
    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="辅数量2", digits='SCSL')

