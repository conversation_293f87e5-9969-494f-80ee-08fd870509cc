# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import json
from .inherit_work_order import InheritWorkOrder as AUX_WO


class InheritRokeProductionTaskUom(models.Model):
    _inherit = "roke.production.task"

    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    plan_uom_info = fields.Char(string="计划数量", compute="_compute_plan_uom_info")
    plan_auxiliary_json = fields.Char(string="计划数量")
    plan_auxiliary1_qty = fields.Float(string="辅助数量1", digits='SCSL')
    plan_auxiliary2_qty = fields.Float(string="辅助数量2", digits='SCSL')
    finish_auxiliary1_qty = fields.Float(string="完成辅助数量1", digits='SCSL')
    finish_auxiliary2_qty = fields.Float(string="完成辅助数量2", digits='SCSL')
    finish_auxiliary_json = fields.Char(string="完成数量")
    is_real_time_calculations = fields.<PERSON><PERSON><PERSON>(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    @api.depends('plan_qty', 'plan_auxiliary1_qty', 'plan_auxiliary2_qty')
    def _compute_plan_uom_info(self):
        for task in self:
            plan_uom_info = str(task.plan_qty) + str(task.uom_id.name)
            if task.product_id and task.product_id.uom_type == '多计量':
                if task.auxiliary_uom1_id:
                    plan_uom_info += str(task.plan_auxiliary1_qty) + str(task.auxiliary_uom1_id.name)
                if task.auxiliary_uom2_id:
                    plan_uom_info += str(task.plan_auxiliary2_qty) + str(task.auxiliary_uom2_id.name)
            task.plan_uom_info = plan_uom_info

    def _get_new_work_order_data(self, routing_line, product, plan_qty, task_type):
        """
        获取工单数据
        :return:
        """
        res = super(InheritRokeProductionTaskUom, self)._get_new_work_order_data(routing_line, product, plan_qty,
                                                                                 task_type)
        # 辅计量计算
        new_data = {}
        if not product.is_free_conversion:
            _res = product.uom_groups_id.main_auxiliary_conversion(product, 'main', res.get('plan_qty'))
            new_data = {
                "plan_auxiliary1_qty": _res.get('aux1_qty'),
                "plan_auxiliary2_qty": _res.get('aux2_qty')
            }
        # 自由换算
        else:
            aux1_qty = self.plan_auxiliary1_qty * routing_line.multiple
            aux2_qty = self.plan_auxiliary2_qty * routing_line.multiple
            _res = product.uom_groups_id.main_auxiliary_conversion(product, 'main', res.get('plan_qty'))
            _res['aux1_qty'] = aux1_qty
            _res['aux2_qty'] = aux2_qty
            new_data = {
                "plan_auxiliary1_qty": aux1_qty,
                "plan_auxiliary2_qty": aux2_qty
            }
        res.update(new_data)
        return res

    def _create_work_order_get_values(self, task, routing_line):
        """
        任务生成工单获取工单数据，添加json返回值
        :return:
        """
        res = super(InheritRokeProductionTaskUom, self)._create_work_order_get_values(task, routing_line)
        if task.product_id.is_free_conversion:
            # 自由换算
            plan_aux1_qty = task.plan_auxiliary1_qty
            plan_aux2_qty = task.plan_auxiliary2_qty
            res.update({
                "plan_auxiliary1_qty": plan_aux1_qty,
                "plan_auxiliary2_qty": plan_aux2_qty
            })
        else:
            value = self.env['roke.uom.groups'].main_auxiliary_conversion(task.product_id, 'main', res.get("plan_qty", 0))
            res.update({
                "plan_auxiliary1_qty": value.get('aux1_qty', 0),
                "plan_auxiliary2_qty": value.get('aux2_qty', 0),
            })
        return res

    def _create_child_wo_get_values(self, task, child_process, main_wo):
        """
        任务生成子工单获取数据，添加辅计量内容
        :return:
        """
        res = super(InheritRokeProductionTaskUom, self)._create_child_wo_get_values(task, child_process, main_wo)
        # 为工单添加json返回值
        data = {
            "plan_auxiliary1_qty": task.plan_auxiliary1_qty,
            "plan_auxiliary2_qty": task.plan_auxiliary2_qty,
        }
        res.update(data)
        return res

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                self.plan_qty = 1
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.plan_qty)
                self.plan_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.plan_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            else:
                self.plan_qty = 1
                self.plan_auxiliary1_qty = 0
                self.plan_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.plan_qty)
                self.plan_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.plan_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.plan_auxiliary1_qty)
                self.plan_qty = qty_json.get('main_qty', 0)
                self.plan_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.plan_auxiliary2_qty)
                self.plan_qty = qty_json.get('main_qty', 0)
                self.plan_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _p_bom_get_pt_demand_vals(self, pt_id, p_bom_line, material_qty, product, first):
        """
        工艺BOM获取任务物料需求
        :return:
        """
        res = super(InheritRokeProductionTaskUom, self)._p_bom_get_pt_demand_vals(pt_id, p_bom_line, material_qty, product, first)
        aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', material_qty)
        res.update({
            "demand_auxiliary1_qty": aux_value.get('aux1_qty', 0),
            "demand_auxiliary2_qty": aux_value.get('aux1_qty', 0)
        })
        return res

    def _e_bom_get_pt_demand_vals(self, pt_id, line, bom, material_qty, first):
        """
        产品BOM获取任务物料需求
        :return:
        """
        res = super(InheritRokeProductionTaskUom, self)._e_bom_get_pt_demand_vals(pt_id, line, bom, material_qty, first)
        aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(line.product_id, 'main', material_qty)
        res.update({
            "demand_auxiliary1_qty": aux_value.get('aux1_qty', 0),
            "demand_auxiliary2_qty": aux_value.get('aux1_qty', 0)
        })
        return res

    def _get_demand_create_picking_line_vals(self, demand):
        """
        获取bom生成调拨单的数据
        """
        res = super(InheritRokeProductionTaskUom, self)._get_demand_create_picking_line_vals(demand)
        aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(demand.material_id, 'main', res.get("qty", 0))
        demand_aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(demand.material_id, 'main', res.get("demand_qty", 0))
        res.update({
            "demand_auxiliary1_qty": demand_aux_value.get("aux1_qty", 0),
            "demand_auxiliary2_qty": demand_aux_value.get("aux2_qty", 0),
            "auxiliary1_qty": aux_value.get("aux1_qty", 0),
            "auxiliary2_qty": aux_value.get("aux2_qty", 0),
        })
        return res

    # 完工数处理：报工、撤回时更新辅计量
    def pt_update_aux_qty(self, finish_qty, finish_auxiliary1_qty, finish_auxiliary2_qty,
                          withdraw=False):
        """
        完工数处理：报工、撤回时更新辅计量：适配自由换算
        :return:
        """
        self.ensure_one()
        write_dict = {}
        if finish_qty:
            write_dict["finish_auxiliary1_qty"] = self.finish_auxiliary1_qty + finish_auxiliary1_qty
            write_dict["finish_auxiliary2_qty"] = self.finish_auxiliary2_qty + finish_auxiliary2_qty or 0
        self.write(write_dict)


