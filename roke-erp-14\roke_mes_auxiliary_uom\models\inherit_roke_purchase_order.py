#!/usr/bin/env python

# -*- coding: utf-8 -*-
# @Time    : 2022-09-28 00:00
# <AUTHOR> 陈常磊
# @Site    :
# @File    : inherit_roke_purchase_order.py
# @Software: PyCharm

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import math


def _get_pd(env, index=False):
    if not index:
        index = 'CGSL'
    return env["decimal.precision"].precision_get(index)


class InheritRokePurchaseOrder(models.Model):
    _inherit = "roke.purchase.order"

    def purchase_receiving(self):
        """
        收货
        :return:
        """
        # 创建收货单
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.detail_ids:
                if not line.qty:
                    continue
                picking_line_vals.append((0, 0, {
                    "purchase_line_id": line.id,
                    "src_location_id": src_location.id,
                    "dest_location_id": dest_location.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "auxiliary1_qty": line.auxiliary1_qty,
                    "auxiliary2_qty": line.auxiliary2_qty,
                    "origin": record.code,
                    "unit_price": line.unit_price,
                    "subtotal": line.subtotal,
                    "discount_rate": line.discount_rate,
                    "discount_amount": line.discount_amount,
                    "after_discount_amount": line.after_discount_amount,
                    "whole_order_offer": line.whole_order_offer,
                    "amount_receivable": line.amount_receivable,
                }))
            self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "purchase_id": record.id,
                "partner_id": record.supplier_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "employee_id": record.employee_id.id,
                "move_line_ids": picking_line_vals,
                "discount_rate": record.discount_rate,
                "discount_amount": record.discount_amount,
                "amount_after_discount": record.amount_after_discount
            })

    def purchase_return_receiving(self):
        """
        退货
        :return:
        """
        # 创建退货单
        picking_type = self._get_picking_type()
        src_location = picking_type.src_location_id
        dest_location = picking_type.dest_location_id
        picking_line_vals = []
        for record in self:
            for line in record.detail_ids:
                if not line.qty:
                    continue
                # 处理json数据
                picking_line_vals.append((0, 0, {
                    "purchase_line_id": line.id,
                    "src_location_id": src_location.id,
                    "dest_location_id": dest_location.id,
                    "product_id": line.product_id.id,
                    "qty": -1 * line.qty,
                    "auxiliary1_qty": -1 * line.auxiliary1_qty,
                    "auxiliary2_qty": -1 * line.auxiliary2_qty,
                    "origin": record.code,
                    "unit_price": line.unit_price,
                    "subtotal": -1 * line.subtotal,
                }))
            self.env["roke.mes.stock.picking"].create({
                "picking_type_id": picking_type.id,
                "purchase_id": record.id,
                "partner_id": record.supplier_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "origin": record.code,
                "employee_id": record.employee_id.id,
                "move_line_ids": picking_line_vals,
                "is_red_order": True,
            })


class InheritRokePurchaseOrderDetail(models.Model):
    _inherit = "roke.purchase.order.detail"

    auxiliary1_qty = fields.Float(string="辅助数量1", digits='CGSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='CGSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")
    auxiliary_json1 = fields.Char(string="待收货数量")
    wait_receiving_auxiliary1_qty = fields.Float(string="待收货辅助数量1", digits='KCSL',
                                                 compute="_compute_aux_wait_receiving_qty")
    wait_receiving_auxiliary2_qty = fields.Float(string="待收货辅助数量2", digits='KCSL',
                                                 compute="_compute_aux_wait_receiving_qty")
    wait_receiving_auxiliary_json = fields.Char('待收货数量', compute="_compute_aux_wait_receiving_qty")
    receiving_auxiliary1_qty = fields.Float(string="收货辅助数量1", digits='KCSL',
                                            compute="_compute_aux_receiving_qty")
    receiving_auxiliary2_qty = fields.Float(string="收货辅助数量2", digits='KCSL',
                                            compute="_compute_aux_receiving_qty")

    @api.depends("receiving_move_ids", "receiving_move_ids.qty", "qty")
    def _compute_aux_wait_receiving_qty(self):
        for record in self:
            if record.purchase_type == '购货':
                receiving_aux1_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and not r.picking_id.is_red_order).mapped("finish_auxiliary1_qty"))
                receiving_aux2_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and not r.picking_id.is_red_order).mapped("finish_auxiliary2_qty"))
            else:
                receiving_aux1_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and r.picking_id.is_red_order).mapped("finish_auxiliary1_qty"))
                receiving_aux2_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and r.picking_id.is_red_order).mapped("finish_auxiliary2_qty"))
                stock_red_qty = 0
            wait_receiving_auxiliary1_qty = record.auxiliary1_qty - abs(receiving_aux1_qty)
            wait_receiving_auxiliary2_qty = record.auxiliary2_qty - abs(receiving_aux2_qty)
            record.wait_receiving_auxiliary1_qty = wait_receiving_auxiliary1_qty
            record.wait_receiving_auxiliary2_qty = wait_receiving_auxiliary2_qty

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.depends("receiving_move_ids", "receiving_move_ids.qty", "qty")
    def _compute_aux_receiving_qty(self):
        for record in self:
            if record.purchase_type == '购货':
                receiving_auxiliary1_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and not r.picking_id.is_red_order).mapped("finish_auxiliary1_qty"))
                receiving_auxiliary2_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and not r.picking_id.is_red_order).mapped("finish_auxiliary2_qty"))

            else:
                receiving_auxiliary1_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and r.picking_id.is_red_order).mapped("finish_auxiliary1_qty"))
                receiving_auxiliary2_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and r.picking_id.is_red_order).mapped("finish_auxiliary2_qty"))
            record.receiving_auxiliary1_qty = receiving_auxiliary1_qty
            record.receiving_auxiliary2_qty = receiving_auxiliary2_qty

    @api.depends("receiving_move_ids", "receiving_move_ids.qty")
    def _compute_receiving_qty(self):
        for record in self:
            if record.purchase_type == '购货':
                receiving_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and not r.picking_id.is_red_order).mapped("finish_qty"))
                stock_red_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == "完成" and r.picking_id.is_red_order).mapped(
                        "finish_qty"))
            else:
                receiving_qty = sum(
                    record.receiving_move_ids.filtered(
                        lambda r: r.state == '完成' and r.picking_id.is_red_order).mapped("finish_qty"))
                stock_red_qty = 0
            record.receiving_qty = receiving_qty
            record.stock_red_qty = abs(stock_red_qty)
            record.wait_receiving = record.qty - round(receiving_qty, 2)