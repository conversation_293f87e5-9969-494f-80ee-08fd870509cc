# -*- coding: utf-8 -*-

import json

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import math
import json


def _get_pd(env, index="CGSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRokeSaleOrderLine(models.Model):
    _inherit = "roke.purchase.requisition.line"

    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='CGSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='CGSK')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON>an(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    purchased_auxiliary1_qty = fields.Float(string="已下达辅助数量1", compute="_compute_quantity", store=True,
                                            digits='CGSL')
    purchased_auxiliary2_qty = fields.Float(string="已下达辅助数量2", compute="_compute_quantity", store=True,
                                            digits='CGSL')
    purchased_auxiliary_json = fields.Char(string="已下达数量", compute="_compute_quantity", store=True)
    remaining_auxiliary1_qty = fields.Float(string="未下达辅助数量1", compute="_compute_quantity", store=True,
                                            digits='CGSL')
    remaining_auxiliary2_qty = fields.Float(string="未下达辅助数量2", compute="_compute_quantity", store=True,
                                            digits='CGSL')
    remaining_auxiliary_json = fields.Char(string="未下达数量", compute="_compute_quantity", store=True)

    @api.depends(
        "demand_qty", "purchased_qty",
        "purchase_order_line_ids", "purchase_order_line_ids.qty",
        "purchase_order_line_ids.state"
    )
    def _compute_quantity(self):
        for line in self:
            line_ids = line.purchase_order_line_ids.filtered(lambda l: l.state != "取消")
            line.purchased_qty = sum(line_ids.mapped("qty"))  # 采购下达数量：即采购数量
            # 已下达辅助数量
            purchased_auxiliary1_qty = sum(line_ids.mapped("auxiliary1_qty"))
            purchased_auxiliary2_qty = sum(line_ids.mapped("auxiliary2_qty"))
            line.purchased_auxiliary1_qty = purchased_auxiliary1_qty
            line.purchased_auxiliary2_qty = purchased_auxiliary2_qty
            # 未下达辅助数量
            line.remaining_qty = line.demand_qty - line.purchased_qty
            remaining_auxiliary1_qty = line.auxiliary1_qty - purchased_auxiliary1_qty
            remaining_auxiliary2_qty = line.auxiliary2_qty - purchased_auxiliary2_qty
            line.remaining_auxiliary1_qty = remaining_auxiliary1_qty
            line.remaining_auxiliary2_qty = remaining_auxiliary2_qty
            line.purchased_subtotal = line.purchased_qty * line.unit_price
            line.remaining_subtotal = line.remaining_qty * line.unit_price

    def action_open_wizard_generate_order(self):
        """
        重写方法，将勾选的条目，生成采购单。
        :return:
        """
        active_line_ids = self.env["roke.purchase.requisition.line"].search([
            ("id", "in", self._context.get('active_ids', []))
        ])
        line_valus = []
        for active_id in active_line_ids:
            line_valus.append((0, 0,
                               {"requisition_line_id": active_id.id,
                                "quantity": active_id.remaining_qty,
                                "purchased_qty": active_id.purchased_qty,
                                "auxiliary1_qty": active_id.auxiliary1_qty,
                                "auxiliary2_qty": active_id.auxiliary2_qty,
                                "quantity_auxiliary1_qty": active_id.remaining_auxiliary1_qty,
                                "quantity_auxiliary2_qty": active_id.remaining_auxiliary2_qty,
                                "notes": active_id.order_id.note}))
        wizard_id = self.env["wizard.generate.purchase.order"].create({
            "line_ids": line_valus
        })
        return {
            'name': '生成采购单', 'type': 'ir.actions.act_window',
            'res_model': 'wizard.generate.purchase.order',
            'view_mode': 'form', 'target': 'new', 'res_id': wizard_id.id,
            'context': {'create': True, 'edit': True, 'delete': True}
        }

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.demand_qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('demand_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.demand_qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.demand_qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.demand_qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
