#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2022-09-27 10:04
# <AUTHOR> 陈常磊
# @Site    :
# @File    : inherit_roke_sale_order.py
# @Software: PyCharm
import json
from collections import Counter
from odoo import models, fields, api, _
import math
from odoo.exceptions import ValidationError, UserError

def _get_pd(env, index="XSSL"):
    return env["decimal.precision"].precision_get(index)

class InheritRokeSaleOrder(models.Model):
    _inherit = "roke.sale.order"

    def _get_sale_delivery_line_vals(self, sale_line, src_location, dest_location):
        """
        获取发货需求明细值添加辅计量单位
        """
        res = super(InheritRokeSaleOrder, self)._get_sale_delivery_line_vals(sale_line, src_location, dest_location)
        res.update({
            "auxiliary1_qty": sale_line.auxiliary1_qty,
            "auxiliary2_qty": sale_line.auxiliary2_qty,
        })
        return res

    def _get_sale_return_delivery_line_vals(self, sale_line, src_location, dest_location):
        """
        获取发货需求明细值添加辅计量单位
        """
        res = super(InheritRokeSaleOrder, self)._get_sale_return_delivery_line_vals(sale_line, src_location,
                                                                                    dest_location)
        res.update({
            "auxiliary1_qty": -1 * sale_line.auxiliary1_qty,
            "auxiliary2_qty": -1 * sale_line.auxiliary2_qty,
        })
        return res

    def _get_copy_line_ids(self, line_id):
        res = super(InheritRokeSaleOrder, self)._get_copy_line_ids(line_id)
        res.update({
            "uom_id": line_id.uom_id.id,
            "auxiliary1_qty": line_id.auxiliary1_qty,
            "auxiliary_uom1_id": line_id.auxiliary_uom1_id.id,
            "auxiliary2_qty": line_id.auxiliary2_qty,
            "auxiliary_uom2_id": line_id.auxiliary_uom2_id.id,
        })
        return res

    def so_create_bom_picking(self):
        """
        销售订单BOM领料
        :return:
        """
        self.ensure_one()  # 不考虑批量领料，否则已领数量算的不准没意义
        boms = self.line_ids.mapped("e_bom_id")
        if not boms:
            raise ValidationError("订单明细未选择BOM，无法生成领料单。")
        material_counters = {}
        demand_list = []
        for line in self.line_ids.filtered(lambda l: l.e_bom_id):
            child_material_counters = self.so_bom_picking_materials(line.e_bom_id, line.order_qty)
            for key, value in child_material_counters.items():
                if key not in material_counters:
                    material_counters[key] = value
                else:
                    material_counters[key]['main_qty'] += value['main_qty']
                    material_counters[key]['aux1_qty'] += value['aux1_qty']
                    material_counters[key]['aux2_qty'] += value['aux2_qty']
            demand_list += self.so_bom_picking_demand(line.e_bom_id, line.order_qty)
        wizard_lines = []
        for material, qty in material_counters.items():
            wizard_lines.append((0, 0, self.so_bom_picking_get_line_val(material, qty)))
        wizard_demands = []
        for demand in demand_list:
            # demand --> 见so_bom_picking_demand_vals返回值
            wizard_demands.append((0, 0, demand))
        res = self.env["roke.so.create.bom.picking.wizard"].create({
            "line_ids": wizard_lines,
            "demand_ids": wizard_demands
        })
        return {
            'name': 'BOM领料',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.so.create.bom.picking.wizard'
        }

    def so_bom_picking_materials(self, bom, qty):
        """
        获取订单BOM物料
        :return:
        """
        material_dict = {}
        for bom_line in bom.bom_line_ids:
            rel_qty = bom_line.qty / bom.qty * qty * (1 + bom_line.loss_rate / 100)
            if not bom_line.product_id.is_free_conversion:
                auxiliary1_qty = (bom_line.auxiliary1_qty / (bom.qty or 1)) * qty * (1 + bom_line.loss_rate / 100)
                auxiliary2_qty = (bom_line.auxiliary2_qty / (bom.qty or 1)) * qty * (1 + bom_line.loss_rate / 100)
            else:
                auxiliary1_qty = 0
                auxiliary2_qty = 0
            material_dict[bom_line.product_id] = {'main_qty': rel_qty,
                                                  'aux1_qty': auxiliary1_qty,
                                                  'aux2_qty': auxiliary2_qty}
        for bom_line in bom.bom_line_ids.filtered(lambda bl: bl.child_bom_id):
            rel_qty = bom_line.qty / bom.qty * qty * (1 + bom_line.loss_rate / 100)
            child_material_dict = self.so_bom_picking_materials(bom_line.child_bom_id, rel_qty)
            for key, value in child_material_dict.items():
                if key not in material_dict:
                    material_dict[key] = value
                else:
                    material_dict[key]['main_qty'] += value['main_qty']
                    material_dict[key]['aux1_qty'] += value['aux1_qty']
                    material_dict[key]['aux2_qty'] += value['aux2_qty']
        return material_dict

    def so_bom_picking_get_line_val(self, material, qty):
        """
        获取向导类明细值
        qty: 未安装辅计量是个float ,安装辅计量之后则是个字典{'main_qty': rel_qty,
                                                  'aux1_qty': auxiliary1_qty,
                                                  'aux2_qty': auxiliary2_qty}
        :return:
        """
        received_qty = sum(self.bom_move_ids.filtered(
            lambda move: move.product_id == material
        ).mapped("qty"))
        if material.uom_type == '多计量':
            received_auxiliary1_qty = sum(self.bom_move_ids.filtered(
                lambda move: move.product_id == material
            ).mapped("auxiliary1_qty"))
            received_auxiliary2_qty = sum(self.bom_move_ids.filtered(
                lambda move: move.product_id == material
            ).mapped("auxiliary2_qty"))
            product_uom_line1 = material.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == material.auxiliary_uom1_id.id)
            product_uom_line2 = material.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == material.auxiliary_uom2_id.id)
            if not material.is_free_conversion:
                default_qty = qty.get('main_qty') - received_qty
                default_auxiliary1_qty = default_qty * product_uom_line1.conversion if product_uom_line1 else 0
                default_auxiliary2_qty = default_qty * product_uom_line2.conversion if product_uom_line2 else 0
            else:
                default_qty = qty.get('main_qty') - received_qty
                default_auxiliary1_qty = 0
                default_auxiliary2_qty = 0
        else:
            default_qty = qty.get('main_qty') - received_qty
            default_auxiliary1_qty = 0
            default_auxiliary2_qty = 0
        return {
            "material_id": material.id,
            "demand_qty": qty.get('main_qty', 0),
            "demand_auxiliary1_qty": qty.get('aux1_qty', 0),
            "demand_auxiliary2_qty": qty.get('aux2_qty', 0),
            "qty": default_qty if default_qty > 0 else 0,
            "auxiliary1_qty": default_auxiliary1_qty if default_auxiliary1_qty > 0 else 0,
            "auxiliary2_qty": default_auxiliary2_qty if default_auxiliary2_qty > 0 else 0
        }

    def so_bom_picking_demand_vals(self, bom, bom_line, demand_qty, demand_auxiliary1_qty=0, demand_auxiliary2_qty=0):
        """
        获取订单BOM物料需求值
        :return:
        """
        if bom_line.product_id.uom_type == '多计量':
            product_uom_line1 = bom_line.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == bom_line.product_id.auxiliary_uom1_id.id)
            product_uom_line2 = bom_line.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == bom_line.product_id.auxiliary_uom2_id.id)
            if not bom_line.product_id.is_free_conversion:
                demand_qty = demand_qty
                demand_auxiliary1_qty = demand_qty * product_uom_line1.conversion if product_uom_line1 else 0
                demand_auxiliary2_qty = demand_qty * product_uom_line2.conversion if product_uom_line2 else 0
            else:
                demand_qty = demand_qty
                demand_auxiliary1_qty = 0
                demand_auxiliary2_qty = 0
        else:
            demand_auxiliary1_qty = 0
            demand_auxiliary2_qty = 0
            demand_qty = demand_qty
        return {
            "product_id": bom.product_id.id,
            "material_id": bom_line.product_id.id,
            "demand_qty": demand_qty,
            "demand_auxiliary1_qty": demand_auxiliary1_qty,
            "demand_auxiliary2_qty": demand_auxiliary2_qty,
        }

    def so_bom_picking_demand(self, bom, qty):
        """
        获取订单BOM物料需求
        :return:
        """
        material_list = []
        for bom_line in bom.bom_line_ids:
            demand_qty = bom_line.qty / bom.qty * qty * (1 + bom_line.loss_rate / 100)
            if not bom_line.product_id.is_free_conversion:
                demand_auxiliary1_qty = (bom_line.auxiliary1_qty / (bom.qty or 1)) * qty * (
                        1 + bom_line.loss_rate / 100)
                demand_auxiliary2_qty = (bom_line.auxiliary2_qty / (bom.qty or 1)) * qty * (
                        1 + bom_line.loss_rate / 100)
            else:
                demand_auxiliary1_qty = 0
                demand_auxiliary2_qty = 0
            material_list.append(self.so_bom_picking_demand_vals(bom, bom_line, demand_qty, demand_auxiliary1_qty,
                                                                 demand_auxiliary2_qty))
            if bom_line.child_bom_id:
                material_list += self.so_bom_picking_demand(bom_line.child_bom_id, demand_qty)
        return material_list

class InheritRokeSaleOrderLine(models.Model):
    _inherit = "roke.sale.order.line"

    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='XSSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='XSSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    deliver_auxiliary1_qty = fields.Float(string="已交货辅助数量1", digits='KCSL',
                                          compute="_compute_aux_deliver_qty")
    deliver_auxiliary2_qty = fields.Float(string="已交货辅助数量2", digits='KCSL',
                                          compute="_compute_aux_deliver_qty")
    deliver_auxiliary_json = fields.Char(string="已交货数量")
    wait_deliver_auxiliary1_qty = fields.Float(compute="_compute_aux_deliver_qty", string="待交货辅助数量1",
                                               digits='KCSL')
    wait_deliver_auxiliary2_qty = fields.Float(compute="_compute_aux_deliver_qty", string="待交货辅助数量2",
                                               digits='KCSL')
    wait_deliver_auxiliary_json = fields.Char(string="待交货数量")
    stock_auxiliary1_qty = fields.Float(string="当前库存辅助数量1", digits='KCSL',
                                        compute="_compute_aux_stock_qty")
    stock_auxiliary2_qty = fields.Float(string="当前库存辅助数量2", digits='KCSL',
                                        compute="_compute_aux_stock_qty")
    can_production_auxiliary1_qty = fields.Float(string="可生产辅助数量1", compute="_compute_production_qty",
                                                 digits='SCSL')
    can_production_auxiliary2_qty = fields.Float(string="可生产辅助数量2", compute="_compute_production_qty",
                                                 digits='SCSL')
    is_green = fields.Boolean('待交货为绿色', compute='_compute_is_green')

    def _compute_is_green(self):
        for record in self:
            if record.order_qty == record.deliver_qty:
                record.is_green = True
            else:
                record.is_green = False

    def _compute_production_qty(self):
        res = super(InheritRokeSaleOrderLine, self)._compute_production_qty()
        for record in self:
            production_auxiliary1_qty = sum(record.pol_ids.mapped("auxiliary1_qty"))
            production_auxiliary2_qty = sum(record.pol_ids.mapped("auxiliary2_qty"))
            can_production_auxiliary1_qty = record.auxiliary1_qty - production_auxiliary1_qty
            can_production_auxiliary2_qty = record.auxiliary2_qty - production_auxiliary2_qty
            record.can_production_auxiliary1_qty = can_production_auxiliary1_qty if can_production_auxiliary1_qty >= 0 else 0
            record.can_production_auxiliary2_qty = can_production_auxiliary2_qty if can_production_auxiliary2_qty >= 0 else 0
        return res

    @api.depends("product_id")
    def _compute_aux_stock_qty(self):
        for record in self:
            # 是否根据客户批次查询
            inventory_picking_strict = self.env['ir.config_parameter'].sudo().get_param('inventory.picking.strict')
            locations = self.env["roke.mes.stock.location"].sudo().search([
                ('location_type', 'in', ['内部位置', '中转位置'])
            ])
            product_id_new = record.product_id
            if inventory_picking_strict:
                product_id_new = record.customer_id
            quants = self.env["roke.mes.stock.quant"].search_quants(
                products=product_id_new,
                locations=locations
            )
            record.stock_auxiliary1_qty = sum(quants.mapped("inventory_auxiliary1_qty"))
            record.stock_auxiliary2_qty = sum(quants.mapped("inventory_auxiliary2_qty"))

    @api.depends("deliver_move_ids", "deliver_move_ids.qty", "deliver_move_ids.line_ids", "order_qty",
                 "deliver_move_ids.state")
    def _compute_aux_deliver_qty(self):
        for record in self:
            if record.sale_type == '销货':
                deliver_auxiliary1_qty = sum(
                    record.deliver_move_ids.filtered(
                        lambda r: r.state == "完成" and not r.picking_id.is_red_order).mapped(
                        "finish_auxiliary1_qty"))
                deliver_auxiliary2_qty = sum(
                    record.deliver_move_ids.filtered(
                        lambda r: r.state == "完成" and not r.picking_id.is_red_order).mapped(
                        "finish_auxiliary2_qty"))
            else:
                deliver_auxiliary1_qty = sum(
                    record.deliver_move_ids.filtered(
                        lambda r: r.state == "完成" and r.picking_id.is_red_order).mapped(
                        "finish_auxiliary1_qty"))
                deliver_auxiliary2_qty = sum(
                    record.deliver_move_ids.filtered(
                        lambda r: r.state == "完成" and r.picking_id.is_red_order).mapped(
                        "finish_auxiliary2_qty"))

            record.deliver_auxiliary1_qty = deliver_auxiliary1_qty
            record.deliver_auxiliary2_qty = deliver_auxiliary2_qty
            record.wait_deliver_auxiliary1_qty = record.auxiliary1_qty - abs(deliver_auxiliary1_qty)
            record.wait_deliver_auxiliary2_qty = record.auxiliary2_qty - abs(deliver_auxiliary2_qty)

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.order_qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('order_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.order_qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.order_qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.order_qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_aux_qty_dict(self, purchase_material):
        """
        通过辅计量计算各个数量
        """
        product = purchase_material.get('material', False)
        qty = purchase_material.get('qty', 0)
        aux1_qty = purchase_material.get('auxiliary1_qty', 0)
        aux2_qty = purchase_material.get('auxiliary2_qty', 0)
        qty_dict = self.env['roke.uom.groups'].get_aux_qty(product, qty, aux1_qty, aux2_qty)
        return qty_dict

    def sol_create_material_purchase(self):
        """
        销售订单行创建物料采购订单
        :return:
        """
        wizard_line_vals = []
        wizard_material_vals = []
        multi_purchase_materials = []
        purchase_materials_list = []

        for record in self:
            if not record.e_bom_id:
                continue
            purchase_materials = record.e_bom_id.get_can_purchase_material(record.order_qty, 1)
            purchase_materials_list += purchase_materials
        # 将需要购买的明细合并
        # 获取所有物料
        material_list = [pur_line.get('material', False) for pur_line in purchase_materials_list]
        set_material_list = list(set(material_list)) if material_list else []
        for mate_line in set_material_list:
            material_qty = 0
            material_auxiliary1_qty = 0
            material_auxiliary2_qty = 0
            for pur_line in purchase_materials_list:
                if pur_line.get('material', False) == mate_line:
                    # 通过辅计量数量换算一下数量
                    qty_dict = self._get_aux_qty_dict(pur_line)
                    qty = qty_dict.get("main_qty", 0)
                    auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
                    auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
                    material_qty += qty
                    material_auxiliary1_qty += auxiliary1_qty
                    material_auxiliary2_qty += auxiliary2_qty

            multi_purchase_materials.append({
                'material': mate_line,
                'qty': material_qty,
                'aux1_qty': material_auxiliary1_qty,
                'aux2_qty': material_auxiliary2_qty,
            })
        # 获取sale_ids
        sale_ids = [line.order_id.id for line in self]
        # 获取采购明细
        for purchase_material in multi_purchase_materials:
            supplier_id = False
            unit_price = 1
            material = purchase_material.get("material", False)
            qty = purchase_material.get("qty", 0)
            aux1_qty = purchase_material.get("aux1_qty", 0)
            aux2_qty = purchase_material.get("aux2_qty", 0)
            # 根据物料获取已生成的采购订单明细已购买了多少
            purchased_qty, purchased_aux1_qty, purchased_aux2_qty = self.get_purchased_qty(material,
                                                                                           list(set(sale_ids)))
            # 根据物料单位类型判断需要辅计量需要购买多少
            # 非自由非取余
            purchase_qty, purchase_aux1_qty, purchase_aux2_qty = 0, 0, 0
            if not material.is_free_conversion:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
                purchase_aux1_qty = aux1_qty - purchased_aux1_qty if aux1_qty - purchased_aux1_qty > 0 else 0
                purchase_aux2_qty = aux2_qty - purchased_aux2_qty if aux2_qty - purchased_aux2_qty > 0 else 0
            # 自由
            else:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
            if purchase_qty and material and material.supplier_price_ids:
                # 获取供应商价格配置
                supplier_price = material.supplier_price_ids[0]
                supplier_id = supplier_price.supplier_id.id
                unit_price = supplier_price.purchase_price
                purchase_qty = supplier_price.min_purchase_num if purchase_qty < supplier_price.min_purchase_num else purchase_qty
            if purchase_qty > 0:
                wizard_line_vals.append((0, 0, {
                    # "sol": record.id,
                    "material_id": material.id,
                    "demand_qty": qty,
                    "demand_auxiliary1_qty": aux1_qty,
                    "demand_auxiliary2_qty": aux2_qty,
                    "supplier_id": supplier_id,
                    "purchased_qty": purchased_qty,
                    "unit_price": unit_price,
                    "qty": purchase_qty,
                    "auxiliary1_qty": purchase_aux1_qty,
                    "auxiliary2_qty": purchase_aux2_qty,
                    "purchased_aux1_qty": purchased_aux1_qty,
                    "purchased_aux2_qty": purchased_aux2_qty
                }))
        for pur_material in purchase_materials_list:
            # 获取详细物料明细
            qty_dict = self._get_aux_qty_dict(pur_material)
            parent_material = pur_material.get("parent_material_id", False)
            qty = qty_dict.get("main_qty", 0)
            demand_auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
            demand_auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
            material = pur_material.get("material", False)
            wizard_material_vals.append((0, 0, {
                "product_id": parent_material.id,
                "material_id": material.id,
                "demand_qty": qty,
                "demand_auxiliary1_qty": demand_auxiliary1_qty,
                "demand_auxiliary2_qty": demand_auxiliary2_qty,
            }))

        if not wizard_line_vals:
            raise ValidationError("没有可采购的原材料/半成品")
        res = self.env["roke.order.create.purchase.wizard"].create({
            "sale_order_ids": [(6, 0, list(set(sale_ids)))],
            "line_ids": wizard_line_vals,
            "material_details_line_ids": wizard_material_vals
        })
        return {
            'name': '批量创建原材料采购订单',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.order.create.purchase.wizard'
        }

    def get_purchased_qty(self, material, sale_ids):
        # 获取已生成的采购订单明细已购买了多少
        purchased_qty = 0
        purchased_aux1_qty = 0
        purchased_aux2_qty = 0
        purchase_order_obj = self.env['roke.purchase.order']
        sale_purchase_order_records = purchase_order_obj.search([('sale_order_ids', '!=', False)])
        purchase_order_list = [rec for rec in sale_purchase_order_records if
                               rec.sale_order_ids and list(set(rec.sale_order_ids.ids) & set(sale_ids))]

        requisition_order_obj = self.env['roke.purchase.requisition']
        sale_requisition_order_records = requisition_order_obj.search([('requisition_order_ids', '!=', False)])
        requisition_order_list = [rec for rec in sale_requisition_order_records if
                                  rec.requisition_order_ids and list(set(rec.requisition_order_ids.ids) & set(sale_ids))]
        # 采购订单
        for rec in purchase_order_list:
            for line in rec.detail_ids:
                if line.product_id.id == material.id:
                    purchased_qty += line.qty
                    purchased_aux1_qty += line.auxiliary1_qty
                    purchased_aux2_qty += line.auxiliary2_qty

        # 请购单
        for rec in requisition_order_list:
            for line in rec.detail_ids:
                if line.product_id.id == material.id:
                    purchased_qty += line.demand_qty
                    purchased_aux1_qty += line.auxiliary1_qty
                    purchased_aux2_qty += line.auxiliary2_qty
        return purchased_qty, purchased_aux1_qty, purchased_aux2_qty
