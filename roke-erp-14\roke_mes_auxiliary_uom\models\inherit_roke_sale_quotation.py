# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import json


class InheritRokeSaleQuotation(models.Model):
    _inherit = "roke.sale.quotation"

    def get_line_data(self, line):
        res = super(InheritRokeSaleQuotation, self).get_line_data(line)
        res.update({
            'auxiliary1_qty': line.auxiliary1_qty,
            'auxiliary2_qty': line.auxiliary2_qty,
        })
        return res


class InheritRokeSaleQuotationLine(models.Model):
    _inherit = "roke.sale.quotation.line"
    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='XSSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='XSSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    uom_id = fields.Many2one("roke.uom", string="计量单位", related="product_id.uom_id")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
	    if not self.env.context.get('calculate_discount', False):
		    self.qty = 0
		    self.auxiliary1_qty = 0
		    self.auxiliary2_qty = 0
		    self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
	    if not self.env.context.get('calculate_discount', False):
		    if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
			    qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
			                                                                       self.qty)
			    self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
			    self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
		    self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
	    if not self.env.context.get('calculate_discount', False):
		    if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
			    qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
			                                                                       self.auxiliary1_qty)
			    self.qty = qty_json.get('main_qty', 0)
			    self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
		    self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
	    if not self.env.context.get('calculate_discount', False):
		    if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
			    qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
			                                                                       self.auxiliary2_qty)
			    self.qty = qty_json.get('main_qty', 0)
			    self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
		    self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_bom_line_val(self, bom_record, line):
        res = super(InheritRokeSaleQuotationLine, self)._get_bom_line_val(bom_record, line)
        res.update({
            'auxiliary1_qty': line.get('auxiliary1_qty', False),
            'auxiliary2_qty': line.get('auxiliary2_qty', False),
        })
        return res

    def _get_child_data_val(self, bom_line, type):
        res = super(InheritRokeSaleQuotationLine, self)._get_child_data_val(bom_line, type)
        aux_list = []
        aux_compute_type = ""
        if bom_line.product_id.uom_type == '多计量' and bom_line.product_id.uom_groups_id:
            aux_list = [{
                "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                "aux_conversion": rec.conversion,
                "aux_uom_name": rec.uom_id.name
            } for rec in bom_line.product_id.uom_groups_id.uom_line_ids]
            if not bom_line.product_id.uom_groups_id.is_free_conversion:
                aux_compute_type = 'normal'
                # 自由
            elif bom_line.product_id.uom_groups_id.is_free_conversion:
                aux_compute_type = 'free'
            else:
                aux_compute_type = ""
        res.update({
            'aux_list': aux_list,
            'aux_compute_type': aux_compute_type,
            'auxiliary1_qty': bom_line.auxiliary1_qty,
            'auxiliary2_qty': bom_line.auxiliary2_qty,
        })
        return res
