# -*- coding: utf-8 -*-
"""
Description:
    任务物料需求添加辅计量内容
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


class InheritSendMaterialRecord(models.Model):
    _inherit = "roke.send.material.record"

    uom_id = fields.Many2one(related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON><PERSON>(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    # 需求数量
    auxiliary_json = fields.Char(string="需求数量")
    auxiliary1_qty = fields.Float(string="需求辅数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="需求辅数量2", digits='SCSL')
    # 消耗数量
    consume_auxiliary_json = fields.Char(string="消耗数量")
    consume_auxiliary1_qty = fields.Float(string="消耗辅数量1", digits='KCSL')
    consume_auxiliary2_qty = fields.Float(string="消耗辅数量2", digits='KCSL')

    def write(self, vals):
        """
        编辑时，同步编辑辅数量相关内容
        :param vals:
        :return:
        """
        if vals.__contains__("consume_qty"):
            consume_aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(self[0].product_id, 'main', vals.get("consume_qty"))
            vals['consume_auxiliary1_qty'] = consume_aux_value.get('aux1_qty', 0)
            vals['consume_auxiliary2_qty'] = consume_aux_value.get('aux2_qty', 0)
        return super(InheritSendMaterialRecord, self).write(vals)
