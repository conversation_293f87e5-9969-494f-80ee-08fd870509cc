# -*- coding: utf-8 -*-
"""
Description:
    表格报工添加辅计量
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritProductionTask(models.Model):
    _inherit = "roke.table.submit.work.line"

    uom_id = fields.Many2one(related="product_id.uom_id", string="计量单位")
    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 合格数量
    finish_auxiliary_json = fields.Char(string="合格数量")
    finish_auxiliary1_qty = fields.Float(string="合格辅助数量1", digits='SCSL')
    finish_auxiliary2_qty = fields.Float(string="合格辅助数量2", digits='SCSL')
    # 不合格数量
    unqualified_auxiliary_json = fields.Char(string="不合格数量")
    unqualified_auxiliary1_qty = fields.Float(string="不合格辅助数量1", digits='SCSL')
    unqualified_auxiliary2_qty = fields.Float(string="不合格辅助数量2", digits='SCSL')

    def _get_work_record_values(self, wo):
        """
        获取报工记录值处理辅数量
        :param wo:
        :return:
        """
        res = super(InheritProductionTask, self)._get_work_record_values(wo)
        res.update({
            "finish_auxiliary1_qty": self.finish_auxiliary1_qty,
            "finish_auxiliary2_qty": self.finish_auxiliary2_qty,
            "unqualified_auxiliary1_qty": self.unqualified_auxiliary1_qty,
            "unqualified_auxiliary2_qty": self.unqualified_auxiliary2_qty
        })
        return res

    def _without_order_get_order_value(self):
        """
        无工单报工创建工单值
        :return:
        """
        res = super(InheritProductionTask, self)._without_order_get_order_value()
        res.update({
            "plan_auxiliary1_qty": self.finish_auxiliary1_qty,
            "plan_auxiliary2_qty": self.finish_auxiliary2_qty
        })
        return res

