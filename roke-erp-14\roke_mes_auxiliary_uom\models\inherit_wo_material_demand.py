# -*- coding: utf-8 -*-
"""
Description:
    任务物料需求添加辅计量内容
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


class InheritWOMaterialDemand(models.Model):
    _inherit = "roke.wo.material.demand"

    uom_id = fields.Many2one(related="material_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one(related="material_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="material_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON>an(string="辅计量是否实时计算", related="material_id.is_real_time_calculations")
    # 需求数量
    demand_auxiliary_json = fields.Char(string="需求数量")
    demand_auxiliary1_qty = fields.Float(string="需求辅数量1", digits='SCSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅数量2", digits='SCSL')
    # 已投数量
    finished_auxiliary_json = fields.Char(string="已投数量", compute="_compute_qty", store=True)
    finished_auxiliary1_qty = fields.Float(string="已投辅助数量1", compute="_compute_qty", digits='KCSL', store=True)
    finished_auxiliary2_qty = fields.Float(string="已投辅助数量2", compute="_compute_qty", digits='KCSL', store=True)

    @api.depends("demand_qty", "record_ids")
    def _compute_qty(self):
        """
        计算已投数量和需求差额
        :return:
        """
        for record in self:
            finished_qty = sum(record.record_ids.mapped("qty"))
            difference_qty = record.demand_qty - finished_qty
            record.finished_qty = finished_qty
            record.difference_qty = difference_qty if difference_qty > 0 else 0
            # 处理已投辅计量
            finished_auxiliary1_qty = sum(record.record_ids.mapped("auxiliary1_qty"))
            finished_auxiliary2_qty = sum(record.record_ids.mapped("auxiliary2_qty"))
            record.finished_auxiliary1_qty = finished_auxiliary1_qty
            record.finished_auxiliary2_qty = finished_auxiliary2_qty
            record.finished_auxiliary_json = json.dumps({
                'main_qty': finished_qty,
                'aux1_qty': finished_auxiliary1_qty,
                'aux2_qty': finished_auxiliary2_qty
            })
