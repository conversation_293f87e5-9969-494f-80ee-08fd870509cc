# -*- coding: utf-8 -*-
"""
Description:
    任务物料需求添加辅计量内容
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import math
import json


class InheritWOMaterialRecord(models.Model):
    _inherit = "roke.wo.material.record"

    uom_id = fields.Many2one(related="material_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one(related="material_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="material_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON><PERSON>(string="辅计量是否实时计算", related="material_id.is_real_time_calculations")
    # 投料数量
    auxiliary_json = fields.Char(string="投料数量")
    auxiliary1_qty = fields.Float(string="投料辅数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="投料辅数量2", digits='SCSL')
