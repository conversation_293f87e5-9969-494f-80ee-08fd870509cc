# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo.exceptions import ValidationError
from odoo import models, fields, api, _
import json
import math


def _get_pd(env, index=False):
    if not index:
        index = 'SCSL'
    return env["decimal.precision"].precision_get(index)


class InheritWorkOrder(models.Model):
    _inherit = "roke.work.order"

    auxiliary_uom1_id = fields.Many2one("roke.uom", string="辅计量单位1", related="product_id.auxiliary_uom1_id")
    auxiliary_uom2_id = fields.Many2one("roke.uom", string="辅计量单位2", related="product_id.auxiliary_uom2_id")
    is_real_time_calculations = fields.Bo<PERSON>an(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 计划数量
    plan_auxiliary_json = fields.Char(string="计划数量")
    plan_auxiliary1_qty = fields.Float(string="计划辅助数量1", digits='SCSL')
    plan_auxiliary2_qty = fields.Float(string="计划辅助数量2", digits='SCSL')
    plan_uom_info = fields.Char(string="计划数量", compute="_compute_plan_uom_info")
    # 完工数量
    finish_auxiliary_json = fields.Char(string="完工数量", copy=False)
    finish_auxiliary1_qty = fields.Float(string="完工辅助数量1", digits='SCSL', copy=False)
    finish_auxiliary2_qty = fields.Float(string="完工辅助数量2", digits='SCSL', copy=False)
    finish_uom_info = fields.Char(string="完工数量", compute="_compute_finish_uom_info")
    # 不合格辅数量
    unqualified_auxiliary_json = fields.Char(string="不合格数量", copy=False)
    unqualified_auxiliary1_qty = fields.Float(string="不合格辅助数量1", digits='SCSL', copy=False)
    unqualified_auxiliary2_qty = fields.Float(string="不合格辅助数量2", digits='SCSL', copy=False)
    unqualified_uom_info = fields.Char(string="不合格数量", compute="_compute_unqualified_uom_info")

    @api.onchange("task_id")
    def _onchange_task_id(self):
        if self.task_id:
            return {"value": {"plan_qty": self.task_id.plan_qty,
                              "plan_auxiliary1_qty": self.task_id.plan_auxiliary1_qty,
                              "plan_auxiliary2_qty": self.task_id.plan_auxiliary2_qty,
                              "product_id": self.task_id.product_id.id}}

    @api.depends('plan_auxiliary1_qty', 'plan_auxiliary2_qty', 'plan_qty')
    def _compute_plan_uom_info(self):
        for rec in self:
            plan_uom_info = str(rec.plan_qty) + str(rec.uom_id.name)
            if rec.product_id and rec.product_id.uom_type == '多计量':
                if rec.auxiliary_uom1_id:
                    plan_uom_info += ' ' + str(rec.plan_auxiliary1_qty) + str(rec.auxiliary_uom1_id.name)
                if rec.auxiliary_uom2_id:
                    plan_uom_info += ' ' + str(rec.plan_auxiliary2_qty) + str(rec.auxiliary_uom2_id.name)
            rec.plan_uom_info = plan_uom_info

    @api.depends('finish_auxiliary1_qty', 'finish_auxiliary2_qty', 'finish_qty')
    def _compute_finish_uom_info(self):
        for rec in self:
            finish_uom_info = str(rec.finish_qty) + str(rec.uom_id.name)
            if rec.product_id and rec.product_id.uom_type == '多计量':
                if rec.auxiliary_uom1_id:
                    finish_uom_info += ' ' + str(rec.finish_auxiliary1_qty) + str(rec.auxiliary_uom1_id.name)
                if rec.auxiliary_uom2_id:
                    finish_uom_info += ' ' + str(rec.finish_auxiliary2_qty) + str(rec.auxiliary_uom2_id.name)
            rec.finish_uom_info = finish_uom_info

    @api.depends('unqualified_auxiliary1_qty', 'unqualified_auxiliary2_qty', 'unqualified_qty')
    def _compute_unqualified_uom_info(self):
        for rec in self:
            unqualified_uom_info = str(rec.unqualified_qty) + str(rec.uom_id.name)
            if rec.product_id and rec.product_id.uom_type == '多计量':
                if rec.auxiliary_uom1_id:
                    unqualified_uom_info += str(rec.unqualified_auxiliary1_qty) + str(rec.auxiliary_uom1_id.name)
                if rec.auxiliary_uom2_id:
                    unqualified_uom_info += str(rec.unqualified_auxiliary2_qty) + str(rec.auxiliary_uom2_id.name)
            rec.unqualified_uom_info = unqualified_uom_info

    def _multi_previous_get_min_auxiliary_finished(self, previous):
        """
        前道工序为并行工序时获取完工数最小的工序
        :return:
        """
        return sorted(previous, key=lambda x: x['finish_auxiliary1_qty'], reverse=True)[0]

    def _get_production_auxiliary_multiple(self, previous):
        """
        获取当前生产倍数
        :param previous:
        :return:
        """
        if len(previous) > 1:
            # 取到最小数对应的前道工单
            rel_previous = self._multi_previous_get_min_auxiliary_finished(previous)
            if not rel_previous.routing_line_id.multiple:  # 无倍数直接返回
                return 1
            return self.routing_line_id.multiple / rel_previous.routing_line_id.multiple
        if not previous.routing_line_id.multiple:  # 无倍数直接返回
            return 1
        return self.routing_line_id.multiple / previous.routing_line_id.multiple

    def _get_previous_finished_auxiliary_qty(self, previous):
        """
        获取前道工序完工数,同级工序的计划数和完工数
        :return:
        """
        # 取余
        sequence_qty = {}
        for p in previous:
            sequence = p.sequence
            if sequence in sequence_qty:
                sequence_qty[sequence] = sequence_qty.get(sequence) + p.finish_auxiliary1_qty
            else:
                sequence_qty[sequence] = p.finish_auxiliary1_qty
        return min(sequence_qty.values())

    def _get_previous_min_auxiliary_qty(self):
        """
        获取前道工序最小完工数
        :return:
        """
        previous = self._get_previous()  # 考虑并行工序原因，前道工序可能有多个
        if previous:
            # 获取前道工序最小完成数
            previous_finished_qty = self._get_previous_finished_auxiliary_qty(previous)
            # 生产倍数
            min_qty = previous_finished_qty * self._get_production_auxiliary_multiple(previous)
            return min_qty
        else:
            return False

    def _get_child_previous_min_auxiliary_qty(self):
        """
        获取子工单的前工序最小完工数量
        :return:
        """
        previous = self._get_child_previous()  # 考虑并行工序原因，前道工序可能有多个
        if previous:
            # 获取前道工序最小完成数
            min_qty = self._get_previous_finished_auxiliary_qty(previous)
        elif self.main_wo_id:
            # 获取主工序的前道工序
            min_qty = self.main_wo_id._get_previous_min_auxiliary_qty()
        else:
            min_qty = False
        return min_qty

    def _get_wo_allow_auxiliary_qty(self):
        """
        获取工单辅计量可报数量
        :return:
        """
        company = self.env.user.company_id
        default_qty = round(self.plan_auxiliary1_qty - self.finish_auxiliary1_qty, _get_pd(self.env))
        allow_qty = round(self.plan_auxiliary1_qty - self.finish_auxiliary1_qty, _get_pd(self.env))
        if company.freedom_work != "允许":  # 禁止自由报工 校验前道工序完成数：当前允许数量等于签到工序最小完工数
            if self.wo_child_type == "child" and self.main_wo_id:
                # 子工序校验可报数量
                child_previous_min_qty = self._get_child_previous_min_auxiliary_qty()
                if child_previous_min_qty > self.plan_auxiliary1_qty:  # 如果前工单最小报工数大于当前工单计划数，那么取当前计划数（拆分工单场景）
                    child_previous_min_qty = self.plan_auxiliary1_qty
                if type(child_previous_min_qty) in (float, int):
                    allow_qty = child_previous_min_qty - self.finish_auxiliary1_qty
                    default_qty = child_previous_min_qty - self.finish_auxiliary1_qty
            else:
                previous_min_qty = self._get_previous_min_auxiliary_qty()
                if previous_min_qty > self.plan_auxiliary1_qty:  # 如果前工单最小报工数大于当前工单计划数，那么取当前计划数（拆分工单场景）
                    previous_min_qty = self.plan_auxiliary1_qty
                if type(previous_min_qty) in (float, int):
                    allow_qty = previous_min_qty - self.finish_auxiliary1_qty
                    default_qty = previous_min_qty - self.finish_auxiliary1_qty
        exceed_plan_qty = float(self.sudo().env["ir.config_parameter"].get_param('exceed.plan.qty', default=0))
        if exceed_plan_qty and company.exceed_plan == "允许":
            allow_qty = round(self.plan_auxiliary1_qty + exceed_plan_qty - self.finish_auxiliary1_qty, _get_pd(self.env))
        if company.complete_basis == "报工数":
            allow_qty = round(allow_qty - self.unqualified_auxiliary1_qty, _get_pd(self.env))
            default_qty = round(default_qty - self.unqualified_auxiliary1_qty, _get_pd(self.env))
        return allow_qty if allow_qty > 0 else 0, default_qty if default_qty > 0 else 0

    def _create_work_record_get_values(self, wait_qty, default_qty, multi):
        """
        打开报工窗口时获取数据
        :return:
        """
        res = super(InheritWorkOrder, self)._create_work_record_get_values(wait_qty, default_qty, multi)
        product = self.product_id
        _wait_qty = wait_qty if wait_qty > 0 else 0
        finish_qty = res.get("finish_qty") or 0
        _finish_qty = finish_qty if finish_qty > 0 else 0
        if not product.is_free_conversion:
            # 取余、自由换算不处理辅计量数量
            wait_auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', _wait_qty)
            finish_auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', _finish_qty)
            res.update({
                "finish_auxiliary1_qty": finish_auxiliary.get("aux1_qty", 0),
                "finish_auxiliary2_qty": finish_auxiliary.get("aux2_qty", 0),
                "wait_auxiliary1_qty": wait_auxiliary.get("aux1_qty", 0),
                "wait_auxiliary2_qty": wait_auxiliary.get("aux2_qty", 0),
            })
        if product.is_free_conversion:
            # 自由换算
            res.update({
                "finish_auxiliary1_qty": 0,
                "finish_auxiliary2_qty": 0,
                "wait_auxiliary1_qty": 0,
                "wait_auxiliary2_qty": 0,
            })
        return res

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.plan_qty = 0
            self.plan_auxiliary1_qty = 0
            self.plan_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_qty')
    def _onchange_aux_plan_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.plan_qty)
                self.plan_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.plan_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.plan_auxiliary1_qty)
                self.plan_qty = qty_json.get('main_qty', 0)
                self.plan_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('plan_auxiliary2_qty')
    def _onchange_plan_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.plan_auxiliary2_qty)
                self.plan_qty = qty_json.get('main_qty', 0)
                self.plan_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_production_result_vals(self, qty, wr_id):
        """
        获取产出物信息添加辅计量
        :param qty:
        :param wr_id:
        :return:
        """
        res = super(InheritWorkOrder, self)._get_production_result_vals(qty, wr_id)
        if type(res) == list:
            result = []
            if len(res) > 1:
                for val in res:
                    # 单件管理会产生多个产出物，此时的辅计量取默认
                    aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(self.product_id, 'main', val.get("qty", 0))
                    val.update({
                        "auxiliary1_qty": aux_value.get('aux1_qty', 0),
                        "auxiliary2_qty": aux_value.get('aux2_qty', 0)
                    })
                    result.append(val)
            else:
                for val in res:
                    # 单个产出直接取报工记录的辅数量
                    work_record = self.env['roke.work.record'].browse(wr_id)
                    val.update({
                        "auxiliary1_qty": work_record.finish_auxiliary1_qty,
                        "auxiliary2_qty": work_record.finish_auxiliary2_qty,
                    })
                    result.append(val)
            return result
        # 单个产出直接取报工记录的辅数量
        work_record = self.env['roke.work.record'].browse(wr_id)
        res.update({
            "auxiliary1_qty": work_record.finish_auxiliary1_qty,
            "auxiliary2_qty": work_record.finish_auxiliary2_qty,
        })
        return res

    def _get_wo_create_picking_wizard_vals(self, src_location, dest_location, allow_dest_locations, material, v):
        """
        获取向导类内容
        :return:
        """
        res = super(InheritWorkOrder, self)._get_wo_create_picking_wizard_vals(src_location, dest_location, allow_dest_locations, material, v)
        aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(material, 'main', v.get("difference_qty", 0))
        demand_aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(material, 'main', v.get("demand_qty", 0))
        res.update({
            "demand_auxiliary1_qty": demand_aux_value.get("aux1_qty", 0),
            "demand_auxiliary2_qty": demand_aux_value.get("aux2_qty", 0),
            "auxiliary1_qty": aux_value.get("aux1_qty", 0),
            "auxiliary2_qty": aux_value.get("aux2_qty", 0),
        })
        return res

    def _get_create_material_demand_vals(self, bom_line, demand_qty):
        """
        获取工单物料需求内容
        :return:
        """
        res = super(InheritWorkOrder, self)._get_create_material_demand_vals(bom_line, demand_qty)
        demand_aux_value = self.env['roke.uom.groups'].main_auxiliary_conversion(bom_line.product_id, 'main', demand_qty)
        res.update({
            "demand_auxiliary1_qty": demand_aux_value.get("aux1_qty", 0),
            "demand_auxiliary2_qty": demand_aux_value.get("aux2_qty", 0),
        })
        return res

    # 委外发料
    def _get_transportation_line_vals(self, record):
        """
        获取发料内容
        :return:
        """
        res = super(InheritWorkOrder, self)._get_transportation_line_vals(record)
        uom_groups_obj = self.env['roke.uom.groups']
        aux_value = uom_groups_obj.main_auxiliary_conversion(record.product_id, 'main', res.get("qty"))
        auxiliary1_qty = aux_value.get("aux1_qty", 0)
        auxiliary2_qty = aux_value.get("aux2_qty", 0)
        res.update({
            "auxiliary1_qty": auxiliary1_qty,
            "auxiliary2_qty": auxiliary2_qty
        })
        return res

    def _get_bom_transportation_line_vals(self, record, bom):
        """
        获取BOM发料内容
        :return:
        """
        res = super(InheritWorkOrder, self)._get_bom_transportation_line_vals(record, bom)
        uom_groups_obj = self.env['roke.uom.groups']
        aux_value = uom_groups_obj.main_auxiliary_conversion(record.product_id, 'main', res.get("qty"))
        res.update({
            "auxiliary1_qty": aux_value.get("aux1_qty", 0),
            "auxiliary2_qty": aux_value.get("aux2_qty", 0)
        })
        return res

    @staticmethod
    def update_aux_get_new_json(old_json, new_json, subtract=False):
        """
        此处对于auxiliary_json的处理貌似不太合理，但辅计量取余计算的设计如此，暂时硬写
        :param old_json:
        :param new_json:
        :return:
        """
        old_main_qty = float(old_json.get("main_qty") or 0)
        old_aux1_qty = float(old_json.get("aux1_qty") or 0)
        old_aux2_qty = float(old_json.get("aux2_qty") or 0)
        new_main_qty = float(new_json.get("main_qty") or 0)
        new_aux1_qty = float(new_json.get("aux1_qty") or 0)
        new_aux2_qty = float(new_json.get("aux2_qty") or 0)
        if subtract:
            return json.dumps({
                'main_qty': old_main_qty - new_main_qty,
                'aux1_qty': old_aux1_qty - new_aux1_qty,
                'aux2_qty': old_aux2_qty - new_aux2_qty
            })
        return json.dumps({
            'main_qty': old_main_qty + new_main_qty,
            'aux1_qty': old_aux1_qty + new_aux1_qty,
            'aux2_qty': old_aux2_qty + new_aux2_qty
        })

    # 完工数处理：报工、撤回时更新辅计量
    def update_aux_qty(self,
                       finish_qty,
                       finish_auxiliary1_qty,
                       finish_auxiliary2_qty,
                       unqualified_qty,
                       unqualified_auxiliary1_qty,
                       unqualified_auxiliary2_qty,
                       withdraw=False):
        """
        完工数处理：报工、撤回时更新辅计量：适配自由换算
        :return:
        """
        self.ensure_one()
        uom_groups_obj = self.env['roke.uom.groups']
        write_dict = {}
        if finish_qty or finish_auxiliary1_qty:
            write_dict["finish_auxiliary1_qty"] = self.finish_auxiliary1_qty + finish_auxiliary1_qty
            write_dict["finish_auxiliary2_qty"] = self.finish_auxiliary2_qty + finish_auxiliary2_qty or 0
        if unqualified_qty or unqualified_auxiliary1_qty:
            write_dict["unqualified_auxiliary1_qty"] = self.unqualified_auxiliary1_qty + unqualified_auxiliary1_qty
            write_dict["unqualified_auxiliary2_qty"] = self.unqualified_auxiliary1_qty + unqualified_auxiliary2_qty or 0
        self.write(write_dict)
        # 判断是否更新生产任务
        if self.task_id:
            max_sequence = max(self.task_id.work_order_ids.filtered(lambda wo: wo.type != '质检').mapped("sequence"))
            if self.sequence == max_sequence:
                self.task_id.pt_update_aux_qty(
                    finish_qty,
                    finish_auxiliary1_qty,
                    finish_auxiliary2_qty,
                    withdraw=withdraw
                )

    def _split_wo_get_values(self, allow_split_qty):
        """
        获取拆分工单的值,添加辅计量
        :return:
        """
        res = super(InheritWorkOrder, self)._split_wo_get_values(allow_split_qty)
        product = self.product_id
        if not product.is_free_conversion:
            # 取余、自由换算不处理辅计量数量
            auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', allow_split_qty)
            res.update({
                "allow_auxiliary1_qty": auxiliary.get("aux1_qty", 0),
                "allow_auxiliary2_qty": auxiliary.get("aux2_qty", 0),
                "split_auxiliary1_qty": auxiliary.get("aux1_qty", 0),
                "split_auxiliary2_qty": auxiliary.get("aux2_qty", 0),
            })
        else:
            # 自由换算
            res.update({
                "allow_auxiliary1_qty": 0,
                "allow_auxiliary2_qty": 0,
                "split_auxiliary1_qty": 0,
                "split_auxiliary2_qty": 0,
            })
        return res

