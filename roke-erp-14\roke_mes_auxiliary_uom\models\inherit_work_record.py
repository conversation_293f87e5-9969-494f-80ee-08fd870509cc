# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritWorkRecord(models.Model):
    _inherit = "roke.work.record"

    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 合格数量
    finish_auxiliary_json = fields.Char(string="合格数量")
    finish_auxiliary1_qty = fields.Float(string="合格辅助数量1", digits='SCSL')
    finish_auxiliary2_qty = fields.Float(string="合格辅助数量2", digits='SCSL')
    # 不合格数量
    unqualified_auxiliary_json = fields.Char(string="不合格数量")
    unqualified_auxiliary1_qty = fields.Float(string="不合格辅助数量1", digits='SCSL')
    unqualified_auxiliary2_qty = fields.Float(string="不合格辅助数量2", digits='SCSL')
    # 不计工资数
    invalid_auxiliary_json = fields.Char(string="不计工资数")
    invalid_auxiliary1_qty = fields.Float(string="不计工资辅助数量1", digits='SCSL')
    invalid_auxiliary2_qty = fields.Float(string="不计工资辅助数量2", digits='SCSL')

    def withdraw_execute_other(self, record):
        """
        撤回报工,修改辅计量
        :return:
        """
        super(InheritWorkRecord, self).withdraw_execute_other(record)
        record.work_order_id.update_aux_qty(
            -record.finish_qty,
            -record.finish_auxiliary1_qty,
            -record.finish_auxiliary2_qty,
            -record.unqualified_qty,
            -record.unqualified_auxiliary1_qty,
            -record.unqualified_auxiliary2_qty,
            withdraw=True
        )
