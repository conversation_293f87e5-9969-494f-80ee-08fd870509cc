#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2022-09-22 10:04
# <AUTHOR> 陈常磊
# @Site    : 
# @File    : roke_uom_groups.py
# @Software: PyCharm

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import math


def _get_pd(env, index):
    if index:
        return env["decimal.precision"].precision_get(index)
    return env["decimal.precision"].precision_get('Stock')


class RokeUomGroups(models.Model):
    _name = "roke.uom.groups"
    _description = "多计量组"
    _inherit = ['mail.thread']

    code = fields.Char(string="组编号", required=True, index=True, tracking=True,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.uom.groups.code'))
    name = fields.Char(string="组名称", required=True, index=True, tracking=True)
    mnemonic_aid = fields.Char(string="助记名", compute="_compute_mnemonic_aid", store=True)
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    note = fields.Text(string="备注")
    uom_line_ids = fields.One2many("roke.uom.line", "uom_groups_id", string="计量单位列表")
    is_free_conversion = fields.Boolean(string="是否自由换算",
                                        help="勾选后，填写业务单据时不会将辅助数量按照换算关系进行强制换算。")
    # is_balance_calculation = fields.Boolean(string="是否取余计算")
    main_uom_id = fields.Many2one("roke.uom", string="计量单位", required=True)
    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)',
         '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def name_get(self):
        res = []
        for record in self:
            name = "%s[%s]" % (record.mnemonic_aid, record.code)
            res += [(record.id, name)]
        return res

    @api.depends("uom_line_ids")
    def _compute_mnemonic_aid(self):
        for rec in self:
            mnemonic_aid = rec.main_uom_id.name + "/" if rec.main_uom_id.name else "/"
            for line in rec.uom_line_ids:
                mnemonic_aid = mnemonic_aid + line.uom_id.name + "/" if line.uom_id.name else mnemonic_aid
            mnemonic_aid = mnemonic_aid[:-1]
            rec.mnemonic_aid = mnemonic_aid

    @api.constrains('uom_line_ids')
    def _check_uom_line_ids(self):
        for rec in self:
            aux1_records = rec.uom_line_ids.filtered(lambda a: a.uom_grade == '辅计量1')
            if len(aux1_records.ids) > 1:
                raise UserError('辅计量单位组只能包含一项辅计量1单位')
            aux2_records = rec.uom_line_ids.filtered(lambda a: a.uom_grade == '辅计量2')
            if len(aux2_records.ids) > 1:
                raise UserError('辅计量单位组只能包含一项辅计量2单位')
            # 判断明细中，单位不能重复
            uom_list = rec.uom_line_ids.mapped('uom_id')
            set_uom_list = list(set(uom_list)) if uom_list else uom_list
            if len(set_uom_list) != len(rec.uom_line_ids.ids):
                raise UserError('计量单位组中，计量单位不能重复')
            if rec.main_uom_id in set_uom_list:
                raise UserError('辅计量不能和主计量单位相同')
    @api.model
    def create(self, vals):
        res = super(RokeUomGroups, self).create(vals)
        if len(res.uom_line_ids) < 1:
            raise UserError('请添加计量单位组合')
        return res

    def write(self, vals):
        res = super(RokeUomGroups, self).write(vals)
        for res in self:
            if len(res.uom_line_ids) < 1:
                raise UserError('请添加计量单位组合')
        return res

    def compute_aux_qty(self, product_id, auxiliary_json, main_qty_filed=False, aux1_qty_filed=False,
                        aux2_qty_filed=False):
        product_onj = self.env['roke.product']
        # if vals.get("auxiliary_json", False):
        #     # 如果获取到了json数据，对其进行解析
        #     if not vals.get('product_id', False):
        #         product_record = product_onj.browse(product_id)
        #     else:
        product_record = product_onj.browse(product_id)
        is_free_conversion = product_record.is_free_conversion
        product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
        product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
        # 如果不取余计算，直接保存json，如果是取余计算，算出新的保存
        # auxiliary_json = json.loads(vals.get("auxiliary_json", ""))
        main_qty = float(auxiliary_json.get('main_qty', 0)) if auxiliary_json.get('main_qty', 0) else 0
        aux1_qty = float(auxiliary_json.get('aux1_qty', 0)) if auxiliary_json.get('aux1_qty', 0) else 0
        aux2_qty = float(auxiliary_json.get('aux2_qty', 0)) if auxiliary_json.get('aux2_qty', 0) else 0
        # 非取余计算
        # 非自由换算
        if not is_free_conversion:
            not_free_main_qty = 0
            not_free_aux1_qty = 0
            not_free_aux2_qty = 0
            if main_qty:
                not_free_main_qty = main_qty
                if product_uom_line1 and product_uom_line1.conversion:
                    not_free_aux1_qty = not_free_main_qty * product_uom_line1.conversion
                if product_uom_line2 and product_uom_line2.conversion:
                    not_free_aux2_qty = not_free_main_qty * product_uom_line2.conversion
            elif aux1_qty:
                not_free_aux1_qty = aux1_qty
                if product_uom_line1 and product_uom_line1.conversion:
                    not_free_main_qty = not_free_aux1_qty / product_uom_line1.conversion
                    if product_uom_line2 and product_uom_line2.conversion:
                        not_free_aux2_qty = not_free_main_qty * product_uom_line2.conversion
            elif aux2_qty:
                not_free_aux2_qty = aux2_qty
                if product_uom_line2 and product_uom_line2.conversion:
                    not_free_main_qty = not_free_aux2_qty / product_uom_line2.conversion
                    if product_uom_line1 and product_uom_line1.conversion:
                        not_free_aux1_qty = not_free_main_qty * product_uom_line1.conversion
            val = {'auxiliary_json': json.dumps(
                {'main_qty': not_free_main_qty, 'aux1_qty': not_free_aux1_qty, 'aux2_qty': not_free_aux2_qty})}
            if main_qty_filed:
                val[main_qty_filed] = not_free_main_qty
            if aux1_qty_filed:
                val[aux1_qty_filed] = not_free_aux1_qty
            if aux2_qty_filed:
                val[aux2_qty_filed] = not_free_aux2_qty
            return val
        else:
            val = {}
            if main_qty_filed:
                val[main_qty_filed] = main_qty
            if aux1_qty_filed:
                val[aux1_qty_filed] = aux1_qty
            if aux2_qty_filed:
                val[aux2_qty_filed] = aux2_qty
            return val

    def main_auxiliary_conversion(self, product, type, value):
        """主辅换算"""
        product_uom_line1 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom1_id.id)
        product_uom_line2 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom2_id.id)
        if type == 'main':
            main_qty = value
            aux1_qty = 0
            aux2_qty = 0
            if product_uom_line1 and product_uom_line1.conversion:
                aux1_qty = value * product_uom_line1.conversion
            if product_uom_line2 and product_uom_line2.conversion:
                aux2_qty = value * product_uom_line2.conversion
            return {'main_qty': main_qty, 'aux1_qty': aux1_qty, 'aux2_qty': aux2_qty}
        elif type == 'aux1':
            aux1_qty = value
            main_qty = 0
            aux2_qty = 0
            if product_uom_line1 and product_uom_line1.conversion:
                main_qty = value / product_uom_line1.conversion
                if product_uom_line2 and product_uom_line2.conversion:
                    aux2_qty = main_qty * product_uom_line2.conversion
            return {'main_qty': main_qty, 'aux1_qty': aux1_qty, 'aux2_qty': aux2_qty}
        elif type == 'aux2':
            aux2_qty = value
            main_qty = 0
            aux1_qty = 0
            if product_uom_line2 and product_uom_line2.conversion:
                main_qty = value / product_uom_line2.conversion
                if product_uom_line1 and product_uom_line1.conversion:
                    aux1_qty = main_qty * product_uom_line1.conversion
            return {'main_qty': main_qty, 'aux1_qty': aux1_qty, 'aux2_qty': aux2_qty}

    def balance_calculation_conversion(self, product, auxiliary_json, index=False):
        """取余计算"""
        main_qty = float(auxiliary_json.get('main_qty', 0)) if auxiliary_json.get('main_qty', 0) else 0
        aux1_qty = float(auxiliary_json.get('aux1_qty', 0)) if auxiliary_json.get('aux1_qty', 0) else 0
        product_uom_line1 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom1_id.id)
        val = {}
        base_qty = main_qty
        base_aux1_qty = 0
        # 处理存到数据库中三个数量字段中的值
        if product_uom_line1 and product_uom_line1.conversion:
            base_aux1_qty = base_qty * product_uom_line1.conversion
        base_aux1_qty = base_aux1_qty + aux1_qty
        actual_base_qty = 0
        if product_uom_line1 and product_uom_line1.conversion:
            actual_base_qty = base_aux1_qty / product_uom_line1.conversion
        # 处理写到json中的值
        json_main_qty = main_qty
        json_aux1_qty = 0
        if product_uom_line1 and product_uom_line1.conversion:
            json_aux1_qty = aux1_qty / product_uom_line1.conversion
            floor_aux1_qty = math.floor(json_aux1_qty)
            if floor_aux1_qty > 0:
                json_main_qty += math.floor(json_aux1_qty)
                json_aux1_qty = aux1_qty - (floor_aux1_qty * product_uom_line1.conversion)
            else:
                json_aux1_qty = aux1_qty
        balance_calculation_auxiliary_json = {'main_qty': round(json_main_qty, _get_pd(self.env, index)),
                                              'aux1_qty': round(json_aux1_qty, _get_pd(self.env, index))}
        return {'main_qty': actual_base_qty, 'aux1_qty': base_aux1_qty,
                'auxiliary_json': balance_calculation_auxiliary_json}

    def compute_auxiliary_json(self, product, aux1_qty):
        """
        根据产品和辅一数量计算json
        """
        product_uom_line1 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom1_id.id)
        json_main_qty = 0
        json_aux1_qty = 0
        if product_uom_line1 and product_uom_line1.conversion:
            json_aux1_qty = aux1_qty / product_uom_line1.conversion
            floor_aux1_qty = math.floor(json_aux1_qty)
            if floor_aux1_qty > 0:
                json_main_qty = math.floor(json_aux1_qty)
                json_aux1_qty = aux1_qty - (floor_aux1_qty * product_uom_line1.conversion)
            else:
                json_aux1_qty = aux1_qty
        auxiliary_json = {"main_qty": json_main_qty,
                          "aux1_qty": json_aux1_qty}
        return auxiliary_json

    def compute_all_auxiliary_json(self, product, main_qty, aux1_qty, aux2_qty):
        """
        根据产品,主数量,辅一数量,辅二数量计算json
        备注: 不区分什么类型的计量类型
        """
        auxiliary_json = {}
        # 非自由、非取余、自由
        auxiliary_json = {
            "main_qty": main_qty,
            "aux1_qty": aux1_qty,
            "aux2_qty": aux2_qty
        }
        return auxiliary_json

    def get_aux_qty(self, product, qty, aux1_qty, aux2_qty):
        """
        通过计量组中最小单位计算其他单位数量
        """
        # 获取该产品下的最小单位

        uom_line_conversion_list = product.uom_groups_id.uom_line_ids.mapped('conversion')
        max_conversion = max(uom_line_conversion_list) if uom_line_conversion_list else False
        uom_line_records = product.uom_groups_id.uom_line_ids.filtered(lambda x: x.conversion == max_conversion)
        uom_line_record = uom_line_records[0] if uom_line_records else False
        if not aux1_qty and not aux2_qty:
            uom_line_record = False
        product_uom_line1 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom1_id.id)
        product_uom_line2 = product.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == product.auxiliary_uom2_id.id)
        main_qty = 0
        auxiliary1_qty = 0
        auxiliary2_qty = 0
        # 非自由非取余
        if not product.is_free_conversion:
            # 如果辅一辅二的换算关系小于1则用主计量换算，否则用最小单位的计量line换算
            if uom_line_record and uom_line_record.conversion >= 1:
                calculate_qty = aux1_qty if uom_line_record.uom_grade == '辅计量1' else aux2_qty
                main_qty = calculate_qty / uom_line_record.conversion
                if uom_line_record.id == product_uom_line1.id:
                    auxiliary1_qty = aux1_qty
                    auxiliary2_qty = main_qty * product_uom_line2.conversion
                else:
                    auxiliary2_qty = aux2_qty
                    auxiliary1_qty = main_qty * product_uom_line1.conversion
            else:
                calculate_qty = qty
                main_qty = calculate_qty
                auxiliary1_qty = calculate_qty * product_uom_line1.conversion
                auxiliary2_qty = calculate_qty * product_uom_line2.conversion
        # 自由
        elif product.is_free_conversion:
            main_qty = qty
        return {
            'main_qty': main_qty,
            'auxiliary1_qty': auxiliary1_qty,
            'auxiliary2_qty': auxiliary2_qty
        }


class RokeUomLine(models.Model):
    _name = "roke.uom.line"
    _description = "计量单位列表"

    uom_groups_id = fields.Many2one("roke.uom.groups", string="多计量组")
    uom_id = fields.Many2one("roke.uom", string="计量单位", required=True)
    uom_grade = fields.Selection([('辅计量1', '辅计量1'), ('辅计量2', '辅计量2')],
                                 string="计量等级")
    conversion = fields.Float(string="换算关系", digits='Stock')
    note = fields.Char(string="换算说明")

    @api.onchange('uom_groups_id')
    def _onchange_uom_groups_id(self):
        if len(self.uom_groups_id.uom_line_ids) > 2:
            raise UserError('目前辅计量不支持超出二种计量单位')

    @api.onchange('uom_id')
    def _onchange_uom_id(self):
        """
        防止重复选择同一个计量单位
        """
        uom_ids = self.uom_groups_id.uom_line_ids.uom_id.mapped('id')
        return {
            'domain': {'uom_id': [('id', 'not in', uom_ids)]}
        }

    @api.constrains('conversion')
    def _check_conversion(self):
        for rec in self:
            if rec.conversion <= 0:
                raise UserError('换算关系不能小于等于0')
