<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--多计量组-->
        <record id="roke_group_uom_groups" model="ir.module.category">
            <field name="name">多计量组</field>
            <field name="sequence">1120</field>
        </record>
        <record id="group_roke_uom_groups_read" model="res.groups">
            <field name="name">查看</field>
            <field name="sequence">1</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_group_uom_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_uom_groups_create" model="res.groups">
            <field name="name">创建</field>
            <field name="sequence">2</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_group_uom_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_uom_groups_update" model="res.groups">
            <field name="name">编辑</field>
            <field name="sequence">3</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_group_uom_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_uom_groups_delete" model="res.groups">
            <field name="name">删除</field>
            <field name="sequence">4</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_group_uom_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <!--辅计量仓库库存-->
        <record id="roke_stock_auxiliary_quant_groups" model="ir.module.category">
            <field name="name">辅计量仓库库存</field>
            <field name="sequence">1120</field>
        </record>
        <record id="group_roke_stock_auxiliary_quant_groups_read" model="res.groups">
            <field name="name">查看</field>
            <field name="sequence">1</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_stock_auxiliary_quant_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_stock_auxiliary_quant_groups_create" model="res.groups">
            <field name="name">创建</field>
            <field name="sequence">2</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_stock_auxiliary_quant_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_stock_auxiliary_quant_groups_update" model="res.groups">
            <field name="name">编辑</field>
            <field name="sequence">3</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_stock_auxiliary_quant_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
        <record id="group_roke_stock_auxiliary_quant_groups_delete" model="res.groups">
            <field name="name">删除</field>
            <field name="sequence">4</field>
            <field name="category_id" ref="roke_mes_auxiliary_uom.roke_stock_auxiliary_quant_groups"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin')), (4, ref('base.default_user'))]"/>
        </record>
    </data>
</odoo>