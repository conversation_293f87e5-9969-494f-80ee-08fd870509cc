odoo.define('roke_mes_auxiliary_uom.AbstractController', function (require) {
    "use strict";

    const core = require('web.core')
    const config = require('web.config');

    const AbstractController = require('web.AbstractController')

    AbstractController.include({


        willStart: function () {
            return Promise.all([this._super.apply(this, arguments), this._getPrecisions()]);
        },

        _getPrecisions: function () {
            return this._rpc({
                model: 'decimal.precision',
                method: 'search_read',
                fields: ['name', 'digits'],
            }).then(function (result) {
                const data = {};
                result.forEach((item) => {
                    data[item.name] = item.digits;
                });
                window.x_precision = data;
            });
        }

    })
})

