odoo.define('roke_mes_auxiliary_uom.auxiliary_input', function (require) {
    "use strict";

    var AbstractField = require('web.AbstractField');
    var core = require('web.core');
    var field_registry = require('web.field_registry');
    var QWeb = core.qweb;

    var AuxiliaryInput = AbstractField.extend({
        resetOnAnyFieldChange: true,
        custom_events: _.extend({}, AbstractField.prototype.custom_events, {}),

        events: _.extend({}, AbstractField.prototype.events, {
            'click .z_popover': 'showPopover',
        }),

        init: function () {
            this._super.apply(this, arguments);
        },

        _getPrecision: function (flag) {
            return this._rpc({
                model: 'decimal.precision',
                method: 'precision_get',
                args: [flag]
            }).then(function (result) {
                return result;
            });
        },

        showPopover: async function (e) {
            let self = this;
            await this._getNewState();
            const target = $(e.currentTarget);
            $(".o_data_row .popover").popover("hide");
            if (this.viewType === 'list') {
                var container = target.closest(".o_selected_row");
            } else {
                var container = target.closest("td");
            }

            target.popover({
                container: container,
                trigger: 'click',
                html: true,
                placement: 'top',
                content: QWeb.render("roke_mes_auxiliary_uom.auxiliaryPopover"),
            }).on('shown.bs.popover', async () => {
                await self._getNewState();
                var default_value = 0;
                let $content = QWeb.render("roke_mes_auxiliary_uom.PopoverContent", {
                    main_qty: self.z_main_qty ? self.z_main_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision),
                    main_uom: self.z_main_uom,
                    aux1_qty: self.z_aux1_qty ? self.z_aux1_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision),
                    aux1_uom: self.z_aux1_uom,
                    aux2_qty: self.z_aux2_qty ? self.z_aux2_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision),
                    aux2_uom: self.z_aux2_uom,
                })
                $(".edit_div_box").replaceWith($content);
                $(".main_uom_id").val(self.z_main_qty ? self.z_main_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision));
                $(".aux1_uom_id").val(self.z_aux1_qty ? self.z_aux1_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision));
                $(".aux2_uom_id").val(self.z_aux2_qty ? self.z_aux2_qty.toFixed(self.z_precision) : default_value.toFixed(self.z_precision));

                $('.z_popover_comfirm').on('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    self.data_json.main_qty = $(".main_uom_id").val() === "-" ? 0 : $(".main_uom_id").val();
                    self.data_json.aux1_qty = $(".aux1_uom_id").val() === "-" ? 0 : $(".aux1_uom_id").val();
                    self.data_json.aux2_qty = $(".aux2_uom_id").val() === "-" ? 0 : $(".aux2_uom_id").val();
                    self._setValue(JSON.stringify(self.data_json));
                    $(".popover").popover("hide");
                });

                $(".o_aux_input").on('click', (ev) => {
                    ev.preventDefault();
                    if (this.is_real_time_calculations) {
                        let current = $(ev.target);
                        $('.o_aux_input').not(current).val('');
                    }
                });

                $('.o_aux_input').off('input propertychange').on('input propertychange', function (ev) {
                    ev.preventDefault();
                    ev.stopPropagation();
                    var inputValue = $(ev.target).val();
                    var f_value = inputValue;

                    if (!/^[0-9]*\.?[0-9]*$/.test(f_value)) {
                        f_value = f_value.replace(/[^\d.]/g, '')
                    }
                    if (f_value.indexOf('.') !== -1 && f_value.split('.').length > 2) {
                        f_value = f_value.replace(/(\..*)\./g, '$1')
                    }
                    if (f_value.split('.').length > 1 && f_value.split('.')[1].length > self.z_precision) {
                        f_value = parseFloat(f_value).toFixed(self.z_precision)
                    }
                    f_value = `${self.z_negative ? '-' : ''}${f_value}`
                    $(ev.target).val(f_value);
                });

            }).popover("show");
        },

        commitChanges: function () {
            if (this.mode === 'edit') {
                let res = this._super.apply(this, arguments);
                this.data_json.main_qty = this.$el.find(".main_uom_id").val() === "-" ? 0 : this.$el.find(".main_uom_id").val();
                this.data_json.aux1_qty = this.$el.find(".aux1_uom_id").val() === "-" ? 0 : this.$el.find(".aux1_uom_id").val();
                this.data_json.aux2_qty = this.$el.find(".aux2_uom_id").val() === "-" ? 0 : this.$el.find(".aux2_uom_id").val();
                if (!this.data_json.length) {
                    return res;
                }
                // console.log(" **** commitChanges ", this.mode, JSON.stringify(this.data_json));
                this._setValue(JSON.stringify(this.data_json));
                return res;
            }
        },

        _getNewState: async function () {
            this.data_json = JSON.parse(this.value || "{}");
            // Option 传入的指定字段，防止字段不同无法统一处理
            this.z_product_field = this.nodeOptions.product_filed;

            this.z_main_qty_field = this.nodeOptions.main_qty_field;
            this.z_aux1_qty_field = this.nodeOptions.aux1_qty_field;
            this.z_aux2_qty_field = this.nodeOptions.aux2_qty_field;

            this.z_main_uom_field = this.nodeOptions.main_uom_field;
            this.z_aux1_uom_field = this.nodeOptions.aux1_uom_field;
            this.z_aux2_uom_field = this.nodeOptions.aux2_uom_field;

            this.z_negative = this.nodeOptions.negative;

            // try {
            //     this.z_precision = await this._getPrecision(this.nodeOptions.precision);
            // }
            // catch {
            //     this.z_precision = 2;
            // }
            
            try {
                if ('x_precision' in window){
                    this.z_precision = window.x_precision[this.nodeOptions.precision]
                }else{
                    this.z_precision = await this._getPrecision(this.nodeOptions.precision);
                    window.x_precision[this.nodeOptions.precision] = this.z_precision;
                }
            }
            catch {
                this.z_precision = 2;
            }
            
            this.z_product_id = this.record.data[this.z_product_field] ? this.record.data[this.z_product_field].data : null;

            this.z_main_qty = this.record.data[this.z_main_qty_field] ? this.record.data[this.z_main_qty_field] : 0.00;
            this.z_aux1_qty = this.record.data[this.z_aux1_qty_field] ? this.record.data[this.z_aux1_qty_field] : 0.00;
            this.z_aux2_qty = this.record.data[this.z_aux2_qty_field] ? this.record.data[this.z_aux2_qty_field] : 0.00;

            this.z_main_uom = this.record.data[this.z_main_uom_field] ? this.record.data[this.z_main_uom_field].data : null;
            this.z_aux1_uom = this.record.data[this.z_aux1_uom_field] ? this.record.data[this.z_aux1_uom_field].data : null;
            this.z_aux2_uom = this.record.data[this.z_aux2_uom_field] ? this.record.data[this.z_aux2_uom_field].data : null;

            this.is_real_time_calculations = this.record.data.is_real_time_calculations || false;

            if (!this.is_real_time_calculations && this.value) {
                this.data_json = JSON.parse(this.value || "{}");
                this.z_main_qty = parseFloat(this.data_json.main_qty || 0.00);
                this.z_aux1_qty = parseFloat(this.data_json.aux1_qty || 0.00);
                this.z_aux2_qty = parseFloat(this.data_json.aux2_qty || 0.00);
            }
        },

        _getUomText: function () {
            let uom_text = "";
            if (this.z_main_uom) {
                uom_text += `${parseFloat(this.z_main_qty || 0.0).toFixed(this.z_precision || 2)} ${this.z_main_uom.display_name}`
            }
            if (this.z_aux1_uom) {
                uom_text += ` ${parseFloat(this.z_aux1_qty || 0.0).toFixed(this.z_precision || 2)} ${this.z_aux1_uom.display_name}`
            }
            if (this.z_aux2_uom) {
                uom_text += ` ${parseFloat(this.z_aux2_qty || 0.0).toFixed(this.z_precision || 2)} ${this.z_aux2_uom.display_name}`
            }
            return uom_text;
        },

        _renderEdit: async function () {
            let self = this;
            await this._getNewState();
            this.$el.empty();
            let uom_text = this._getUomText();
            let show_button = this.z_product_id ? true : false;
            this.$el.append(QWeb.render('roke_mes_auxiliary_uom.auxiliary_input1', {
                uom_text: uom_text, show_button: show_button
            }));
        },

        _renderReadonly: async function () {
            await this._getNewState();
            $('.popover').popover("hide");
            this.$el.empty();
            let uom_text = this._getUomText();
            this.$el.append(QWeb.render('roke_mes_auxiliary_uom.auxiliary_input1', {
                uom_text: uom_text, show_button: false
            }));
        },
    });
    field_registry.add('auxiliary_input', AuxiliaryInput);
});