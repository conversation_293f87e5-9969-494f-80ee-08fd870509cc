<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="roke_mes_auxiliary_uom.PopoverContent">
        <div class="edit_div_box" style="display: flex;flex-direction: row;">
            <div t-if="main_uom" class="aux_div">
                <input class="main_uom_input o_aux_input o_input main_uom_id" style="width: 80px; height: 24px;" name="main_qty" />
                <span class="uom_span" style="margin-left: 2px; margin-right: 2px;" t-esc="main_uom.display_name"/>
            </div>
            <div t-if="aux1_uom" class="aux_div">
                <input class="aux1_uom_input o_aux_input o_input aux1_uom_id" style="width: 80px; height: 24px;" name="aux1_qty"/>
                <span style="margin-left: 2px; margin-right: 2px;" t-esc="aux1_uom.display_name"/>
            </div>
            <div t-if="aux2_uom" class="aux_div">
                <input class="aux2_uom_input o_aux_input o_input aux2_uom_id" style="width: 80px; height: 24px;" name="aux2_qty" />
                <span style="margin-left: 2px; margin-right: 2px;" t-esc="aux2_uom.display_name"/>
            </div>
            <div style="flex: 1 1 1; display: flex;">
                <button class="btn btn-sm btn-primary z_popover_comfirm">确认</button>
            </div>
        </div>
    </t>

    <t t-name="roke_mes_auxiliary_uom.auxiliaryPopover">
        <div class="edit_div_box" style="display: flex;flex-direction: row;"/>
    </t>

    <t t-name="roke_mes_auxiliary_uom.auxiliary_input1">
        <div style="display: flex;flex-direction: row; position: relative; align-items: center;">
            <div style="flex: 1 1 0; display: flex;" t-esc="uom_text"/>
            <div t-if="show_button" style="flex: 1 1 1; display: flex; position: absolute; right: 0">
                <button class="btn btn-sm btn-primary z_popover">
                    <i class="fa fa-pencil-square-o"/>
                </button>
            </div>
        </div>
    </t>
</templates>
