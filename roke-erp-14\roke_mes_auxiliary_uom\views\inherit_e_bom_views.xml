<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_au_inherit_e_bom_line_editable_tree" model="ir.ui.view">
        <field name="name">au.inherit.e_bom.line.editable.tree</field>
        <field name="model">roke.mes.e_bom.line</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_mes_e_bom_line_editable_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="uom_id" readonly="1" force_save="1"/>
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" class="oe_edit_only"/>
            </xpath>
        </field>
    </record>
    <record id="view_au_inherit_e_bom_line_tree" model="ir.ui.view">
        <field name="name">au.inherit.e_bom.line.tree</field>
        <field name="model">roke.mes.e_bom.line</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_mes_e_bom_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="uom_id" readonly="1" force_save="1"/>
                <field name="is_real_time_calculations" invisible="1"/>
                <field name="uom_id" invisible="1"/>
                <field name="auxiliary1_qty" optional="show" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" optional="show" readonly="1" force_save="1"/>
                <field name="auxiliary2_qty" optional="show" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show" readonly="1" force_save="1"/>
            </xpath>
        </field>
    </record>
</odoo>
