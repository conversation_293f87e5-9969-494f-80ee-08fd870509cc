<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工单-->
    <record id="view_auxiliary_uom_inherit_entrust_order_tree_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.entrust.order.tree</field>
        <field name="model">roke.work.order</field>
        <field name="inherit_id" ref="roke_mes_entrust_order.new_view_roke_entrust_order_tree"/>
        <field name="arch" type="xml">
            <!--计划数量处理-->
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="plan_auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1"/>
                <field name="plan_auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1"/>
            </xpath>
            <!--完工数量-->
            <xpath expr="//field[@name='finish_qty']" position="after">
                <field name="finish_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="finish_auxiliary2_qty" readonly="1" force_save="1"/>
            </xpath>
            <!--不合格数量-->
            <xpath expr="//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="unqualified_auxiliary2_qty" readonly="1" force_save="1"/>
            </xpath>
        </field>
    </record>

    <record id="view_auxiliary_uom_inherit_view_roke_entrust_order_edit_tree" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.entrust.order.edit.tree</field>
        <field name="model">roke.work.order</field>
        <field name="inherit_id" ref="roke_mes_entrust_order.new_view_roke_entrust_order_edit_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="plan_auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1"/>
                <field name="plan_auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1"/>
            </xpath>
            <!--完工数量-->
            <xpath expr="//field[@name='finish_qty']" position="after">
                <field name="finish_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="finish_auxiliary2_qty" readonly="1" force_save="1"/>
            </xpath>
            <!--不合格数量-->
            <xpath expr="//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="unqualified_auxiliary2_qty" readonly="1" force_save="1"/>
            </xpath>
        </field>
    </record>

    <record id="view_auxiliary_uom_inherit_entrust_order_form_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.entrust.order.form</field>
        <field name="model">roke.work.order</field>
        <field name="inherit_id" ref="roke_mes_entrust_order.new_view_roke_entrust_order_form"/>
        <field name="arch" type="xml">
            <!--计划数量-->
            <xpath expr="//div[@name='plan_qty']" position="replace">
                <div name="plan_qty" class="o_row">
                    <field name="plan_qty" required="1" attrs="{'readonly': [('state', '=', '已完工')]}"
                           class="oe_edit_only"/>
                    <span name="plan_uom" class="oe_edit_only">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="plan_auxiliary1_qty" class="oe_edit_only"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="plan_uom1" class="oe_edit_only">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="plan_auxiliary2_qty" class="oe_edit_only"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="plan_uom2" class="oe_edit_only">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                    <field name="plan_uom_info" class="oe_read_only"/>
                </div>
            </xpath>
            <!--完成数量-->
            <xpath expr="//div[@name='finish_qty']" position="replace">
                <div name="finish_qty" class="o_row">
                    <field name="finish_qty" invisible="1"/>
                    <field name="finish_auxiliary1_qty" invisible="1"/>
                    <field name="finish_auxiliary2_qty" invisible="1"/>
                    <field name="finish_uom_info"/>
                </div>
            </xpath>
            <!--不合格数量-->
            <xpath expr="//div[@name='unqualified_qty']" position="replace">
                <div name="unqualified_qty" class="o_row">
                    <field name="unqualified_qty" invisible="1"/>
                    <field name="unqualified_auxiliary1_qty" invisible="1"/>
                    <field name="unqualified_auxiliary2_qty" invisible="1"/>
                    <field name="unqualified_uom_info"/>
                </div>
            </xpath>
            <!--发料记录-->
            <xpath expr="//field[@name='material_ids']/tree//field[@name='qty']" position="after">
                <field name="uom_id"/>
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1"/>
            </xpath>
            <xpath expr="//field[@name='material_ids']/tree//field[@name='consume_qty']" position="after">
                <field name="consume_auxiliary1_qty"/>
                <field name="consume_auxiliary2_qty"/>
            </xpath>
            <!--收料记录-->
            <xpath expr="//field[@name='record_ids']/tree//field[@name='finish_qty']" position="after">
                <field name="finish_auxiliary1_qty"/>
                <field name="finish_auxiliary2_qty"/>
            </xpath>
            <xpath expr="//field[@name='record_ids']/tree//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty"/>
                <field name="unqualified_auxiliary2_qty"/>
            </xpath>
        </field>
    </record>
</odoo>
