<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_roke_auxiliary_uom_inherit_product_form_view" model="ir.ui.view">
        <field name="name">roke.auxiliary.uom.inherit.product.form</field>
        <field name="model">roke.product</field>
        <field name="inherit_id" ref="roke_mes_base.view_roke_product_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary_uom1_id" readonly="1" force_save="1"
                       invisible="1"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1"
                       invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='uom_id']" position="before">
                <field name="uom_type" required="1"/>
                <field name="uom_groups_id"
                       attrs="{'invisible': [('uom_type', '=', '单计量')], 'required': [('uom_type', '=', '多计量')]}"/>
                <field name="is_free_conversion" invisible="1"/>
                <field name="is_real_time_calculations" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>
