<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="uom_inherit_view_pt_material_demand_tree" model="ir.ui.view">
        <field name="name">uom.inherit.roke.pt.material.demand.tree</field>
        <field name="model">roke.pt.material.demand</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_pt_material_demand_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="after">
                <field name="uom_id"/>
                <field name="demand_auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id"/>
                <field name="demand_auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="uom_inherit_view_pt_material_demand_form" model="ir.ui.view">
        <field name="name">uom.inherit.roke.pt.material.demand.form</field>
        <field name="model">roke.pt.material.demand</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_pt_material_demand_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="replace">
                <label for="demand_qty"/>
                <div name="demand_qty" class="o_row">
                    <field name="demand_qty" required="1"
                           class="oe_edit_only"/>
                    <span name="demand_uom" class="oe_edit_only">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="demand_auxiliary1_qty" class="oe_edit_only"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="demand_uom1" class="oe_edit_only">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="demand_auxiliary2_qty" class="oe_edit_only"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="demand_uom2" class="oe_edit_only">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                    <field name="demand_uom_info" class="oe_read_only"/>
                </div>
            </xpath>
        </field>
    </record>
    <!--生产任务处-->
    <record id="uom_inherit_view_pt_material_demand_task_form" model="ir.ui.view">
        <field name="name">uom.inherit.roke.pt.material.demand.task.form</field>
        <field name="model">roke.production.task</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_material_enterprise_inherit_pt_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='pt_demand_ids']/tree/field[@name='demand_qty']" position="after">
                <field name="uom_id"/>
                <field name="demand_auxiliary1_qty"/>
                <field name="auxiliary_uom1_id"/>
                <field name="demand_auxiliary2_qty"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
        </field>
    </record>
    <record id="uom_inherit_view_stock_pt_material_demand_task_form" model="ir.ui.view">
        <field name="name">uom.inherit.stock.pt.material.demand.task.form</field>
        <field name="model">roke.production.task</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_stock_inherit_pt_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='stock_qty']" position="after">
                <field name="uom_id"/>
                <field name="stock_auxiliary1_qty"/>
                <field name="auxiliary_uom1_id"/>
                <field name="stock_auxiliary2_qty"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
        </field>
    </record>

</odoo>
