<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="inherit_view_roke_mes_assemble_disassemble_form" model="ir.ui.view">
        <field name="name">inherit.roke.mes.assemble.disassemble.form</field>
        <field name="model">roke.mes.assemble.disassemble</field>
        <field name="inherit_id" ref="roke_mes_assemble_disassemble.view_roke_mes_assemble_disassemble_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id', '=', False)]}" force_save="1"/>
                <field name="auxiliary_uom1_id"/>
                <field name="auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom2_id', '=', False)]}" force_save="1"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id', '=', False)]}" force_save="1"/>
                <field name="auxiliary_uom1_id"/>
                <field name="auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom2_id', '=', False)]}" force_save="1"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
        </field>
    </record>
</odoo>
