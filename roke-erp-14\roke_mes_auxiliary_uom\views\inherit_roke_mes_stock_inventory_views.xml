<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="inherit_aux_line_roke_mes_stock_inventory_sheet_tree" model="ir.ui.view">
        <field name="name">inherit.aux.roke.mes.stock.inventory.sheet.line.tree</field>
        <field name="model">roke.mes.stock.inventory.sheet.line</field>
        <field name="inherit_id" ref="roke_mes_stock.line_roke_mes_stock_inventory_sheet_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="sheet_auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                <field name="auxiliary_uom1_id" invisible="1"/>
                <field name="sheet_auxiliary2_qty" force_save="1"
                       attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" invisible="1"/>
                <field name="is_real_time_calculations" invisible="1"/>
                <!--                <field name="is_balance_calculation" invisible="1"/>-->
                <!--                <field name="sheet_auxiliary_json" widget="auxiliary_input"-->
                <!--                       options="{'precision': 'KCSL','product_filed': 'product_id', 'main_qty_field': 'qty', 'main_uom_field': 'uom_id', 'aux1_qty_field': 'sheet_auxiliary1_qty', 'aux1_uom_field': 'auxiliary_uom1_id', 'aux2_qty_field': 'sheet_auxiliary2_qty', 'aux2_uom_field': 'auxiliary_uom2_id'}"/>-->
            </xpath>
            <!--            <xpath expr="//field[@name='qty']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <xpath expr="//field[@name='sys_qty']" position="after">
                <field name="system_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="auxiliary_uom1_id" invisible="1"/>
                <field name="system_auxiliary2_qty" force_save="1"
                       readonly="1"/>
                <field name="auxiliary_uom2_id" invisible="1"/>
                <field name="is_real_time_calculations" invisible="1"/>
                <!--                <field name="system_auxiliary_json" widget="auxiliary_input" readonly="1" force_save="1"-->
                <!--                       options="{'precision': 'KCSL','product_filed': 'product_id', 'main_qty_field': 'sys_qty', 'main_uom_field': 'uom_id', 'aux1_qty_field': 'system_auxiliary1_qty', 'aux1_uom_field': 'auxiliary_uom1_id', 'aux2_qty_field': 'system_auxiliary2_qty', 'aux2_uom_field': 'auxiliary_uom2_id'}"/>-->
            </xpath>
            <!--            <xpath expr="//field[@name='sys_qty']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
            <xpath expr="//field[@name='difference']" position="after">
                <field name="difference_auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="auxiliary_uom1_id" invisible="1"/>
                <field name="difference_auxiliary2_qty" force_save="1"
                       readonly="1"/>
                <field name="auxiliary_uom2_id" invisible="1"/>
                <field name="is_real_time_calculations" invisible="1"/>
                <!--                <field name="difference_auxiliary_json" widget="auxiliary_input" readonly="1" force_save="1"-->
                <!--                       options="{'precision': 'KCSL','product_filed': 'product_id', 'main_qty_field': 'difference', 'main_uom_field': 'uom_id', 'aux1_qty_field': 'difference_auxiliary1_qty', 'aux1_uom_field': 'auxiliary_uom1_id', 'aux2_qty_field': 'difference_auxiliary2_qty', 'aux2_uom_field': 'auxiliary_uom2_id'}"/>-->
            </xpath>
            <!--            <xpath expr="//field[@name='difference']" position="attributes">-->
            <!--                <attribute name="invisible">1</attribute>-->
            <!--            </xpath>-->
        </field>
    </record>
</odoo>
