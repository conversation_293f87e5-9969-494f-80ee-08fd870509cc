<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="inherit_aux_view_roke_mes_detailed_work_move_line_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.detailed.work.move.line.tree</field>
        <field name="model">roke.mes.stock.move.line</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_detailed_work_move_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_purchase_rec_move_line_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.purchase.rec.move.line.tree</field>
        <field name="model">roke.mes.stock.move.line</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_move_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_sale_deliver_move_line_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.sale.deliver.move.line.tree</field>
        <field name="model">roke.mes.stock.move.line</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_move_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_general_in_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.general.in.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_in_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_general_out_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.general.out.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_out_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_general_transfer_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.general.transfer.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_transfer_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_purchase_receiving_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.purchase.receiving.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_receiving_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_purchase_return_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.purchase.return.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_mes_purchase_return_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_sale_deliver_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.purchase.receiving.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_deliver_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="inherit_aux_view_roke_mes_sale_return_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.view.roke.mes.purchase.return.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_mes_sale_return_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_aux_view_roke_mes_general_red_out_move_tree" model="ir.ui.view">
        <field name="name">roke.mes.stock.general.red.out.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_red_out_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_aux_view_roke_mes_general_red_in_move_tree" model="ir.ui.view">
        <field name="name">inherit.aux.roke.mes.stock.general.red.in.move.tree</field>
        <field name="model">roke.mes.stock.move</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_general_red_in_move_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}" optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>

</odoo>
