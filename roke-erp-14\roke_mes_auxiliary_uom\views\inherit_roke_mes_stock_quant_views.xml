<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="inherit_view_roke_mes_stock_quant_tree" model="ir.ui.view">
        <field name="name">roke.mes.stock.quant.tree</field>
        <field name="model">roke.mes.stock.quant</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_stock_quant_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">

                <field name="auxiliary1_qty" sum="辅数量1合计" optional="show"/>
                <field name="auxiliary_uom1_id"/>
                <field name="auxiliary2_qty" sum="辅数量2合计" optional="show"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
            <xpath expr="//field[@name='reserved_quantity']" position="after">
                <field name="reserved_auxiliary1_qty" sum="已占用辅数量1" optional="hide"/>
                <field name="reserved_auxiliary2_qty" sum="已占用辅数量2" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='inventory_quantity']" position="after">
                <field name="inventory_auxiliary1_qty" sum="可用库存辅数量1" optional="hide"/>
                <field name="inventory_auxiliary2_qty" sum="可用库存辅数量1" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!--form-->
    <record id="inherit_view_roke_mes_stock_quant_form" model="ir.ui.view">
        <field name="name">roke.mes.stock.quant.form</field>
        <field name="model">roke.mes.stock.quant</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_stock_quant_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="auxiliary1_qty"/>
                <field name="auxiliary2_qty"/>
                <!--                <label for="auxiliary1_qty" string="辅数量1"/>-->
                <!--                <div name="auxiliary1_qty" class="o_row">-->
                <!--                    <field name="auxiliary1_qty"/>-->
                <!--                    <span>-->
                <!--                        <field name="auxiliary_uom1_id"/>-->
                <!--                    </span>-->
                <!--                </div>-->
                <!--                <label for="auxiliary2_qty" string="辅数量2"/>-->
                <!--                <div name="auxiliary2_qty" class="o_row">-->
                <!--                    <field name="auxiliary2_qty"/>-->
                <!--                    <span>-->
                <!--                        <field name="auxiliary_uom2_id"/>-->
                <!--                    </span>-->
                <!--                </div>-->
            </xpath>
            <xpath expr="//notebook//page//field[@name='in_move_line_ids']//tree//field[@name='qty']" position="after">
                <field name="uom_id"/>
                <field name="auxiliary1_qty"/>
                <field name="auxiliary_uom1_id"/>
                <field name="auxiliary2_qty"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
            <xpath expr="//notebook//page//field[@name='out_move_line_ids']//tree//field[@name='qty']" position="after">
                <field name="uom_id"/>
                <field name="auxiliary1_qty"/>
                <field name="auxiliary_uom1_id"/>
                <field name="auxiliary2_qty"/>
                <field name="auxiliary_uom2_id"/>
            </xpath>
        </field>
    </record>

    <!--pivot-->
    <record model="ir.ui.view" id="inherit_view_roke_mes_stock_quant_pivot">
        <field name="name">roke.mes.stock.quant.pivot</field>
        <field name="model">roke.mes.stock.quant</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_mes_stock_quant_pivot"/>
        <field name="arch" type="xml">
            <xpath expr="//pivot//field[@name='qty']" position="after">
                <field name="auxiliary1_qty" type="measure"/>
                <field name="auxiliary2_qty" type="measure"/>
            </xpath>
        </field>
    </record>
</odoo>
