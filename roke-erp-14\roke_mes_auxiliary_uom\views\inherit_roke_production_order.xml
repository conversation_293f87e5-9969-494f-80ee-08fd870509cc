<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="inherit_view_roke_production_order_line_tree_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.order.line.tree</field>
        <field name="model">roke.production.order.line</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_order_line_tree"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty"
                       attrs="{'readonly':['|',('auxiliary_uom1_id','=',False),('state', 'in', ['已完工', '强制完工'])]}"
                       optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty"
                       attrs="{'readonly':['|',('auxiliary_uom2_id','=',False),('state', 'in', ['已完工', '强制完工'])]}"
                       optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='finish_qty']" position="after">
                <field name="finish_auxiliary1_qty"
                       readonly="1" force_save="1"
                       optional="show"/>
                <field name="finish_auxiliary2_qty"
                       readonly="1" force_save="1"
                       optional="show"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="inherit_view_roke_production_order_line_form_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.order.line.form</field>
        <field name="model">roke.production.order.line</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_order_line_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='qty']" position="replace">

                <div name="qty" class="o_row">
                    <field name="qty" required="1" attrs="{'readonly': [('task_id', '!=', False)]}"/>
                    <span name="qty_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)], 'readonly': [('task_id', '!=', False)]}"
                           force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)], 'readonly': [('task_id', '!=', False)]}"
                           force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//div[@name='finish_qty']" position="replace">
                <div name="finish_qty" class="o_row">
                    <field name="finish_qty" readonly="1"/>
                    <span name="finish_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary1_qty"
                           readonly="1"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="finish_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary2_qty"
                           readonly="1"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="finish_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
    <!--增加计划数-->
    <record id="inherit_view_roke_po_add_plan_qty_wizard_form_uom" model="ir.ui.view">
        <field name="name">inherit.uom..po.add.plan.qty.wizard.form</field>
        <field name="type">form</field>
        <field name="model">roke.po.add.plan.qty.wizard</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_po_add_plan_qty_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='origin_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="origin_auxiliary1_qty" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="origin_auxiliary2_qty"
                       optional="show"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='add_qty']" position="after">
                <field name="add_auxiliary1_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"/>
                <field name="add_auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
            </xpath>
            <xpath expr="//field[@name='new_qty']" position="after">
                <field name="new_auxiliary1_qty" optional="show"/>
                <field name="new_auxiliary2_qty" optional="show"/>
            </xpath>
        </field>
    </record>

</odoo>
