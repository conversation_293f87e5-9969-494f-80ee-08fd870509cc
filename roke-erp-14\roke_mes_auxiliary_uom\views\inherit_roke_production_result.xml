<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="inherit_view_roke_production_result_tree_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.result.tree</field>
        <field name="model">roke.production.result</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_result_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="auxiliary1_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='residue']" position="after">
                <field name="residue_auxiliary1_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"/>
                <field name="residue_auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
            </xpath>
            <xpath expr="//field[@name='move_qty']" position="after">
                <field name="move_auxiliary1_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"/>
                <field name="move_auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="inherit_view_roke_production_result_form_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.result.form</field>
        <field name="model">roke.production.result</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_result_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='qty']" position="replace">
                <div name="qty" class="o_row">
                    <field name="qty" required="1" class="oe_edit_only" readonly="1"/>
                    <span name="qty_uom" class="oe_edit_only">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary1_qty" required="1" class="oe_edit_only" readonly="1"
                           attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom1" class="oe_edit_only">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary2_qty" required="1" class="oe_edit_only" readonly="1"
                           attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom2" class="oe_edit_only">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                    <field name="qty_uom_info" class="oe_read_only"/>
                </div>
            </xpath>
            <xpath expr="//div[@name='residue']" position="replace">
                <div name="residue" class="o_row">
                    <field name="residue" required="1" class="oe_edit_only" readonly="1"/>
                    <span name="residue_uom" class="oe_edit_only">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="residue_auxiliary1_qty" required="1" class="oe_edit_only" readonly="1"
                           attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="residue_uom1" class="oe_edit_only">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="residue_auxiliary2_qty" required="1" class="oe_edit_only" readonly="1"
                           attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="residue_uom2" class="oe_edit_only">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                    <field name="residue_uom_info" class="oe_read_only"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='line_ids']/tree//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty"
                       attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty"
                       attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/form//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty"
                       attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary2_qty"
                       attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>

</odoo>
