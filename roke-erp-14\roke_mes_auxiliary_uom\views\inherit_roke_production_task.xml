<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="inherit_view_roke_production_task_form_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.task.form</field>
        <field name="model">roke.production.task</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_task_form"/>
        <field name="arch" type="xml">

            <xpath expr="//div[@name='plan_qty']" position="replace">
                <div name="plan_qty" class="o_row">
                    <field name="plan_qty" required="1" class="oe_edit_only"
                           attrs="{'readonly': ['|',('allow_create_wo', '!=', True),('state', '=', '已完工')]}"/>
                    <span name="plan_uom" class="oe_edit_only">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="plan_auxiliary1_qty" required="1" class="oe_edit_only"
                           attrs="{'readonly': ['|',('allow_create_wo', '!=', True),('state', '=', '已完工')],'invisible':[('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="plan_uom1" class="oe_edit_only">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="plan_auxiliary2_qty" required="1" class="oe_edit_only"
                           attrs="{'readonly': ['|',('allow_create_wo', '!=', True),('state', '=', '已完工')] ,'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="plan_uom2" class="oe_edit_only">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                    <field name="plan_uom_info" class="oe_read_only"/>
                </div>

            </xpath>
            <!--            <xpath expr="//div[@name='plan_qty']" position="after">-->
            <!--                <field name="plan_uom_info" class="oe_read_only"/>-->
            <!--            </xpath>-->
        </field>
    </record>

    <!--tree-->
    <record id="inherit_view_roke_production_task_tree_uom" model="ir.ui.view">
        <field name="name">inherit.uom.roke.production.task.tree</field>
        <field name="model">roke.production.task</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_production_task_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="plan_auxiliary1_qty"
                       attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom1_id" readonly="1" force_save="1" optional="show"/>
                <field name="plan_auxiliary2_qty"
                       attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"
                       optional="show"/>
                <field name="auxiliary_uom2_id" readonly="1" force_save="1" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='finish_qty']" position="after">
                <field name="finish_auxiliary1_qty"
                       attrs="{'readonly':[('auxiliary_uom1_id','=',False)]}"
                       optional="show"/>
                <field name="finish_auxiliary2_qty"
                       attrs="{'readonly':[('auxiliary_uom2_id','=',False)]}"
                       optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
