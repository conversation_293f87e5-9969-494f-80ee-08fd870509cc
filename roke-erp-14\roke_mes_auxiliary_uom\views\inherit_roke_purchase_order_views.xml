<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="inherit_view_roke_purchase_order_detail_edit_tree" model="ir.ui.view">
        <field name="name">inherit.roke.purchase.order.detail.edit.tree</field>
        <field name="model">roke.purchase.order.detail</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_purchase_order_detail_edit_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='wait_receiving']" position="after">
                <field name="wait_receiving_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="wait_receiving_auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"
                       force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
