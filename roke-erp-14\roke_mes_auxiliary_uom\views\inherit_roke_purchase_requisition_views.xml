<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="inherit_view_roke_purchase_requisition_form" model="ir.ui.view">
        <field name="name">inherit.roke.purchase.requisition.form</field>
        <field name="model">roke.purchase.requisition</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_purchase_requisition_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
    <!--tree-->
    <record id="inherit_view_roke_purchase_requisition_line_tree" model="ir.ui.view">
        <field name="name">inherit.roke.purchase.requisition.line.tree</field>
        <field name="model">roke.purchase.requisition.line</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_purchase_requisition_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
