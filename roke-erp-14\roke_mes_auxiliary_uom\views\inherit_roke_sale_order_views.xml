<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="inherit_view_roke_sale_order_line_tree" model="ir.ui.view">
        <field name="name">inherit.roke.sale.order.line.tree</field>
        <field name="model">roke.sale.order.line</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_order_line_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='deliver_qty']" position="after">
                <field name="deliver_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="deliver_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="is_green" invisible="1"/>
                <field name="wait_deliver_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show" decoration-success="is_green"/>
                <field name="wait_deliver_auxiliary2_qty" attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"
                       force_save="1" optional="show" decoration-success="is_green"/>
            </xpath>
        </field>
    </record>
</odoo>
