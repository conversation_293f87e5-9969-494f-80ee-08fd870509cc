<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工单-->
    <record id="view_aux_uom_inherit_table_submit_work_view" model="ir.ui.view">
        <field name="name">aux.uom.inherit.table.submit.work.form</field>
        <field name="model">roke.table.submit.work</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_table_submit_work_form"/>
        <field name="arch" type="xml">
            <!--完工数量-->
            <xpath expr="//field[@name='line_ids']/tree//field[@name='finish_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="finish_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="finish_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/form//field[@name='finish_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="finish_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="finish_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <!--不合格数量-->
            <xpath expr="//field[@name='line_ids']/tree//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="unqualified_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/form//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="unqualified_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
