<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="uom_inherit_view_wo_material_demand_tree" model="ir.ui.view">
        <field name="name">uom.inherit.wo.material.demand.tree</field>
        <field name="model">roke.wo.material.demand</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_wo_material_demand_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='finished_qty']" position="after">
                <field name="finished_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="finished_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
    <record id="uom_inherit_view_wo_material_demand_edit_tree" model="ir.ui.view">
        <field name="name">uom.inherit.wo.material.demand.edit.tree</field>
        <field name="model">roke.wo.material.demand</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_wo_material_demand_edit_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='finished_qty']" position="after">
                <field name="finished_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="finished_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="uom_inherit_view_wo_material_demand_form" model="ir.ui.view">
        <field name="name">uom.inherit.wo.material.demand.form</field>
        <field name="model">roke.wo.material.demand</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_wo_material_demand_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="replace">
                <label for="demand_qty"/>
                <div name="demand_qty" class="o_row">
                    <field name="demand_qty"/>
                    <span name="demand_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="demand_auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="demand_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="demand_auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="demand_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//field[@name='finished_qty']" position="replace">
                <label for="finished_qty"/>
                <div name="finished_qty" class="o_row">
                    <field name="finished_qty"/>
                    <span name="finished_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="finished_auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="finished_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="finished_auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="finished_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
