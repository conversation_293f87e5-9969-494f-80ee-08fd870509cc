<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="uom_inherit_view_wo_material_record_tree" model="ir.ui.view">
        <field name="name">uom.inherit.wo.material.record.tree</field>
        <field name="model">roke.wo.material.record</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_wo_material_record_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
    <!--form-->
    <record id="uom_inherit_view_wo_material_record_form" model="ir.ui.view">
        <field name="name">uom.inherit.wo.material.record.form</field>
        <field name="model">roke.wo.material.record</field>
        <field name="inherit_id" ref="roke_mes_material_enterprise.view_roke_wo_material_record_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="replace">
                <label for="qty"/>
                <div name="qty" class="o_row">
                    <field name="qty"/>
                    <span name="qty_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
