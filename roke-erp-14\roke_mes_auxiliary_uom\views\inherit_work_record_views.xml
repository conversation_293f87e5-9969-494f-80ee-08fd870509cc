<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--报工记录-->
    <record id="view_auxiliary_uom_inherit_work_record_embed_tree_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.work.record.embed.tree</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_work_record_embed_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="finish_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="finish_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="unqualified_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
    <record id="view_auxiliary_uom_inherit_work_record_tree_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.work.record.tree</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_work_record_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='uom_id']" position="after">
                <field name="finish_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="finish_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='unqualified_qty']" position="after">
                <field name="unqualified_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="unqualified_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
    <record id="view_auxiliary_uom_inherit_work_record_form_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.work.record.form</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_work_record_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@name='finish_qty']" position="replace">
                <div name="finish_qty" class="o_row">
                    <field name="finish_qty"/>
                    <span name="finish_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                    <span name="finish_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="finish_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//span[@name='unqualified_uom']" position="after">
                <field name="unqualified_auxiliary1_qty"
                       attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                <span name="unqualified_uom1">
                    <field name="auxiliary_uom1_id" class="uom_width"/>
                </span>
                <field name="unqualified_auxiliary2_qty"
                       attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                       force_save="1"/>
                <span name="unqualified_uom2">
                    <field name="auxiliary_uom2_id" class="uom_width"/>
                </span>
            </xpath>
            <xpath expr="//div[@name='finish_qty']" position="after">
                <label for="invalid_salary_qty"/>
                <div name="invalid_salary_qty" class="o_row">
                    <field name="invalid_salary_qty"/>
                    <span name="invalid_uom">
                        <field name="uom_id" readonly="1" class="uom_width"/>
                    </span>
                    <field name="invalid_auxiliary1_qty"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="invalid_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="invalid_auxiliary2_qty"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="invalid_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
    <!--不计工资数-->
    <record id="view_auxiliary_uom_inherit_salary_wr_tree_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.salary.wr.tree</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_salary.view_roke_salary_inherit_work_record_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invalid_salary_qty']" position="after">
                <field name="invalid_auxiliary1_qty" optional="show"/>
                <field name="invalid_auxiliary2_qty" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="view_auxiliary_uom_inherit_salary_wr_embed_tree_view" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.salary.wr.embed.tree</field>
        <field name="model">roke.work.record</field>
        <field name="inherit_id" ref="roke_mes_salary.view_roke_salary_inherit_work_record_embed_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invalid_salary_qty']" position="after">
                <field name="invalid_auxiliary1_qty" optional="show"/>
                <field name="invalid_auxiliary2_qty" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
