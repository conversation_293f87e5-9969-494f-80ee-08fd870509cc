<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--辅计量仓库库存-->
    <!--search-->
    <!--<record id="roke_stock_auxiliary_quant_search" model="ir.ui.view">
        <field name="name">roke.stock.auxiliary.quant.search</field>
        <field name="model">roke.stock.auxiliary.quant</field>
        <field name="arch" type="xml">
            <search string="多计量仓库库存">
                <field name="product_id"/>
                <field name="location_id"/>
                <field name="lot_id"/>
                <field name="quant_date"/>
                <filter string="内部位置" name="内部位置" domain="[('location_type', '=', '内部位置')]"/>
                <filter string="生产位置" name="生产位置" domain="[('location_type', '=', '生产位置')]"/>
                <group expand="0" string="Group By">
                    <filter string="位置" name="groupby_location_id" context="{'group_by':'location_id'}"/>
                    <filter string="产品" name="groupby_product_id" context="{'group_by':'product_id'}"/>
                    <filter string="批次序列号码" name="groupby_lot_id" context="{'group_by':'lot_id'}"/>
                    <filter string="计量单位" name="groupby_uom_id" context="{'group_by':'uom_id'}"/>
                </group>
            </search>
        </field>
    </record>
    &lt;!&ndash;tree&ndash;&gt;
    <record id="roke_stock_auxiliary_quant_tree" model="ir.ui.view">
        <field name="name">roke.stock.auxiliary.quant.tree</field>
        <field name="model">roke.stock.auxiliary.quant</field>
        <field name="arch" type="xml">
            <tree string="多计量仓库库存">
                <field name="location_type" invisible="1"/>
                <field name="product_id"/>
                <field name="location_id"/>
                <field name="lot_id"/>
                <field name="qty" sum="合计" optional="show"/>&lt;!&ndash;&ndash;&gt;
                <field name="uom_id"/>
                &lt;!&ndash;<field name="reserved_quantity" sum="占用合计" optional="show"/>
                <field name="inventory_quantity" sum="可用合计" optional="show"/>&ndash;&gt;
            </tree>
        </field>
    </record>
    &lt;!&ndash;action&ndash;&gt;
    <record id="view_roke_stock_auxiliary_quant_action" model="ir.actions.act_window">
        <field name="name">多计量仓库库存</field>
        <field name="res_model">roke.stock.auxiliary.quant</field>
        <field name="view_mode">tree</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">
            {
            'create': False,
            'edit': False,
            'delete': False,
            'search_default_内部位置': True,
            'search_default_groupby_product_id': True,
            'search_default_groupby_location_id': True,
            'search_default_groupby_lot_id': True
            }
        </field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            当库存调拨单据执行后在此功能进行查看仓库库存。
          </p>
        </field>
    </record>

access_roke_stock_auxiliary_quant_r,roke_stock_auxiliary_quant_r,model_roke_stock_auxiliary_quant,base.group_user,1,0,0,0
access_roke_stock_auxiliary_quant_c,roke_stock_auxiliary_quant_c,model_roke_stock_auxiliary_quant,roke_mes_auxiliary_uom.group_roke_stock_auxiliary_quant_groups_create,0,0,1,0
access_roke_stock_auxiliary_quant_u,roke_stock_auxiliary_quant_u,model_roke_stock_auxiliary_quant,roke_mes_auxiliary_uom.group_roke_stock_auxiliary_quant_groups_update,0,1,0,0
access_roke_stock_auxiliary_quant_d,roke_stock_auxiliary_quant_d,model_roke_stock_auxiliary_quant,roke_mes_auxiliary_uom.group_roke_stock_auxiliary_quant_groups_update,0,0,0,1
    -->
</odoo>
