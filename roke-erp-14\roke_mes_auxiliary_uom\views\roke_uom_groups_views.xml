<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--多计量组-->
    <!--search-->
    <record id="view_roke_uom_groups_search" model="ir.ui.view">
        <field name="name">roke.uom.groups.search</field>
        <field name="model">roke.uom.groups</field>
        <field name="arch" type="xml">
            <search string="多计量组">
                <field name="name"/>
                <field name="code"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_uom_groups_tree" model="ir.ui.view">
        <field name="name">roke.uom.groups.tree</field>
        <field name="model">roke.uom.groups</field>
        <field name="arch" type="xml">
            <tree string="多计量组">
                <field name="code"/>
                <field name="mnemonic_aid"/>
                <field name="name"/>
                <field name="note"/>
                <field name="create_uid" string="创建人" optional="show"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_uom_groups_form" model="ir.ui.view">
        <field name="name">roke.uom.groups.form</field>
        <field name="model">roke.uom.groups</field>
        <field name="arch" type="xml">
            <form string="多计量组">
                <sheet>
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <group id="g1">
                        <group>
                            <group>
                                <field name="code"/>
                                <field name="is_free_conversion"
                                   force_save="1"/>
                            </group>
                            <group>
                                <field name="main_uom_id"/>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="name"/>
                            </group>
                            <group>
                                <field name="active" widget="boolean_toggle"/>
                            </group>
                        </group>
                    </group>
                    <group id="g2">
                        <field name="note" nolabel="1" placeholder="此处可以填写备注或描述" />
                    </group>
                    <notebook>
                        <page string="计量单位组合" name="uom_line_ids">
                            <field name="uom_line_ids">
                                <tree editable="bottom">
                                    <field name="uom_groups_id" invisible="1"/>
                                    <field name="uom_id"/>
                                    <field name="uom_grade" required="1"/>
                                    <field name="conversion" force_save="1"/>
                                    <field name="note" optional="show"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <div name="message" class="alert alert-info" role="alert" style="margin-bottom:0px;">
                        --计量组：一组计量内不允许有重复的计量单位<br/>
                        --换算说明：比如”米“为主计量单位，如果”卷“的换算关系填写3；那么即为【1米==3卷】
                    </div>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_uom_groups_action" model="ir.actions.act_window">
        <field name="name">多计量组</field>
        <field name="res_model">roke.uom.groups</field>
        <field name="view_mode">tree,form</field>
        <field name="form_view_id" ref="view_roke_uom_groups_form"/>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个多计量组。
          </p><p>
            或者您也可以选择批量导入功能一次性导入多个多计量组。
          </p>
        </field>
    </record>

</odoo>
