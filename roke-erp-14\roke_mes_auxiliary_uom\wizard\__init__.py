# -*- coding: utf-8 -*-
from . import inherit_create_work_record_wizard
from . import inherit_picking_move_qty
from . import inherit_stock_query_census
from . import inherit_stock_rds_quant
from . import inherit_picking_all_move
from . import inherit_stock_quant_action_wizard
from . import inherit_picking_backorder
from . import inherit_result_put_warehouse_wizard
from . import inherit_wizard_purchase_receiving
from . import inherit_wizard_generate_red_picking
from . import inhert_wizard_generate_purchase_order
from . import inherit_bom_create_picking_wizard
from . import inherit_roke_purchase_return_stock_wizard
from . import inherit_roke_sale_return_stock_wizard
from . import inherit_sale_delivery_wizard
from . import inherit_sale_production_wizard
from . import inherit_wizard_order_deduct
from . import inherit_roke_order_create_purchase_wizard
from . import inherit_purchase_wizard_order_deduct
from . import inherit_wizard_stock_deduct
from . import inherit_split_task_wizard
from . import inherit_split_work_order_wizard
from . import inherit_transfer_work_order_wizard
from . import inherit_ptm_create_purchase_wizard
from . import inherit_wo_create_picking_wizard
from . import inherit_material_input_wizard
from . import inherit_transportation_product_wizard
from . import inherit_assign_customer_wizard
from . import inherit_so_create_bom_picking_wizard
