# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _, SUPERUSER_ID


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)

class InheritAssignCustomerWizard(models.TransientModel):
    _inherit = "assign.customer.wizard"

    product_id = fields.Many2one("roke.product", string="产品")
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON>an(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 允许拆分数量
    auxiliary_json = fields.Char(string="委外数量")
    auxiliary1_qty = fields.Float(string="委外辅数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="委外辅数量2", digits='SCSL')

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_onchange_vals(self, qty):
        """
        获取onchange值
        :return:
        """
        res = super(InheritAssignCustomerWizard, self)._get_onchange_vals(qty)
        product = self.entrust_ids.product_id
        res.update({
            "product_id": product.id
        })
        if not product.is_free_conversion:
            # 取余、自由换算不处理辅计量数量
            auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', qty)
            res.update({
                "auxiliary1_qty": auxiliary.get("aux1_qty", 0),
                "auxiliary2_qty": auxiliary.get("aux2_qty", 0),
            })
        else:
            # 自由换算
            res.update({
                "auxiliary1_qty": 0,
                "auxiliary2_qty": 0,
            })
        return res

    def _get_entrust_split_vals(self):
        """
        获取转委外拆分出工单的值
        :return:
        """
        res = super(InheritAssignCustomerWizard, self)._get_entrust_split_vals()
        res.update({
            "split_auxiliary1_qty": self.auxiliary1_qty,
            "split_auxiliary2_qty": self.auxiliary2_qty
        })
        return res


