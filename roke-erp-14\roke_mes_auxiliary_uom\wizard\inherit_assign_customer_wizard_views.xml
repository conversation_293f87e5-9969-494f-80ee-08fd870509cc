<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="view_uom_inherit_assign_customer_wizard_form_view" model="ir.ui.view">
        <field name="name">roke.uom.inherit.assign.customer.wizard.form</field>
        <field name="model">assign.customer.wizard</field>
        <field name="inherit_id" ref="roke_mes_entrust_order.view_assign_customer_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="replace">
                <label for="qty"/>
                <div name="qty" class="o_row">
                    <field name="qty"
                           attrs="{'invisible': [('can_update_qty', '!=', True)], 'require': [('can_update_qty', '=', True)]}"/>
                    <span name="qty_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary1_qty"
                           attrs="{'invisible': ['|',('can_update_qty', '!=', True), ('auxiliary_uom1_id','=',False)], 'require': [('can_update_qty', '=', True)]}"
                           force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary2_qty"
                           attrs="{'invisible': ['|',('can_update_qty', '!=', True), ('auxiliary_uom2_id','=',False)], 'require': [('can_update_qty', '=', True)]}"
                           force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
