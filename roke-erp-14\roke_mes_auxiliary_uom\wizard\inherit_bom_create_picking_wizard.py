# -*- coding: utf-8 -*-
"""
Description:
    生产任务BOM领料时添加辅计量数量
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import json


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritBomCreatePickingWizard(models.TransientModel):
    _inherit = "roke.bom.create.picking.wizard"

    def _get_picking_line_vals(self, src_location, dest_location, line):
        """获取调拨单需求"""
        res = super(InheritBomCreatePickingWizard, self)._get_picking_line_vals(src_location, dest_location, line)
        res.update({
            "auxiliary1_qty": line.auxiliary1_qty,
            "auxiliary2_qty": line.auxiliary2_qty,
        })
        return res


class InheritBomCreatePickingLineWizard(models.TransientModel):
    _inherit = "roke.bom.create.picking.line.wizard"

    uom_id = fields.Many2one(related="material_id.uom_id", string="主计量")
    auxiliary_uom1_id = fields.Many2one(related="material_id.auxiliary_uom1_id", string="辅计量1")
    auxiliary_uom2_id = fields.Many2one(related="material_id.auxiliary_uom2_id", string="辅计量2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="material_id.is_real_time_calculations")
    # 需求数
    demand_auxiliary1_qty = fields.Float(string="需求辅数量1", digits='KCSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅数量2", digits='KCSL')
    demand_auxiliary_json = fields.Char(string="需求数量")
    # 领料数
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('material_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'main',
                                                                                    self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux1',
                                                                                    self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux2',
                                                                                    self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
