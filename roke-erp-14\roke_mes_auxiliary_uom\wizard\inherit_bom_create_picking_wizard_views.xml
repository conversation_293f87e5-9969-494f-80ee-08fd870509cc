<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_au_inherit_bom_create_picking_wizard_form" model="ir.ui.view">
        <field name="name">au.inherit.bom.create.picking.wizard.form</field>
        <field name="model">roke.bom.create.picking.wizard</field>
        <field name="inherit_id" ref="roke_mes_stock.roke_bom_create_picking_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='qty']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>
