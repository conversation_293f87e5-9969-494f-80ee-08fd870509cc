# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.tools.float_utils import float_round
from datetime import timedelta
import datetime
import json
import math


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


def float_to_time(hours):
    """ Convert a number of hours into a time object. """
    if hours == 24.0:
        return datetime.time.max
    fractional, integral = math.modf(hours)
    return datetime.time(int(integral), int(float_round(60 * fractional, precision_digits=0)), 0)


class InheritCreateWorkRecordWizard(models.TransientModel):
    _inherit = "roke.create.work.record.wizard"

    auxiliary_uom1_id = fields.Many2one("roke.uom", string="辅计量单位1", related="work_order_id.auxiliary_uom1_id")
    auxiliary_uom2_id = fields.Many2one("roke.uom", string="辅计量单位2", related="work_order_id.auxiliary_uom2_id")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    # 可报工数量
    wait_auxiliary_json = fields.Char(string="可报工数量")
    wait_auxiliary1_qty = fields.Float(string="可报工辅助数量1", digits='SCSL')
    wait_auxiliary2_qty = fields.Float(string="可报工辅助数量2", digits='SCSL')
    # 合格数量
    finish_auxiliary_json = fields.Char(string="合格数量")
    finish_auxiliary1_qty = fields.Float(string="合格辅助数量1", digits='SCSL')
    finish_auxiliary2_qty = fields.Float(string="合格辅助数量2", digits='SCSL')
    # 不合格数量
    unqualified_auxiliary_json = fields.Char(string="不合格数量")
    unqualified_auxiliary1_qty = fields.Float(string="不合格辅助数量1", digits='SCSL')
    unqualified_auxiliary2_qty = fields.Float(string="不合格辅助数量2", digits='SCSL')
    # 不计工资数
    invalid_auxiliary_json = fields.Char(string="不计工资数")
    invalid_auxiliary1_qty = fields.Float(string="不计工资辅助数量1", digits='SCSL')
    invalid_auxiliary2_qty = fields.Float(string="不计工资辅助数量2", digits='SCSL')

    def check_create_value(self):
        """
        检查完成数、不合格数、工时数
        :return:
        """
        if self.finish_auxiliary1_qty or self.unqualified_auxiliary1_qty:
            # 有辅1数量也允许报工，取余换算时
            return True
        return super(InheritCreateWorkRecordWizard, self).check_create_value()

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.wait_qty = 0
            self.wait_auxiliary1_qty = 0
            self.wait_auxiliary2_qty = 0
            self.finish_qty = 0
            self.finish_auxiliary1_qty = 0
            self.finish_auxiliary2_qty = 0
            self.unqualified_qty = 0
            self.unqualified_auxiliary1_qty = 0
            self.unqualified_auxiliary2_qty = 0
            self.invalid_salary_qty = 0
            self.invalid_auxiliary1_qty = 0
            self.invalid_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('wait_qty')
    def _onchange_wait_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.wait_qty)
                self.wait_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.wait_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('wait_auxiliary1_qty')
    def _onchange_wait_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.wait_auxiliary1_qty)
                self.wait_qty = qty_json.get('main_qty', 0)
                self.wait_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('wait_auxiliary2_qty')
    def _onchange_wait_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.wait_auxiliary2_qty)
                self.wait_qty = qty_json.get('main_qty', 0)
                self.wait_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('finish_qty')
    def _onchange_finish_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.finish_qty)
                self.finish_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.finish_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('finish_auxiliary1_qty')
    def _onchange_finish_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.finish_auxiliary1_qty)
                self.finish_qty = qty_json.get('main_qty', 0)
                self.finish_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('finish_auxiliary2_qty')
    def _onchange_finish_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.finish_auxiliary2_qty)
                self.finish_qty = qty_json.get('main_qty', 0)
                self.finish_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('unqualified_qty')
    def _onchange_aux_unqualified_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.unqualified_qty)
                self.unqualified_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.unqualified_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('unqualified_auxiliary1_qty')
    def _onchange_unqualified_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.unqualified_auxiliary1_qty)
                self.unqualified_qty = qty_json.get('main_qty', 0)
                self.unqualified_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('unqualified_auxiliary2_qty')
    def _onchange_unqualified_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.unqualified_auxiliary2_qty)
                self.unqualified_qty = qty_json.get('main_qty', 0)
                self.unqualified_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('invalid_salary_qty')
    def _onchange_invalid_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.invalid_salary_qty)
                self.invalid_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.invalid_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('invalid_auxiliary1_qty')
    def _onchange_invalid_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.invalid_auxiliary1_qty)
                self.invalid_salary_qty = qty_json.get('main_qty', 0)
                self.invalid_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('invalid_auxiliary2_qty')
    def _onchange_invalid_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.invalid_auxiliary2_qty)
                self.invalid_salary_qty = qty_json.get('main_qty', 0)
                self.invalid_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_work_record_dict(self, employee_ids, allot_list):
        """
        获取报工记录创建内容
        :return:
        """
        res = super(InheritCreateWorkRecordWizard, self)._get_work_record_dict(employee_ids, allot_list)
        res.update({
            "finish_auxiliary1_qty": self.finish_auxiliary1_qty,
            "finish_auxiliary2_qty": self.finish_auxiliary2_qty,
            "unqualified_auxiliary1_qty": self.unqualified_auxiliary1_qty,
            "unqualified_auxiliary2_qty": self.unqualified_auxiliary2_qty,
            "invalid_auxiliary1_qty": self.invalid_auxiliary1_qty,
            "invalid_auxiliary2_qty": self.invalid_auxiliary2_qty,
        })
        return res

    def confirm(self, center=False):
        """
        更新工单辅数量
        :return:
        """
        res = super(InheritCreateWorkRecordWizard, self).confirm(center)
        if not self._context.get('routing_create'):
            # 非工艺报工才更新工单的辅计量
            self.work_order_id.update_aux_qty(
                self.finish_qty,
                self.finish_auxiliary1_qty,
                self.finish_auxiliary2_qty,
                self.unqualified_qty,
                self.unqualified_auxiliary1_qty,
                self.unqualified_auxiliary2_qty,
            )
        return res

    def _get_routing_create_vals(self, wo, plan_qty, finish_qty):
        """
        获取工艺报工的值
        :return:
        """
        res = super(InheritCreateWorkRecordWizard, self)._get_routing_create_vals(wo, plan_qty, finish_qty)
        res.update({
            "finish_auxiliary1_qty": self.finish_auxiliary1_qty,
            "finish_auxiliary2_qty": self.finish_auxiliary2_qty,
            "unqualified_auxiliary1_qty": self.unqualified_auxiliary1_qty,
            "unqualified_auxiliary2_qty": self.unqualified_auxiliary2_qty,
            "invalid_auxiliary1_qty": self.invalid_auxiliary1_qty,
            "invalid_auxiliary2_qty": self.invalid_auxiliary2_qty
        })
        return res

    def _exceed_plan_get_new_qty(self, finish_qty):
        """
        超计划时获取默认完成数，辅计量处理
        :return:
        """
        res = super(InheritCreateWorkRecordWizard, self)._exceed_plan_get_new_qty(finish_qty)
        product = self.product_id
        if not product.is_free_conversion:
            # 取余、自由换算不处理辅计量数量
            finish_auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', finish_qty)
            res.update({
                "finish_auxiliary1_qty": finish_auxiliary.get("aux1_qty", 0),
                "finish_auxiliary2_qty": finish_auxiliary.get("aux2_qty", 0)
            })
        else:
            # 自由换算
            res.update({
                "finish_auxiliary1_qty": 0,
                "finish_auxiliary2_qty": 0
            })
        return res
