<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_auxiliary_uom_inherit_create_work_record_wizard_form" model="ir.ui.view">
        <field name="name">auxiliary.uom.inherit.create.work.record.wizard.form</field>
        <field name="model">roke.create.work.record.wizard</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_create_work_record_wizard_form"/>
        <field name="arch" type="xml">
            <!-- 隱藏label-->
            <xpath expr="//div[@name='wait_qty']" position="replace">
                <div name="wait_qty" class="o_row">
                    <field name="wait_qty" readonly="1"/>
                    <span name="wait_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="wait_auxiliary1_qty" readonly="1"
                           attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                    <span name="wait_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="wait_auxiliary2_qty"
                           readonly="1"
                           attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="wait_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>

            </xpath>
            <xpath expr="//div[@name='finish_qty']" position="replace">
                <div name="finish_qty" class="o_row">
                    <field name="finish_qty"/>
                    <span name="finish_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary1_qty"
                           attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                    <span name="finish_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="finish_auxiliary2_qty"
                           attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="finish_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>

            </xpath>
            <xpath expr="//div[@name='unqualified_qty']" position="replace">
                <div name="unqualified_qty" class="o_row">
                    <field name="unqualified_qty"/>
                    <span name="unqualified_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="unqualified_auxiliary1_qty"
                           attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                       force_save="1"/>
                    <span name="unqualified_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="unqualified_auxiliary2_qty"
                           attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="unqualified_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
                <label for="invalid_salary_qty"/>
                <div name="invalid_salary_qty" class="o_row">
                    <field name="invalid_salary_qty"/>
                    <span name="invalid_salary_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="invalid_auxiliary1_qty" attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"/>
                    <span name="invalid_salary_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="invalid_auxiliary2_qty" attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"/>
                    <span name="invalid_salary_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
