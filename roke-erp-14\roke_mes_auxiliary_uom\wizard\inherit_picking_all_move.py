#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_picking_all_move.py.py
@Time:
    2022/11/28 15:13
@Site: 
    
@Desc:
    
"""
from odoo import models, fields, api, _
import json
import math


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRokeWizardMesPickingAllMove(models.TransientModel):
    _inherit = "roke.wizard.mes.picking.all.move"

    def prepare_move_line_dict(self, move, stock_lot_id=None):
        result = super(InheritRokeWizardMesPickingAllMove, self).prepare_move_line_dict(move, stock_lot_id)
        # 跟踪单一批次号的情况
        if move.product_id.track_type == 'unique':
            product_uom_line1 = move.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == move.product_id.auxiliary_uom1_id.id)
            product_uom_line2 = move.product_id.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == move.product_id.auxiliary_uom2_id.id)
            # 三种换算关系---1、取余  2、自由  3、非自由-非取余
            # 非自由
            if not move.product_id.uom_groups_id.is_free_conversion:
                auxiliary1_qty = product_uom_line1.conversion if product_uom_line1 else 0
                auxiliary2_qty = product_uom_line2.conversion if product_uom_line2 else 0
            # 自由
            else:
                auxiliary1_qty = move.auxiliary1_qty / move.qty
                auxiliary2_qty = move.auxiliary2_qty / move.qty
        # 不是跟踪单一批次
        else:
            # 三种换算关系---1、取余  2、自由  3、非自由-非取余
            # 1、取余

            auxiliary1_qty = move.auxiliary1_qty
            auxiliary2_qty = move.auxiliary2_qty
        result.update({
            "auxiliary1_qty": auxiliary1_qty,
            "auxiliary2_qty": auxiliary2_qty,
        })
        return result
