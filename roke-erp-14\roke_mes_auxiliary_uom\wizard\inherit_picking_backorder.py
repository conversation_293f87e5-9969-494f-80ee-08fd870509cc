# -*- coding: utf-8 -*-
"""
Description:
    欠单
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InheritRokeWizardMesPickingBackorder(models.TransientModel):
    _inherit = "roke.wizard.mes.picking.backorder"

    def no_backorder(self):
        # 没有欠单
        # self.picking_id.picking_update_backorder()
        self.picking_id.make_finish()
        return {'type': 'ir.actions.act_window_close'}
