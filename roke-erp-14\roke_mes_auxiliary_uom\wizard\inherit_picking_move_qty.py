#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2022-09-28 10:26
# <AUTHOR> 陈常磊
# @Site    :
# @File    : inherit_picking_move_qty.py
# @Software: PyCharm

from odoo import models, fields, api, _
import json
from odoo.exceptions import UserError


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)

class InheritRokeStockPickingMoveWizard(models.TransientModel):
    _inherit = "roke.stock.picking.move.wizard"

    def prepare_move_line_dict(self, line, lot_id, quant_id):
        result = super(InheritRokeStockPickingMoveWizard, self).prepare_move_line_dict(line, lot_id, quant_id)
        result.update({
            "auxiliary1_qty": line.auxiliary1_qty,
            "auxiliary2_qty": line.auxiliary2_qty,
        })
        return result


class InheritRokeStockPickingMoveQtyWizard(models.TransientModel):
    _inherit = "roke.stock.picking.move.qty.wizard"

    uom_id = fields.Many2one("roke.uom", related="wizard_id.product_id.uom_id", string="计量单位")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="wizard_id.product_id.auxiliary_uom1_id",
                                        string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="wizard_id.product_id.auxiliary_uom2_id",
                                        string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)


    def confirm(self):
        """校验辅计量不允许负库存"""
        setting = self.env['ir.config_parameter'].sudo().get_param('stock.negative.inventory')
        for line in self.line_ids:
            # 原位置是内部位置或中转位置且不是红单的，目的位置是内部位置或中转位置且是红单的
            if (self.move_id.src_location_id.location_type in ["内部位置",
                                                               "中转位置"] and not self.move_id.picking_id.is_red_order) or (
                    self.move_id.dest_location_id.location_type in ["内部位置",
                                                                    "中转位置"] and self.move_id.picking_id.is_red_order):
                if self.product_id.track_type == "none":
                    if '禁止' == setting:  # 不允许负库存
                        quant_id = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                         locations=line.src_location_id,
                                                                                         product_states=line.product_state_id)
                        quant_id = quant_id.filtered(lambda quant: not quant.lot_id and quant.inventory_quantity > 0)
                        quant_id = quant_id[0] if quant_id else False
                    else:  # 允许负库存
                        quant_id = self.env["roke.mes.stock.quant"].sudo().search_quants(products=self.product_id,
                                                                                         locations=line.src_location_id,
                                                                                         product_states=line.product_state_id)
                        quant_id = quant_id.filtered(lambda quant: not quant.lot_id)
                        quant_id = quant_id[0] if quant_id else False
                        # 允许负库存的前提下，判断仓库如果为【内部位置】且【数量超出】且【仓库未勾选允许负库存】时
                        if self.move_id.src_location_id.location_type == '内部位置':
                            if not quant_id or quant_id.inventory_auxiliary1_qty - line.auxiliary1_qty < 0.0 or quant_id.inventory_auxiliary2_qty - line.auxiliary2_qty:
                                if not self.move_id.src_location_id.is_minus:
                                    raise UserError("产品【%s】库存不足，不允许负库存调拨，仓库【%s】应设置为允许负库存" %
                                                    (self.product_id.name, line.src_location_id.name))
                        # -----end-----
                    if (not quant_id or ((quant_id.inventory_auxiliary1_qty - line.auxiliary1_qty < 0.0) or (
                            quant_id.inventory_auxiliary2_qty - line.auxiliary2_qty < 0.0))) and setting == '禁止':
                        raise UserError("产品【%s】库存不足，不允许负库存调拨" % self.product_id.name)

        return super(InheritRokeStockPickingMoveQtyWizard, self).confirm()
