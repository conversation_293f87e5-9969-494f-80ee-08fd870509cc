<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="inherit_view_roke_stock_picking_move_wizard_form" model="ir.ui.view">
        <field name="name">inherit.roke.stock.picking.move.wizard.form</field>
        <field name="model">roke.stock.picking.move.wizard</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_stock_picking_move_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_view_roke_stock_red_picking_move_wizard_form" model="ir.ui.view">
        <field name="name">inherit.roke.stock.red.picking.move.wizard.form</field>
        <field name="model">roke.stock.picking.move.wizard</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_stock_red_picking_move_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
