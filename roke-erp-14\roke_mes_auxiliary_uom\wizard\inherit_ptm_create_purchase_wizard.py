# -*- coding: utf-8 -*-
"""
Description:
    生产任务下级物料采购明细
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import json


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritPtmCreatePurchaseWizard(models.TransientModel):
    _inherit = "roke.ptm.create.purchase.wizard"

    def _get_purchase_detail_val(self, supplier_line):
        """
        获取采购订单内容
        :return:
        """
        res = super(InheritPtmCreatePurchaseWizard, self)._get_purchase_detail_val(supplier_line)
        res.update({
            "auxiliary1_qty": supplier_line.auxiliary1_qty,
            "auxiliary2_qty": supplier_line.auxiliary2_qty
        })
        return res


class InheritPtmCreatePurchaseLineWizard(models.TransientModel):
    _inherit = "roke.ptm.create.purchase.line.wizard"

    uom_id = fields.Many2one("roke.uom", related="material_id.uom_id", string="主计量")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom1_id", string="辅计量1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom2_id", string="辅计量2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="material_id.is_real_time_calculations")
    # 本次采购
    auxiliary_json = fields.Char(string="本次采购")
    auxiliary1_qty = fields.Float(string="本次采购辅数量1", digits='CGSL')
    auxiliary2_qty = fields.Float(string="本次采购辅数量2", digits='CGSL')
    # 需求数量
    demand_auxiliary_json = fields.Char(string="需求数量", related="pt_material_id.demand_auxiliary_json")
    demand_auxiliary1_qty = fields.Float(string="需求辅数量1", related="pt_material_id.demand_auxiliary1_qty", digits='SCSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅数量2", related="pt_material_id.demand_auxiliary2_qty", digits='SCSL')

    @api.onchange('material_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'main',
                                                                                    self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux1',
                                                                                    self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux2',
                                                                                    self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
