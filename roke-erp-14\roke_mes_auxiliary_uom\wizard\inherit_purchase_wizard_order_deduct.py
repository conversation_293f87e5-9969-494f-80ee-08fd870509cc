# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class InheritWizardPurchaseOrderDeductLine(models.TransientModel):
	_inherit = "wizard.purchase.order.deduct.line"

	qty = fields.Float(related="order_line_id.qty", string="数量", digits='YSYFSL')
	uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
	auxiliary1_qty = fields.Float(related="order_line_id.auxiliary1_qty", string="辅助数量1", digits='YSYFSL')
	auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
	auxiliary2_qty = fields.Float(related="order_line_id.auxiliary2_qty", string="辅助数量2", digits='YSYFSL')
	auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
	auxiliary_json = fields.Char(related="order_line_id.auxiliary_json", string="数量")
	is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
	                                           related="product_id.is_real_time_calculations")
