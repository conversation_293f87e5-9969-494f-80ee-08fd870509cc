# -*- coding: utf-8 -*-
"""
Description:
    产出物：入库、调整数量
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)

# 修正数量
class InheritUomResultPutWarehouseWizard(models.TransientModel):
    _inherit = "roke.result.put.warehouse.wizard"

    def _get_move_line_val(self, line):
        res = super(InheritUomResultPutWarehouseWizard, self).get_move_line_val(line)
        res.update({
            'auxiliary1_qty': line.stock_auxiliary1_qty,
            'auxiliary2_qty': line.stock_auxiliary2_qty,
        })
        return res

    def _default_get_line_vals(self, result):
        """
        获取明细内容
        :return:
        """
        res = super(InheritUomResultPutWarehouseWizard, self)._default_get_line_vals(result)
        res.update({
            "wait_auxiliary1_qty": result.residue_auxiliary1_qty,
            "wait_auxiliary2_qty": result.residue_auxiliary2_qty,
            "stock_auxiliary1_qty": result.residue_auxiliary1_qty,
            "stock_auxiliary2_qty": result.residue_auxiliary2_qty
        })
        return res

    def _get_result_move_vals(self, line, dest_location):
        """
        获取产出移动内容
        :return:
        """
        res = super(InheritUomResultPutWarehouseWizard, self)._get_result_move_vals(line, dest_location)
        res.update({
            "auxiliary1_qty": line.stock_auxiliary1_qty,
            "auxiliary2_qty": line.stock_auxiliary2_qty,
        })
        return res

    def _get_stock_move_vals(self, line, dest_location, src_location, customer):
        """
        获取库存移动内容
        :return:
        """
        res = super(InheritUomResultPutWarehouseWizard, self)._get_stock_move_vals(line, dest_location, src_location, customer)
        res.update({
            "auxiliary1_qty": line.stock_auxiliary1_qty,
            "auxiliary2_qty": line.stock_auxiliary2_qty,
        })
        return res

    def _get_picking_line_vals(self, line, dest_location, src_location, result_move, customer):
        """
        获取调拨明细行内容
        :return:
        """
        res = super(InheritUomResultPutWarehouseWizard, self)._get_picking_line_vals(line, dest_location, src_location, result_move, customer)
        res.update({
            "auxiliary1_qty": line.stock_auxiliary1_qty,
            "auxiliary2_qty": line.stock_auxiliary2_qty,
        })
        return res


class InheritUomResultPutWarehouseLineWizard(models.TransientModel):
    _inherit = "roke.result.put.warehouse.line.wizard"

    product_id = fields.Many2one(related="result_id.product_id", string="产品")
    uom_id = fields.Many2one(related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 待入库数量
    wait_auxiliary_json = fields.Char(string="待入库数量")
    wait_auxiliary1_qty = fields.Float(string="待入库数量1", digits='SCSL')
    wait_auxiliary2_qty = fields.Float(string="待入库数量2", digits='SCSL')
    # 入库数量
    stock_auxiliary_json = fields.Char(string="入库数量")
    stock_auxiliary1_qty = fields.Float(string="入库数量1", digits='SCSL')
    stock_auxiliary2_qty = fields.Float(string="入库数量2", digits='SCSL')

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.stock_qty = 0
            self.stock_auxiliary1_qty = 0
            self.stock_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('stock_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.stock_qty)
                self.stock_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.stock_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('stock_auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.stock_auxiliary1_qty)
                self.stock_qty = qty_json.get('main_qty', 0)
                self.stock_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('stock_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.stock_auxiliary2_qty)
                self.stock_qty = qty_json.get('main_qty', 0)
                self.stock_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
