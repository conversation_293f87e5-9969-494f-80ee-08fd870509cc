<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--临时订单过度-->
    <record id="uom_inherit_view_result_put_warehouse_wizard_form" model="ir.ui.view">
        <field name="name">uom.inherit.result.put.warehouse.wizard.form</field>
        <field name="model">roke.result.put.warehouse.wizard</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_result_put_warehouse_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='wait_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="wait_auxiliary1_qty" readonly="1"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="wait_auxiliary2_qty" optional="show"
                       readonly="1" force_save="1"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='stock_qty']" position="after">
                <field name="stock_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="stock_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>
</odoo>