# -*- coding: utf-8 -*-
"""
Description:
    订单（销售订单/生产订单）创建原材料采购订单
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _, SUPERUSER_ID
import logging
from odoo.exceptions import ValidationError
import math
import json

_logger = logging.getLogger(__name__)


class InheritRokeOrderCreatePurchaseWizard(models.TransientModel):
    _inherit = "roke.order.create.purchase.wizard"

    def _deal_qty(self, rec, quants):
        """
			处理明细行数据
		"""
        stock_qty = sum(quants.mapped("qty"))
        stock_auxiliary1_qty = sum(quants.mapped("auxiliary1_qty"))
        stock_auxiliary2_qty = sum(quants.mapped("auxiliary2_qty"))
        line_qty = rec.qty
        line_auxiliary1_qty = rec.auxiliary1_qty
        line_auxiliary2_qty = rec.auxiliary2_qty
        # 如果产品是非自由非取余
        if not rec.material_id.is_free_conversion:
            current_qty = line_qty - stock_qty
            if current_qty <= 0:
                rec.wizard_id = False
            else:
                value = self.env['roke.uom.groups'].main_auxiliary_conversion(rec.material_id, 'main', current_qty)
                rec.qty = value.get('main_qty', 0)
                rec.auxiliary1_qty = value.get('aux1_qty', 0)
                rec.auxiliary2_qty = value.get('aux2_qty', 0)
        else:
            current_qty = line_qty - stock_qty
            current_auxiliary1_qty = line_auxiliary1_qty - stock_auxiliary1_qty
            current_auxiliary2_qty = line_auxiliary2_qty - stock_auxiliary2_qty
            if current_qty <= 0:
                rec.wizard_id = False
            else:
                rec.qty = current_qty
                rec.auxiliary1_qty = current_auxiliary1_qty if current_auxiliary1_qty > 0 else 0
                rec.auxiliary2_qty = current_auxiliary2_qty if current_auxiliary2_qty > 0 else 0

    def _get_line_vals(self, sale_lines):
        wizard_line_vals = []
        multi_purchase_materials = []
        purchase_materials_list = []

        for record in sale_lines:
            if not record.e_bom_id:
                continue
            purchase_materials = record.e_bom_id.get_can_purchase_material(record.order_qty, 1)
            purchase_materials_list += purchase_materials
        # 将需要购买的明细合并
        # 获取所有物料
        material_list = [pur_line.get('material', False) for pur_line in purchase_materials_list]
        set_material_list = list(set(material_list)) if material_list else []
        for mate_line in set_material_list:
            material_qty = 0
            material_auxiliary1_qty = 0
            material_auxiliary2_qty = 0
            for pur_line in purchase_materials_list:
                if pur_line.get('material', False) == mate_line:
                    # 通过辅计量数量换算一下数量
                    qty_dict = sale_lines._get_aux_qty_dict(pur_line)
                    qty = qty_dict.get("main_qty", 0)
                    auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
                    auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
                    material_qty += qty
                    material_auxiliary1_qty += auxiliary1_qty
                    material_auxiliary2_qty += auxiliary2_qty

            multi_purchase_materials.append({
                'material': mate_line,
                'qty': material_qty,
                'aux1_qty': material_auxiliary1_qty,
                'aux2_qty': material_auxiliary2_qty,
            })
        # 获取sale_ids
        sale_ids = [line.order_id.id for line in sale_lines]
        # 获取采购明细
        for purchase_material in multi_purchase_materials:
            supplier_id = False
            unit_price = 1
            material = purchase_material.get("material", False)
            qty = purchase_material.get("qty", 0)
            aux1_qty = purchase_material.get("aux1_qty", 0)
            aux2_qty = purchase_material.get("aux2_qty", 0)
            # 根据物料获取已生成的采购订单明细已购买了多少
            purchased_qty, purchased_aux1_qty, purchased_aux2_qty = sale_lines.get_purchased_qty(material,
                                                                                                list(set(sale_ids)))
            # 根据物料单位类型判断需要辅计量需要购买多少
            # 非自由非取余
            purchase_qty, purchase_aux1_qty, purchase_aux2_qty = 0, 0, 0
            if not material.is_free_conversion:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
                purchase_aux1_qty = aux1_qty - purchased_aux1_qty if aux1_qty - purchased_aux1_qty > 0 else 0
                purchase_aux2_qty = aux2_qty - purchased_aux2_qty if aux2_qty - purchased_aux2_qty > 0 else 0
            # 自由
            else:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
            if purchase_qty and material and material.supplier_price_ids:
                # 获取供应商价格配置
                supplier_price = material.supplier_price_ids[0]
                supplier_id = supplier_price.supplier_id.id
                unit_price = supplier_price.purchase_price
                purchase_qty = supplier_price.min_purchase_num if purchase_qty < supplier_price.min_purchase_num else purchase_qty
            if purchase_qty > 0:
                wizard_line_vals.append((0, 0, {
                    # "sol": record.id,
                    "material_id": material.id,
                    "demand_qty": qty,
                    "demand_auxiliary1_qty": aux1_qty,
                    "demand_auxiliary2_qty": aux2_qty,
                    "supplier_id": supplier_id,
                    "purchased_qty": purchased_qty,
                    "unit_price": unit_price,
                    "qty": purchase_qty,
                    "auxiliary1_qty": purchase_aux1_qty,
                    "auxiliary2_qty": purchase_aux2_qty,
                    "purchased_aux1_qty": purchased_aux1_qty,
                    "purchased_aux2_qty": purchased_aux2_qty
                }))
        return wizard_line_vals

    def _pol_get_line_vals(self, production_lines):
        wizard_line_vals = []
        multi_purchase_materials = []
        purchase_materials_list = []

        for record in production_lines:
            if not record.e_bom_id:
                continue
            purchase_materials = record.e_bom_id.get_can_purchase_material(record.qty, 1)
            purchase_materials_list += purchase_materials
        # 将需要购买的明细合并
        # 获取所有物料
        material_list = [pur_line.get('material', False) for pur_line in purchase_materials_list]
        set_material_list = list(set(material_list)) if material_list else []
        for mate_line in set_material_list:
            material_qty = 0
            material_auxiliary1_qty = 0
            material_auxiliary2_qty = 0
            for pur_line in purchase_materials_list:
                if pur_line.get('material', False) == mate_line:
                    # 通过辅计量数量换算一下数量
                    qty_dict = production_lines._get_aux_qty_dict(pur_line)
                    qty = qty_dict.get("main_qty", 0)
                    auxiliary1_qty = qty_dict.get("auxiliary1_qty", 0)
                    auxiliary2_qty = qty_dict.get("auxiliary2_qty", 0)
                    material_qty += qty
                    material_auxiliary1_qty += auxiliary1_qty
                    material_auxiliary2_qty += auxiliary2_qty

            multi_purchase_materials.append({
                'material': mate_line,
                'qty': material_qty,
                'aux1_qty': material_auxiliary1_qty,
                'aux2_qty': material_auxiliary2_qty,
            })
        # 获取sale_ids
        production_ids = [line.order_id.id for line in production_lines]
        # 获取采购明细
        for purchase_material in multi_purchase_materials:
            supplier_id = False
            unit_price = 1
            material = purchase_material.get("material", False)
            qty = purchase_material.get("qty", 0)
            aux1_qty = purchase_material.get("aux1_qty", 0)
            aux2_qty = purchase_material.get("aux2_qty", 0)
            # 根据物料获取已生成的采购订单明细已购买了多少
            purchased_qty, purchased_aux1_qty, purchased_aux2_qty = production_lines.get_purchased_qty(material,
                                                                                                       list(
                                                                                                           set(production_ids)))
            # 根据物料单位类型判断需要辅计量需要购买多少
            # 非自由非取余
            purchase_qty, purchase_aux1_qty, purchase_aux2_qty = 0, 0, 0
            if not material.is_free_conversion:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
                purchase_aux1_qty = aux1_qty - purchased_aux1_qty if aux1_qty - purchased_aux1_qty > 0 else 0
                purchase_aux2_qty = aux2_qty - purchased_aux2_qty if aux2_qty - purchased_aux2_qty > 0 else 0
            # 自由
            else:
                purchase_qty = qty - purchased_qty if qty - purchased_qty > 0 else 0
            if purchase_qty and material and material.supplier_price_ids:
                # 获取供应商价格配置
                supplier_price = material.supplier_price_ids[0]
                supplier_id = supplier_price.supplier_id.id
                unit_price = supplier_price.purchase_price
                purchase_qty = supplier_price.min_purchase_num if purchase_qty < supplier_price.min_purchase_num else purchase_qty
            if purchase_qty > 0:
                wizard_line_vals.append((0, 0, {
                    # "sol": record.id,
                    "material_id": material.id,
                    "demand_qty": qty,
                    "demand_auxiliary1_qty": aux1_qty,
                    "demand_auxiliary2_qty": aux2_qty,
                    "supplier_id": supplier_id,
                    "purchased_qty": purchased_qty,
                    "unit_price": unit_price,
                    "qty": purchase_qty,
                    "auxiliary1_qty": purchase_aux1_qty,
                    "auxiliary2_qty": purchase_aux2_qty,
                    "purchased_aux1_qty": purchased_aux1_qty,
                    "purchased_aux2_qty": purchased_aux2_qty
                }))
        return wizard_line_vals

    def confirm(self):
        """
        生成采购订单
        :return:
        """
        lines = self.line_ids.filtered(lambda l: l.qty > 0 and l.selected)
        # if lines.filtered(lambda l: not l.supplier_id):
        #     raise ValidationError("需要采购的明细必须录入供应商")
        purchase_order_vals = []
        supplier_ids = list(set(lines.mapped("supplier_id").ids))
        if lines.filtered(lambda l: not l.supplier_id):  # 处理不选择供应商的需求/(ㄒoㄒ)/~~
            supplier_ids.append(False)
        for supplier_id in supplier_ids:
            supplier_lines = lines.filtered(lambda l: l.supplier_id.id == supplier_id)
            purchase_detail = []
            for supplier_line in supplier_lines:
                # 采购明细备注来源订单
                origin_note = ""
                if supplier_line.sol:
                    if origin_note:
                        origin_note += "、"
                    origin_note += supplier_line.sol.order_id.display_name
                elif supplier_line.pol:
                    if origin_note:
                        origin_note += "、"
                    origin_note += supplier_line.pol.order_id.display_name
                qty = math.ceil(supplier_line.qty)
                auxiliary1_qty = math.ceil(supplier_line.auxiliary1_qty)
                auxiliary2_qty = math.ceil(supplier_line.auxiliary2_qty)
                # 计算主数量
                if not supplier_line.material_id.is_free_conversion:
                    value = {}
                    if qty:
                        value = self.env['roke.uom.groups'].main_auxiliary_conversion(supplier_line.material_id, 'main', qty)
                    elif auxiliary1_qty:
                        value = self.env['roke.uom.groups'].main_auxiliary_conversion(supplier_line.material_id, 'aux1', auxiliary1_qty)
                    elif auxiliary2_qty:
                        value = self.env['roke.uom.groups'].main_auxiliary_conversion(supplier_line.material_id, 'aux2', auxiliary2_qty)
                    qty = value.get('main_qty', 0)
                else:
                    qty = qty
                purchase_detail.append((0, 0, {
                    "unit_price": supplier_line.unit_price,
                    "product_id": supplier_line.material_id.id,
                    "qty": qty,
                    "auxiliary1_qty": auxiliary1_qty,
                    "auxiliary2_qty": auxiliary2_qty,
                    "note": origin_note
                }))
            purchase_order_vals.append({
                "supplier_id": supplier_id,
                "sale_order_ids": [(6, 0, self.sale_order_ids.ids)],
                "production_order_ids": [(6, 0, self.production_order_ids.ids)],
                "detail_ids": purchase_detail,
                "note": "订单采购原材料"
            })
        if purchase_order_vals:
            purchase_orders = self.env["roke.purchase.order"].create(purchase_order_vals)
            if self.env.context.get('confirm'):
                purchase_ids = purchase_orders.filtered(lambda po: po.supplier_id)
                for p in purchase_ids:
                    p.make_confirm()
            return {
                'name': '本次创建的采购订单',
                'type': 'ir.actions.act_window',
                'view_mode': 'tree,form',
                'target': 'current',
                'domain': [("id", "in", purchase_orders.ids)],
                'res_model': 'roke.purchase.order'
            }

    def requisition_confirm(self):
        """确认生成采购请购单"""
        lines = self.line_ids.filtered(lambda l: l.qty > 0 and l.selected)
        purchase_requisition_detail = []
        if not self.employee_id:
            raise ValidationError("生成请购单时,请购人员不能为空")
        for line in lines:
            # 采购明细备注来源订单
            origin_note = ""
            if line.sol:
                if origin_note:
                    origin_note += "、"
                origin_note += line.sol.order_id.display_name
            elif line.pol:
                if origin_note:
                    origin_note += "、"
                origin_note += line.pol.order_id.display_name
            qty = math.ceil(line.qty)
            auxiliary1_qty = math.ceil(line.auxiliary1_qty)
            auxiliary2_qty = math.ceil(line.auxiliary2_qty)
            # 计算主数量
            if not line.material_id.is_free_conversion:
                value = {}
                if qty:
                    value = self.env['roke.uom.groups'].main_auxiliary_conversion(line.material_id, 'main',
                                                                                  qty)
                elif auxiliary1_qty:
                    value = self.env['roke.uom.groups'].main_auxiliary_conversion(line.material_id, 'aux1',
                                                                                  auxiliary1_qty)
                elif auxiliary2_qty:
                    value = self.env['roke.uom.groups'].main_auxiliary_conversion(line.material_id, 'aux2',
                                                                                  auxiliary2_qty)
                qty = value.get('main_qty', 0)
            else:
                qty = qty
            purchase_requisition_detail.append((0, 0, {
                "unit_price": line.unit_price,
                "product_id": line.material_id.id,
                "demand_qty": qty,
                "auxiliary1_qty": auxiliary1_qty,
                "auxiliary2_qty": auxiliary2_qty,
                "note": origin_note,
                "supplier_id": line.supplier_id.id

            }))
        purchase_requisition_val = {
            "detail_ids": purchase_requisition_detail,
            "employee_id": self.employee_id.id,
            "note": "订单采购原材料",
            "requisition_order_ids": [(6, 0, self.sale_order_ids.ids)]
        }
        if purchase_requisition_val:
            purchase_requisitions = self.env["roke.purchase.requisition"].create(purchase_requisition_val)
            if self.env.context.get('confirm'):
                for p in purchase_requisitions:
                    p.action_make_confirm()
            return {
                'name': '本次创建的采购请购单',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'current',
                'res_id': purchase_requisitions.id,
                'res_model': 'roke.purchase.requisition'
            }


class InheritRokeOrderCreatePurchaseLineWizard(models.TransientModel):
    _inherit = "roke.order.create.purchase.line.wizard"

    uom_id = fields.Many2one("roke.uom", related="material_id.uom_id", string="计量单位")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='CGSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom1_id",
                                        string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='CGSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom2_id",
                                        string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="material_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")
    demand_auxiliary1_qty = fields.Float(string="需求辅助数量1", digits='CGSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅助数量1", digits='CGSL')
    demand_auxiliary_json = fields.Char(string="需求数量")

    purchased_aux1_qty = fields.Float(string="已采购辅助数量1", digits='CGSL')
    purchased_aux2_qty = fields.Float(string="已采购辅助数量2", digits='CGSL')
    purchased_qty_json = fields.Char(string="已采购数量")

    @api.onchange('material_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'main',
                                                                                    self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux1',
                                                                                    self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux2',
                                                                                    self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)


class InheritRokeOrderCreatePurchaseMaterialLineWizard(models.TransientModel):
    _inherit = "roke.order.create.purchase.material.line.wizard"

    demand_auxiliary1_qty = fields.Float(string="需求辅助数量", digits='CGSL')
    demand_auxiliary2_qty = fields.Float(string="需求辅助数量", digits='CGSL')
    demand_auxiliary_json = fields.Char(string="数量")
    uom_id = fields.Many2one("roke.uom", related="material_id.uom_id", string="计量单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom1_id",
                                        string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom2_id",
                                        string="辅计量单位2")
