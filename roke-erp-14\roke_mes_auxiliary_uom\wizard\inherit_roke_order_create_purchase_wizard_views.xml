<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="inherit_aux_roke_order_create_purchase_wizard_form" model="ir.ui.view">
        <field name="name">inherit.aux.roke.order.create.purchase.wizard.form</field>
        <field name="model">roke.order.create.purchase.wizard</field>
        <field name="inherit_id" ref="roke_mes_sale.roke_order_create_purchase_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='demand_qty']" position="after">
                <field name="demand_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
            <xpath expr="//field[@name='material_details_line_ids']//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='purchased_qty']" position="after">
                <field name="purchased_aux1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="purchased_aux2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
        </field>
    </record>

</odoo>
