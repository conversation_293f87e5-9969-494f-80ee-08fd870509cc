<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--临时订单过度-->
    <record id="inherit_uom_view_roke_sale_delivery_wizard_form" model="ir.ui.view">
        <field name="name">inherit.uom.roke.sale.delivery.wizard.form</field>
        <field name="model">roke.sale.delivery.wizard</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_delivery_wizard_form"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='current_stock_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='order_qty']" position="after">
                <field name="order_auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="order_auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='deliver_qty']" position="after">
                <field name="deliver_auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="deliver_auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>