# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import json
import math


def _get_pd(env, index="XSSL"):
    return env["decimal.precision"].precision_get(index)

class InheritSaleProductionWizard(models.TransientModel):
    _inherit = "roke.sale.production.wizard"

    def _get_line_values(self, product, sale_line):
        res = super(InheritSaleProductionWizard, self)._get_line_values(product, sale_line)
        delivery_pro_qty_method = self.env['ir.config_parameter'].sudo().get_param('delivery.pro.qty.method',
                                                                                   default="订单数量-生产中")
        if delivery_pro_qty_method == "订单数量-生产中":
            production_auxiliary1_qty = sale_line.can_production_auxiliary1_qty
            production_auxiliary2_qty = sale_line.can_production_auxiliary2_qty
        elif delivery_pro_qty_method == "订单数量-当前库存-生产中":
            production_auxiliary1_qty = sale_line.can_production_auxiliary1_qty - sale_line.stock_auxiliary1_qty if sale_line.can_production_auxiliary1_qty - sale_line.stock_auxiliary1_qty >= 0 else 0
            production_auxiliary2_qty = sale_line.can_production_auxiliary2_qty - sale_line.stock_auxiliary2_qty if sale_line.can_production_auxiliary2_qty - sale_line.stock_auxiliary2_qty >= 0 else 0
        elif delivery_pro_qty_method == "订单数量-当前库存":
            production_auxiliary1_qty = sale_line.auxiliary1_qty - sale_line.stock_auxiliary1_qty if sale_line.auxiliary1_qty - sale_line.stock_auxiliary1_qty >= 0 else 0
            production_auxiliary2_qty = sale_line.auxiliary2_qty - sale_line.stock_auxiliary2_qty if sale_line.auxiliary2_qty - sale_line.stock_auxiliary2_qty >= 0 else 0
        else:
            production_auxiliary1_qty = sale_line.auxiliary1_qty
            production_auxiliary2_qty = sale_line.auxiliary2_qty
        res['production_auxiliary1_qty'] = production_auxiliary1_qty
        res['production_auxiliary2_qty'] = production_auxiliary2_qty
        # 订单数量
        order_auxiliary1_qty = sale_line.auxiliary1_qty
        order_auxiliary2_qty = sale_line.auxiliary2_qty
        res['order_auxiliary1_qty'] = order_auxiliary1_qty
        res['order_auxiliary2_qty'] = order_auxiliary2_qty
        return res

    def _get_line_dic(self, line):
        res = super(InheritSaleProductionWizard, self)._get_line_dic(line)
        res['auxiliary1_qty'] = line.production_auxiliary1_qty
        res['auxiliary2_qty'] = line.production_auxiliary2_qty
        return res


class InheritSaleProductionDemandWizard(models.TransientModel):
    _inherit = "roke.sale.production.demand.wizard"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    production_auxiliary1_qty = fields.Float(string="本次生产辅计量1", digits='SCSL')  # TODO 不限制本次可生产数
    production_auxiliary2_qty = fields.Float(string="本次生产辅计量2", digits='SCSL')  # TODO 不限制本次可生产数
    production_auxiliary_json = fields.Char(string="本次生产数量")
    order_auxiliary1_qty = fields.Float(string="订单辅计量1", digits='SCSL')  # TODO 不限制本次可生产数
    order_auxiliary2_qty = fields.Float(string="订单辅计量2", digits='SCSL')  # TODO 不限制本次可生产数
    order_auxiliary_json = fields.Char(string="订单数量")

    @api.onchange('production_qty', 'product_id')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            self.production_auxiliary1_qty = 0
            self.production_auxiliary2_qty = 0
            if self.product_id and self.product_id.uom_type == '多计量':
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.production_qty)
                self.production_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.production_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.production_qty = 0
            self.production_auxiliary1_qty = 0
            self.production_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('production_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.production_qty)
                self.production_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.production_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('production_auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.production_auxiliary1_qty)
                self.production_qty = qty_json.get('main_qty', 0)
                self.production_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('production_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.production_auxiliary2_qty)
                self.production_qty = qty_json.get('main_qty', 0)
                self.production_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
