<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--临时订单过度-->
    <record id="view_inherit_aux_roke_sale_production_wizard_form" model="ir.ui.view">
        <field name="name">roke.inherit.aux.sale.production.wizard.form</field>
        <field name="model">roke.sale.production.wizard</field>
        <field name="type">form</field>
        <field name="inherit_id" ref="roke_mes_sale.view_roke_sale_production_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='production_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="production_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="production_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='order_qty']" position="after">
                <field name="order_auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="order_auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>