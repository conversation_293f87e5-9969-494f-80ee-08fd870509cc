# -*- coding: utf-8 -*-
"""
Description:
    订单（销售订单/生产订单）创建原材料采购订单
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _, SUPERUSER_ID
import logging
from odoo.exceptions import ValidationError
import math
import json

_logger = logging.getLogger(__name__)


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)

class InheritRokeSOCreateBOMPickingWizard(models.TransientModel):
    _inherit = "roke.so.create.bom.picking.wizard"

    def _get_picking_line_vals(self, src_location, dest_location, line):
        """
        获取调拨单需求
        :return:
        """
        res = super(InheritRokeSOCreateBOMPickingWizard, self)._get_picking_line_vals(src_location, dest_location,
                                                                                      line)
        res.update({
            "auxiliary1_qty": line.auxiliary1_qty,
            "auxiliary2_qty": line.auxiliary2_qty,
        })
        return res


class InheritRokeSOCreateBOMPickingLineWizard(models.TransientModel):
    _inherit = "roke.so.create.bom.picking.line.wizard"

    uom_id = fields.Many2one("roke.uom", related="material_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="material_id.is_real_time_calculations")
    demand_auxiliary1_qty = fields.Float(string='需求辅一数量', digits='KCSL')
    demand_auxiliary2_qty = fields.Float(string='需求辅二数量', digits='KCSL')
    demand_auxiliary_json = fields.Char(string='需求辅助计量单位')
    auxiliary1_qty = fields.Float(string="本次领料辅一数量", digits='KCSL')
    auxiliary2_qty = fields.Float(string="本次领料辅二数量", digits='KCSL')
    auxiliary_json = fields.Char(string='辅助计量单位')
    received_auxiliary1_qty = fields.Float(compute="_compute_received_qty", string="已领数量辅一数量")
    received_auxiliary2_qty = fields.Float(compute="_compute_received_qty", string="已领数量辅二数量")
    received_auxiliary_json = fields.Char(string="已领数量辅助计量单位")

    @api.onchange('material_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'main',
                                                                                    self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux1',
                                                                                    self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.material_id and self.material_id.uom_type == '多计量' and not self.material_id.is_free_conversion:
                qty_json = self.material_id.uom_groups_id.main_auxiliary_conversion(self.material_id, 'aux2',
                                                                                    self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _compute_received_qty(self):
        """
        获取已领数量
        :return:
        """
        sale_orders = self.env["roke.sale.order"].browse(self._context.get('active_ids', False))
        received_moves = sale_orders.bom_move_ids
        for record in self:
            received_qty = sum(
                received_moves.filtered(
                    lambda move: move.product_id == record.material_id
                ).mapped("finish_qty")
            )
            received_auxiliary1_qty = 0
            received_auxiliary2_qty = 0
            if record.material_id.uom_type == '多计量':
                product_uom_line1 = record.material_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == record.material_id.auxiliary_uom1_id.id)
                product_uom_line2 = record.material_id.uom_groups_id.uom_line_ids.filtered(
                    lambda a: a.uom_id.id == record.material_id.auxiliary_uom2_id.id)
                if not record.material_id.is_free_conversion:
                    received_auxiliary1_qty = received_qty * product_uom_line1.conversion if product_uom_line1 else 0
                    received_auxiliary2_qty = received_qty * product_uom_line2.conversion if product_uom_line2 else 0
                else:
                    received_auxiliary1_qty = 0
                    received_auxiliary2_qty = 0
            record.received_qty = received_qty
            record.received_auxiliary1_qty = received_auxiliary1_qty
            record.received_auxiliary2_qty = received_auxiliary2_qty


class InheritRokeSOCreateBOMPickingDemandWizard(models.TransientModel):
    _inherit = "roke.so.create.bom.picking.demand.wizard"

    demand_auxiliary1_qty = fields.Float(string='需求辅一数量', digits='CGSL')
    demand_auxiliary2_qty = fields.Float(string='需求辅二数量', digits='CGSL')
    demand_auxiliary_json = fields.Char(string='需求辅助计量单位')
    uom_id = fields.Many2one("roke.uom", related="material_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="material_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="material_id.is_real_time_calculations")
