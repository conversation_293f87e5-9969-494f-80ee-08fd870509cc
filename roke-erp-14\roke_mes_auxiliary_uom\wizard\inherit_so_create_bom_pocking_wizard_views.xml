<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="inherit_aux_roke_so_create_bom_picking_wizard_form" model="ir.ui.view">
        <field name="name">inherit.aux.roke.so.create.bom.picking.wizard.form</field>
        <field name="model">roke.so.create.bom.picking.wizard</field>
        <field name="inherit_id" ref="roke_mes_sale.roke_so_create_bom_picking_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']/tree/field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/tree/field[@name='qty']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/tree/field[@name='received_qty']" position="after">
                <field name="received_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="received_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
            </xpath>
            <xpath expr="//field[@name='demand_ids']/tree/field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>

</odoo>
