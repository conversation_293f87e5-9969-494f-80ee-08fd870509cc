# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _, SUPERUSER_ID


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)
class InheritSplitTaskWizard(models.TransientModel):
    _inherit = "roke.mes.split.task.wizard"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 可拆分计划数
    auxiliary_json = fields.Char(string="可拆分计划数")
    auxiliary1_qty = fields.Float(string="可拆分辅助数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="可拆分辅助数量2", digits='SCSL')

    @api.model
    def create(self, vals):
        res = super(InheritSplitTaskWizard, self).create(vals)
        aux1_qty = res.origin_task_id.plan_auxiliary1_qty
        aux2_qty = res.origin_task_id.plan_auxiliary2_qty
        res.auxiliary1_qty = aux1_qty
        res.auxiliary2_qty = aux1_qty
        return res

    def _get_split_task_vals(self, line):
        """
        获取拆分的下级任务内容
        :param task:
        :return:
        """
        res = super(InheritSplitTaskWizard, self)._get_split_task_vals(line)
        res.update({
            "plan_auxiliary1_qty": line.auxiliary1_qty,
            "plan_auxiliary2_qty": line.auxiliary2_qty
        })
        return res


class InheritSplitTaskLineWizard(models.TransientModel):
    _inherit = "roke.mes.split.task.line.wizard"

    product_id = fields.Many2one("roke.product", related="wizard_id.product_id", string="产品")
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 数量
    auxiliary_json = fields.Char(string="数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='SCSL')
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='SCSL')

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
