<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="view_yom_inherit_split_task_wizard_form_view" model="ir.ui.view">
        <field name="name">roke.uom.inherit.split.task.wizard.form</field>
        <field name="model">roke.mes.split.task.wizard</field>
        <field name="inherit_id" ref="roke_mes_production.view_split_task_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='can_split_qty']" position="replace">
                <label for="can_split_qty"/>
                <div name="can_split_qty" class="o_row">
                    <field name="can_split_qty"/>
                    <span name="qty_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary1_qty"
                           readonly="1" attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="auxiliary2_qty"
                           readonly="1" attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" readonly="1" force_save="1" optional="show"/>
                <field name="auxiliary_uom2_id" optional="show"/>

            </xpath>

        </field>
    </record>
</odoo>
