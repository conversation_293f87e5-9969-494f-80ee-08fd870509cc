# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _, SUPERUSER_ID


def _get_pd(env, index="XSSL"):
    return env["decimal.precision"].precision_get(index)

class InheritSplitWOWizard(models.TransientModel):
    _inherit = "roke.split.work.order.wizard"

    product_id = fields.Many2one(related="origin_wo_id.product_id", string="产品")
    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.<PERSON><PERSON><PERSON>(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 允许拆分数量
    allow_auxiliary_json = fields.Char(string="允许拆分数量")
    allow_auxiliary1_qty = fields.Float(string="允许拆分辅数量1", digits='SCSL')
    allow_auxiliary2_qty = fields.Float(string="允许拆分辅数量2", digits='SCSL')
    # 允许拆分数量
    split_auxiliary_json = fields.Char(string="每个工单数量")
    split_auxiliary1_qty = fields.Float(string="每个工单辅数量1", digits='SCSL')
    split_auxiliary2_qty = fields.Float(string="每个工单辅数量2", digits='SCSL')

    @api.onchange('split_qty', 'product_id')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            self.split_auxiliary1_qty = 0
            self.split_auxiliary2_qty = 0
            if self.product_id and self.product_id.uom_type == '多计量':
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.split_qty)
                self.split_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.split_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.split_qty = 0
            self.split_auxiliary1_qty = 0
            self.split_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('split_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.split_qty)
                self.split_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.split_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('split_auxiliary1_qty')
    def _onchange_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.split_auxiliary1_qty)
                self.split_qty = qty_json.get('main_qty', 0)
                self.split_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('split_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.split_auxiliary2_qty)
                self.split_qty = qty_json.get('main_qty', 0)
                self.split_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def _get_copy_data(self):
        """
        获取拆出工单值,添加辅计量处理
        :return:
        """
        res = super(InheritSplitWOWizard, self)._get_copy_data()
        res.update({
            "plan_auxiliary1_qty": self.split_auxiliary1_qty,
            "plan_auxiliary2_qty": self.split_auxiliary2_qty,
        })
        return res

    def _get_origin_wo_write_vals(self, sum_split_qty):
        """
        获取原工单编辑值,添加辅计量处理
        :return:
        """
        res = super(InheritSplitWOWizard, self)._get_origin_wo_write_vals(sum_split_qty)
        new_plan_qty = res.get("plan_qty")
        product = self.product_id
        if not product.is_free_conversion:
            # 取余、自由换算不处理辅计量数量
            auxiliary = self.env['roke.uom.groups'].main_auxiliary_conversion(product, 'main', new_plan_qty)
            res.update({
                "plan_auxiliary1_qty": auxiliary.get("aux1_qty", 0),
                "plan_auxiliary2_qty": auxiliary.get("aux2_qty", 0),
            })
        else:
            # 自由换算
            res.update({
                "plan_auxiliary1_qty": 0,
                "plan_auxiliary2_qty": 0,
            })
        return res

