<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--form-->
    <record id="view_uom_inherit_split_wo_wizard_form_view" model="ir.ui.view">
        <field name="name">roke.uom.inherit.split.wo.wizard.form</field>
        <field name="model">roke.split.work.order.wizard</field>
        <field name="inherit_id" ref="roke_mes_production.view_roke_split_work_order_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='allow_split_qty']" position="replace">
                <label for="allow_split_qty"/>
                <div name="allow_split_qty" class="o_row">
                    <field name="allow_split_qty"/>
                    <span name="qty_uom">
                        <field name="uom_id" class="uom_width"/>
                    </span>
                    <field name="allow_auxiliary1_qty"
                           readonly="1" attrs="{'invisible':[('auxiliary_uom1_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="allow_auxiliary2_qty"
                           readonly="1" attrs="{'invisible':[('auxiliary_uom2_id','=',False)]}"
                           force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>
            <xpath expr="//field[@name='split_qty']" position="replace">
                <label for="split_qty"/>
                <div name="split_qty" class="o_row">
                    <field name="split_qty"/>
                    <span name="qty_uom">
                        <field name="uom_id"/>
                    </span>
                    <field name="split_auxiliary1_qty" readonly="1"
                           attrs="{'invisible': [('auxiliary_uom1_id','=',False)]}" required="1" force_save="1"/>
                    <span name="qty_uom1">
                        <field name="auxiliary_uom1_id" class="uom_width"/>
                    </span>
                    <field name="split_auxiliary2_qty" readonly="1"
                           attrs="{'invisible': [('auxiliary_uom2_id','=',False)]}" required="1" force_save="1"/>
                    <span name="qty_uom2">
                        <field name="auxiliary_uom2_id" class="uom_width"/>
                    </span>
                </div>
            </xpath>

        </field>
    </record>
</odoo>
