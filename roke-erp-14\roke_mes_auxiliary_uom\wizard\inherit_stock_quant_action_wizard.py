# -*- coding: utf-8 -*-
"""
Description:
    继承入库出库调拨
Versions:
    Created by www.rokedata.com
"""
from ast import literal_eval
from odoo import _, api, fields, models
from odoo.exceptions import ValidationError
import time
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
import json


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)
class InheritRokeStockPickingWizard(models.TransientModel):
    _inherit = "roke.stock.picking.wizard"

    def confirm(self):
        # 创建调拨单
        self.check_stoct_move_condition()
        PickingObj = self.env["roke.mes.stock.picking"]
        pick = PickingObj.create({
            'picking_type_id': self.picking_type_id.id,
            'partner_id': self.partner_id.id,
            'src_location_id': self.src_location_id.id,
            'dest_location_id': self.dest_location_id.id,
            'picking_date': self.picking_date,
            'note': self.note
        })
        # 取作业人员
        operator_id = None
        for line in self.line_ids:
            # 根据移动类型的不同，所取的源位置与目标位置有所不同 -- 
            # 入库：目标位置是明细行中的位置、源位置是表头源位置
            # 出库： 源位置是明细行中的位置、目标位置是表头目标位置
            if self.type_index == '入库':
                src_location_id = self.src_location_id
                dest_location_id = line.location_id
            elif self.type_index == '出库':
                src_location_id = line.location_id
                dest_location_id = self.dest_location_id
            else:
                src_location_id = line.location_id
                dest_location_id = self.dest_location_id
            line.lot.write({'note': line.lot_note})
            operator_id = line.work_user.id
            self.env["roke.mes.stock.move"].create({
                "picking_id": pick.id,
                "src_location_id": self.src_location_id.id,
                "dest_location_id": self.dest_location_id.id,
                "product_id": line.product_id.id,
                "qty": line.qty,
                "auxiliary1_qty": line.auxiliary1_qty,
                "auxiliary2_qty": line.auxiliary2_qty,
                "move_date": fields.Date.context_today(self),
                "line_ids": [(0, 0, {
                    "picking_id": pick.id,
                    'src_location_id': src_location_id.id,
                    'dest_location_id': dest_location_id.id,
                    "product_id": line.product_id.id,
                    "qty": line.qty,
                    "move_date": fields.Date.context_today(self),
                    "auxiliary1_qty": line.auxiliary1_qty,
                    "auxiliary2_qty": line.auxiliary2_qty,
                    "lot_id": line.lot.id,
                    "lot_note": line.lot_note
                })]
            })
        pick.write({'operator_id': operator_id})
        pick.make_finish()


class InheritRokeStockPickingLineWizard(models.TransientModel):
    _inherit = "roke.stock.picking.line.wizard"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
