#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_stock_query_census.py.py
@Time:
    2022/10/9 15:07
@Site: 
    
@Desc:
    
"""

from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InheritStockQueryCensusWizard(models.TransientModel):
    _inherit = "stock.query.census.wizard"

    auxiliary1_qty = fields.Float(related="move_line_id.auxiliary1_qty", string="辅助数量1", store=True, digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="move_line_id.auxiliary_uom1_id", string="辅计量单位1",
                                        store=True)
    auxiliary2_qty = fields.Float(related="move_line_id.auxiliary2_qty", string="辅助数量2", store=True, digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="move_line_id.auxiliary_uom2_id", string="辅计量单位2",
                                        store=True)
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
