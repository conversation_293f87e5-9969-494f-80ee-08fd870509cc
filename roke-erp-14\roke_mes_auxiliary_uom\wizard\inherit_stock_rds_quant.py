#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_stock_rds_quant.py
@Time:
    2022/10/9 17:00
@Site:

@Desc:

"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError


class InheritStockRDSQuantSearchWizard(models.TransientModel):
    _inherit = "stock.rds.quant.search.wizard"

    def confirm_search(self):
        obj = super(InheritStockRDSQuantSearchWizard, self).confirm_search()
        rds_ids = obj["domain"][0][2]
        rdss = self.env['stock.rds.quant.wizard'].browse(rds_ids)
        for rds in rdss:
            # 查库存数量
            quants = self.env["roke.mes.stock.quant"].sudo().search_quants(products=rds.product_id, lots=rds.lot_id)
            quants = quants.filtered(lambda quant: quant.location_type == "内部位置")  # 添加过滤条件
            rds.write({
                "auxiliary1_qty": sum(quants.mapped("auxiliary1_qty")),
                "auxiliary2_qty": sum(quants.mapped("auxiliary2_qty"))
            })
        return obj

class InheritStockRDSQuantWizard(models.TransientModel):
    _inherit = "stock.rds.quant.wizard"

    auxiliary1_qty = fields.Float(string="库存辅数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1",
                                        store=True)
    auxiliary2_qty = fields.Float(string="库存辅数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2",
                                        store=True)
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
