# -*- coding: utf-8 -*-
"""
Description:
    委外发料
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
import json


def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritTransportationProductInfoWizard(models.TransientModel):
    _inherit = "transportation.product.info.wizard"

    def _get_move_list_vals(self, move):
        """
        获取发料内容
        :return:
        """
        res = super(InheritTransportationProductInfoWizard, self)._get_move_list_vals(move)
        res.update({
            "auxiliary1_qty": move.auxiliary1_qty,
            "auxiliary2_qty": move.auxiliary2_qty
        })
        return res


class InheritStockMoveInfoLine(models.TransientModel):
    _inherit = "stock.move.info.line"

    uom_id = fields.Many2one(related="product_id.uom_id", string="主计量")
    auxiliary_uom1_id = fields.Many2one(related="product_id.auxiliary_uom1_id", string="辅计量1")
    auxiliary_uom2_id = fields.Many2one(related="product_id.auxiliary_uom2_id", string="辅计量2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算", related="product_id.is_real_time_calculations")
    # 需求数
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
