<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="uom_inherit_transportation_product_info_wizard_form" model="ir.ui.view">
        <field name="name">uom.inherit.transportation.product.info.wizard.form</field>
        <field name="model">transportation.product.info.wizard</field>
        <field name="inherit_id" ref="roke_mes_entrust_order.transportation_product_info_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>

            </xpath>
        </field>
    </record>
</odoo>
