# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json
import math

def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)

class RokeRedPickingWizard(models.TransientModel):
    _inherit = "roke.red.picking.wizard"

    def prepare_wizard_line_dict(self, move, total_red_qty):
        result = super(RokeRedPickingWizard, self).prepare_wizard_line_dict(move, total_red_qty)
        product_uom1_line = move.product_id.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == move.product_id.auxiliary_uom1_id.id)
        product_uom2_line = move.product_id.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == move.product_id.auxiliary_uom2_id.id)
        if not move.product_id.is_free_conversion:
            # 计算辅数量1
            if not product_uom1_line:
                auxiliary1_qty = 0
            else:
                auxiliary1_qty = move.finish_qty * product_uom1_line.conversion
            # 计算辅数量2
            if not product_uom2_line:
                auxiliary2_qty = 0
            else:
                auxiliary2_qty = move.finish_qty * product_uom2_line.conversion
        else:
            auxiliary1_qty = move.finish_auxiliary1_qty
            auxiliary2_qty = move.finish_auxiliary2_qty
        result.update({"auxiliary1_qty": -1 * max(auxiliary1_qty, 0), "auxiliary2_qty": -1 * max(auxiliary2_qty, 0)})
        return result

    def prepare_move_dict(self, line):
        result = super(RokeRedPickingWizard, self).prepare_move_dict(line)
        result.update({
            "auxiliary1_qty": line.auxiliary1_qty, "auxiliary2_qty": line.auxiliary2_qty,
        })
        return result


class RokeRedPickingWizardLine(models.TransientModel):
    _inherit = "roke.red.picking.wizard.line"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")

    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.re_finish_qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('re_finish_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.re_finish_qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.re_finish_qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.re_finish_qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)
