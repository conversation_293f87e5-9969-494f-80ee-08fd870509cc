<?xml version="1.0" encoding="UTF-8" ?>
<odoo>

    <record id="inherit_view_roke_red_picking_wizard_form" model="ir.ui.view">
        <field name="name">roke.red.picking.wizard.form</field>
        <field name="model">roke.red.picking.wizard</field>
        <field name="inherit_id" ref="roke_mes_stock.view_roke_red_picking_wizard_form"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='re_finish_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>

</odoo>
