# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import json
import math

def _get_pd(env, index="KCSL"):
    return env["decimal.precision"].precision_get(index)

class RokePurchaseReceivingWizard(models.TransientModel):
    _inherit = "roke.purchase.receiving.wizard"

    def prepare_line_value(self, line):
        """
        增加辅计量信息
        :param line:
        :return:
        """
        result = super(RokePurchaseReceivingWizard, self).prepare_line_value(line)
        product_uom1_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == line.product_id.auxiliary_uom1_id.id)
        product_uom2_line = line.product_id.uom_groups_id.uom_line_ids.filtered(
            lambda a: a.uom_id.id == line.product_id.auxiliary_uom2_id.id)
        if not line.product_id.is_free_conversion:
            # 计算辅数量1
            if not product_uom1_line:
                auxiliary1_qty = 0
            else:
                auxiliary1_qty = (line.qty - line.receiving_qty) * product_uom1_line.conversion
            # 计算辅数量2
            if not product_uom2_line:
                auxiliary2_qty = 0
            else:
                auxiliary2_qty = (line.qty - line.receiving_qty) * product_uom2_line.conversion
        else:
            auxiliary1_qty = line.auxiliary1_qty - line.receiving_auxiliary1_qty
            auxiliary2_qty = line.auxiliary2_qty - line.receiving_auxiliary2_qty
        result.update({"auxiliary1_qty": max(auxiliary1_qty, 0), "auxiliary2_qty": max(auxiliary2_qty, 0)})
        order_auxiliary1_qty = line.auxiliary1_qty
        order_auxiliary2_qty = line.auxiliary2_qty
        result['order_auxiliary1_qty'] = order_auxiliary1_qty
        result['order_auxiliary2_qty'] = order_auxiliary2_qty
        return result

    def prepare_move_dict(self, line, src_location_id, dest_location_id):
        """
        move 信息
        :param line:
        :param src_location_id:
        :param dest_location_id:
        :return:
        """
        result = super(RokePurchaseReceivingWizard, self).prepare_move_dict(line, src_location_id, dest_location_id)
        result.update({
            "auxiliary1_qty": line.auxiliary1_qty, "auxiliary2_qty": line.auxiliary2_qty
        })
        return result

    def prepare_move_line_dict(self, move, stock_lot_id=None):
        result = super(RokePurchaseReceivingWizard, self).prepare_move_line_dict(move, stock_lot_id)
        result.update({
            "auxiliary1_qty": move.auxiliary1_qty, "auxiliary2_qty": move.auxiliary2_qty
        })
        return result


class RokePurchaseReceivingWizardLine(models.TransientModel):
    _inherit = "roke.purchase.receiving.wizard.line"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='KCSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='KCSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary_json = fields.Char(string="数量")
    order_auxiliary1_qty = fields.Float(string="订单辅计量1", digits='CGSL')  # TODO 不限制本次可生产数
    order_auxiliary2_qty = fields.Float(string="订单辅计量2", digits='CGSL')  # TODO 不限制本次可生产数
    order_auxiliary_json = fields.Char(string="订单数量")

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.receive_qty = 0
            self.auxiliary1_qty = 0
            self.auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('receive_qty')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.receive_qty)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.auxiliary1_qty)
                self.receive_qty = qty_json.get('main_qty', 0)
                self.auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.auxiliary2_qty)
                self.receive_qty = qty_json.get('main_qty', 0)
                self.auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)