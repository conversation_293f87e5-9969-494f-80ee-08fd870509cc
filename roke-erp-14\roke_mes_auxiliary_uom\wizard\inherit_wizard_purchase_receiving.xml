<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherit_view_roke_purchase_receiving_wizard_form" model="ir.ui.view">
        <field name="name">roke.purchase.receiving.wizard.form</field>
        <field name="model">roke.purchase.receiving.wizard</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_roke_purchase_receiving_wizard_form"/>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='receive_qty']" position="after">
                <field name="auxiliary1_qty" readonly="1" force_save="1"/>
                <field name="auxiliary2_qty" invisible="1" force_save="1"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="order_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="order_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>