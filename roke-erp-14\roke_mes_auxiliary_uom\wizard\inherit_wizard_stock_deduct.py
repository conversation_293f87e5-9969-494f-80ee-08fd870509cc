# -*- coding:utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class InheritWizardStockPickingDeductLine(models.TransientModel):
    _inherit = "wizard.stock.picking.deduct.line"
    _description = "收款明细"

    uom_id = fields.Many2one("roke.uom", related="product_id.uom_id", string="计量单位")
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    auxiliary1_qty = fields.Float(related="order_line_id.finish_auxiliary1_qty", string="数量", digits='KCSL')
    auxiliary2_qty = fields.Float(related="order_line_id.finish_auxiliary2_qty", string="数量", digits='KCSL')
    auxiliary_json = fields.Char(string="数量", related="order_line_id.finish_auxiliary_json")

    @api.onchange("deduct_amount")
    def onchange_deduct_amount(self):
        if self.deduct_amount > self.unpaid_amount:
            raise ValidationError("优惠金额不能大于待收/付款金额")
        self.pay_amount = round(self.unpaid_amount - self.deduct_amount, 2)

    @api.onchange("pay_amount")
    def onchange_pay_amount(self):
        if self.pay_amount > round(self.unpaid_amount - self.deduct_amount, 2):
            raise ValidationError("收/付款金额不能大于优惠后金额")
