<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="inherit_uom_view_wizard_stock_picking_order_deduct_form_pay" model="ir.ui.view">
        <field name="name">inherit.uom.wizard.stock.picking.deduct.pay.form</field>
        <field name="model">wizard.stock.picking.deduct</field>
        <field name="inherit_id" ref="roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form_pay"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='deduct_line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" readonly="1"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       readonly="1" force_save="1"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>

    <record id="inherit_uom_view_wizard_stock_picking_order_deduct_form" model="ir.ui.view">
        <field name="name">inherit.uom.wizard.stock.picking.deduct.form</field>
        <field name="model">wizard.stock.picking.deduct</field>
        <field name="inherit_id" ref="roke_mes_account_stock.view_wizard_stock_picking_order_deduct_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='deduct_line_ids']//field[@name='qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" readonly="1"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       readonly="1" force_save="1"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
        </field>
    </record>
</odoo>
