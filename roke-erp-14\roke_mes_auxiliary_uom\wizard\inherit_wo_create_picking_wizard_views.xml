<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_au_inherit_wo_create_picking_wizard_form" model="ir.ui.view">
        <field name="name">au.inherit.wo.create.picking.wizard.form</field>
        <field name="model">roke.wo.create.picking.wizard</field>
        <field name="inherit_id" ref="roke_mes_workshop_material.roke_wo_create_picking_wizard_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="demand_auxiliary1_qty" readonly="1"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="demand_auxiliary2_qty" optional="show"
                       readonly="1" force_save="1"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>
            <xpath expr="//field[@name='qty']" position="after">
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show" required="1"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}" required="1"/>
            </xpath>
            <xpath expr="//field[@name='stock_qty']" position="after">
                <field name="stock_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show" required="1"/>
                <field name="stock_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom2_id','=',False)]}" required="1"/>
            </xpath>
        </field>
    </record>
</odoo>
