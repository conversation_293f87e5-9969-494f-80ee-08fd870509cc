# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import json
import math


def _get_pd(env, index="CGSL"):
    return env["decimal.precision"].precision_get(index)


class InhertWizardGeneratePurchaseOrder(models.TransientModel):
    _inherit = "wizard.generate.purchase.order"

    def _get_purchase_detail_val(self, purchase_order_id, line_id):
        res = super(InhertWizardGeneratePurchaseOrder, self)._get_purchase_detail_val(purchase_order_id, line_id)
        res['auxiliary1_qty'] = line_id.quantity_auxiliary1_qty
        res['auxiliary2_qty'] = line_id.quantity_auxiliary2_qty
        return res


class InhertWizardGeneratePurchaseOrderLine(models.TransientModel):
    _inherit = 'wizard.generate.purchase.order.line'

    auxiliary_json = fields.Char(string="数量")
    auxiliary_json1 = fields.Char(string="已下达数量")
    auxiliary_json2 = fields.Char(string="本次采购数量")
    auxiliary1_qty = fields.Float(string="辅助数量1", digits='CGSL')
    auxiliary_uom1_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom1_id", string="辅计量单位1")
    auxiliary2_qty = fields.Float(string="辅助数量2", digits='CGSL')
    auxiliary_uom2_id = fields.Many2one("roke.uom", related="product_id.auxiliary_uom2_id", string="辅计量单位2")
    is_real_time_calculations = fields.Boolean(string="辅计量是否实时计算",
                                               related="product_id.is_real_time_calculations")
    purchased_auxiliary1_qty = fields.Float(string="已下达数量1", digits='CGSL',
                                            related='requisition_line_id.purchased_auxiliary1_qty')
    purchased_auxiliary2_qty = fields.Float(string="已下达数量2", digits='CGSL',
                                            related='requisition_line_id.purchased_auxiliary2_qty')
    quantity_auxiliary1_qty = fields.Float(string="本次采购数量1", digits='CGSL')
    quantity_auxiliary2_qty = fields.Float(string="本次采购数量2", digits='CGSL')

    @api.onchange('product_id')
    def _onchange_aux_product(self):
        if not self.env.context.get('calculate_discount', False):
            self.quantity = 0
            self.quantity_auxiliary1_qty = 0
            self.quantity_auxiliary2_qty = 0
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('quantity')
    def _onchange_aux_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'main',
                                                                                   self.quantity)
                self.quantity_auxiliary1_qty = qty_json.get('aux1_qty', 0)
                self.quantity_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('quantity_auxiliary1_qty')
    def _onchange_plan_auxiliary1_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux1',
                                                                                   self.quantity_auxiliary1_qty)
                self.quantity = qty_json.get('main_qty', 0)
                self.quantity_auxiliary2_qty = qty_json.get('aux2_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    @api.onchange('quantity_auxiliary2_qty')
    def _onchange_auxiliary2_qty(self):
        if not self.env.context.get('calculate_discount', False):
            if self.product_id and self.product_id.uom_type == '多计量' and not self.product_id.is_free_conversion:
                qty_json = self.product_id.uom_groups_id.main_auxiliary_conversion(self.product_id, 'aux2',
                                                                                   self.quantity_auxiliary2_qty)
                self.quantity = qty_json.get('main_qty', 0)
                self.quantity_auxiliary1_qty = qty_json.get('aux1_qty', 0)
            self.env.context = dict(self.env.context, calculate_discount=True)

    def compute_aux_qty(self, vals, product_id=False):
        product_onj = self.env['roke.product']
        if vals.get("auxiliary_json2", False):
            # 如果获取到了json数据，对其进行解析
            if not vals.get('product_id', False):
                product_record = product_onj.browse(product_id)
            else:
                product_record = product_onj.browse(vals.get('product_id', False))
            is_free_conversion = product_record.is_free_conversion
            product_uom_line1 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom1_id.id)
            product_uom_line2 = product_record.uom_groups_id.uom_line_ids.filtered(
                lambda a: a.uom_id.id == product_record.auxiliary_uom2_id.id)
            # 如果不取余计算，直接保存json，如果是取余计算，算出新的保存
            auxiliary_json = json.loads(vals.get("auxiliary_json", ""))
            main_qty = float(auxiliary_json.get('main_qty', 0)) if auxiliary_json.get('main_qty', 0) else 0
            aux1_qty = float(auxiliary_json.get('aux1_qty', 0)) if auxiliary_json.get('aux1_qty', 0) else 0
            aux2_qty = float(auxiliary_json.get('aux2_qty', 0)) if auxiliary_json.get('aux2_qty', 0) else 0
            # 非自由换算
            if not is_free_conversion:
                not_free_main_qty = 0
                not_free_aux1_qty = 0
                not_free_aux2_qty = 0
                if main_qty:
                    not_free_main_qty = main_qty
                    if product_uom_line1 and product_uom_line1.conversion:
                        not_free_aux1_qty = not_free_main_qty * product_uom_line1.conversion
                    if product_uom_line2 and product_uom_line2.conversion:
                        not_free_aux2_qty = not_free_main_qty * product_uom_line2.conversion
                elif aux1_qty:
                    not_free_aux1_qty = aux1_qty
                    if product_uom_line1 and product_uom_line1.conversion:
                        not_free_main_qty = not_free_aux1_qty / product_uom_line1.conversion
                        if product_uom_line2 and product_uom_line2.conversion:
                            not_free_aux2_qty = not_free_main_qty * product_uom_line2.conversion
                elif aux2_qty:
                    not_free_aux2_qty = aux2_qty
                    if product_uom_line2 and product_uom_line2.conversion:
                        not_free_main_qty = not_free_aux2_qty / product_uom_line2.conversion
                        if product_uom_line1 and product_uom_line1.conversion:
                            not_free_aux1_qty = not_free_main_qty * product_uom_line1.conversion
                vals['quantity'] = not_free_main_qty
                vals['quantity_auxiliary1_qty'] = not_free_aux1_qty
                vals['quantity_auxiliary2_qty'] = not_free_aux2_qty
            else:
                vals['quantity'] = main_qty
                vals['quantity_auxiliary1_qty'] = aux1_qty
                vals['quantity_auxiliary2_qty'] = aux2_qty
        return vals
