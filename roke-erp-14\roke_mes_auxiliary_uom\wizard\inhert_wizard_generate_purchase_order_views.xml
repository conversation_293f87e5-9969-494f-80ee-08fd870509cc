<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="inherit_view_wizard_generate_purchase_order_form" model="ir.ui.view">
        <field name="name">wizard.generate.purchase.order.form</field>
        <field name="model">wizard.generate.purchase.order</field>
        <field name="inherit_id" ref="roke_mes_purchase.view_wizard_generate_purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='line_ids']//field[@name='demand_qty']" position="after">
                <field name="uom_id" optional="show"/>
                <field name="auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="auxiliary_uom1_id" optional="show"/>
                <field name="auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}" force_save="1"/>
                <field name="auxiliary_uom2_id" optional="show"/>
            </xpath>

            <xpath expr="//field[@name='line_ids']//field[@name='purchased_qty']" position="after">
                <field name="purchased_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="purchased_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}" force_save="1"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']//field[@name='quantity']" position="after">
                <field name="quantity_auxiliary1_qty" attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}"
                       force_save="1" optional="show"/>
                <field name="quantity_auxiliary2_qty" optional="show"
                       attrs="{'readonly': [('auxiliary_uom1_id','=',False)]}" force_save="1"/>
            </xpath>
        </field>
    </record>
</odoo>
