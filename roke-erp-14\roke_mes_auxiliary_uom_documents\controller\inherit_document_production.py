# -*- coding: utf-8 -*-
"""
Description:
    文档管理图纸报工辅计量处理
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import logging
from odoo import http, SUPERUSER_ID
from odoo.addons.roke_mes_documents.controllers.document_production import Main
import json

_logger = logging.getLogger(__name__)


def _get_pd(env, index="SCSL"):
    return env["decimal.precision"].precision_get(index)


class InheritRisDocument(Main):

    def _document_get_aux_info(self, product):
        """
        图纸报工获取辅计量内容，和工单获取辅计量内容的区别是，这里返回的数量都是0
        :param product:
        :return:
        """
        if product.uom_groups_id:
            if not product.uom_groups_id.is_free_conversion:
                aux_compute_type = 'normal'
            # 自由
            else:
                aux_compute_type = 'free'
        else:
            aux_compute_type = ""
        aux_list = [{
            "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
            "aux_conversion": rec.conversion,
            "aux_uom_name": rec.uom_id.name,
            "aux_qty": 0
        } for rec in product.uom_groups_id.uom_line_ids]
        return {
            "aux_list": aux_list,
            "uom_name": product.uom_id.name or "",
            "aux_compute_type": aux_compute_type
        }

    def get_document_wo_vals(self, order):
        """
        获取图纸报工工单值
        :return:
        """
        res = super(InheritRisDocument, self).get_document_wo_vals(order)
        res.update(self._document_get_aux_info(order.product_id))
        return res

    def get_document_no_wo_vals(self, process, product):
        """
        获取图纸报工无工单值
        :return:
        """
        res = super(InheritRisDocument, self).get_document_no_wo_vals(process, product)
        aux_info = self._document_get_aux_info(product)
        res.update(aux_info)
        unaux_list = aux_info.get("aux_list", [])
        res.update({
            "unaux_list": unaux_list
        })
        return res

    def _get_get_document_wr_vals(self, wo, employee, data):
        """
        获取图纸报工提交值
        :return:
        """
        res = super(InheritRisDocument, self)._get_get_document_wr_vals(wo, employee, data)
        res["aux_compute_type"] = data.get("aux_compute_type", "")
        res["aux_list"] = data.get("aux_list", [])
        res["unaux_list"] = data.get("unaux_list", [])
        return res

    @http.route('/roke/mes/get_order_by_qrcode', type='json', auth="none", methods=['POST', 'OPTIONS'], csrf=False,
                cors='*')
    def get_order_by_qrcode(self):
        res = super(InheritRisDocument, self).get_order_by_qrcode()
        data = res.get('result', [])
        if data:
            result = data.get('result', {})
            product = result.get('product', {})
            product_id = product.get('id', False)
            if product_id:
                product_record = http.request.env['roke.product'].sudo().browse(product_id)
                aux_compute_type = ''
                if product_record.uom_groups_id:
                    if not product_record.uom_groups_id.is_free_conversion:
                        aux_compute_type = 'normal'
                    # 自由
                    else:
                        aux_compute_type = 'free'
                    aux_list = [{
                        "aux_type": 1 if rec.uom_grade == "辅计量1" else 2,
                        "aux_conversion": rec.conversion,
                        "aux_uom_name": rec.uom_id.name
                    } for rec in product_record.uom_groups_id.uom_line_ids]
                    result.update({
                        "aux_list": aux_list,
                        "uom_name": product_record.uom_id.name or "",
                        "aux_compute_type": aux_compute_type
                    })
                    data.update({
                        "result": result
                    })
        res.update({
            "result": data
        })
        return res
