# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import json
from odoo import models, fields, api, _


class InheritRokeWorkRecordChangeLine(models.Model):
    _inherit = "roke.work.record.change.line"

    def change_execute_other(self):
        res = super(InheritRokeWorkRecordChangeLine, self).change_execute_other()
        self.wr_id.work_order_id.update_aux_qty(
            -self.change_qty,
            0,
            0,
            0,
            0,
            0        )
        return res

    def cancel_change_execute_other(self):
        res = super(InheritRokeWorkRecordChangeLine, self).change_execute_other()
        self.wr_id.work_order_id.update_aux_qty(
            self.change_qty,
            0,
            0,
            0,
            0,
            0
        )
        return res
