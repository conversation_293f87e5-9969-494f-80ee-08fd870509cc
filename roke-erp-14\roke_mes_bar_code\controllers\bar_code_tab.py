# -*- coding: utf-8 -*-
"""
Description:
   一码通相关接口
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
import logging
from odoo.exceptions import UserError
from odoo.addons.roke_mes_client.controller.mobile import Mobile

_logger = logging.getLogger(__name__)


class BarCodeTab(http.Controller):

    @http.route('/roke/get_barcode_lines_tab', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_barcode_lines_tab(self):
        """
        获取条码类别对应条码明细和一码通接口
        :return:
        """
        code = http.request.jsonrequest.get('code')
        try:
            if code:
                barcode = http.request.env['roke.barcode'].sudo().search([('code', '=', code)], limit=1)
                if barcode:
                    barcode_rule = barcode.barcode_rule
                    barcode_lines = barcode_rule.barcode_lines
                    tab_ids = barcode_rule.tab_ids
                    barcode_lines_list = []
                    tab_ids_list = []
                    for line in barcode_lines:
                        barcode_line = {
                            "code": line.code,  # 条码号
                            "qty": line.qty,  # 数量
                            "auxiliary_qty": line.auxiliary_qty,  # 辅数量
                            "create_uid": line.create_uid.name,  # 创建人
                            "create_date": line.create_date,  # 创建时间
                            "note": line.note  # 说明
                        }
                        barcode_lines_list.append(barcode_line)
                    for tab in tab_ids:
                        tab_id = {
                            "path_name": tab.path_name.name,  # 跳转路径名称
                            "path_index": tab.path_index  # 跳转路径标识
                        }
                        tab_ids_list.append(tab_id)
                    result = {'state': 'success', 'msg': '获取成功', 'barcode_lines': barcode_lines_list,
                              'tab_ids': tab_ids_list}
                else:
                    raise UserError('找不到对应的条码记录!')
            else:
                raise UserError('入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msg': str(e)}

        return result

    @http.route('/roke/get_barcode_line', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                    cors='*')
    def get_barcode_line(self):
        """
        获取条码内容
        :return:
        """
        code = http.request.jsonrequest.get('code')
        try:
            if code:
                barcode = http.request.env['roke.barcode'].sudo().search([('code', '=', code)], limit=1)
                barcode_line = [{
                    "field_index": "package_code",
                    "value_id": barcode.id,
                    "value": barcode.code,
                    "field_description": "条码号",
                    "field_type": "many2one",
                },
                    {
                        "field_index": "qty",
                        "value_id": False,
                        "value": barcode.qty,
                        "field_description": "数量",
                        "field_type": "float",
                    },
                    {
                        "field_index": "auxiliary_qty",
                        "value_id": False,
                        "value": barcode.auxiliary_qty,
                        "field_description": "辅数量",
                        "field_type": "float",
                    },
                    {
                        "field_index": "create_uid",
                        "value_id": barcode.create_uid.id,
                        "value": barcode.create_uid.name,
                        "field_description": "创建人",
                        "field_type": "many2one",
                    },
                    {
                        "field_index": "create_date",
                        "value_id": False,
                        "value": barcode.create_date,
                        "field_description": "创建时间",
                        "field_type": "datetime",
                    },
                    {
                        "field_index": "note",
                        "value_id": False,
                        "value": barcode.note,
                        "field_description": "说明",
                        "field_type": "char",
                    }
                ]
                if barcode:
                    result = {'state': 'success', 'msg': '获取成功',
                              'barcode_line': barcode_line}
                else:
                    raise UserError('条码不存在!')
            else:
                raise UserError('入参存在为空!')
        except Exception as e:
            result = {'state': 'error', 'msg': e}
        return result


class InheritMobile(Mobile):
    @http.route('/roke/get_mobile_order', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_mobile_order(self):
        """
        是否移动下单
        如果是移动下单：返回model和id
        """
        code = http.request.jsonrequest.get('code')
        try:
            if not code:
                raise UserError('入参存在为空!')

            barcode = http.request.env['roke.barcode'].sudo().search([('code', '=', code)], limit=1)
            if not barcode:
                raise UserError('找不到对应的条码记录!')

            barcode_rule = barcode.barcode_rule
            tab_ids = barcode_rule.tab_ids

            user = http.request.env.user
            allow_functions = user.groups_id.app_function_ids
            categories = http.request.env['roke.app.function.category'].search(
                [('function_ids', 'in', tab_ids.mapped('path_name').ids)], order='sequence')
            category_info = []
            if categories:
                for category in categories:
                    functions = []
                    functions_data = http.request.env['roke.app.function'].search(
                        [('category_id', '=', category.id), ('id', 'in', tab_ids.mapped('path_name').ids)])
                    for function in functions_data:
                        if not user.has_group("base.group_system") and function not in allow_functions:
                            # 系统管理员不做限制
                            continue
                        if function.general_order:
                            functions.append({
                                "index": function.index,
                                "function_id": function.id,
                                'model_id': barcode.model_id.model,
                                "order_id": barcode.source_data,
                                "name": function.name,
                                "general_order": function.general_order,
                                "icon": "../../static/%s.png" % (function.icon_image or function.index)
                            })
                        else:
                            icon = function.icon_image or function.index
                            functions.append(self._get_function_info(function, icon))
                    if functions:
                        if category.app_attribute == '移动端':
                            category_info.append({
                                "title": category.name,
                                "icon_1": "../../static/%s.png" % (str(category.index) + '_1'),
                                "icon_2": "../../static/%s.png" % (str(category.index) + '_2'),
                                "icon_3": "../../static/%s.png" % (str(category.index) + '_3'),
                                "icon_4": "../../static/%s.png" % (str(category.index) + '_4'),
                                "details": functions
                            })
            return {"state": "success", "msgs": "获取成功", "result": category_info}
        except Exception as e:
            return {"state": "error", "msgs": str(e)}
