# -*- coding: utf-8 -*-
"""
Description:
    通用单据配置接口
Versions:
    Created by www.rokedata.com<wsc>
"""
import math
from datetime import timedelta
from odoo import models, fields, http, SUPERUSER_ID, api, _
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)
import requests, json
from datetime import date, datetime
import time, re, sys, os
import jinja2
import qrcode
import io
import base64
from odoo.addons.roke_mes_client.controller.app_general_order import Mobile

if hasattr(sys, 'frozen'):
    # When running on compiled windows binary, we don't have access to package loader.
    path = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'views'))
    loader = jinja2.FileSystemLoader(path)
else:
    loader = jinja2.PackageLoader('odoo.addons.roke_mes_client', "views")

env = jinja2.Environment(loader=loader, autoescape=True)
env.filters["json"] = json.dumps


class InheritMobile(Mobile):

    @http.route('/roke/get_general_order_list', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_general_order_list(self):
        """
        获取单据列表接口
        :return:
        """
        res = super(InheritMobile, self).get_general_order_list()
        if res.get('state', 'error') == 'success':
            model_index = http.request.jsonrequest.get('model_index', "")
            function_id = http.request.jsonrequest.get('function_id', "")
            general_order = self._get_general_order(model_index, function_id)
            res['document_capacity'] = general_order.document_capacity
            res['capacity_count'] = general_order.capacity_count
            res['is_auto_save'] = general_order.is_auto_save
            res['is_auto_print'] = general_order.is_auto_print
            res['is_qty_edit'] = general_order.is_qty_edit
            res['barcode_rule_id'] = general_order.barcode_rule_id.id
            res['print_url'] = general_order.print_style.report_name if general_order.print_style else ''
            res['report_name'] = self.get_print_template(general_order.print_style.id if general_order.print_style else False)
        return res

    @http.route('/roke/get_barcode_order_detail', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def get_barcode_order_detail(self):
        """
        获取包码详情接口
        :return:
        """
        package_code = http.request.jsonrequest.get('package_code', "")
        function_id = http.request.jsonrequest.get('function_id', "")
        general_order = self._get_general_order('roke.barcode.package', function_id)
        if not general_order:
            return {"state": "error", "msgs": "未找到对应单据，请联系管理员配置移动端单据。"}
        ObjModel = http.request.env['roke.barcode.package']
        record = False
        if package_code:
            record = ObjModel.search([('package_code', '=', int(package_code))], limit=1)
        record_header = self._get_field_valus(record, general_order.header_field_ids)
        # 获取单据的明细字段
        record_detail_record = []
        top_label_list = self._get_top_label(general_order.detail_form_field_ids, detail=True)
        detail_model = False
        if record:
            detail_records = getattr(record, 'line_ids')
            for detail_record in detail_records:
                record_detail_record.append(
                    self._get_field_valus(detail_record, general_order.detail_form_field_ids, detail=True))
        record_detail_format = self._get_field_valus(False, general_order.detail_form_field_ids, detail=True)
        function_list = self._get_functions(record, general_order.order_function_ids, user=http.request.env.user.id)
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "record_header": record_header,
            "record_detail_record": record_detail_record,
            "record_detail_format": record_detail_format,
            "top_label_list": top_label_list,
            "function_list": function_list,
            "detail_display_list": general_order.detail_display_list
        }
        return return_data


class Package(http.Controller):

    @http.route('/roke/check_package_code', type='json', methods=["POST", "OPTIONS"], auth='user', csrf=False,
                cors='*')
    def check_package_code(self):
        """
        包号校验接口
        :return:
        """
        code = http.request.jsonrequest.get('code')
        # 如果有包号则去校验，如果没有包号则新建
        if code:
            package = http.request.env['roke.barcode'].sudo().search([('code', '=', code)], limit=1)
            if package:
                barcode = http.request.env['roke.barcode.package'].sudo().search([('package_code', '=', package.id)], limit=1)
                res = {'state': 'success', 'msg': '获取成功', 'code': package.code, 'id': package.id, 'order_id': str(barcode.id) if barcode else False}
            else:
                res = {'state': 'error', 'msg': '输入的包号未在系统中获取到,请重新输入'}
        else:
            # 如果没有编号则根据配置方案中的打包类型数据自动创建一个
            barcode_rule_id = http.request.jsonrequest.get('barcode_rule_id')
            barcode_rule_record = http.request.env['roke.barcode.rule'].sudo().browse(barcode_rule_id)
            if not barcode_rule_record:
                res = {'state': 'error', 'msg': '未获取到打包类型,无法自动创建包号'}
            else:
                if not barcode_rule_record.item_ids:
                    res = {'state': 'error', 'msg': '未设置条码组成元素,无法自动创建包号'}
                else:
                    barcode_record = http.request.env["roke.barcode"].sudo().create({
                        "barcode_rule": barcode_rule_record.id,
                        "code": barcode_rule_record.next_code()
                    })
                    res = {'state': 'success', 'msg': '获取成功', 'code': barcode_record.code, 'id': barcode_record.id, 'order_id': False}
        return res