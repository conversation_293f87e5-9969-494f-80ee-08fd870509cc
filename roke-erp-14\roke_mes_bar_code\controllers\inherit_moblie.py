# -*- coding: utf-8 -*-

import math
from datetime import timed<PERSON>ta
from odoo import models, fields, http, SUPERUSER_ID, api, _
import logging
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)
import requests, json
from datetime import date, datetime
import time, re, sys, os
import jinja2
from odoo.addons.roke_mes_client.controller.mobile import Mobile

if hasattr(sys, 'frozen'):
    # When running on compiled windows binary, we don't have access to package loader.
    path = os.path.realpath(os.path.join(os.path.dirname(__file__), '..', 'views'))
    loader = jinja2.FileSystemLoader(path)
else:
    loader = jinja2.PackageLoader('odoo.addons.roke_mes_client', "views")

env = jinja2.Environment(loader=loader, autoescape=True)
env.filters["json"] = json.dumps

class InheritMobilePhone(Mobile):

    def _get_function_info(self, function, icon):
        """
        获取app功能内容
        :param function:
        :return:
        """
        res = super(InheritMobilePhone, self)._get_function_info(function, icon)
        if res.get('index') == 'roke.barcode.package':
            general_order = http.request.env(user=SUPERUSER_ID)['roke.app.general.order.setting'].search(
                [('app_function_id', '=', function.id)], order='id', limit=1)
            if general_order:
                res['barcode_function'] = general_order.barcode_function
            else:
                res['barcode_function'] = '打包功能'
        return res