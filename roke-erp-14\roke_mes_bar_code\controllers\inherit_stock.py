# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError
import json
import logging
import base64
from datetime import datetime,date
import time
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
import re
import math
from odoo.addons.roke_mes_stock.controller.main import Main

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]

class InheritStock(Main):

    def _get_in_detailed_info1(self, product, product_qty, product_lot, location_id, location_dict, quant_id,
                               barcode_code=False):
        detailed_info = {
            'd_id': product.id,
            'd_code': product.code,
            'd_name': product.name,
            'd_model': '' if product.model == False else product.model,
            'd_qty': product_qty,
            'd_lot': product_lot.name or '',
            'master_uom': product.uom_id.name or '',
            'src_location_id': location_id,
            'dest_location_id': location_dict['dest_location_id'],
            'in_out_quant_id': quant_id.id,
            'barcode_code': barcode_code
        }
        return detailed_info

    def _get_in_detailed_info2(self, product, product_qty, product_lot, location_id, location_dict, quant_id,
                               barcode_code=False):
        detailed_info = {
            'd_id': product.id,
            'd_code': product.code,
            'd_name': product.name,
            'd_model': '' if product.model == False else product.model,
            'd_qty': product_qty,
            'd_lot': product_lot or '',
            'master_uom': product.uom_id.name or '',
            'src_location_id': location_id,
            'dest_location_id': location_dict['dest_location_id'],
            'in_out_quant_id': quant_id.id,
            'barcode_code': barcode_code
        }
        return detailed_info

    def _get_in_detailed_info3(self, product, product_qty, product_lot, location_id, location_dict, quant_id,
                               barcode_code=False):
        detailed_info = {
            'd_id': product.id,
            'd_code': product.code,
            'd_name': product.name,
            'd_model': '' if product.model == False else product.model,
            'd_qty': product_qty,
            'd_lot': product_lot or '',
            'master_uom': product.uom_id.name or '',
            'src_location_id': location_dict['src_location_id'],
            'dest_location_id': location_id,
            'in_out_quant_id': quant_id.id,
            'barcode_code': barcode_code
        }
        return detailed_info

    def _get_out_detailed_info1(self, product, product_qty, location_id, location_dict, quant_id, barcode_code=False):
        detailed_info = {
            'd_id': product.id,
            'd_code': product.code,
            'd_name': product.name,
            'd_model': '' if product.model == False else product.model,
            'd_qty': product_qty,
            'd_lot': quant_id.lot_id.name or '',
            'master_uom': product.uom_id.name or '',
            'src_location_id': location_id,
            'dest_location_id': location_dict['dest_location_id'],
            'in_out_quant_id': quant_id.id,
            'barcode_code': barcode_code
        }
        return detailed_info

    def _get_out_detailed_info2(self, product, product_qty, location_id, location_dict, quant_id, barcode_code=False):
        detailed_info = {
            'd_id': product.id,
            'd_code': product.code,
            'd_name': product.name,
            'd_model': '' if product.model == False else product.model,
            'd_qty': product_qty,
            'd_lot': quant_id.lot_id.name or '',
            'master_uom': product.uom_id.name or '',
            'src_location_id': location_id,
            'dest_location_id': location_dict['dest_location_id'],
            'in_out_quant_id': quant_id.id,
            'barcode_code': barcode_code
        }
        return detailed_info

    # 确认入库/出库/调拨判断接口

    def _get_in_move_line_vals(self, i, picking_id, move_id, lot_id):
        res = super(InheritStock, self)._get_in_move_line_vals(i, picking_id, move_id, lot_id)
        res['barcode_code'] = i.get('barcode_code') or ''
        return res

    def _get_out_move_line_vals(self, i, picking_id, move_id, lot_id):
        res = super(InheritStock, self)._get_out_move_line_vals(i, picking_id, move_id, lot_id)
        res['barcode_code'] = i.get('barcode_code') or ''
        return res
