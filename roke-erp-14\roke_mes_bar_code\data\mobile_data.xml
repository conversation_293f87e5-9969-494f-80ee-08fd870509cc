<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="default_roke_barcode_rule" model="roke.barcode.rule">
            <field name="name">默认</field>
            <field name="state">128</field>
            <field name="model_id" ref="roke_mes_base.model_roke_product"/>
            <field name="print_style" ref="barcode_code"/>
            <field name="item_ids" eval="[(5, 0, 0),
                (0, 0, {'sequence': '1', 'type': '时间', 'date_format': '四位年'}),
                (0, 0, {'sequence': '5', 'type': '序号', 'code_length': '3', 'code_model': 'always'}),
            ]"/>
        </record>

        <!--打包功能-->
        <record id="mobile_roke_barcode_rule_function" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_bar_code.model_roke_barcode_package"/>
            <field name="model_name">条码打包</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="detail_field_id" ref="roke_mes_bar_code.field_roke_barcode_package__line_ids"/>
            <field name="detail_model_id" ref="roke_mes_bar_code.model_roke_barcode_package_line"/>
            <field name="base_data">False</field>
            <field name="barcode_rule_id" ref="roke_mes_bar_code.default_roke_barcode_rule"/>
            <field name="barcode_package_name">默认-条码打包功能</field>
            <field name="barcode_function">打包功能</field>
        </record>

        <record id="mobile_roke_barcode_list_field_f_ids_01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package__package_code"/>
        </record>
        <record id="mobile_roke_barcode_list_field_f_ids_02" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package__create_uid"/>
            <field name="zdy_field_description">创建人</field>
        </record>
        <record id="mobile_roke_barcode_list_field_f_ids_03" model="roke.app.general.order.fields.list">
            <field name="sequence">10</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package__create_date"/>
            <field name="zdy_field_description">创建时间</field>
        </record>

        <record id="mobile_roke_barcode_search_field_f_ids_01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package__package_code"/>
        </record>
        <record id="mobile_roke_barcode_search_field_f_ids_02" model="roke.app.general.order.fields.search">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package__create_uid"/>
        </record>

        <record id="mobile_roke_barcode_detail_f_field_ids_01" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package_line__package_code"/>
        </record>
        <record id="mobile_roke_barcode_detail_f_field_ids_02" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_function"/>
            <field name="field_id" ref="field_roke_barcode_package_line__qty"/>
        </record>


        <!--打包记录-->
        <record id="mobile_roke_barcode_rule_record" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_bar_code.model_roke_barcode_package"/>
            <field name="model_name">打包记录</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="detail_field_id" ref="roke_mes_bar_code.field_roke_barcode_package__line_ids"/>
            <field name="detail_model_id" ref="roke_mes_bar_code.model_roke_barcode_package_line"/>
            <field name="base_data">False</field>
            <field name="barcode_rule_id" ref="roke_mes_bar_code.default_roke_barcode_rule"/>
            <field name="barcode_package_name">默认-条码打包记录</field>
            <field name="barcode_function">打包记录</field>
        </record>

        <record id="mobile_roke_barcode_list_r_field_ids_01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package_line__package_code"/>
        </record>
        <record id="mobile_roke_barcode_list_r_field_ids_02" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package_line__create_uid"/>
            <field name="zdy_field_description">创建人</field>
        </record>
        <record id="mobile_roke_barcode_list_r_field_ids_03" model="roke.app.general.order.fields.list">
            <field name="sequence">10</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package_line__create_date"/>
            <field name="zdy_field_description">创建时间</field>
        </record>

        <record id="mobile_roke_barcode_search_field_r_ids_01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package__package_code"/>
        </record>
        <record id="mobile_roke_barcode_search_field_r_ids_02" model="roke.app.general.order.fields.search">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package__create_uid"/>
        </record>

        <record id="mobile_roke_barcode_detail_r_field_ids_01" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package_line__package_code"/>
        </record>
        <record id="mobile_roke_barcode_detail_r_field_ids_02" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">5</field>
            <field name="order_id" ref="mobile_roke_barcode_rule_record"/>
            <field name="field_id" ref="field_roke_barcode_package_line__qty"/>
        </record>
    </data>
</odoo>