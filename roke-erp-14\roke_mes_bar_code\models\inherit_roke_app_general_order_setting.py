# -*- coding: utf-8 -*-"""
# Description:
#     APP通用单据配置
# Versions:
#     Created by www.rokedata.com<wsc>
# """
from odoo import models, fields, api, _
import requests
import logging
import json, re
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class InheritRokeAppGeneralOrderSetting(models.Model):
	_inherit = "roke.app.general.order.setting"

	barcode_package_name = fields.Char(string="方案名称")
	barcode_rule_id = fields.Many2one('roke.barcode.rule', string='打包类型')
	document_capacity = fields.Boolean(string="单据容量")
	capacity_count = fields.Integer(string="容量")
	is_auto_save = fields.Boolean(string="自动保存")
	is_auto_print = fields.Boolean(string="自动打印")
	model = fields.Char(string="模型", related='model_id.model')
	print_style = fields.Many2one('ir.actions.report', string="打印样式", related='barcode_rule_id.print_style')
	is_qty_edit = fields.Boolean(string="条码数量可修改")
	barcode_function = fields.Selection([("打包记录", "打包记录"), ("打包功能", "打包功能")], string="功能类型", default="打包功能")

	@api.onchange('document_capacity')
	def onchage_document_capacity(self):
		self.capacity_count = 0
		self.is_auto_print = False
		self.is_auto_save = False

	@api.model
	def default_get(self, field_names):
		default = super().default_get(field_names)
		if self.env.context.get('is_barcode_package', False):
			model_record = self.env['ir.model'].search([('model', '=', 'roke.barcode.package')], limit=1)
			detail_model_record = self.env['ir.model'].search([('model', '=', 'roke.barcode.package.line')], limit=1)
			detail_field_record = self.env['ir.model.fields'].search(
				[('model', '=', 'roke.barcode.package'), ('name', '=', 'line_ids')], limit=1)
			app_category_record = self.env['roke.app.function.category'].search([('index', '=', 'ydxd')], limit=1)
			default['model_id'] = model_record.id
			default['belong_to_platform'] = 'APP移动下单'
			default['is_mobile'] = True
			default['model_name'] = model_record.name
			default['detail_field_id'] = detail_field_record.id
			default['detail_model_id'] = detail_model_record.id
			default['display_list'] = '卡片显示'
			default['detail_display_list'] = '列表显示'
			default['app_category_id'] = app_category_record.id
		return default
