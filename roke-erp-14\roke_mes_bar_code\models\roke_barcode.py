# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<Caiqigang>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from odoo.modules.module import get_module_resource
from datetime import datetime, timedelta


class RokeBarcode(models.Model):
    _name = "roke.barcode"
    _inherit = ['mail.thread', 'mail.activity.mixin', 'roke.order.print.mixin']
    _description = "条码字典"
    _order = "id desc"
    _rec_name = "code"

    model_id = fields.Many2one('ir.model', string="模型")
    model_field_id = fields.Integer(string="源id")
    source_data = fields.Char(string="源数据", index=True, tracking=True, copy=False)
    code = fields.Char(string="条码号", required=True, index=True, tracking=True, copy=False, default="/")
    qty = fields.Float(string="数量", tracking=True)
    auxiliary_qty = fields.Float(string="辅数量", tracking=True)

    barcode_rule = fields.Many2one('roke.barcode.rule', string="条码类型")

    note = fields.Text(string="说明")
    index = fields.Char(string="生成标识")

    line_ids = fields.One2many('roke.barcode.line', 'barcode_id', string="条码属性")

    _sql_constraints = [
        ('code_product_unique', 'UNIQUE(code)', '已存在此条码，不可重复。')
    ]

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.barcode.code')
        return super(RokeBarcode, self).create(vals)


class RokeBarcodeLine(models.Model):
    _name = "roke.barcode.line"
    _description = "条码属性"
    _order = "id desc"
    _rec_name = "field_id"

    barcode_id = fields.Many2one('roke.barcode', string="条码字典")
    field_id = fields.Many2one('ir.model.fields', string="属性名称")
    field_value = fields.Char(string="属性值")