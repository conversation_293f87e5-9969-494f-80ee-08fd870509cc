# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<Caiqigang>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from odoo.modules.module import get_module_resource
from datetime import datetime, timedelta


class RokeBarcodePackage(models.Model):
    _name = "roke.barcode.package"
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = "条码打包"
    _order = "id desc"
    _rec_name = "code"

    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False)
    package_code = fields.Many2one('roke.barcode', string="包号", required=True)

    qty = fields.Float(string="数量", tracking=True)
    auxiliary_qty = fields.Float(string="辅数量", tracking=True)
    line_ids = fields.One2many("roke.barcode.package.line", "package_number", string="条码打包明细")

    note = fields.Text(string="说明")


class RokeBarcodePackageLine(models.Model):
    _name = "roke.barcode.package.line"
    _inherit = ['mail.thread']
    _description = "条码打包详情"

    package_code = fields.Many2one('roke.barcode', string="条码号", required=True)
    qty = fields.Float(string="数量", tracking=True, required=True)
    auxiliary_qty = fields.Float(string="辅数量", tracking=True)
    pruduct = fields.Many2one('roke.product', string="产品")

    package_number = fields.Many2one('roke.barcode.package', string="打包号", required=True)
    note = fields.Text(string="说明")


    @api.model
    def create(self, vals):
        existing_line = self.search([('package_code', '=', vals.get('package_code')), ('package_number', '=', vals.get('package_number'))])
        if existing_line:
            raise ValidationError("不能添加重复的条码号")
        return super(RokeBarcodePackageLine, self).create(vals)

    # @api.model
    # def write(self, vals):
    #     if 'package_code' in vals or 'package_number' in vals:
    #         existing_line = self.search([('package_code', '=', vals.get('package_code', self.package_code.id)),
    #                                      ('package_number', '=', vals.get('package_number', self.package_number.id))])
    #         if existing_line:
    #             raise ValidationError("不能修改为重复的条码号")
    #     return super(RokeBarcodePackageLine, self).write(vals)
