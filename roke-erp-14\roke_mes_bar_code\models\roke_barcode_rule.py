# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<Caiqigang>
"""
from odoo import models, fields, api, _
import uuid
import datetime
import hashlib
from odoo.exceptions import ValidationError, UserError

CODE = """
obj = env["roke.barcode.rule"].search([("id", "=", {})])
obj.generate_code(env.context)
"""


class RokeBarcodeRule(models.Model):
    _name = "roke.barcode.rule"
    _inherit = ['mail.thread']
    _description = "条码类别"

    name = fields.Char(string="类别名称", required=True,
                       index=True, tracking=True, copy=False)
    state = fields.Selection([("128", "Code128"), ("39", "Code39"), ("Code39 Extended", "Code39 Extended"),
                              ("Code128A", "Code128A"), ("Code128B", "Code128B"), ("Code128C", "Code128C"),
                              ("Code93", "Code93"), ("Code93 Extended", "Code93 Extended"), ("MSI", "MSI"),
                              ("PostNet", "PostNet"), ("CodeBar", "CodeBar"), ("EAN8", "EAN8"), ("EAN13", "EAN13"),
                              ("UPC_A", "UPC_A"), ("UPC_E0", "UPC_E0"), ("UPC_E1", "UPC_E1"),
                              ("UPC_Supp2", "UPC_Supp2"),
                              ("UPC_Supp5", "UPC_Supp5"), ("EAN128", "EAN128"), ("EAN128A", "EAN128A"),
                              ("EAN128B", "EAN128B"),
                              ("EAN128C", "EAN128C"), ("PDF417", "PDF417"), ("DateMatrix", "DateMatrix"),
                              ("qrcode", "QRCode")], required=True, default="128",
                             tracking=True, string="编码类型")
    model_id = fields.Many2one('ir.model', string="模型")
    automatic_creation = fields.Boolean(string="自动创建", default=True)

    code_template = fields.Text(string="格式", compute='_compute_text')
    item_ids = fields.One2many("roke.barcode.rule.item", "rule_id", string="组成元素")
    tab_ids = fields.One2many("roke.barcode.rule.tab", "rule_id", string="一码通")
    code_count = fields.Integer(string="手工生成条码数量", compute="_compute_code_count")

    note = fields.Text(string="说明")

    barcode_lines = fields.One2many('roke.barcode', 'barcode_rule', string="条码明细")

    print_style = fields.Many2one('ir.actions.report', string="打印样式")

    show_fields = fields.Many2many('ir.model.fields', string="显示字段", domain="[('model_id', '=', model_id)]")

    @api.depends('barcode_lines')
    def _compute_barcode_lines(self):
        for rule in self:
            rule.barcode_lines = self.env['roke.barcode'].search(
                [('barcode_rule', '=', rule.id)])

    def _compute_code_count(self):
        for record in self:
            record.code_count = len(record.barcode_lines)

    def _compute_text(self):
        for record in self:
            res = ""
            for i in record.item_ids:
                if i.type == '固定值':
                    res += "%s" % (i.fixed_value)
                elif i.type == '产品信息':
                    res += "{产品%s}" % i.product_field
                elif i.type == '时间':
                    res += "{%s}" % i.date_format
                elif i.type == '序号':
                    res += "{序号}"
                elif i.type == '源字段':
                    res += "{" + f"{i.field_id.name or '源字段'}" + "[取值位置:取值长度]}"
            record.code_template = res

    def _create_date_range(self, code_model, sequence, dt):
        """
        创建date_range
        :param dt: string
        :return:
        """
        DateRangeObj = self.sudo().env['ir.sequence.date_range']
        seq_date = DateRangeObj.search([
            ('sequence_id', '=', sequence.id), ('date_from',
                                                '<=', dt), ('date_to', '>=', dt)
        ], limit=1)
        if not seq_date:
            # 创建seq_date
            date_dt = fields.Date.from_string(dt)
            year = date_dt.strftime('%Y')
            month = date_dt.strftime('%m')
            day = date_dt.strftime('%d')

            date_from = ""
            date_to = ""
            if code_model == "day":
                date_from = '{}-{}-{}'.format(year, month, day)
                date_to = '{}-{}-{}'.format(year, month, day)
            elif code_model == "month":
                next_month = date_dt.replace(
                    day=28) + datetime.timedelta(days=4)
                month_last = next_month - \
                    datetime.timedelta(days=next_month.day)
                date_from = '{}-{}-01'.format(year, month)
                date_to = month_last.strftime('%Y-%m-%d')
            elif code_model == "year":
                date_from = '{}-01-01'.format(year)
                date_to = '{}-12-31'.format(year)
            if date_from and date_to:
                self.env['ir.sequence.date_range'].sudo().create({
                    'date_from': date_from,
                    'date_to': date_to,
                    'sequence_id': sequence.id,
                })

    def _get_lot_token(self, code):
        """
        lot加密防伪
        # 加密字符串暂时写死，后面可以考虑使用配置参数进行设置
        :return:
        """
        m = hashlib.md5()
        m.update(("roke-=-%s-=-mes" % code).encode("utf8"))
        result = m.hexdigest()
        return result[0:5]

    def _get_next_code_value(self, item, sequence_date=None, active_id=False, model_name=False):
        """
        获取批次号内容
        """
        result = ""
        if item.type == "固定值":
            result = item.fixed_value
        elif item.type == "序号":
            if item.code_model != "always":
                # 创建ir.sequence.date_range
                self._create_date_range(item.code_model, item.sequence_id, sequence_date)
            result = item.sequence_id.next_by_id(sequence_date=sequence_date)
        elif item.type == "时间":
            # 格式化时间
            format_str = "%y%m%d"
            if item.date_format == "四位年":
                format_str = "%Y"
            elif item.date_format == "两位年":
                format_str = "%y"
            elif item.date_format == "月":
                format_str = "%m"
            elif item.date_format == "日":
                format_str = "%d"
            result = sequence_date.strftime(format_str)
        elif item.type == "源字段" and active_id and model_name:
            model_value = self.env[model_name].sudo().search([("id", "=", active_id)])
            field_value = getattr(model_value, item.field_id.name, "")
            if item.field_id.ttype == "date":
                field_value = field_value.strftime('%Y-%m-%d')
            elif item.field_id.ttype == "datetime":
                field_value = field_value.strftime('%Y-%m-%d %H:%M:%S')
            else:
                field_value = str(field_value)
            result = field_value[item.position_int or 0:item.str_length if item.str_length > 0 else None]
        return result

    def next_code(self, sequence_date=None, track=False, active_id=False, model_name=False):
        """
        生成序号
        :param valus:{k:v, k:v}
        :return:
        """
        sequence_date = sequence_date or fields.Date.today()
        result = ""
        for item in self.item_ids:
            result += self._get_next_code_value(item, sequence_date=sequence_date, active_id=active_id,
                                                model_name=model_name)
        if track:
            # 追溯
            parameter = self.sudo().env['ir.config_parameter']
            base_url = parameter.get_param('web.base.url')
            result = "%s/track?code=%s&t=%s" % (base_url, result, self._get_lot_token(result))
        return result

    def create_barcode(self):
        """
        生成条码
        :return:
        """
        wizard = self.env["roke.mes.barcode.wizard"].create({
            "lot_rule_id": self.id,
            "model_id": self.model_id.id,
        })
        return {
            'name': '生成条码',
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_id': wizard.id,
            'target': 'new',
            'res_model': 'roke.mes.barcode.wizard'
        }

    def show_lot_codes_action(self):
        """
        查看已生成的条码号
        :return:
        """
        return {
            'name': '%s 手工生成的条码号' % self.display_name,
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [("id", "in", self.barcode_lines.ids)],
            'res_model': 'roke.barcode'
        }

    def init_product_lot_rule(self):
        products = self.env["roke.product"].search(
            [("lot_rule_id", "=", False)])
        products.write({
            "lot_rule_id": self.env.ref("roke_mes_material_enterprise.roke_mes_lot_rule_default")
        })

    def generate_code(self, context):
        active_ids = context.get("active_ids", [])
        for active_id in active_ids:
            self.env["roke.barcode"].create({
                "barcode_rule": self.id, "model_id": self.model_id.id,
                "code": self.next_code(active_id=active_id, model_name=self.model_id.model),
                "source_data": active_id,
                "model_field_id": active_id
            })

    # ---- AUTOMATION -------------------------------------------------------------------------------

    def check_sequence(self, vals, type):
        if type == 'create':
            if not vals.get('item_ids'):
                raise UserError('请配置组成元素!')
        else:
            if not vals.get('item_ids'):
                type_list = []
                for i in self.item_ids:
                    type_list.append(i.type)
                print(type_list)
                if '序号' not in type_list:
                    raise UserError('组成元素缺少【序号】类型，无法保持唯一性，请重新配置!')

    @api.constrains("item_ids")
    def constrains_item_ids(self):
        type_list = []
        for item in self.item_ids:
            type_list.append(item.type)
        if '序号' not in type_list:
            raise UserError('组成元素缺少【序号】类型，无法保持唯一性，请重新配置!')

    @api.model
    def create(self, values):
        self.check_sequence(values, 'create')
        res = super(RokeBarcodeRule, self).create(values)
        res.create_automation()
        return res

    def create_automation(self):
        automation_obj = self.env["base.automation"]
        automation_obj.sudo().create({
            "config_id": self.id,
            "name": "条码类别 {} {}".format(self.name, self.model_id.name),
            "active":  self.automatic_creation,
            "model_id": self.model_id.id, "trigger": "on_create",
            "state": "code", "code": CODE.format(self.id)
        })

    def write(self, values):
        self.check_sequence(values, 'write')
        res = super(RokeBarcodeRule, self).write(values)
        self.write_automation()
        return res

    def write_automation(self):
        automation_obj = self.env["base.automation"]
        automation_res = automation_obj.sudo().search(
            [("config_id", "=", self.id)])
        if automation_res:
            automation_res.sudo().write({
                "name": "条码类别 {} {}".format(self.name, self.model_id.name),
                "active":  self.automatic_creation,
                "model_id": self.model_id.id,
                "state": "code", "code": CODE.format(self.id)
            })
        else:
            self.create_automation()

    def unlink(self):
        self.unlink_automation()
        res = super(RokeBarcodeRule, self).unlink()
        return res

    def unlink_automation(self):
        automation_obj = self.env["base.automation"]
        automation_res = automation_obj.sudo().search(
            [("config_id", "=", self.id)])
        automation_res.unlink()


class RokeMESBarcodeRuleItem(models.Model):
    _name = "roke.barcode.rule.item"
    _description = "条码类别组成元素"
    _order = "sequence, id"

    rule_id = fields.Many2one("roke.barcode.rule", string="规则")
    sequence = fields.Integer(string="序号", required=True, default=10)

    type = fields.Selection([("固定值", "固定值"), ("时间", "时间"),
                            ("序号", "序号"), ("源字段", "源字段")], string="类型", required=True, default="固定值")
    field_id = fields.Many2one("ir.model.fields", string="源字段")
    position_int = fields.Integer(string="取值位置")
    str_length = fields.Integer(string="取值长度")
    # 固定值
    fixed_value = fields.Char(string="固定值")
    # 时间
    date_format = fields.Selection(
        [("四位年", "四位年"), ("两位年", "两位年"), ("月", "月"), ("日", "日")], string="日期格式")
    # 序号
    code_length = fields.Integer(string="序号长度", default=3)
    code_model = fields.Selection(
        [("day", "每天更新"), ("month", "每月更新"), ("year", "每年更新"), ("always", "一直累加")], string="序号更新")
    sequence_id = fields.Many2one("ir.sequence", string="关联序列")

    @api.onchange("type")
    def _onchange_type(self):
        """
        :return:
        """
        if self.type == "序号":
            return {"value": {"code_model": "always"}}

    def _create_sequence(self, padding, code_model):
        index = str(uuid.uuid4()).replace("-", "")
        sequence = self.sudo().env['ir.sequence'].create({
            "name": "批次规则_%s" % index,
            "padding": padding,
            "code": "roke-%s" % index,
            "use_date_range": True if code_model != "always" else False
        })
        return sequence.id

    @api.model
    def create(self, vals):
        if not vals.get("sequence_id", None) and vals.get("code_length") and vals.get("code_model"):
            vals["sequence_id"] = self._create_sequence(
                vals["code_length"], vals["code_model"])
        res = super(RokeMESBarcodeRuleItem, self).create(vals)
        return res

    def write(self, vals):
        res = super(RokeMESBarcodeRuleItem, self).write(vals)
        if "code_model" in vals or "code_length" in vals:
            for record in self:
                record.sequence_id.write({
                    "padding": record.code_length,
                    "use_date_range": True if record.code_model != "always" else False
                })
        return res

    def unlink(self):
        self.sequence_id.unlink()
        return super(RokeMESBarcodeRuleItem, self).unlink()


class RokeMESBarcodeRuleTab(models.Model):
    _name = "roke.barcode.rule.tab"
    _description = "条码类别一码通"
    _order = "sequence,id"

    rule_id = fields.Many2one("roke.barcode.rule", string="规则")
    sequence = fields.Integer(string="序号", required=True, default=10)
    path_name = fields.Many2one("roke.app.function", string="跳转路径名称")
    path_index = fields.Char(related='path_name.index', string="跳转路径标识")
