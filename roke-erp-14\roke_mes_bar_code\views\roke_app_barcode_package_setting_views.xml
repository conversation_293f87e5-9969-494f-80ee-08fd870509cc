<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_roke_app_barcode_package_setting_tree" model="ir.ui.view">
        <field name="name">roke.app.barcode.package.setting.tree</field>
        <field name="model">roke.app.general.order.setting</field>
        <field name="arch" type="xml">
            <tree string="条码打包方案">
                <field name="barcode_package_name"/>
                <field name="barcode_rule_id"/>
                <field name="create_uid"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_app_barcode_package_setting_form" model="ir.ui.view">
        <field name="name">roke.app.barcode.package.setting.form</field>
        <field name="model">roke.app.general.order.setting</field>
        <field name="arch" type="xml">
            <form string="条码打包方案">
                <!--                <sheet>-->
                <group id="g1">
                    <group>
                        <group>
                            <field name="belong_to_platform" invisible="1"/>
                            <field name="model_id" invisible="1"/>
                            <field name="model_name" invisible="1"/>
                            <field name="detail_field_id" invisible="1"/>
                            <field name="detail_model_id" invisible="1"/>
                            <field name="display_list" invisible="1"/>
                            <field name="detail_display_list" invisible="1"/>
                            <field name="app_function_id" invisible="1"/>
                            <field name="app_category_id" invisible="1"/>
                            <field name="barcode_package_name" required="1"/>
                            <field name="document_capacity"/>
                        </group>
                        <group>
                            <field name="default_domain" optional="show" widget="domain"
                                   options="{'model': 'model_index', 'in_dialog': True}" string="打包记录默认过滤"/>
                            <field name="capacity_count" attrs="{'invisible':[('document_capacity','=',False)]}"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="barcode_rule_id" required="1"/>
                            <field name="is_auto_save" invisible="1"/>
                            <field name="is_qty_edit" invisible="0"/>
                        </group>
                        <group>
                            <field name="print_style"/>
                            <field name="barcode_function" required="1"/>
                            <field name="is_auto_print" attrs="{'invisible':[('document_capacity','=',False)]}"/>
                        </group>
                    </group>
                </group>
                <notebook>
                    <field name="is_mobile" invisible="1"/>
                    <page string="打包记录">
                        <button name="multi_add_fields_action" string="批量添加字段" type="object" class="oe_highlight"
                                attrs="{'invisible': [('model_id', '=', False)]}" context="{'fields_type': 'list'}"/>
                        <field name="list_field_ids" attrs="{'readonly': [('model_id', '=', False)]}">
                            <tree editable="bottom">
                                <field name="order_id" invisible="1"/>
                                <field name="sequence" optional="show" widget="handle"/>
                                <field name="field_id" optional="show"
                                       domain="[('model_id', '=', parent.model_id)]"
                                       options="{'no_create': True}"/>
                                <field name="zdy_field_description" optional="show"/>
                                <field name="field_name" optional="show"/>
                                <field name="field_ttype" optional="show"/>
                                <field name="field_required" optional="show"/>
                                <field name="field_readonly" optional="show"/>
                                <field name="primary" optional="show"/>
                            </tree>
                        </field>
                    </page>
                    <page string="搜索字段">
                        <button name="multi_add_fields_action" string="批量添加字段" type="object" class="oe_highlight"
                                attrs="{'invisible': [('model_id', '=', False)]}" context="{'fields_type': 'search'}"/>
                        <field name="search_field_ids" attrs="{'readonly': [('model_id', '=', False)]}">
                            <tree editable="bottom">
                                <field name="order_id" invisible="1"/>
                                <field name="sequence" optional="show" widget="handle"/>
                                <field name="field_id" optional="show"
                                       domain="[('model_id', '=', parent.model_id),('ttype', 'not in', ['one2many','many2many'])]"
                                       options="{'no_create': True}"/>
                                <field name="zdy_field_description" optional="show"/>
                                <field name="field_name" optional="show"/>
                                <field name="field_ttype" optional="hide"/>
                                <field name="field_required" optional="hide"/>
                                <field name="primary" optional="hide"/>
                                <field name="processing_action" optional="hide"/>
                                <field name="group_data" optional="hide"/>
                            </tree>
                        </field>
                    </page>
<!--                    <page string="表头字段">-->
<!--                        <button name="multi_add_fields_action" string="批量添加字段" type="object" class="oe_highlight"-->
<!--                                attrs="{'invisible': [('model_id', '=', False)]}" context="{'fields_type': 'header'}"/>-->
<!--                        <field name="header_field_ids" attrs="{'readonly': [('model_id', '=', False)]}">-->
<!--                            <tree editable="bottom">-->
<!--                                <field name="order_id" invisible="1"/>-->
<!--                                <field name="sequence" optional="show" widget="handle"/>-->
<!--                                <field name="field_id" optional="show"-->
<!--                                       domain="[('model_id', '=', parent.model_id)]"-->
<!--                                       options="{'no_create': True}"/>-->
<!--                                <field name="zdy_field_description" optional="show"/>-->
<!--                                <field name="field_name" optional="show"/>-->
<!--                                <field name="field_ttype" optional="show"/>-->
<!--                                <field name="is_required" optional="hide"/>-->
<!--                                <field name="field_required" optional="show"-->
<!--                                       attrs="{'readonly': [('is_required', '=', True)]}"/>-->
<!--                                <field name="field_readonly" optional="show"/>-->
<!--                                <field name="is_allow_create" optional="show"-->
<!--                                       attrs="{'invisible': [('field_ttype', 'not in', ('many2one','one2many','many2many'))]}"/>-->
<!--                                <field name="primary" optional="show"/>-->
<!--                                <field name="display_mode" optional="show"/>-->
<!--                                <field name="function_type" optional="show"/>-->
<!--                                <field name="default_value" optional="show"-->
<!--                                       attrs="{'readonly': [('field_ttype', 'not in', ('char','datetime','date','boolean','integer','float','text','selection','many2one'))]}"/>-->
<!--                                <field name="select_mode" optional="show"-->
<!--                                       attrs="{'readonly': [('field_ttype', 'not in', ('many2one','one2many','many2many'))],'invisible': [('field_ttype', 'not in', ('many2one','one2many','many2many'))]}"/>-->
<!--                                <field name="domain_field" optional="show" readonly="1"/>-->
<!--                                <button name="confirm_domain_data" icon="fa-pencil" type="object" title="过滤数据"-->
<!--                                        string="过滤数据" attrs="{'invisible': [('field_ttype', '!=', 'many2one')]}"/>-->
<!--                            </tree>-->
<!--                        </field>-->
<!--                    </page>-->
                    <page string="表体字段" attrs="{'invisible': [('is_mobile', '=', True)]}">
                        <button name="multi_add_fields_action" string="批量添加字段" type="object" class="oe_highlight"
                                attrs="{'invisible': [('detail_model_id', '=', False)]}"
                                context="{'fields_type': 'detail_form'}"/>
                        <field name="detail_form_field_ids" attrs="{'readonly': [('detail_model_id', '=', False)]}">
                            <tree editable="bottom">
                                <field name="order_id" invisible="1"/>
                                <field name="sequence" optional="show" widget="handle"/>
                                <field name="field_id" optional="show"
                                       domain="[('model_id', '=', parent.detail_model_id)]"
                                       options="{'no_create': True}"/>
                                <field name="zdy_field_description" optional="show"/>
                                <field name="field_name" optional="show"/>
                                <field name="field_ttype" optional="show"/>
                                <field name="is_required" optional="hide"/>
                                <field name="field_required" optional="show"
                                       attrs="{'readonly': [('is_required', '=', True)]}"/>
                                <field name="field_readonly" optional="show"/>
                                <field name="is_allow_create" optional="show"
                                       attrs="{'invisible': [('field_ttype', 'not in', ('many2one','one2many','many2many'))]}"/>
                                <field name="is_show_fold" optional="hide"/>
                                <field name="primary" optional="show"/>
                                <field name="detail_list_field" string="列表显示" optional="show"/>
                                <field name="batch_list_field" string="批量显示" optional="show"/>
                                <field name="default_value" optional="show"
                                       attrs="{'readonly': [('field_ttype', 'not in', ('many2one','datetime','date','boolean','char','integer','float','text','selection'))]}"/>
                                <field name="select_mode" optional="show"
                                       attrs="{'readonly': [('field_ttype', 'not in', ('many2one','one2many','many2many'))],'invisible': [('field_ttype', 'not in', ('many2one','one2many','many2many'))]}"/>
                                <field name="domain_field" optional="show" readonly="1"/>
                                <button name="confirm_domain_data" icon="fa-pencil" type="object" title="过滤数据"
                                        string="过滤数据" attrs="{'invisible': [('field_ttype', '!=', 'many2one')]}"/>
                            </tree>
                        </field>
                    </page>
<!--                    <page string="功能按钮" attrs="{'invisible': [('is_mobile', '=', True)]}">-->
<!--                        <field name="order_function_ids">-->
<!--                            <tree editable="bottom">-->
<!--                                <field name="function_name" optional="show"/>-->
<!--                                <field name="function_index" optional="show"/>-->
<!--                                &lt;!&ndash;                                <field name="is_ginseng" invisible="1" optional="show"/>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                <field name="function_model_id" attrs="{'readonly': [('is_ginseng', '=', False)],'required': [('is_ginseng', '=', True)]}" optional="show"/>&ndash;&gt;-->
<!--                                &lt;!&ndash;                                <field name="function_fields" widget="many2many_tags" readonly="1" optional="show" force_save="True"/>&ndash;&gt;-->
<!--                                <field name="condition" optional="show"/>-->
<!--                                <field name="allow_list" optional="show"/>-->
<!--                            </tree>-->
<!--                            <form string="通用单据功能">-->
<!--                                <group>-->
<!--                                    <group>-->
<!--                                        <field name="order_id"/>-->
<!--                                        <field name="function_name"/>-->
<!--                                        &lt;!&ndash;                                        <field name="is_ginseng" invisible="1"/>&ndash;&gt;-->
<!--                                        &lt;!&ndash;                                        <field name="function_model_id" attrs="{'readonly': [('is_ginseng', '=', False)],'required': [('is_ginseng', '=', True)]}"/>&ndash;&gt;-->
<!--                                        &lt;!&ndash;                                        <field name="function_fields" widget="many2many_tags" readonly="1" force_save="True"/>&ndash;&gt;-->
<!--                                    </group>-->
<!--                                    <group>-->
<!--                                        <field name="function_index"/>-->
<!--                                        <field name="condition"/>-->
<!--                                        <field name="allow_list"/>-->
<!--                                    </group>-->
<!--                                </group>-->
<!--                            </form>-->
<!--                        </field>-->
<!--                    </page>-->
                </notebook>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_app_barcode_package_setting_action" model="ir.actions.act_window">
        <field name="name">条码打包方案</field>
        <field name="res_model">roke.app.general.order.setting</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_roke_app_barcode_package_setting_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_roke_app_barcode_package_setting_form')})]"/>
        <field name="domain">[('model', '=', 'roke.barcode.package')]</field>
        <field name="context">{'is_barcode_package': True}</field>
        <field name="form_view_id" ref="view_roke_app_barcode_package_setting_form"/>
    </record>

</odoo>
