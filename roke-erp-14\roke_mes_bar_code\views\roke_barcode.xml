<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--条码字典-->
    <!--search-->
    <record id="view_roke_barcode_search" model="ir.ui.view">
        <field name="name">roke.barcode.search</field>
        <field name="model">roke.barcode</field>
        <field name="arch" type="xml">
            <search string="条码字典">
                <field name="barcode_rule"/>
                <field name="model_id"/>
                <field name="source_data"/>
                <field name="code"/>
                <field name="qty"/>
                <field name="auxiliary_qty"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_barcode_tree" model="ir.ui.view">
        <field name="name">roke.barcode.tree</field>
        <field name="model">roke.barcode</field>
        <field name="arch" type="xml">
            <tree string="条码字典">
                <field name="barcode_rule"/>
                <field name="model_id" optional="show"/>
                <field name="source_data" optional="show"/>
                <field name="code" optional="show"/>
                <field name="qty" optional="show"/>
                <field name="auxiliary_qty" optional="show"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_barcode_form" model="ir.ui.view">
        <field name="name">roke.barcode.form</field>
        <field name="model">roke.barcode</field>
        <field name="arch" type="xml">
            <form string="条码信息">
                <group id="g1" col="4">
                    <group>
                        <field name="barcode_rule"/>
                        <field name="code"/>
                    </group>
                    <group>
                        <field name="model_id"/>
                        <field name="auxiliary_qty"/>
                    </group>
                    <group>
                        <field name="create_uid" string="创建人"/>
                        <field name="create_date" string="创建时间"/>
                    </group>
                    <group>
                        <field name="source_data"/>
                        <field name="qty"/>
                    </group>
                </group>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <!--tree-->
    <record id="view_roke_barcode_line_tree" model="ir.ui.view">
        <field name="name">roke.barcode.line.tree</field>
        <field name="model">roke.barcode.line</field>
        <field name="arch" type="xml">
            <tree string="条码属性">
                <field name="field_id" optional="show"/>
                <field name="field_value" optional="show"/>
            </tree>
        </field>
    </record>

    <!--form-->
    <record id="view_roke_barcode_line_form" model="ir.ui.view">
        <field name="name">roke.barcode.line.form</field>
        <field name="model">roke.barcode.line</field>
        <field name="arch" type="xml">
            <form string="条码属性">
                <group id="g1" col="4">
                    <group>
                        <field name="field_id"/>
                    </group>
                    <group>
                        <field name="field_value"/>
                    </group>
                    <group>
                    </group>
                    <group>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_barcode_action" model="ir.actions.act_window">
        <field name="name">条码字典</field>
        <field name="res_model">roke.barcode</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_barcode_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个条码字典。
            </p>
        </field>
    </record>

</odoo>
