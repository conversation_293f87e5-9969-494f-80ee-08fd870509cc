<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--条码打包-->
    <!--search-->
    <record id="view_roke_barcode_package_search" model="ir.ui.view">
        <field name="name">roke.barcode.package.search</field>
        <field name="model">roke.barcode.package</field>
        <field name="arch" type="xml">
            <search string="条码打包">
                <field name="code"/>
                <field name="package_code"/>
                <field name="qty"/>
                <field name="auxiliary_qty"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_barcode_package_tree" model="ir.ui.view">
        <field name="name">roke.barcode.package.tree</field>
        <field name="model">roke.barcode.package</field>
        <field name="arch" type="xml">
            <tree string="条码打包">
                <field name="code"/>
                <field name="package_code" optional="show"/>
                <field name="qty" optional="show"/>
                <field name="auxiliary_qty" optional="show"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <record id="view_roke_barcode_package_form" model="ir.ui.view">
        <field name="name">roke.barcode.package.form</field>
        <field name="model">roke.barcode.package</field>
        <field name="arch" type="xml">
            <form string="条码信息">
                <group id="g1" col="4">
                    <group>
                        <field name="code"/>
                        <field name="qty"/>
                    </group>
                    <group>
                        <field name="create_date" string="创建时间"/>
                        <field name="auxiliary_qty"/>
                    </group>
                    <group>
                        <field name="create_uid" string="创建人"/>
                    </group>
                    <group>
                        <field name="package_code"/>
                    </group>
                </group>
                <notebook>
                    <page string="条码明细" name="line_ids">
                        <field name="line_ids">
                            <tree editable="bottom">
                                <field name="package_number" invisible="1"/>
                                <field name="package_code"/>
                                <field name="qty"/>
                                <field name="auxiliary_qty"/>
                                <field name="pruduct"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_roke_barcode_package_line_form" model="ir.ui.view">
        <field name="name">roke.barcode.package.line.form</field>
        <field name="model">roke.barcode.package.line</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group>
                        <field name="package_code"/>
                        <field name="package_number"/>
                        <field name="qty"/>
                    </group>
                    <group>
                        <field name="auxiliary_qty"/>
                        <field name="pruduct"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="view_roke_barcode_package_action" model="ir.actions.act_window">
        <field name="name">条码打包</field>
        <field name="res_model">roke.barcode.package</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_barcode_package_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个条码打包。
            </p>
        </field>
    </record>

</odoo>
