<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--条码类别-->
    <!--search-->
    <record id="view_roke_barcode_rule_search" model="ir.ui.view">
        <field name="name">roke.barcode.rule.search</field>
        <field name="model">roke.barcode.rule</field>
        <field name="arch" type="xml">
            <search string="条码类别">
                <field name="name"/>
                <field name="model_id"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_barcode_rule_tree" model="ir.ui.view">
        <field name="name">roke.barcode.rule.tree</field>
        <field name="model">roke.barcode.rule</field>
        <field name="arch" type="xml">
            <tree string="条码类别">
                <field name="name"/>
                <field name="model_id" optional="show"/>
                <field name="code_template" optional="show"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建日期"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_barcode_rule_form" model="ir.ui.view">
        <field name="name">roke.barcode.rule.form</field>
        <field name="model">roke.barcode.rule</field>
        <field name="arch" type="xml">
            <form string="条码信息">
                <header>
                    <button name="create_barcode" type="object" string="创建条码" class="oe_highlight"
                    icon="fa-arrow-down"
                    attrs="{'invisible':[('automatic_creation','=',True)]}"/>
                </header>
                <div name="button_box" class="oe_button_box">
                    <button name="show_lot_codes_action" class="oe_stat_button" icon="fa-list" type="object">
                        <field name="code_count" widget="statinfo" string="条码数量"/>
                    </button>
                </div>
                <group id="g1" col="4">
                    <group>
                        <field name="name"/>
                        <field name="automatic_creation" widget="boolean_toggle"/>

                    </group>
                    <group>
                        <field name="model_id" required="1"/>
                        <field name="create_date" string="创建时间"/>

                    </group>
                    <group>
                        <field name="print_style" domain="[('model', '=', 'roke.barcode.package')]"/>
                        <field name="create_uid" string="创建人"/>
                    </group>
                    <group>
                        <field name="code_template" widget="char"/>
                    </group>
                </group>
                <notebook>
                    <page string="组成元素">
                        <field name="item_ids">
                            <tree editable="bottom">
                                <field name="rule_id" invisible="1"/>
                                <field name="type"/>
                                <field name="field_id" domain="[('model_id', '=', parent.model_id),
                                 ('ttype', '!=', ['many2one', 'one2many', 'many2many'])]"
                                       attrs="{'invisible':[('type', '!=', '源字段')], 'required':[('type', '=', '源字段')]}"/>
                                <field name="position_int" attrs="{'invisible':[('type', '!=', '源字段')], 'required':[('type', '=', '源字段')]}"/>
                                <field name="str_length" attrs="{'invisible':[('type', '!=', '源字段')], 'required':[('type', '=', '源字段')]}"/>
                                <field name="fixed_value" attrs="{'invisible':[('type', '!=', '固定值')], 'required':[('type', '=', '固定值')]}"/>
                                <field name="date_format" attrs="{'invisible':[('type', '!=', '时间')], 'required':[('type', '=', '时间')]}"/>
                                <field name="code_length" attrs="{'invisible':[('type', '!=', '序号')], 'required':[('type', '=', '序号')]}"/>
                                <field name="code_model" attrs="{'invisible':[('type', '!=', '序号')], 'required':[('type', '=', '序号')]}"/>
                                <field name="sequence_id" optional="hide" options="{'no_create': True}" readonly="1"/>
                            </tree>
                        </field>
                    </page>
                    <page string="条码明细" name="barcode_lines">
                        <field name="barcode_lines">
                            <tree editable="bottom">
                                <field name="code"/>
                                <field name="qty"/>
                                <field name="auxiliary_qty"/>
                                <field name="create_uid" string="创建人"/>
                                <field name="create_date" string="创建日期"/>
                            </tree>
                        </field>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
            </form>
        </field>
    </record>

    <record id="view_roke_barcode_rule_action" model="ir.actions.act_window">
        <field name="name">条码类别</field>
        <field name="res_model">roke.barcode.rule</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="form_view_id" ref="view_roke_barcode_rule_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                点击左上角“创建”按钮新增一个条码类别。
            </p>
        </field>
    </record>

</odoo>
