# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError
import xlwt
import xlrd
from io import BytesIO
import base64
import logging
import datetime

_logger = logging.getLogger(__name__)
_s_date = datetime.date(1899, 12, 31).toordinal() - 1


class RokeGenerateLotCodeWizard(models.TransientModel):
    _name = "roke.mes.barcode.wizard"
    _description = "手动生成条码号"

    lot_rule_id = fields.Many2one("roke.barcode.rule", string="序号规则", required=True)
    qty = fields.Integer(string="生成数量", default=1)
    sequence_date = fields.Date(string="序号日期", default=fields.Date.context_today)
    index = fields.Char(string="生成标识", help="生成标识可用于生成后筛选")

    model_id = fields.Many2one('ir.model', string="模型")
    model_field_id = fields.Integer(string="源id")
    source_data = fields.Char(string="源数据", index=True, tracking=True, copy=False)

    def _get_lot_code_vals(self):
        # 获取条码内容
        return {
            "barcode_rule": self.lot_rule_id.id,
            "model_id": self.model_id.id,
            "code": self.lot_rule_id.next_code(sequence_date=self.sequence_date),
            "index": self.index or "",
            "model_field_id": self.model_field_id or 0,
            "source_data": self.model_field_id or 0
        }

    def confirm(self):
        create_vals = []
        for i in range(self.qty):
            create_vals.append(self._get_lot_code_vals())
        new_codes = self.env["roke.barcode"].create(create_vals)
        return {
            'name': '本次生成的条码号',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [("id", "in", new_codes.ids)],
            'res_model': 'roke.barcode'
        }

