# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _

from odoo.addons.web.controllers import main
from odoo.addons.roke_mes_client.controller.login import Login

from odoo.http import request
from odoo.tools.translate import _
from datetime import datetime, date

import logging

_logger = logging.getLogger(__name__)


class InheritLogin(Login):


    def _get_user_employees(self, user):
        """
        获取用户可报工员工
        :return:
        """
        return list(user.employee_ids.mapped("name"))

class AthLogin(main.Home):

    def additional_verification(self):
        result = request.env['res.config.settings'].sudo().message_fetch_poll()
        deadline = result.get("deadline")
        series = result.get("series")
        if deadline == "*":
            return {}
        else:
            try:
                given_date = datetime.strptime(deadline, "%Y-%m-%d").date()
                current_date = date.today()
                if given_date <= current_date:
                    return {"error": f"您的{series}服务已超期，请联系客服400-006-0611续费"}
                else:
                    return {}
            except ValueError:
                return {}

    @http.route('/web/login', type='http', auth="public")
    def web_login(self, redirect=None, **kw):
        """
        校验是否到期
        """
        main.ensure_db()
        values = request.params.copy()
        if request.httprequest.method == 'POST':
            if request.params['login']:
                verif_result = self.additional_verification()
                if verif_result.get("error", False):
                    values["error"] = verif_result["error"]
                
        if "error" in values:
            return request.render('web.login', values)
        return super(AthLogin, self).web_login(redirect, **kw)