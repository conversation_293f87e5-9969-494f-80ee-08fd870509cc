# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
import logging
import os
from jinja2 import FileSystemLoader, Environment

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static/html")
env = Environment(loader=templateloader)

_logger = logging.getLogger(__name__)

class Main(http.Controller):

    @http.route('/roke/get_team_qr', type='http', auth='public', csrf=False)
    def get_team_qr(self):
        values = {}
        # 修改Jinja2 分隔符
        env.variable_start_string = '[['
        env.variable_end_string = ']]'
        template = env.get_template('quick_registration_team_employees.html')
        html = template.render(values)
        return html


    @http.route('/roke/check_employee_skill', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def check_employee_skill(self):
        """
        校验员工技能等级
        """
        work_center_id = http.request.jsonrequest.get("work_center_id")
        if not work_center_id:
            return {"state": "error", "msgs": "必须入参工作中心"}
        work_center = http.request.env["roke.work.center"].browse(int(work_center_id))
        employee = http.request.env['roke.employee'].search([("user_id", "=", http.request.env.user.id)])
        if len(employee) != 1:
            return {
                "state": "success",
                "msgs": "当前登录用户获取到多个员工，请先联系管理员保证一个用户对应一个员工",
                "check_result": False,
                "job_number": employee.job_number or "",
                "employee_name": employee.name or "",
                "position": employee.position_id.name or "",
                "skill_level": employee.skill_level_id.name or "",
                "work_center": work_center.name or "",
            }
        if work_center.skill_level_ids and employee.skill_level_id not in work_center.skill_level_ids:
            return {
                "state": "success",
                "msgs": "员工%s 禁止登录 %s，请联系管理员授权技能等级" % (employee.name, work_center.name),
                "check_result": False,
                "job_number": employee.job_number or "",
                "employee_name": employee.name or "",
                "position": employee.position_id.name or "",
                "skill_level": employee.skill_level_id.name or "",
                "work_center": work_center.name or "",
            }
        else:
            return {
                "state": "success",
                "msgs": "校验通过",
                "check_result": True,
                "job_number": employee.job_number or "",
                "employee_name": employee.name or "",
                "position": employee.position_id.name or "",
                "skill_level": employee.skill_level_id.name or "",
                "work_center": work_center.name or "",
            }

    @http.route('/roke/get_work_center_data', type='json', methods=['POST', 'OPTIONS'], auth='user', cors='*',
                csrf=False)
    def get_work_center_data(self):
        _self = http.request
        work_center_id = _self.jsonrequest.get("work_center_id", 0)
        domain = []
        if work_center_id:
            domain.append("|")
            domain.append(("parent_id", "=", work_center_id))
            domain.append(("id", "=", work_center_id))
        else:
            domain.append(("parent_id", "=", False))
        work_center_list = _self.env["roke.work.center"].search(domain)
        data = []
        for v in work_center_list:
            count = _self.env["roke.work.center"].search_count([("parent_id", "=", v.id)])
            is_parent = v.id == int(work_center_id) if work_center_id else False
            data.append({
                "id": v.id,
                "name": v.name,
                "parent_id": v.parent_id.id or 0,
                "note": v.note,
                "is_parent": is_parent,
                "no_below": (count != 0) if not is_parent else False
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    @http.route('/roke/process/get_process_category_data', type='json', methods=['POST', 'OPTIONS'], auth='user',
                cors='*', csrf=False)
    def get_process_category_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", 0)
        if category_id == -1:
            data = [{
                "id": -1,
                "name": "无（无类型）",
                "parent_id": 0,
                "note": "",
                "is_parent": True,
                "no_below": False
            }]
            return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}
        domain = []
        if category_id:
            domain.append("|")
            domain.append(("parent_id", "=", category_id))
            domain.append(("id", "=", category_id))
        else:
            domain.append(("parent_id", "=", False))
        category_list = _self.env["roke.process.category"].search(domain)
        data = []
        for v in category_list:
            count = _self.env["roke.process.category"].search_count([("parent_id", "=", v.id)])
            is_parent = v.id == int(category_id) if category_id else False
            data.append({
                "id": v.id,
                "name": v.name,
                "parent_id": v.parent_id.id or 0,
                "note": v.note,
                "is_parent": is_parent,
                "no_below": (count != 0) if not is_parent else False
            })
        process_count = _self.env["roke.process"].search_count([("category_id", "=", False)])
        if process_count and category_id == 0:
            data.append({
                "id": -1,
                "name": "无（无类型）",
                "parent_id": 0,
                "note": "",
                "is_parent": False,
                "no_below": False
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    @http.route('/roke/process/get_process_data', type='json', methods=['POST', 'OPTIONS'], auth='user', cors='*',
                csrf=False)
    def get_process_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", False)
        domain = []
        if category_id == -1:
            domain.append(("category_id", "=", False))
        elif category_id and category_id != -1:
            domain.append(("category_id", "=", category_id))
        equipment_list = _self.env["roke.process"].search(domain)
        data = []
        for v in equipment_list:
            data.append({
                "name": v.name or "",
                "id": v.id,
                "code": v.code,
                "category_id": v.category_id.id,
                "process_type": v.process_type,
                "internal_code": v.internal_code,
                "rated_working_hours": v.rated_working_hours,
                "prepare_work_hours": v.prepare_work_hours
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    @http.route('/roke/product/get_product_category_data', type='json', methods=['POST', 'OPTIONS'], auth='user',
                cors='*', csrf=False)
    def get_product_category_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", 0)
        if category_id == -1:
            data = [{
                "id": -1,
                "name": "无（无类型）",
                "parent_id": 0,
                "note": "",
                "is_parent": True,
                "no_below": False
            }]
            return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}
        domain = []
        if category_id:
            domain.append("|")
            domain.append(("parent_id", "=", category_id))
            domain.append(("id", "=", category_id))
        else:
            domain.append(("parent_id", "=", False))
        category_list = _self.env["roke.product.category"].search(domain)
        data = []
        for v in category_list:
            count = _self.env["roke.product.category"].search_count([("parent_id", "=", v.id)])
            is_parent = v.id == int(category_id) if category_id else False
            data.append({
                "id": v.id,
                "name": v.name,
                "parent_id": v.parent_id.id or 0,
                "note": v.note,
                "is_parent": is_parent,
                "no_below": (count != 0) if not is_parent else False
            })
        process_count = _self.env["roke.product"].search_count([("category_id", "=", False)])
        if process_count and category_id == 0:
            data.append({
                "id": -1,
                "name": "无（无类型）",
                "parent_id": 0,
                "note": "",
                "is_parent": False,
                "no_below": False
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    @http.route('/roke/product/get_product_data', type='json', methods=['POST', 'OPTIONS'], auth='user', cors='*',
                csrf=False)
    def get_product_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", False)
        domain = []
        if category_id == -1:
            domain.append(("category_id", "=", False))
        elif category_id and category_id != -1:
            domain.append(("category_id", "=", category_id))
        equipment_list = _self.env["roke.product"].search(domain)
        data = []
        for v in equipment_list:
            data.append({
                "name": v.name or "",
                "id": v.id,
                "code": v.code,
                "category_id": v.category_id.id
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

