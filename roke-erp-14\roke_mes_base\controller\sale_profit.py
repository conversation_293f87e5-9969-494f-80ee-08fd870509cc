# -*- coding: utf-8 -*-
"""
Description:
    
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_production.controller.production import Production
import logging
import math

_logger = logging.getLogger(__name__)


class SaleProfit(http.Controller):

    @http.route('/roke/get_product_category_tree', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_product_category_tree(self):
        """
        获取类别树的结构
        return:
        [
        {
            "id": 2,
            "name": "类别A",
            "children": [
                {
                    "id": 3,
                    "name": "类别A-1",
                    "children": [
                        {
                            "id": 6,
                            "name": "类别A-1-1",
                            "children": [ ]
                        },
                        {
                            "id": 10,
                            "name": "类别A-1-2",
                            "children": [ ]
                        }
                    ]
                },
                {
                    "id": 9,
                    "name": "类别A-2",
                    "children": [
                        {
                            "id": 11,
                            "name": "类别A-2-1",
                            "children": [ ]
                        }
                    ]
                }
            ]
        }
    ]
        """
        result = []
        product_category_obj = http.request.env['roke.product.category']
        # 先获取第一层类别（没有父级的）
        all_product_category_records = product_category_obj.sudo().search([('parent_id', '=', False)])
        for rec in all_product_category_records:
            result.append(
                {
                    'id': rec.id,
                    'name': rec.name,
                    'children': self._get_child_product_category(rec)
                }
            )
        return result

    def _get_child_product_category(self, product_category):
        """递归获取子类别"""
        product_category_obj = http.request.env['roke.product.category']
        product_category_records = product_category_obj.sudo().search([('parent_id', '=', product_category.id)])
        child_category_list = []
        if not product_category_records:
            return []
        for rec in product_category_records:
            child_val = {
                'id': rec.id,
                'name': rec.name,
                'children': self._get_child_product_category(rec)
            }
            child_category_list.append(child_val)
        return child_category_list

    @http.route('/roke/get_sale_product_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_sale_product_list(self):
        """
        获取产品列表
        """
        result = []
        product_obj = http.request.env['roke.product']
        values = http.request.jsonrequest
        product_category_id = values.get('product_category_id', False)
        # 先获取第一层bom（没有父级的）
        if product_category_id:
            product_records = product_obj.sudo().search(
                [('category_id', '=', product_category_id), ('active', '=', True)])
            # 组装child_data数据
            for rec in product_records:
                data_val = {
                    'product_id': rec.id,
                    # 产品名称
                    'name': rec.name,
                    # 规格
                    'specification': rec.specification if rec.specification else '',
                    # 直接材料
                    'direct_material_price': rec.direct_material_price,
                    # 直接人工
                    'direct_labor_price': rec.direct_labor_price,
                    # 毛利
                    'gross_profit_rate': rec.gross_profit_rate,
                    # 销售价
                    'unit_price': rec.unit_price,
                }
                result.append(data_val)
        return result

    @http.route('/roke/compute_product_list_unit_price', type='json', methods=['POST', 'OPTIONS'], auth="none",
                csrf=False,
                cors='*')
    def compute_product_list_unit_price(self):
        """
        产品列表中毛利率和销售单价变动
        毛利率计算公式:  毛利率 = (销售价 - 成本) / 销售价
        """
        values = http.request.jsonrequest
        product_list_json = values.get('product_list_json', False)
        if product_list_json:
            # 获取成本
            direct_material_price = product_list_json.get('direct_material_price', 0)
            direct_labor_price = product_list_json.get('direct_labor_price', 0)
            cost_account = direct_material_price + direct_labor_price
            # 销售价& 毛利率
            unit_price = product_list_json.get('unit_price', 0)
            gross_profit_rate = product_list_json.get('gross_profit_rate', 0)
            # 哪个值变动了
            change_field = product_list_json.get('change_field', 'unit_price')
            # 如果是销售价变了
            if change_field == 'unit_price':
                gross_profit_rate = (unit_price - cost_account) / unit_price if unit_price > 0 else 0
                product_list_json.update({'gross_profit_rate': round(gross_profit_rate, 2)})
            # 毛利率变动
            # 根据毛利率算销售价公式：  销售价 = 成本 / （1 - 毛利率）
            else:
                if cost_account > 0 and gross_profit_rate == 1:
                    return {'state': 'error', 'msgs': '当存在成本的时候,毛利率不能为1'}
                else:
                    unit_price = cost_account / (1 - gross_profit_rate) if gross_profit_rate <= 1 else 0
                    product_list_json.update({'unit_price': round(unit_price, 2)})
        return product_list_json
