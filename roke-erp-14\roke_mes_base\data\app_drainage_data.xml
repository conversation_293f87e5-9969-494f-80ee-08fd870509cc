<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--引流版-业务伙伴-->
        <record id="drainage_roke_partner" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_partner"/>
            <field name="model_name">业务伙伴</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--引流版-产品信息-->
        <record id="drainage_roke_product" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_product"/>
            <field name="model_name">产品信息</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--表头字段-->
        <record id="drainage_roke_product_header_field_ids01" model="roke.app.drainage.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="drainage_roke_product"/>
            <field name="field_id" ref="field_roke_product__name"/>
        </record>
        <record id="drainage_roke_product_header_field_ids02" model="roke.app.drainage.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="drainage_roke_product"/>
            <field name="field_id" ref="field_roke_product__specification"/>
        </record>
        <record id="drainage_roke_product_header_field_ids03" model="roke.app.drainage.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="drainage_roke_product"/>
            <field name="field_id" ref="field_roke_product__uom_id"/>
        </record>
        <!--引流版-工艺路线-->
        <record id="drainage_roke_routing" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_routing"/>
            <field name="model_name">工艺路线</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--引流版-工序-->
        <record id="drainage_roke_process" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_process"/>
            <field name="model_name">工序</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--表头字段-->
        <record id="drainage_roke_process_header_field_ids01" model="roke.app.drainage.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="roke_mes_base.drainage_roke_process"/>
            <field name="field_id" ref="field_roke_process__name"/>
        </record>
        <!--roke_mes_salary base_qty-->
        <record id="drainage_roke_process_header_field_ids03" model="roke.app.drainage.order.fields.header">
            <field name="sequence">3</field>
            <field name="order_id" ref="roke_mes_base.drainage_roke_process"/>
            <field name="field_id" ref="field_roke_process__default_employee_ids"/>
        </record>
        <!--引流版-附件-->
        <record id="drainage_ir_attachment" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_ir_attachment"/>
            <field name="model_name">附件</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--引流版-单位-->
        <record id="drainage_roke_uom" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_uom"/>
            <field name="model_name">单位</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--引流版-人员信息-->
        <record id="drainage_roke_employee" model="roke.app.drainage.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_employee"/>
            <field name="model_name">人员信息</field>
            <field name="base_data">True</field>
            <field name="internal_customize">True</field>
        </record>
        <!--默认值-产品-默认工艺路线-->
        <record id="default_routing" model="roke.app.default">
            <field name="type_name">many2one</field>
            <field name="related_model_id" ref="roke_mes_base.model_roke_product"/>
            <field name="related_field_id" ref="roke_mes_base.field_roke_product__routing_id"/>
        </record>
        <!--默认值-产品-单位-->
        <record id="default_uom" model="roke.app.default">
            <field name="type_name">many2one</field>
            <field name="related_model_id" ref="roke_mes_base.model_roke_product"/>
            <field name="related_field_id" ref="roke_mes_base.field_roke_product__uom_id"/>
        </record>
    </data>
</odoo>