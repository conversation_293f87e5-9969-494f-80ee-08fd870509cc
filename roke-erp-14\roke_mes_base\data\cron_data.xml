<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="1">
        <!--初始基础数据-->
        <record id="ir_cron_roke_operation_log_upload_base" model="ir.cron">
            <field name="name">反馈使用数据</field>
            <field name="model_id" ref="roke_mes_client.model_roke_operation_log"/>
            <field name="state">code</field>
            <field name="code">model.data_upload_cloud()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <field name="active" eval="True"/>
        </record>

        <record id="ir_cron_auto_update_area" model="ir.cron">
            <field name="name">自动获取行政区域信息</field>
            <field name="model_id" ref="roke_mes_base.model_roke_mes_politics_region"/>
            <field name="state">code</field>
            <field name="code">model.auto_update_area()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">months</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>