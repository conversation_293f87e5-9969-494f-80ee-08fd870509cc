<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record forcecreate="True" id="roke_decimal_production" model="decimal.precision">
            <field name="name">Production</field>
            <field name="digits">2</field>
        </record>
        <record forcecreate="True" id="roke_decimal_salary" model="decimal.precision">
            <field name="name">Salary</field>
            <field name="digits">2</field>
        </record>
        <!--TODO 字段精度：fields.Float(digits='产品数')-->
        <!--TODO round精度：self.env["decimal.precision"].precision_get("产品数")-->
        <!--
            TOD<PERSON> def _get_pd_sc(self):
            TODO     return self.env["decimal.precision"].precision_get("产品数")

            TODO 调用 _get_pd_sc(self)
            TODO fix
        -->
    </data>
</odoo>
