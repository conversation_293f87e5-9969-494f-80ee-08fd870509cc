<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--客户-->
        <record id="roke_partner_demo" model="roke.partner">
            <field name="code">demo</field>
            <field name="name">演示客户</field>
            <field name="customer">True</field>
            <field name="supplier">True</field>
            <field name="note">● 首次创建账套时系统添加的演示客户</field>
        </record>
        <!--人员-->
        <record id="roke_employee_demo" model="roke.employee">
            <field name="code">demo</field>
            <field name="job_number">demo001</field>
            <field name="name">演示员工</field>
            <field name="team_id" eval="ref('roke_mes_base.roke_work_team_default')"/>
            <field name="note">● 首次创建账套时系统添加的演示员工</field>
        </record>
        <!--产品-->
        <record id="roke_product_demo" model="roke.product">
            <field name="code">demo</field>
            <field name="name">演示产品</field>
            <field name="note">● 首次创建账套时系统添加的演示产品</field>
        </record>
        <!--工序-->
        <record id="roke_process_demo1" model="roke.process">
            <field name="code">demo1</field>
            <field name="name">演示工序一</field>
            <field name="note">● 首次创建账套时系统添加的演示工序</field>
        </record>
        <record id="roke_process_demo2" model="roke.process">
            <field name="code">demo2</field>
            <field name="name">演示工序二</field>
            <field name="note">● 首次创建账套时系统添加的演示工序</field>
        </record>
        <record id="roke_process_demo3" model="roke.process">
            <field name="code">demo3</field>
            <field name="name">演示工序三</field>
            <field name="note">● 首次创建账套时系统添加的演示工序</field>
        </record>
        <!--工艺路线-->
        <record id="roke_routing_demo" model="roke.routing">
            <field name="code">demo</field>
            <field name="internal_code">demo001</field>
            <field name="name">演示工艺路线</field>
            <field name="note">● 首次创建账套时系统添加的演示工艺路线</field>
        </record>
        <!--工艺路线明细-->
        <record id="roke_routing_line_demo1" model="roke.routing.line">
            <field name="sequence">1</field>
            <field name="routing_id" eval="ref('roke_mes_base.roke_routing_demo')"/>
            <field name="process_id" eval="ref('roke_mes_base.roke_process_demo1')"/>
        </record>
        <record id="roke_routing_line_demo2" model="roke.routing.line">
            <field name="sequence">2</field>
            <field name="routing_id" eval="ref('roke_mes_base.roke_routing_demo')"/>
            <field name="process_id" eval="ref('roke_mes_base.roke_process_demo2')"/>
        </record>
        <record id="roke_routing_line_demo3" model="roke.routing.line">
            <field name="sequence">3</field>
            <field name="routing_id" eval="ref('roke_mes_base.roke_routing_demo')"/>
            <field name="process_id" eval="ref('roke_mes_base.roke_process_demo3')"/>
        </record>
        <!--成品库-->
        <record id="roke_finished_warehouse_default" model="roke.finished.warehouse">
            <field name="name">默认成品库</field>
        </record>
    </data>
</odoo>
