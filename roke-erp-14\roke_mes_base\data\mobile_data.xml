<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!--移动下单-工序-->
        <record id="mobile_roke_process" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_process"/>
            <field name="model_name">工序</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="process_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">20</field>
            <field name="order_id" ref="mobile_roke_process"/>
            <field name="field_id" ref="field_roke_process__name"/>
            <field name="primary">True</field>
        </record>

        <!--移动下单-工艺路线-->
        <record id="mobile_roke_routing" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_routing"/>
            <field name="model_name">工艺路线</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="routing_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__name"/>
            <field name="primary">True</field>
        </record>
        <record id="routing_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__line_ids"/>
        </record>

        <!--移动下单-工艺明细-->
        <record id="mobile_routing_line" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_routing_line"/>
            <field name="model_name">工艺明细</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="process_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">20</field>
            <field name="order_id" ref="mobile_routing_line"/>
            <field name="field_id" ref="field_roke_routing_line__process_id"/>
            <field name="primary">True</field>
        </record>

        <!--移动下单-作业规范-->
        <record id="mobile_standard" model="roke.app.general.order.setting">
            <field name="model_id" ref="model_roke_work_standard_item"/>
            <field name="model_name">作业规范</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--移动下单-工艺路线-->
        <record id="mobile_master_roke_routing" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_routing"/>
            <field name="model_name">工艺路线</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_produce_mark"/>
            <field name="detail_field_id" ref="field_roke_routing__line_ids"/>
            <field name="detail_model_id" ref="roke_mes_base.model_roke_routing_line"/>
            <field name="base_data">False</field>
<!--            <field name="is_batch_add">True</field>-->
            <field name="field_index" ref="field_roke_routing_line__process_id"/>
        </record>

        <!--列表字段-->
        <record id="roke_routing_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__code"/>
            <field name="primary">True</field>
        </record>
        <record id="roke_routing_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__name"/>
            <field name="primary">False</field>
        </record>
        <record id="roke_routing_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__note"/>
            <field name="primary">False</field>
        </record>

        <!--筛选字段-->
        <record id="roke_routing_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__code"/>
        </record>
        <record id="roke_routing_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__name"/>
        </record>
        <record id="roke_routing_search_field_ids03" model="roke.app.general.order.fields.search">
            <field name="sequence">3</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__note"/>
        </record>

        <!--表头字段-->
        <record id="roke_routing_header_field_ids01" model="roke.app.general.order.fields.header">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__code"/>
            <field name="primary">True</field>
            <field name="field_readonly">True</field>
            <field name="display_mode">详情</field>
        </record>
        <record id="roke_routing_header_field_ids02" model="roke.app.general.order.fields.header">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing__name"/>
            <field name="display_mode">详情与创建</field>
        </record>

        <!--表体字段-->
        <record id="roke_routing_detail_form_field_ids01" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">1</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing_line__sequence"/>
            <field name="detail_list_field">True</field>
            <field name="batch_list_field">True</field>
        </record>
        <record id="roke_routing_detail_form_field_ids02" model="roke.app.general.order.fields.detail.form">
            <field name="sequence">2</field>
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="field_id" ref="field_roke_routing_line__process_id"/>
            <field name="primary">True</field>
            <field name="detail_list_field">True</field>
            <field name="batch_list_field">True</field>
        </record>

        <!--单据功能-->
        <record id="roke_routing_function_ids01" model="roke.app.general.order.function">
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="function_name">编辑</field>
            <field name="function_index">write</field>
        </record>
        <record id="roke_routing_function_ids02" model="roke.app.general.order.function">
            <field name="order_id" ref="mobile_master_roke_routing"/>
            <field name="function_name">删除</field>
            <field name="function_index">unlink</field>
        </record>

        <!--移动下单-业务伙伴-->
        <record id="base_roke_partner" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_partner"/>
            <field name="model_name">业务伙伴</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="base_roke_partner_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__name"/>
            <field name="primary">True</field>
        </record>
        <record id="base_roke_partner_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">2</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__customer"/>
            <field name="primary">False</field>
        </record>
        <record id="base_roke_partner_list_field_ids03" model="roke.app.general.order.fields.list">
            <field name="sequence">3</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__supplier"/>
            <field name="primary">False</field>
        </record>
        <record id="base_roke_partner_list_field_ids04" model="roke.app.general.order.fields.list">
            <field name="sequence">4</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__contacts"/>
            <field name="primary">True</field>
        </record>
        <record id="base_roke_partner_list_field_ids05" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__contacts_phone"/>
            <field name="primary">False</field>
        </record>
        <record id="base_roke_partner_list_field_ids06" model="roke.app.general.order.fields.list">
            <field name="sequence">6</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__address"/>
            <field name="primary">False</field>
        </record>

        <record id="roke_partner_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_partner__code"/>
        </record>

        <record id="roke_partner_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="base_roke_partner"/>
            <field name="field_id" ref="field_roke_routing__name"/>
        </record>

        <!--移动下单-产品信息-->
        <record id="base_roke_product" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_product"/>
            <field name="model_name">产品信息</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="base_roke_product_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">20</field>
            <field name="order_id" ref="base_roke_product"/>
            <field name="field_id" ref="field_roke_product__name"/>
            <field name="primary">False</field>
        </record>

        <record id="base_roke_product_search_field_ids01" model="roke.app.general.order.fields.search">
            <field name="sequence">1</field>
            <field name="order_id" ref="base_roke_product"/>
            <field name="field_id" ref="field_roke_product__name"/>
            <field name="primary">False</field>
        </record>
        <record id="base_roke_product_search_field_ids02" model="roke.app.general.order.fields.search">
            <field name="sequence">2</field>
            <field name="order_id" ref="base_roke_product"/>
            <field name="field_id" ref="field_roke_product__code"/>
            <field name="primary">False</field>
        </record>

        <!--移动下单-人员信息-->
        <record id="base_roke_employee" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_employee"/>
            <field name="model_name">人员信息</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="base_roke_employee_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">20</field>
            <field name="order_id" ref="base_roke_employee"/>
            <field name="field_id" ref="field_roke_employee__name"/>
            <field name="primary">False</field>
        </record>

        <!--移动下单-班组信息-->
        <record id="base_roke_work_team" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_work_team"/>
            <field name="model_name">班组信息</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--移动下单-工作中心-->
        <record id="base_roke_work_center" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_work_center"/>
            <field name="model_name">工作中心</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--移动下单-班次-->
        <record id="base_roke_classes" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_classes"/>
            <field name="model_name">班次</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--移动下单-部门信息-->
        <record id="base_roke_department" model="roke.app.general.order.setting">
            <field name="model_id" ref="roke_mes_base.model_roke_department"/>
            <field name="model_name">部门信息</field>
            <field name="app_category_id" ref="roke_mes_client.mobile_menu_ydxd1"/>
            <field name="base_data">True</field>
        </record>

        <!--列表字段-->
        <record id="base_roke_department_list_field_ids01" model="roke.app.general.order.fields.list">
            <field name="sequence">1</field>
            <field name="order_id" ref="base_roke_department"/>
            <field name="field_id" ref="field_roke_department__name"/>
            <field name="primary">True</field>
        </record>
        <record id="base_roke_department_list_field_ids02" model="roke.app.general.order.fields.list">
            <field name="sequence">5</field>
            <field name="order_id" ref="base_roke_department"/>
            <field name="field_id" ref="field_roke_department__note"/>
        </record>

    </data>
    <data noupdate="1">
        <record id="mobile_master_roke_routing" model="roke.app.general.order.setting">
            <field name="is_batch_add">True</field>
        </record>
    </data>
</odoo>
