<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="roke_product_state_qualified" model="roke.product.state">
            <field name="name">合格</field>
        </record>
        <record id="roke_product_state_defective" model="roke.product.state">
            <field name="name">次品</field>
        </record>
        <record id="roke_product_state_waste" model="roke.product.state">
            <field name="name">废品</field>
        </record>
    </data>
</odoo>
