<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--工作中心编号-->
    <record id="roek_work_center_code_sequence" model="ir.sequence">
        <field name="name">工作中心编号</field>
        <field name="code">roke.work.center.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--车间产线编号-->
    <record id="roek_workshop_code_sequence" model="ir.sequence">
        <field name="name">车间产线编号</field>
        <field name="code">roke.workshop.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--业务伙伴编号-->
    <record id="roek_partner_code_sequence" model="ir.sequence">
        <field name="name">业务伙伴编号</field>
        <field name="code">roke.partner.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--班组信息编号-->
    <record id="roek_work_team_code_sequence" model="ir.sequence">
        <field name="name">班组信息编号</field>
        <field name="code">roke.work.team.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--部门信息编号-->
    <record id="roke_department_code_sequence" model="ir.sequence">
        <field name="name">部门信息编号</field>
        <field name="code">roke.department.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--人员信息编号-->
    <record id="roek_employee_code_sequence" model="ir.sequence">
        <field name="name">人员信息编号</field>
        <field name="code">roke.employee.code</field>
        <field eval="True" name="use_date_range"/>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--产品信息编号-->
    <record id="roek_product_code_sequence" model="ir.sequence">
        <field name="name">产品信息编号</field>
        <field name="code">roke.product.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--工序编号-->
    <record id="roek_process_code_sequence" model="ir.sequence">
        <field name="name">工序编号</field>
        <field name="code">roke.process.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
    <!--工艺路线编号-->
    <record id="roke_routing_code_sequence" model="ir.sequence">
        <field name="name">工艺路线编号</field>
        <field name="code">roke.routing.code</field>
        <field name="padding">3</field>
        <field name="company_id" eval="False"/>
    </record>
</odoo>
