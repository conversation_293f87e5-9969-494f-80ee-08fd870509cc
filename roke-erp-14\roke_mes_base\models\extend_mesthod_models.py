# -*- coding: utf-8 -*-
import logging
from odoo import models, _
from odoo.exceptions import UserError


def get_selection_field_values(self, field_name, value=None):
    """
    获取 selection 字段的中文值
    :param record: Odoo 模型记录
    :param field_name: 字段名
    :param value: 字段值（可选）
    :return: 中文 label
    """
    field = self._fields.get(field_name)
    if not field or field.type != 'selection':
        return ''

    if value is None:
        value = getattr(self, field_name)

    selections = field.selection
    if callable(selections):  # 动态 selection
        selections = selections(self)

    for k, v in selections:
        if k == value:
            return v
    return ''

models.BaseModel.get_selection_field_values = get_selection_field_values