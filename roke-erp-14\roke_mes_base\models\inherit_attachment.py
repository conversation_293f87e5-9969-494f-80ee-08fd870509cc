# -*- coding: utf-8 -*-
"""
生成文档预览的地址
"""
import base64
import urllib.parse

from odoo import models, fields, api, tools, _
import uuid


EXTENSIONS_2D = [
    "dwg", "dxf"
]
EXTENSIONS_3D = [
    "sldasm", "sldprt", "asm", "par", "pwd", "psm", "prt", "neu", "iam", "ipt",
    "catpart", "catproduct", "catshape", "cgr", "3dxml", "stp", "step", "stpz",
    "stepz", "stpx", "stpxz", "igs", "iges", "stl", "obj", "mtl", "x_t", "xmt_txt",
    "x_b", "xmt_bin", "p_b", "xmp_bin", "xmp_txt", "sat", "sab", "jt", "3dm",
    "fbx", "gltf", "glb", "prc", "u3d"
]
API = {
    "CREATE_JOB": "/transform/createJob",
    "DOWNLOAD": "/workspace/downloadFile",
}


class InheritIrAttachment(models.Model):
    _inherit = "ir.attachment"

    @api.model
    def preview_attachment_url(self, attachment_id):
        attachment_res = self.env["ir.attachment"].sudo().search(
            [("id", "=", attachment_id)])
        if not attachment_res:
            return {"code": 1, "message": "没有找到相关附件", "data": None}
        else:
            result = {"code": 0, "message": "附件地址"}
            extension = attachment_res.name.split(".")[-1].lower()
            if (extension in EXTENSIONS_2D or extension in EXTENSIONS_3D) and attachment_res.url:
                icp = self.sudo().env['ir.config_parameter']
                transform_service = icp.get_param('p.transform.service')
                model_url = attachment_res.url + ".scs"
                model_url_base64 = base64.b64encode(model_url.encode()).decode()
                url = f"{transform_service}/transform2/z3d/?model={urllib.parse.quote(model_url_base64)}"
                result["data"] = {"extra": True, "url": url}
                return result
            if not attachment_res.access_token:
                attachment_res.generate_access_token()
            code = str(uuid.uuid4())
            content_url = f"/web/content/{attachment_res.id}/{code}_{attachment_res.name}" \
                          f"?access_token={attachment_res.sudo().access_token}"
            result["data"] = {"url": content_url}
            return result