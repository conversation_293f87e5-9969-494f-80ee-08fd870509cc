# -*- coding: utf-8 -*-
import logging
from odoo import models, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class IrModuleModule(models.Model):
    _inherit = 'ir.module.module'

    def button_immediate_install(self):
        """ Installs the selected module(s) immediately and fully,
        returns the next res.config action to execute

        :returns: next res.config item to execute
        :rtype: dict[str, object]
        """
        module_list = []
        try:
            module_list = self.env['res.config.settings'].sudo().message_fetch_modules()
        except:
            _logger.info(" * " * 50)
            _logger.info("获取不可用模块列表时出错。")
            _logger.info(" * " * 50)

        if self.name in module_list:
            raise UserError("没有授权信息，请联系客服************。")

        res = self._button_immediate_function(type(self).button_install)
        # res = super(IrModuleModule, self).button_immediate_install()
        return res
