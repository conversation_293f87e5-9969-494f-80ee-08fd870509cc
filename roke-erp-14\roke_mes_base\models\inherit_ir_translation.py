# -*- coding: utf-8 -*-
"""
翻译
"""
from odoo import models, fields, api, tools, _
import uuid


class InheritIrTranslation(models.Model):
    _inherit = "ir.translation"

    def init(self):
        try:
            translation_obj = self.env['ir.translation']
            menu_obj = self.env['ir.ui.menu']
            menu_app = menu_obj.sudo().search([('name', '=', 'Apps'), ('web_icon', '!=', False)], limit=1)
            app = translation_obj.sudo().search([
                ('module', '=', 'base'),('name','=','ir.ui.menu,name'),('res_id','=',menu_app.id)],limit=1)
            if app:
                app.write({'value': '应用管理'})
            # else:
            #     translation_obj.create({
            #         'src': 'Apps',
            #         'value': '应用管理',
            #         'name': 'ir.ui.menu,name',
            #         'lang': 'zh_CN',
            #         'module': 'base',
            #         'type': 'model',
            #         'state': 'translated',
            #         'res_id': menu_app.id
            #     })
            menu_setting = menu_obj.sudo().search([('name', '=', '设置'), ('web_icon', '!=', False)], limit=1)
            setting = translation_obj.sudo().search([
                ('module', '=', 'base'), ('name', '=', 'ir.ui.menu,name'),('res_id','=',menu_setting.id)],limit=1)
            if setting:
                setting.write({'value': '系统设置'})
            # else:
            #     translation_obj.create({
            #         'src': 'Settings',
            #         'value': '系统设置',
            #         'name': 'ir.ui.menu,name',
            #         'lang': 'zh_CN',
            #         'module': 'base',
            #         'type': 'model',
            #         'state': 'translated',
            #         'res_id': menu_setting.id
            #     })
        except Exception as e:
            print(e)