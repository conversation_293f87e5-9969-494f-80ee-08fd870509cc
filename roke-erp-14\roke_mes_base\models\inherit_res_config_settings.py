# -*- coding: utf-8 -*-
"""
Description:
    系统参数
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api
import requests
import json

class InheritConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    preview_service = fields.Char(string="预览服务地址")
    storage_service = fields.Char(string="工业文件上传服务")
    transform_service = fields.Char(string="工业文件转换服务")

    def set_values(self):
        super(InheritConfigSettings, self).set_values()
        icp = self.env['ir.config_parameter']
        icp.sudo().set_param('kkfileview.url', self.preview_service)
        icp.sudo().set_param('p.storage.service', self.storage_service)
        icp.sudo().set_param('p.transform.service', self.transform_service)

    @api.model
    def get_values(self):
        icp = self.env['ir.config_parameter']
        res = super(InheritConfigSettings, self).get_values()
        res.update(
            preview_service=icp.sudo().get_param('kkfileview.url'),
            storage_service=icp.sudo().get_param('p.storage.service'),
            transform_service=icp.sudo().get_param('p.transform.service'),
        )
        return res

    @api.model
    def message_fetch_modules(self):
        icp = self.env['ir.config_parameter']
        database_uuid = icp.sudo().get_param("database.uuid", False)
        requests_url = icp.sudo().get_param("roke.mes.cloud.url", False)
        module_list = [] # 不可安装列表
        if not database_uuid or not requests_url:
            module_list = []
        try:
            result = requests.post(
                f"{requests_url}/roke/mes/cloud/factory/auth_modules",
                data=json.dumps({"database_uuid": database_uuid}),
                headers={"Content-Type": "application/json"}
            )
            if result.status_code == 200:
                result = json.loads(result.content).get("result")
                module_list = result.get("module_list", [])
        except:
            module_list = []
        return module_list

    @api.model
    def message_fetch_poll(self):
        icp = self.env['ir.config_parameter']
        database_uuid = icp.sudo().get_param("database.uuid", False)
        requests_url = icp.sudo().get_param("roke.mes.cloud.url", False)
        if not database_uuid or not requests_url:
            return {"deadline": None, "series": ""}
        
        try:
            result = requests.post(
                f"{requests_url}/roke/mes/cloud/deadline",
                data=json.dumps({"database_uuid": database_uuid}),
                headers={"Content-Type": "application/json"}
            )
            if result.status_code == 200:
                result = json.loads(result.content).get("result")
                deadline = result.get("deadline", "*")
                series = result.get("series", "")
            else:
                deadline = '*'
                series = ""
        except:
            deadline = '*'
            series = ""
        return {"deadline": deadline, "series": series}
