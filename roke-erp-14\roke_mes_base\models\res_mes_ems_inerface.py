#!/usr/bin/env python
# -*- coding:utf-8 -*-
import json
import re
from odoo import models, fields, api
import requests
from odoo.exceptions import ValidationError


class RokeEmsDataParam(models.TransientModel):
    _name = 'roke.common.data.param'
    _description = '接口数据拉取'
    _rec_name = 'url'

    url = fields.Char(string="外部接口地址")
    method = fields.Selection([
        ('get', 'GET'),
        ('post', 'POST')
    ], string="请求方式", default='post')

    param_ids = fields.One2many('roke.common.data.param.line', 'head_id', string='自定义参数')

    result = fields.Text(string="返回数据", readonly=True, default='null')

    @api.constrains('url')
    def _check_url(self):
        pattern = r'^(http|https)://'
        for record in self:
            if not record.url or not re.match(pattern, record.url):
                raise ValidationError("接口地址必须以 http:// 或 https:// 开头")

    @api.constrains('param_ids')
    def _check_unique_param_keys(self):
        for record in self:
            all_keys = set()
            for param in record.param_ids:
                if param.key in all_keys:
                    raise ValidationError(f"参数名【{param.key}】重复，请确保每个参数名唯一。")
                all_keys.add(param.key)

    def fetch_data(self):
        url = self.url
        params = {}
        for param in self.param_ids:
            if not param.key:
                continue
            if param.value_type == 'integer':
                params[param.key] = int(param.value)
            elif param.value_type == 'float':
                params[param.key] = float(param.value)
            elif param.value_type == 'boolean':
                params[param.key] = param.value.lower() in ('true', '1', 'yes')
            elif param.value_type == 'json':
                try:
                    params[param.key] = json.loads(param.value)
                except json.JSONDecodeError:
                    params[param.key] = param.value  # 出错时就保持原始字符串
            else:
                params[param.key] = param.value

        try:
            if self.method == 'get':
                response = requests.get(url, params=params)
            else:
                response = requests.post(url, json=params)
            if response.status_code == 200:
                self.result = response.text
                # 弹窗提示成功
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Success',
                        'message': '数据拉取成功！',
                        'sticky': False,
                    }
                }
            else:
                self.result = f"请求失败：HTTP状态码 {response.status_code}"
        except Exception as e:
            self.result = f"请求失败：{str(e)}"


class RokeEmsDataParamLine(models.TransientModel):
    _name = 'roke.common.data.param.line'
    _description = '自定义参数'

    key = fields.Char(string='参数名')
    value = fields.Char(string='参数值')
    value_type = fields.Selection([
        ('string', '字符串'),
        ('integer', '整数'),
        ('float', '浮点数'),
        ('boolean', '布尔值'),
        ('json', 'JSON对象'),
        ('list',"数组")
    ], string='参数类型', default='string')
    head_id = fields.Many2one('roke.common.data.param', string='接口头')

    @api.constrains('value', 'value_type')
    def _check_value_type(self):
        for record in self:
            if not record.value:
                continue  # 空值跳过校验
            try:
                if record.value_type == 'integer':
                    int(record.value)
                elif record.value_type == 'float':
                    float(record.value)
                elif record.value_type == 'boolean':
                    if record.value.lower() not in ('true', 'false', '1', '0', 'yes', 'no'):
                        raise ValueError('布尔类型需要是true/false/1/0/yes/no之一')
                elif record.value_type == 'json':
                    json.loads(record.value)
                elif record.value_type == 'list':
                    parsed = json.loads(record.value)
                    if not isinstance(parsed, list):
                        raise ValueError("值必须是一个 JSON 数组")
                # 字符串不用校验
            except Exception as e:
                raise ValidationError(f"参数【{record.key}】的值与类型不匹配: {str(e)}")
