# -*- coding: utf-8 -*-
import ast
from odoo import models, fields, api, _
from datetime import timedelta
from odoo.tools.float_utils import float_round


def time_to_float(t):
    """时间转换为小数"""
    return float_round(t.hour + t.minute/60 + t.second/3600, precision_digits=2)


class RokeWorkShift(models.Model):
    _name = 'roke.work.shift'
    _rec_name = "name"

    name = fields.Char(string='班次名称')


class RokeClasses(models.Model):
    _name = "roke.classes"
    _description = "班次字典"
    _rec_name = "work_shift_id"

    def _compute_domain(self):
        config_work_shifts = self.env['ir.config_parameter'].sudo().get_param('work.shift.ids', False),
        if not config_work_shifts:
            return [('id', 'in', [])]
        work_shifts = self.env['roke.work.shift'].sudo().search([('id', 'in', ast.literal_eval(list(config_work_shifts)[0] or "False"))])
        return [('id', 'in', work_shifts.ids)]

    work_shift_id = fields.Many2one('roke.work.shift', string='班次', domain=_compute_domain)
    name = fields.Char(string="名称", related='work_shift_id.name', store=True)
    start_time = fields.Float(string="开始时间", default="0")
    end_time = fields.Float(string="结束时间", default="0")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    @api.onchange('start_time', 'end_time')
    def _onchange_start_time(self):
        if self.start_time < 0 or self.start_time >= 24:
            return {
                'warning': {'title': "时间错误",
                            'message': "时间不能小于0或大于等于24"},
                'value': {'start_time': 0}
            }
        if self.end_time < 0 or self.end_time >= 24:
            return {
                'warning': {'title': "时间错误",
                            'message': "时间不能小于0或大于等于24"},
                'value': {'end_time': 0}
            }

    def get_now_classes(self):
        """
        获取当前时间的班次
        :return:
        """
        now_time = fields.Datetime.now() + timedelta(hours=8)
        time_float = time_to_float(now_time)
        return self.search([("start_time", "<", time_float), ("end_time", ">=", time_float)], limit=1)


