# -*- coding: utf-8 -*-
"""
Description:
    部门信息
Versions:
    Created by www.rokedata.com
"""

from odoo import models, fields, api, _
from odoo.osv import expression

class RokeDepartment(models.Model):
    _name = "roke.department"
    _description = "部门信息"
    _inherit = ['mail.thread']
    _order = "id desc"

    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.department.code'))
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    up_department = fields.Many2one('roke.department',string="上级部门")
    manager_id = fields.Many2one("roke.employee", string="部门组长", tracking=True)
    employee_ids = fields.One2many("roke.employee", "department_id", string="部门成员")
    note = fields.Text(string="备注")
    child_department_ids = fields.One2many("roke.department", "up_department", string="下级部门")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    employee_line_ids = fields.One2many("roke.department.line", "line_id", string="部门成员")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

class RokeDepartmentLine(models.Model):
    _name = "roke.department.line"
    _description = "部门人员信息"

    line_id = fields.Many2one("roke.department", string="部门", required=True, ondelete='cascade')
    employee_id = fields.Many2one("roke.employee", string="班组成员")
    code = fields.Char(related="employee_id.code", string="编号")
    job_number = fields.Char(related="employee_id.job_number", string="工号")
    phone = fields.Char(related="employee_id.phone", string="电话")
    team_weighted = fields.Float(related="employee_id.team_weighted", string="班组报工权重")
    user_id = fields.Many2one(related="employee_id.user_id", string="系统用户")
    position_id = fields.Many2one(related="employee_id.position_id", string="职位")
    gender = fields.Selection(related="employee_id.gender", string='性别')
    is_on_job = fields.Selection(related="employee_id.is_on_job", string='在职情况')
    id_number = fields.Char(related="employee_id.id_number", string="身份证号")
    age = fields.Char(related="employee_id.age", string="年龄")
    registered_residence = fields.Char(related="employee_id.registered_residence", string="户口所在地")
    skill_level_id = fields.Many2one(related="employee_id.skill_level_id", string='技能等级', index=True)
    note = fields.Text(related="employee_id.note", string="备注")
    team_id = fields.Many2one(related="employee_id.team_id", string='班组')

    @api.model
    def create(self, vals):
        res = super(RokeDepartmentLine, self).create(vals)
        res.employee_id.write({'department_id': res.line_id.id})
        return res
