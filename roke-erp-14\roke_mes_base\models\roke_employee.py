# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.modules.module import get_module_resource
from odoo.exceptions import ValidationError
import base64
import re
import logging
from odoo.osv import expression
import time

_logger = logging.getLogger(__name__)

class RokePositionDict(models.Model):
    _name = "roke.position.dict"
    _description = "职位字典"

    name = fields.Char(string='名称', required=True, translate=True)
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    _sql_constraints = [
        ('name_unique', 'UNIQUE(name, company_id)', '名称已存在，不可重复。')
    ]

class RokeEmployee(models.Model):
    _name = "roke.employee"
    _inherit = ['mail.thread', 'image.mixin']
    _description = "人员信息"
    _order = "name"
    _rec_name = "name"

    @api.model
    def _default_image(self):
        image_path = get_module_resource('roke_mes_base', 'static/src/img', 'default_image.png')
        return base64.b64encode(open(image_path, 'rb').read())

    erp_id = fields.Char(string="ERP ID")
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.employee.code'))
    job_number = fields.Char(string="工号")
    phone = fields.Char(string="电话")
    team_id = fields.Many2one("roke.work.team", string="班组")
    team_weighted = fields.Float(string="班组报工权重", default=1)
    image_1920 = fields.Image(default=_default_image)
    user_id = fields.Many2one("res.users", string="系统用户")
    position_id = fields.Many2one("roke.position.dict", string="职位")
    gender = fields.Selection([('男', '男'), ('女', '女')], string='性别')
    is_on_job = fields.Selection([('在职', '在职'), ('离职', '离职')], string='在职情况')
    id_number = fields.Char(string="身份证号")
    age = fields.Char(string="年龄")
    registered_residence = fields.Char(string="户口所在地")
    skill_level_id = fields.Many2one('roke.skill.level', string='技能等级', index=True)

    note = fields.Text(string="备注")
    department_id = fields.Many2one('roke.department', string='部门')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def name_get(self):
        return super(RokeEmployee, self).name_get()

    # 处理导入编号问题
    @api.model
    def create(self, vals):
        tag = True
        if vals.get('code') != 'demo' and not vals.get('code'):
            while tag:
                code = self.env['ir.sequence'].next_by_code('roke.employee.code')
                if not self.env['roke.employee'].search([('code','=',code)]):
                    vals.update({'code':code})
                    tag = False
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.employee.code')
        return super(RokeEmployee, self).create(vals)

    @api.onchange("id_number")
    def _onchange_id_number(self):
        local_time = time.localtime(time.time())
        if self.id_number and re.match(r"(^\d{15}$)|(^\d{17}(\d|X)$)", self.id_number):
            age = int(local_time[0]) - int(self.id_number[6:10])
            if age > 0:
                if int(local_time[1]) - int(self.id_number[10:12]) > 0:
                    self.age = age
                elif int(local_time[1]) - int(self.id_number[10:12]) == 0:
                    if int(local_time[2]) - int(self.id_number[12:14]) >= 0:
                        self.age = age
                    else:
                        self.age = age - 1
                else:
                    self.age = age - 1
            else:
                return {"warning": {
                    "title": "提醒", "message": "身份证号格式错误"
                }, "value": {
                    "id_number": "",
                    "age": ""
                }}
        # 校验身份证号码
        if self.id_number and not re.match(r"(^\d{15}$)|(^\d{17}(\d|X)$)", self.id_number):
            return {"warning": {
                "title": "提醒", "message": "身份证号格式错误"
            }, "value": {
                "id_number": "",
                "age": ""
            }}

    def create_system_user(self):
        """
        创建登录用户
        :return:
        """
        UserObj = self.sudo().env["res.users"]
        for employee in self:
            if not employee.phone:
                raise ValidationError("创建用户必须录入员工手机号。")
            _logger.info("员工创建登录用户")
            if not employee.job_number:
                _logger.info("员工无工号,将使用员工名称为其创建登录用户")
                login = employee.name
            else:
                login = employee.job_number
            login_user = UserObj.create({
                "name": employee.name,
                "login": login,
                "phone": employee.phone,
                "tz": "Asia/Shanghai",
                "notification_type": "inbox",
                "password": "000000",
                "employee_ids": [(6, 0, [employee.id])]
            })
            employee.write({"user_id": login_user.id})

    def get_user_employee(self):
        """
        获取当前登录人对应的员工
        :return:
        """
        self.sudo().search([("user_id", "=", self.env.user.id)])

    @api.onchange("phone")
    def _onchange_phone(self):
        """
        校验电话格式
        :return:
        """
        if self.phone and not re.match(r"^(1[3|4|5|6|7|8|9])\d{9}$|^0\d{2,3}-?\d{7,8}$", self.phone):
            return {"warning": {
                "title": "提醒", "message": "电话格式错误"
            }, "value": {
                "phone": ""
            }}

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)


class ResUsers(models.Model):
    _inherit = 'res.users'

    openid = fields.Char(string='WeChat OpenID')