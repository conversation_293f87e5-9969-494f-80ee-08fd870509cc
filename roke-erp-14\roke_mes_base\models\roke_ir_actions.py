from odoo import fields, models, api
import logging
_logger = logging.getLogger(__name__)


class IrActionsReportInherit(models.Model):
    _inherit = 'ir.actions.report'

    def _render_template(self, template, values=None):
        """
        继承原生_render_template方法
        """
        res = super(IrActionsReportInherit, self)._render_template(template, values)
        try:
            report_obj = self.env['ir.actions.report']
            print_log_obj = self.env['roke.print.log']
            actions_report_record = report_obj.sudo().search([('report_name', '=', template)],limit=1) if template else False
            if values and values.get('docs', []):
                print_log_list = []
                for rec in values.get('docs', []):
                    res_model = rec._name
                    res_id = rec.id
                    print_name = actions_report_record.name if actions_report_record else ''
                    print_log_list.append({
                        # 打印的模型
                        'res_model': res_model,
                        # 打印的记录ID
                        'res_id': res_id,
                        # 打印的名称
                        'print_name': print_name,
                        # 打印次数
                        'print_count': 1
                        })
                    # 对生成任务单特殊处理
                    if rec._name == 'roke.production.task':
                        rec.print_user_id = self.env.user.id
                        rec.print_time = fields.Datetime.now()
                        rec.print_count += 1
                    # 对采购订单特殊处理
                    elif rec._name == 'roke.purchase.order':
                        rec.print_count += 1
                if print_log_list:
                    print_log_obj.create(print_log_list)
            return res
        except Exception as e:
            _logger.info(e)
            return res
