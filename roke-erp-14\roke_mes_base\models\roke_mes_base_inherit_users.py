# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
import logging
import re
_logger = logging.getLogger(__name__)


class RokeMesBaseInheritUsers(models.Model):
    _inherit = "res.users"

    employee_ids = fields.Many2many("roke.employee", "roke_employee_user_rel", string="可报工员工",
                                    help="可使用该账号报工的员工")

    employee_id = fields.Many2one("roke.employee", string="员工")
    is_hide_employee = fields.Boolean(string="是否隐藏员工", compute="_compute_is_hide_employee", default=True)
    is_required_employee = fields.<PERSON><PERSON>an(string="是否必填员工", compute="_compute_is_hide_employee")
    employee_domain = fields.Many2many("roke.employee", "roke_employee_user_domain_rel", string="员工过滤",
                                       compute="_compute_is_hide_employee")

    @api.depends('name', 'phone')
    def _compute_is_hide_employee(self):
        roke_employee_obj = self.env['roke.employee']
        for rec in self:
            if not rec.id:
                rec.employee_id = False
            rec.employee_domain = [(6, 0, [])]
            if rec.name and rec.phone:
                is_auto = False
                # 先根据手机号进行判断【用户的手机号与员工（中未关联用户）的手机号进行精确匹配，匹配到了，则直接关联】
                roke_employee_record = roke_employee_obj.search(
                    [('phone', '=', rec.phone), ('user_id', '=', False), ('active', '=', True)],
                    limit=1)
                if roke_employee_record:
                    is_auto = True
                else:
                    # 如果手机号没匹配到，再根据姓名进行判断【用户的姓名与员工（中未关联用户）的姓名进行精确匹配，如匹配到一个则直接关联，如果匹配多个，则弹窗窗口进行选择】
                    roke_employee_record = roke_employee_obj.search(
                        [('name', '=', rec.name), ('user_id', '=', False), ('active', '=', True)])
                    if not roke_employee_record:
                        is_auto = True
                    elif len(roke_employee_record) == 1:
                        is_auto = True
                    else:
                        rec.employee_domain = [(6, 0, roke_employee_record.ids)]
                if is_auto:
                    rec.is_hide_employee = True
                    rec.is_required_employee = False
                else:
                    rec.is_hide_employee = False
                    rec.is_required_employee = True

            else:
                rec.is_hide_employee = True
                rec.is_required_employee = False
            if rec.employee_id:
                rec.is_hide_employee = False
                rec.is_required_employee = True

    # @api.constrains('employee_id', 'name', 'phone')
    # def _check_employee_id(self):
    #     for rec in self:
    #         pass

    @api.model_create_multi
    def create(self, vals_list):
        users = super(RokeMesBaseInheritUsers, self).create(vals_list)
        for rec in users:
            rec.create_employee()
        return users

    def create_employee(self):
        self.ensure_one()
        roke_employee_obj = self.env['roke.employee']
        if self.phone and self.name and not self.employee_id:
            # 先根据手机号进行判断【用户的手机号与员工（中未关联用户）的手机号进行精确匹配，匹配到了，则直接关联】
            roke_employee_record = roke_employee_obj.search(
                [('phone', '=', self.phone), ('user_id', '=', False), ('active', '=', True)],
                limit=1)
            if roke_employee_record:
                self.employee_id = roke_employee_record.id
                roke_employee_record.user_id = self.id
            else:
                # 如果手机号没匹配到，再根据姓名进行判断【用户的姓名与员工（中未关联用户）的姓名进行精确匹配，如匹配到一个则直接关联，如果匹配多个，则弹窗窗口进行选择】
                roke_employee_record = roke_employee_obj.search(
                    [('name', '=', self.name), ('user_id', '=', False), ('active', '=', True)])
                if not roke_employee_record:
                    roke_employee_record = roke_employee_obj.create({
                        'name': self.name,
                        'phone': self.phone,
                        'user_id': self.id
                    })
                    self.employee_id = roke_employee_record.id
                elif len(roke_employee_record) == 1:
                    self.employee_id = roke_employee_record.id
                    roke_employee_record.user_id = self.id
                else:
                    pass
        elif self.phone and self.name and self.employee_id:
            self.employee_id.user_id = self.id
