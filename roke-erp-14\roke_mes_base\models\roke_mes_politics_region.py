#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
        PyCharm
@File:
        roke_mes_politics_region.py
@Time:
        2024/7/20 10:00
@Site: 
    
@Desc:
    
"""

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
import requests
import logging

_logger = logging.getLogger(__name__)


class IrConfigParameter(models.Model):
    _inherit = "ir.config_parameter"

    def init(self, force=False):
        super(IrConfigParameter, self).init(force=force)
        param = self.sudo().search([("key", "=", "amap.web.url")])
        param1 = self.sudo().search([("key", "=", "amap.web.key")])
        if force or not param:
            param.set_param("amap.web.url", "https://restapi.amap.com/v3/config/district")
        if force or not param1:
            param.set_param("amap.web.key", "4f9bd79ef43c9724a942defd1187a175")


class RokeMsePoliticsRegion(models.Model):
    _name = "roke.mes.politics.region"
    _description = "行政区域"
    _order = "adcode"
    _rec_name = "name"
    _parent_name = "parent_id"
    _parent_store = True

    adcode = fields.Char(string="编号")
    name = fields.Char(string="名称")
    complete_name = fields.Char("完整名称", compute='_compute_complete_name', store=True)
    continuous_name = fields.Char("连续名称", compute='_compute_complete_name', store=True)
    center = fields.Char(string="经纬度")
    level = fields.Selection(
        [("country", "国家"), ("province", "省份"), ("city", "城市"), ("district", "区县"), ("street", "乡镇")], string="层级")
    parent_id = fields.Many2one("roke.mes.politics.region", string="上级行政区域")
    child_ids = fields.One2many("roke.mes.politics.region", "parent_id", string="下级行政区域")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    parent_path = fields.Char(index=True)
    citycode = fields.Char(string="区号")

    @api.depends('name', 'parent_id.complete_name')
    def _compute_complete_name(self):
        for area in self:
            if area.parent_id:
                area.complete_name = '%s/%s' % (area.parent_id.complete_name, area.name)
                area.continuous_name = '%s%s' % (area.parent_id.continuous_name, area.name)
            else:
                area.complete_name = '%s' % (area.name)
                area.continuous_name = '%s' % (area.name)

    def auto_update_area(self):
        ConfigParamObj = self.env["ir.config_parameter"].sudo()
        amap_web_url = ConfigParamObj.get_param("amap.web.url")
        amap_web_key = ConfigParamObj.get_param("amap.web.key")
        url = "%s?key=%s&subdistrict=%s" % (amap_web_url, amap_web_key, 4)
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "1":

                def save_area(area_data, parent_id=None):
                    area_domain = [
                        ("adcode", "=", area_data.get("adcode")),
                        ("name", "=", area_data.get("name")),
                        ("level", "=", area_data.get("level"))
                    ]
                    area_obj = {
                        "adcode": area_data.get("adcode"),
                        "name": area_data.get("name"),
                        "center": area_data.get("center"),
                        "level": area_data.get("level"),
                        "parent_id": parent_id,
                        "citycode": area_data.get("citycode") or False
                    }
                    area = self.search(area_domain)
                    if not area:
                        _logger.info("=====执行创建 %s[%s]" % (area_data.get("name"), area_obj))
                        area = self.create(area_obj)
                    else:
                        _logger.info("=====执行修改 %s[%s]" % (area_data.get("name"), area_obj))
                        area.write(area_obj)
                    if area_data.get("districts"):
                        child_area_datas = area_data.get("districts")
                        for child_area_data in child_area_datas:
                            save_area(child_area_data, parent_id=area.id)

                area_datas = data.get("districts")
                for area_data in area_datas:
                    save_area(area_data=area_data, parent_id=None)
