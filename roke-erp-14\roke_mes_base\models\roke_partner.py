# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.modules.module import get_module_resource
import base64
from random import randint
import re
from odoo.exceptions import UserError
from odoo.osv import expression


class RokePartnerTag(models.Model):
    _description = '业务伙伴标签'
    _name = 'roke.partner.tag'
    _order = 'name'

    def _get_default_color(self):
        return randint(1, 11)

    name = fields.Char(string='名称', required=True, translate=True)
    color = fields.Integer(string='颜色索引', default=_get_default_color)
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


class RokePartner(models.Model):
    _name = "roke.partner"
    _inherit = ['mail.thread', 'image.mixin']
    _description = "业务伙伴"
    _order = "code"

    @api.model
    def _default_image(self):
        image_path = get_module_resource('roke_mes_base', 'static/src/img', 'default_image.png')
        return base64.b64encode(open(image_path, 'rb').read())

    def _default_category(self):
        return self.env['roke.partner.tag'].browse(self._context.get('category_id'))

    erp_id = fields.Char(string="ERP ID")
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.partner.code'))
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    customer = fields.Boolean(string="客户")
    supplier = fields.Boolean(string="供应商")
    contacts = fields.Char(string="联系人")
    contacts_phone = fields.Char(string="联系电话")
    address = fields.Text(string="地址")
    image_1920 = fields.Image(default=_default_image)
    tag_ids = fields.Many2many('roke.partner.tag', column1='partner_id', column2='tag_id', string='标签', default=_default_category)
    user_id = fields.Many2one("res.users", string="系统用户")
    note = fields.Text(string="备注")
    vat_number = fields.Char(string="税号", track_visibility="always")
    legal_person = fields.Char(string="法人")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    srm_id = fields.Char(string="SRM ID")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)',
         '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    # @api.constrains('customer', 'supplier')
    # def _check_customer_supplier(self):
    #     for rec in self:
    #         if not rec.customer and not rec.supplier:
    #             raise UserError('请为该数据指定是客户还是供应商')

    @api.model
    def create(self, vals):
        if not vals.__contains__("customer"):
            vals["customer"] = self.env.context.get('customer') or False
        if not vals.__contains__("supplier"):
            vals["supplier"] = self.env.context.get('supplier') or False
        return super(RokePartner, self).create(vals)

    @api.onchange("contacts_phone")
    def _onchange_phone(self):
        """
        校验电话格式
        :return:
        """
        if self.contacts_phone and not re.match(r"^(1[3|4|5|6|7|8|9])\d{9}$|^0\d{2,3}-?\d{7,8}$", self.contacts_phone):
            return {"warning": {
                "title": "提醒", "message": "电话格式错误"
            }, "value": {
                "contacts_phone": ""
            }}

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)




