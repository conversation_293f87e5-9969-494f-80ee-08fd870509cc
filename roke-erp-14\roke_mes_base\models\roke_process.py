# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.osv import expression
from urllib import parse


class RokeProcess(models.Model):
    _name = "roke.process"
    _inherit = ['mail.thread']
    _description = "工序"
    _order = "code"

    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.process.code'))
    category_id = fields.Many2one("roke.process.category", string="工序类别", tracking=True)
    process_type = fields.Selection([("main", "主工序"), ("child", "子工序")], string="分类", tracking=True, default="main")
    internal_code = fields.Char(string="内部标识", tracking=True)
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    without_wo_produce = fields.Boolean(string="无工单报工记录产出物", help="该工序无工单报工时是否产生产出物", default=False, tracking=True)
    note = fields.Text(string="描述")
    child_process_ids = fields.One2many("roke.process.child", "main_process_id", string="子工序")
    # 作业指导图片
    instruction_file_data = fields.Image('作业指导图片')
    image_128 = fields.Image('作业指导图片', related="instruction_file_data", max_width=128, max_height=128)
    standard_item_ids = fields.One2many("roke.work.standard.item", "process_id", string="作业规范")
    default_employee_ids = fields.Many2many("roke.employee", string="默认报工人员")
    rated_working_hours = fields.Float(string="额定工时")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    is_press = fields.Boolean(string="是否扣除工装生命周期", default=False)

    prepare_work_hours = fields.Float(string="准备工时")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.onchange("process_type")
    def _onchange_process_type(self):
        if self.process_type == "main":
            # 检查是否属于其它工序的子工序
            if self.env["roke.process.child"].search([("child_process_id", "in", self.ids)]):
                return {
                    "value": {"process_type": "child"},
                    "warning": {"title": "分类选择错误", "message": "该工序已作为子工序使用，禁止修改工序分类。"}
                }
        elif self.process_type == "child":
            # 检查是否作为主工序在工艺路线中使用
            if self.env["roke.routing.line"].search([("process_id", "in", self.ids)]):
                return {
                    "value": {"process_type": "main"},
                    "warning":{"title": "分类选择错误", "message": "该工序已在工艺路线中作为主工序使用，禁止修改工序分类。"}
                }

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

    def get_instruction_file_url(self, file_type=None):
        """
        获取作业知道图片预览地址
        :param file_type: 入参‘base64’或预览地址
        :return:
        """
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachment = self.sudo().env['ir.attachment'].search([
            ("res_model", "=", "roke.process"),
            ("res_id", "=", self.id),
            ("res_field", "=", "instruction_file_data")
        ])  # 必须带sudo，原因见odoo-14.0/odoo/addons/base/models/ir_attachment.py 第441行
        if not attachment:
            return False
        if file_type == "base64":
            return [{
                "id": attachment.id,
                "name": attachment.name,
                "type": "base64",
                "data": attachment.datas,
                "is_picture": False,
            }]
        if not attachment.access_token:
            attachment.generate_access_token()
        if attachment.mimetype == "application/pdf":
            # pdf 预览
            content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
            url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
            is_picture = False
        else:
            # 图片 预览
            url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
            is_picture = True if attachment.index_content == 'image' else False
        return [{
            "id": attachment.id,
            "name": attachment.name,
            "mimetype": attachment.mimetype,
            "type": "url",
            "data": url,
            "is_picture": is_picture,
        }]

    def multi_add_child_process(self):
        # 批量添加子工序
        return {
            'name': '批量添加子工序',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.multi.add.child.process',
            'view_mode': 'form',
            'target': 'new'
        }

    @api.onchange("child_process_ids", "child_process_ids.child_rated_wh")
    def _onchange_child_process_rated_wh(self):
        """
        子工序额定工时onchange
        :return:
        """
        self.refresh_rated_working_hours()

    def refresh_rated_working_hours(self):
        """
        刷新额定工时，主工序额定工时等于子工序额定工时合计;存在子工序时，主工序额定工时禁止修改
        :return:
        """
        self.ensure_one()
        if self.child_process_ids:
            self.rated_working_hours = sum(self.child_process_ids.mapped("child_rated_wh"))


class RokeProcessChild(models.Model):
    _name = "roke.process.child"
    _inherits = {"roke.process": "child_process_id"}
    _description = "子工序"
    _order = "sequence"

    sequence = fields.Integer(string="序号", default=1)
    main_process_id = fields.Many2one("roke.process", string="主工序", ondelete='cascade')
    routing_line_id = fields.Many2one("roke.routing.line", string="工艺明细", ondelete='cascade')
    child_process_id = fields.Many2one("roke.process", string="子工序", required=True, ondelete='cascade')
    child_rated_wh = fields.Float(string="子工序额定工时")

    @api.onchange("child_process_id")
    def _onchange_child_process_id(self):
        if self.child_process_id:
            return {"value": {"child_rated_wh": self.child_process_id.rated_working_hours}}

    def check_child_rated_wh(self):
        """
        校验额定工时，子工序额定工时合计必须等于主工序额定工时
        :return:
        """
        main_process_list = list(set(self.main_process_id))
        routing_line_list = list(set(self.routing_line_id))
        for main_process in main_process_list:
            main_process.refresh_rated_working_hours()
        for routing_line in routing_line_list:
            routing_line.refresh_rated_working_hours()

    @api.model
    def create(self, vals):
        res = super(RokeProcessChild, self).create(vals)
        res.check_child_rated_wh()
        return res

    def write(self, vals):
        res = super(RokeProcessChild, self).write(vals)
        if vals.get("child_rated_wh"):
            self.check_child_rated_wh()
        return res
