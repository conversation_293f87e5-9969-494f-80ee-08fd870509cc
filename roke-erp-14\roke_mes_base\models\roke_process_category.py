# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _


class RokeProcessCategory(models.Model):
    _name = "roke.process.category"
    _description = "工序类别"

    name = fields.Char(string="名称", required=True, index=True)
    active = fields.Boolean(string="有效的", default=True)
    parent_id = fields.Many2one("roke.process.category", string="上级类别")
    child_ids = fields.One2many("roke.process.category", "parent_id", string="下级类别")
    child_process_ids = fields.One2many("roke.process", "category_id", string="归属工序")
    standard_item_ids = fields.One2many("roke.work.standard.item", "process_category_id", string="作业规范")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

