# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.modules.module import get_module_resource
import base64
from odoo.osv import expression
from odoo.exceptions import ValidationError



class RokeProductFeatures(models.Model):
    _name = "roke.product.features"
    _description = "产品特征"

    name = fields.Char(string='名称', required=True, translate=True)
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)


class RokeProductState(models.Model):
    _name = "roke.product.state"
    _description = "产品状态"

    _sql_constraints = [('product_state_name_unique', 'unique(name)',
                         '产品状态名称重复了!')]

    name = fields.Char(string='名称', required=True, translate=True)
    process = fields.Boolean(string='工序', default=True)


class RokeProduct(models.Model):
    _name = "roke.product"
    _inherit = ['mail.thread', 'image.mixin']
    _description = "产品信息"
    _order = "code desc"

    def _default_uom_id(self):
        return self.env["roke.uom"].search([], limit=1)

    def _default_category_id(self):
        return self.env["roke.product.category"].search([("name", "=", "默认")], limit=1)

    erp_id = fields.Char(string="ERP ID")
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default="保存自动生成编号")
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    name_repeat = fields.Boolean(string="* 产品名称重复", compute="_compute_name_repeat")
    category_id = fields.Many2one("roke.product.category", string="产品类别", tracking=True,
                                  default=_default_category_id)
    specification = fields.Char(string="规格")
    model = fields.Char(string="型号")
    routing_id = fields.Many2one("roke.routing", string="默认工艺路线")
    uom_id = fields.Many2one("roke.uom", string="单位", default=_default_uom_id)
    image_1920 = fields.Image(string="产品图片")
    note = fields.Text(string="备注")
    without_wo_process_ids = fields.Many2many("roke.process", string="无工单工序")
    product_features = fields.Many2one("roke.product.features", string="产品特征")
    standard_item_ids = fields.One2many("roke.work.standard.item", "product_id", string="作业规范")
    type = fields.Selection([("半成品", "半成品"), ("产成品", "产成品"), ("原材料", "原材料"), ("其他", "其他")],
                            string='产品属性')
    work_center_ids = fields.Many2many("roke.work.center", string="适用工作中心")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    standard_weight = fields.Float(string='标准重量')
    spoiled_rates = fields.Float(string="废品率(%)", default=0, digits='Production')
    unit_price = fields.Float(string="销售单价", default=1.0, digits='Account')
    direct_material_price = fields.Float(string="直接材料", default=0.0, digits='Account')
    direct_labor_price = fields.Float(string="直接人工", default=0.0, digits='Account')
    manufacturing_costs = fields.Float(string="制造费用", default=0.0, digits='Account')
    gross_profit_rate = fields.Float(string="毛利率", digits='Account', compute="_compute_gross_profit_rate")

    _sql_constraints = [
        ('check_spoiled_rates', 'CHECK (spoiled_rates >= 0 AND spoiled_rates <= 100)', '废品率必须在0到100之间！'),
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]


    @api.constrains('spoiled_rates')
    def _check_spoiled_rates(self):
        for product in self:
            if product.spoiled_rates < 0 or product.spoiled_rates > 100:
                raise ValidationError("废品率必须在0到100之间！")

    @api.depends('direct_material_price', 'direct_labor_price', 'unit_price')
    def _compute_gross_profit_rate(self):
        for rec in self:
            cost_account = rec.direct_material_price + rec.direct_labor_price
            gross_profit_rate = (rec.unit_price - cost_account) / rec.unit_price if rec.unit_price > 0 else 0
            rec.gross_profit_rate = gross_profit_rate

    @api.model
    def create(self, vals):
        if not vals.get('code') or vals.get('code') in ["保存自动生成编号", ""]:
            product_category = self.env["roke.product.category"]
            if vals.get('category_id'):
                product_category = product_category.browse(vals.get('category_id'))
            if product_category.sequence_id:
                code = product_category.sequence_id.next_by_id()
            else:
                code = self.env['ir.sequence'].next_by_code('roke.product.code')
            vals["code"] = code
        return super(RokeProduct, self).create(vals)

    @api.onchange("name")
    def _compute_name_repeat(self):
        for record in self:
            record.name_repeat = len(self.search([("name", "=", record.name)]).filtered(lambda p: p != record)) > 0

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        import_file = self.env.context.get("import_file", False)
        if import_file:  # 如果是导入时的搜索，不重写过滤方法
            return super(RokeProduct, self)._name_search(
                name, args, operator=operator, limit=limit, name_get_uid=name_get_uid
            )

        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            # 默认过滤条件，没有在设置中配置产品显示名称时使用
            # 当存在配置时，此domain将被覆盖
            if operator == 'ilike':
                domain = ['|', ('name', 'ilike', name), ('code', '=', name)]

                product_display_names = self.env['ir.config_parameter'].sudo().get_param(
                    'product.display.name.ids', default=[]
                )

                if product_display_names:  # 查询到了配置，按情况覆盖domain
                    product_display_name_ids = [int(rec) for rec in product_display_names if rec.isdigit()]
                    search_field = [
                        rec.code for rec in
                        self.env['roke.product.display.name.settings'].browse(product_display_name_ids)
                    ]

                    # 有配置情况下的默认过滤条件，在给定字符串中包含特殊'【' 和 '】'符号且解析过程中没有异常时使用
                    # 如果给定字符串中包含特殊'【' 和 '】'符号且解析过程中没有异常，此domain将被覆盖
                    domain = [('name', 'ilike', name)]
                    for rec in search_field:
                        domain.insert(0, '|')
                        domain.append((rec, 'ilike', name))
                    try:
                        if "【" in name and "】" in name:  # 处理搜索完整的显示名称时，比如：产品名称【产品型号】
                            rel_name = name.split("【")[0]
                            search_field_val = name.split("【")[1].split("】")[0]
                            domain = [('name', 'ilike', rel_name)]
                            for rec in search_field:
                                domain.insert(0, '|')
                                domain.append((rec, 'ilike', search_field_val))
                    except Exception as e:
                        pass

                is_ks_search = self.env.context.get("is_ks_search", False)
                if is_ks_search:  # 如果是 ks_list_search 时的搜索，需要再过滤准确的值，因为上面的过滤使用了"或条件"且display_name不可被过滤
                    result = super(RokeProduct, self).search(domain + args)
                    result_ids = result.filtered(lambda r: name in r.display_name)
                    if result_ids:
                        domain = [("id", "in", result_ids.ids)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

    def _product_name_get(self, product, product_display_name_list):
        """
        获取产品显示名称
        """
        name = product.name
        for rec in product_display_name_list:
            extra = getattr(product, rec)
            if rec == "product_features":
                extra = extra.display_name
            if extra:
                name += f"【{extra}】"
        return name

    def name_get(self):
        res = []
        product_display_names = self.env['ir.config_parameter'].sudo().get_param('product.display.name.ids', default=[])
        product_display_name_ids = [int(rec) for rec in product_display_names if rec.isdigit()]
        product_display_name_list = [rec.code for rec in
                                     self.env['roke.product.display.name.settings'].browse(product_display_name_ids)]
        for record in self:
            res.append((record.id, self._product_name_get(record, product_display_name_list)))
        return res
