# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError


class RokeProductCategory(models.Model):
    _name = "roke.product.category"
    _description = "产品类别"

    erp_id = fields.Char(string="ERP ID")
    name = fields.Char(string="名称", required=True, index=True)
    parent_id = fields.Many2one("roke.product.category", string="上级类别")
    child_ids = fields.One2many("roke.product.category", "parent_id", string="下级类别")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    index = fields.Char(string="序号标识")
    sequence_id = fields.Many2one("ir.sequence", string='序号规则')

    pc_id = fields.Char(string="ERP产品ID")

    def get_parent(self, ch_id, par_id):
        """
        递归上级类别是否为此类别
        """
        if not par_id:
            return False
        if par_id != ch_id:
            return self.get_parent(ch_id, par_id.parent_id)
        return True

    def _create_product_category_sequence(self, index, company_id=None):
        """
        创建产品类别序号
        :param index:
        :return:
        """
        return self.env['ir.sequence'].create({
            'name': "产品类别序号" + ' ' + index,
            'prefix': index,
            'padding': 5,
            'company_id': company_id or self.env.company.id,
        })

    @api.model
    def create(self, vals):
        if vals.get("index"):
            sequence = self._create_product_category_sequence(vals.get("index"), vals.get('company_id'))
            vals['sequence_id'] = sequence.id
        res = super(RokeProductCategory, self).create(vals)
        par = res.get_parent(res, res.parent_id)
        if par:
            raise UserError('关系错误，存在上级类别的为此类别的数据')
        return res

    def write(self, vals):
        if vals.get("index"):
            for record in self:
                if not record.sequence_id:
                    sequence = self._create_product_category_sequence(vals.get("index"), vals.get('company_id'))
                    vals['sequence_id'] = sequence.id
                else:
                    record.sequence_id.write({
                        "prefix": vals['index'],
                        "name": "产品类别序号" + ' ' + vals['index']
                    })
        res = super(RokeProductCategory, self).write(vals)
        for record in self:
            par = self.get_parent(record, record.parent_id)
            if par:
                raise UserError('关系错误，存在上级类别的为此类别的数据')
        return res

    def update_parent(self):
        """PS同步后自动获取上级类别"""
        for record in self:
            isParent = True
            index = 1
            while isParent:
                if index == len(record.erp_id):
                    isParent = False
                parent = self.search([("erp_id", '=', record.erp_id[:len(record.erp_id) - index])])
                if parent:
                    # 当前先以product.category为例
                    record.write({
                        'parent_id': parent.id,
                    })
                    isParent = False
                else:
                    index += 1





