# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.exceptions import ValidationError


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class RokeRouting(models.Model):
    _name = "roke.routing"
    _inherit = ['mail.thread']
    _description = "工艺路线"
    _order = "id desc"

    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.routing.code'))
    state = fields.Selection([("待确认", "待确认"), ("确认", "确认")], string="状态", default="待确认", index=True, tracking=True)
    internal_code = fields.Char(string="内部标识", tracking=True)
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    note = fields.Text(string="描述")
    line_ids = fields.One2many("roke.routing.line", "routing_id", string="工艺明细")
    process_description = fields.Char(string="工艺详情", compute="_compute_process_description", store=True, tracking=True)
    product_ids = fields.Many2many("roke.product", string="可用产品")
    template_id = fields.Many2one('roke.routing.template', string="工艺路线模板", tracking=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.routing.code')
        return super(RokeRouting, self).create(vals)

    def name_get(self):
        res = []
        for record in self:
            name = "%s（%s）" % (record.name, record.internal_code or record.code)
            res.append((record.id, name))
        return res

    @api.constrains("prepare_work_hours")
    def _constrains_prepare_work_hours(self):
        for v in self:
            if v.prepare_work_hours < 0:
                raise ValidationError("准备工时不能小于”0“")

    @api.depends("line_ids.process_id")
    def _compute_process_description(self):
        for record in self:
            process_list = []
            for line in record.line_ids:
                if line.process_id:
                    process_list.append(line.process_id.name)
            if process_list:
                record.process_description = " ▶ ".join(process_list)
            else:
                record.process_description = ""

    def make_confirm(self):
        """
        置为确认
        """
        self.write({"state": "确认"})

    def cancel_confirm(self):
        """
        取消确认
        """
        self.write({"state": "待确认"})

    def line_reference(self):
        """
        参照工艺路线规则创建工艺明细
        """
        add_process_lines = []
        for line in self.template_id.line_ids:
            add_process_lines.append((0, 0, {
                "sequence": line.sequence,
                "process_id": line.process_id.id
            }))
        wizard = self.env["roke.wizard.multi.add.process"].with_context(active_id=self.id).create({
            "line_ids": add_process_lines
        })
        wizard.confirm()

    def _get_standard_item_vals(self, standard_item):
        """
        作业规范
        """
        return {
            "sequence": standard_item.sequence,
            "title": standard_item.title,
            "name": standard_item.name,
            "image_1920": standard_item.image_1920
        }

    def get_routing_line_value(self, line):
        """
        获取工艺明细内容
        :param process:
        :return:
        """
        standard_items = []
        for standard_item in line.standard_item_ids:
            standard_items.append((0, 0, self._get_standard_item_vals(standard_item)))
        return {
            "sequence": line.sequence,
            "process_id": line.process_id.id,
            "routing_id": self.id,
            "standard_item_ids": standard_items
        }

    def multi_create_routing_line(self, origin_routing):
        """
        批量创建工艺明细
        :param new_routing: 新工艺路线
        :param origin_routing: 来源工艺路线
        :return:
        """
        self.line_ids.unlink()  # 删除已有工艺明细
        new_lines = self.env['roke.routing.line']
        for line in origin_routing.line_ids:
            new_lines.create(self.get_routing_line_value(line))

    def multi_add_routing_action(self):
        return {
            'name': '批量添加工序',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.wizard.multi.add.process',
            'view_mode': 'form',
            'target': 'new'
        }

    def reference_create_routing_line_action(self):
        # 参照已有工艺路线创建工艺明细
        return {
            'name': '参照已有工艺路线创建工艺明细',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_model': 'roke.wizard.reference.routing.line'
        }

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

    def copy(self):
        """
        复制工艺路线，明细参照当前工艺
        """
        res = super(RokeRouting, self).copy()
        self.env["roke.wizard.reference.routing.line"].with_context(active_id=res.id).create({
            "origin_routing_id": self.id
        }).confirm()
        return res


class RokeRoutingLine(models.Model):
    _name = "roke.routing.line"
    _description = "工艺明细"
    _order = "sequence"
    _rec_name = "process_id"

    sequence = fields.Integer(string="序号", required=True, default=1)
    routing_id = fields.Many2one("roke.routing", string="工艺路线", required=True, index=True, ondelete='cascade')
    process_id = fields.Many2one("roke.process", string="工序", required=True, index=True)
    multiple = fields.Float(string="生产倍数", default=1, help="", compute="_compute_multiple", store=True)
    work_qty = fields.Float(string="工序报工数", default=1, help="工序报工与成品关系，根据此关系生成对应的工单数量。",
                            digits='Production')
    finished_qty = fields.Float(string="最终成品数", default=1, help="工序报工与成品关系，根据此关系生成对应的工单数量。",
                                digits='Production')
    note = fields.Text(string="描述")
    standard_item_ids = fields.One2many("roke.work.standard.item", "routing_line_id", string="作业规范")
    standard_items_number = fields.Integer(compute='_compute_standard_items_number', string='作业规范数量')
    rated_working_hours = fields.Float(string="额定工时")
    prepare_work_hours = fields.Float(related="process_id.prepare_work_hours", string="准备工时")
    direct_labor_price = fields.Float(string="加工费", default=0.0, digits='Account')
    child_process_ids = fields.One2many("roke.process.child", "routing_line_id", string="子工序")
    child_process_number = fields.Integer(compute='_compute_child_process_number', string='子工序数量')

    _sql_constraints = [
        ('check_work_finished_qty', 'CHECK(multiple>0 AND finished_qty>0)', '报工数和成品数必须大于0')
    ]

    def _compute_standard_items_number(self):
        for record in self:
            record.standard_items_number = len(record.standard_item_ids)

    def action_standard_items_view(self):
        """
        查看作业规范
        :return:
        """
        return {
            'name': '%s 的作业规范' % self.display_name,
            'type': 'ir.actions.act_window',
            'res_model': 'roke.work.standard.item',
            'domain': [('routing_line_id', '=', self.id)],
            'view_mode': 'tree,form',
            "views": [
                [self.env.ref('roke_mes_base.view_roke_work_standard_item_editable_tree').id, "tree"],
                [self.env.ref('roke_mes_base.view_roke_work_standard_item_form').id, "form"]
            ],
            'target': 'current',
            'context': {
                'default_routing_line_id': self.id
            }
        }

    def _compute_child_process_number(self):
        for record in self:
            record.child_process_number = len(record.child_process_ids)

    def action_child_process_view(self):
        """
        查看子工序
        :return:
        """
        return {
            'name': '%s 的子工序' % self.display_name,
            'type': 'ir.actions.act_window',
            'res_model': 'roke.process.child',
            'domain': [('routing_line_id', '=', self.id)],
            'view_mode': 'tree,form',
            'target': 'current',
            'context': {
                'default_routing_line_id': self.id
            }
        }

    @api.depends("work_qty", "finished_qty")
    def _compute_multiple(self):
        for record in self:
            record.multiple = record.work_qty / record.finished_qty

    def refresh_rated_working_hours(self):
        """
        刷新额定工时，主工序额定工时等于子工序额定工时合计;存在子工序时，主工序额定工时禁止修改
        :return:
        """
        self.ensure_one()
        if self.child_process_ids:
            self.rated_working_hours = sum(self.child_process_ids.mapped("child_rated_wh"))
