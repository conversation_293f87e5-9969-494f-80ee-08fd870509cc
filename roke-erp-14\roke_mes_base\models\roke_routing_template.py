# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api


class RokeRoutingConfig(models.Model):
    _name = "roke.routing.template"
    _description = "工艺路线模板"
    _inherit = ["mail.thread"]

    name = fields.Char(string="名称", tracking=True)
    active = fields.Boolean(string="有效", default=True, tracking=True)
    remark = fields.Text(string="备注", tracking=True)
    line_ids = fields.One2many("roke.routing.template.line", "template_id", string="模板明细")
    process_description = fields.Char(string="明细", compute="_compute_process_description", store=True, tracking=True)

    @api.depends("line_ids.process_id")
    def _compute_process_description(self):
        for record in self:
            process_list = []
            for line in record.line_ids:
                if line.process_id:
                    process_list.append(line.process_id.name)
            if process_list:
                record.process_description = " ▶ ".join(process_list)
            else:
                record.process_description = ""

    def multi_add_routing_action(self):
        form_id = self.env.ref("roke_mes_base.view_roke_wizard_multi_add_process_form").id
        return {
            "name": "批量添加工序",
            "type": "ir.actions.act_window",
            "view_mode": "form",
            "views": [(form_id, "form")],
            "target": "new",
            "res_model": "roke.wizard.multi.add.process",
            "context": {},
        }


class RokeRoutingTemplateLine(models.Model):
    _name = "roke.routing.template.line"
    _description = "工艺路线规则明细"
    _order = "sequence"

    template_id = fields.Many2one("roke.routing.template", string="工艺模板")
    sequence = fields.Integer(string="序号")
    process_id = fields.Many2one("roke.process", string="工序")
    remark = fields.Text(string="备注")




