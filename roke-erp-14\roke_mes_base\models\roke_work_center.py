# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.osv import expression


class RokeWorkCenter(models.Model):
    _name = "roke.work.center"
    _inherit = ['mail.thread']
    _description = "工作中心"
    _order = "name"

    erp_id = fields.Char(string="ERP ID")
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.work.center.code'))
    active = fields.Boolean(string="有效的", default=True)
    type_id = fields.Many2one("roke.work.center.type", string="工作台类别", tracking=True)
    state = fields.Selection([("启用", "启用"), ("停用", "停用"), ("异常", "异常")], required=True, string="状态", tracking=True, default="启用")
    parent_id = fields.Many2one("roke.work.center", string="上级工作中心", tracking=True)
    child_ids = fields.One2many("roke.work.center", "parent_id", string="下级工作中心")
    child_qty = fields.Integer(string="下级工作中心数量", compute="_compute_child_qty")
    note = fields.Text(string="备注")
    standard_item_ids = fields.One2many("roke.work.standard.item", "work_center_id", string="作业规范")
    skill_level_ids = fields.Many2many("roke.skill.level", string="技能要求")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def name_get(self):
        res = []
        for record in self:
            name = "%s(%s)" % (record.name, record.code)
            res.append((record.id, name))
        return res

    def _compute_child_qty(self):
        for record in self:
            record.child_qty = len(record.child_ids)

    def show_child_action(self):
        # 查看下级工作中心
        return {
            'name': '下级工作中心',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', self.child_ids.ids)],
            'res_model': 'roke.work.center',
            'context': {
                'create': False
            }
        }

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)
