# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from ast import literal_eval
from odoo import models, fields, api, _
import time
import datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError


class RokeMesWorkCenterType(models.Model):
    _name = "roke.work.center.type"
    _description = "工作中心类别"
    _order = "id"

    name = fields.Char(string="名称")
    parent_id = fields.Many2one("roke.work.center.type", string="上级类别")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
