# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.osv import expression
from odoo.modules.module import get_module_resource
import base64
import qrcode
from io import BytesIO

class RokeWorkTeam(models.Model):
    _name = "roke.work.team"
    _inherit = ['mail.thread']
    _description = "班组信息"
    _order = "id desc"

    @api.model
    def _default_image(self):
        image_path = get_module_resource('roke_mes_crm', 'static/src/img', 'default_image.png')
        return base64.b64encode(open(image_path, 'rb').read())

    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.work.team.code'))
    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    manager_id = fields.Many2one("roke.employee", string="班组长", tracking=True)
    employee_ids = fields.One2many("roke.employee", "team_id", string="班组成员")
    note = fields.Text(string="备注")
    parent_id = fields.Many2one("roke.work.team",string="上级班组")
    child_team_ids = fields.One2many("roke.work.team", "parent_id", string="下级班组")
    restrict_work_date = fields.Boolean(string="报工日期限制", help="报工时是否限制提交的报工时间")
    last_days = fields.Integer(string="前几天可报", default=2, help="设置几天前的工作不可再报工")
    last_time = fields.Float(string="前几天可报时间", default=0)
    after_days = fields.Integer(string="后几天可报", default=0, help="设置几天后的工作不可报工")
    after_time = fields.Float(string="后几天可报时间", default=24)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    product_category_ids = fields.Many2many('roke.product.category', string='产品类别')
    team_qr_code = fields.Image(string="班组二维码")
    employee_line_ids = fields.One2many("roke.employee.line", "line_id", string="班组成员")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def check_team_parent(self, team_parent):
        """校验上级班组是否循环设置"""
        parent_list = [team_parent.id]
        while team_parent.parent_id:
            parent_list.append(team_parent.parent_id.id)
            team_parent = team_parent.parent_id
        if self.ids[0] in parent_list:
            return False
        else:
            return True

    @api.onchange("parent_id")
    def _onchange_parent_id(self):
        if self.ids and self.parent_id and not self.check_team_parent(self.parent_id):
            return {"value": {"parent_id": False},
                    "warning": {"title": "错误", "message": "禁止循环设置上级班组！"}
                    }

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

    def action_create_qr(self):
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        # 添加数据
        web_base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        url = web_base_url + "/roke/get_team_qr" + "?team_id=" + str(self.id)
        qr.add_data(url)
        qr.make(fit=True)

        # 创建二维码图片
        img = qr.make_image(fill='black', back_color='white')

        # 将图片转换为base64格式
        buffered = BytesIO()
        img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()
        self.team_qr_code = img_str

class RokeEmployeeLine(models.Model):
    _name = "roke.employee.line"
    _description = "班组人员信息"

    line_id = fields.Many2one("roke.work.team", string="班组", required=True, ondelete='cascade')
    employee_id = fields.Many2one("roke.employee", string="班组成员")
    code = fields.Char(related="employee_id.code", string="编号")
    job_number = fields.Char(related="employee_id.job_number", string="工号")
    phone = fields.Char(related="employee_id.phone", string="电话")
    team_weighted = fields.Float(related="employee_id.team_weighted", string="班组报工权重")
    user_id = fields.Many2one(related="employee_id.user_id", string="系统用户")
    position_id = fields.Many2one(related="employee_id.position_id", string="职位")
    gender = fields.Selection(related="employee_id.gender", string='性别')
    is_on_job = fields.Selection(related="employee_id.is_on_job", string='在职情况')
    id_number = fields.Char(related="employee_id.id_number", string="身份证号")
    age = fields.Char(related="employee_id.age", string="年龄")
    registered_residence = fields.Char(related="employee_id.registered_residence", string="户口所在地")
    skill_level_id = fields.Many2one(related="employee_id.skill_level_id", string='技能等级', index=True)
    note = fields.Text(related="employee_id.note", string="备注")
    department_id = fields.Many2one(related="employee_id.department_id", string='部门')

    @api.model
    def create(self, vals):
        res = super(RokeEmployeeLine, self).create(vals)
        res.employee_id.write({'team_id': res.line_id.id})
        return res
