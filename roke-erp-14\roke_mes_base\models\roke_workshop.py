# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.osv import expression


class RokeWorkshop(models.Model):
    _name = "roke.workshop"
    _inherit = ['mail.thread']
    _description = "车间产线"
    _order = "name"

    name = fields.Char(string="名称", required=True, index=True, tracking=True)
    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.workshop.code'))
    manager_id = fields.Many2one("roke.employee", string="车间组长", tracking=True)
    parent_id = fields.Many2one("roke.workshop", string="上级车间", tracking=True)
    child_ids = fields.One2many("roke.workshop", "parent_id", string="下级车间")
    child_qty = fields.Integer(string="下级车间数量", compute="_compute_child_qty")
    note = fields.Text(string="备注")
    active = fields.Boolean(string="有效的", default=True)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    employee_ids = fields.One2many("roke.workshop.line", "line_id", string="产线成员")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code, company_id)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def name_get(self):
        res = []
        for record in self:
            name = "%s(%s)" % (record.name, record.code)
            res.append((record.id, name))
        return res

    def _compute_child_qty(self):
        for record in self:
            record.child_qty = len(record.child_ids)

    def show_child_action(self):
        # 查看下级工作中心
        return {
            'name': '下级车间产线',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', self.child_ids.ids)],
            'res_model': 'roke.workshop',
            'context': {
                'create': False
            }
        }

    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        args = args or []
        if operator == 'ilike' and not (name or '').strip():
            domain = []
        else:
            if operator == "ilike":
                domain = ['|', ('name', 'ilike', name), ('code', 'ilike', name)]
            else:
                domain = [('name', operator, name)]
        return self._search(expression.AND([domain, args]), limit=limit, access_rights_uid=name_get_uid)

class RokeWorkshopLine(models.Model):
    _name = "roke.workshop.line"
    _description = "产线成员"
    _order = "employee_id"

    line_id = fields.Many2one("roke.workshop", string="车间产线")
    employee_id = fields.Many2one("roke.employee", string="成员")
    job_number = fields.Char(related="employee_id.job_number", string="工号")
    phone = fields.Char(related="employee_id.phone", string="电话")
    team_id = fields.Many2one(related="employee_id.team_id", string="班组")
    user_id = fields.Many2one(related="employee_id.user_id", string="系统用户")
    position_id = fields.Many2one(related="employee_id.position_id", string="职位")
    gender = fields.Selection(related="employee_id.gender", string='性别')
    is_on_job = fields.Selection(related="employee_id.is_on_job", string='在职情况')