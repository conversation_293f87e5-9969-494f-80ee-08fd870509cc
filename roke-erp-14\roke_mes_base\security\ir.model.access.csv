id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_roke_work_center_type_system,roke_work_center_type_system,model_roke_work_center_type,base.group_system,1,1,1,1
access_roke_work_center_system,roke_work_center_system,model_roke_work_center,base.group_system,1,1,1,1
access_roke_workshop_system,roke_workshop_system,model_roke_workshop,base.group_system,1,1,1,1
access_roke_workshop_line_system,roke_workshop_line_system,model_roke_workshop_line,base.group_system,1,1,1,1
access_roke_partner_tag_system,roke_partner_tag_system,model_roke_partner_tag,base.group_system,1,1,1,1
access_roke_partner_system,roke_partner_system,model_roke_partner,base.group_system,1,1,1,1
access_roke_work_team_system,roke_work_team_system,model_roke_work_team,base.group_system,1,1,1,1
access_roke_employee_system,roke_employee_system,model_roke_employee,base.group_system,1,1,1,1
access_roke_product_system,roke_product_system,model_roke_product,base.group_system,1,1,1,1
access_roke_product_category_system,roke_product_category_system,model_roke_product_category,base.group_system,1,1,1,1
access_roke_process_category_system,roke_process_category_system,model_roke_process_category,base.group_system,1,1,1,1
access_roke_uom_system,roke_uom_system,model_roke_uom,base.group_system,1,1,1,1
access_roke_process_system,roke_process_system,model_roke_process,base.group_system,1,1,1,1
access_roke_process_child_system,roke_process_child_system,model_roke_process_child,base.group_system,1,1,1,1
access_roke_routing_system,roke_routing_system,model_roke_routing,base.group_system,1,1,1,1
access_roke_routing_line_system,roke.routing.line.system,model_roke_routing_line,base.group_system,1,1,1,1
access_roke_routing_template_user,roke_routing_template_user,model_roke_routing_template,base.group_user,1,0,0,0
access_roke_routing_template_line_user,roke_routing_template_line_user,model_roke_routing_template_line,base.group_user,1,0,0,0
access_roke_routing_template_system,roke_routing_template_system,model_roke_routing_template,base.group_system,1,1,1,1
access_roke_routing_template_line_system,roke_routing_template_line_system,model_roke_routing_template_line,base.group_system,1,1,1,1
access_roke_finished_warehouse_system,roke_finished_warehouse_system,model_roke_finished_warehouse,base.group_system,1,1,1,1
access_roke_work_standard_item_system,roke_work_standard_item_system,model_roke_work_standard_item,base.group_system,1,1,1,1

access_roke_wizard_multi_add_process_system,roke.wizard.multi.add.process.system,model_roke_wizard_multi_add_process,base.group_system,1,1,1,1
access_roke_wizard_multi_add_process_line_system,roke.wizard.multi.add.process.line.system,model_roke_wizard_multi_add_process_line,base.group_system,1,1,1,1
access_roke_multi_add_child_process_user,roke_multi_add_child_process_user,model_roke_multi_add_child_process,base.group_user,1,1,1,1
access_roke_multi_add_child_process_line_user,roke_multi_add_child_process_line_user,model_roke_multi_add_child_process_line,base.group_user,1,1,1,1
access_roke_wizard_reference_routing_line_system,roke.wizard.reference.routing.line.system,model_roke_wizard_reference_routing_line,base.group_system,1,1,1,1
access_export_excel_template_system,export_excel_template_system,model_export_excel_template,base.group_system,1,1,1,1

access_roke_position_dict_system,roke_position_dict_system,model_roke_position_dict,base.group_system,1,1,1,1
access_roke_product_features_system,roke_product_features_system,model_roke_product_features,base.group_system,1,1,1,1

access_roke_classes_system,roke_classes_system,model_roke_classes,base.group_system,1,1,1,1
access_roke_department_base,roke.department,model_roke_department,base.group_system,1,1,1,1
access_roke_print_log,roke_print_log,model_roke_print_log,base.group_system,1,1,1,1
access_roke_product_display_name_settings,roke_product_display_name_settings,model_roke_product_display_name_settings,base.group_system,1,1,1,1
access_roke_mes_employee_import_wizard,roke_mes_employee_import_wizard,model_roke_mes_employee_import_wizard,base.group_system,1,1,1,1
access_roke_product_state,roke_product_state,model_roke_product_state,base.group_system,1,1,1,1
access_roke_skill_level_system,roke_skill_level_system,model_roke_skill_level,base.group_system,1,1,1,1

access_roke_employee_line,roke.employee.line,model_roke_employee_line,base.group_system,1,1,1,1
access_roke_department_line,roke.department.line,model_roke_department_line,base.group_system,1,1,1,1

access_roke_mes_politics_region_system,roke_mes_politics_region_system,model_roke_mes_politics_region,base.group_system,1,1,1,1
access_routing_import_wizard_system,routing_import_wizard_system,model_routing_import_wizard,base.group_user,1,1,1,1
access_roke_work_shift,roke_work_shift,model_roke_work_shift,base.group_user,1,1,1,1

access_roke_common_data_param_user,roke_common_data_param,model_roke_common_data_param,base.group_user,1,1,1,1
access_roke_common_data_param_line_user,roke_common_data_param_line,model_roke_common_data_param_line,base.group_user,1,1,1,1