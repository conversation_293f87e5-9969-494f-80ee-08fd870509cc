<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!--多公司-->
        <!--班次-->
        <record id="roke_classes_multi_company_rule" model="ir.rule">
            <field name="name">班次多公司记录规则</field>
            <field name="model_id" ref="model_roke_classes"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--部门-->
        <record id="roke_department_multi_company_rule" model="ir.rule">
            <field name="name">部门多公司记录规则</field>
            <field name="model_id" ref="model_roke_department"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--职位-->
        <record id="roke_position_dict_multi_company_rule" model="ir.rule">
            <field name="name">职位多公司记录规则</field>
            <field name="model_id" ref="model_roke_position_dict"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--人员-->
        <record id="roke_employee_multi_company_rule" model="ir.rule">
            <field name="name">人员多公司记录规则</field>
            <field name="model_id" ref="model_roke_employee"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--成品仓库-->
        <record id="roke_finished_warehouse_multi_company_rule" model="ir.rule">
            <field name="name">成品仓库多公司记录规则</field>
            <field name="model_id" ref="model_roke_finished_warehouse"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--业务伙伴标签-->
        <record id="roke_partner_tag_multi_company_rule" model="ir.rule">
            <field name="name">业务伙伴标签多公司记录规则</field>
            <field name="model_id" ref="model_roke_partner_tag"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--业务伙伴-->
        <record id="roke_partner_multi_company_rule" model="ir.rule">
            <field name="name">业务伙伴多公司记录规则</field>
            <field name="model_id" ref="model_roke_partner"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--工序-->
        <record id="roke_process_multi_company_rule" model="ir.rule">
            <field name="name">工序多公司记录规则</field>
            <field name="model_id" ref="model_roke_process"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--工序类别-->
        <record id="roke_process_category_multi_company_rule" model="ir.rule">
            <field name="name">工序类别多公司记录规则</field>
            <field name="model_id" ref="model_roke_process_category"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--产品特征-->
        <record id="roke_product_features_multi_company_rule" model="ir.rule">
            <field name="name">产品特征多公司记录规则</field>
            <field name="model_id" ref="model_roke_product_features"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--产品-->
        <record id="roke_product_multi_company_rule" model="ir.rule">
            <field name="name">产品多公司记录规则</field>
            <field name="model_id" ref="model_roke_product"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--产品类别-->
        <record id="roke_product_category_multi_company_rule" model="ir.rule">
            <field name="name">产品类别多公司记录规则</field>
            <field name="model_id" ref="model_roke_product_category"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--工艺路线-->
        <record id="roke_routing_multi_company_rule" model="ir.rule">
            <field name="name">工艺路线多公司记录规则</field>
            <field name="model_id" ref="model_roke_routing"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--工作中心-->
        <record id="roke_work_center_multi_company_rule" model="ir.rule">
            <field name="name">工作中心多公司记录规则</field>
            <field name="model_id" ref="model_roke_work_center"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--工作中心类型-->
        <record id="roke_work_center_type_multi_company_rule" model="ir.rule">
            <field name="name">工作中心类型多公司记录规则</field>
            <field name="model_id" ref="model_roke_work_center_type"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--作业规范-->
        <record id="roke_work_standard_item_multi_company_rule" model="ir.rule">
            <field name="name">作业规范多公司记录规则</field>
            <field name="model_id" ref="model_roke_work_standard_item"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--班组-->
        <record id="roke_work_team_multi_company_rule" model="ir.rule">
            <field name="name">班组多公司记录规则</field>
            <field name="model_id" ref="model_roke_work_team"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        <!--行政区域-->
        <record id="roke_mes_politics_region_company_rule" model="ir.rule">
            <field name="name">行政区域多公司记录规则</field>
            <field name="model_id" ref="model_roke_mes_politics_region"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>