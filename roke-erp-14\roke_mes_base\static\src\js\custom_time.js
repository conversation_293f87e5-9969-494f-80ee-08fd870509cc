odoo.define('roke_mes_base.CustomTimeField', function (require) {
    "use strict";

    var AbstractField = require('web.AbstractField');
    var fieldRegistry = require('web.field_registry');

    var CustomTimeField = AbstractField.extend({
        template: 'roke_mes_base.TimeWidgetTemplate',
        events: {
        'change input': '_onInputChanged',
            },

        /**
         * 初始化时设置初始值
         */
        init: function () {
            this._super.apply(this, arguments);
            this.value = this.value || '';
        },

        /**
         * 在 start 阶段将值同步到 input 元素
         */
        start: function () {
             this._super.apply(this, arguments);
             // 强制根据当前 mode 渲染一次
            if (this.mode === 'readonly') {
                this._renderReadonly();
            } else {
                this._renderEdit();
            }
        },

        /**
         * 输入框变化时更新字段值
         */
        _onInputChanged: function (ev) {
            var value = ev.target.value;
            this._setValue(value); // 更新 Odoo 字段值
        },

        /**
         * 可编辑模式下渲染 input 控件
         */
         _renderEdit: function () {
            this.$el.empty();
            this.$input = $('<input type="time" step="1">').val(this._formatTime(this.value));
            this.$el.append(this.$input);
    },

    _renderReadonly: function () {
         this.$el.empty();
         this.$input = $('<input type="text" style="border:0px" >').val(this._formatTime(this.value));
         this.$el.append(this.$input);

    },
        /**
         * 格式化时间为 HH:mm:ss
         */
        _formatTime: function (value) {
            if (!value) return '';
            if (value.includes(' ')) {
                value = value.split(' ')[1]; // 提取时间部分
            }
            let parts = value.split(':');
            if (parts.length === 2) {
                return `${parts[0]}:${parts[1]}:00`; // 补全秒数
            }
            return value;
        },
    });

    fieldRegistry.add('custom_time', CustomTimeField);

    return CustomTimeField;
});
