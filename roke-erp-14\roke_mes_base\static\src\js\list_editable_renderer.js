odoo.define('extends_views.EditableListRenderer', function (require) {
    "use strict";

    const ListRenderer = require('web.ListRenderer');
    const core = require('web.core');
    const { WidgetAdapterMixin } = require('web.OwlCompatibility');


    ListRenderer.include({

        /**
         * Handles the keyboard navigation according to events triggered by field
         * widgets.
         * - previous: move to the first activable cell on the left if any, if not
         *          move to the rightmost activable cell on the row above.
         * - next: move to the first activable cell on the right if any, if not move
         *          to the leftmost activable cell on the row below.
         * - next_line: move to leftmost activable cell on the row below.
         *
         * Note: moving to a line below if on the last line or moving to a line
         * above if on the first line automatically creates a new line.
         *
         * @private
         * @param {OdooEvent} ev
         */
        _onNavigationMove: function (ev) {
            var self = this;
            // Don't stop the propagation when navigating up while not editing any row
            if (this.currentRow === null && ev.data.direction === 'up') {
                return;
            }
            ev.stopPropagation(); // stop the event, the action is done by this renderer
            if (ev.data.originalEvent && ['next', 'previous'].includes(ev.data.direction)) {
                ev.data.originalEvent.preventDefault();
                ev.data.originalEvent.stopPropagation();
            }
            switch (ev.data.direction) {
                case 'previous':
                    if (this.currentFieldIndex > 0) {
                        this._selectCell(this.currentRow, this.currentFieldIndex - 1, { inc: -1, wrap: false })
                            .guardedCatch(this._moveToPreviousLine.bind(this));
                    } else {
                        this._moveToPreviousLine();
                    }
                    break;
                case 'next':
                    if (this.currentFieldIndex + 1 < this.columns.length) {
                        this._selectCell(this.currentRow, this.currentFieldIndex + 1, { wrap: false })
                            .guardedCatch(this._moveToNextLine.bind(this));
                    } else {
                        this._moveToNextLine();
                    }
                    break;

                // 修改：上下左右 - 开始
                case 'left': // 左
                    if (this.currentFieldIndex > 0) {
                        this._selectCell(this.currentRow, this.currentFieldIndex - 1, { inc: -1, wrap: false })
                            .guardedCatch(this._moveToPreviousLine.bind(this, { arrow: true, default: 'max' }));
                    } else {
                        this._moveToPreviousLine({ arrow: true, default: 'max' });
                    }
                    break;
                case 'right': // 右
                    if (this.currentFieldIndex + 1 < this.columns.length) {
                        this._selectCell(this.currentRow, this.currentFieldIndex + 1, { wrap: false })
                            .guardedCatch(this._moveToNextLine.bind(this, { arrow: true, default: 'min' }));
                    } else {
                        this._moveToNextLine({ arrow: true, default: 'min' });
                    }
                    break;
                case 'down': // 下
                    if (!this.editable && this.selection.length === 1 && this._getRecordID(this.currentRow) === ev.target.dataPointID) {
                        this.unselectRow();
                    } else {
                        this._moveToNextLine({ arrow: true });
                    }
                    break;
                case 'up': // 上
                    if (!this.editable && this.selection.length === 1 && this._getRecordID(this.currentRow) === ev.target.dataPointID) {
                        this.unselectRow();
                    } else {
                        this._moveToPreviousLine({ arrow: true });
                    }
                    break;
                // 修改：上下左右 - 结束

                case 'next_line':
                    // If the list is readonly and the current is the only record editable, we unselect the line
                    if (!this.editable && this.selection.length === 1 &&
                        this._getRecordID(this.currentRow) === ev.target.dataPointID) {
                        this.unselectRow();
                    } else {
                        this._moveToNextLine({ forceCreate: true });
                    }
                    break;
                case 'cancel':
                    // stop the original event (typically an ESCAPE keydown), to
                    // prevent from closing the potential dialog containing this list
                    // also auto-focus the 1st control, if any.
                    ev.data.originalEvent.stopPropagation();
                    var rowIndex = this.currentRow;
                    var cellIndex = this.currentFieldIndex + 1;
                    this.trigger_up('discard_changes', {
                        recordID: ev.target.dataPointID,
                        onSuccess: function () {
                            self._enableRecordSelectors();
                            var recordId = self._getRecordID(rowIndex);
                            if (recordId) {
                                var correspondingRow = self._getRow(recordId);
                                correspondingRow.children().eq(cellIndex).focus();
                            } else if (self.currentGroupId) {
                                self.$('a[data-group-id="' + self.currentGroupId + '"]').focus();
                            } else {
                                self.$('.o_field_x2many_list_row_add a:first').focus(); // FIXME
                            }
                        }
                    });
                    break;
            }
        },

        /**
         * Moves the focus to the nearest editable row before or after the current one.
         * If we arrive at the end of the list (or of a group in the grouped case) and the list
         * is editable="bottom", we create a new record, otherwise, we move the
         * cursor to the first row (of the next group in the grouped case).
         *
         * @private
         * @param {number} next whether to move to the next or previous row
         * @param {Object} [options]
         * @param {boolean} [options.forceCreate=false] typically set to true when
         *   navigating with ENTER ; in this case, if the next row is the 'Add a
         *   row' one, always create a new record (never skip it, like TAB does
         *   under some conditions)
         */
        _moveToSideLine: function (next, options) {
            let self = this
            options = options || {};
            const recordID = this._getRecordID(this.currentRow);
            this.commitChanges(recordID).then(() => {
                const record = this._getRecord(recordID);
                const multiEdit = this.isInMultipleRecordEdition(recordID);
                if (!multiEdit) {
                    const fieldNames = this.canBeSaved(recordID);
                    if (fieldNames.length && (record.isDirty() || options.forceCreate)) {
                        // the current row is invalid, we only leave it if it is not dirty
                        // (we didn't make any change on this row, which is a new one) and
                        // we are navigating with TAB (forceCreate=false)
                        return;
                    }
                }
                // compute the index of the next (record) row to select, if any
                const side = next ? 'first' : 'last';
                const borderRowIndex = this._getBorderRow(side).prop('rowIndex');
                // 修改：上下左右 - 开始
                // const cellIndex = next ? 0 : this.allFieldWidgets[recordID].length - 1;
                let cellIndex = this.currentFieldIndex;
                if (options && options.default) {
                    cellIndex = options.default === "min" ? 0 : this.allFieldWidgets[recordID].length - 1;
                }
                // 修改：上下左右 - 结束
                const cellOptions = { inc: next ? 1 : -1, force: true };
                const $currentRow = this._getRow(recordID);
                const $nextRow = this._getNearestEditableRow($currentRow, next);
                let nextRowIndex = null;
                let groupId;
                if (!this.isGrouped) {
                    // 修改：上下左右 - 开始
                    if ($nextRow.length) {
                        nextRowIndex = $nextRow.prop('rowIndex') - 1;

                        // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块 和 增加了Tree视图表头固定功能 而增加的补丁
                        if (!self.parent_view_type) {
                            // 如果没有父视图，说明是Tree视图。此时因固定表头，多复制出来了一个header
                            nextRowIndex = $nextRow.prop('rowIndex');
                            if (self.xc_seach_box) {
                                // nextRowIndex -= 4; // 2个header * (表头一行 + 搜索栏一行)
                                nextRowIndex -= 2; // 1个header * (表头一行 + 搜索栏一行)
                            } else {
                                if (self.ks_module) {
                                    // nextRowIndex -= 2; // 2个header * (表头一行) 
                                    nextRowIndex -= 1; // 1个header * (表头一行) 
                                }else{
                                    nextRowIndex -= 1; // 1个header * (表头一行) 
                                }
                            }
                        }
                    } else if (this.editable) {
                        if (options && options.arrow) {
                            nextRowIndex = borderRowIndex - 1;

                            // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块 和 增加了Tree视图表头固定功能 而增加的补丁
                            if (!self.parent_view_type) {
                                // 如果没有父视图，说明是Tree视图。此时因固定表头，多复制出来了一个header
                                if (self.xc_seach_box) {
                                    // nextRowIndex = borderRowIndex - 4; // 2个header * (表头一行 + 搜索栏一行)
                                    nextRowIndex = borderRowIndex - 2; // 1个header * (表头一行 + 搜索栏一行)
                                } else {
                                    // nextRowIndex = borderRowIndex - 2; // 2个header * (表头一行) 
                                    nextRowIndex = borderRowIndex - 1; // 1个header * (表头一行) 
                                    if (self.ks_module) {
                                        // nextRowIndex = borderRowIndex - 2; // 2个header * (表头一行) 
                                        nextRowIndex = borderRowIndex - 1; // 1个header * (表头一行) 
                                    } else {
                                        nextRowIndex = borderRowIndex - 1; // 1个header * (表头一行)  
                                    }
                                }
                            }
                        }
                    } else if (!this.editable) {
                        nextRowIndex = borderRowIndex - 1;

                        // if (self.xc_seach_box) {
                        //     nextRowIndex = self.parent_view_type ? borderRowIndex - 1 : borderRowIndex - 2;
                        // }

                        // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块 和 增加了Tree视图表头固定功能 而增加的补丁
                        if (!self.parent_view_type) {
                            // 如果没有父视图，说明是Tree视图。此时因固定表头，多复制出来了一个header
                            if (self.xc_seach_box) {
                                // nextRowIndex = borderRowIndex - 4; // 2个header * (表头一行 + 搜索栏一行)
                                nextRowIndex = borderRowIndex - 2; // 1个header * (表头一行 + 搜索栏一行)
                            } else {
                                if (self.ks_module) {
                                    // nextRowIndex = borderRowIndex - 2; // 2个header * (表头一行) 
                                    nextRowIndex = borderRowIndex - 1; // 1个header * (表头一行) 
                                } else {
                                    nextRowIndex = borderRowIndex - 1; // 1个header * (表头一行)  
                                }
                            }
                        }
                    } else if (!options.forceCreate && !record.isDirty()) {
                        this.trigger_up('discard_changes', {
                            recordID: recordID,
                            onSuccess: this.trigger_up.bind(this, 'activate_next_widget', { side: side }),
                        });
                        return;
                    }
                    // 修改：上下左右 - 结束
                } else {
                    // grouped case
                    var $directNextRow = $currentRow.next();
                    if (next && this.editable === "bottom" && $directNextRow.hasClass('o_add_record_row')) {
                        // the next row is the 'Add a line' row (i.e. the current one is the last record
                        // row of the group)
                        if (options.forceCreate || record.isDirty()) {
                            // if we modified the current record, add a row to create a new record
                            groupId = $directNextRow.data('group-id');
                        } else {
                            // if we didn't change anything to the current line (e.g. we pressed TAB on
                            // each cell without modifying/entering any data), we discard that line (if
                            // it was a new one) and move to the first record of the next group
                            nextRowIndex = ($nextRow.prop('rowIndex') - 1) || null;
                            // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块增加的补丁
                            if (self.xc_seach_box && !self.parent_view_type) {
                                // nextRowIndex = ($nextRow.prop('rowIndex') - 2 - 2) || null;
                                nextRowIndex = ($nextRow.prop('rowIndex') - 1 - 2) || null;
                            } else {
                                nextRowIndex = ($nextRow.prop('rowIndex') - 1 - 2) || null;
                            }
                            this.trigger_up('discard_changes', {
                                recordID: recordID,
                                onSuccess: () => {
                                    if (nextRowIndex !== null) {
                                        if (!record.res_id) {
                                            // the current record was a new one, so we decrement
                                            // nextRowIndex as that row has been removed meanwhile
                                            nextRowIndex--;
                                        }
                                        this._selectCell(nextRowIndex, cellIndex, cellOptions);
                                    } else {
                                        // we were in the last group, so go back to the top
                                        this._selectCell(borderRowIndex, cellIndex, cellOptions);
                                    }
                                },
                            });
                            return;
                        }
                    } else {
                        // there is no 'Add a line' row (i.e. the create feature is disabled), or the
                        // list is editable="top", we focus the first record of the next group if any,
                        // or we go back to the top of the list
                        // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块增加的补丁
                        if (self.xc_seach_box && !self.parent_view_type) {
                            // nextRowIndex = $nextRow.length ? ($nextRow.prop('rowIndex') - 2 - 2) : borderRowIndex - 2 - 2;
                            nextRowIndex = $nextRow.length ? ($nextRow.prop('rowIndex') - 1 - 2) : borderRowIndex - 1 - 2;
                        } else {
                            nextRowIndex = $nextRow.length ? ($nextRow.prop('rowIndex') - 1 - 2) : borderRowIndex - 1 - 2;
                        }
                    }
                }

                // if there is a (record) row to select, select it, otherwise, add a new record (in the
                // correct group, if the view is grouped)
                if (nextRowIndex !== null) {
                    // cellOptions.force = true;
                    this._selectCell(nextRowIndex, cellIndex, cellOptions);
                } else if (this.editable) {
                    // if for some reason (e.g. create feature is disabled) we can't add a new
                    // record, select the first record row
                    this.unselectRow().then(this.trigger_up.bind(this, 'add_record', {
                        groupId: groupId,
                        onFail: this._selectCell.bind(this, borderRowIndex, cellIndex, cellOptions),
                    }));
                }
            });
        },


        _onCellClick: function (event) {
            // The special_click property explicitely allow events to bubble all
            // the way up to bootstrap's level rather than being stopped earlier.
            var $td = $(event.currentTarget);
            var $tr = $td.parent();

            var rowIndex = $tr.prop('rowIndex') - 1;

            // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块 和 增加了Tree视图表头固定功能 而增加的补丁
            if (!this.parent_view_type) {
                // 如果没有父视图，说明是Tree视图。此时因固定表头，多复制出来了一个header
                rowIndex = $tr.prop('rowIndex');
                if (this.xc_seach_box) {
                    // rowIndex -= 4; // 2个header * (表头一行 + 搜索栏一行)
                    rowIndex -= 2; // 1个header * (表头一行 + 搜索栏一行)
                } else {
                    if (this.ks_module) {
                        // rowIndex -= 2; // 2个header * (表头一行)
                        rowIndex -= 1; // 1个header * (表头一行)
                    } else {
                        rowIndex -= 1; // 1个header * (表头一行)
                    }
                }
            }
            if (!this._isRecordEditable($tr.data('id')) || $(event.target).prop('special_click')) {
                return;
            }
            var fieldIndex = Math.max($tr.find('.o_field_cell').index($td), 0);
            this._selectCell(rowIndex, fieldIndex, { event: event });
        },

        setRowMode: function (recordID, mode) {
            var self = this;
            var record = self._getRecord(recordID);
            if (!record) {
                return Promise.resolve();
            }

            var editMode = (mode === 'edit');
            var $row = this._getRow(recordID);
            this.currentRow = editMode ? $row.prop('rowIndex') - 1 : null;

            // Note: 这是一个补丁：对安装了 roke_pub_tree_search 模块 和 增加了Tree视图表头固定功能 而增加的补丁
            if (!this.parent_view_type) {

                // 如果没有父视图，说明是Tree视图。此时因固定表头，多复制出来了一个header
                if (editMode) {
                    if (this.xc_seach_box) {
                        // this.currentRow = $row.prop('rowIndex') - 4;  // 2个header * (表头一行 + 搜索栏一行)
                        this.currentRow = $row.prop('rowIndex') - 2;  // 1个header * (表头一行 + 搜索栏一行)
                    } else {
                        if (this.ks_module) {
                            // this.currentRow = $row.prop('rowIndex') - 2; // 2个header * (表头一行)
                            this.currentRow = $row.prop('rowIndex') - 1; // 1个header * (表头一行)
                        } else {
                            this.currentRow = $row.prop('rowIndex') - 1; // 1个header * (表头一行)
                        }
                    }
                } else {
                    this.currentRow = null;
                }
            }
          
            var $tds = $row.children('.o_data_cell');
            var oldWidgets = _.clone(this.allFieldWidgets[record.id]);

            // Prepare options for cell rendering (this depends on the mode)
            var options = {
                renderInvisible: editMode,
                renderWidgets: editMode,
            };
            options.mode = editMode ? 'edit' : 'readonly';

            // Switch each cell to the new mode; note: the '_renderBodyCell'
            // function might fill the 'this.defs' variables with multiple promise
            // so we create the array and delete it after the rendering.
            var defs = [];
            this.defs = defs;
            _.each(this.columns, function (node, colIndex) {
                var $td = $tds.eq(colIndex);
                var $newTd = self._renderBodyCell(record, node, colIndex, options);

                // Widgets are unregistered of modifiers data when they are
                // destroyed. This is not the case for simple buttons so we have to
                // do it here.
                if ($td.hasClass('o_list_button')) {
                    self._unregisterModifiersElement(node, recordID, $td.children());
                }

                // For edit mode we only replace the content of the cell with its
                // new content (invisible fields, editable fields, ...).
                // For readonly mode, we replace the whole cell so that the
                // dimensions of the cell are not forced anymore.
                if (editMode) {
                    $td.empty().append($newTd.contents());
                } else {
                    self._unregisterModifiersElement(node, recordID, $td);
                    $td.replaceWith($newTd);
                }
            });
            delete this.defs;

            // Destroy old field widgets
            _.each(oldWidgets, this._destroyFieldWidget.bind(this, recordID));

            // Toggle selected class here so that style is applied at the end
            $row.toggleClass('o_selected_row', editMode);
            if (editMode) {
                this._disableRecordSelectors();
            } else {
                this._enableRecordSelectors();
            }

            return Promise.all(defs).then(function () {
                // mark Owl sub components as mounted
                WidgetAdapterMixin.on_attach_callback.call(self);

                // necessary to trigger resize on fieldtexts
                core.bus.trigger('DOM_updated');
            });
        },


    })

});
