odoo.define('roke_mes_base.Utils', async function (require) {
    'use strict';

    const rpc = require("web.rpc");

    const API_CONTROLLER = {
        DEMO: "/request/demo",
    }

    const ApiUtils = {
        /**
         * Demo 请求路由
         * */
        _request_controller: function (options) {
            return rpc.query({
                route: API_CONTROLLER.DEMO,
                params: options,
            }).then(result => {
                return result;
            });
        },
    };

    const OrmUtils = {
        /**
         * Demo 请求ORM
         * */
        _search: function (options) {
            return rpc.query({
                model: options.model,
                method: "search",
                args: [options.domain],
            }).then(result => {
                return result;
            });
        },

        _searchRead: function (options) {
            return rpc.query({
                model: options.model,
                method: "search_read",
                args: [
                    options.domain,
                    options.fields,
                ],
            }).then(result => {
                return result;
            });
        },

    };

    const CommonUtils = {
        /**
         * 获取预览服务
         * */
        _getPreviewService() {
            return OrmUtils._searchRead({
                model: "ir.config_parameter",
                domain: [["key", "=", "kkfileview.url"]],
                fields: ["value"]
            })
        },

        _previewDocAttachmentURL: function (document_id) {
            return rpc.query({
                model: "documents.document",
                method: "preview_attachment_url",
                args: [document_id],
            }).then(result => {
                return result;
            });
        },

        _previewAttachmentURL: function (attachment_id) {
            return rpc.query({
                model: "ir.attachment",
                method: "preview_attachment_url",
                args: [attachment_id],
            }).then(result => {
                return result;
            });
        },

        /**
         * 单文件预览
         */
        async _makePreviewURL(file_url) {
            let result = await this._getPreviewService();
            if (result.length > 0) {
                let service = result[0].value;
                return {
                    code: 0, message: "文件预览",
                    data: {url: `${service}/onlinePreview?url=${encodeURIComponent(Base64.encode(file_url))}`}
                }
            } else {
                return {
                    code: 1, message: "未设置预览服务",
                    data: {title: "无法预览文件", content: "没有找到预览服务器的相关配置。请到设置-常规设置-预览服务中设置 Server URL。"}
                }
            }
        },

        /**
         * 多图片文件预览仅适用图片
         */
        async _makePreviewURLMulti(file_urls) {
            let result = await this._getPreviewService();
            if (result.length > 0) {
                let service = result[0].value;
                let file_url = file_urls.join('|');
                return {
                    code: 0, message: "文件预览",
                    data: {url: `${service}/picturesPreview?urls=${encodeURIComponent(Base64.encode(file_url))}`}
                }
            } else {
                return {
                    code: 1, message: "未设置预览服务",
                    data: {title: "无法预览文件", content: "没有找到预览服务器的相关配置。请到设置-常规设置-预览服务中设置 Server URL。"}
                }
            }
        },

    };

    return {CommonUtils, OrmUtils, ApiUtils};


});
