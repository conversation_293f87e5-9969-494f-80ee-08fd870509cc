<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--用户添加手机号-->
    <record id="view_roke_mes_inherit_client_users_form_view" model="ir.ui.view">
        <field name="name">roke.mes.inherit.client.users.form</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="roke_mes_client.view_roke_client_inherit_users_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='mobile_default_function']" position="before">
                <field name="employee_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
            </xpath>
            <xpath expr="//sheet/div/group/field[@name='partner_id']" position="replace">
                <field name="is_hide_employee" invisible="1"/>
                <field name="is_required_employee" invisible="1"/>
                <field name="employee_domain" invisible="1"/>
                <field name="employee_id" domain="[('id','in', employee_domain)]"
                       attrs="{'invisible': [('is_hide_employee', '=', True)], 'required': [('is_required_employee', '=', True)],'readonly': [('id', '!=', False)]}"/>
                <field name="phone" required="1"/>
            </xpath>
        </field>
    </record>

</odoo>
