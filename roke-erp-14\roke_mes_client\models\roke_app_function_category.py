# -*- coding: utf-8 -*-
"""
Description:
    APP全部接口菜单
    Created by www.rokedata.com<wsc>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class RokeAppFunctionCategory(models.Model):
    _name = "roke.app.function.category"
    _description = "移动端功能类比"

    sequence = fields.Integer(string="序号", default=10)
    name = fields.Char(string="类别名称")
    is_mobile = fields.Boolean(string="是否app显示",default=True)
    function_ids = fields.One2many("roke.app.function", "category_id", string="功能菜单")
    index = fields.Char(string="类别标识")
    note = fields.Text(string="备注", tracking=True)
    app_attribute = fields.Selection([("移动端", "移动端"), ("平板端", "平板端")], string="应用属性", default='移动端')

    is_prefabrication = fields.Boolean(string='是否预制字段', default=False)

    is_iot_lamp_function = fields.<PERSON><PERSON>an(string="是否是物联网灯功能", default=False)

    def write_index(self):
        """
        标识补充
        :return:
        """
        category_ids = self.env['roke.app.function.category'].search([])
        for i in category_ids:
            if i.name == '移动下单':
                i.write({'index': 'ydxd'})
            if i.name == '报工台':
                i.write({'index': 'bgt', 'app_attribute': '平板端'})
            if i.name == '报工':
                i.write({'index': 'bg'})
            if i.name == '报表':
                i.write({'index': 'bb'})
            if i.name == '待办事项':
                i.write({'index': 'dbsx'})
            if i.name == '仓库管理':
                i.write({'index': 'ckgl'})
            if i.name == '审批':
                i.write({'index': 'sp'})
            if i.name == '生产':
                i.write({'index': 'produce'})
            if i.name == '销售':
                i.write({'index': 'sale'})
            if i.name == '采购':
                i.write({'index': 'procure'})


class RokeAppFunction(models.Model):
    _name = "roke.app.function"
    _description = "移动端功能"
    _order = "sequence, id"

    index = fields.Char(string="标识")
    name = fields.Char(string="功能名称")
    category_id = fields.Many2one('roke.app.function.category', string="类别")
    default = fields.Boolean(string="默认")
    active = fields.Boolean(string="状态", default=True)
    general_order = fields.Boolean(string="通用单据", default=False)
    custom_report = fields.Boolean(string="自定义报表", default=False)
    report_url = fields.Char(string="查询地址")
    icon_image = fields.Selection([("default_1", "默认图标一"), ("default_2", "默认图标二"),("default_3", "默认图标三")],string="默认图标")

    is_prefabrication = fields.Boolean(string='是否预制字段', default=False)
    config_query = fields.Boolean(string="配置报表", default=False)
    query_id = fields.Many2one('roke.query.app.mixing.model', string="报表选择", domain=[("journaling_type", "=", "移动端报表")])

    is_ylb = fields.Boolean(string='引流版迁移功能', default=False)
    ylb_index = fields.Char(string="迁移标识")

    sequence = fields.Integer(string="序号", default=1)
    active_index = fields.Selection([("原始功能", "原始功能"), ("功能替换", "功能替换")], default="原始功能", string="隐藏标识")

    is_iot_lamp_function = fields.Boolean(string="是否是物联网灯功能", default=False)

    @api.onchange("custom_report")
    def _onchange_custom_report(self):
        if not self.custom_report:
            return {"value": {"report_url": ""}}

