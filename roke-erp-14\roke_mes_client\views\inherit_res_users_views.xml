<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--用户添加手机号-->
    <record id="view_roke_client_inherit_users_form_view" model="ir.ui.view">
        <field name="name">roke.client.inherit.users.form</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='access_rights']/group[1]" position="after">
                <group name="roke_mobile" string="其它终端权限">
                     <!--<field name="mobile_menu_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/> 废弃-->
                    <field name="allow_function_ids" invisible="1"/>
                    <field name="mobile_default_function" widget="many2many_tags" options="{'no_open': True, 'no_create': True}" domain="[('id', 'in', allow_function_ids)]"/>
                </group>
            </xpath>
            <xpath expr="//group[@name='messaging']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='login']" position="attributes">
                <attribute name="string">用户名</attribute>
            </xpath>
            <xpath expr="//field[@name='tz_offset']" position="after">
                <field name="phone" placeholder="此处填写手机号" string="手机号"/>
            </xpath>
        </field>
    </record>

    <record id="view_roke_client_inherit_users_simple_modif_form_view" model="ir.ui.view">
        <field name="name">roke.client.inherit.users.simple.modif.form</field>
        <field name="model">res.users</field>
        <field name="inherit_id" ref="base.view_users_form_simple_modif"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='tz_offset']" position="after">
                <field name="phone" placeholder="此处填写手机号" string="手机号"/>
            </xpath>
            <xpath expr="//group[@name='signature']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='api_key_wizard']/../.." position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

</odoo>
