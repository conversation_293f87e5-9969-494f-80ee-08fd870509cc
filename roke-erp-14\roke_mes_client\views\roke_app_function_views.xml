<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--APP功能类别-->
    <!--search-->
    <record id="view_roke_app_function_category_menu_search" model="ir.ui.view">
        <field name="name">roke.app.function.category.menu.search</field>
        <field name="model">roke.app.function.category</field>
        <field name="arch" type="xml">
            <search string="APP功能类别">
                <field name="index"/>
                <field name="name"/>
                <field name="note"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_app_function_category_menu_tree" model="ir.ui.view">
        <field name="name">roke.app.function.category.menu.tree</field>
        <field name="model">roke.app.function.category</field>
        <field name="arch" type="xml">
            <tree string="APP功能类别">
                <field name="sequence"/>
                <field name="index" string="类别标志" required="1"/>
                <field name="name" string="类别名称" required="1"/>
                <field name="app_attribute" required="1"/>
                <field name="note"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_app_function_category_menu_form" model="ir.ui.view">
        <field name="name">roke.app.function.category.menu.form</field>
        <field name="model">roke.app.function.category</field>
        <field name="arch" type="xml">
            <form string="移动端全部功能菜单">
                <group col="4">
                    <group>
                        <field name="sequence"/>
                    </group>
                    <group>
                        <field name="name" string="类别名称" required="1"/>
                    </group>
                    <group>
                        <field name="index" string="类别标志" required="1"/>
                    </group>
                    <group>
                        <field name="app_attribute" required="1"/>
                    </group>
                </group>
                <group>
                     <field name="is_iot_lamp_function"/>
                </group>
                <group>
                    <field name="note"/>
                </group>
            </form>
        </field>
    </record>
<!--    action-->
    <record id="view_roke_app_function_category_menu_action" model="ir.actions.act_window">
        <field name="name">APP功能类别</field>
        <field name="res_model">roke.app.function.category</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
    <!--APP功能管理-->
    <!--search-->
    <record id="view_roke_app_function_search" model="ir.ui.view">
        <field name="name">roke.app.function.search</field>
        <field name="model">roke.app.function</field>
        <field name="arch" type="xml">
            <search string="APP功能管理">
                <field name="name"/>
                <field name="index"/>
                <field name="default"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_app_function_tree" model="ir.ui.view">
        <field name="name">roke.app.function.tree</field>
        <field name="model">roke.app.function</field>
        <field name="arch" type="xml">
            <tree string="APP功能管理">
                <field name="name"/>
                <field name="index"/>
                <field name="category_id">
                    <field name="name" />
                </field>
                <field name="active"/>
<!--                <field name="default"/>-->
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_app_function_form" model="ir.ui.view">
        <field name="name">roke.app.function.form</field>
        <field name="model">roke.app.function</field>
        <field name="arch" type="xml">
            <form string="APP功能管理">
                <sheet>
                    <group id="g1">
                        <group>
                            <field name="name" required="1"/>
                            <field name="category_id">
                                <field name="name" />
                            </field>
                            <field name="default" invisible="1"/>
                            <field name="general_order"/>
<!--                            <field name="active"/>-->
                            <field name="is_ylb"/>
                        </group>
                        <group>
                            <field name="index" required="1"/>
                            <field name="icon_image"/>
                            <field name="custom_report"/>
                            <field name="config_query"/>
                            <field name="is_iot_lamp_function" />
                            <field name="sequence"/>
                            <field name="query_id" options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible': [('config_query', '=', False)],'required': [('config_query', '=', True)]}"/>
                            <field name="report_url" attrs="{'invisible': [('custom_report', '=', False)],'required': [('custom_report', '=', True)]}"/>
                            <field name="ylb_index" attrs="{'invisible': [('is_ylb', '=', False)],'required': [('is_ylb', '=', True)]}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_app_function_action" model="ir.actions.act_window">
        <field name="name">APP功能管理</field>
        <field name="res_model">roke.app.function</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>
