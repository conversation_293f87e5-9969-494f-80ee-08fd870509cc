# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.roke_mes_base.tools import http_tool
import math
import json
import logging
import datetime
import requests
from io import StringIO
import traceback
from datetime import datetime, timedelta
import pytz
_logger = logging.getLogger(__name__)
headers = [("Content-Type", "application/json; charset=utf-8")]


def convert_utc_to_local(dt, tz_name='Asia/Shanghai'):
    if not dt:
        return ""
    # 假设传入的 dt 是字符串或 Odoo 的 datetime 字段
    if isinstance(dt, str):
        dt_utc = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
    else:
        dt_utc = dt

    # 设置 UTC 时间为 aware datetime
    dt_utc = pytz.utc.localize(dt_utc)

    # 转换为目标时区时间
    target_tz = pytz.timezone(tz_name)
    dt_local = dt_utc.astimezone(target_tz)

    return dt_local.strftime("%Y-%m-%d %H:%M:%S")


class ClientApiMesEms(http.Controller):

    @http.route('/roke/ems/today_energy', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_today_energy(self):
        # 获取今日用电量实时用电数据
        equipment_id = http.request.jsonrequest.get('equipment_id', None)
        if equipment_id is None:
            return {
                "code": "100",
                "state": "error",
                "msg": "设备ID不能为空",
                "data":{},
            }
        today_energy_data = self._get_today_energy_data(equipment_id)
        if not today_energy_data:
            return {
                "code": "100",
                "state": "error",
                "msg": "该设备今日暂无数据",
                "data":{},
            }
        return {
                "code": "200",
                "state": "success",
                "msg": "",
                "data":today_energy_data ,
            }


    @http.route('/roke/ems/hourly_energy', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_hourly_energy(self):
        # 获取最近4个小时的能耗数据
        # 日期 如：2025-05-07
        date = http.request.jsonrequest.get('date', None)
        # 能耗项目 code
        item = http.request.jsonrequest.get('item', None)
        equipment_id = http.request.jsonrequest.get('equipment_id', None)
        if not equipment_id:
            return {
                "code": "100",
                "date":'',
                "state": "error",
                "msg": "设备ID不能为空",
                "data":[],
            }

        hourly_energy_data = self._get_hourly_energy_data(equipment_id, date, item)
        if not hourly_energy_data:
            return {
                "code": "100",
                "state": "error",
                "msg": "该设备今日暂无数据",
                "data":[],
            }
        return {
                "code": "200",
                "state": "success",
                'date':date,
                "msg": "",
                "data":hourly_energy_data ,
            }

    def _get_today_energy_data(self, equipment_id,today=False,item=''):
        # 获取当前日期
        if not today:
            today = fields.Date.today()
        if not item:
            item = 'ELEC001'

        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))

        # 查询今日的能耗数据
        energy_lines = http.request.env['roke.ems.consume.order.line'].sudo().search([
            ('equipment_id', '=', equipment_id),
            ('consume_items_id.code', '=', item),
            ('consume_time', '>=', start_of_day),
            ('consume_time', '<=', end_of_day)
        ])
        if not energy_lines:
            return  []

        # 初始化变量
        total_consumption = 0
        voltage = 0
        current = 0
        power = 0

        first_line = energy_lines[0]
        for line in energy_lines:
            total_consumption += line.qty  # 累计用电量

        # 构造返回数据
        today_energy_data = {
            'uom_name':first_line.uom_id.name,
            'uom_id':first_line.uom_id.id,
            "instant_power": power, # 瞬时功率
            "voltage": voltage, # 电压
            "current": current, #电流
            "total_consumption": round(total_consumption, 2)
        }
        
        return today_energy_data

    def _get_hourly_energy_data(self, equipment_id,today=False,item=''):
        # 获取当前日期
        if not today:
            today = fields.Date.today()
        else:
            today = fields.Date.from_string(today)
        if not item:
            item = 'ELEC001'
        # 查询今日的能耗数据
        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))
        # 查询今日的能耗数据
        energy_lines = http.request.env['roke.ems.consume.order.line'].sudo().search([
                ('equipment_id', '=', equipment_id),
                ('consume_items_id.code', '=', item),
                ('consume_time', '>=', start_of_day),
                ('consume_time', '<=', end_of_day)
            ])

        # 初始化每小时能耗数据字典
        hourly_energy_data = {
            "05:00": 0,
            "09:00": 0,
            "13:00": 0,
            "17:00": 0,
            "20:00": 0,
            "00:00": 0,
        }


        if not energy_lines:
            return  []

        first_line = energy_lines[0]
        uom_id = first_line.uom_id.id
        uom_name = first_line.uom_id.name

        # 遍历能耗数据，累加每小时的用电量
        for line in energy_lines:
            consume_time = fields.Datetime.from_string(line.consume_time + timedelta(hours=8))
            hour = consume_time.hour
            if 0 <= hour < 5:
                hourly_energy_data["05:00"] += line.qty
            if 5 <= hour < 9:
                hourly_energy_data["09:00"] += line.qty
            elif 9 <= hour < 13:
                hourly_energy_data["13:00"] += line.qty
            elif 13 <= hour < 17:
                hourly_energy_data["17:00"] += line.qty
            elif 17 <= hour < 20:
                hourly_energy_data["20:00"] += line.qty
            elif 20 <= hour < 24:
                hourly_energy_data["00:00"] += line.qty

        # 构造返回数据
        result = [
            {"time": "05:00", "consumption": round(hourly_energy_data["05:00"], 2),'uom_name':uom_name,'uom_id':uom_id},
            {"time": "09:00", "consumption": round(hourly_energy_data["09:00"], 2),'uom_name':uom_name,'uom_id':uom_id},
            {"time": "13:00", "consumption": round(hourly_energy_data["13:00"], 2),'uom_name':uom_name,'uom_id':uom_id},
            {"time": "17:00", "consumption": round(hourly_energy_data["17:00"], 2),'uom_name':uom_name,'uom_id':uom_id},
            {"time": "20:00", "consumption": round(hourly_energy_data["20:00"], 2),'uom_name':uom_name,'uom_id':uom_id},
            {"time": "00:00", "consumption": round(hourly_energy_data["00:00"], 2),'uom_name':uom_name,'uom_id':uom_id}
        ]

        return result