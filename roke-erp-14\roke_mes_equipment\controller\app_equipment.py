# -*- coding: utf-8 -*-

from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.roke_mes_base.tools import http_tool
import math
import json
import logging
import datetime
import requests
from io import StringIO
import traceback
import logging
_logger = logging.getLogger(__name__)

class RokeMesAppEquipment(http.Controller):
        @http.route('/roke/equipment/inspection_project/create', methods=['POST', 'OPTIONS'], type='json', auth='none',
                    csrf=False)

        def _create_inspection_project(self):
            """ 创建点检计划 """
            project = None
            data = http.request.jsonrequest
            required_fields = ['equipment_id', 'check_plan_id', 'frequency_type', 'start_date', 'end_date']
            for field in required_fields:
                if not data.get(field):
                    return {
                        "state": "error",
                        "msgs": f"缺少必填字段：{field}",
                        "data": {}
                    }

            try:
                vals = {
                    'equipment_id': int(data.get('equipment_id')),
                    'check_plan_id': int(data.get('check_plan_id')),
                    'frequency_type': data.get('frequency_type'),
                    'start_date': data.get('start_date'),
                    'end_date': data.get('end_date'),
                    'active': data.get('active'),
                    'check_user_id': data.get('check_user_id') or http.request.env.user.id,
                }

                # 时间格式转换（去除8小时时区偏移）
                vals['start_date'] = fields.Datetime.from_string(vals['start_date']) - datetime.timedelta(hours=8)
                vals['end_date'] = fields.Datetime.from_string(vals['end_date']) - datetime.timedelta(hours=8)
                vals['start_date'] = fields.Datetime.to_string(vals['start_date'])
                vals['end_date'] = fields.Datetime.to_string(vals['end_date'])

                project = http.request.env(user=http.request.env.user.id or SUPERUSER_ID)['roke.mes.equipment.inspection.project'].create(vals)

                return {
                    "state": "success",
                    "msgs": "点检计划创建成功",
                    "data": {
                        "project_id": project.id,
                        "project_code": project.code,
                        "note": project.note,
                    }
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                if project:
                    project.unlink()
                http.request.env.cr.rollback()
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/inspection_project/get_one', methods=['POST','OPTIONS'], type='json', auth='none',
                    csrf=False)
        def _get_inspection_project(self):
            """ 获取单个点检计划详情 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return {
                    "state": "error",
                    "msgs": "缺少 project_id 参数",
                    "data": {}
                }

            project = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment.inspection.project'].with_context(active_test=False).browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的点检计划不存在",
                    "data": {}
                }

            result = {
                "project_id": project.id,
                "code": project.code or "",

                # 设备信息
                "equipment_id": project.equipment_id.id,
                "equipment_code": project.equipment_id.code or "",
                "equipment_name": project.equipment_id.name or "",

                # 点检方案信息
                "check_plan_id": project.check_plan_id.id,
                "check_plan_name": project.check_plan_id.name or "",
                "check_plan_type": project.check_plan_id.type or "",

                # 计划信息
                "frequency_type": project.frequency_type or "",
                "frequency_type_name": project.get_selection_field_values("frequency_type", project.frequency_type),
                "start_date": (project.start_date + datetime.timedelta(hours=8)).strftime(
                    '%Y-%m-%d %H:%M:%S') if project.start_date else "",
                "end_date": (project.end_date + datetime.timedelta(hours=8)).strftime(
                    '%Y-%m-%d %H:%M:%S') if project.end_date else "",
                "note": project.note or "",
                "active": project.active,
                "check_user_id": project.check_user_id.id or "",
                "check_user_name": project.check_user_id.name or "",
            }

            return {
                "state": "success",
                "msgs": "查询成功",
                "data": result
            }

        @http.route('/roke/equipment/inspection_project/put', methods=['POST','OPTIONS'], type='json', auth='none',
                    csrf=False)
        def _write_inspection_project(self):
            """ 编辑点检计划 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return {
                    "state": "error",
                    "msgs": "缺少 project_id 参数用于更新",
                    "data": {}
                }

            project = http.request.env['roke.mes.equipment.inspection.project'].sudo().browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的点检计划不存在",
                    "data": {}
                }

            write_vals = {}

            for field in ['equipment_id', 'check_plan_id', 'frequency_type', 'start_date', 'end_date', 'active',
                          'check_user_id']:
                value = data.get(field)
                if value is not None:
                    if field in ['equipment_id', 'check_plan_id']:
                        write_vals[field] = int(value)
                    elif field in ['start_date', 'end_date']:
                        dt = fields.Datetime.from_string(value) - datetime.timedelta(hours=8)
                        write_vals[field] = fields.Datetime.to_string(dt)
                    else:
                        write_vals[field] = value

            try:
                project.write(write_vals)
                return {
                    "state": "success",
                    "msgs": "点检计划更新成功",
                    "data": {
                        "project_id": project.id,
                        "project_code": project.code,
                        "note": project.note,
                    }
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                http.request.env.cr.rollback()
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/inspection_project/delete', methods=['POST','OPTIONS'], type='json', auth='none',
                    csrf=False)
        def _delete_inspection_project(self):
            """ 删除点检计划 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return http.Response(json.dumps({
                    "state": "error",
                    "msgs": "缺少 project_id 参数用于删除",
                    "data": {}
                }), content_type='application/json')


            project = http.request.env['roke.mes.equipment.inspection.project'].sudo().browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的点检计划不存在",
                    "data": {}
                }


            try:
                project.unlink()
                return {
                    "state": "success",
                    "msgs": "",
                    "data": {}
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/inspection_projects/get', type='json', methods=['POST', 'OPTIONS'], auth='none',
                    csrf=False, cors='*')
        def get_inspection_projects(self):
            """
            获取点检计划数据（扁平化输出）
            :param equipment_id: （可选）设备ID
            :param frequency_type: （可选）频率类型
            :param page: 当前页码（可选，默认为1）
            :param page_size: 每页记录数（可选，默认为5）
            :return:
                {
                }
            """
            data = http.request.jsonrequest
            equipment_id = data.get('equipment_id')  # 可选参数
            frequency_type = data.get('frequency_type')  # 可选参数

            # 分页参数
            page = int(data.get('page', 1))
            page_size = int(data.get('page_size', 5))

            domain = []
            if equipment_id:
                domain.append(('equipment_id', '=', int(equipment_id)))
            if frequency_type:
                domain.append(('frequency_type', '=', frequency_type))


            # 分页查询
            projects = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment.inspection.project'].with_context(active_test=False).search(
                domain, offset=(page - 1) * page_size, limit=page_size)

            result = []
            for project in projects:
                result.append({
                    "project_id": project.id,
                    "code": project.code or "",

                    # 设备字段
                    "equipment_id": project.equipment_id.id,
                    "equipment_code": project.equipment_id.code or "",
                    "equipment_name": project.equipment_id.name or "",

                    # 点检方案字段
                    "check_plan_id": project.check_plan_id.id,
                    "check_plan_name": project.check_plan_id.name or "",
                    "check_plan_type": project.check_plan_id.type or "",

                    # 计划字段
                    "frequency_type": project.frequency_type or "",
                    "frequency_type_name": project.get_selection_field_values("frequency_type",  project.frequency_type),
                    "start_date": (project.start_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.start_date else "",
                    "end_date": (project.end_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.end_date else "",
                    "note": project.note or "",
                    "active": project.active,
                    "check_user_id": project.check_user_id.id or "",
                    "check_user_name": project.check_user_id.name or "",
                })

            return {
                "state": "success",
                "msgs": "获取成功",
                "data": result
            }


        """
        保养计划
        
        """
        @http.route('/roke/equipment/maintenance_project/create', methods=['POST', 'OPTIONS'], type='json', auth='none', csrf=False)
        def _create_maintenance_project(self):
            """ 创建保养计划 """
            project = None
            data = http.request.jsonrequest
            required_fields = ['equipment_id', 'maintenance_scheme_id', 'frequency_type', 'start_date', 'end_date','repair_user_id','user_id','priority','active']
            for field in required_fields:
                if not data.get(field):
                    return {
                        "state": "error",
                        "msgs": f"缺少必填字段：{field}",
                        "data": {}
                    }

            try:
                vals = {
                    'equipment_id': int(data.get('equipment_id')),
                    'maintenance_scheme_id': int(data.get('maintenance_scheme_id')),
                    'frequency_type': data.get('frequency_type'),
                    'start_date': data.get('start_date'),
                    'end_date': data.get('end_date'),
                    'active': data.get('active'),
                    'user_id': data.get('user_id') or http.request.env.user.id,
                    'repair_user_id': data.get('repair_user_id') or http.request.env.user.id,
                    'priority': data.get('priority', 'normal'),
                }

                # 时间格式转换（去除8小时时区偏移）
                vals['start_date'] = fields.Datetime.from_string(vals['start_date']) - datetime.timedelta(hours=8)
                vals['end_date'] = fields.Datetime.from_string(vals['end_date']) - datetime.timedelta(hours=8)
                vals['start_date'] = fields.Datetime.to_string(vals['start_date'])
                vals['end_date'] = fields.Datetime.to_string(vals['end_date'])

                project = http.request.env(user=http.request.env.user.id or SUPERUSER_ID)['roke.mes.equipment.maintenance.project'].create(vals)

                return {
                    "state": "success",
                    "msgs": "保养计划创建成功",
                    "data": {
                        "project_id": project.id,
                        "project_code": project.code,
                        "note": project.note,
                    }
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                if project:
                    project.unlink()
                http.request.env.cr.rollback()
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/maintenance_project/get_one', methods=['POST','OPTIONS'], type='json', auth='none', csrf=False)
        def _get_maintenance_project(self):
            """ 获取单个保养计划详情 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return {
                    "state": "error",
                    "msgs": "缺少 project_id 参数",
                    "data": {}
                }

            project = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment.maintenance.project'].with_context(active_test=False).browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的保养计划不存在",
                    "data": {}
                }

            result = {
                "project_id": project.id,
                "code": project.code or "",
                "equipment_id": project.equipment_id.id,
                "equipment_code": project.equipment_id.code or "",
                "equipment_name": project.equipment_id.name or "",
                "maintenance_scheme_id": project.maintenance_scheme_id.id,
                "maintenance_scheme_name": project.maintenance_scheme_id.name or "",
                "frequency_type": project.frequency_type or "",
                "frequency_type_name": project.get_selection_field_values("frequency_type", project.frequency_type),
                "start_date": (project.start_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.start_date else "",
                "end_date": (project.end_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.end_date else "",
                "note": project.note or "",
                "active": project.active,
                "user_id": project.user_id.id or "",
                "user_name": project.user_id.name or "",
                "repair_user_id": project.repair_user_id.id or "",
                "repair_user_name": project.repair_user_id.name or "",
                "priority": project.priority or "",
                "priority_name":project.get_selection_field_values("priority", project.priority),

            }

            return {
                "state": "success",
                "msgs": "",
                "data": result
            }

        @http.route('/roke/equipment/maintenance_project/put', methods=['POST','OPTIONS'], type='json', auth='none', csrf=False)
        def _write_maintenance_project(self):
            """ 编辑保养计划 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return {
                    "state": "error",
                    "msgs": "缺少 project_id 参数用于更新",
                    "data": {}
                }

            project = http.request.env['roke.mes.equipment.maintenance.project'].sudo().browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的保养计划不存在",
                    "data": {}
                }

            write_vals = {}
            required_fields = ['equipment_id', 'maintenance_scheme_id', 'frequency_type', 'start_date', 'end_date',
                               'repair_user_id', 'user_id', 'priority', 'active']
            for field in required_fields:
                value = data.get(field)
                if value is not None:
                    if field in ['equipment_id', 'maintenance_scheme_id']:
                        write_vals[field] = int(value)
                    elif field in ['start_date', 'end_date']:
                        dt = fields.Datetime.from_string(value) - datetime.timedelta(hours=8)
                        write_vals[field] = fields.Datetime.to_string(dt)
                    else:
                        write_vals[field] = value

            try:
                project.write(write_vals)
                return {
                    "state": "success",
                    "msgs": "",
                    "data": {
                        "project_id": project.id,
                        "project_code": project.code,
                        "note": project.note,
                    }
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                http.request.env.cr.rollback()
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/maintenance_project/delete', methods=['POST','OPTIONS'], type='json', auth='none', csrf=False)
        def _delete_maintenance_project(self):
            """ 删除保养计划 """
            data = http.request.jsonrequest
            project_id = data.get('project_id')

            if not project_id:
                return {
                    "state": "error",
                    "msgs": "缺少 project_id 参数用于删除",
                    "data": {}
                }

            project = http.request.env['roke.mes.equipment.maintenance.project'].sudo().browse(int(project_id))
            if not project.exists():
                return {
                    "state": "error",
                    "msgs": "指定的保养计划不存在",
                    "data": {}
                }

            try:
                project.unlink()
                return {
                    "state": "success",
                    "msgs": "",
                    "data": {}
                }

            except Exception as e:
                _logger.error(traceback.format_exc())
                return {
                    "state": "error",
                    "msgs": str(e),
                    "data": {}
                }

        @http.route('/roke/equipment/maintenance_projects/get', type='json', methods=['POST', 'OPTIONS'], auth='none', csrf=False, cors='*')
        def get_maintenance_projects(self):
            """
            获取保养计划列表
            """
            data = http.request.jsonrequest
            equipment_id = data.get('equipment_id')
            frequency_type = data.get('frequency_type')
            page = int(data.get('page', 1))
            page_size = int(data.get('page_size', 10))

            domain = []
            if equipment_id:
                domain.append(('equipment_id', '=', int(equipment_id)))
            if frequency_type:
                domain.append(('frequency_type', '=', frequency_type))

            projects = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment.maintenance.project'].with_context(active_test=False).search(
                domain, offset=(page - 1) * page_size, limit=page_size)

            result = []
            for project in projects:
                result.append({
                    "project_id": project.id,
                    "code": project.code or "",
                    "equipment_id": project.equipment_id.id,
                    "equipment_code": project.equipment_id.code or "",
                    "equipment_name": project.equipment_id.name or "",
                    "maintenance_scheme_id": project.maintenance_scheme_id.id,
                    "maintenance_scheme_name": project.maintenance_scheme_id.name or "",
                    "frequency_type": project.frequency_type or "",
                    "frequency_type_name": project.get_selection_field_values("frequency_type", project.frequency_type),
                    "start_date": (project.start_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.start_date else "",
                    "end_date": (project.end_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if project.end_date else "",
                    "note": project.note or "",
                    "active": project.active,
                    "user_name": project.user_id.name or "",
                    "repair_user_name": project.repair_user_id.name or "",
                    "priority": project.priority or "",
                    "priority_name": project.get_selection_field_values("priority", project.priority),

                })

            return {
                "state": "success",
                "msgs": "获取成功",
                "data": result
            }

        """
        工单记录
        """

        @http.route('/roke/equipment/work_record/get', type='json', methods=['POST', 'OPTIONS'], auth='user', csrf=False)
        def get_work_records(self):
            """
            获取当前用户相关的报工记录
            参数：
                product_id: int（可选）
                process_id: int（可选）
                work_date: str (YYYY-MM-DD)（可选，默认今天）
            返回：
                列表数据，包含报工时间、产品、工序、合格数、不合格数、关联工单等信息
            """
            data = http.request.jsonrequest
            user_id =  data.get('user_id')
            if not user_id:
                user_id = http.request.env.user.id

            employee_id = http.request.env(user=SUPERUSER_ID)['roke.employee'].search([('user_id', '=', user_id)],limit=1)
            # 解析参数
            product_id = data.get('product_id')
            process_id = data.get('process_id')
            work_date_str = data.get('work_time')
            page = int(data.get('page') or 1)
            page_size = int(data.get('page_size') or 10)

            domain = [
                ('employee_ids', 'in', employee_id.ids)
            ]
            if work_date_str:
                work_date = datetime.datetime.strptime(work_date_str, '%Y-%m-%d').date()
                time_domain = [
                    ('work_time', '>=', fields.Datetime.to_string(datetime.datetime.combine(work_date, datetime.time.min) - datetime.timedelta(hours=8))),
                    ('work_time', '<=', fields.Datetime.to_string(datetime.datetime.combine(work_date, datetime.time.max) - datetime.timedelta(hours=8))),
                ]
                domain += time_domain
            if product_id:
                domain.append(('product_id', '=', int(product_id)))
            if process_id:
                domain.append(('process_id', '=', int(process_id)))

            # 查询记录
            records = http.request.env(user=SUPERUSER_ID)['roke.work.record'].search(
                domain, offset=(page - 1) * page_size, limit=page_size)

            total_count  = http.request.env(user=SUPERUSER_ID)['roke.work.record'].search_count(domain)

            result = []
            for record in records:
                result.append({
                    'work_time': (record.work_time+ datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S') if record.work_time else None,
                    'product_id': record.product_id.id,
                    'product_name': record.product_id.name,
                    'process_id': record.process_id.id,
                    'process_name': record.process_id.name,
                    'finish_qty': round(record.finish_qty,2),
                    'unqualified_qty': round(record.unqualified_qty,2),
                    'work_order_id': record.work_order_id.id,
                    'work_order_code': record.work_order_id.code,
                })

            return {
                "state": "success",
                "msgs": "",
                "data": result,
                "total_count": total_count,
            }

