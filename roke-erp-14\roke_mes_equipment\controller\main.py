# -*- coding: utf-8 -*-
"""
Description:
    设备检查接口
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_mes_base.tools import http_tool
import json
import logging
import math
from datetime import datetime, date, timedelta
from odoo.addons.roke_mes_base.tools import http_tool

_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]


class Main(http.Controller):

    @http.route('/roke/get_equipment_data', type='json', methods=['POST', 'OPTIONS'], auth='user', cors='*', csrf=False)
    def get_equipment_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", False)
        domain = [("e_state", "!=", "报废")]
        if category_id:
            domain.append(("category_id", "=", category_id))
        equipment_list = _self.env["roke.mes.equipment"].search(domain)
        data = []
        for v in equipment_list:
            data.append({
                "name": v.name or "",
                "id": v.id,
                "code": v.code,
                "category_id": v.category_id.id,
                "e_state": v.e_state,
                "price": v.price
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    @http.route('/roke/get_equipment_category_data', type='json', methods=['POST', 'OPTIONS'], auth='user', cors='*', csrf=False)
    def get_equipment_type_data(self):
        _self = http.request
        category_id = _self.jsonrequest.get("category_id", 0)
        domain = []
        if category_id:
            domain.append("|")
            domain.append(("parent_id", "=", category_id))
            domain.append(("id", "=", category_id))
        else:
            domain.append(("parent_id", "=", False))
        category_list = _self.env["roke.mes.equipment.category"].sudo().search(domain)
        data = []
        for v in category_list:
            count = _self.env["roke.mes.equipment.category"].sudo().search_count([("parent_id", "=", v.id)])
            is_parent = v.id == int(category_id) if category_id else False
            data.append({
                "id": v.id,
                "name": v.name,
                "parent_id": v.parent_id.id or 0,
                "note": v.note,
                "is_parent": is_parent,
                "no_below": (count != 0) if not is_parent else False
            })
        return {'code': 0, 'state': 'success', 'msgs': '获取成功', 'data': data}

    # 获取当前工作中心下的设备
    @http.route('/roke/workcenter_equipment', type='json', methods=['POST', 'OPTIONS'], auth='none', cors='*', csrf=False)
    def get_workcenter_equipment(self):
        """
        获取当前工作中心下的设备
            入参：{'workcenter_id': '工作中心id'}
            返回:return: {'state': 'success' or 'error', 'msgs': '描述信息', 'datas': '设备列表'}
        """
        _logger.info('------获取工作中心id------')
        workcenter_id = http.request.jsonrequest.get('workcenter_id')
        _logger.info('入参：{}'.format(workcenter_id))
        if not workcenter_id:
            _logger.info('缺少入参')
            return {'state': 'error', 'msgs': '缺少入参 (需入参：workcenter_id)'}
        datas_list = []
        equipments_list = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([('work_center_id', '=', int(workcenter_id))])
        if equipments_list:
            for equipment in equipments_list:
                datas_list.append({
                    'equipment_id': equipment.id,
                    'equipment_name': equipment.name,
                    'equipment_code': equipment.code,
                    'equipment_state': http_tool.selection_to_dict("roke.mes.equipment", "e_state")[equipment.e_state]
                })

            _logger.info('获取成功: {}'.format(datas_list))
            return {'state': 'success', 'msgs': '获取成功', 'datas': datas_list}

        else:
            _logger.info('获取失败，未找到设备')
            return {'state': 'error', 'msgs': '获取失败'}

    @http.route('/roke/get_all_equipment', type='http', auth="none", methods=['GET'], csrf=False, cors='*')
    def get_all_equipment(self):
        """
        获取所有设备
        :return:
        """
        equipmentObj = http.request.env(user=SUPERUSER_ID)["roke.mes.equipment"]
        equipments = equipmentObj.search([])
        result = []
        for equipment in equipments:
            result.append({
                "id": equipment.id,
                "name": equipment.display_name
            })
        return http.Response(json.dumps({"state": "success", "msgs": "获取成功", "equipments": result}), headers=headers)

    @http.route('/roke/spot_check_result', type='json', auth="none", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def spot_check_result(self):
        """
        提交点检结果
            workcenter_id:工作中心ID
            check_items:点检结果明细-->[{
                item_id: 点检项ID
                result: normal: 正常; anomaly: 异常; fault: 故障
                description: 描述
                file: base64图片
            }]
        """
        _logger.info(http.request.jsonrequest)
        workcenter_id = http.request.jsonrequest.get("workcenter_id")
        workcenter = http.request.env(user=SUPERUSER_ID)['roke.mes.work.center'].search([
            ("id", "=", int(workcenter_id))
        ])
        check_items = http.request.jsonrequest.get('check_items')
        try:
            if type(check_items) in (str,):
                check_items = eval(http.request.jsonrequest.get('check_items') or [])
            else:
                check_items = check_items
        except Exception as e:
            return {"state": "error", "msgs": '点检结果明细格式错误 参考[{"item_id": "点检项ID","result": "normal","description": "描述"}]'}
        SpotCheckLine = http.request.env(user=workcenter.now_user_id.id)['roke.mes.eqpt.spot.check.line']
        SpotCheckRecord = http.request.env(user=workcenter.now_user_id.id)['roke.mes.eqpt.spot.check.record']
        for check_item in check_items:
            item_id = check_item.get("item_id")
            result = check_item.get("result")
            description = check_item.get("description")
            file = check_item.get("file")  # TODO 处理故障&异常照片保存
            check_line = SpotCheckLine.browse(int(item_id))
            check_line.write({
                "result": result,
                "description": description
            })
            SpotCheckRecord += check_line.record_id
        SpotCheckRecord.write({"state": "finish", "finish_time": fields.Datetime.now(), "finish_user_id": workcenter.now_user_id.id})
        return {"state": "success", "msgs": "操作成功"}
    
    @http.route('/roke/get/equipment/maintenance_info', type='json', methods=['POST', 'OPTIONS'], auth='none', cors='*', csrf=False)
    def get_equipment_maintenance_info(self):
        """
        获取设备点检，保养，维修，更换件记录，设备当天生产合格数
        入参: {"equipment_id": 设备ID}
        返回: {
            "state": "success",
            "msgs": "获取成功",
            "equipment_maintenance_list": [...]
        }
        """
        req = http.request.jsonrequest
        equipment_id = req.get("equipment_id")
        domain = [("code", "!=", False)]
        if equipment_id:
            domain.append(("id", "=", equipment_id))
        today = fields.Date.today()
        tomorrow = today + timedelta(days=1)
        week_start = today - timedelta(days=today.weekday())
        week_end = week_start + timedelta(days=7)
        equipment_ids = http.request.env["roke.mes.equipment"].sudo().search(domain)
        equipment_maintenance_list = []
        for equipment_id in equipment_ids:
            check_record_id = http.request.env["roke.mes.eqpt.spot.check.record"].sudo().search([
                ("equipment_id", "=", equipment_id.id),
                ("create_date", ">=", datetime.combine(today, datetime.min.time())),
                ("create_date", "<", datetime.combine(tomorrow, datetime.min.time()))
            ], limit=1)
            MaintenanceRecord = http.request.env["roke.mes.maintenance.order"].sudo()
            maintain_order_id = MaintenanceRecord.search([
                ("equipment_id", "=", equipment_id.id),
                ("create_date", ">=", datetime.combine(week_start, datetime.min.time())),
                ("create_date", "<", datetime.combine(week_end, datetime.min.time())),
                ("type", "=", "maintain")
            ], limit=1)
            repair_id = MaintenanceRecord.search([
                ("equipment_id", "=", equipment_id.id),
                ("report_time", ">=", datetime.combine(today, datetime.min.time())),
                ("report_time", "<", datetime.combine(tomorrow, datetime.min.time())),
                ("type", "=", "repair")
            ], limit=1)
            ChangeRecord = http.request.env["roke.mes.equipment.change.record"].sudo()
            change_record_ids = ChangeRecord.search([
                ("maintenance_order_id", "=", repair_id.id),
            ])
            equipment_maintenance_list.append({
                "equipment_id": equipment_id.id,
                "equipment_code": equipment_id.code,
                "equipment_name": equipment_id.name,
                "check_info": self.get_today_spot_check(check_record_id),
                "maintain_info": self.get_this_week_maintenance(maintain_order_id),
                "repair_info": self.get_equipment_repair(repair_id, MaintenanceRecord),
                "change_info": self.get_equipment_change(change_record_ids, ChangeRecord),
                "finish_qty": self.get_equipment_today_finish_qty(equipment_id, today, tomorrow)
            })
        return {"state": "success", "msgs": "获取成功", "equipment_maintenance_list": equipment_maintenance_list}

    def get_equipment_today_finish_qty(self, equipment_id, today, tomorrow):
        """获取设备当天生产合格数"""
        finish_qty = sum(http.request.env["roke.work.record"].sudo().search([("work_center_id", "=", equipment_id.work_center_id.id), 
                                                            ("work_time", ">=", datetime.combine(today, datetime.min.time())), 
                                                            ("work_time", "<", datetime.combine(tomorrow, datetime.min.time()))]).mapped("finish_qty"))
        return finish_qty
            
    def get_equipment_change(self, change_record_ids, ChangeRecord):
        """
        获取设备更换件记录
        """
        
        status = "no_task"  # 无更换
        detail = {}

        if change_record_ids:
            # 有任务，判断状态
            status = "has_task"  # 已更换
            detail = {
                "record_date": change_record_ids[0].record_date,  # 最近更换时间
                "removed_part": ".".join(change_record_ids.filtered(lambda x: x.name).mapped("name")),  # 拆下备件
                "installed_part": ".".join(change_record_ids.filtered(lambda x: x.new_name).mapped("new_name")),  # 换上备件
                "change_time": ChangeRecord.search_count([("e_id", "=", change_record_ids[0].e_id.id)])  # 更换次数
            }
        else:
            # 无任务时的基本信息
            detail = {
                "record_date": "",  # 最近更换时间
                "removed_part": "",  # 拆下备件
                "installed_part": "",  # 换上备件
                "change_time": 0  # 更换次数
            }

        return {
                "status": status,
                "detail": detail
            }

    def get_equipment_repair(self, repair_id, MaintenanceRecord):
        """
        获取设备维修记录
        """
        
        status = "no_task"  # 无维修
        detail = {}

        if repair_id:
            # 有任务，判断状态

            if repair_id.state == "finish":
                status = "finished"  # 已完成
            else:
                status = "in_progress"  # 维修中

            change_record_ids = http.request.env["roke.mes.equipment.change.record"].sudo().search([("maintenance_order_id", "=", record.id)])
            detail = {
                "equipment_name": repair_id.equipment_id.name,  # 设备名称
                "last_maintenance_time": repair_id.report_time and str(repair_id.report_time) or "",  # 最近保养时间
                "repair_time": MaintenanceRecord.search_count([("equipment_id", "=", repair_id.equipment_id.id)]),  # 维修次数
                "repair_user": repair_id.repair_user_id.name if repair_id and repair_id.repair_user_id else "",  # 维修人
                "removed_part": ".".join(change_record_ids.mapped("name")),  # 拆下备件
                "installed_part": ".".join(change_record_ids.mapped("new_name")),  # 换上备件
            }
        else:
            # 无任务时的基本信息
            detail = {
                "last_maintenance_time": "",  # 最近保养时间
                "repair_time": "",  # 维修次数
                "repair_user": "",  # 维修人
                "removed_part": "",  # 拆下备件
                "installed_part": "",  # 换上备件
            }

        return {
            "status": status,
            "detail": detail
        }
    
    def get_today_spot_check(self, check_record_id):
        """
        获取设备当天的点检记录及状态
        """
        
        status = "no_task"  # 无任务
        detail = {}
        # 有任务，判断状态
        if check_record_id.state == "finish":
            status = "finished"  # 已完成
        else:
            # 检查是否超时
            now = fields.Datetime.now()
            if check_record_id.estimated_completion_time and now > check_record_id.estimated_completion_time:
                status = "timeout"  # 超时
            elif check_record_id.item_record_ids.filtered(lambda x: x.result != False):
                status = "in_progress"  # 进行中
            else:
                status = "not_started"  # 未开始

        # 组装详细信息
        spot_items = []
        for line in check_record_id.item_record_ids:
            spot_items.append(line.check_item_id.name)
        detail = {
            "last_spot_check_time": check_record_id.start_date and str(check_record_id.start_date) or "",  # 最近点检时间
            "spot_check_plan": check_record_id.check_plan_id.name if check_record_id.check_plan_id else "",  # 点检方案
            "spot_check_items": ",".join(spot_items),  # 点检项目
            "spot_check_result": "normal",  # 点检结果
        }

        # 判断是否有异常/故障
        if check_record_id.normal_state == "abnormal":
            repair_order_id = http.request.env["roke.mes.maintenance.order"].sudo().search([("spot_check_record_id", "=", record.id)], limit=1)
            change_record_id = http.request.env["roke.mes.equipment.change.record"].sudo().search([("maintenance_order_id", "=", repair_order_id.id)], limit=1)
            # 维修相关字段
            detail.update({
                "repair_status": http_tool.get_selection_field_values(repair_order_id, "state"),  # 维修状态
                "repair_user": repair_order_id.repair_user_id.name if repair_order_id.repair_user_id else "",  # 维修人
                "repair_finish_time": repair_order_id.finish_time and str(repair_order_id.finish_time) or "",  # 维修完成时间
                "removed_part": change_record_id.name or "",  # 拆下备件
                "installed_part": change_record_id.new_name or "",  # 换上备件
                "spot_check_result": "abnormal"  # 点检结果
            })
        return {
                "status": status,
                "detail": detail
            }
    
    def get_this_week_maintenance(self, maintain_order_id):
        """
        获取指定设备本周的保养记录及状态
        """
        
        status = "no_task"  # 无任务
        detail = {}

        if maintain_order_id:
            # 有任务，判断状态
            now = fields.Datetime.now()
            if maintain_order_id.state == "finish":
                status = "finished"  # 已完成
            elif maintain_order_id.estimated_completion_time and now > maintain_order_id.estimated_completion_time:
                status = "timeout"  # 超时
            elif maintain_order_id.item_ids.filtered(lambda x: x.state != "wait"):
                status = "in_progress"  # 进行中
            else:
                status = "not_started"  # 未开始

            # 组装详细信息
            maintenance_items = []
            for line in maintain_order_id.item_ids:
                maintenance_items.append(line.item_id.name)

            detail = {
                "last_maintenance_time": maintain_order_id.report_time and str(maintain_order_id.report_time) or "",  # 最近保养时间
                "maintenance_plan_name": maintain_order_id.maintenance_scheme_id.name if maintain_order_id.maintenance_scheme_id else "",  # 保养方案名称
                "maintenance_items": ",".join(maintenance_items),  # 保养项目
                "maintenance_result": "normal",  # 保养结果
            }

            # 判断是否有异常/故障，需要显示维修相关字段
            if maintain_order_id.normal_state == "abnormal":
                change_record_ids = http.request.env["roke.mes.equipment.change.record"].sudo().search([("maintenance_order_id", "=", maintain_order_id.id)])

                # 维修相关字段（只有保养结果异常时才显示）
                detail.update({
                    "maintenance_result": "abnormal",  # 保养结果
                    "repair_status": http_tool.get_selection_field_values(maintain_order_id, "state") if maintain_order_id else "",  # 维修状态
                    "repair_user": maintain_order_id.repair_user_id.name if maintain_order_id and maintain_order_id.repair_user_id else "",  # 维修人
                    "repair_finish_time": maintain_order_id.finish_time and str(maintain_order_id.finish_time) or "" if maintain_order_id else "",  # 维修完成时间
                    "removed_part": ".".join(change_record_ids.mapped("name")),  # 拆下备件
                    "installed_part": ".".join(change_record_ids.mapped("new_name")),  # 换上备件
                })
        else:
            # 无任务时的基本信息
            detail = {
                "last_maintenance_time": "",  # 最近保养时间
                "maintenance_plan_name": "",  # 保养方案名称
                "maintenance_items": "",  # 保养项目
                "maintenance_result": "normal",  # 保养结果
            }

        return {
            "status": status,
            "detail": detail
        }
    
    @http.route('/roke/spare_part/list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_spare_part_list(self):
        """
        获取备件数据列表
        :param name: 备件名称（可选，支持模糊搜索）
        :param page: 页码（可选，默认1）
        :param page_size: 每页数量（可选，默认10）
        :return: 备件列表数据
        """
        try:
            # 获取请求参数
            name = http.request.jsonrequest.get('name', '')
            page = int(http.request.jsonrequest.get('page', 1))
            page_size = int(http.request.jsonrequest.get('page_size', 10))

            # 构建查询条件
            domain = []
            if name:
                domain.append(('name', 'ilike', name))

            # 查询备件总数
            total_count = http.request.env['roke.spare.part'].search_count(domain)

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询备件数据
            spare_parts = http.request.env['roke.spare.part'].search(
                domain,
                limit=page_size,
                offset=offset,
                order='create_date desc'
            )

            # 组装返回数据
            spare_part_list = []
            for spare_part in spare_parts:
                # 处理图片数据
                image_url = ""
                if spare_part.image:
                    # 生成图片访问URL
                    image_url = f"/web/image/roke.spare.part/{spare_part.id}/image"

                # 格式化理论寿命显示
                theoretical_life_display = ""
                if spare_part.theoretical_life:
                    unit_name = "年" if spare_part.life_unit == "year" else "月"
                    theoretical_life_display = f"{spare_part.theoretical_life}{unit_name}"

                spare_part_list.append({
                    "id": spare_part.id,
                    "name": spare_part.name or "",
                    "code": spare_part.code or "",
                    "model": spare_part.model or "",
                    "manufacturer": spare_part.manufacturer or "",
                    "theoretical_life": spare_part.theoretical_life or 0,
                    "life_unit": spare_part.life_unit or "",
                    "theoretical_life_display": theoretical_life_display,
                    "usage_count": spare_part.usage_record_count or 0,
                    "image_url": image_url,
                    "uom_name": spare_part.uom_id.name if spare_part.uom_id else "",
                    "note": spare_part.note or ""
                })

            # 计算总页数
            total_pages = math.ceil(total_count / page_size) if total_count > 0 else 1

            return {
                "state": "success",
                "msgs": "获取成功",
                "data": {
                    "spare_parts": spare_part_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total_count": total_count,
                        "total_pages": total_pages,
                        "has_next": page < total_pages,
                        "has_prev": page > 1
                    }
                }
            }

        except Exception as e:
            _logger.error(f"获取备件列表失败: {str(e)}")
            return {
                "state": "error",
                "msgs": f"获取备件列表失败: {str(e)}"
            }

    @http.route('/roke/spare_part/usage_records', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_spare_part_usage_records(self):
        """
        根据备件ID查询备件使用记录
        :param spare_part_id: 备件ID（必填）
        :param page: 页码（可选，默认1）
        :param page_size: 每页数量（可选，默认10）
        :return: 备件使用记录列表
        """
        try:
            # 获取请求参数
            spare_part_id = http.request.jsonrequest.get('spare_part_id')
            page = int(http.request.jsonrequest.get('page', 1))
            page_size = int(http.request.jsonrequest.get('page_size', 10))

            # 验证必填参数
            if not spare_part_id:
                return {
                    "state": "error",
                    "msgs": "缺少必传参数: spare_part_id"
                }

            # 构建查询条件
            domain = [('spare_part_id', '=', int(spare_part_id))]

            # 查询使用记录总数
            total_count = http.request.env['roke.spare.part.usage.record'].search_count(domain)

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询使用记录数据
            usage_records = http.request.env['roke.spare.part.usage.record'].search(
                domain,
                limit=page_size,
                offset=offset,
                order='replacement_time desc'
            )

            # 组装返回数据
            usage_record_list = []
            for record in usage_records:
                # 处理时间格式化（加8小时时区转换）
                replacement_time = ""
                if record.replacement_time:
                    replacement_time = (record.replacement_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')

                expiry_time = ""
                if record.expiry_time:
                    expiry_time = (record.expiry_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M:%S')

                usage_record_list.append({
                    "id": record.id,
                    "equipment_id": record.equipment_id.id if record.equipment_id else None,
                    "equipment_name": record.equipment_id.name if record.equipment_id else "",
                    "removed_part_id": record.removed_part_id.id if record.removed_part_id else None,
                    "removed_part_name": record.removed_part_id.name if record.removed_part_id else "",
                    "replacement_time": replacement_time,
                    "expiry_time": expiry_time,
                    "remaining_days": record.remaining_days or 0,
                    "usage_days": record.usage_days or 0,
                })

            # 计算总页数
            total_pages = math.ceil(total_count / page_size) if total_count > 0 else 1

            return {
                "state": "success",
                "msgs": "获取成功",
                "data": {
                    "usage_records": usage_record_list,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total_count": total_count,
                        "total_pages": total_pages,
                        "has_next": page < total_pages,
                        "has_prev": page > 1
                    }
                }
            }

        except ValueError:
            return {
                "state": "error",
                "msgs": "参数格式错误，spare_part_id必须为整数"
            }
        except Exception as e:
            _logger.error(f"获取备件使用记录失败: {str(e)}")
            return {
                "state": "error",
                "msgs": f"获取备件使用记录失败: {str(e)}"
            }

    @http.route('/roke/spare_part/uom_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_uom_list(self):
        """
        获取计量单位列表
        :return: 计量单位列表数据
        """
        try:
            # 查询所有计量单位
            uom_list = http.request.env['roke.uom'].search([])

            # 组装返回数据
            uom_data = []
            for uom in uom_list:
                uom_data.append({
                    "id": uom.id,
                    "name": uom.name,
                    "note": uom.note or ""
                })

            return {
                "state": "success",
                "msgs": "获取成功",
                "data": uom_data
            }

        except Exception as e:
            _logger.error(f"获取计量单位列表失败: {str(e)}")
            return {
                "state": "error",
                "msgs": f"获取计量单位列表失败: {str(e)}"
            }

    @http.route('/roke/spare_part/create', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_spare_part(self):
        """
        创建备件
        :param name: 备件名称（必填，最大20字符）
        :param theoretical_life: 理论寿命（必填，整数）
        :param life_unit: 寿命单位（必填，month/year）
        :param manufacturer: 厂家（可选，最大20字符）
        :param model: 型号（可选，最大20字符）
        :param uom_id: 单位ID（必填）
        :param image: 备件图片（可选，base64编码）
        :return: 创建结果
        """
        try:
            # 获取请求参数
            name = http.request.jsonrequest.get('name', '').strip()
            theoretical_life = http.request.jsonrequest.get('theoretical_life')
            life_unit = http.request.jsonrequest.get('life_unit', 'month')
            manufacturer = http.request.jsonrequest.get('manufacturer', '').strip()
            model = http.request.jsonrequest.get('model', '').strip()
            uom_id = http.request.jsonrequest.get('uom_id')
            image = http.request.jsonrequest.get('image')

            # 验证必填参数
            if not name:
                return {
                    "state": "error",
                    "msgs": "备件名称不能为空"
                }

            if len(name) > 20:
                return {
                    "state": "error",
                    "msgs": "备件名称不能超过20个字符"
                }

            if theoretical_life is None:
                return {
                    "state": "error",
                    "msgs": "理论寿命不能为空"
                }

            # 验证理论寿命为正整数
            try:
                theoretical_life = int(theoretical_life)
                if theoretical_life <= 0:
                    return {
                        "state": "error",
                        "msgs": "理论寿命必须为正整数"
                    }
            except (ValueError, TypeError):
                return {
                    "state": "error",
                    "msgs": "理论寿命必须为整数"
                }

            if life_unit not in ['month', 'year']:
                return {
                    "state": "error",
                    "msgs": "寿命单位只能选择月或年"
                }

            if not uom_id:
                return {
                    "state": "error",
                    "msgs": "计量单位不能为空"
                }

            # 验证计量单位是否存在
            uom = http.request.env['roke.uom'].browse(int(uom_id))
            if not uom.exists():
                return {
                    "state": "error",
                    "msgs": "选择的计量单位不存在"
                }

            # 验证可选参数长度
            if manufacturer and len(manufacturer) > 20:
                return {
                    "state": "error",
                    "msgs": "厂家名称不能超过20个字符"
                }

            if model and len(model) > 20:
                return {
                    "state": "error",
                    "msgs": "型号不能超过20个字符"
                }

            # 准备创建数据
            vals = {
                'name': name,
                'theoretical_life': theoretical_life,
                'life_unit': life_unit,
                'uom_id': uom_id,
            }

            # 添加可选字段
            if manufacturer:
                vals['manufacturer'] = manufacturer
            if model:
                vals['model'] = model
            if image:
                vals['image'] = image

            # 创建备件记录
            spare_part = http.request.env['roke.spare.part'].create(vals)

            # 返回创建成功的数据
            return {
                "state": "success",
                "msgs": "备件创建成功",
                "data": {
                    "id": spare_part.id,
                    "name": spare_part.name,
                    "code": spare_part.code,
                    "model": spare_part.model or "",
                    "manufacturer": spare_part.manufacturer or "",
                    "theoretical_life": spare_part.theoretical_life,
                    "life_unit": spare_part.life_unit,
                    "uom_name": spare_part.uom_id.name if spare_part.uom_id else ""
                }
            }

        except ValueError as e:
            return {
                "state": "error",
                "msgs": "参数格式错误"
            }
        except Exception as e:
            _logger.error(f"创建备件失败: {str(e)}")
            return {
                "state": "error",
                "msgs": f"创建备件失败: {str(e)}"
            }

    @http.route('/roke/spare_part/update', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def update_spare_part(self):
        """
        编辑备件信息
        :param spare_part_id: 备件ID（必填）
        :param name: 备件名称（可选，最大20字符）
        :param theoretical_life: 理论寿命（可选，整数）
        :param life_unit: 寿命单位（可选，month/year）
        :param manufacturer: 厂家（可选，最大20字符）
        :param model: 型号（可选，最大20字符）
        :param uom_id: 单位ID（可选）
        :param image: 备件图片（可选，base64编码）
        :return: 更新结果
        """
        try:
            # 获取请求参数
            spare_part_id = http.request.jsonrequest.get('spare_part_id')
            name = http.request.jsonrequest.get('name', '').strip() if http.request.jsonrequest.get('name') else None
            theoretical_life = http.request.jsonrequest.get('theoretical_life')
            life_unit = http.request.jsonrequest.get('life_unit')
            manufacturer = http.request.jsonrequest.get('manufacturer', '').strip() if http.request.jsonrequest.get('manufacturer') else None
            model = http.request.jsonrequest.get('model', '').strip() if http.request.jsonrequest.get('model') else None
            uom_id = http.request.jsonrequest.get('uom_id')
            image = http.request.jsonrequest.get('image')

            # 验证必填参数
            if not spare_part_id:
                return {
                    "state": "error",
                    "msgs": "备件ID不能为空"
                }

            # 查找备件记录
            spare_part = http.request.env['roke.spare.part'].browse(int(spare_part_id))
            if not spare_part.exists():
                return {
                    "state": "error",
                    "msgs": "备件记录不存在"
                }

            # 准备更新数据
            vals = {}

            # 验证并设置备件名称
            if name is not None:
                if not name:
                    return {
                        "state": "error",
                        "msgs": "备件名称不能为空"
                    }
                if len(name) > 20:
                    return {
                        "state": "error",
                        "msgs": "备件名称不能超过20个字符"
                    }
                vals['name'] = name

            # 验证并设置理论寿命
            if theoretical_life is not None:
                try:
                    theoretical_life = int(theoretical_life)
                    if theoretical_life <= 0:
                        return {
                            "state": "error",
                            "msgs": "理论寿命必须为正整数"
                        }
                    vals['theoretical_life'] = theoretical_life
                except (ValueError, TypeError):
                    return {
                        "state": "error",
                        "msgs": "理论寿命必须为整数"
                    }

            # 验证并设置寿命单位
            if life_unit is not None:
                if life_unit not in ['month', 'year']:
                    return {
                        "state": "error",
                        "msgs": "寿命单位只能选择月或年"
                    }
                vals['life_unit'] = life_unit

            # 验证并设置计量单位
            if uom_id is not None:
                uom = http.request.env['roke.uom'].browse(int(uom_id))
                if not uom.exists():
                    return {
                        "state": "error",
                        "msgs": "选择的计量单位不存在"
                    }
                vals['uom_id'] = uom_id

            # 验证并设置厂家
            if manufacturer is not None:
                if len(manufacturer) > 20:
                    return {
                        "state": "error",
                        "msgs": "厂家名称不能超过20个字符"
                    }
                vals['manufacturer'] = manufacturer

            # 验证并设置型号
            if model is not None:
                if len(model) > 20:
                    return {
                        "state": "error",
                        "msgs": "型号不能超过20个字符"
                    }
                vals['model'] = model

            # 设置图片
            if image is not None:
                vals['image'] = image

            # 如果没有要更新的字段
            if not vals:
                return {
                    "state": "error",
                    "msgs": "没有要更新的字段"
                }

            # 更新备件记录
            spare_part.write(vals)

            # 返回更新后的数据
            return {
                "state": "success",
                "msgs": "备件信息更新成功",
                "data": {
                    "id": spare_part.id,
                    "name": spare_part.name,
                    "code": spare_part.code,
                    "model": spare_part.model or "",
                    "manufacturer": spare_part.manufacturer or "",
                    "theoretical_life": spare_part.theoretical_life,
                    "life_unit": spare_part.life_unit,
                    "uom_name": spare_part.uom_id.name if spare_part.uom_id else ""
                }
            }

        except ValueError as e:
            return {
                "state": "error",
                "msgs": "参数格式错误"
            }
        except Exception as e:
            _logger.error(f"更新备件信息失败: {str(e)}")
            return {
                "state": "error",
                "msgs": f"更新备件信息失败: {str(e)}"
            }
