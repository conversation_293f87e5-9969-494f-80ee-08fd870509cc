<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<!--        <record id="ir_cron_execute_scheme" model="ir.cron">-->
<!--            <field name="name">生成设备保养任务</field>-->
<!--            <field name="model_id" ref="model_roke_mes_maintenance_scheme"/>-->
<!--            <field name="state">code</field>-->
<!--            <field name="code">model.check_scheme_date()</field>-->
<!--            <field name="interval_number">1</field>-->
<!--            <field name="interval_type">days</field>-->
<!--            <field name="numbercall">-1</field>-->
<!--            <field name="doall" eval="True"/>-->
<!--            <field name="active" eval="True" />-->
<!--        </record>-->
        <record id="ir_cron_create_equipment_timing" model="ir.cron">
            <field name="name">生成设备计时记录</field>
            <field name="model_id" ref="model_roke_equipment_timing"/>
            <field name="state">code</field>
            <field name="code">model.create_equipment_timing()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="True"/>
            <!--UTC时间16:05，即北京时间凌晨00:05-->
            <field name="nextcall">2025-06-12 16:05:00</field>
            <field name="active" eval="True" />
        </record>
    </data>
</odoo>
