from odoo import api, fields, models


class RokeEquipmentNotifyConfig(models.Model):
    _name = "roke.equipment.notify.config"
    _description = "设备通知配置"

    type = fields.Selection(string="类型", selection=[("保养", "保养"), ("维修", "维修"), ("点检", "点检")], required=True)
    is_notify = fields.Selection([("是", "是"), ("否", "否")], string="是否发送通知", default="否")
    notify_group_id = fields.Many2one("res.groups", string="通知用户组")
    notify_user_ids = fields.Many2many("res.users", string="通知用户")
    roke_app = fields.Boolean(string="融科app")