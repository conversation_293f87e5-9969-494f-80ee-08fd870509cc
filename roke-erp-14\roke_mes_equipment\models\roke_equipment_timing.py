# -*- coding: utf-8 -*-
"""
Description:
    设备计时模型
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, date, timedelta
import requests


class RokeEquipmentTiming(models.Model):
    _name = "roke.equipment.timing"
    _description = '设备计时记录'
    _order = 'record_date desc, id desc'

    # 基本字段
    equipment_id = fields.Many2one(
        'roke.mes.equipment', 
        string='设备', 
        required=True, 
        help="选择要记录计时的设备"
    )
    
    # 计时字段
    startup_hours = fields.Float(
        string='开机工时', 
        digits=(10, 2), 
        default=0.0,
        help="设备开机运行的总工时（小时）"
    )
    processing_hours = fields.Float(
        string='加工工时', 
        digits=(10, 2), 
        default=0.0,
        help="设备实际进行加工作业的工时（小时）"
    )
    idle_hours = fields.Float(
        string='空闲工时', 
        compute='_compute_idle_hours', 
        store=True,
        digits=(10, 2),
        help="设备开机但未进行加工的空闲时间（开机工时 - 加工工时）"
    )
    
    # 时间字段
    record_date = fields.Date(
        string='记录日期', 
        required=True, 
        default=fields.Date.context_today,
        help="计时记录的日期"
    )
    
    utilization_rate = fields.Float(
        string='设备利用率(%)', 
        compute='_compute_utilization_rate',
        store=True,
        digits=(5, 2),
        help="加工工时占开机工时的百分比"
    )
    
    # 关联字段
    company_id = fields.Many2one(
        'res.company', 
        string='公司', 
        default=lambda self: self.env.company
    )

    @api.model
    def create_equipment_timing(self):
        """创建设备计时记录"""
        equipment_ids = self.env["roke.mes.equipment"].search([("code", "!=", False)])
        yesterday = date.today() - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')
        for item in equipment_ids:
            url = f"https://dws-platform.xbg.rokeris.com/dev-api/public/device/state/changes/{item.code}/{yesterday_str}/{yesterday_str}"
            response = requests.get(url)
            if response.status_code == 200:
                res = response.json()
                if res["code"] == 200:
                    data = res.get("data", {})
                    changes = data.get("changes", {})
                    yesterday_data = changes.get(yesterday_str, [])
                    yellow = sum([i["duration_seconds"] for i in yesterday_data if i["state"] == "yellow"])
                    green = sum([i["duration_seconds"] for i in yesterday_data if i["state"] == "green"])
                    red = sum([i["duration_seconds"] for i in yesterday_data if i["state"] == "red"])
                    vals = {
                        "equipment_id": item.id,
                        "startup_hours": round((red + yellow + green) / 3600, 2),
                        "processing_hours": round(green / 3600, 2),
                        "record_date": yesterday
                    }
                    self.create(vals)

    @api.depends('startup_hours', 'processing_hours')
    def _compute_idle_hours(self):
        """计算空闲工时"""
        for record in self:
            record.idle_hours = max(0, record.startup_hours - record.processing_hours)

    @api.depends('startup_hours', 'processing_hours')
    def _compute_utilization_rate(self):
        """计算设备利用率"""
        for record in self:
            if record.startup_hours > 0:
                record.utilization_rate = (record.processing_hours / record.startup_hours)
            else:
                record.utilization_rate = 0.0
