# -*- coding: utf-8 -*-
"""
Description:
    设备验收单
Versions:
    Created by www.rokedata.com
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import qrcode
import io
import base64


class RokeMesEquipmentAcceptance(models.Model):
    _name = "roke.mes.equipment.acceptance"
    _inherit = ['mail.thread']
    _description = '设备验收单'
    _order = 'create_date desc'
    _rec_name = "code"

    equipment_name = fields.Char(string='设备名称', required=True, track_visibility='onchange')
    supplier_id = fields.Many2one("roke.partner", string="供货商")
    contact_information = fields.Char(string="联系方式")
    acceptance_date = fields.Date(string="验收时间", default=fields.Date.today())

    price = fields.Float(string="设备单价")
    category_id = fields.Many2one('roke.mes.equipment.category', string='设备类别', track_visibility='onchange')
    entry_date = fields.Date(string="进厂时间", default=fields.Date.today())
    manufacturer_code = fields.Char(string="厂商编号")

    specification = fields.Char(string="规格型号")

    code = fields.Char(string="验收单单号", required=True, index=True, tracking=True, copy=False, default="/")
    note = fields.Text(string='备注')
    state = fields.Selection([('草稿', '草稿'), ('验收完成', '验收完成')], string="状态", default='草稿', track_visibility='onchange')
    file_ids = fields.Many2many("ir.attachment", "roke_equipment_acceptance_attachments_rel", string="附件")

    # 外包装信息
    is_outer_packaging_intact = fields.Selection([('是', '是'), ('否', '否')], default='是', string="外包装是否完好")
    handling_suggestion = fields.Text(string="处理意见")

    # 附属设备信息
    auxiliary_equipment_lines = fields.One2many("roke.auxiliary.equipment.line", "auxiliary_id", string="附属设备信息")

    # 配件信息
    spare_part_ids = fields.Many2many(string="备件信息", comodel_name="roke.spare.part",
                                               relation="equipment_acceptance_spare_part_rel", column1="equipment_acceptance_id",
                                               column2="spare_part_id")

    # 确认按钮
    def action_confirm(self):
        if self.state == '草稿':
            auxiliary_equipment_lines = []
            for aux in self.auxiliary_equipment_lines:
                auxiliary_equipment_lines.append(
                    (0, 0, {
                        "equipment_name": aux.equipment_name,
                        "specification": aux.specification,
                        "uom_id": aux.uom_id.id,
                        "acceptance_qty": aux.acceptance_qty,
                        "note": aux.note
                    })
                )
            self.env['roke.mes.equipment'].create({
                "name": self.equipment_name,
                "specification": self.specification,
                "code": '',
                "category_id": self.category_id.id,
                "active": True,
                "location": '',
                "user_id": False,
                "price": self.price,
                "auxiliary_equipment_lines": auxiliary_equipment_lines,
                "spare_part_ids": [(6, 0, self.spare_part_ids.ids)],
                "is_outer_packaging_intact": self.is_outer_packaging_intact,
                "handling_suggestion": self.handling_suggestion,
                "note": self.note,
                "file_ids": [(6, 0, self.file_ids.ids)]
            })
            self.state = '验收完成'
        else:
            raise UserError("该设备已完成验收,请勿重复验收!")

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.equipment.acceptance.code')
        return super(RokeMesEquipmentAcceptance, self).create(vals)

class RokeAuxiliaryEquipmentLine(models.Model):
    _name = "roke.auxiliary.equipment.line"
    _description = '附属设备信息'

    auxiliary_id = fields.Many2one("roke.mes.equipment.acceptance", string="设备验收单", required=True, ondelete='cascade')
    equipment_name = fields.Char(string='设备名称', required=True, track_visibility='onchange')
    specification = fields.Char(string="规格型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    acceptance_qty = fields.Integer(string="验收数量")
    note = fields.Text(string='备注')


class RokeAccessoryLine(models.Model):
    _name = "roke.accessory.line"
    _description = '配件信息'

    accessory_id = fields.Many2one("roke.mes.equipment.acceptance", string="设备验收单", required=True, ondelete='cascade')
    accessory_name = fields.Char(string='配件名称', required=True, track_visibility='onchange')
    specification = fields.Char(string="规格型号")
    uom_id = fields.Many2one("roke.uom", string="单位")
    acceptance_qty = fields.Integer(string="验收数量")
    note = fields.Text(string='备注')