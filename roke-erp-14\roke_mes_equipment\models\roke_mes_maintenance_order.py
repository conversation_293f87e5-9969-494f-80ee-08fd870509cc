# -*- coding: utf-8 -*-
"""
Description:
    设备维修保养
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import datetime


class RokeMesMaintenanceOrder(models.Model):
    _name = "roke.mes.maintenance.order"
    _description = "维保任务单"
    _inherit = ['mail.thread']
    _rec_name = "code"
    _order = "finished, id desc"

    code = fields.Char(string="编号", default="新建")
    type = fields.Selection([("repair", "维修单"), ("maintain", "保养单")], string="单据类型", default="repair")
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备")
    equipment_code = fields.Char(related="equipment_id.code", string="设备编号", store=True)
    user_id = fields.Many2one("res.users", string="指派人")
    state = fields.Selection([("wait", "待派工"), ("assign", "已派工"), ("finish", "完成"), ("postpone", "延期"), ("cancel", "取消")], string="状态", default="wait")
    priority = fields.Selection([('low', '低'), ('normal', '中'), ('high', '高'), ('urgent', '急')], string="紧急程度", default="normal")
    deadline = fields.Date(string="最后期限")
    postpone_description = fields.Text(string="延期说明")
    cancel_description = fields.Text(string="取消说明")
    finished = fields.Boolean(string="已完成")
    finish_time = fields.Datetime(string="完成时间")
    # 保养
    maintenance_scheme_id = fields.Many2one("roke.mes.maintenance.scheme", string="保养方案")
    last_maintenance_date = fields.Date(string="上次保养日期")
    item_ids = fields.One2many("roke.mes.maintenance.order.item", "order_id", string="保养项目")
    note = fields.Text(string="备注")
    # 维修
    fault_description = fields.Text(string="故障描述")
    fault_files = fields.Many2many("ir.attachment", "roke_fault_attachments_rel", string="故障附件")
    report_time = fields.Datetime(string="报修时间", default=fields.Datetime.now)
    report_user_id = fields.Many2one("res.users", string="报修人", default=lambda self: self.env.user.id)

    repair_description = fields.Text(string="维修描述")
    repair_files = fields.Many2many("ir.attachment", "roke_repair_attachments_rel", string="维修附件")
    repair_user_id = fields.Many2one("res.users", string="维修人")
    special_work_ids = fields.Many2many("roke.mes.special.work", "maintenance_special_order_rel", "mo_id", "sw_id", string="特种作业单")
    special_work_count = fields.Integer(string="特种作业单数量", compute="_compute_special_work_count")
    picture = fields.Binary('图片')  # 报修图片
    repair_picture = fields.Binary('维修图片')  # 维修图片
    # normal_state = fields.Selection([('normal', '正常'), ('abnormal', '异常')], string='是否正常', default='normal')
    fault_id = fields.Many2one('roke.mes.equipment.fault', string='故障类型')
    fault_name = fields.Char(related="fault_id.name", string='故障名称')
    repair_origin = fields.Selection([('normal', '日常报修'), ('maintain', '维保报修'), ('check', '巡检报修')], string='报修来源')
    phone = fields.Char('联系电话')
    e_location = fields.Char('设备地点')
    change = fields.Char('新装配件')
    remove = fields.Char('拆下配件')
    evaluate = fields.Char('维修评价')
    level = fields.Selection([('a', '优'), ('b', '良'), ('c', '差')], string='评价等级')
    worker_is_read = fields.Boolean('已读', default=False)
    dispatcher_is_read = fields.Boolean('已读', default=False)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    use_time = fields.Float(string="维修用时", store=True, compute="_compute_use_time")
    spot_check_record_id = fields.Many2one(string="点检记录", comodel_name="roke.mes.eqpt.spot.check.record")
    estimated_completion_time = fields.Datetime(string="预计完成时间")
    start_date = fields.Datetime(string="开始时间")

    origin_project_id = fields.Many2one("roke.mes.equipment.maintenance.project", string="来源")

    normal_state = fields.Selection([('normal', '正常'), ('abnormal', '异常')], string='是否正常',
                                    compute='_compute_normal_state', store=True)

    maintain_state = fields.Selection([
        ('unassigned', '待指派'),
        ('not_started', '未开始'),
        ('in_progress', '进行中'),
        ("finish", "完成"),
        ("postpone", "延期"),
        ("cancel", "取消")
    ], string='状态', compute='_compute_maintain_state', store=True,readonly=True )

    @api.depends('user_id', 'item_ids.result','item_ids.state','state')
    def _compute_maintain_state(self):
        for record in self:
            if record.type != "maintain":
                # 只针对保养单生效
                record.maintain_state = False
                continue

            if not record.user_id:
                record.maintain_state = "unassigned"  # 待指派
            else:
                item_count = len(record.item_ids)
                if item_count == 0:
                    record.maintain_state = "not_started"  # 无项目
                else:
                    completed_items = record.item_ids.filtered(lambda i: i.state != "wait")
                    if len(completed_items) == 0:
                        record.maintain_state = "not_started"  # 未开始
                    elif len(completed_items) < item_count:
                        record.maintain_state = "in_progress"  # 进行中
                    else:
                        record.maintain_state = "finish"  # 已完成

            if record.state == "cancel":
                record.maintain_state = "cancel"

            if record.state == "postpone":
                record.maintain_state = "postpone"

            if record.state == "finish":
                record.maintain_state = "finish"

    @api.depends("item_ids",'item_ids.result')
    def _compute_normal_state(self):
        for record in self:
            has_abnormal = any(line.result in ["anomaly", "fault"] for line in self.item_ids)
            record.normal_state = "abnormal" if has_abnormal else "normal"

    @api.depends("report_time", "finish_time")
    def _compute_use_time(self):
        for record in self:
            if record.report_time and record.finish_time:
                record.use_time = (record.finish_time - record.report_time).total_seconds() / 3600

    def set_state_finish(self):
        self.ensure_one()
        if self.type != "repair":
            raise UserError(_("当前单据类型不允许完成"))
        if self.state not in ["assign", "postpone"]:
            raise UserError(_("当前状态不允许完成"))
        self.execute_order("finish")

    def create_equipment_change_record(self):
        self.ensure_one()
        return {
            'name': '更换件记录',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.spare.part.usage.record',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_equipment_id': self.equipment_id.id,
                'default_maintenance_order_id': self.id
            }
        }

    def send_wx_message_maintain(self):
        """保养任务派工"""
        for user in self.user_id:
            hr_ = self.env['hr.employee'].search([('user_id', '=', user.id)])
            data = {
                "model_name": "roke.mes.maintenance.order",
                "res_id": self.id,
                "user_id": self.user.id,
                "user_wx_id": hr_.OpenID or "",
                "send_type": "维保任务提醒"
            }
            self.sudo().env["roke.wx.send.record"].create(data).send_wx()

    def send_wx_message_finish_repair(self):
        """维修完成"""
        # 获取派工人员
        hr_ = self.env['hr.employee'].search([('user_id', '=', self.repair_user_id.id)])
        data = {
            "model_name": "roke.mes.maintenance.order",
            "res_id": self.id,
            "user_id": self.repair_user_id.id,
            "user_wx_id": hr_.OpenID or "",
            "send_type": "维修完成通知"
        }
        self.sudo().env["roke.wx.send.record"].create(data).send_wx()

    def send_wx_message_submit_repair(self):
        """报修推送"""
        # 获取派工人员
        groups = self.env['res.groups'].search([('name', '=', '派工人员'), ('category_id.name', '=', '设备管理')])
        for user in groups.users:
            hr_ = self.env['hr.employee'].search([('user_id', '=', user.id)])
            data = {
                "model_name": "roke.mes.maintenance.order",
                "res_id": self.id,
                "user_id": user.id,
                "user_wx_id": hr_.OpenID or "",
                "send_type": "报修工单通知"
            }
            self.sudo().env["roke.wx.send.record"].create(data).send_wx()

    def send_wx_message(self):
        """派工消息推送"""
        for user in self.user_id:
            hr_ = self.env['hr.employee'].search([('user_id', '=', user.id)])
            data = {
                "model_name": "roke.mes.maintenance.order",
                "res_id": self.id,
                "user_id": user.id,
                "user_wx_id": hr_.OpenID or "",
                "send_type": "工单派工通知"
            }
            self.sudo().env["roke.wx.send.record"].create(data).send_wx()

    def _compute_special_work_count(self):
        for record in self:
            record.special_work_count = len(record.special_work_ids)

    def action_special_work(self):
        if not self.special_work_count:
            return
        elif self.special_work_count == 1:
            return {
                'name': '%s 的特种作业单' % self.display_name,
                'type': 'ir.actions.act_window',
                'res_model': 'roke.mes.special.work',
                'view_mode': 'form',
                'res_id': self.special_work_ids.ids[0],
                'target': 'current'
            }
        else:
            return {
                'name': '%s 的特种作业单' % self.display_name,
                'type': 'ir.actions.act_window',
                'res_model': 'roke.mes.special.work',
                'view_mode': 'tree,form',
                'domain': [("id", "in", self.special_work_ids.ids)],
                'target': 'current'
            }

    def create_special_work_order(self):
        """
        创建特种作业单
        :return:
        """
        return {
            'name': '%s 的特种作业单' % self.display_name,
            'type': 'ir.actions.act_window',
            'res_model': 'roke.mes.special.work',
            'view_mode': 'form',
            'target': 'current',
            'context': {
                'default_maintenance_order_ids': [(6, 0, [self.id])],
                'default_equipment_ids': [(6, 0, [self.equipment_id.id])],
                'default_related_record': '维修' if self.type == 'repair' else '保养'
            }
        }

    @api.onchange("maintenance_scheme_id")
    def _onchange_maintenance_scheme_id(self):
        if self.maintenance_scheme_id:
            _ids_list = []
            for item in self.maintenance_scheme_id.item_ids:
                _item = self.env['roke.mes.maintenance.order.item'].create({
                    "item_id": item.id,
                    'maintenance_description':item.note,
                    "state": "wait"
                })
                _ids_list.append(_item.id)
            return {"value": {
                "last_maintenance_date": self.maintenance_scheme_id.last_maintenance_date,
                "item_ids": [(6, 0, _ids_list)]
            }}
        else:
            return {"value": {
                "item_ids": None
            }}

    @api.model
    def create(self, vals):
        if vals.get("code", "新建") == "新建":
            # if vals.get("type", "repair") == "repair":
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.repair.code')
            # else:
            #     vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.maintain.code')
        res = super(RokeMesMaintenanceOrder, self).create(vals)
        # 为了使附件能在讨论栏预览
        res.fault_files.write({"res_model": "roke.mes.maintenance.order", "res_id": res.id})
        res.repair_files.write({"res_model": "roke.mes.maintenance.order", "res_id": res.id})
        # if not vals.get('equipment_id'):
        #     raise UserError('设备信息不可为空')
        return res

    def write(self, vals):
        res = super(RokeMesMaintenanceOrder, self).write(vals)
        if vals.get("fault_files"):
            for record in self:
                record.fault_files.write({"res_model": "roke.mes.maintenance.order", "res_id": record.id})
        if vals.get("repair_files"):
            for record in self:
                print(record.repair_files)
                record.repair_files.write({"res_model": "roke.mes.maintenance.order", "res_id": record.id})
        return res

    def button_dispatch(self):
        if not self.user_id:
            raise UserError('指派人为空不可派工')
        self.write({"state": "assign"})

    def execute_order_entrance(self):
        # 按钮执行维保单
        state = dict(self.env.context).get("state")
        # 保养单完成时校验保养项目是否全部处理
        if state == "finish" and self.type == "maintain" and self.item_ids.filtered(lambda item: item.state == "wait"):
            raise UserError("保养项目必须全部处理后才可以完成当前保养任务。")
        elif state == "wait":
            self.write({"state":"wait"})
            self.equipment_id.write({"in_repair": True, "e_state": "报修"})  # 设备置为报修状态
            return
        wizard = self.env["roke.execute.maintenance.order.wizard"].create({
            "order_id": self.id,
            "target_state": state,
            "order_type": self.type
        })
        return {
            "type": "ir.actions.act_window",
            "name": "确认执行【%s】操作" % dict(self.sudo().env["roke.mes.maintenance.order"].fields_get(allfields=["state"])["state"]['selection'])[state],
            "view_mode": "form",
            "view_type": "form",
            "res_model": "roke.execute.maintenance.order.wizard",
            'res_id': wizard.id,
            'target': 'new',
        }

    def execute_order(self, state="finish", description=""):
        # 执行维保单
        if state == "postpone":
            write_dict = {"state": state, "postpone_description": description}
        elif state == "cancel":
            write_dict = {"state": state, "cancel_description": description}
        else:
            write_dict = {"state": state, "finished": True, "finish_time": fields.Datetime.now()}

        if self.type == "maintain":
            self.write(write_dict)
            self.maintenance_scheme_id.write({"last_maintenance_date": fields.Date.context_today(self)})
        else:
            if state == "finish":
                write_dict["repair_description"] = description
                write_dict["repair_user_id"] = self.env.user.id
            self.write(write_dict)
            if state in ["cancel", "finish"]:
                if self.equipment_id:
                    self.equipment_id.write({"in_repair": False, "e_state": "在用"})  # 设备恢复正常状态
                else:
                    self.tool_id.write({"state": "draft"})


class RokeMesMaintenanceOrderItem(models.Model):
    _name = "roke.mes.maintenance.order.item"
    _description = "保养任务明细"

    order_id = fields.Many2one("roke.mes.maintenance.order", string="保养单")
    item_id = fields.Many2one("roke.mes.maintenance.item", string="保养项目")
    state = fields.Selection([("wait", "等待"), ("finish", "完成"), ("ignore", "忽略")], string="状态", default="wait")
    execute_user_id = fields.Many2one("res.users", string="操作人",default=lambda self: self.env.user)
    execute_time = fields.Datetime(string="操作时间",default=lambda self: fields.Datetime.now())
    execute_files = fields.Many2many("ir.attachment", string="操作附件")
    description = fields.Text(string="操作内容")
    maintenance_description = fields.Text(string="保养内容")
    picture = fields.Binary('图片')
    is_update = fields.Boolean(string='已修改', default=False)
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    result = fields.Selection([("wait", "未检查"), ("normal", "正常"), ("anomaly", "异常"), ("fault", "故障")],
                              string="结果判定", default="wait")


    @api.onchange("item_id")
    def _onchange_item_id(self):
        for record in self:
            if record.item_id:
                record.maintenance_description = record.item_id.note
            else:
                record.maintenance_description = ""


    def execute_entrance(self):
        # 按钮执行维保项目
        state = dict(self.env.context).get("state")
        if state in ["wait", "ignore"]:
            self.write({"state": state})
            return
        else:
            wizard = self.env["roke.execute.maintenance.item.wizard"].create({
                "order_item_id": self.id,
                "target_state": state
            })
            return {
                "type": "ir.actions.act_window",
                "name": "完成保养",
                "view_mode": "form",
                "view_type": "form",
                "res_model": "roke.execute.maintenance.item.wizard",
                'res_id': wizard.id,
                'target': 'new',
            }

    def execute(self, state="finish", description="",result=""):
        """
        执行保养明细
        :param state:
        :param description:
        :return:
        """
        self.write({
            "state": state,
            "execute_user_id": self.env.user.id,
            "execute_time": fields.Datetime.now(),
            "description": description,
            "result":result,
        })

    def maintenance_item_delete(self):
        self = self.sudo()
        self.unlink()

    def spare_parts_replacement(self):
        self.ensure_one()
        return {
            'name': '更换件记录',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.spare.part.usage.record',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_equipment_id': self.order_id.equipment_id.id,
                'default_maintenance_order_id': self.order_id.id
            }
        }
