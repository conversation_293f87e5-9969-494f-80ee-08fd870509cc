# -*- coding: utf-8 -*-
from odoo import api, fields, models
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta


class RokeSparePartUsageRecord(models.Model):
    _name = "roke.spare.part.usage.record"
    _description = "备件使用记录"
    _order = "replacement_time desc"

    equipment_id = fields.Many2one('roke.mes.equipment', string="针对设备", required=True, help="使用备件的设备")
    spare_part_id = fields.Many2one('roke.spare.part', string="备件", required=True, help="使用的备件")
    removed_part_id = fields.Many2one('roke.spare.part', string="拆下备件", help="被替换下来的备件")
    replacement_time = fields.Datetime(string="更换时间", required=True, default=fields.Datetime.now, help="备件更换的时间")
    expiry_time = fields.Datetime(string="备件寿命到期时间", compute="_compute_expiry_time", store=True, help="根据理论寿命计算的到期时间")
    maintenance_order_id = fields.Many2one('roke.mes.maintenance.order', string="关联维修单", help="关联的维修工单")
    
    # 计算字段
    remaining_days = fields.Integer(string="剩余天数", compute="_compute_remaining_days", help="距离到期的剩余天数")
    usage_days = fields.Integer(string="已使用天数", compute="_compute_usage_days", help="已经使用的天数")
    


    @api.depends('spare_part_id.theoretical_life', 'spare_part_id.life_unit', 'replacement_time')
    def _compute_expiry_time(self):
        """计算备件寿命到期时间"""
        for record in self:
            if record.spare_part_id:
                replacement_time = record.replacement_time
                theoretical_life = record.spare_part_id.theoretical_life
                life_unit = record.spare_part_id.life_unit
                
                if life_unit == 'year':
                    # 按年计算
                    record.expiry_time = replacement_time + relativedelta(years=int(theoretical_life))
                elif life_unit == 'month':
                    # 按月计算
                    record.expiry_time = replacement_time + relativedelta(months=int(theoretical_life))
                else:
                    record.expiry_time = False
            else:
                record.expiry_time = False

    @api.depends('expiry_time')
    def _compute_remaining_days(self):
        """计算剩余天数"""
        for record in self:
            if record.expiry_time:
                now = datetime.now()
                if record.expiry_time > now:
                    delta = record.expiry_time - now
                    record.remaining_days = delta.days
                else:
                    record.remaining_days = 0
            else:
                record.remaining_days = 0

    @api.depends('replacement_time')
    def _compute_usage_days(self):
        """计算已使用天数"""
        for record in self:
            if record.replacement_time:
                now = datetime.now()
                delta = now - record.replacement_time
                record.usage_days = delta.days
            else:
                record.usage_days = 0

    @api.onchange('spare_part_id')
    def _onchange_spare_part_id(self):
        """当选择备件时，自动计算到期时间"""
        if self.spare_part_id and self.replacement_time:
            self._compute_expiry_time()

    @api.onchange('replacement_time')
    def _onchange_replacement_time(self):
        """当更换时间变化时，重新计算到期时间"""
        if self.spare_part_id and self.replacement_time:
            self._compute_expiry_time()
