# -*- coding: utf-8 -*-
from datetime import timedelta, time, datetime, date

from dateutil.relativedelta import relativedelta
from odoo import models, fields, api, _
from odoo.exceptions import UserError,ValidationError
import logging
_logger = logging.getLogger(__name__)

WEEKDAYS = {
    0: "周一",
    1: "周二",
    2: "周三",
    3: "周四",
    4: "周五",
    5: "周六",
    6: "周日"
}
class RokeMesEquipmentCommonInspection(models.Model):
    _name = "roke.mes.equipment.common.inspection"
    _description = "检查计划"
    _inherit = ['mail.thread']
    _order = "id desc"
    _rec_name = "equipment_id"

    code = fields.Char(string="编号", required=True, default="新建")
    equipment_id = fields.Many2one("roke.mes.equipment", string="设备", required=True)
    specification = fields.Char(string="设备型号", related="equipment_id.specification", readonly=True)
    warranty_date = fields.Date(string="保修期截止", related="equipment_id.warranty_date", readonly=True)
    start_date = fields.Datetime(string="生成时间", required=True)
    end_date = fields.Datetime(string="结束时间", required=True)

    start_now_time = fields.Datetime(string="当前执行时间", compute='_compute_start_now_time',readonly=True)
    end_now_time = fields.Datetime(string="当前结束时间", compute='_compute_start_now_time',readonly=True)

    @api.depends('start_date','end_date')
    def _compute_start_now_time(self):
        current_datetime = fields.Datetime.now() + timedelta(hours=8)
        for record in self:
            start_dt = fields.Datetime.from_string(record.start_date + timedelta(hours=8))
            end_dt = fields.Datetime.from_string(record.end_date + timedelta(hours=8))
            start_dt = current_datetime.strftime('%Y-%m-%d') + ' ' + start_dt.time().strftime("%H:%M:%S")
            end_dt = current_datetime.strftime('%Y-%m-%d') + ' ' + end_dt.time().strftime("%H:%M:%S")
            record.start_now_time = start_dt
            record.end_now_time = end_dt

    frequency_type = fields.Selection([
        ("daily", "每天"),
        ("weekly", "每周"),
        ("monthly", "每月")
    ], string="频率类型", default="daily", required=True)
    active = fields.Boolean(default=True,string="启用/禁用")
    def toggle_active(self):
        for record in self:
            record.active = not record.active

    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    ir_cron = fields.Many2one("ir.cron", string="自动任务")

    note = fields.Text(string="说明",compute='_compute_note',readonly=True)
    @api.depends('frequency_type','start_date','end_date')
    def _compute_note(self):
        for record in self:
            start_date = record.start_date + timedelta(hours=8)
            end_date = record.end_date + timedelta(hours=8)
            if record.frequency_type == 'daily':
                record.note = f"""每天{fields.Datetime.to_string(start_date)[11:]}开始,{fields.Datetime.to_string(end_date)[11:]}结束"""

            if record.frequency_type == 'weekly':
                record.note = f"""每周{(WEEKDAYS.get(
                    start_date.weekday())
                )}的{fields.Datetime.to_string(start_date)[11:]}开始,每周{WEEKDAYS.get(end_date.weekday())}的{fields.Datetime.to_string(end_date)[11:]}结束"""

            if record.frequency_type == 'monthly':
                record.note = f"""每月{start_date.day}号的{fields.Datetime.to_string(start_date)[11:]}开始,每月{end_date.day}的{fields.Datetime.to_string(end_date)[11:]}结束"""
            record.note = "执行时间段："+record.note
    @api.constrains('equipment_id')
    def _check_unique_equipment(self):
        for record in self:
            duplicate = self.with_context(active_test=False).search([
                ('id', '!=', record.id),
                ('equipment_id', '=', record.equipment_id.id),
            ], limit=1)
            if duplicate:
                raise ValidationError(_("每个设备只能有一个计划！"))

    def action_view_cron(self):
        """
        跳转到对应的 ir.cron 记录
        """
        self.ensure_one()
        if not self.ir_cron:
            raise UserError(_("该计划尚未关联任何定时任务！"))

        return {
            'type': 'ir.actions.act_window',
            'name': _('关联的定时任务'),
            'res_model': 'ir.cron',
            'res_id': self.ir_cron.id,
            'view_mode': 'form',
            'views': [(False, 'form')],
            'target': 'current',
            'context': self.env.context
        }

    def _compute_next_call(self, now, start_dt, frequency_type):
        """
        计算下一次执行时间 nextcall
        :param now: 当前时间 datetime.datetime
        :param start_dt: 起始时间 datetime.datetime
        :param frequency_type: 频率类型 ('daily', 'weekly', 'monthly')
        :return: 下一个执行时间 datetime.datetime
        """
        if frequency_type == 'daily':
            # 每天固定时间执行
            today_schedule = now.replace(
                hour=start_dt.hour,
                minute=start_dt.minute,
                second=start_dt.second,
                microsecond=0
            )
            if now < today_schedule:
                return today_schedule - timedelta(hours=8)
            else:
                return today_schedule + timedelta(days=1) - timedelta(hours=8)

        elif frequency_type == 'weekly':
            # 每周固定星期几和时间执行
            target_weekday = start_dt.weekday()
            days_ahead = (target_weekday - now.weekday() + 7) % 7 or 7  # 向后推到下周同一天
            next_date = now.replace(
                hour=start_dt.hour,
                minute=start_dt.minute,
                second=start_dt.second,
                microsecond=0
            )
            if now < next_date:
                return next_date - timedelta(hours=8)
            else:
                return next_date + timedelta(days=days_ahead) - timedelta(hours=8)

        elif frequency_type == 'monthly':
            # 每月固定日期和时间执行
            try:
                next_date = now.replace(day=start_dt.day)
            except ValueError:
                # 处理月末无对应日期的情况（例如 2 月没有第 30 号）
                next_date = (now.replace(day=1) + relativedelta(months=1)) \
                    .replace(day=start_dt.day)

            next_datetime = next_date.replace(
                hour=start_dt.hour,
                minute=start_dt.minute,
                second=start_dt.second,
                microsecond=0
            )

            if now < next_datetime:
                return next_datetime -timedelta(hours=8)
            else:
                return next_datetime + relativedelta(months=1) -timedelta(hours=8)

        # 默认 fallback
        return now + timedelta(hours=1)

    def update_ir_cron(self,type="点检"):
        """
        创建或更新定时任务
        """
        frequency_type_dict = {
            'daily': "每天",
            'weekly': "每周",
            'monthly': "每月",
        }
        for record in self:
            if record.ir_cron:
                # 根据频率类型计算下次执行时间
                # 计算首次执行时间
                # 解析 start_date
                start_dt = fields.Datetime.from_string(record.start_date + timedelta(hours=8))
                now = fields.Datetime.now()
                # 计算下一个执行时间
                nextcall = self._compute_next_call(now, start_dt, record.frequency_type)
                record.ir_cron.write({
                    'name': f'设备{type}计划[设备：%s，频率：%s]' % (record.equipment_id.name,frequency_type_dict.get(record.frequency_type)),
                    'interval_number': 1,
                    'interval_type': self._get_interval_type(record.frequency_type),
                    'code': 'model.execute_spot_check_record(%d)' % record.id,
                    'nextcall':nextcall.strftime('%Y-%m-%d %H:%M:%S'),
                    'active':record.active,

                })
            else:
                # 根据频率类型计算下次执行时间
                # 计算首次执行时间
                # 解析 start_date
                start_dt = fields.Datetime.from_string(record.start_date + timedelta(hours=8))
                now = fields.Datetime.now()

                # 计算下一个执行时间
                nextcall = self._compute_next_call(now, start_dt, record.frequency_type)
                cron_id = self.env['ir.cron'].create({
                    'name': f'设备{type}计划[设备：%s，频率：%s]' % (record.equipment_id.name, frequency_type_dict.get(record.frequency_type)),
                    'model_id': self.env['ir.model'].search(
                        [('model', '=', self._name)]).id,
                    'state': 'code',
                    'code': 'model.execute_spot_check_record(%d)' % record.id,
                    'interval_number': 1,
                        'interval_type': self._get_interval_type(record.frequency_type),
                    'numbercall': -1,
                    'doall': True,
                    'nextcall':nextcall.strftime('%Y-%m-%d %H:%M:%S'),
                    'active': record.active,
                })
                record.ir_cron = cron_id

    def _get_interval_type(self, frequency_type):
        """
        根据频率类型返回对应的interval_type
        """
        return {
            'daily': 'days',
            'weekly': 'weeks',
            'monthly': 'months'
        }.get(frequency_type, 'days')



    def unlink(self):
        for record in self:
            if record.ir_cron:
                record.ir_cron.unlink()
        return super(RokeMesEquipmentCommonInspection, self).unlink()


    @api.onchange('frequency_type')
    def _onchange_frequency_type(self):
        today = date.today()
        if self.frequency_type == 'daily':
            now = fields.Datetime.now()
            self.start_date = fields.Datetime.to_string(now.replace(hour=7, minute=0, second=0) -timedelta(hours=8))
            self.end_date = fields.Datetime.to_string(now.replace(hour=10, minute=0, second=0) -timedelta(hours=8))
        elif self.frequency_type == 'weekly':
            weekday = today.weekday()
            monday = today - timedelta(days=weekday)
            sunday = monday + timedelta(days=6)
            self.start_date = fields.Datetime.to_string(datetime.combine(monday, time(7, 0)) - timedelta(hours=8))
            self.end_date = fields.Datetime.to_string(datetime.combine(sunday, time(19, 0)) - timedelta(hours=8))
        elif self.frequency_type == 'monthly':
            first_day = today.replace(day=1)
            last_day = (first_day + relativedelta(months=1)) - timedelta(days=1)
            self.start_date = fields.Datetime.to_string(datetime.combine(first_day, time(7, 0)) -timedelta(hours=8))
            self.end_date = fields.Datetime.to_string(datetime.combine(last_day, time(19, 0)) -timedelta(hours=8))

    @api.constrains('frequency_type', 'start_date', 'end_date')
    def _check_start_end_date(self):
        for record in self:
            start_date = fields.Datetime.from_string(record.start_date +timedelta(hours=8) )
            end_date = fields.Datetime.from_string(record.end_date + timedelta(hours=8))
            today = fields.Date.today()
            current_week_start = today - timedelta(days=today.weekday())  # 本周一
            next_week_start = current_week_start + timedelta(days=7)  # 下周一

            current_month_start = today.replace(day=1)
            next_month_start = (current_month_start + relativedelta(months=1))

            if record.frequency_type == 'daily':
                if start_date.date() != end_date.date():
                    raise ValidationError(_("每天模式下，开始时间和结束时间应该在同一天。"))
                # 检查 start_date 和 end_date 是否都在同一周内
            elif record.frequency_type == 'weekly':
                if not (current_week_start <= start_date.date() < next_week_start and
                            current_week_start <= end_date.date() < next_week_start):
                     raise ValidationError(_("每周模式下，开始时间和结束时间必须在同一个自然周内。"))
            elif record.frequency_type == 'monthly':
                if not (current_month_start <= start_date.date() < next_month_start and
                        current_month_start <= end_date.date() < next_month_start):
                    raise ValidationError(_("每月模式下，开始时间和结束时间必须在同一个自然月内。"))

            if start_date > end_date:
                raise ValidationError(_("开始时间不能晚于结束时间。"))


class RokeMesEquipmentInspectionProject(models.Model):
    _name = "roke.mes.equipment.inspection.project"
    _inherit = "roke.mes.equipment.common.inspection"

    check_user_id = fields.Many2one("res.users", string="点检人", required=True)
    check_plan_id = fields.Many2one("roke.mes.eqpt.spot.check.plan", string="点检方案", required=True)

    order_ids = fields.One2many("roke.mes.eqpt.spot.check.record", string="点检记录",
                                            compute="_compute_order_ids", readonly=True)

    @api.depends("check_plan_id", 'equipment_id')
    def _compute_order_ids(self):
        for rec in self:
            rec.order_ids = self.env['roke.mes.eqpt.spot.check.record'].search(
                [('origin_project_id', '=', rec.id)])

    def action_view_maiantenance_order(self):
        tree = self.env.ref('roke_mes_equipment.view_roke_mes_eqpt_spot_check_record_tree')
        form = self.env.ref('roke_mes_equipment.view_roke_mes_eqpt_spot_check_record_form')
        return {
            'name': _('点检记录'),
            'view_mode': 'tree,form',
            'res_model': 'roke.mes.eqpt.spot.check.record',
            'type': 'ir.actions.act_window',
            'domain': [('origin_project_id', '=', self.id)],
            'views': [(tree.id, 'tree'), (form.id, 'form')],
            'context': {'group_by': 'start_date:day' }
        }

    @api.model
    def create(self, vals):
        vals["code"] = self.env['ir.sequence'].next_by_code('roke.mes.equipment.inspection.project.code')
        record = super(RokeMesEquipmentCommonInspection, self).create(vals)
        record.update_ir_cron()
        return record

    def _create_check_record(self, record, start_dt, end_dt):
        """创建点检记录辅助方法"""

        res = self.env["roke.mes.eqpt.spot.check.record"].create({
            "check_plan_id": record.check_plan_id.id,
            "equipment_id": record.equipment_id.id,
            "assign_user_ids": [(6, 0, [self.env.user.id])],
            "finish_user_id":record.check_user_id.id,
            "state": "not_started",
            "description": "自动点检任务",
            "estimated_completion_time": end_dt - timedelta(hours=8),
            'start_date': start_dt - timedelta(hours=8),
            'normal_state': '',
            'origin_project_id': record.id,
        })

    def execute_spot_check_record(self, res):
        """
        执行点检记录生成（精确到时分秒并按频率重复）
        """
        _logger.info("开始执行点检记录生成，记录ID: %s", res)
        records = self.browse(res)
        # current_datetime = fields.Datetime.now()
        current_datetime = fields.Datetime.now() + timedelta(hours=8)
        _logger.info("当前时间: %s", current_datetime)

        for record in records:
            _logger.info("处理点检计划 ID: %d，设备: %s", record.id, record.equipment_id.name)

            # 获取计划的时间设置（包含时分秒）
            start_dt = fields.Datetime.from_string(record.start_date + timedelta(hours=8))
            end_dt = fields.Datetime.from_string(record.end_date + timedelta(hours=8))
            _logger.info("计划时间范围: %s - %s", start_dt.time(), end_dt.time())
            existing = self.env["roke.mes.eqpt.spot.check.record"].search([
                ("check_plan_id", "=", record.check_plan_id.id),
                ("equipment_id", "=", record.equipment_id.id),
                ('origin_project_id','=',record.id),
                ("create_date", ">=", record.start_now_time - timedelta(hours=8)),
                ("create_date", "<=", record.end_now_time - timedelta(hours=8)),
            ], limit=1)

            if existing:
                _logger.info("该时间段内已存在记录，跳过创建")
                continue
            # 根据频率类型检查是否应该生成记录
            if record.frequency_type == 'daily':
                _logger.info("检查每日频率条件")
                if start_dt.time() <= current_datetime.time() <= end_dt.time():
                    _logger.info("满足每日点检条件，创建点检记录")
                    self._create_check_record(record,record.start_now_time,record.end_now_time)

            elif record.frequency_type == 'weekly':
                _logger.info("检查每周频率条件")
                if (start_dt.weekday() == current_datetime.weekday() and
                        start_dt.time() <= current_datetime.time() <= end_dt.time()):
                    _logger.info("满足每周点检条件，创建点检记录")
                    self._create_check_record(record, record.start_now_time, record.end_now_time)

            elif record.frequency_type == 'monthly':
                _logger.info("检查每月频率条件")
                if (start_dt.day == current_datetime.day and
                        start_dt.time() <= current_datetime.time() <= end_dt.time()):
                    _logger.info("满足每月点检条件，创建点检记录")
                    self._create_check_record(record,record.start_now_time,record.end_now_time)
    def write(self, vals):
        res = super(RokeMesEquipmentInspectionProject, self).write(vals)
        if any(field in vals for field in ['frequency_type', 'check_plan_id', 'equipment_id','start_date','active']):
            self.update_ir_cron()
        return res

    @api.onchange('equipment_id')
    def _onchange_equipment_id(self):
        domain = {'check_plan_id': []}
        if self.equipment_id:
            domain = {'check_plan_id': [('equipment_id', '=', self.equipment_id.id)]}
        return {"domain": domain}