<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- 设备点检计划序列号定义 -->
        <record id="roke_mes_equipment_maintenance_project_code" model="ir.sequence">
            <field name="name">设备保养计划编号</field>
            <field name="code">roke.mes.equipment.maintenance.project.code</field>
            <field name="prefix">eqp-Maintenance-</field>  <!-- 可根据需求调整前缀 -->
            <field name="padding">4</field>  <!-- 数字部分长度 -->
            <field name="company_id" eval="False"/>  <!-- 全局序列号 -->
            <field name="implementation">standard</field>
        </record>
        <record id="view_roke_mes_equipment_maintenance_project_form" model="ir.ui.view">
            <field name="name">roke.mes.equipment.maintenance.project.form</field>
            <field name="model">roke.mes.equipment.maintenance.project</field>
            <field name="arch" type="xml">
                <form string="设备保养计划">
                    <header>
                        <button name="action_view_maiantenance_order" string="查看保养任务"
                                type="object"
                                icon="fa-list-alt"
                                attrs="{'invisible': [('order_ids', '=', [])]}"
                                class="btn-info btn-icon-only">
                         <field name="order_ids" invisible="1"/>
                        </button>

                       <button name="action_view_cron" string="查看定时任务"
                                type="object"
                                icon="fa-clock-o"
                                attrs="{'invisible': [('ir_cron', '=', False)]}"
                                class="btn-info btn-icon-only">

                        </button>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box"/>
                        <widget name="web_ribbon" text="有效" bg_color="bg-success"
                                attrs="{'invisible': [('active', '=', False)]}"/>
                        <widget name="web_ribbon" text="归档" bg_color="bg-danger"
                                attrs="{'invisible': [('active', '=', True)]}"/>

                        <field name="ir_cron" invisible="1"/>


                        <group string="设备基础信息" col="3">
                            <group>
                                <field name="equipment_id" options="{'no_create_edit':1,'no_create':1}"/>
                                <field name="maintenance_scheme_id" options="{'no_create_edit':1,'no_create':1}"/>
                                <field name="priority" options="{'no_create_edit':1,'no_create':1}" required="1"/>

                            </group>
                            <group>
                                <field name="specification" readonly="1"/>
                                <field name="repair_user_id" options="{'no_create_edit':1,'no_create':1}"/>
                            </group>
                            <group>
                                <field name="warranty_date" readonly="1"/>
                                <field name="user_id" options="{'no_create_edit':1,'no_create':1}"/>
                            </group>
                        </group>
                        <group string="保养循环规则" col="3">
                            <group>
                                <field name="frequency_type"
                                       help="例如:每天指的是：在当天时间里设置时间点，之后每天都会生成一个保养记录"/>
                                <field name="active" widget="boolean_toggle" string="启用/禁用"/>
                            </group>
                            <group>
                                <field name="start_date"/>
                            </group>
                            <group>
                                <field name="end_date"/>
                            </group>
                        </group>
                        <group string="说明">
                            <field name="note" nolabel="1"/>
                        </group>
                    </sheet>
                </form>

            </field>
        </record>

        <!-- 设备点检计划列表视图 -->
        <record id="view_roke_mes_equipment_maintenance_project_tree" model="ir.ui.view">
            <field name="name">roke.mes.equipment.maintenance.project.tree</field>
            <field name="model">roke.mes.equipment.maintenance.project</field>
            <field name="arch" type="xml">
                <tree string="设备保养计划">
                    <field name="code" invisible="1"/>
                    <field name="equipment_id"/>
                    <field name="specification"/>
                    <field name="warranty_date"/>
                    <field name="user_id"/>
                    <field name="maintenance_scheme_id"/>
                    <field name="repair_user_id"/>
                    <field name="frequency_type"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                </tree>
            </field>
        </record>

        <record id="view_roke_mes_equipment_maintenance_project_search" model="ir.ui.view">
            <field name="name">roke.mes.equipment.maintenance.project.search</field>
            <field name="model">roke.mes.equipment.maintenance.project</field>
            <field name="arch" type="xml">
                <search string="设备保养计划搜索">
                    <!-- 基础搜索字段 -->
                    <field name="equipment_id"/>
                    <field name="user_id"/>
                    <field name="maintenance_scheme_id"/>
                    <field name="repair_user_id"/>
                    <field name="frequency_type"/>
                    <field name="start_date"/>
                    <field name="end_date"/>

                    <!-- 过滤器分组 -->
                    <filter string="有效" name="active" domain="[('active', '=', True)]"/>
                    <filter string="已归档" name="no_active" domain="[('active', '=', False)]"/>

                    <!-- 分组选项 -->
                    <group expand="0" string="分组">
                        <filter string="按频率类型" name="frequency_type" context="{'group_by': 'frequency_type'}"/>
                        <filter string="指派人" name="user_id" context="{'group_by': 'user_id'}"/>
                        <filter string="保养人" name="user_id" context="{'group_by': 'repair_user_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- 设备点检计划操作动作 -->
        <record id="action_roke_mes_equipment_maintenance_project" model="ir.actions.act_window">
            <field name="name">设备保养计划</field>
            <field name="res_model">roke.mes.equipment.maintenance.project</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="view_roke_mes_equipment_maintenance_project_search"/>
            <field name="context" >{'type':"maintenance"} </field>
            <field name="domain" >['|',('active','=',True),('active','=',False)]</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    点击这里创建新的设备保养计划
                </p>
            </field>
        </record>

    </data>
</odoo>