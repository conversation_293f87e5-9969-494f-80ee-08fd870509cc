<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 设备计时记录 - 列表视图 -->
    <record id="view_roke_equipment_timing_tree" model="ir.ui.view">
        <field name="name">roke.equipment.timing.tree</field>
        <field name="model">roke.equipment.timing</field>
        <field name="arch" type="xml">
            <tree string="设备计时记录" create="0" edit="0" delete="0">
                <field name="equipment_id"/>
                <field name="startup_hours" sum="总开机工时"/>
                <field name="processing_hours" sum="总加工工时"/>
                <field name="utilization_rate" widget="percentage"/>
                <field name="record_date"/>
            </tree>
        </field>
    </record>
    <!-- 设备计时记录 - 搜索视图 -->
    <record id="view_roke_equipment_timing_search" model="ir.ui.view">
        <field name="name">roke.equipment.timing.search</field>
        <field name="model">roke.equipment.timing</field>
        <field name="arch" type="xml">
            <search string="设备计时记录搜索">
                <field name="equipment_id"/>
                <field name="record_date" string="记录日期"/>
                <separator/>
                <filter name="today" string="今天" domain="[('record_date', '=', context_today().strftime('%Y-%m-%d'))]"/>
                <filter name="this_week" string="本周" domain="[('record_date', '&gt;=', (context_today() - datetime.timedelta(days=context_today().weekday())).strftime('%Y-%m-%d')), ('record_date', '&lt;=', (context_today() + datetime.timedelta(days=6-context_today().weekday())).strftime('%Y-%m-%d'))]"/>
                <filter name="this_month" string="本月" domain="[('record_date', '&gt;=', context_today().replace(day=1).strftime('%Y-%m-%d')), ('record_date', '&lt;=', ((context_today().replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <separator/>
            </search>
        </field>
    </record>
    <!-- 设备计时记录 - 动作 -->
    <record id="action_roke_equipment_timing" model="ir.actions.act_window">
        <field name="name">设备计时记录</field>
        <field name="res_model">roke.equipment.timing</field>
        <field name="view_mode">tree</field>
        <field name="search_view_id" ref="view_roke_equipment_timing_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建设备计时记录
            </p>
            <p>
                记录设备的开机工时和加工工时，用于分析设备利用率和生产效率。
            </p>
        </field>
    </record>

</odoo>
