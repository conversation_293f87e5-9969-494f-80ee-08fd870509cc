<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--设备点检记录-->
    <!--search-->
    <record id="view_roke_mes_eqpt_spot_check_record_search" model="ir.ui.view">
        <field name="name">roke.mes.eqpt.spot.check.record.search</field>
        <field name="model">roke.mes.eqpt.spot.check.record</field>
        <field name="arch" type="xml">
            <search string="设备点检记录">
                <field name="check_plan_id"/>
                <field name="equipment_id"/>
                <separator/>
                <filter name="wait" string="等待" domain="[('state', '=', 'not_started')]"/>
                <filter name="finish" string="完成" domain="[('state', '=', 'finish')]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_eqpt_spot_check_record_tree" model="ir.ui.view">
        <field name="name">roke.mes.eqpt.spot.check.record.tree</field>
        <field name="model">roke.mes.eqpt.spot.check.record</field>
        <field name="arch" type="xml">
            <tree string="设备点检记录">
                <field name="code"/>
                <field name="equipment_id"/>
                <field name="check_plan_id"/>
                <field name="create_uid" string="创建人"/>
                <field name="start_date" />
                <field name="estimated_completion_time"/>
                <field name="finish_user_id"/>
                <field name="finish_time"/>

                <field name="description"/>
                <field name="normal_state"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_eqpt_spot_check_record_form" model="ir.ui.view">
        <field name="name">roke.mes.eqpt.spot.check.record.form</field>
        <field name="model">roke.mes.eqpt.spot.check.record</field>
        <field name="arch" type="xml">
            <form string="设备点检记录">
                <header>
                    <button name="make_finish" type="object" class='oe_highlight' string="完成"
                            attrs="{'invisible':[('state', '!=', 'not_started')]}"/>
                    <button name="make_cancel" type="object" string="取消"
                            attrs="{'invisible':[('state', '!=', 'not_started')]}"/>
                    <button name="make_wait" type="object" string="置为等待"
                            attrs="{'invisible':[('state', '=', 'not_started')]}"/>
                    <button name="repair_request" type="object" string="报修" class='oe_highlight'
                            attrs="{'invisible':['|', ('normal_state', '!=', 'abnormal'), ('state', '!=', 'not_started')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
<!--                <sheet>-->
                <group string="设备基础信息" col="3">
                    <group>
                        <field name="equipment_id"/>
                        <field name="finish_user_id"/>
                    </group>
                    <group>
                        <field name="check_plan_id"/>
                        <field name="start_date" />
                    </group>
                    <group>
                        <field name="create_uid" string="创建人"/>
                        <field name="estimated_completion_time"/>
                    </group>
                </group>
                <group>
                    <field name="description"/>
                </group>
                <notebook>
                    <page string="检查结果">
                        <field name="item_record_ids" readonly="1">
                            <tree editable="bottom">
                                <field name="record_id" invisible="1"/>
                                <field name="check_item_id"/>
                                <field name="check_value"/>
                                <field name="input_type_id" optional="hide"/>
                                <field name="standard_value" optional="hide"/>
                                <field name="upper_value" optional="hide"/>
                                <field name="lower_value" optional="hide"/>
                                <field name="result"/>
                                <field name="description" optional="show"/>
                                <button name="execute_entrance" type="object" icon="fa-check" string="执行"
                                        attrs="{'invisible':[('result', '!=', 'wait')]}"
                                        context="{'state': 'finish'}"/>
                                <button name="execute_entrance" type="object" icon="fa-reply-all" string="置为等待"
                                        attrs="{'invisible':[('result', '=', 'wait')]}"
                                        context="{'state': 'wait'}"/>
                                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
                            </tree>
                            <form>
                                <header>
                                    <button name="execute_entrance" type="object" icon="fa-check" string="执行"
                                            attrs="{'invisible':[('result', '!=', 'wait')]}"
                                            context="{'state': 'finish'}"/>
                                    <button name="execute_entrance" type="object" icon="fa-reply-all" string="置为等待"
                                            attrs="{'invisible':[('result', '=', 'wait')]}"
                                            context="{'state': 'wait'}"/>
                                </header>
                                <group>
                                    <group>
                                        <field name="record_id"/>
                                        <field name="check_item_id"/>
                                        <field name="standard_value" attrs="{'invisible': [('input_type', 'not in', ['text', 'select'])]}"/>
                                        <field name="upper_value" attrs="{'invisible': [('input_type', 'not in', ['int', 'float'])]}"/>
                                        <field name="lower_value" attrs="{'invisible': [('input_type', 'not in', ['int', 'float'])]}"/>
                                    </group>
                                    <group>
                                        <field name="input_type_id" required="1"/>
                                        <field name="input_type" invisible="1"/>
                                        <field name="check_value"/>
                                        <field name="result"/>
                                        <field name="description"/>
                                    </group>
                                </group>
                            </form>
                        </field>
                        <group>
                            <field name="normal_state"/>
                        </group>
                    </page>
                    <page string="图片">
                        <field name="picture" widget="image"/>
                    </page>
                </notebook>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--pivot-->
    <record id="view_roke_mes_eqpt_spot_check_record_pivot" model="ir.ui.view">
        <field name="name">roke.mes.eqpt.spot.check.record.pivot</field>
        <field name="model">roke.mes.eqpt.spot.check.record</field>
        <field name="arch" type="xml">
            <pivot display_quantity="True" sample="1">
                <field name="check_plan_id"  type="row"/>
                <field name="equipment_id"  type="row"/>
                <field name="normal_state" type="col"/>
                <field name="code" type="col"/>
            </pivot>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_mes_eqpt_spot_check_record_action" model="ir.actions.act_window">
        <field name="name">设备点检记录</field>
        <field name="res_model">roke.mes.eqpt.spot.check.record</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_eqpt_spot_check_record_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
