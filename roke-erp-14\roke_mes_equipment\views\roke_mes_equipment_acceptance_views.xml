<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--tree-->
    <record id="view_roke_mes_equipment_acceptance_tree" model="ir.ui.view">
        <field name="name">roke.mes.equipment.acceptance.tree</field>
        <field name="model">roke.mes.equipment.acceptance</field>
        <field name="arch" type="xml">
            <tree string="设备验收单">
                <field name="code"/>
                <field name="equipment_name"/>
                <field name="specification"/>
                <field name="manufacturer_code"/>
                <field name="supplier_id"/>
                <field name="state"/>
                <field name="create_uid" string="创建人"/>
                <field name="create_date" string="创建时间"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_auxiliary_equipment_line_tree" model="ir.ui.view">
        <field name="name">roke.auxiliary.equipment.line.tree</field>
        <field name="model">roke.auxiliary.equipment.line</field>
        <field name="arch" type="xml">
            <tree string="附属设备信息" editable="bottom">
                <field name="auxiliary_id" invisible='1'/>
                <field name="equipment_name"/>
                <field name="specification"/>
                <field name="uom_id"/>
                <field name="acceptance_qty"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <record id="view_roke_accessory_line_tree" model="ir.ui.view">
        <field name="name">roke.accessory.line.tree</field>
        <field name="model">roke.accessory.line</field>
        <field name="arch" type="xml">
            <tree string="配件信息" editable="bottom">
                <field name="accessory_id" invisible='1'/>
                <field name="accessory_name"/>
                <field name="specification"/>
                <field name="uom_id"/>
                <field name="acceptance_qty"/>
                <field name="note"/>
            </tree>
        </field>
    </record>

    <!--form-->
    <record id="view_roke_mes_equipment_acceptance_form" model="ir.ui.view">
        <field name="name">roke.mes.equipment.acceptance.form</field>
        <field name="model">roke.mes.equipment.acceptance</field>
        <field name="arch" type="xml">
            <form string="设备验收单">
                <header>
                    <button name="action_confirm" type="object" class='oe_highlight' string="确认"
                            attrs="{'invisible':[('state', '=', '验收完成')]}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <group col="4">
                    <group>
                        <field name="equipment_name" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        <field name="price" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        <field name="specification" attrs="{'readonly':[('state','=','验收完成')]}"/>
                    </group>
                    <group>
                        <field name="supplier_id" domain="[('supplier', '=', True)]" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        <field name="category_id" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                    </group>
                    <group>
                        <field name="contact_information" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        <field name="entry_date" attrs="{'readonly':[('state','=','验收完成')]}"/>
                    </group>
                    <group>
                        <field name="acceptance_date" required="1" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        <field name="manufacturer_code" attrs="{'readonly':[('state','=','验收完成')]}"/>
                    </group>
                </group>
                <notebook>
                    <page string="外包装信息">
                        <group col="4">
                            <group>
                                <field name="is_outer_packaging_intact" widget="radio" required="1" options="{'horizontal': True}" attrs="{'readonly':[('state','=','验收完成')]}"/>
                            </group>
                            <group></group>
                            <group></group>
                            <group></group>
                        </group>
                        <group>
                            <field name="handling_suggestion" placeholder="此处可以填写处理意见" attrs="{'readonly':[('state','=','验收完成')]}"/>
                        </group>
                    </page>
                    <page string="附属设备信息">
                        <field name="auxiliary_equipment_lines" attrs="{'readonly':[('state','=','验收完成')]}"
                               context="{'tree_view_ref': 'roke_mes_equipment.view_roke_auxiliary_equipment_line_tree'}"/>
                    </page>
                    <page string="备件信息">
                        <field name="spare_part_ids" attrs="{'readonly':[('state','=','验收完成')]}"/>
                    </page>
                </notebook>
                <group>
                    <field name="file_ids" widget="many2many_pic_preview"/>
                </group>
                <group>
                    <field name="note" placeholder="此处可以填写备注或描述"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_roke_auxiliary_equipment_line_form" model="ir.ui.view">
        <field name="name">roke.auxiliary.equipment.line.form</field>
        <field name="model">roke.auxiliary.equipment.line</field>
        <field name="arch" type="xml">
            <form string="附属设备信息">
                <group col="3">
                    <group>
                        <field name="auxiliary_id"/>
                        <field name="equipment_name"/>
                    </group>
                    <group>
                        <field name="specification"/>
                        <field name="uom_id"/>
                    </group>
                    <group>
                        <field name="acceptance_qty"/>
                        <field name="note"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <record id="view_roke_accessory_line_form" model="ir.ui.view">
        <field name="name">roke.accessory.line.form</field>
        <field name="model">roke.accessory.line</field>
        <field name="arch" type="xml">
            <form string="配件信息">
                <group col="3">
                    <group>
                        <field name="accessory_id"/>
                        <field name="accessory_name"/>
                    </group>
                    <group>
                        <field name="specification"/>
                        <field name="uom_id"/>
                    </group>
                    <group>
                        <field name="acceptance_qty"/>
                        <field name="note"/>
                    </group>
                </group>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_mes_equipment_acceptance_action" model="ir.actions.act_window">
        <field name="name">设备验收单</field>
        <field name="res_model">roke.mes.equipment.acceptance</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_equipment_acceptance_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
