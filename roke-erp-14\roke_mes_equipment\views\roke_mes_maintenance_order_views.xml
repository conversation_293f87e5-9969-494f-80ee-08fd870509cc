<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--保养任务-->
    <!--search-->
    <record id="view_roke_mes_maintenance_order_search" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.search</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <search string="保养任务">
                <field string="单据" name="code"
                    filter_domain="['|', ('code', 'ilike', self), ('equipment_id', 'ilike', self)]"/>
                <field name="user_id"/>
                <field name="maintenance_scheme_id"/>
                <separator/>
                <filter string="优先级：低" name="low" domain="[('priority','=','low')]"/>
                <filter string="优先级：中" name="normal" domain="[('priority','=','normal')]"/>
                <filter string="优先级：高" name="high" domain="[('priority','=','high')]"/>
                <group expand="1" string="Group By">
                    <filter string="保养方案" name="maintenance_scheme_id" context="{'group_by':'maintenance_scheme_id'}"/>
<!--                    <filter string="指派人" name="user_id" context="{'group_by':'user_id'}"/>-->
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_maintenance_order_tree" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.tree</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <tree string="保养任务" decoration-muted="(state == 'cancel')" decoration-warning="(state == 'postpone')" decoration-success="(state == 'finish')">
                <field name="code"/>
                <field name="equipment_id"/>
                <field name="maintenance_scheme_id"/>
<!--                <field name="last_maintenance_date"/>-->
                <field name="user_id" />
                <field name="repair_user_id"  string="保养人"/>
                <field name="start_date" optional="show"/>
                <field name="estimated_completion_time" optional="show"/>
                <field name="deadline" optional="show"/>
                <field name="finished" />
<!--                <field name="priority"/>-->
                <field name="state" invisible="0"/>
                <field name="maintain_state"  invisible="1"/>
<!--                <field name="normal_state"/>-->
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_maintenance_order_form" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.form</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <form string="保养任务">
                <header>
                    <button name="execute_order_entrance" type="object" class='oe_highlight' string="完成"
                            attrs="{'invisible':[('state', 'not in', ['wait', 'postpone'])]}"
                            context="{'state': 'finish'}"/>
                    <button name="execute_order_entrance" type="object" string="延期"
                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                            context="{'state': 'postpone'}"/>
                    <button name="execute_order_entrance" type="object" string="取消"
                            attrs="{'invisible':[('state', 'not in', ['wait', 'postpone'])]}"
                            context="{'state': 'cancel'}"/>
                    <button name="execute_order_entrance" type="object" string="置为等待"
                            attrs="{'invisible':[('state', '!=', 'cancel')]}"
                            context="{'state': 'wait'}"/>
                    <button name="create_special_work_order" type="object" class='oe_highlight' string="创建特种作业单"
                            attrs="{'invisible':[('state', 'in', ['finish', 'cancel'])]}"
                            context="{'state': 'wait'}"/>
                    <button name="repair_request" type="object" string="报修" class='oe_highlight'
                            attrs="{'invisible':[('normal_state', '!=', 'abnormal')]}"/>
<!--                    <field name="state" widget="statusbar"/>-->
                </header>
<!--                <sheet>-->
                    <div class="oe_button_box" name="button_box">
                        <button type="object" name="action_special_work" class="oe_stat_button"
                            icon="fa-list">
                            <field name="special_work_count" string="特种作业单" widget="statinfo"/>
                        </button>
                    </div>
<!--                    <div class="oe_title">-->
<!--                        <label for="code" class="oe_edit_only"/>-->
<!--                        <h1><field name="code" readonly="1"/></h1>-->
<!--                    </div>-->
                    <field name="type" invisible="1"/>
                    <group col="3" name="task_header">
                        <group>
                            <field name="equipment_id" required="1" options="{'no_create': True}"/>
                            <field name="repair_user_id" required="0" options="{'no_create': True}" string="保养人"/>
                            <field name="estimated_completion_time" />
                        </group>
                        <group>
                            <field name="maintenance_scheme_id" required="1" options="{'no_create': True}"/>
                            <field name="priority" required="1"/>
<!--                            <field name="maintain_state" />-->
                            <field name="state"  readonly="1"/>
                        </group>
                        <group>
                            <field name="user_id" required="0" options="{'no_create': True}"/>
                            <field name="start_date"/>
                        </group>
                    </group>
                    <group name="note">
                        <field name="note"/>
                        <field name="last_maintenance_date" invisible="1" force_save="1"/>

                    </group>

                    <notebook>
                        <page string="保养项目">
                            <field name="item_ids">
                                <tree editable="bottom">
                                    <field name="order_id" invisible="1"/>
                                    <field name="item_id" required="1" force_save="1"/>
                                    <field name="maintenance_description"  force_save="1" />
                                    <field name="execute_time" required="1"/>
                                    <field name="execute_user_id" required="1" />
                                    <field name="description"  />
                                    <field name="result" />
                                    <field name="state" readonly="1"/>
                                    <button name="execute_entrance" type="object" icon="fa-check" string="完成"
                                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                                            context="{'state': 'finish'}"/>
                                    <button name="execute_entrance" type="object" icon="fa-eye-slash" string="忽略"
                                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                                            context="{'state': 'ignore'}"/>
                                    <button name="execute_entrance" type="object" icon="fa-reply-all" string="置为等待"
                                            attrs="{'invisible':[('state', '!=', 'ignore')]}"
                                            context="{'state': 'wait'}"/>
                                     <button name="maintenance_item_delete" type="object" class='oe_highlight' string="删除"/>
                                     <button name="spare_parts_replacement" type="object"  class='oe_highlight' string="备件更换"/>

                                </tree>
                            </field>
                            <field name="postpone_description" placeholder="此处录入延期说明" readonly="1"/>
                            <field name="cancel_description" placeholder="此处录入取消说明" readonly="1"/>
                        </page>
                        <page string="图片">
                            <field name="picture" widget="image"/>
                        </page>
                    </notebook>
                   <group>
                       <field name="normal_state"  readonly="1"/>
                   </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!-- pivot-->
    <record id="view_roke_mes_maintenance_order_b_pivot" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.b.pivot</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <pivot display_quantity="True" sample="1">
                <field name="priority"  type="row"/>
                <field name="state"  type="row"/>
                <field name="equipment_id" type="row"/>
                <field name="report_user_id" type="col"/>
            </pivot>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_mes_maintenance_order_action" model="ir.actions.act_window">
        <field name="name">保养任务</field>
        <field name="res_model">roke.mes.maintenance.order</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_maintenance_order_form"/>
        <field name="search_view_id" ref="view_roke_mes_maintenance_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_roke_mes_maintenance_order_tree')}),
            (0, 0, {'view_mode': 'pivot', 'view_id': ref('view_roke_mes_maintenance_order_b_pivot')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_roke_mes_maintenance_order_form')})]"/>
        <field name="domain">[("type", "=", "maintain")]</field>
        <field name="context">{'default_type': 'maintain'}</field>
    </record>
    <!--批量创建特种作业单-->
    <act_window name="创建特种作业单"
        id="act_create_special_work_order"
        res_model="roke.maintain.create.special.work.wizard"
        binding_model="roke.mes.maintenance.order"
        view_mode="form"
        target="new"
    />
    <!--保养项目-->
    <!--tree-->
    <record id="view_roke_mes_maintenance_order_item_tree" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.item.tree</field>
        <field name="model">roke.mes.maintenance.order.item</field>
        <field name="arch" type="xml">
            <tree string="保养任务明细" decoration-warning="(state == 'ignore')" decoration-success="(state == 'finish')">
                <field name="order_id"/>
                <field name="item_id"/>
                <field name="execute_user_id"/>
                <field name="execute_time"/>
                <field name="description"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_maintenance_order_item_form" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.item.form</field>
        <field name="model">roke.mes.maintenance.order.item</field>
        <field name="arch" type="xml">
            <form string="保养任务明细">
                <header>
                    <button name="execute_entrance" type="object" class='oe_highlight' string="完成"
                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                            context="{'state': 'finish'}"/>
                    <button name="execute_entrance" type="object" string="忽略"
                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                            context="{'state': 'ignore'}"/>
                    <button name="execute_entrance" type="object" string="置为等待"
                            attrs="{'invisible':[('state', '!=', 'ignore')]}"
                            context="{'state': 'wait'}"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="item_id" class="oe_edit_only"/>
                        <h1><field name="item_id" readonly="1"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="execute_user_id" readonly="1"/>
                            <field name="execute_time" readonly="1"/>
                            <field name="picture"/>
                        </group>
                        <group>
                            <field name="description"/>
                            <field name="execute_files" widget="many2many_binary" string="添加故障照片" nolabel="1" colspan="2"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <!--故障类别-->
    <!--search-->
    <record id="view_roke_mes_equipment_fault_search" model="ir.ui.view">
        <field name="name">roke.mes.equipment.fault.search</field>
        <field name="model">roke.mes.equipment.fault</field>
        <field name="arch" type="xml">
            <search string="故障类别">
                <field name="name"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_equipment_fault_tree" model="ir.ui.view">
        <field name="name">roke.mes.equipment.fault.tree</field>
        <field name="model">roke.mes.equipment.fault</field>
        <field name="arch" type="xml">
            <tree string="故障类别" editable="top">
                <field name="name"/>
                <field name="company_id" groups="base.group_multi_company" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mes_equipment_fault_action" model="ir.actions.act_window">
        <field name="name">特种设备检验机构</field>
        <field name="res_model">roke.mes.equipment.fault</field>
        <field name="view_mode">tree</field>
        <field name="type">ir.actions.act_window</field>

        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>

</odoo>
