<?xml version="1.0" encoding="utf-8"?>
<odoo>
     <!--保养任务编号-->
    <record id="roek_mes_maintain_scheme_code_sequence_1" model="ir.sequence">
        <field name="name">保养方案编号</field>
        <field name="code">roke.mes.maintain.scheme.code</field>
        <field name="prefix">BYFA</field>
        <field name="padding">4</field>
    </record>

      <!--保养项目编号-->
    <record id="roek_mes_maintain_item_code_sequence" model="ir.sequence">
        <field name="name">保养项目编号</field>
        <field name="code">roke.mes.maintain.item.code</field>
        <field name="prefix">BYXM</field>
        <field name="padding">4</field>
    </record>
    <!--保养方案-->
    <!--search-->
    <record id="view_roke_mes_maintenance_scheme_search" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.scheme.search</field>
        <field name="model">roke.mes.maintenance.scheme</field>
        <field name="arch" type="xml">
            <search string="保养方案">
                <field string="保养方案" name="name"
                    filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_maintenance_scheme_tree" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.scheme.tree</field>
        <field name="model">roke.mes.maintenance.scheme</field>
        <field name="arch" type="xml">
            <tree string="保养方案">
                <field name="code"/>
                <field name="name"/>
                <field name="show_frequency"/>
                <field name="last_maintenance_date" optional="hide"/>
                <field name="next_maintenance_date"/>
            </tree>
        </field>
    </record>
    <!--pivot-->
    <record id="view_roke_mes_maintenance_scheme_pivot" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.scheme.pivot</field>
        <field name="model">roke.mes.maintenance.scheme</field>
        <field name="arch" type="xml">
            <pivot display_quantity="True" sample="1">
                <field name="frequency_unit"  type="row"/>
                <field name="frequency" type="col"/>
                <field name="code" type="col"/>
                <field name="name" type="col"/>
            </pivot>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_maintenance_scheme_form" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.scheme.form</field>
        <field name="model">roke.mes.maintenance.scheme</field>
        <field name="arch" type="xml">
            <form string="保养方案">
<!--                <sheet>-->
                    <widget name="web_ribbon" text="归档" bg_color="bg-danger" attrs="{'invisible': [('active', '=', True)]}"/>
                    <widget name="web_ribbon" text="生效中" bg_color="bg-success" attrs="{'invisible': [('active', '!=', True)]}"/>
<!--                    <div class="oe_title">-->
<!--                        <label for="code" class="oe_edit_only"/>-->
<!--                        <h1><field name="code" required="1"/></h1>-->
<!--                    </div>-->
                    <field name="active" invisible="1"/>
                    <group col="4">
                        <group>
                            <field name="name" required="1"/>
                             <label for="frequency"/>
                             <div class="o_row">
                                <field name="frequency" required="1"/>
                                <span>
                                    <field name="frequency_unit" required="1"/>
                                </span>
                            </div>
                        </group>
                        <group>
                            <field name="equipment_ids" required="1" widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="last_maintenance_date"/>
                        </group>
                        <group>
                            <field name="next_maintenance_date"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="保养项目">
                            <field name="item_ids">
                                <tree >
                                    <field name="code"/>
                                    <field name="name"/>
                                    <field name="note"/>
                                </tree>
                            </field>
                        </page>
                        <page string="保养记录">
                            <field name="maintenance_order_ids" readonly="1">
                                <tree>
                                    <field name="code"/>
                                    <field name="equipment_id"/>
                                    <field name="maintenance_scheme_id"/>
                                    <field name="last_maintenance_date"/>
                                    <field name="user_id"/>
                                    <field name="state"/>
                                </tree>
                                <form>
                                    <sheet>
                                        <div class="oe_title">
                                            <label for="code" class="oe_edit_only"/>
                                            <h1><field name="code" readonly="1"/></h1>
                                        </div>
                                        <field name="type" invisible="1"/>
                                        <group>
                                            <group>
                                                <field name="equipment_id" options="{'no_open': True}"/>
                                                <field name="user_id" options="{'no_open': True}"/>
                                            </group>
                                            <group>
                                                <field name="maintenance_scheme_id" options="{'no_create': True}"/>
                                                <field name="last_maintenance_date"/>
                                            </group>
                                        </group>
                                        <notebook>
                                            <page string="保养项目">
                                                <field name="item_ids">
                                                    <tree editable="bottom">
                                                        <field name="order_id" invisible="1"/>
                                                        <field name="item_id"/>
                                                        <field name="execute_user_id" readonly="1"/>
                                                        <field name="execute_time" readonly="1"/>
                                                        <field name="execute_files" readonly="1"/>
                                                        <field name="description" readonly="1"/>
                                                        <field name="state"/>
                                                    </tree>
                                                </field>
                                                <field name="postpone_description" placeholder="此处录入延期说明"/>
                                                <field name="cancel_description" placeholder="此处录入取消说明"/>
                                            </page>
                                        </notebook>
                                    </sheet>
                                </form>
                            </field>
                        </page>
                    </notebook>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_mes_maintenance_scheme_action" model="ir.actions.act_window">
        <field name="name">保养方案</field>
        <field name="res_model">roke.mes.maintenance.scheme</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="form_view_id" ref="view_roke_mes_maintenance_scheme_form"/>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
</odoo>
