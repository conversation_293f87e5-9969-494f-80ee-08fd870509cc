<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--维修任务-->
    <!--search-->
    <record id="view_roke_mes_repair_order_search" model="ir.ui.view">
        <field name="name">roke.mes.repair.order.search</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <search string="维修任务">
                <field string="单据" name="code"
                    filter_domain="['|', ('code', 'ilike', self), ('equipment_id', 'ilike', self)]"/>
                <field name="user_id"/>
                <field name="report_user_id"/>
                <separator/>
                <filter string="优先级：低" name="low" domain="[('priority','=','low')]"/>
                <filter string="优先级：中" name="normal" domain="[('priority','=','normal')]"/>
                <filter string="优先级：高" name="high" domain="[('priority','=','high')]"/>
<!--                <group expand="1" string="Group By">-->
<!--                    <filter string="指派人" name="user_id" context="{'group_by':'user_id'}"/>-->
<!--                </group>-->
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_mes_repair_order_tree" model="ir.ui.view">
        <field name="name">roke.mes.repair.order.tree</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
<!--            @wudi-->
<!--            添加 create="false"-->
            <tree string="维修任务" create="false" decoration-muted="(state == 'cancel')" decoration-warning="(state == 'postpone')" decoration-success="(state == 'finish')">
                <field name="code"/>
                <field name="equipment_id"/>
                <field name="report_user_id"/>
                <field name="report_time"/>
                <field name="priority"/>
                <field name="repair_user_id"/>
                <field name="deadline"/>
                <field name="fault_description"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_mes_repair_order_form" model="ir.ui.view">
        <field name="name">roke.mes.repair.order.form</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <form string="维修任务">
                <header>
                    <button name="button_dispatch" type="object" class='oe_highlight' string="派工"
                            attrs="{'invisible':[('state', '!=', 'wait')]}"/>
                    <button name="execute_order_entrance" type="object" class='oe_highlight' string="完成"
                            attrs="{'invisible':[('state', 'not in', ['wait', 'postpone'])]}"
                            context="{'state': 'finish'}"/>
                    <button name="execute_order_entrance" type="object" string="延期"
                            attrs="{'invisible':[('state', '!=', 'wait')]}"
                            context="{'state': 'postpone'}"/>
                    <button name="execute_order_entrance" type="object" string="取消"
                            attrs="{'invisible':[('state', 'not in', ['wait', 'postpone'])]}"
                            context="{'state': 'cancel'}"/>
                    <button name="execute_order_entrance" type="object" string="置为等待"
                            attrs="{'invisible':[('state', '!=', 'cancel')]}"
                            context="{'state': 'wait'}"/>
<!--                    <button name="create_special_work_order" type="object" class='oe_highlight' string="创建特种作业单"-->
<!--                            attrs="{'invisible':[('state', 'in', ['finish', 'cancel'])]}"-->
<!--                            context="{'state': 'wait'}"/>-->
                    <!-- <field name="state" widget="statusbar"/> -->
                </header>
<!--                <sheet>-->
                <div class="oe_button_box" name="button_box">
                    <button type="object" name="action_special_work" class="oe_stat_button"
                        icon="fa-list">
                        <field name="special_work_count" string="特种作业单" widget="statinfo"/>
                    </button>
                </div>
<!--                <div class="oe_title">-->
<!--                    <label for="code" class="oe_edit_only"/>-->
<!--                    <h1><field name="code" readonly="1"/></h1>-->
<!--                </div>-->
                <field name="type" invisible="1"/>
                <group string="设备基础信息" col="3">
                    <group>
                        <field name="equipment_id" required="1" options="{'no_create': True}"/>
                        <field name="repair_user_id" required="1" options="{'no_create': True}"/>
                        <field name="deadline"/>
                    </group>
                    <group>
                        <field name="report_user_id" required="1"/>
                        <field name="priority" required="1"/>
                    </group>
                    <group>
                        <field name="user_id" required="1"/>
                        <field name="state" readonly="1"/>
                    </group>
                </group>
                <group>
                    <field name="fault_description"/>
                </group>
                <group string="维修处理" col="3">
                    <group>
                         <field name="report_time"/>
                         <field name="start_date"/>
                    </group>
                    <group>

                        <field name="finish_time"/>
                    </group>
                    <group>
                        <button name="create_equipment_change_record" string="备件更换" type="object"
                            class='oe_highlight' attrs="{'invisible':[('state', 'not in', ['assign', 'postpone'])]}"/>
                    </group>
                </group>
                <group>
                    <field name="repair_description" placeholder="此处录入维修处理说明"/>
                </group>
<!--                </sheet>-->
                <div class="oe_chatter">
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

<!--    <record id="view_roke_mes_maintenance_order_pivot" model="ir.ui.view">-->
<!--         <field name="name">roke.mes.maintenance.order.pivot</field>-->
<!--         <field name="model">roke.mes.maintenance.order</field>-->
<!--         <field name="arch" type="xml">-->
<!--             <pivot string="维修" disable_linking="True">-->
<!--                  <field name="state"  type="col"/>-->
<!--                  <field name="equipment_id"/>-->
<!--                 <field name="process_id" type="row"/>-->
<!--                 <field name="product_id" type="row"/>-->
<!--                 <field name="move_date" interval="day" type="col"/>-->
<!--                 <field name="finish_qty" type="measure"/>-->
<!--                 <field name="scrap_qty" type="measure"/>-->
<!--                 <field name="repair_qty" type="measure"/>-->
<!--             </pivot>-->
<!--         </field>-->
<!--    </record>-->

    <record id="view_roke_mes_maintenance_order_pivot" model="ir.ui.view">
        <field name="name">roke.mes.maintenance.order.pivot</field>
        <field name="model">roke.mes.maintenance.order</field>
        <field name="arch" type="xml">
            <pivot display_quantity="True" sample="1">
                <field name="priority"  type="row"/>
                <field name="state"  type="row"/>
                <field name="equipment_id" type="row"/>
                <field name="report_user_id" type="col"/>
            </pivot>
        </field>
    </record>

    <!--action-->
    <record id="view_roke_mes_repair_order_action" model="ir.actions.act_window">
        <field name="name">维修任务</field>
        <field name="res_model">roke.mes.maintenance.order</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="search_view_id" ref="view_roke_mes_repair_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_roke_mes_repair_order_tree')}),
            (0, 0, {'view_mode': 'pivot', 'view_id': ref('view_roke_mes_maintenance_order_pivot')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_roke_mes_repair_order_form')})]"/>
        <field name="domain">[("type", "=", "repair")]</field>
        <field name="context">{'default_type': 'repair'}</field>
    </record>
</odoo>
