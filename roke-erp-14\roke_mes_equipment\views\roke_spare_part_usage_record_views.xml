<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--备件使用记录管理-->
    <!--search-->
    <record id="view_roke_spare_part_usage_record_search" model="ir.ui.view">
        <field name="name">roke.spare.part.usage.record.search</field>
        <field name="model">roke.spare.part.usage.record</field>
        <field name="arch" type="xml">
            <search string="备件使用记录">
                <field name="equipment_id"/>
                <field name="spare_part_id"/>
                <filter string="本月更换" name="this_month" domain="[('replacement_time', '&gt;=', (context_today() - relativedelta(months=1)).strftime('%Y-%m-01')), ('replacement_time', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                <filter string="本年更换" name="this_year" domain="[('replacement_time', '&gt;=', context_today().strftime('%Y-01-01')), ('replacement_time', '&lt;=', context_today().strftime('%Y-12-31'))]"/>
            </search>
        </field>
    </record>
    
    <!--tree-->
    <record id="view_roke_spare_part_usage_record_tree" model="ir.ui.view">
        <field name="name">roke.spare.part.usage.record.tree</field>
        <field name="model">roke.spare.part.usage.record</field>
        <field name="arch" type="xml">
            <tree string="备件使用记录">
                <field name="spare_part_id"/>
                <field name="equipment_id"/>
                <field name="removed_part_id"/>
                <field name="replacement_time"/>
                <field name="expiry_time"/>
            </tree>
        </field>
    </record>
    
    <!--form-->
    <record id="view_roke_spare_part_usage_record_form" model="ir.ui.view">
        <field name="name">roke.spare.part.usage.record.form</field>
        <field name="model">roke.spare.part.usage.record</field>
        <field name="arch" type="xml">
            <form string="备件使用记录">
                <sheet>
                    <group>
                        <group>
                            <field name="equipment_id" options="{'no_create': True}"/>
                            <field name="spare_part_id" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="replacement_time"/>
                            <field name="removed_part_id"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!--action-->
    <record id="action_roke_spare_part_usage_record" model="ir.actions.act_window">
        <field name="name">备件使用记录</field>
        <field name="res_model">roke.spare.part.usage.record</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="view_roke_spare_part_usage_record_search"/>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                创建第一个备件使用记录
            </p>
            <p>
                在这里可以管理所有的备件使用记录，跟踪备件的使用情况、寿命状态等。
            </p>
        </field>
    </record>
</odoo>
