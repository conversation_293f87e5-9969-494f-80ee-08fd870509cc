# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class InheritWorkOrder(models.Model):
    _inherit = "roke.work.order"

    def _get_previous_finished_qty(self, previous):
        """
        获取前道工序完工数
            如果当前工单的前工序是合并工序，那么允许数量=默认数量=前工序完工数的合计（前工序可能为多个）
        :return:
        """
        if len(previous) > 1 and previous[0].routing_line_id.merge_finish_qty:
            # 获取前道同级合并工序，并返回其合格数合并值
            return sum(previous.mapped("finish_qty"))
        return super(InheritWorkOrder, self)._get_previous_finished_qty(previous)

    def _get_same_sequence_wo(self):
        """
        获取同级工序
        :return:
        """
        return self.task_id.work_order_ids.filtered(lambda wo: wo.sequence == self.sequence and wo != self)

    def _get_wo_allow_qty(self):
        """
        获取工单可报数量
            1.如果当前工单是完工数合并的工序，那么允许数量=默认数量=原允许数量-同级工序的完工数量
            2.如果当前工单的前工序是合并工序，那么允许数量=默认数量=前工序完工数的合计（前工序可能为多个）
        :return:
        """
        company = self.env.user.company_id
        allow_qty, default_qty = super(InheritWorkOrder, self)._get_wo_allow_qty()
        if self.routing_line_id.merge_finish_qty:
            # 获取同级合并工序
            same_sequence_wo = self._get_same_sequence_wo()
            if same_sequence_wo:
                # 减去同级合并工序的合格数
                allow_qty -= sum(same_sequence_wo.mapped("finish_qty"))
                default_qty -= sum(same_sequence_wo.mapped("finish_qty"))
                if company.complete_basis == "报工数":
                    # 减去同级合并工序的不合数
                    allow_qty -= sum(same_sequence_wo.mapped("unqualified_qty"))
                    default_qty -= sum(same_sequence_wo.mapped("unqualified_qty"))
        return allow_qty, default_qty

    def _get_finish_check_qty(self, self_finish_qty, self_unqualified_qty):
        """
        获取工单完成时实际校验数，可能是合格数，也可能是不合格数
        :return:
        """
        if self.routing_line_id.merge_finish_qty and self.state != '已完工':  # 合并工序且工单未完工
            same_sequence_wo = self._get_same_sequence_wo()  # 获取同级工序
            all_finish_qty = round(self_finish_qty + sum(same_sequence_wo.mapped("finish_qty")), _get_pd(self.env))  # 获取累计完成数
            check_qty = all_finish_qty
            if self.env.user.company_id.complete_basis == "报工数":  # 判断完工依据
                check_qty += round((self_unqualified_qty + sum(same_sequence_wo.mapped("unqualified_qty"))), _get_pd(self.env))
                # 获取前道工序所有不合格数量，即前道工序报不合格后，当前工序应报数量要减掉前道不合格的数量。后面可能再调整
                # previous = self.task_id.work_order_ids.filtered(lambda wo: wo.sequence < self.sequence)
                previous = self._get_finish_check_previous()
                if previous:
                    check_qty += sum(previous.mapped("unqualified_qty"))
            # 同级工序置为完成状态
            if check_qty >= self.plan_qty:
                same_sequence_wo.write({"state": "已完工"})
        else:
            check_qty = super(InheritWorkOrder, self)._get_finish_check_qty(self_finish_qty, self_unqualified_qty)
        return check_qty

    def withdraw_report_work_order(self, qty, unqualified_qty, work_hours, work_record):
        """
        撤回报工时，同级工序置为未完工
        """
        super(InheritWorkOrder, self).withdraw_report_work_order(qty, unqualified_qty, work_hours, work_record)
        if self.routing_line_id.merge_finish_qty:
            self._get_same_sequence_wo().write({"state": "进行中"})
