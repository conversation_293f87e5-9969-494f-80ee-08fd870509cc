#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
@Author:
        ChenChangLei
@License:
        Copyright © 山东融科数据服务有限公司.
@Contact:
        <EMAIL>
@Software:
         PyCharm
@File:
    inherit_roke_work_order.py.py
@Time:
    2022/11/24 14:19
@Site: 
    
@Desc:
    
"""

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from odoo.exceptions import UserError
from urllib import parse
from datetime import timedelta

class InheritRokeWorkOrder(models.Model):
    _inherit = "roke.work.order"

    plan_start_time = fields.Date('排产开始时间', required=True, store=True, default=fields.Date.context_today)
    plan_stop_time = fields.Date('排产结束时间', required=True, store=True, default=fields.Date.context_today)
    is_passive = fields.Boolean(string="是否被动修改", default=False)

    requirement_entrust_line_ids = fields.Char(string="委外明细id列表")

    @api.model
    def create(self, vals):
        res = super(InheritRokeWorkOrder, self).create(vals)
        # 委外
        if res.requirement_entrust_line_ids:
            for i in eval(res.requirement_entrust_line_ids):
                line = self.env['roke.requirement.entrust.line'].search([('id', '=', i)])
                entrust_ids = line.entrust_ids.ids
                entrust_ids.append(res.id)
                line.write({
                    'entrust_ids': entrust_ids
                })
        return res

    def compute_date(self, plan_date, in_adv_time, compute_type):

        in_adv_time_unit = self.env.company.in_adv_time_unit
        """
        计算提前期
        """
        if in_adv_time_unit == "天":
            in_adv_time_day = in_adv_time
        elif in_adv_time_unit == "周":
            in_adv_time_day = in_adv_time * 7
        elif in_adv_time_unit == "月":
            in_adv_time_day = in_adv_time * 30
        elif in_adv_time_unit == "季":
            in_adv_time_day = in_adv_time * 90
        elif in_adv_time_unit == "年":
            in_adv_time_day = in_adv_time * 365
        else:
            raise UserError("单位有误，请检查!")
        if compute_type == "-":
            date_result = plan_date - timedelta(days=in_adv_time_day)
        else:
            date_result = plan_date + timedelta(days=in_adv_time_day)
        return date_result

    # def write(self, vals):
    #     # 处理表头修改时会传入多条工单数据，但实际多条中每一条都会重新走一遍此方法，所以不添加循环self
    #     if len(self) == 1:
    #         if self:
    #             if self.state != "未完工": # 不是未完工状态时移除开始结束时间，不允许拖动修改日期
    #                 if vals.__contains__("plan_start_time"):
    #                     vals.pop("plan_start_time")
    #                 if vals.__contains__("plan_stop_time"):
    #                     vals.pop("plan_stop_time")
    #     res = super(InheritRokeWorkOrder, self).write(vals)
    #     if len(self) == 1:
    #         if self.is_passive == False:
    #             # 如果开始时间修改了，那么需要修改工序序号小于当前序号的开始结束时间
    #             if vals.__contains__("plan_start_time"):
    #                 # 查上工序的工单
    #                 work_order = self.search([
    #                     ("task_id", "=", self.task_id.id),
    #                     ("sequence", "<", self.sequence),
    #                 ], order="sequence desc", limit=1)
    #                 if work_order:
    #                     if work_order.state == "未完工":
    #                         # 修改时间--目前 开始、结束时间为相同，后续考虑工作中心产能等进行计算
    #                         in_adv_time = work_order.routing_line_id.in_adv_time # 工序提前期
    #                         # 减去提前期计算上一工序的时间
    #                         date_result = self.compute_date(
    #                             plan_date=self.plan_start_time,
    #                             in_adv_time=in_adv_time,
    #                             compute_type="-"
    #                         )
    #                         # 查它的上序是否还有
    #                         work_orders = self.search([
    #                             ("task_id", "=", work_order.task_id.id),
    #                             ("sequence", "<", work_order.sequence),
    #                         ], order="sequence desc", limit=1)
    #                         if work_orders:
    #                             is_passive = False
    #                         else:
    #                             is_passive = True
    #                         work_order.write({
    #                             "plan_start_time": date_result,
    #                             "plan_stop_time": date_result,
    #                             "is_passive": is_passive
    #                         })
    #             # 如果结束时间修改了，那么需要修改工序序号大于当前序号的开始结束时间
    #             if vals.__contains__("plan_stop_time"):
    #                 # 查下工序的工单
    #                 work_order = self.search([
    #                     ("task_id", "=", self.task_id.id),
    #                     ("sequence", ">", self.sequence),
    #                 ], order="sequence asc", limit=1)
    #                 if work_order:
    #                     if work_order.state == "未完工":
    #                         # 修改时间--目前 开始、结束时间为相同，后续考虑工作中心产能等进行计算
    #                         in_adv_time = self.routing_line_id.in_adv_time  # 工序提前期
    #                         # 加上提前期计算上一工序的时间
    #                         date_result = self.compute_date(
    #                             plan_date=self.plan_stop_time,
    #                             in_adv_time=in_adv_time,
    #                             compute_type="+"
    #                         )
    #                         # 查它的下序是否还有
    #                         work_orders = self.search([
    #                             ("task_id", "=", work_order.task_id.id),
    #                             ("sequence", ">", work_order.sequence),
    #                         ], order="sequence desc", limit=1)
    #                         if work_orders:
    #                             is_passive = False
    #                         else:
    #                             is_passive = True
    #                         work_order.write({
    #                             "plan_start_time": date_result,
    #                             "plan_stop_time": date_result,
    #                             "is_passive": is_passive
    #                         })
    #         else:
    #             self.is_passive = False
    #     return res


    def write(self, vals):
        # 处理表头修改时会传入多条工单数据，但实际多条中每一条都会重新走一遍此方法，所以不添加循环self
        if len(self) == 1:
            if self:
                if self.state not in ["未派工", "未开工", "进行中"]: # 不是未完工状态时移除开始结束时间，不允许拖动修改日期
                    if vals.__contains__("plan_start_time"):
                        vals.pop("plan_start_time")
                    if vals.__contains__("plan_stop_time"):
                        vals.pop("plan_stop_time")
        res = super(InheritRokeWorkOrder, self).write(vals)
        if len(self) == 1:
            if self.is_passive == False:
                # 如果开始时间修改了，那么需要修改工序序号小于当前序号的开始结束时间
                if vals.__contains__("plan_start_time"):
                    # 查上工序的工单
                    work_orders = self.search([
                        ("task_id", "=", self.task_id.id),
                        ("sequence", "<", self.sequence),
                    ], order="sequence desc")
                    if work_orders:
                        in_adv_time = 0
                        for work_order in work_orders:
                            if work_order.state in ["未派工", "未开工", "进行中"]:
                                # 修改时间--目前 开始、结束时间为相同，后续考虑工作中心产能等进行计算
                                in_adv_time += work_order.routing_line_id.in_adv_time # 工序提前期
                                # 减去提前期计算上一工序的时间
                                date_result = self.compute_date(
                                    plan_date=self.plan_start_time,
                                    in_adv_time=in_adv_time,
                                    compute_type="-"
                                )
                                work_order.write({
                                    "plan_start_time": date_result,
                                    "plan_stop_time": date_result,
                                    "is_passive": True
                                })
                # 如果结束时间修改了，那么需要修改工序序号大于当前序号的开始结束时间
                if vals.__contains__("plan_stop_time"):
                    # 查下工序的工单
                    work_orders = self.search([
                        ("task_id", "=", self.task_id.id),
                        ("sequence", ">", self.sequence),
                    ], order="sequence asc")

                    if work_orders:
                        in_adv_time = 0
                        for work_order in work_orders:
                            if work_order.state in ["未派工", "未开工", "进行中"]:
                                # 查上级提前期
                                previous_work_order = self.search([
                                    ("task_id", "=", work_order.task_id.id),
                                    ("sequence", "=", work_order.sequence-1),
                                ], order="sequence desc", limit=1)
                                # 修改时间--目前 开始、结束时间为相同，后续考虑工作中心产能等进行计算
                                in_adv_time += previous_work_order.routing_line_id.in_adv_time  # 工序提前期
                                # 加上提前期计算上一工序的时间
                                date_result = self.compute_date(
                                    plan_date=self.plan_stop_time,
                                    in_adv_time=in_adv_time,
                                    compute_type="+"
                                )
                                work_order.write({
                                    "plan_start_time": date_result,
                                    "plan_stop_time": date_result,
                                    "is_passive": True
                                })
            else:
                self.is_passive = False
        return res

    def confirm_add(self):
        kitting_id = self.env.context.get('default_kitting_id')
        line_obj = self.env['roke.kitting.order.line']
        customer, domain_ids = [], eval(self.env.context.get('default_domain_ids'))
        for record in self:
            line_obj.create({
                'line_id': int(kitting_id),
                'origin_code': record.code,
                'origin_type': '工单',
                'origin_product_id': record.product_id.id,
                'origin_lot': record.task_id.lot_code if record.task_id else '',
                'origin_date': record.plan_date,
                'origin_qty': record.plan_qty,
                'kitting_qty': 0,
                'wo_id': record.id
            })
            # 已添加数据
            domain_ids.append(record.id)
            # 客户
            if record.customer_id:
                if record.customer_id.id not in customer:
                    customer.append(record.customer_id.id)
        self.env['roke.kitting.order'].search([('id', '=', int(kitting_id))], limit=1).write({
            'model_class': '工单',
            'customer': customer,
            'domain_ids': domain_ids
        })
        return {'type': 'ir.actions.act_window_close'}