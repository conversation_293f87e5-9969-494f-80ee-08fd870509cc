# -*- coding: utf-8 -*-
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.exceptions import UserError, AccessDenied
from odoo.addons.roke_mes_base.tools import http_tool
import math
import json
import logging
import datetime
import requests
from io import StringIO
import traceback
from datetime import datetime, timedelta
import pytz
_logger = logging.getLogger(__name__)
headers = [("Content-Type", "application/json; charset=utf-8")]


def convert_utc_to_local(dt, tz_name='Asia/Shanghai'):
    if not dt:
        return ""
    # 假设传入的 dt 是字符串或 Odoo 的 datetime 字段
    if isinstance(dt, str):
        dt_utc = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
    else:
        dt_utc = dt

    # 设置 UTC 时间为 aware datetime
    dt_utc = pytz.utc.localize(dt_utc)

    # 转换为目标时区时间
    target_tz = pytz.timezone(tz_name)
    dt_local = dt_utc.astimezone(target_tz)

    return dt_local.strftime("%Y-%m-%d %H:%M:%S")


class ClientApiWorkOrder(http.Controller):

    def _get_work_order_details(self, equipment_id, product_id=False,state="", zuoye_id=False,limit=False, page=False):


        # workshop_id = equipment_obj.workshop_id
        # if not workshop_id:
        #     return {"code": "100", "state": "error", "msg": "系统中找不到此设备对应产线", "data": []}
        # domain = [("workshop_id", "=", workshop_id.id)]
        domain = []
        if equipment_id:
            domain.append(("equipment_id", "=", equipment_id))
        if product_id:
            domain.append(("product_id", "=", product_id))
        if state:
            domain.append(("state", "=", state)) # # 未开工/未完成/强制完工/完工
        if zuoye_id:
            # domain.append(("workshop_id.employee_ids.employee_id", "=", int(zuoye_id)))
            domain.append(('record_ids.employee_ids','=',zuoye_id))
        if limit and page:
            offset = (page - 1) * limit
            all_roke_work_orders = http.request.env["roke.work.order"].sudo().search(domain,  limit=limit, offset=offset)
        else:
            all_roke_work_orders = http.request.env["roke.work.order"].sudo().search(domain)

        if not all_roke_work_orders:
            return {"code": "100", "state": "error", "msg": "该设备对应的产品或者状态或者作业人,没有工单", "data": []}

        work_order_list = list()
        for work_order in all_roke_work_orders:
            zuoye_names = work_order.record_ids.mapped("employee_ids").mapped('name')
            if zuoye_names:
                zuoye_names = ",".join(work_order.record_ids.mapped("employee_ids").mapped('name'))
            else:
                zuoye_names = ""
            allow_qty, default_qty = work_order._get_wo_allow_qty()
            vals = {
                "work_order_id":work_order.id,
                "code": work_order.code,
                "state": work_order.state,
                'product_id':work_order.product_id.id,
                "product_name": work_order.product_id.name,
                "plan_qty": work_order.plan_qty,
                "process_name": work_order.process_id.name or '',
                "finish_qty": work_order.finish_qty,
                "no_finish_qty": work_order.plan_qty - work_order.finish_qty,
                "employee_names": ",".join(work_order.employee_ids.mapped("name")),  # 指派人
                "zuoye_names": zuoye_names, # 作业任务 报工记录的人员
                "uom_id": work_order.uom_id.id,
                'uom_name': work_order.uom_id.name,
                "wo_start_time": convert_utc_to_local(work_order.wo_start_time)[5:] if work_order.wo_start_time else "", # 开工时间
                "task_id": work_order.task_id.id or None,
                "task_code": work_order.task_id.code or '',
                # "work_center_id": work_order.work_center_id.id,
                # "work_center_name": work_order.work_center_id.name,
                "workshop_id": work_order.workshop_id.id or None,
                "workshop_name": work_order.workshop_id.name or '', #工作中心
                "dispatch_time":convert_utc_to_local(work_order.dispatch_time), # 下单日期
                'wait_qty': allow_qty,
            }
            work_order_list.append(vals)
        result =  {
            "code": "200",
            "state": "success",
            "msg": "",
            "data": work_order_list,
        }
        return result
    @http.route("/roke/work_order/work_order_list", type="json", methods=["POST", "OPTIONS"], auth="user",csrf=False,
                cors="*")
    def get_work_order_list(self):
        equipment_id = http.request.jsonrequest.get("equipment_id", False)
        product_id = http.request.jsonrequest.get("product_id", False)
        state = http.request.jsonrequest.get("state", "")
        zuoye_id = http.request.jsonrequest.get("zuoye_id", False)
        limit = http.request.jsonrequest.get("limit", False)
        page = http.request.jsonrequest.get("page", 1)
        # if not equipment_id:
        #     return {"code": "100", "state": "error", "msg": "所传设备ID不能为空", "data": []}
        result = self._get_work_order_details(equipment_id, product_id,state,  zuoye_id,  limit, page)
        return result

    @http.route("/roke/work_order/create_work_record", type="json", methods=["POST", "OPTIONS"], auth="user", csrf=False,
                cors="*")
    def create_work_record(self):
        work_order_id = http.request.jsonrequest.get("work_order_id", False)
        wait_qty = http.request.jsonrequest.get("wait_qty", 0)
        if not work_order_id:
            return {"code": "100", "state": "error", "msg": "所传工单ID不能为空", "work_record_id": []}
        item_list = http.request.jsonrequest.get("item_list", [])
        if not item_list:
            return {"code": "100", "state": "error", "msg": "所传报工记录不能为空", "work_record_id": []}
        try:
            new_records = None
            work_order_obj = http.request.env["roke.work.order"].browse(work_order_id)
            if work_order_obj.wo_start_state == "未开工":
                return {"code": "100", "state": "error", "msg": "当前工单未开工，禁止报工", "work_record_id": []}
            if work_order_obj.wo_child_type == "main" and work_order_obj.child_wo_ids:
                return {"code": "100", "state": "error", "msg": "主工序工单禁止报工", "work_record_id": []}
            if work_order_obj.state in ["暂停", "强制完工"]:
                return {"code": "100", "state": "error", "msg": "工单（{}）状态是{}不允许报工".
                format(work_order_obj.code, work_order_obj.state), "work_record_id": []}
            all_qty = sum(map(lambda x: x.get('finish_qty', 0), item_list))
            if all_qty > wait_qty:
                return {"code": "100", "state": "error", "msg": "报工数量不能大于可报工数量", "work_record_id": []}
            work_record_list = list()
            for item in item_list:
                zuoye_id = int(item.get("zuoye_id", 0))
                finish_qty = float(item.get("finish_qty", 0))
                work_hours = item.get("work_hours", 0)
                unqualified_qty = float(item.get("unqualified_qty", 0))
                # allot_records = [
                #     (0, 0, {
                #         'employee_id': emp_id,
                #         'weighted': 1,
                #         'proportion': 100 / len(employee_ids) if employee_ids else 100  # 均分比例
                #     }) for emp_id in [zuoye_id]
                # ]
                val = {
                    "code": http.request.env['ir.sequence'].next_by_code('roke.work.record.code'),
                    "work_order_id": work_order_obj.id,
                    "product_id": work_order_obj.product_id.id,
                    "uom_id": work_order_obj.uom_id.id,
                    "process_id": work_order_obj.process_id.id,
                    "team_id": work_order_obj.team_id.id,
                    "employee_ids": [zuoye_id],
                    "work_center_id": work_order_obj.work_center_id.id,
                    # "classes_id": work_order_obj.classes_id.id,
                    "finish_qty": finish_qty,
                    "unqualified_qty": unqualified_qty,
                    "work_hours": work_hours,
                    "customer_id": work_order_obj.customer_id.id,
                    "work_time": convert_utc_to_local(fields.Datetime.now()),
                    "device_info": '',
                    "note": 'app 报工',
                    "allot_ids":[(0, 0, {
                                'employee_id': zuoye_id,
                                'weighted': 1,
                                'proportion': 100
                            })],
                    "count_record": '',
                }

                work_record_list.append((0,0,val))

            work_order_obj.write({
                "record_ids":  work_record_list
            })
            new_records = work_order_obj.record_ids
            for new_record in new_records:
                work_order_obj.finish_report_work_order(new_record.finish_qty, 0, new_record.work_hours,
                                                    finish_time=new_record.work_time, wr_id=new_record.id)


            return {"code": "200", "state": "success", "msg": "报工记录创建成功", "work_record_id": new_records.ids}

        except Exception as e:
            buff = StringIO()
            traceback.print_exc(file=buff)
            trace_e = buff.getvalue()
            if new_records:
                _logger.error(new_records)
                for new_record in new_records:
                    try:
                        new_records.withdraw()
                    except Exception as withdraw_error:
                        new_record.unlink()
            http.request.env.cr.rollback()
            return {"code": "100", "state": "error", "msg": "报工记录创建失败:%s"%trace_e, "work_record_id": []}
    @http.route("/roke/work_order/get_equipment_zuoye_employees", type="json", methods=["POST", "OPTIONS"], auth="none",
            csrf=False, cors="*")
    def get_equipment_zuoye_employees(self):
        data = http.request.jsonrequest
        equipment_id = data.get("equipment_id", False)
        page = int(data.get("page", 1))
        page_size = int(data.get("limit", False))  # 默认每页20条

        # if not equipment_id:
        #     return {"code": "100", "state": "error", "msg": "所传设备ID不能为空"}

        # 获取设备对象
        if equipment_id:
            equipment_obj = http.request.env["roke.mes.equipment"].sudo().search([("id", "=", equipment_id)], limit=1)
            if not equipment_obj:
                return {"code": "100", "state": "error", "msg": "系统中找不到此设备", "data": []}

            # 获取设备所在车间
            workshop = equipment_obj.workshop_id
            if not workshop:
                return {"code": "100", "state": "error", "msg": "该设备未绑定车间", "data": []}

            # 获取所有员工记录
            employee_rels = workshop.employee_ids.mapped("employee_id")
            # 假设这是一个 roke.mes.workshop.employee 类型的关系表记录集
            if not employee_rels:
                return {"code": "100", "state": "error", "msg": "该车间下的产线没有关联员工", "data": []}
        else:
            employee_rels = http.request.env["roke.employee"].sudo().search([])
        if page and page_size:
            # 分页处理
            start = (page - 1) * page_size
            end = start + page_size
            paginated_rels = employee_rels[start:end]
        else:
            paginated_rels = employee_rels

        # 构造返回数据
        employee_list = []
        for emp_rel in paginated_rels:
            # employee = emp_rel.employee_id
            employee_list.append({
                "employee_id": emp_rel.id,
                "name": emp_rel.name,
                "code": emp_rel.code,
            })

        return {
            "code": "200",
            "state": "success",
            "msg": "",
            "data": employee_list
        }









