# -*- coding: utf-8 -*-
"""
Description:
    报工相关接口
Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.tools.float_utils import float_round
from odoo.addons.roke_mes_base.tools import http_tool
import datetime
import time
from time import sleep
import math
import json
import logging
_logger = logging.getLogger(__name__)
headers = [('Content-Type', 'application/json; charset=utf-8')]
submit_timestamp = {}  # 用户报工记录，同一用户一秒内仅可报一次
product_timestamp = {}  # 用户报工记录，同一产品3秒内仅可报一次


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


def float_to_time(hours):
    """ 将小时数转换为时间对象. """
    if hours == 24.0:
        return datetime.time.max
    fractional, integral = math.modf(hours)
    return datetime.time(int(integral), int(float_round(60 * fractional, precision_digits=0)), 0)


def get_team_category_list():
    # 用户id
    user_id = http.request.uid or http.request.session.uid
    # 人员id
    emp = http.request.env['roke.employee'].search([('user_id', '=', user_id)], limit=1)
    category_list = [e.id for e in emp.team_id.product_category_ids]
    return category_list


class Production(http.Controller):

    def _get_work_standard(self, work_order, file_type=None):
        """
        获取作业规范
        :return:
        """

        standard_list = []
        for item in work_order.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自工单数据】"
            })
        for item in work_order.product_id.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自产品数据】"
            })
        for item in work_order.process_id.category_id.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自工序类别】"
            })
        for item in work_order.work_center_id.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自工作中心】"
            })
        return standard_list

    def _get_work_order(self, work_order, detail=False, file_type=None):
        """
        获取工单信息
        :param work_order:
        :return:
        """
        allow_qty, default_qty = work_order._get_wo_allow_qty()

        next_process_dict = {}  # 下道工序
        move_qty = default_qty if default_qty > 0 else 0  # 默认流转数
        # 获取下道工序
        next_wos = work_order.task_id.work_order_ids.filtered(lambda wo: wo.sequence > work_order.sequence)
        if next_wos:
            next_wo = sorted(next_wos, key=lambda x: x['sequence'], reverse=False)[0]
            next_process_dict = {
                "id": next_wo.process_id.id,
                "name": "%s(%s)" % (next_wo.process_id.name, next_wo.process_id.code)
            }
        # 判断是否末道工序，末道工序可录入产品状态
        product_state = work_order.last
        # 额定工时
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        exceeding_rated_wh_val = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
        exceeding_rated_wh = http_tool.selection_to_dict("res.config.settings", "exceeding_rated_wh")[
            exceeding_rated_wh_val]
        autofill_rated_wh = True if ConfigParameter.get_param('autofill.rated.working.hours') == "1" else False
        # 作业规范
        standard_list = self._get_work_standard(work_order, file_type=file_type)
        if detail:
            collection_list = []
            collection_result_list = []
            try:
                for item in work_order.collection_item_ids:
                    collection_list.append({
                        "id": item.id,
                        "name": item.name,
                        "data_type": item.data_type,
                        "dict_model_index": item.dict_model_index or "",
                        "required": item.required,
                        "standard_value": item.standard_value_str,
                        "single_items": item.select_item_ids.mapped("value") if item.data_type == "单选" else [],
                        "multiple_items": item.select_item_ids.mapped("value") if item.data_type == "多选" else []
                    })
                for result in work_order.collection_result_ids:
                    result_val = result.result
                    if result.collection_item_id.data_type == "多选":
                        result_val = result_val.split("，")
                    collection_result_list.append({
                        "id": result.collection_item_id.id,
                        "name": result.collection_item_id.name,
                        "standard_value": result.standard_value_str,
                        "result": result_val
                    })
            except Exception as e:
                pass
            employee_list = []
            for employee in work_order.employee_ids:
                employee_list.append({
                    "id": employee.id,
                    "name": employee.display_name,
                })
            # 校验是否可报工
            return {
                "id": work_order.id,
                "code": work_order.code or "",
                "order_code": work_order.order_id.display_name or "",
                "customer": work_order.customer_id.display_name or "",
                "product": work_order.product_id.display_name or "",
                "uom": work_order.uom_id.name or "",
                "auxiliary_uom": "",
                "process": work_order.process_id.display_name or "",
                "process_id": work_order.process_id.id or "",
                "plan_qty": round(work_order.plan_qty, _get_pd(http.request.env())),  # 计划数量
                "wait_qty": default_qty or 0,  # 待完成数
                "finish_qty": work_order.finish_qty or 0,  # 已完成数量
                "progress": round(work_order.finish_qty / work_order.plan_qty * 100) if work_order.plan_qty else 0,  # 进度%
                "allow_qty": default_qty or 0,  # 允许数量
                "custom_move": False,
                "next_process_dict": next_process_dict,
                "move_qty": move_qty,
                "allow": allow_qty > 0,  # 允许报工
                "plan_date": work_order.plan_date.strftime('%y/%m/%d') if work_order.plan_date else "",
                "priority": work_order.priority,
                "state": work_order.state,
                "project_code": work_order.order_id.project_code or "",
                "work_center": {"id": work_order.work_center_id.id, "name": work_order.work_center_id.display_name or ""},
                "workshop": {"id": work_order.workshop_id.id, "name": work_order.workshop_id.display_name or ""},
                "team": {"id": work_order.team_id.id, "name": work_order.team_id.display_name or ""},
                "employee": employee_list,
                "standard_list": standard_list,
                "file_url": work_order.get_instruction_file_url(file_type=file_type) or [],
                "collection_list": collection_list,
                "collection_result_list": collection_result_list,
                "salary_type": "",
                "salary_rule": "",
                "team_salary_type": "",
                "allow_input_material": False,  # 允许投料
                "material_demand_list": [],  # 物料需求
                "scan_lot": "none",  # 绑定产成品条码方式
                "allow_by_product": False,  # 是否允许报工副产品
                "by_products": [],   # 允许报工的副产品列表
                "quantity": False,
                "task_type": work_order.type or work_order.task_id.type or "",
                "allow_change_product": False,  # 是否允许变更报工产品
                "rated_working_hours": work_order.rated_working_hours,
                "exceeding_rated_wh": exceeding_rated_wh,
                "autofill_rated_wh": autofill_rated_wh,
                "planned_start_time":work_order.planned_start_time or "",
                "planned_finish_time":work_order.plan_date or "",
                "unqualified_qty":work_order.unqualified_qty or 0,
                "work_hours":work_order.work_hours or 0,
                "product_state": product_state,
                "wo_start_state": work_order.wo_start_state or "",
                "wo_start_time": work_order.wo_start_time.strftime('%y/%m/%d %H:%M:%S') if work_order.wo_start_time else "",
                "wo_finish_time": work_order.wo_finish_time.strftime('%y/%m/%d %H:%M:%S') if work_order.wo_finish_time else "",
                "wo_duration": work_order.wo_duration or "",
                "wo_manual_start": work_order.wo_manual_start or "",
                "wo_manual_finish": work_order.wo_manual_finish or "",
                "deadline": work_order.deadline or ""
            }
        return {
            "id": work_order.id,
            "code": work_order.code or "",
            "order_code": work_order.order_id.display_name or "",
            "project_code": work_order.order_id.project_code or "",
            "customer": work_order.customer_id.display_name or "",
            "product": work_order.product_id.display_name or "",
            "uom": work_order.uom_id.name or "",
            "work_center": {"id": work_order.work_center_id.id, "name": work_order.work_center_id.display_name or ""},
            "workshop": {"id": work_order.workshop_id.id, "name": work_order.workshop_id.display_name or ""},
            "team": {"id": work_order.team_id.id, "name": work_order.team_id.display_name or ""},
            "auxiliary_uom": "",
            "process": work_order.process_id.display_name or "",
            "process_id": work_order.process_id.id,
            "plan_qty": round(work_order.plan_qty, _get_pd(http.request.env())),  # 计划数量
            "wait_qty": default_qty or 0,  # 待完成数
            "finish_qty": work_order.finish_qty or 0,  # 已完成数量
            "unqualified_qty": work_order.unqualified_qty or 0,
            "work_hours": work_order.work_hours or 0,
            "progress": round(work_order.finish_qty / work_order.plan_qty * 100) if work_order.plan_qty else 0,  # 进度%
            "allow_qty": default_qty,  # 允许数量
            "allow": allow_qty > 0,  # 允许报工
            "plan_date": work_order.plan_date.strftime('%y/%m/%d') if work_order.plan_date else "",
            "priority": work_order.priority,
            "custom_move": False,
            "state": work_order.state,
            "standard_list": standard_list,
            "file_url": work_order.get_instruction_file_url(file_type=file_type) or [],
            "allow_input_material": False,  # 允许投料
            "quantity": False,
            "task_type": work_order.task_id.type or "",
            "allow_change_product": False,  # 是否允许变更报工产品
            "rated_working_hours": work_order.rated_working_hours or 0,
            "exceeding_rated_wh": exceeding_rated_wh,
            "autofill_rated_wh": autofill_rated_wh,
            "planned_start_time": work_order.planned_start_time or "",
            "planned_finish_time": work_order.plan_date or "",
            "team_id": work_order.team_id.name or "",
            "employee_ids": work_order.employee_ids.mapped("name"),
            "product_state": product_state,
            "assigned_state": "已指派" if work_order.work_center_id or work_order.team_id or work_order.employee_ids else "未指派",
            "wo_start_state": work_order.wo_start_state or "",
            "wo_start_time": work_order.wo_start_time.strftime('%y/%m/%d %H:%M:%S') if work_order.wo_start_time else "",
            "wo_finish_time": work_order.wo_finish_time.strftime('%y/%m/%d %H:%M:%S') if work_order.wo_finish_time else "",
            "wo_duration": work_order.wo_duration,
            "wo_manual_start": work_order.wo_manual_start or "",
            "wo_manual_finish": work_order.wo_manual_finish or "",
            "deadline": work_order.deadline or ""
        }

    def _get_work_record(self, work_record, detail=False):
        """
        获取报工记录信息
        :param work_record:
        :return:
        """
        salary_subtotal_list = []
        allot_records = http.request.env['roke.work.record.allot'].search([('work_record_id', '=', work_record.id)])
        for allot_record in allot_records:
            salary_subtotal_list.append({
                "employee_id": allot_record.employee_id.display_name,
                "salary_subtotal": allot_record.salary_subtotal,
            })
        return {
            "id": work_record.id,
            "without_order": True if not work_record.pt_id and not work_record.result_ids else False,  # 是否无工单报工，是否允许编辑报工
            "code": work_record.code or "",
            "customer": work_record.customer_id.display_name or "",
            "order_code": work_record.work_order_id.sudo().order_id.code or "",
            "task_code": work_record.pt_id.code or "",
            "project_code": work_record.project_code or "",
            "product_code": work_record.product_id.code or "",
            "product": work_record.product_id.display_name or "",
            "process": work_record.process_id.display_name or "",
            "report_qty": round(work_record.finish_qty + work_record.unqualified_qty, _get_pd(http.request.env())),
            "finish_qty": round(work_record.finish_qty, _get_pd(http.request.env())),
            "unqualified_qty": round(work_record.unqualified_qty, _get_pd(http.request.env())),
            "work_hours": work_record.work_hours,
            "report_time": (work_record.work_time + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
            "work_center": work_record.work_center_id.display_name or "",
            "team": work_record.team_id.display_name or "",
            "employee": ",".join(work_record.employee_ids.mapped("name")) or "",
            "salary": 0,
            "salary_type": "",
            "can_print": len(work_record.result_ids) > 0,
            "create_user": work_record.create_uid.name,
            "dispatch_qty": work_record.work_order_id.sudo().plan_qty or 0,
            "dispatch_time": (work_record.work_order_id.sudo().dispatch_time + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M') if work_record.work_order_id.sudo().dispatch_time else "",
            "note": work_record.note or "",
            "salary2": work_record.process_id.salary,
            "salary_subtotal_list": salary_subtotal_list,
            "rated_work_hours": work_record.work_order_id.sudo().rated_working_hours or 0,
            "average_work_hours": ((work_record.work_order_id.sudo().rated_working_hours or 0) /
                                   round(work_record.finish_qty or 0.0, _get_pd(http.request.env()))) if
            round(work_record.finish_qty or 0.0, _get_pd(http.request.env())) else 0
        }

    @http.route('/roke/get_wo_standard_file', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_wo_standard_file(self):
        order_id = http.request.jsonrequest.get('order_id', False)
        file_type = http.request.jsonrequest.get('file_type', False)
        if not order_id:
            # 创建调用记录
            return {"state": "error", "msgs": "必须入参order_id"}
        try:
            work_order = http.request.env['roke.work.order'].browse(int(order_id))
        except Exception as e:
            _logger.error(e)
            work_order = http.request.env['roke.work.order'].search([("code", "=", order_id)])
            if not work_order:
                return {"state": "error", "msgs": "扫码内容【%s】格式错误或未找到相关记录" % str(order_id)}
        standard_list = self._get_work_standard(work_order, file_type=file_type)
        return {
            "state": "success",
            "msgs": "获取成功",
            "standard_list": standard_list,
            "file_url": work_order.get_instruction_file_url(file_type=file_type) or []
        }

    @http.route('/roke/get_auxiliary_is_installed', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_auxiliary_is_installed(self):
        ir_module_obj = http.request.env['ir.module.module']
        aux_module_record = ir_module_obj.sudo().search([('name', '=', 'roke_mes_auxiliary_uom')], limit=1)
        is_installed = True if aux_module_record.state == "installed" else False
        return {
            'is_installed': is_installed
        }

    @http.route('/roke/mes/work_start', type='http', methods=['GET', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_start(self):
        """
        开工，记录开工时间
        :return:
        """
        uid = http.request.uid or http.request.session.uid
        if uid:
            # 将未停工的记录置为已停工
            http.request.env(user=SUPERUSER_ID)['roke.work.start.record'].search([
                ("user_id", "=", uid),
                ("state", "=", "工作中")
            ]).write({
                "stop_time": fields.Datetime.now(),
                "state": "已停工"
            })
            start_record = http.request.env(user=SUPERUSER_ID)['roke.work.start.record'].create({
                "user_id": uid
            })
            return http.Response(json.dumps({
                "state": "success",
                "msgs": "开工成功",
                "start_time": (start_record.start_time + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S"),
                "suspend_duration": 0
            }), headers=headers)
        else:
            return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未登录，请先登录"}), headers=headers)

    @http.route('/roke/mes/work_stop', type='http', methods=['GET', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_stop(self):
        """
        停工，记录停工时间
        :return:
        """
        uid = http.request.uid or http.request.session.uid
        if uid:
            start_record = http.request.env(user=SUPERUSER_ID)['roke.work.start.record'].search([
                ("user_id", "=", uid),
                ("state", "=", "工作中")
            ], limit=1)
            start_record.write({
                "stop_time": fields.Datetime.now(),
                "state": "已停工"
            })
            # 暂停状态下直接停工，那么写一下暂停记录的结束时间
            if start_record.is_suspend:
                start_record.restart()
            return http.Response(json.dumps({"state": "success", "msgs": "停工成功"}), headers=headers)
        else:
            return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未登录，请先登录"}), headers=headers)

    @http.route('/roke/mes/work_suspend', type='http', methods=['GET', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_suspend(self):
        """
        暂停
        :return:
        """
        uid = http.request.uid or http.request.session.uid
        if uid:
            start_record = http.request.env(user=SUPERUSER_ID)['roke.work.start.record'].search([
                ("user_id", "=", uid),
                ("state", "=", "工作中"),
                ("is_suspend", "=", False)
            ])
            if not start_record:
                return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未开工或已暂停，无法执行暂停操作"}), headers=headers)
            start_record.suspend()
            return http.Response(json.dumps({"state": "success", "msgs": "暂停成功"}), headers=headers)
        else:
            return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未登录，请先登录"}), headers=headers)

    @http.route('/roke/mes/work_restart', type='http', methods=['GET', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_restart(self):
        """
        恢复
        :return:
        """
        uid = http.request.uid or http.request.session.uid
        if uid:
            start_record = http.request.env(user=SUPERUSER_ID)['roke.work.start.record'].search([
                ("user_id", "=", uid),
                ("state", "=", "工作中"),
                ("is_suspend", "=", True)
            ])
            start_record.restart()
            if not start_record:
                return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未开工或不是暂停状态，无法执行恢复操作"}), headers=headers)
            return http.Response(json.dumps({
                "state": "success",
                "msgs": "恢复成功",
                "start_time": (start_record.start_time + datetime.timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S"),
                "suspend_duration": sum(start_record.suspend_ids.mapped("suspend_duration"))
            }), headers=headers)
        else:
            return http.Response(json.dumps({"state": "error", "msgs": "当前位置尚未登录，请先登录"}), headers=headers)

    def _get_work_records_total(self, work_records, today):
        """
        获取报工记录的合计
        :param work_records:
        :return:
        """
        return {
            "state": "success",
            "msgs": "获取成功",
            "today": today,
            "finish_qty": round(sum(work_records.mapped("finish_qty")), _get_pd(http.request.env())),
            "unqualified_qty": round(sum(work_records.mapped("unqualified_qty")), _get_pd(http.request.env())),
            "scrap_qty": 0,
            "repair_qty": 0,
            "salary": 0,
        }

    # 获取当日绩效
    @http.route('/roke/get_toady_totals', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_toady_totals(self):
        """
        获取当日绩效
        TODO 多人使用一个账号时，当日绩效查询区分
        :return:
        """
        today = fields.Date.today()
        start_date = datetime.datetime.combine(today, datetime.time()) - datetime.timedelta(hours=8)
        end_date = datetime.datetime.combine(today, datetime.time()) + datetime.timedelta(days=1, hours=-8)
        work_records = http.request.env['roke.work.record'].search([
            ("create_uid", "=", http.request.env.user.id),
            ("work_time", ">=", start_date),
            ("work_time", "<=", end_date)
        ])
        employee_qty = len(set(work_records.mapped("employee_ids")))
        return_data = self._get_work_records_total(work_records, "%s月%s日,共%s人" % (today.month, today.day, str(employee_qty)))
        return return_data

    # 获取报工记录
    @http.route('/roke/work_record_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_record_list(self):
        """
        获取报工记录
        筛选条件：编号、人员、班组、工作中心、产品、工序、客户、项目号、订单号、任务号、报工时间开始、报工时间结束
        :return:
        """
        code = http.request.jsonrequest.get('code', False)
        wo_id = http.request.jsonrequest.get('wo_id', False)
        employee_id = http.request.jsonrequest.get('employee_id', False)
        team_id = http.request.jsonrequest.get('team_id', False)
        work_center_id = http.request.jsonrequest.get('work_center_id', False)
        product_id = http.request.jsonrequest.get('product_id', False)
        process_id = http.request.jsonrequest.get('process_id', False)
        customer_id = http.request.jsonrequest.get('customer_id', False)
        project_code = http.request.jsonrequest.get('project_code', False)
        order_code = http.request.jsonrequest.get('order_code', False)
        task_code = http.request.jsonrequest.get('task_code', False)
        date_start = http.request.jsonrequest.get('date_start', False)
        date_end = http.request.jsonrequest.get('date_end', False)
        work_code = http.request.jsonrequest.get('work_code', False)

        groups_field = http.request.jsonrequest.get('groups_field', False)  # 工序process/成品product/客户customer
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数

        if code:
            domain = [("code", "=", code)]
        else:
            domain = []
            if wo_id:
                domain.append(("work_order_id", "=", int(wo_id)))
            if product_id:
                domain.append(("product_id", "=", int(product_id)))
            if process_id:
                domain.append(("process_id", "=", int(process_id)))
            if customer_id:
                domain.append(("customer_id", "=", int(customer_id)))
            if project_code:
                domain.append(("project_code", "ilike", project_code))
            if order_code:
                domain.append(("work_order_id.order_id.code", "ilike", order_code))
            if employee_id:
                domain.append(("employee_ids", "in", int(employee_id)))
            if team_id:
                domain.append(("team_id", "=", int(team_id)))
            if task_code:
                domain.append(("work_order_id.task_id.code", "=", task_code))
            if work_center_id:
                domain.append(("work_center_id", "=", int(work_center_id)))
            if date_start:
                domain.append(("work_time", ">=", date_start))
            if date_end:
                domain.append(("work_time", "<=", date_end))
            if work_code:
                domain.append(("work_code", "ilike", work_code))

        myself = http.request.jsonrequest.get('myself', False)
        if myself:
            domain.append(("create_uid", "=", http.request.uid))
        if groups_field:
            groups_field_dict = {"process": "process_id", "product": "product_id", "customer": "customer_id"}
            if page_size:
                work_records = http.request.env['roke.work.record'].sudo().web_read_group(domain=domain, groupby=[
                    groups_field_dict.get(groups_field, "")],
                    fields=["finish_qty", "unqualified_qty", "plan_qty", "work_hours"], limit=page_size,
                    offset=(page_no - 1) * page_size)
            else:
                work_records = http.request.env['roke.work.record'].sudo().web_read_group(domain=domain, groupby=[
                    groups_field_dict.get(groups_field, "")],
                    fields=["finish_qty", "unqualified_qty", "plan_qty", "work_hours"])
            for v in work_records.get("groups", []):
                finish_qty = v.get("finish_qty", 0)
                unqualified_qty = v.get("unqualified_qty", 0)
                report_qty = finish_qty + unqualified_qty
                name = v.get(groups_field_dict.get(groups_field, ""), "")
                if finish_qty == 0:
                    v["pass_rate"] = 0
                else:
                    v["pass_rate"] = round(finish_qty / report_qty, 4) * 100
                v["report_qty"] = report_qty
                if type(name) != tuple:
                    v["name"] = name or ""
                else:
                    v["name"] = (name[1] or "") if name.__len__() == 2 else (name or "")
            total_number = work_records.get("length", 0)
            total_page = math.ceil(total_number / page_size) if page_size else 0
            return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                           "total_number": total_number, "result": work_records.get("groups", [])}
            return return_data
        if page_size:
            work_records = http.request.env['roke.work.record'].search(domain, limit=page_size,
                                                                       offset=(page_no - 1) * page_size,
                                                                       order="create_date desc")
        else:
            work_records = http.request.env['roke.work.record'].search(domain, order="create_date desc")
        # 处理分页
        total_number = http.request.env['roke.work.record'].search_count(domain)
        total_page = math.ceil(total_number / page_size) if page_size else 0  # 总页数
        result = []
        for work_record in work_records:
            result.append(self._get_work_record(work_record))
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    # 获取报工记录
    @http.route('/roke/pt_work_record_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def pt_work_record_list(self):
        """
        获取任务报工记录
        :return:
        """
        pt_id = http.request.jsonrequest.get('pt_id', False)
        query = http.request.jsonrequest.get('query', False)
        if not pt_id:
            return {"state": "error", "msgs": "必须入参任务ID"}
        product_task = http.request.env['roke.production.task'].browse(pt_id)
        work_records = product_task.record_ids
        if query:
            work_records = work_records.filtered(lambda wr: query in wr.process_id.name or query in "".join(wr.employee_ids.mapped("name")))
        work_orders = work_records.mapped("work_order_id").sorted(key="sequence")
        result = []
        i = 1
        for work_order in work_orders:
            wo_wrs = work_records.filtered(lambda wr: wr.work_order_id == work_order)
            work_record_list = []
            for wo_wr in wo_wrs:
                work_record_list.append(self._get_work_record(wo_wr))
            result.append({
                "process_name": "%s-%d" % (work_order.process_id.name, i),
                "qty": len(wo_wrs),
                "work_record_list": work_record_list
            })
            i += 1
        return_data = {"state": "success", "msgs": "获取成功", "result": result}
        return return_data

    # 获取工单列表
    def _get_wo_list_domain(self, values):
        code = values.get('code', False)
        product_id = values.get('product_id', False)
        process_id = values.get('process_id', False)
        customer_id = values.get('customer_id', False)
        project_code = values.get('project_code', False)
        order_code = values.get('order_code', False)
        date_start = values.get('date_start', False)
        date_end = values.get('date_end', False)
        date_finish_time_start = values.get('date_finish_time_start', False)
        date_finish_time_end = values.get('date_finish_time_end', False)
        priority = values.get('priority', False)
        state = values.get('state', False)
        work_center_id = values.get('work_center_id', False)
        search_type = values.get('search_type', False)
        not_assigned = values.get('not_assigned', False)  # 未指派
        self_wo = values.get('self_wo', False)
        delivery_today = values.get('delivery_today', False)  # true
        overdue = values.get('overdue', False)  # true
        domain = [("product_id", "!=", False), ("child_wo_ids", "=", False)]  # 工单列表禁止获取存在子工序的工单

        if delivery_today:
            domain.append([
                ("planned_start_time", "<=", datetime.datetime.now().strftime('%Y-%m-%d')),
                ("plan_date", ">=", datetime.datetime.now().strftime('%Y-%m-%d'))
            ])
        if overdue:
            domain.append(("plan_date", "<", datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            domain.append(("state", "in", ["未完工", "暂停"]))

        if code and search_type == '工单':
            domain.append(("code", "ilike", code))
        if code and search_type == '任务':
            domain.append(("task_id.code", "ilike", code))
        if code and search_type == '生产订单':
            domain.append(("order_id.code", "ilike", code))
        if product_id:
            domain.append(("product_id", "=", int(product_id)))
        if process_id:
            domain.append(("process_id", "=", int(process_id)))
        if customer_id:
            domain.append(("customer_id", "=", int(customer_id)))
        if project_code:
            domain.append(("project_code", "ilike", project_code))
        if order_code:
            domain.append(("order_id.code", "ilike", order_code))
        if priority:
            domain.append(("priority", "=", priority))
        if state:
            domain.append(("state", "=", state))
        if date_start:
            domain.append(("plan_date", ">=", date_start))
        if date_end:
            domain.append(("plan_date", "<=", date_end))
        if date_finish_time_start:
            domain.append(("plan_date", ">=", date_finish_time_start))
        if date_finish_time_end:
            domain.append(("plan_date", "<=", date_finish_time_end))
        if work_center_id:
            domain.append(("work_center_id", "=", int(work_center_id)))
        if not_assigned:
            domain.append(("work_center_id", "=", False))
            domain.append(("team_id", "=", False))
            domain.append(("employee_ids", "=", False))
        if self_wo:
            employee = http.request.env['roke.employee'].search([("user_id", "=", http.request.env.user.id)])
            if employee:
                domain.append(("employee_ids", "in", [employee.id]))
        return domain

    def filtered_work_order(self, work_orders, values):
        """
        筛选工单
        :return:
        """
        if values.get('assigned', False):
            return work_orders.filtered(lambda wo: wo.work_center_id or wo.team_id or wo.employee_ids)
        return work_orders

    @http.route('/roke/work_order_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_order_list(self):
        """
        获取工单列表
        筛选条件：编号、产品、工序、客户、项目号、订单号、交期开始、交期结束
        :return:
        """
        values = http.request.jsonrequest

        order_by = http.request.jsonrequest.get('order_by', "asc")  # 正序asc/倒叙desc
        order_by_field = http.request.jsonrequest.get('order_by_field', False)  # 优先级priority/完成时间plan_date
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        domain = self._get_wo_list_domain(values)
        if domain is None:
            return {"state": "error",
                    "msgs": "筛选的工序未被允许，请联系管理员设置：生产管理-报工默认配置-添加允许的工序"}

        if values.get('assigned', False):
            domain.append("|")
            domain.append(("work_center_id", "!=", False))
            domain.append("|")
            domain.append(("employee_ids", "!=", False))
            domain.append(("team_id", "!=", False))
        if order_by_field:
            work_orders = http.request.env['roke.work.order'].with_context(
                check_allow_process=True
            ).search(domain, order=f"{order_by_field} {order_by}", limit=page_size, offset=(page_no - 1) * page_size)
        else:
            work_orders = http.request.env['roke.work.order'].with_context(
                check_allow_process=True
            ).search(domain, limit=page_size, offset=(page_no - 1) * page_size)
        total_number = http.request.env['roke.work.order'].with_context(check_allow_process=True).search_count(domain)
        # work_orders = self.filtered_work_order(work_orders, values)
        # 处理分页
        total_page = math.ceil(total_number / page_size) if page_size else 0
        result = []
        for work_order in work_orders:
            result.append(self._get_work_order(work_order))
        return_data = {"state": "success",
                       "msgs": "获取成功",
                       "page_no": page_no,
                       "total_page": total_page,
                       "total_number": total_number,
                       "result": result}
        return return_data

    @http.route('/roke/employee_assigned_qty', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def employee_assigned_qty(self):
        """
        工单指派人员
        :return:
        """
        values = http.request.jsonrequest
        start_date = values.get("start_date")
        end_date = values.get("end_date")
        employee_ids = values.get("employee_ids") or []
        employees = http.request.env(user=SUPERUSER_ID)['roke.employee'].browse(employee_ids)
        # 默认未来一周
        if not start_date or not end_date:
            start_date = fields.Date.context_today(http.request)
            end_date = start_date + datetime.timedelta(days=7)
        # 获取工单
        work_orders = http.request.env(user=SUPERUSER_ID)['roke.work.order'].search([
            ("plan_date", ">=", start_date), ("plan_date", "<", end_date), ("employee_ids", "in", employees.ids)
        ])
        date_list = []
        employee_datas = {}
        wo_date = start_date
        while wo_date < end_date:
            date_list.append(wo_date.strftime("%Y-%m-%d"))
            for employee in employees:
                employee_qty = sum(work_orders.filtered(lambda wo: employee in wo.employee_ids and wo.plan_date == wo_date).mapped("plan_qty"))
                if employee.name in employee_datas:
                    employee_datas.get(employee.name).append(employee_qty)
                else:
                    employee_datas[employee.name] = [employee_qty]
            wo_date = wo_date + datetime.timedelta(days=1)
        employee_list = [{"name": name, "data": data} for name, data in employee_datas.items()]
        return {"state": "success", "msgs": "获取成功", "date_list": date_list, "employee_list": employee_list}

    # 获取工单详情
    @http.route('/roke/work_order_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_order_detail(self):
        """
        获取工单列表
        筛选条件：编号、产品、工序、客户、项目号、订单号、交期开始、交期结束
        :return:
        """
        order_id = http.request.jsonrequest.get('order_id', False)
        file_type = http.request.jsonrequest.get('file_type', False)
        _logger.info(order_id)
        if not order_id:
            # 创建调用记录
            return {"state": "error", "msgs": "必须入参order_id"}
        allow_process = http.request.env["roke.user.default.setting"].search([
            ("user_id", "=", http.request.env.user.id)
        ]).allow_process_ids
        try:
            work_order = http.request.env['roke.work.order'].browse(int(order_id))
        except Exception as e:
            _logger.error(e)
            work_order = http.request.env['roke.work.order'].search([("code", "=", order_id)])
            if not work_order:
                return {"state": "error", "msgs": "扫码内容【%s】格式错误或未找到相关记录" % str(order_id)}
        if work_order.child_wo_ids:
            return {
                "state": "error",
                "msgs": "主工序禁止报工，请选择该工序下的子工序进行报工"
            }
        if allow_process and work_order.process_id not in allow_process:
            return {
                "state": "error",
                "msgs": "当前登录人禁止操作工序 %s 上的工单，如果需要对这个工序报工，请先设置【生产设置-默认报工配置-允许的工序】" % work_order.process_id.display_name
            }
        result = self._get_work_order(work_order, detail=True, file_type=file_type)
        if result:
            return {"state": "success", "msgs": "获取成功", "result": result}
        else:
            return {"state": "error", "msgs": "扫码内容【%s】格式错误或未找到相关记录" % str(order_id)}

    def _process_get_salary_rule(self, process):
        """
        通过工序获取计薪规则   实现于工资模块
        :return:
        """
        return "无", "无"

    def _get_production_info(self, product, detail=False):
        return {
            "id": product.id,
            "code": product.code,
            "name": product.display_name,
            "specification": product.specification or "",
            "model": product.model or "",
            "category_name": product.category_id.name or "",
            "unit_price": product.unit_price,
            "uom_name": product.uom_id.name or ''
        }

    def _get_product_child_category(self, category, level):
        """获取下级产品类别"""
        result = []
        child_level = level + 1
        for child in category.child_ids:
            result.append({
                "id": child.id,
                "name": child.name,
                "level": child_level,
                "child_category": self._get_product_child_category(child, child_level)
            })
        return result

    # 获取产品类别
    @http.route('/roke/get_product_category', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_product_category(self):
        result = []
        domain = [("parent_id", "=", False)]
        ProductCategoryObj = http.request.env['roke.product.category']
        # 处理班组是否关联产品类别，如果存在产品类别就加过滤
        category_list = get_team_category_list()
        if category_list:
            domain.append(('id', 'in', category_list))
        category_ids = ProductCategoryObj.search(domain)
        for category_id in category_ids:
            level = 1
            result.append({
                "id": category_id.id,
                "name": category_id.name,
                "level": level,
                "child_category": self._get_product_child_category(category_id, level)
            })
        return {"state": "success", "msgs": "获取成功", "result": result}

    # 获取产品信息
    @http.route('/roke/get_product_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_product_list(self):
        """
        获取产品信息
        :return:
                """
        product_condition = http.request.jsonrequest.get('product', False)  # 产品名称或编号
        category_id = http.request.jsonrequest.get('category_id', False)  # 产品类别id
        so_id = http.request.jsonrequest.get('so_id', "")

        # 校验产品类别是否存在，若不存在则返回没有绑定产品类别的产品
        domain = []
        if category_id:
            domain.append(('category_id', '=', category_id))
        # 处理班组是否关联产品类别，如果存在产品类别就加过滤
        category_list = get_team_category_list()
        if category_list:
            domain.append(('category_id', 'in', category_list))
        if so_id:
            so = http.request.env['roke.sale.order'].browse(int(so_id))
            so_product_id = []
            for line in so.line_ids:
                so_product_id.append(line.product_id.id)
            domain.append(("id", "in", so_product_id))
        if product_condition:
            domain = ["|", ("name", "ilike", product_condition), ("code", "=", product_condition)]
        products = http.request.env['roke.product'].search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(products)
        if page_size:
            total_page = math.ceil(len(products) / page_size)  # 总页数
            products = products[(page_no-1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for product in products:
            result.append(self._get_production_info(product, detail=False))
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page, "total_number":total_number, "result": result}
        return return_data

    # 获取产品信息
    @http.route('/roke/auth_none/get_product_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def get_auth_none_product_list(self):
        """
        获取产品信息
        :return:
                """
        product_condition = http.request.jsonrequest.get('product', False)  # 产品名称或编号
        category_id = http.request.jsonrequest.get('category_id', False)  # 产品类别id
        so_id = http.request.jsonrequest.get('so_id', "")

        # 校验产品类别是否存在，若不存在则返回没有绑定产品类别的产品
        domain = []
        if category_id:
            domain.append(('category_id', '=', category_id))
        if so_id:
            so = http.request.env['roke.sale.order'].sudo().browse(int(so_id))
            so_product_id = []
            for line in so.line_ids:
                so_product_id.append(line.product_id.id)
            domain.append(("id", "in", so_product_id))
        if product_condition:
            domain = ["|", ("name", "ilike", product_condition), ("code", "=", product_condition)]
        products = http.request.env['roke.product'].sudo().search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(products)
        if page_size:
            total_page = math.ceil(len(products) / page_size)  # 总页数
            products = products[(page_no-1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for product in products:
            result.append(self._get_production_info(product, detail=False))
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page, "total_number":total_number, "result": result}
        return return_data

    # 获取产品信息
    @http.route('/roke/get_product_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_product_detail(self):
        """
        获取产品详情
        :return:
        """
        product_id = http.request.jsonrequest.get('product_id', False)  # 产品名称或编号
        if not product_id:
            return {"state": "error", "msgs": "必须入参产品ID"}
        product = http.request.env['roke.product'].browse(int(product_id))
        return_data = self._get_production_info(product, detail=True)
        return_data["state"] = "success"
        return_data["msgs"] = "获取成功"
        return return_data

    def _get_process_info(self, process):
        """
        获取工序信息
        :param process:
        :return:
        """
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        exceeding_rated_wh_val = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
        exceeding_rated_wh = http_tool.selection_to_dict("res.config.settings", "exceeding_rated_wh")[exceeding_rated_wh_val]
        autofill_rated_wh = True if ConfigParameter.get_param('autofill.rated.working.hours') == "1" else False
        return {
            "id": process.id,
            "code": process.code,
            "name": "%s(%s)" % (process.name, process.code),
            "category_name": process.category_id.name,
            "salary": process.salary,
            "rated_working_hours": process.rated_working_hours,
            "exceeding_rated_wh": exceeding_rated_wh,
            "autofill_rated_wh": autofill_rated_wh,
            "product_state": process.without_wo_produce
        }

    # 获取工序信息
    @http.route('/roke/get_process_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_process_list(self):
        """
        获取工序信息
        :return:
        """
        product_id = http.request.jsonrequest.get('product_id', False)  # 产品ID
        process_condition = http.request.jsonrequest.get('process', False)  # 工序名称或编号
        category_id = http.request.jsonrequest.get('category_id', False)
        task_id = http.request.jsonrequest.get('task_id', False)
        filtered_employee = http.request.jsonrequest.get('filtered_employee', False)

        #校验工序类别是否存在，若不存在则返回没有绑定工序类别的工序
        domain = []
        process_list = []
        default_process = False
        if product_id:
            product = http.request.env['roke.product'].browse(int(product_id))
            process_list = product.without_wo_process_ids
            # 判断是否存在默认工序
            if product.without_wo_process_ids:
                default_process = True
        if category_id:
            domain = [('category_id', '=', category_id)]
        if task_id:
            process_list = http.request.env['roke.production.task'].browse(int(task_id)).work_order_ids.process_id
            if category_id:
                process_list = process_list.filtered(lambda p: p.category_id.id == int(category_id))
        if process_condition:
            domain = ["|", ("name", "ilike", process_condition), ("code", "=", process_condition)]
        if not process_list and not task_id:
            process_list = http.request.env['roke.process'].search(domain)
        # 处理允许的工序。只返回允许的工序
        default_setting = http.request.env['roke.user.default.setting'].search([("user_id", "=", http.request.uid or http.request.session.uid)])
        if default_setting.allow_process_ids:
            process_list = process_list.filtered(lambda p: p in default_setting.allow_process_ids)
        # 根据人员筛选
        if filtered_employee:
            process_list = process_list.filtered(
                lambda p: p.default_employee_ids and http.request.env.user.id in p.default_employee_ids.mapped("user_id").ids
            )
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(process_list)
        if page_size:
            total_page = math.ceil(len(process_list) / page_size)  # 总页数
            process_list = process_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for process in process_list:
            result.append(self._get_process_info(process))
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result,"default_process": default_process}
        return return_data

    def _get_process_child_category(self, category, level, allow_category):
        """获取下级工序类别"""
        result = []
        child_level = level + 1
        for child in category.child_ids:
            if allow_category and child not in allow_category:
                continue
            result.append({
                "id": child.id,
                "name": child.name,
                "level": child_level,
                "child_category": self._get_process_child_category(child, child_level, allow_category)
            })
        return result

    def _get_parent_process_category(self, category):
        """
        获取允许的工序类别
        :return:
        """

        if category.parent_id:
            category = category + self._get_parent_process_category(category.parent_id)
        return category

    @http.route('/roke/get_process_category', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_process_category(self):
        """获取工序类别"""
        result = []
        domain = [("parent_id", "=", False)]
        ProcessCategoryObj = http.request.env['roke.process.category']
        # 校验用户的工序类别权限
        default_setting = http.request.env['roke.user.default.setting'].search([("user_id", "=", http.request.uid or http.request.session.uid)])
        allow_category = ProcessCategoryObj
        if default_setting.allow_process_ids:
            # 如果设置了允许的工序、、获取允许的工序的工序类别
            allow_process_categories = default_setting.allow_process_ids.mapped("category_id")
            for allow_process_category in allow_process_categories:
                allow_category = allow_category + self._get_parent_process_category(allow_process_category)
        if allow_category:
            domain.append(("id", "in", allow_category.ids))
        category_ids = ProcessCategoryObj.search(domain)
        for category_id in category_ids:
            level = 1
            result.append({
                "id": category_id.id,
                "name": category_id.name,
                "level": level,
                "child_category": self._get_process_child_category(category_id, level, allow_category)
            })
        # 获取允许的工序类别
        return {"state": "success", "msgs": "获取成功", "result": result}

    # 获取客户信息
    @http.route('/roke/get_customer_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_customer_list(self):
        """
        获取客户信息
        :return:
        """
        customer_condition = http.request.jsonrequest.get('customer', False)  # 工序名称或编号
        domain = []
        if customer_condition:
            domain = ["|", ("name", "ilike", customer_condition), ("code", "ilike", customer_condition)]
        domain.append(("customer", "=", True))
        customer_list = http.request.env['roke.partner'].search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(customer_list)
        if page_size:
            total_page = math.ceil(len(customer_list) / page_size)  # 总页数
            customer_list = customer_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for customer in customer_list:
            result.append({
                "id": customer.id,
                "name": customer.display_name
            })
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    # 获取工作中心信息
    @http.route('/roke/get_work_center_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def get_work_center_list(self):
        """
        获取工作中心信息
        TODO 恢复auth
        :return:
        """
        work_center_condition = http.request.jsonrequest.get('work_center', False)  # 工序名称或编号
        wo_id = http.request.jsonrequest.get('wo_id', False)
        process_id = http.request.jsonrequest.get('process_id', False)
        domain = []
        if work_center_condition:
            domain = ["|", ("name", "ilike", work_center_condition), ("code", "ilike", work_center_condition)]
        if wo_id:
            # wo_id可能是工单ID也可能是工单编号
            wo = http.request.env(user=SUPERUSER_ID)['roke.work.order']
            try:
                wo = wo.browse(int(wo_id))
            except Exception as e:
                pass
            if not wo:
                wo = wo.search([("code", "=", wo_id)])
            wo_work_center_ids = wo.routing_line_id.work_center_ids.work_center_id.ids
            if not wo_work_center_ids:
                wo_work_center_ids = wo.process_id.work_center_ids.work_center_id.ids
            if wo_work_center_ids:
                domain.append(("id", "in", wo_work_center_ids))
        if process_id:
            process = http.request.env(user=SUPERUSER_ID)['roke.process'].browse(int(process_id))
            process_work_center_ids = process.work_center_ids.work_center_id.ids
            if process_work_center_ids:
                domain.append(("id", "in", process_work_center_ids))
        work_center_list = http.request.env(user=SUPERUSER_ID)['roke.work.center'].search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(work_center_list)
        if page_size:
            total_page = math.ceil(len(work_center_list) / page_size)  # 总页数
            work_center_list = work_center_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for work_center in work_center_list:
            result.append({
                "id": work_center.id,
                "name": work_center.display_name
            })
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    # 获取班次信息
    @http.route('/roke/get_classes_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_classes_list(self):
        """
        获取班次信息
        :return:
        """
        classes_list = http.request.env['roke.classes'].search([])
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(classes_list)
        if page_size:
            total_page = math.ceil(len(classes_list) / page_size)  # 总页数
            classes_list = classes_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        result = []
        for classes in classes_list:
            result.append({
                "id": classes.id,
                "name": classes.display_name,
                "start_time": float_to_time(classes.start_time),
                "end_time": float_to_time(classes.end_time)
            })
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    # 获取班组信息
    @http.route('/roke/get_team_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_team_list(self):
        """
        获取班组信息
        :return:
        """
        team_condition = http.request.jsonrequest.get('team', False)  # 工序名称或编号
        domain = []
        if team_condition:
            domain = ["|", ("name", "ilike", team_condition), ("code", "ilike", team_condition)]
        team_list = http.request.env['roke.work.team'].search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(team_list)
        if page_size:
            total_page = math.ceil(len(team_list) / page_size)  # 总页数
            team_list = team_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for team in team_list:
            result.append({
                "id": team.id,
                "name": "%s(%s)" % (team.name, team.code)
            })
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    # 获取人员信息
    @http.route('/roke/get_employee_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_employee_list(self):
        """
        获取人员信息
        :return:
        """
        employee_condition = http.request.jsonrequest.get('employee', False)  # 工序名称或编号
        team_id = http.request.jsonrequest.get('team_id', False)

        domain = []
        if employee_condition:
            # domain = [
            #     "|", "|", "|",
            #     ("name", "ilike", employee_condition),
            #     ("code", "ilike", employee_condition),
            #     ("job_number", "ilike", employee_condition),
            #     ("phone", "ilike", employee_condition)
            # ]
            domain = [
                "|",
                ("name", "ilike", employee_condition),
                ("code", "ilike", employee_condition)
            ]
        employee_list = http.request.env['roke.employee'].search(domain).filtered(lambda e: e.id in http.request.env.user.employee_ids.ids)
        if employee_list and team_id:
            employee_list = employee_list.filtered(lambda e: e.team_id.id == int(team_id))
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(employee_list)
        if page_size:
            total_page = math.ceil(len(employee_list) / page_size)  # 总页数
            employee_list = employee_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            # TODO 请求无分页时暂时返回所有数据，等待前端改动完毕后取消此判断
            total_page = 0
        result = []
        for employee in employee_list:
            result.append({
                "id": employee.id,
                "name": employee.name,
                "code": employee.code,
                "team_id": employee.team_id.id,
                "team_weighted": employee.team_weighted or 1
            })
        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        return return_data

    def _get_scrap_reason(self, domain):
        """
        获取报废原因，实现于质量模块
        :param values:
        :return:
        """
        return []

    # 获取报废原因
    @http.route('/roke/get_scrap_reason_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_scrap_reason_list(self):
        """
        获取报废原因
        :return:
        """
        scrap_reason_condition = http.request.jsonrequest.get('scrap_reason', False)  # 工序名称或编号
        domain = []
        if scrap_reason_condition:
            domain = ["|", ("name", "ilike", scrap_reason_condition), ("code", "ilike", scrap_reason_condition)]
        result = self._get_scrap_reason(domain)

        if not result:
            return_data = {"state": "success", "msgs": "获取成功", "result": result}
        else:
            return_data = {"state": "success", "msgs": "获取成功", "page_no": result[1], "total_page": result[2],
                           "total_number": result[3], "result": result[0]}
        return return_data

    def _get_repair_reason(self, domain):
        """
        获取返修原因，实现于质量模块
        :param values:
        :return:
        """
        return []

    # 获取返修原因
    @http.route('/roke/get_repair_reason_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_repair_reason_list(self):
        """
        获取返修原因
        :return:
        """
        repair_reason_condition = http.request.jsonrequest.get('repair_reason', False)  # 工序名称或编号
        domain = []
        if repair_reason_condition:
            domain = ["|", ("name", "ilike", repair_reason_condition), ("code", "ilike", repair_reason_condition)]
        result = self._get_repair_reason(domain)

        if not result:
            return_data = {"state": "success", "msgs": "获取成功", "result": result}
        else:
            return_data = {"state": "success", "msgs": "获取成功", "page_no": result[1], "total_page": result[2],
                           "total_number": result[3], "result": result[0]}
        return return_data

    # 通过工序获取计薪规则
    @http.route('/roke/process_get_salary_rule', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def process_get_salary_rule(self):
        """
        通过工序获取计薪规则
        :return:
        """
        process_id = http.request.jsonrequest.get('process_id', False)  # 工序ID
        if not process_id:
            return {"state": "error", "msgs": "必须入参工序ID"}
        try:
            process = http.request.env['roke.process'].browse(process_id)
            salary_rule, salary_type = self._process_get_salary_rule(process)
        except:
            salary_rule, salary_type = "无", "无"
        return_data = {"state": "success", "msgs": "获取成功", "salary_rule": salary_rule, "salary_type": salary_type}
        return return_data

    def _get_process_salary_rule(self, process, product):
        """
        通过工序获取计薪规则   实现于工资模块
        :return:
        """
        return "无", "无"

    @http.route('/roke/get_process_salary_rule', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_process_salary_rule(self):
        """
		通过工序获取计薪规则
		:return:
		"""
        process_id = http.request.jsonrequest.get('process_id', False)  # 工序ID
        product_id = http.request.jsonrequest.get('product_id', False)  # 产品
        if not process_id:
            return {"state": "error", "msgs": "必须入参工序ID"}
        try:
            process = http.request.env['roke.process'].browse(process_id)
            product = http.request.env['roke.product'].browse(product_id)
            salary_rule, salary_type = self._get_process_salary_rule(process, product)
        except:
            salary_rule, salary_type = "无", "无"
        return_data = {"state": "success", "msgs": "获取成功", "salary_rule": salary_rule, "salary_type": salary_type}
        return return_data

    def _get_work_record_values(self, wo, values):
        """
        获取创建工单需要的数据
        报工采集：roke.create.wr.collection.wizard--->采集模块处理
        报废明细：roke.create.wr.scrap.detail.wizard--->质量模块处理
        返修明细：roke.create.wr.repair.detail.wizard--->质量模块处理
        :param wo: 工单
        :param values: 入参
        :return:
        """
        work_center_id = values.get('work_center_id', False)  # 工作中心
        classes_id = values.get('classes_id', False)  # 班次
        team_id = values.get('team_id', False)  # 班组
        finish_qty = values.get('finish_qty') or 0  # 合格数量
        note = values.get('note', "")  # 合格数量
        unqualified_qty = values.get('unqualified_qty') or 0  # 不合格数量
        work_hours = values.get('work_hours') or 0  # 工时
        employee_list = values.get('employee_list', [])  # 人员分配：[{'employee':1, 'weighted': 1}]
        report_time = values.get('report_time', False)  # 报工时间
        device_info = values.get('device_info', "")
        product_state_id = values.get('product_state_id', False)  # TODO 产品状态
        if not report_time:
            report_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            report_time = datetime.datetime.strptime(report_time, "%Y-%m-%d %H:%M:%S")
        create_dict = {
            "work_order_id": wo.id,
            "wait_qty": round(wo.plan_qty - wo.finish_qty, _get_pd(http.request.env())),
            "team_id": int(team_id) if team_id else False,
            "work_center_id": int(work_center_id) if work_center_id else False,
            "classes_id": int(classes_id) if classes_id else False,
            "work_hours": float(work_hours),
            "finish_qty": float(finish_qty),
            "unqualified_qty": float(unqualified_qty),
            "work_time": report_time,
            "device_info": device_info,
            "product_state_id": product_state_id,
            "note": note
        }
        if len(employee_list) > 1:
            allot_ids = []
            for employee in employee_list:
                allot_ids.append((0, 0, {
                    "employee_id": int(employee.get("employee_id")),
                    "weighted": float(employee.get("weighted", 1))
                }))
            create_dict["allot_ids"] = allot_ids
            create_dict["multi"] = True
        else:
            create_dict["employee_id"] = int(employee_list[0].get("employee_id"))
        return create_dict

    def _without_order_get_order_value(self, product_id, process_id, finish_qty, collection_list, values):
        """
        无工单报工获取新建工单数据
        collection_item_ids--->采集模块添加
        :param product_id: 产品ID
        :param process_id: 工序ID
        :return:
        """
        process = http.request.env['roke.process'].sudo().browse(int(process_id))
        return {
            "process_id": int(process_id),
            "product_id": int(product_id),
            "plan_qty": finish_qty
        }

    def _check_work_submit(self, work_order, values, check_freedom_work=True):
        """
        校验报工限制：是否   超量报工、是否自由报工
        :param wo: 工单
        :param values: 入参
        :return:
        """
        # 校验报工数
        # 校验不合格数
        # 校验返修报废数
        # 校验返修报废明细
        # company = http.request.env.user.company_id
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        company = http.request.env(user=SUPERUSER_ID)["res.users"].browse(SUPERUSER_ID).company_id
        finish_qty = float(values.get("finish_qty") or 0)
        unqualified_qty = float(values.get("unqualified_qty") or 0)
        work_hours = float(values.get("work_hours") or 0)
        allow_qty, default_qty = work_order._get_wo_allow_qty()
        report_qty = round(finish_qty, _get_pd(http.request.env()))  # 待校验的报工数
        report_type = "合格数"
        if company.complete_basis != "合格数":
            report_type = "合格数+不合格数"
            report_qty = round(report_qty + unqualified_qty, _get_pd(http.request.env()))  # 待校验的报工数
        if check_freedom_work and company.freedom_work != "允许":  # 是否自由报工
            if report_qty > allow_qty:
                # 获取前道工序
                previous = work_order._get_previous()
                if previous:
                    previous_process = previous[0].process_id.display_name
                else:
                    previous_process = ""
                return {
                    "state": "error",
                    "msgs": "请先完成前道工序%s。当前可报数量【%s】，本次报工%s【%s】，禁止报工。（如想取消该限制，请联系系统管理员将“自由报工”选项设置为“允许”）" % (
                    previous_process or "", str(allow_qty), report_type, str(report_qty))
                }
        exceed_plan_qty = float(ConfigParameter.get_param('exceed.plan.qty', default=0))
        if company.exceed_plan != "允许" or (company.exceed_plan == "允许" and exceed_plan_qty):  # 是否超计划报工
            if report_qty > allow_qty:
                return {
                    "state": "error",
                    "msgs": "报工数量不可大于可报数量：%s。（如想取消该限制，请将该提示展示给管理员：设置系统【允许自由报工（生产设置）】并【允许超库存报工（物料设置）】或【允许超计划报工（生产设置）】" % str(
                        allow_qty)
                }
        # 校验额定工时
        if work_order.rated_working_hours:
            exceeding_rated_wh = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
            if exceeding_rated_wh == "disallowed":
                rated_work_hours = (finish_qty + unqualified_qty) * work_order.rated_working_hours
                if work_hours > rated_work_hours:
                    return {
                        "state": "error",
                        "msgs": "报工失败！根据额定工时设置，当前报工数量最大可报工时：%s（如果想取消该限制，请联系管理员调整工艺额定工时或设置允许超额定工时报工）" % str(rated_work_hours)
                    }
        return None

    def _work_submit_input_material(self, wo, material_list):
        """
        报工时投料
        :param wo:
        :param material_list:
        :return:
        """
        return None

    def check_quality(self, values):
        """
        校验质量模块
        :param values:
        :return:
        """
        return

    def check_join_in_data(self, values):
        work_order_id = values.get('work_order_id', False)  # 工单ID
        product_id = values.get('product_id', False)  # 产品
        process_id = values.get('process_id', False)  # 工序
        finish_qty = float(values.get('finish_qty') or 0)  # 合格数量
        unqualified_qty = float(values.get('unqualified_qty') or 0)  # 不合格数量
        work_hours = float(values.get('work_hours') or 0)  # 工时数
        employee_list = values.get('employee_list', [])  # 人员分配：[{'employee_id':1, 'weighted': 1}]
        # 校验入参工单ID、产品、工序、人员列表、不合格数和报废数、返修数
        if not work_order_id and (not product_id or not process_id):
            return {"state": "error", "msgs": "无工单ID时表示无工单报工，必须入参产品ID和工序ID"}
        if not employee_list or type(employee_list) not in (list, tuple):
            return {"state": "error", "msgs": "请选择人员"}
        if float(finish_qty) < 0 or float(unqualified_qty) < 0:
            return {"state": "error", "msgs": "合格数或不合格数不得小于0"}
        # 判断是否能获取到辅计量数据
        aux_list = values.get('aux_list', [])  # 合格数量辅计量数据
        unaux_list = values.get('unaux_list', [])  # 不合格辅计量数据
        if aux_list or unaux_list:
            finish_aux_list = [aux.get('aux_qty', 0) for aux in aux_list if aux.get('aux_qty', 0) > 0]
            unqualified_aux_list = [aux.get('aux_qty', 0) for aux in unaux_list if aux.get('aux_qty', 0) > 0]
            if not float(finish_qty) and not float(unqualified_qty) and not float(
                    work_hours) and not finish_aux_list and not unqualified_aux_list:
                return {"state": "error", "msgs": "合格数或不合格数或工时数不可同时为0"}
        else:
            if not float(finish_qty) and not float(unqualified_qty) and not float(work_hours):
                return {"state": "error", "msgs": "合格数或不合格数或工时数不可同时为0"}
        return False

    # 报工
    @http.route('/roke/work_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_submit(self, **kwargs):
        """
        提交报工记录
        work_order_id:工单ID
        product_id:产品ID
        process_id:工序ID
        work_center_id:工作中心ID
        team_id:班组ID
        finish_qty:合格数
        unqualified_qty:不合格数
        scrap_qty:报废数
        repair_qty:返修数
        work_hours:工时数
        employee_list:人员列表---->[{"employee_id": 员工ID, "weighted": 分配权重}]
        collection_list:采集结果---->[{"item_id": 采集项ID, "result": 采集结果}]
        scrap_list:报废明细---->[{"reason_id": 报废原因ID, "qty": 报废数量}]
        repair_list:返修明细---->[{"reason_id": 返修原因ID, "qty": 返修数量}]
        :return:
        """
        if kwargs:
            values = kwargs
        else:
            values = http.request.jsonrequest
        # 一秒内禁止同一用户重复提交
        uid = http.request.uid or http.request.session.uid
        time_stamp = int(time.time() * 1000)
        last_time = submit_timestamp.get(uid)
        if last_time and time_stamp - last_time < 1000:
            submit_timestamp[uid] = time_stamp
            return
        submit_timestamp[uid] = time_stamp
        work_order_id = values.get('work_order_id', False)  # 工单ID
        product_id = values.get('product_id', False)  # 产品
        process_id = values.get('process_id', False)  # 工序
        finish_qty = float(values.get('finish_qty') or 0)  # 合格数量
        unqualified_qty = float(values.get('unqualified_qty') or 0)  # 不合格数量
        work_hours = float(values.get('work_hours') or 0)  # 工时数
        employee_list = values.get('employee_list', [])  # 人员分配：[{'employee_id':1, 'weighted': 1}]
        collection_list = values.get('collection_list', [])  # 采集列表
        customer_id = values.get('customer_id', False)  # 客户ID
        production_order_id = values.get('production_order_id', False)  # 生产订单ID
        confirm = values.get('confirm', True)  # 生产订单ID

        check_quality = self.check_quality(values)
        if check_quality:
            return check_quality

        check_data = self.check_join_in_data(values)
        if check_data:
            return check_data

        # 获取工单
        WorkOrderObj = http.request.env['roke.work.order']
        if work_order_id:
            work_order = WorkOrderObj.browse(work_order_id)
            check_result = self._check_work_submit(work_order, values)
            if check_result:
                return check_result
        else:
            work_order = WorkOrderObj.create(
                self._without_order_get_order_value(product_id, process_id, finish_qty, collection_list, values)
            )
            # 无工单报工后修改客户等内容，因为是related字段，直接创建写不进值
            wo_write_dict = {}
            if customer_id:
                wo_write_dict["customer_id"] = int(customer_id)
            if production_order_id:
                production_order = http.request.env['roke.production.order'].browse(int(production_order_id))
                wo_write_dict["project_code"] = production_order.project_code or production_order.display_name or ""
            if wo_write_dict:
                work_order.write(wo_write_dict)
        # 3秒内禁止相同产品报工，防止报工时库存账本表锁住
        product_last_time = product_timestamp.get(work_order.product_id.id)
        if product_last_time and time_stamp - product_last_time < 3000:
            product_timestamp[work_order.product_id.id] = time_stamp
            sleep(3)
        product_timestamp[work_order.product_id.id] = time_stamp
        # 投料
        material_list = values.get('material_list', [])
        input_result = self._work_submit_input_material(work_order, material_list)
        if input_result:
            return input_result  # 禁止投料产品本身校验
        work_record = http.request.env['roke.work.record']
        try:
            # 创建工单报工记录并确认
            wizard = http.request.env['roke.create.work.record.wizard'].sudo().create(
                self._get_work_record_values(work_order, values)
            )
            wizard.confirm(not confirm)  # 确认执行报工,创建报工记录
            work_record = wizard.wr_id
            # 报工图片记录到单据备注栏
            attachment_ids = []
            Attachment = http.request.env['ir.attachment']
            i = 1
            for image in values.get("image_datas", []):
                attachment = Attachment.create({
                    'name': "报工：%s-%s.png" % (work_record.display_name, str(i)),
                    'datas': image,
                    'res_model': "roke.work.record",
                    'res_id': work_record.id
                })
                attachment._post_add_create()
                attachment_ids.append(attachment.id)
                i += 1
            if attachment_ids:
                work_record.message_post(body="", message_type="comment", subtype_xmlid="mail.mt_note", attachment_ids=attachment_ids)
            return_data = {
                "state": "success",
                "msgs": "报工成功",
                "print_lot_code_ids": work_record.get_print_lot_code_ids(),
                "work_order": work_order.code,
                "work_record_id": work_record.id,
                "product": work_order.product_id.display_name,
                "finish_qty": work_record.finish_qty,
                "work_time": (work_record.work_time + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M')
            }
            http.request.env.cr.commit()
        except Exception as e:
            _logger.error("！！！！！报工接口报错！！！！！")
            _logger.error(e)
            if work_record:
                _logger.error(work_record)
                work_record.unlink()
            return {"state": "error", "msgs": e}
        return return_data

    # 撤销报工
    @http.route('/roke/work_withdraw', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_withdraw(self):
        """
        撤销报工
        :return:
        """
        work_record_id = http.request.jsonrequest.get('work_record_id', False)  # 报工记录ID
        if not work_record_id:
            return {"state": "error", "msgs": "撤销报工必须入参报工记录ID"}
        work_record = http.request.env['roke.work.record'].browse(work_record_id)
        try:
            # 校验是否允许撤回报工
            work_record._withdraw_check_after_wo(work_record)
            work_record.withdraw()
        except Exception as e:
            return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "撤销报工成功"}

    def _get_multi_submit_line_value(self, record):
        """
        获取创建批量报工明细值
        :param record:
        :return:
        """
        allot_ids = []
        for employee in record.get("employee_list", []):
            allot_ids.append((0, 0, {
                "employee_id": int(employee.get("employee_id")),
                "weighted": float(employee.get("weighted", 1))
            }))
        if record.get("wo_id", False):
            wo = http.request.env["roke.work.order"].browse(int(record.get("wo_id")))
            product_id = wo.product_id.id
            process_id = wo.process_id.id
        else:
            product_id = record.get("product_id", False)
            process_id = record.get("process_id", False)
        work_time = record.get("work_time", False)
        if not work_time:
            work_time = fields.Datetime.now()
        else:
            work_time = datetime.datetime.strptime(work_time, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=8)
        return {
            "wo_id": record.get("wo_id", False),
            "product_id": product_id,
            "process_id": process_id,
            "team_id": record.get("team_id", False),
            "classes_id": record.get("classes_id", False),
            "product_state_id": record.get("product_state_id", False),
            "employee_id": record.get("employee_id", False),
            "allot_ids": allot_ids,
            "work_center_id": record.get("work_center_id", False),
            "finish_qty": record.get("finish_qty", 0),
            "unqualified_qty": record.get("unqualified_qty", 0),
            "work_hours": record.get("work_hours", 0),
            "work_time": work_time
        }

    # 批量报工
    @http.route('/roke/work_multi_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def work_multi_submit(self):
        multi_record_list = http.request.jsonrequest.get('multi_record_list', False)  # 批量报工列表
        create_values = []
        for record in multi_record_list:
            create_values.append((0, 0, self._get_multi_submit_line_value(record)))
        record = http.request.env['roke.multi.submit.work.record'].create({
            "line_ids": create_values
        })
        if http.request.jsonrequest.get('confirm', False):
            record.confirm()
        return {"state": "success", "msgs": "报工成功"}

    # 投料
    @http.route('/roke/input_material', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def input_material(self):

        return {"state": "error", "msgs": "未安装投料模块"}

    # 获取系统库存及物料
    @http.route('/roke/get_input_material_info', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_input_material_info(self):

        return {"state": "error", "msgs": "未安装投料模块"}

    # 撤回投料
    @http.route('/roke/withdraw_material', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def withdraw_material(self):

        return {"state": "error", "msgs": "未安装投料模块"}

    # 报工获取默认值
    @http.route('/roke/get_user_default_setting', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_user_default_setting(self):
        uid = http.request.uid or http.request.session.uid
        default_setting = http.request.env['roke.user.default.setting'].search([("user_id", "=", uid)])
        employee_list = []
        if default_setting.default_employee_ids:
            for employee in default_setting.default_employee_ids:
                employee_list.append({
                    "id": employee.id,
                    "name": "%s(%s)" % (employee.name, employee.code),
                    "team_id": employee.team_id.id or ''
                })
        elif default_setting.user_id.employee_ids:
            employee = default_setting.user_id.employee_ids[0]
            employee_list.append({
                "id": employee.id,
                "name": "%s(%s)" % (employee.name, employee.code),
                "team_id": employee.team_id.id or ''
            })
        process_dict = {}
        if default_setting.default_process_id:
            process_dict["id"] = default_setting.default_process_id.id
            process_dict["name"] = "%s(%s)" % (default_setting.default_process_id.name, default_setting.default_process_id.code)
        team_dict = {}
        if default_setting.default_team_id:
            team_dict["id"] = default_setting.default_team_id.id
            team_dict["name"] = "%s(%s)" % (default_setting.default_team_id.name, default_setting.default_team_id.code)
        work_center_dict = {}
        if default_setting.default_work_center_id:
            work_center_dict["id"] = default_setting.default_work_center_id.id
            work_center_dict["name"] = "%s(%s)" % (default_setting.default_work_center_id.name, default_setting.default_work_center_id.code)
        report_show_salary = False
        if http.request.env['ir.config_parameter'].sudo().get_param('report.show.salary', default="不显示") == "显示":
            report_show_salary = True
        return_data = {
            "state": "success",
            "msgs": "获取成功",
            "report_show_salary": report_show_salary,
            "process_id": process_dict,
            "employee_ids": employee_list,
            "team_id": team_dict,
            "work_center_id": work_center_dict,
            "allow_edit_weighted": default_setting.allow_edit_weighted
        }
        return return_data

    # 报工设置默认值
    @http.route('/roke/set_user_default_setting', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def set_user_default_setting(self):
        values = http.request.jsonrequest
        process_id = values.get("process_id") or False
        employee_ids = values.get("employee_ids") or []
        team_id = values.get("team_id") or False
        work_center_id = values.get("work_center_id") or False
        write_dict = {}
        write_dict["default_process_id"] = process_id
        write_dict["default_employee_ids"] = [(6, 0, employee_ids)]
        write_dict["default_team_id"] = team_id
        write_dict["default_work_center_id"] = work_center_id
        uid = http.request.uid or http.request.session.uid
        default_setting = http.request.env['roke.user.default.setting'].search([("user_id", "=", uid)])
        default_setting.write(write_dict)
        return {"state": "success", "msgs": "设置成功"}

    # 获取订单报工列表
    @http.route('/roke/get_order_work_submit_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_order_work_submit_list(self):
        values = http.request.jsonrequest
        order_code = values.get("order_code") or False
        # 查询销售订单
        try:
            sale_order = http.request.env['roke.sale.order'].search([("code", "=", order_code)], limit=1)
        except Exception as e:
            sale_order = None
        # 查询生产订单
        if not sale_order:
            production_order = http.request.env['roke.production.order'].search([("code", "=", order_code)], limit=1)
            pols = production_order.line_ids
        else:
            pols = http.request.env["roke.production.order.line"].search([
                ("sale_order_line", "in", sale_order.line_ids.ids)
            ])
        if not pols:
            return {"state": "error", "msgs": "订单获取失败，请确认该订单已开始生产。"}
        # 获取工艺路线
        routing_set = set(pols.mapped("routing_id"))
        if len(routing_set) > 1:
            return {"state": "error", "msgs": "当前订单下产品存在多条工艺路线，不可使用该功能。"}
        try:
            routing = list(routing_set)[0]
        except:
            return {"state": "error", "msgs": "当前订单下产品没有工艺路线，不可使用该功能。"}
        # 获取客户信息
        customer = pols[0].order_id.customer_id.display_name or ""
        # 获取允许的工序
        allow_process = http.request.env['roke.user.default.setting'].search([("user_id", "=", http.request.uid or http.request.session.uid)]).allow_process_ids
        # 获取表头数据
        title_settings = http.request.env(user=SUPERUSER_ID)['roke.order.submit.setting'].search([])
        # title = list(title_settings.mapped("title")) + ["产品名称", "规格型号", "计划产量"]
        title = list(title_settings.mapped("title"))
        for routing_line in routing.line_ids:
            if allow_process and routing_line.process_id not in allow_process:
                # 校验允许的工序
                continue
            # 处理子工序
            if routing_line.child_process_ids:
                for child_process in routing_line.child_process_ids:
                    title.append(child_process.display_name)
            else:
                title.append(routing_line.display_name)

        # 获取表体数据
        detail = []
        fixed_detail = []
        for pol in pols:
            pol_list = []
            pol1_list = [pol.product_id.display_name, pol.product_id.specification or "", pol.qty]
            custom_vals = []
            for title_setting in title_settings:
                field = title_setting.field_id
                field_value = ""
                # 获取字段值
                if title_setting.model_id.model == "roke.product":
                    field_value = getattr(pol.product_id, field.name)
                elif title_setting.model_id.model == "roke.sale.order.line":
                    field_value = getattr(pol.sale_order_line, field.name)
                elif title_setting.model_id.model == "roke.sale.order":
                    field_value = getattr(pol.sale_order_line.order_id, field.name)
                elif title_setting.model_id.model == "roke.production.order":
                    field_value = getattr(pol.order_id, field.name)
                elif title_setting.model_id.model == "roke.production.order.line":
                    field_value = getattr(pol, field.name)
                # 格式化字段值
                if field.ttype == "many2one":
                    value = field_value.display_name if field_value else ""
                elif field.ttype in ["one2many", "many2many"]:
                    value = []
                    for v in field_value:
                        value.append(v.display_name)
                else:
                    value = field_value or ""
                custom_vals.append(value)
            pol_list += custom_vals
            # pol_list += pol1_list
            title_len = len(pol_list)
            for wo in pol.task_id.work_order_ids.filtered(lambda wo: not wo.child_wo_ids) or []:
                if allow_process and wo.process_id not in allow_process:
                    # 校验允许的工序
                    continue
                collection_list = self.pt_get_wo_collection(wo)
                allow_qty, default_qty = wo._get_wo_allow_qty()
                pol_list.append({"wo_id": wo.id, "finish_qty": wo.finish_qty, "allow_qty": allow_qty,"collection_list": collection_list})
            if len(pol_list) > title_len:  # 存在允许的工单时才返回
                detail.append(pol_list)
                fixed_detail.append(pol1_list)
        # 记录调用日志
        return {
            "state": "success",
            "msgs": "获取成功",
            "result": {"order_code": order_code, "customer": customer, "title": title, "detail": detail,
                       'fixed_detail': fixed_detail}
        }

    # 判断是否允许报工
    def process_order_allow_work(self, production_order):
        pols = production_order.line_ids
        routing_set = set(pols.mapped("routing_id"))
        if pols and len(routing_set) == 1:
            return True
        else:
            return False

    # 获取订单详情
    @http.route('/roke/get_order_information', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_order_information(self):
        values = http.request.jsonrequest
        order_code = values.get("order_code", "")
        customer_id = values.get("customer_id", "")

        result = []
        domain = []
        if order_code:
            domain.append(("code", "=", order_code))
        if customer_id:
            customer = http.request.env['roke.partner'].browse(int(customer_id))
            domain.append(("customer_id", "=", customer.id))
        production_orders = http.request.env['roke.production.order'].search(domain)
        if production_orders:
            for production_order in production_orders:
                product = ''
                for line in production_order.line_ids:
                    product += line.product_id.name + '|'
                result.append({
                    "order_code": production_order.code or '',
                    "customer_id": production_order.customer_id.name or '',
                    "product_list": product or '',
                    "order_date": (production_order.create_date + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
                    "deadline": production_order.plan_date or '',
                    "is_allow_work": self.process_order_allow_work(production_order)
                })
        return {
            "state": "success",
            "msgs": "获取成功",
            "result": result
        }

    def _get_no_order_get_process_info(self, process_id, product_id=None, file_type=None):
        process = http.request.env['roke.process'].browse(process_id)
        product = http.request.env['roke.product']
        if product_id:
            product = product.browse(product_id)
        try:
            salary_rule, salary_type = self._process_get_salary_rule(process)
        except:
            salary_rule, salary_type = "无", "无"
        by_products = []
        try:
            for by_product in process.by_product_ids.filtered(lambda p: not p.auto):
                by_products.append({
                    "product_id": by_product.product_id.id,
                    "product_name": by_product.product_id.display_name,
                })
        except Exception as e:
            _logger.error(e)

        collection_list = []
        try:
            for item in process.collection_item_ids:
                collection_list.append({
                    "id": item.id,
                    "name": item.name,
                    "data_type": item.data_type,
                    "dict_model_index": item.dict_model_index or "",
                    "required": item.required,
                    "single_items": item.select_item_ids.mapped("value") if item.data_type == "单选" else [],
                    "multiple_items": item.select_item_ids.mapped("value") if item.data_type == "多选" else []
                })
        except Exception:
            pass
        # 校验产出物是否需要绑定批次
        operation_after_completion = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('operation.after.completion', default="无操作")
        scan_lot = "none"
        if operation_after_completion == "扫码绑定批次":
            if process.without_wo_produce:
                scan_lot = "single" if product.track_type == "lot" else "multi"
        print_code = False
        if operation_after_completion == "打印批次条码":
            if process.without_wo_produce:
                print_code = True
        # 作业规范
        standard_list = []
        for item in product.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自产品数据】"
            })
        for item in process.category_id.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "note": "【来自工序类别】"
            })

        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        exceeding_rated_wh_val = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
        exceeding_rated_wh = http_tool.selection_to_dict("res.config.settings", "exceeding_rated_wh")[exceeding_rated_wh_val]
        autofill_rated_wh = True if ConfigParameter.get_param('autofill.rated.working.hours') == "1" else False
        return {
            "salary_rule": salary_rule,
            "salary_type": salary_type,
            "allow_by_product": True if by_products else False,
            "by_products": by_products,
            "quantity": False,
            "allow_input_material": False,
            "scan_lot": scan_lot,
            "print_code": print_code,
            "file_url": process.get_instruction_file_url(file_type=file_type) or [],
            "collection_list": collection_list,
            "standard_list": standard_list,
            "rated_working_hours": process.rated_working_hours,
            "exceeding_rated_wh": exceeding_rated_wh,
            "autofill_rated_wh": autofill_rated_wh
        }

    @http.route('/roke/no_order_get_process_info', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def no_ordno_orderer_get_process_info(self):
        values = http.request.jsonrequest
        process_id = values.get("process_id")
        product_id = values.get("product_id", False)
        file_type = values.get('file_type', False)
        if not process_id:
            return {"state": "error", "msgs": "必须入参工序ID"}
        return {
            "state": "success",
            "msgs": "获取成功",
            "result": self._get_no_order_get_process_info(process_id, product_id=product_id, file_type=file_type)
        }

    @http.route('/roke/production_track', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def production_track(self):
        """
        生产追溯
            半成品、成品批次码或件码时，能够查询该批次/件次产品对应的历史记录
        :return:
        """
        return {"state": "error", "msgs": "尚未开通此功能，如需使用请联系服务商添加追溯功能。"}

    @http.route('/roke/production_external_track', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def production_external_track(self):
        """
        外部追溯
            半成品、成品批次码或件码时，能够查询该批次/件次产品对应的历史记录
        :return:
        """
        return {"state": "error", "msgs": "尚未开通此功能，如需使用请联系服务商添加追溯功能。"}

    @staticmethod
    def _get_track_collection_list(collections):
        """
        追溯获取数采信息
            数采模块
        :return:
        """
        return []

    def _code_get_production_task(self, pt_code, limit=0, state=None):
        """
        根据任务编号获取生产任务
        :param pt_code:
        :return:
        """
        domain = []
        if pt_code:
            domain.append(('code', 'ilike', pt_code))
        if state:
            domain.append(('state', 'ilike', state))
        if not limit:
            return http.request.env["roke.production.task"].search(domain)
        return http.request.env["roke.production.task"].search(domain, limit=limit)

    def _code_get_production_task_auth_none(self, pt_code, limit=0, state=None):
        """
        根据任务编号获取生产任务
        :param pt_code:
        :return:
        """
        domain = []
        if pt_code:
            domain.append(('code', 'ilike', pt_code))
        if state:
            domain.append(('state', 'ilike', state))
        if not limit:
            return http.request.env(user=SUPERUSER_ID)["roke.production.task"].search(domain)
        return http.request.env(user=SUPERUSER_ID)["roke.production.task"].search(domain, limit=limit)

    @http.route('/roke/fast_work_order_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def fast_work_order_submit(self):
        """
        快速报工
        :return:
        """
        values = http.request.jsonrequest
        pt_code = values.get("pt_code", False)  # 任务编号
        process_id = values.get("process_id", False)  # 工序id
        employee_list = values.get('employee_list', False)  # 报工人员id

        if pt_code and process_id and employee_list:
            try:
                process_id = int(process_id)
            except:
                return {'state': 'error', 'msgs': 'process_id不是数字类型'}
            if "http" in pt_code:
                # 支持带追溯地址的追溯条码
                # pt_code = http://xxxx.com/t?c=123&a=dasfCUNFLssfe
                try:
                    pt_code = pt_code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    pt_code = ""
            task = self._code_get_production_task(pt_code, limit=1)
            if not task:
                return {'state': 'error', 'msgs': '未找到此任务'}
            work_order = http.request.env["roke.work.order"].search([('task_id', '=', task.id), ('process_id', '=', process_id)])
            if not work_order:
                return {'state': 'error', 'msgs': '未在此任务中找到当前工序！'}
            if work_order.state != "未完工":
                return {'state': 'error', 'msgs': '当前工单状态为%s，禁止报工，请确认是否重复扫码。' % work_order.state}
            values.update({
                "finish_qty": work_order.plan_qty
            })

            check_result = self._check_work_submit(work_order, values)
            if check_result:
                return check_result

            wizard = http.request.env['roke.create.work.record.wizard'].create(
                self._get_work_record_values(work_order, values)
            )
            wizard.confirm()
            return {'state': 'success', 'msgs': '报工完成',
                    'work_order': work_order.code,
                    'task_code': work_order.task_id.code or "",
                    'product': work_order.product_id.display_name,
                    'process': work_order.process_id.display_name,
                    'finish_qty': wizard.finish_qty,
                    'work_time': str(wizard.work_time)[2:10].replace('-', '/')}
        elif not pt_code:
            return {'state': 'error', 'msgs': '必须入参任务编号'}
        elif not process_id:
            return {'state': 'error', 'msgs': '必须入参工序id'}
        elif not employee_list:
            return {'state': 'error', 'msgs': '必须入参报工人员'}

    @http.route('/roke/fast_employee_wo_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def fast_employee_wo_submit(self):
        """
        快速报工
        :return:
        """
        values = http.request.jsonrequest
        wo_code = values.get("wo_code", False)  # 任务编号
        employee_list = values.get('employee_list', False)  # 报工人员id
        if not wo_code or not employee_list:
            return {'state': 'error', 'msgs': '必须输入工单编号和人员'}
        work_order = http.request.env["roke.work.order"].search([('code', '=', wo_code)])
        if not work_order:
            return {'state': 'error', 'msgs': '未找到对应工单！'}
        allow_qty, default_qty = work_order._get_wo_allow_qty()
        submit_qty = default_qty
        if not submit_qty:
            submit_qty = allow_qty
        if not submit_qty:
            return {'state': 'error', 'msgs': '当前工单无可报数量，已忽略本次报工！'}
        values.update({
            "finish_qty": submit_qty
        })
        check_result = self._check_work_submit(work_order, values)
        if check_result:
            return check_result
        wizard = http.request.env['roke.create.work.record.wizard'].create(
            self._get_work_record_values(work_order, values)
        )
        wizard.confirm()
        return {'state': 'success', 'msgs': '报工完成',
                'work_order': work_order.code,
                'finish_qty': wizard.finish_qty,
                'work_time': str(wizard.work_time)[2:10].replace('-', '/')}

    @http.route('/roke/report_get_wo_allow_qty', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def report_get_wo_allow_qty(self):
        """
        报表获取工单可报数量
        :return:
        """
        values = http.request.jsonrequest
        wo_id = values.get("wo_id", False)
        if not wo_id or type(wo_id) != int:
            return 0
        else:
            wo = http.request.env(user=SUPERUSER_ID)['roke.work.order'].browse(wo_id)
            allow_qty, allow_qty = wo._get_wo_allow_qty()
            return allow_qty

    @http.route('/roke/get_default_work_team', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_default_work_team(self):
        """
        获取默认班组。用于班组报工
        :return:
        """
        team_id = http.request.env['roke.team.submit.wr']._get_default_team()
        default_team = {}
        if team_id:
            team = http.request.env['roke.work.team'].browse(team_id)
            default_team = {"team_id": team.id, "team_name": team.display_name}
        return {'state': 'success', 'msgs': '获取成功', 'default_team': default_team}

    def get_team_setting_vals(self, team_setting):
        """
        获取设置值
        :return:
        """
        field = team_setting.field_id
        return {
            # "model": self.model_id.name,
            "index": field.name,
            "title": team_setting.title,
            "type": field.ttype,
            "relation": field.relation or "",
            "select_list": list(field.selection_ids.mapped("name")),
            "required": field.required
        }

    @http.route('/roke/get_team_work_setting', type='json', methods=['GET', 'POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_team_work_setting(self):
        settings = http.request.env['roke.team.submit.setting'].search([])
        result = []
        team_setting = settings.filtered(lambda s: s.model_id.model == 'roke.team.submit.wr')
        employee_setting = settings.filtered(lambda s: s.model_id.model == 'roke.team.submit.wr.employee')
        team_line_setting = settings.filtered(lambda s: s.model_id.model == 'roke.team.submit.wr.line')
        if team_setting:
            team_setting_vals = []
            for team in team_setting:
                team_setting_vals.append(self.get_team_setting_vals(team))
            result.append({
                "result": "班组报工",
                "model_data": team_setting_vals
            })
        if employee_setting:
            employee_setting_vals = []
            for employee in employee_setting:
                employee_setting_vals.append(self.get_team_setting_vals(employee))
            result.append({
                "result": "班组报工人员",
                "model_data": employee_setting_vals
            })
        if team_line_setting:
            line_setting_vals = []
            for line in team_line_setting:
                line_setting_vals.append(self.get_team_setting_vals(line))
            result.append({
                "result": "班组报工明细",
                "model_data": line_setting_vals
            })
        return {'state': 'success', 'msgs': "获取成功", "result": result}

    @http.route('/roke/get_team_work_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_team_work_list(self):
        """
        获取班组报工列表
        :return:
        """
        values = http.request.jsonrequest
        domain = []
        code = values.get("code")
        team_id = values.get("team_id")
        classes_id = values.get("classes_id")
        if code:
            domain.append(("code", "ilike", values.get("code")))
        if team_id:
            domain.append(("team_id", "=", int(team_id)))
        if classes_id:
            domain.append(("classes_id", "=", int(classes_id)))
        team_submit_records = http.request.env['roke.team.submit.wr'].search(domain)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(team_submit_records)
        if page_size:
            total_page = math.ceil(len(team_submit_records) / page_size)  # 总页数
            team_submit_records = team_submit_records[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        result = []
        for team_submit_record in team_submit_records:
            result.append({
                "id": team_submit_record.id,
                "code": team_submit_record.code,
                "state": team_submit_record.state,
                "team": team_submit_record.team_id.display_name,
                "classes": team_submit_record.classes_id.display_name or "",
                "submit_date": (team_submit_record.submit_date + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
                "create_date": (team_submit_record.create_date + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M')
            })
        return {'state': 'success', 'msgs': '获取成功', "page_no": page_no, "total_page": total_page,
                "total_number": total_number, 'result': result}

    def _get_team_setting_value(self, model_name, record):
        """
        获取班组报工详情的值
        :param model_name:
        :return:
        """
        res = []
        settings = http.request.env['roke.team.submit.setting'].search([("model_id.model", "=", model_name)])
        for setting in settings:
            field = setting.field_id
            value = ""
            if field.ttype == "many2one":
                related_record = getattr(record, field.name)
                if related_record:
                    value = related_record.display_name
            elif field.ttype == "selection":
                select_value = getattr(record, field.name)
                if select_value:
                    value = http_tool.selection_to_dict(model_name, field.name)[select_value]
            elif field.ttype == "datetime":
                time_value = getattr(record, field.name)
                if time_value:
                    value = (time_value + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M')
            else:
                value = getattr(record, field.name)
            if field.ttype not in ("boolean", "many2one"):
                value = value or ""
            res.append({
                "name": setting.title,
                "index": field.name,
                "type": field.ttype,
                "result": value
            })
        return res

    def _get_team_work_employee_wr_detail(self, team_wr):
        """
        获取班组报工保工明细详情
        :return:
        """
        res = {
            "wr_id": team_wr.wr_id.id,
            "work_order": team_wr.wo_id.display_name or "",
            "work_order_id": team_wr.wo_id.id,
            "without_order": True if not team_wr.wr_id.pt_id and not team_wr.wr_id.result_ids else False,  # 是否无工单报工，是否允许编辑报工
            "product": team_wr.product_id.display_name,
            "product_id": team_wr.product_id.id,
            "process": team_wr.process_id.display_name,
            "process_id": team_wr.process_id.id,
            "work_center": team_wr.work_center_id.display_name,
            "work_center_id": team_wr.work_center_id.id,
            "finish_qty": team_wr.finish_qty,
            "unqualified_qty": team_wr.unqualified_qty,
            "work_hours": team_wr.work_hours,
            "note": team_wr.note or "",
            "collection_list": [],
            "set_data": self._get_team_setting_value("roke.team.submit.wr.line", team_wr)
        }
        return res

    def _get_team_work_employee_detail(self, team_wr_employee):
        """
        获取班组报工人员详情
        :return:
        """
        work_record_list = []
        for team_wr in team_wr_employee.wr_line_ids:
            work_record_list.append(self._get_team_work_employee_wr_detail(team_wr))
        res = {
            "employee": team_wr_employee.employee_id.display_name,
            "employee_id": team_wr_employee.employee_id.id,
            "sum_work_hours": team_wr_employee.sum_work_hours,
            "work_record_list": work_record_list,
            "set_data": self._get_team_setting_value("roke.team.submit.wr.employee", team_wr_employee)
        }
        return res

    @http.route('/roke/get_team_work_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_team_work_detail(self):
        """
        获取班组报工详情
        :return:
        """
        values = http.request.jsonrequest
        team_work_id = values.get("team_work_id")
        if not team_work_id:
            return {'state': 'error', 'msgs': '必须入参班组报工ID'}
        team_submit_record = http.request.env['roke.team.submit.wr'].browse(int(team_work_id))
        # 人员列表
        employee_list = []
        for team_wr_employee in team_submit_record.employee_line_ids:
            employee_list.append(self._get_team_work_employee_detail(team_wr_employee))
        result = {
            "id": team_submit_record.id,
            "code": team_submit_record.code,
            "state": team_submit_record.state,
            "team": team_submit_record.team_id.display_name,
            "team_id": team_submit_record.team_id.id,
            "classes": team_submit_record.classes_id.display_name or "",
            "classes_id": team_submit_record.classes_id.id or "",
            "note": team_submit_record.note or "",
            "submit_date": (team_submit_record.submit_date + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
            "create_date": (team_submit_record.create_date + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
            "employee_list": employee_list,
            "set_data": self._get_team_setting_value("roke.team.submit.wr", team_submit_record)
        }
        return {'state': 'success', 'msgs': '获取成功', 'result': result}

    def _get_team_work_submit_setting_value(self, model_name, val):
        """
        获取班组报工详情的值
        :param model_name:
        :return:
        """
        res = {}
        for field_name, value in val.items():
            setting = http.request.env['roke.team.submit.setting'].search([("model_id.model", "=", model_name), ("field_id.name", "=", field_name)])
            if setting.field_id.ttype == "selection":
                selection_vals = http_tool.selection_to_dict(model_name, field_name)
                for k, v in selection_vals.items():
                    if v == value:
                        value = k
                        break
            elif setting.field_id.ttype == "datetime":
                value = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M") - datetime.timedelta(hours=8)
            res[field_name] = value
        return res

    def _get_team_work_submit_wr_vals(self, team_wr_val, confirm=True):
        """
        获取班组报工记录内容
        :return:
        """
        work_order_id = team_wr_val.get("work_order_id") or False
        product_id = team_wr_val.get("product_id") or False
        process_id = team_wr_val.get("process_id") or False
        finish_qty = team_wr_val.get("finish_qty") or 0
        unqualified_qty = team_wr_val.get("unqualified_qty") or 0
        work_hours = team_wr_val.get("work_hours") or 0
        work_center_id = team_wr_val.get("work_center_id") or False
        note = team_wr_val.get("note", "")
        if work_order_id:
            work_order = http.request.env["roke.work.order"].browse(int(work_order_id))
            if work_order:
                # 检查提交数量
                if confirm:
                    check_result = self._check_work_submit(work_order, team_wr_val, check_freedom_work=False)
                    if check_result:
                        return check_result
                product_id = work_order.product_id.id
                process_id = work_order.process_id.id
        res = {
            "wo_id": work_order_id,
            "product_id": product_id,
            "process_id": process_id,
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "work_hours": work_hours,
            "work_center_id": work_center_id,
            "note": note,
        }
        res.update(self._get_team_work_submit_setting_value("roke.team.submit.wr.line", team_wr_val.get("set_data") or {}))
        return res

    def _tw_get_el_vals(self, employee_val, team_wr_vals):
        """
        班组报工获取人员记录的值
        :return:
        """
        employee_id = employee_val.get("employee_id")
        sum_work_hours = employee_val.get("sum_work_hours", 0)
        res = {
            "employee_id": int(employee_id),
            "sum_work_hours": sum_work_hours,
            "wr_line_ids": team_wr_vals
        }
        res.update(self._get_team_work_submit_setting_value("roke.team.submit.wr.employee", employee_val.get("set_data") or {}))
        return res

    @http.route('/roke/team_work_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def team_work_submit(self):
        """
        班组报工提交
        :return:
        """
        values = http.request.jsonrequest
        # 日期、班组、班次、备注、人员列表、报工记录、采集记录
        work_date = values.get("work_date")
        center_judge = values.get("center_judge", False)  # 是否是数字工位的报工
        team_id = values.get("team_id")
        classes_id = values.get("classes_id", False)
        note = values.get("note", "")
        employee_list = values.get("employee_list", [])
        if values.__contains__("confirm"):
            confirm = values.get('confirm') or False
        else:
            confirm = True  # 临时处理，如果没有入参此参数，那么班组报工的这个接口还按以前的逻辑：提交时直接报工
        id = values.get('id')
        team_submit = http.request.env["roke.team.submit.wr"]
        if id:
            team_submit = team_submit.browse(id)
            if team_submit.state == "已报工":
                return {'state': 'error', 'msgs': '当前单据已报工禁止编辑', 'new_id': team_submit.id}
        if not team_id:
            return {'state': 'error', 'msgs': '必须入参班组ID。'}
        if not employee_list:
            return {'state': 'error', 'msgs': '本次提交没有提交报工的人员。'}
        # 遍历人员列表
        employee_vals = []
        for employee_val in employee_list:
            employee_id = employee_val.get("employee_id")
            if not employee_id:
                return {'state': 'error', 'msgs': '人员列表必须入参人员ID。'}
            work_record_list = employee_val.get("work_record_list", [])
            # 遍历报工内容
            team_wr_vals = []
            for team_wr_val in work_record_list:
                team_wr_val = self._get_team_work_submit_wr_vals(team_wr_val, False)
                if 'state' in team_wr_val:
                    return team_wr_val
                team_wr_vals.append((0, 0, team_wr_val))
            employee_vals.append((0, 0, self._tw_get_el_vals(employee_val, team_wr_vals)))
        if team_submit:
            for employee_line in team_submit.employee_line_ids:
                employee_vals.append((2, employee_line.id))
            write_vals = {
                "team_id": int(team_id),
                "classes_id": int(classes_id) if classes_id else False,
                "submit_date": datetime.datetime.strptime(work_date, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=8),
                "note": note,
                "employee_line_ids": employee_vals
            }
            write_vals.update(self._get_team_work_submit_setting_value("roke.team.submit.wr", values.get("set_data") or {}))
            team_submit.write(write_vals)
        else:
            # 创建班组报工记录
            create_vals = {
                "team_id": int(team_id),
                "classes_id": int(classes_id) if classes_id else False,
                "submit_date": datetime.datetime.strptime(work_date, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=8),
                "note": note,
                "employee_line_ids": employee_vals
            }
            create_vals.update(self._get_team_work_submit_setting_value("roke.team.submit.wr", values.get("set_data") or {}))
            team_submit = http.request.env["roke.team.submit.wr"].create(create_vals)
        msgs = "保存成功"
        if confirm:
            try:
                team_submit.confirm(center_judge)
                msgs = "报工成功"
            except Exception as e:
                _logger.error("班组报工错误")
                _logger.error(e)
                team_submit.withdraw()
                # team_submit.unlink()
                return {'state': 'error', 'msgs': e, "new_id": team_submit.id}
        return {'state': 'success', 'msgs': msgs, "new_id": team_submit.id}

    @http.route('/roke/team_work_confirm', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def team_work_confirm(self):
        values = http.request.jsonrequest
        team_work_id = values.get("team_work_id")
        if not team_work_id:
            return {'state': 'error', 'msgs': '必须入参班组报工ID'}
        team_submit_record = http.request.env['roke.team.submit.wr'].browse(int(team_work_id))
        if team_submit_record.state == "已报工":
            return {'state': 'error', 'msgs': '当前记录已报工，禁止重复报工（可以重新进入页面即可刷新状态）。'}
        try:
            team_submit_record.confirm()
        except Exception as e:
            team_submit_record.withdraw()
            return {'state': 'error', 'msgs': e}
        return {'state': 'success', 'msgs': "报工成功"}

    @http.route('/roke/team_work_delete', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def team_work_delete(self):
        values = http.request.jsonrequest
        team_work_id = values.get("team_work_id")
        if not team_work_id:
            return {'state': 'error', 'msgs': '必须入参班组报工ID'}
        team_submit_record = http.request.env['roke.team.submit.wr'].browse(int(team_work_id))
        try:
            team_submit_record.withdraw()
            team_submit_record.unlink()
        except Exception as e:
            return {'state': 'error', 'msgs': e}
        return {'state': 'success', 'msgs': "删除成功"}

    # 订单获取生产任务
    @http.route('/roke/order_get_pt', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def order_get_pt(self):
        values = http.request.jsonrequest
        order_code = values.get("order_code") or False
        # 查询销售订单
        try:
            sale_order = http.request.env['roke.sale.order'].search([("code", "=", order_code)], limit=1)
        except Exception as e:
            sale_order = None
        # 查询生产订单
        if not sale_order:
            production_order = http.request.env['roke.production.order'].search([("code", "=", order_code)], limit=1)
            pols = production_order.line_ids
        else:
            pols = http.request.env["roke.production.order.line"].search([
                ("sale_order_line", "in", sale_order.line_ids.ids)
            ])
        if not pols.task_id.work_order_ids:
            return {"state": "error", "msgs": "订单获取失败，请确认该订单已开始生产。"}
        result = []
        extra_fields = []
        for pol in pols:
            if not pol.task_id.work_order_ids:
                continue
            collection_list = []
            for wo in pol.task_id.work_order_ids:
                try:
                    for item in wo.collection_item_ids:
                        add_collection_list = {
                            "id": item.id,
                            "name": item.name,
                            "data_type": item.data_type,
                            "dict_model_index": item.dict_model_index or "",
                            "required": item.required,
                            "single_items": item.select_item_ids.mapped("value") if item.data_type == "单选" else [],
                            "multiple_items": item.select_item_ids.mapped("value") if item.data_type == "多选" else []
                        }
                        if add_collection_list not in collection_list:
                            collection_list.append(add_collection_list)
                except Exception:
                    continue
            title_settings = http.request.env(user=SUPERUSER_ID)['roke.order.submit.setting'].search([])
            extra_fields_vals = {}
            for title_setting in title_settings:
                field = title_setting.field_id
                field_value = ""
                # 获取字段值
                if title_setting.model_id.model == "roke.product":
                    field_value = getattr(pol.product_id, field.name)
                elif title_setting.model_id.model == "roke.sale.order.line":
                    field_value = getattr(pol.sale_order_line, field.name)
                elif title_setting.model_id.model == "roke.sale.order":
                    field_value = getattr(pol.sale_order_line.order_id, field.name)
                elif title_setting.model_id.model == "roke.production.order":
                    field_value = getattr(pol.order_id, field.name)
                elif title_setting.model_id.model == "roke.production.order.line":
                    field_value = getattr(pol, field.name)
                # 格式化字段值
                if field.ttype == "many2one":
                    value = field_value.display_name if field_value else ""
                elif field.ttype in ["one2many", "many2many"]:
                    value = []
                    for v in field_value:
                        value.append(v.display_name)
                else:
                    value = field_value or ""
                extra_fields_vals[title_setting.title] = value
            extra_fields.append(extra_fields_vals)
            result.append({
                "task_id": pol.task_id.id,
                "product": "%s(%s)" % (pol.task_id.product_id.name, pol.task_id.product_id.code),
                "specification": pol.task_id.product_id.specification or '',
                "plan_qty": pol.task_id.plan_qty,
                "finish_qty": pol.task_id.finish_qty,
                "process_qty": len(pol.task_id.routing_id.line_ids),
                "collection_list": collection_list,
                "extra_fields": extra_fields
            })
        return {"state": "success", "msgs": "获取成功", "customer": pols[0].order_id.customer_id.display_name or "",
                "result": result, "extra_fields": extra_fields}

    # 获取订单班组报工人员
    @http.route('/roke/get_order_team_employees', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_order_team_employees(self):
        values = http.request.jsonrequest
        team_id = values.get("team_id")
        task_id = values.get("task_id")

        team = http.request.env["roke.work.team"].browse(int(team_id))
        task = http.request.env["roke.production.task"].browse(int(task_id))
        employee_id_list = []
        for wo in task.work_order_ids:
            employee_id_list += wo.process_id.default_employee_ids.ids
        employee_ids = team.employee_ids.filtered(lambda e: e.id in set(employee_id_list))
        employee_list = []
        for employee_id in employee_ids:
            employee_list.append({
                "id": employee_id.id,
                "name": employee_id.name
            })
        return {"state": "success", "msgs": "获取成功", "employee_ids": employee_list}

    # 提交订单批量工序报工
    @http.route('/roke/multi_process_wo_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def multi_process_wo_submit(self):
        values = http.request.jsonrequest
        task_id = values.get("task_id")
        employee_ids = values.get("employee_ids") or []

        task = http.request.env["roke.production.task"].browse(int(task_id))
        if not task.work_order_ids:
            return {"state": "error", "msgs": "请确认该任务已开始生产。"}
        if len(employee_ids) > len(task.work_order_ids):
            return {"state": "error", "msgs": "当前报工人数（%s）大于当前工序数（%s），请调整报工人员" % (len(employee_ids),
                                                                                 len(task.routing_id.line_ids))}
        wizard_list = []
        for wo in task.work_order_ids:
            employee_set = set(employee_ids) & set(wo.process_id.default_employee_ids.ids)
            if not employee_set:
                for wizard in wizard_list:
                    wizard.unlink()
                return {"state": "error", "msgs": "选择的人员无法满足当前%s道工序" % (len(task.routing_id.line_ids))}
            else:
                # values["employee_list"] = []
                # for employee_id in employee_set:
                #     values["employee_list"].append({'employee_id': employee_id, 'weighted': 1})
                values["employee_list"] = [{'employee_id': list(employee_set)[0], 'weighted': 1}]
                check_result = self._check_work_submit(wo, values)
                if check_result:
                    return check_result
                wizard_list.append(http.request.env['roke.create.work.record.wizard'].create(
                    self._get_work_record_values(wo, values)
                ))
        for wizard in wizard_list:
            try:
                wizard.confirm()
            except Exception as e:
                return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "报工完成"}

    def _get_production_task_vals(self, task):
        """
        获取任务信息
        :return:
        """
        return {
            "id": task.id,
            "code": task.code,
            "product_id": task.product_id.display_name,
            "customer": task.customer_id.name or "",
            "work_center": "",
            "work_center_id": False,
            "workshop": {"id": task.workshop_id.id, "name": task.workshop_id.display_name or ""},
            "team": {"id": task.team_id.id, "name": task.team_id.display_name or ""},
            "plan_date": task.plan_date,
            "plan_qty": task.plan_qty,
            "finish_qty": task.finish_qty,
            "unqualified_qty": task.sum_unqualified_qty,
            "uom": task.uom_id.name,
            "state": task.state,
            "note": task.note or "",
            "priority": task.priority,
            "brand": False  # 是否管理商标
        }

    def _get_production_task(self, search_type=None, state=None, product=None, code=None, product_id=None):
        """
        获取生产任务
        """
        tasks = http.request.env["roke.production.task"]
        if search_type == "任务" and code:
            if "http" in code:
                # 支持带追溯地址的追溯条码
                # code = http://xxxx.com/t?c=123&a=dasfCUNFLssfe
                try:
                    code = code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    code = ""
            tasks = self._code_get_production_task(code, state=state)
        elif search_type == "生产订单" and code:
            domain = []
            if code:
                domain = [('order_id.code', 'ilike', code)]
            if state:
                domain.append(("state", "=", state))
            tasks = tasks.sudo().search(domain)
        # 筛选产品和状态
        if code:
            if product_id:
                tasks = tasks.filtered(lambda pt: pt.product_id.id == int(product_id))
            elif product:
                tasks = tasks.filtered(lambda pt: product in pt.product_id.display_name)
            if state:
                tasks = tasks.filtered(lambda pt: pt.state == state)
        else:
            domain = []
            if state:
                domain.append(("state", "=", state))
            if product_id:
                domain.append(("product_id", "=", int(product_id)))
            elif product:
                products = http.request.env["roke.product"].name_search(product, operator='ilike')
                if products:
                    domain.append(("product_id", "in", [p[0] for p in products]))
            tasks = tasks.search(domain)
        return tasks

    # 获取任务列表
    @http.route('/roke/get_production_task_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_production_task_list(self):
        values = http.request.jsonrequest
        product = values.get("product")
        product_id = values.get("product_id")
        code = values.get("code")
        state = values.get("state")
        has_work_record = values.get("has_work_record", False)
        type = values.get("type", False)
        tasks = self._get_production_task(search_type=type, state=state, product=product, code=code, product_id=product_id)
        plan_start_date1 = values.get("plan_start_date1")
        plan_start_date2 = values.get("plan_start_date2")
        plan_finish_date1 = values.get("plan_finish_date1")
        plan_finish_date2 = values.get("plan_finish_date2")
        if plan_start_date1 and plan_start_date2:
            plan_start_date1 = datetime.datetime.strptime(plan_start_date1, '%Y-%m-%d').date()
            plan_start_date2 = datetime.datetime.strptime(plan_start_date2, '%Y-%m-%d').date()
            tasks = tasks.filtered(lambda pt: pt.plan_start_date and pt.plan_start_date >= plan_start_date1 and pt.plan_start_date < plan_start_date2)
        if plan_finish_date1 and plan_finish_date2:
            plan_finish_date1 = datetime.datetime.strptime(plan_finish_date1, '%Y-%m-%d').date()
            plan_finish_date2 = datetime.datetime.strptime(plan_finish_date2, '%Y-%m-%d').date()
            tasks = tasks.filtered(lambda pt: pt.plan_date and pt.plan_date >= plan_finish_date1 and pt.plan_date < plan_finish_date2)
        if has_work_record == "是":
            tasks = tasks.filtered(lambda pt: len(pt.record_ids) > 0)
        elif has_work_record == "否":
            tasks = tasks.filtered(lambda pt: not len(pt.record_ids))
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(tasks)
        if page_size:
            total_page = math.ceil(len(tasks) / page_size)  # 总页数
            tasks = tasks[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        result = []
        for task in tasks:
            result.append(self._get_production_task_vals(task))

        return_data = {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page,
                       "total_number": total_number, "result": result}
        # 接口调用记录
        return return_data

    @http.route('/roke/get_production_task_list_type', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def get_production_task_list_type(self):
        return [
            {
                'value': 1,
                'text': '产品'
            },
            {
                'value': 2,
                'text': '任务'
            },
            {
                'value': 3,
                'text': '生产订单'
            }
        ]

    def pt_get_wo_collection(self, wo):
        """
        任务获取工单列表下的采集项
        :return:
        """
        return []

    def pt_get_wo_vals(self, wo, default_qty, print_code):
        """
        任务报工获取工单值
        """
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        exceeding_rated_wh_val = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
        exceeding_rated_wh = http_tool.selection_to_dict("res.config.settings", "exceeding_rated_wh")[exceeding_rated_wh_val]
        autofill_rated_wh = True if ConfigParameter.get_param('autofill.rated.working.hours') == "1" else False
        return {
            "id": wo.id,
            "process_name": wo.process_id.name,
            "process_id": wo.process_id.id,
            "work_center_name": wo.work_center_id.display_name or "",
            "work_center_id": wo.work_center_id.id,
            "finish_qty": wo.finish_qty,
            "plan_qty": wo.plan_qty,
            "unqualified_qty": wo.unqualified_qty,
            "priority": wo.priority,
            "allow_qty": default_qty,
            "state": wo.state,
            "print_code": print_code,
            "collection_list": self.pt_get_wo_collection(wo),
            "rated_working_hours": wo.rated_working_hours,
            "exceeding_rated_wh": exceeding_rated_wh,
            "autofill_rated_wh": autofill_rated_wh,
            "start_time": wo.planned_start_time.strftime('%Y-%m-%d %H:%M:%S') if wo.planned_start_time else "",
            "end_time": wo.plan_date.strftime('%Y-%m-%d %H:%M:%S') if wo.plan_date else "",
            "work_hours": 0 if wo.state == '未完工' else sum(wo.record_ids.mapped("work_hours"))
        }

    # 任务报工获取工单列表
    @http.route('/roke/pt_get_wo_list_yl', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def pt_get_wo_list_yl(self):
        values = http.request.jsonrequest
        task_id = values.get("task_id")
        task_code = values.get("task_code") or ""
        process = values.get("process")
        file_type = http.request.jsonrequest.get('file_type', False)
        filtered_employee = values.get('filtered_employee', False)
        if task_id:
            task = http.request.env["roke.production.task"].browse(int(task_id))
        else:
            if "http" in task_code:
                try:
                    task_code = task_code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    task_code = ""
            task = self._code_get_production_task(task_code, limit=1)
        WoObj = http.request.env["roke.work.order"]
        if not task:
            return {"state": "error", "msgs": "未获取到生产任务"}
        if len(task) > 1:
            return {"state": "error", "msgs": "获取到多个生产任务，请输入具体的任务编号。"}
        work_orders = WoObj.with_context(
            check_allow_process=True
        ).search([("task_id", "=", task.id), ("child_wo_ids", "=", False)])

        standard_list = []
        for item in task.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
            })
        # 如果filtered_employee为True，筛选工序中的默认当前任务报工人员为登陆人对应的工序.TODO 明明后台加数据权限就能解决的事情，为什么要编码呢？！
        if filtered_employee:
            work_orders = work_orders.filtered(
                lambda wo: wo.process_id.default_employee_ids and
                           http.request.env.user.id in wo.process_id.default_employee_ids.mapped("user_id").ids
            )
        process_list = []
        for wo in work_orders:
            process_list.append({
                "id": wo.process_id.id,
                "name": wo.process_id.name,
            })
        if process:
            work_orders = work_orders.filtered(lambda wo: wo.process_id.id in process)
        wo_list = []

        operation_after_completion = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param(
            'operation.after.completion', default="无操作"
        )
        print_code = False
        if operation_after_completion == "打印批次条码":
            print_code = True
        for wo in work_orders:
            allow_qty, default_qty = wo._get_wo_allow_qty()
            same_sequence_wo = wo.task_id.work_order_ids.filtered(lambda work_order: work_order.sequence == wo.sequence)
            if len(same_sequence_wo) > 1:
                default_qty = 0
            wo_list.append(self.pt_get_wo_vals(wo, default_qty, print_code))
        quick = False if http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('app.task.quick.report', default="不启用") == "不启用" else True
        return {
            "state": "success",
            "msgs": "获取成功",
            "task_id": task.id,
            "task_vals": self._get_production_task_vals(task),
            "quick": quick,
            "product": {"id": task.product_id.id, "name": task.product_id.name},
            "wo_list": wo_list,
            "process_list": process_list,
            "standard_list": standard_list,
            "file_url": task.get_instruction_file_url(file_type=file_type) or [],
        }

    # 任务报工获取工单列表
    @http.route('/roke/pt_get_wo_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def pt_get_wo_list(self):
        values = http.request.jsonrequest
        task_id = values.get("task_id")
        task_code = values.get("task_code") or ""
        process = values.get("process")
        file_type = http.request.jsonrequest.get('file_type', False)
        filtered_employee = values.get('filtered_employee', False)
        if task_id:
            task = http.request.env["roke.production.task"].browse(int(task_id))
        else:
            if "http" in task_code:
                try:
                    task_code = task_code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    task_code = ""
            task = self._code_get_production_task(task_code, limit=1)
        if not task:
            return {"state": "error", "msgs": "未获取到生产任务"}
        if len(task) > 1:
            return {"state": "error", "msgs": "获取到多个生产任务，请输入具体的任务编号。"}
        WoObj = http.request.env["roke.work.order"]
        work_orders = WoObj.with_context(
            check_allow_process=True
        ).search([("task_id", "=", task.id), ("child_wo_ids", "=", False)])  # 工单列表禁止获取存在子工序的工单
        standard_list = []
        for item in task.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "is_picture": item.get_image_preview_url(file_type=file_type)[0].get('is_picture')
            })
        # 如果filtered_employee为True，筛选工序中的默认当前任务报工人员为登陆人对应的工序
        if filtered_employee:
            work_orders = work_orders.filtered(
                lambda wo: wo.process_id.default_employee_ids and
                           http.request.env.user.id in wo.process_id.default_employee_ids.mapped("user_id").ids
            )
        # 已完工的工序不在出现在列表
        process_list = []
        for wo in work_orders:
            if wo.state == "未完工":
                process_list.append({
                    "id": wo.process_id.id,
                    "name": wo.process_id.name,
                })
        wo_list = []

        operation_after_completion = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param(
            'operation.after.completion', default="无操作"
        )
        print_code = False
        if operation_after_completion == "打印批次条码":
            print_code = True
        if process and isinstance(process, list):
            work_orders = work_orders.filtered(lambda wo: wo.process_id.id in process)
        for wo in work_orders:
            if wo.state == "未完工":
                allow_qty, default_qty = wo._get_wo_allow_qty()
                if wo.main_wo_id:
                    same_sequence_wo = wo.task_id.work_order_ids.filtered(lambda work_order: work_order.sequence == wo.sequence and work_order.main_wo_id == wo.main_wo_id)
                else:
                    same_sequence_wo = wo.task_id.work_order_ids.filtered(lambda work_order: work_order.sequence == wo.sequence and not work_order.main_wo_id)
                if len(same_sequence_wo) > 1:
                    default_qty = 0
                wo_list.append(self.pt_get_wo_vals(wo, default_qty, print_code))
        quick = False if http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('app.task.quick.report', default="不启用") == "不启用" else True
        return {
            "state": "success",
            "msgs": "获取成功",
            "task_id": task.id,
            "task_vals": self._get_production_task_vals(task),
            "quick": quick,
            "product": {"id": task.product_id.id, "name": task.product_id.name},
            "wo_list": wo_list,
            "process_list": process_list,
            "standard_list": standard_list,
            "file_url": task.get_instruction_file_url(file_type=file_type) or [],
        }

    def pt_get_wo_vals_new(self, wo, allow_qty, default_qty, print_code):
        """
        任务报工获取工单值
        """
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        exceeding_rated_wh_val = ConfigParameter.get_param('exceeding.rated.working.hours', default="allowed")
        exceeding_rated_wh = http_tool.selection_to_dict("res.config.settings", "exceeding_rated_wh")[exceeding_rated_wh_val]
        autofill_rated_wh = True if ConfigParameter.get_param('autofill.rated.working.hours') == "1" else False
        work_center = wo.work_center_id
        if not work_center:
            if wo.routing_line_id.work_center_ids:
                work_center = wo.routing_line_id.work_center_ids[0].work_center_id
            elif wo.process_id.work_center_ids:
                work_center = wo.process_id.work_center_ids[0].work_center_id
        return {
            "id": wo.id,
            "process_name": wo.process_id.name,
            "process_id": wo.process_id.id,
            "work_center_name": work_center.display_name or "",
            "work_center_id": work_center.id,
            "finish_qty": wo.finish_qty,
            "plan_qty": wo.plan_qty,
            "unqualified_qty": wo.unqualified_qty,
            "priority": wo.priority,
            "allow_qty": allow_qty,
            "default_qty": default_qty,
            "salary_type": "",
            "state": wo.state,
            "print_code": print_code,
            "collection_list": self.pt_get_wo_collection(wo),
            "rated_working_hours": wo.rated_working_hours,
            "exceeding_rated_wh": exceeding_rated_wh,
            "autofill_rated_wh": autofill_rated_wh,
            "work_hours": 0 if wo.state == '未完工' else sum(wo.record_ids.mapped("work_hours"))
        }

    # 任务报工获取工单列表(新)
    @http.route('/roke/pt_get_wo_list_new', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def pt_get_wo_list_new(self):
        values = http.request.jsonrequest
        task_id = values.get("task_id")
        task_code = values.get("task_code") or ""
        process = values.get("process")
        file_type = http.request.jsonrequest.get('file_type', False)
        filtered_employee = values.get('filtered_employee', False)
        if task_id:
            task = http.request.env["roke.production.task"].browse(int(task_id))
        else:
            if "http" in task_code:
                try:
                    task_code = task_code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    task_code = ""
            task = self._code_get_production_task(task_code, limit=1)
        if not task:
            return {"state": "error", "msgs": "未获取到生产任务"}
        if len(task) > 1:
            return {"state": "error", "msgs": "获取到多个生产任务，请输入具体的任务编号。"}
        WoObj = http.request.env["roke.work.order"]
        work_orders = WoObj.with_context(
            check_allow_process=True
        ).search([("task_id", "=", task.id), ("child_wo_ids", "=", False)])  # 工单列表禁止获取存在子工序的工单
        standard_list = []
        for item in task.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "is_picture": item.get_image_preview_url(file_type=file_type)[0].get('is_picture')
            })
        # 如果filtered_employee为True，筛选工序中的默认当前任务报工人员为登陆人对应的工序
        if filtered_employee:
            work_orders = work_orders.filtered(
                lambda wo: wo.process_id.default_employee_ids and
                           http.request.env.user.id in wo.process_id.default_employee_ids.mapped("user_id").ids
            )
        # 已完工的工序不在出现在列表
        process_list = []
        for wo in work_orders:
            process_list.append({
                "id": wo.process_id.id,
                "name": wo.process_id.name,
            })
        wo_list = []
        operation_after_completion = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param(
            'operation.after.completion', default="无操作"
        )
        print_code = False
        if operation_after_completion == "打印批次条码":
            print_code = True
        if process and isinstance(process, list):
            work_orders = work_orders.filtered(lambda wo: wo.process_id.id in process)
        for wo in work_orders:
            allow_qty, default_qty = wo._get_wo_allow_qty()
            wo_list.append(self.pt_get_wo_vals_new(wo, allow_qty, default_qty, print_code))
        quick = False if http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('app.task.quick.report', default="不启用") == "不启用" else True
        return {
            "state": "success",
            "msgs": "获取成功",
            "task_id": task.id,
            "task_vals": self._get_production_task_vals(task),
            "quick": quick,
            "product": {"id": task.product_id.id, "name": task.product_id.name},
            "wo_list": wo_list,
            "process_list": process_list,
            "standard_list": standard_list,
            "file_url": task.get_instruction_file_url(file_type=file_type) or [],
        }

    # 任务报工获取工单列表(免登录)
    @http.route('/roke/auth_none/pt_get_wo_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def pt_get_wo_list_auth_none(self):
        values = http.request.jsonrequest
        task_id = values.get("task_id")
        task_code = values.get("task_code") or ""
        process = values.get("process")
        file_type = http.request.jsonrequest.get('file_type', False)
        filtered_employee = values.get('filtered_employee', False)
        if task_id:
            task = http.request.env["roke.production.task"].sudo().browse(int(task_id))
        else:
            if "http" in task_code:
                try:
                    task_code = task_code.split("?")[1].split("&")[0].split("=")[1]
                except Exception as e:
                    task_code = ""
            task = self._code_get_production_task_auth_none(task_code, limit=1)
        if not task:
            return {"state": "error", "msgs": "未获取到生产任务"}
        if len(task) > 1:
            return {"state": "error", "msgs": "获取到多个生产任务，请输入具体的任务编号。"}
        WoObj = http.request.env(user=SUPERUSER_ID)["roke.work.order"]
        work_orders = WoObj.with_context(
            check_allow_process=True
        ).search([("task_id", "=", task.id), ("child_wo_ids", "=", False)])  # 工单列表禁止获取存在子工序的工单
        standard_list = []
        for item in task.standard_item_ids:
            standard_list.append({
                "title": item.title,
                "name": item.name,
                "image_url": item.get_image_preview_url(file_type=file_type),
                "is_picture": item.get_image_preview_url(file_type=file_type)[0].get('is_picture')
            })
        # 如果filtered_employee为True，筛选工序中的默认当前任务报工人员为登陆人对应的工序
        if filtered_employee:
            work_orders = work_orders.filtered(
                lambda wo: wo.process_id.default_employee_ids and
                           http.request.env.user.id in wo.process_id.default_employee_ids.mapped("user_id").ids
            )
        # 已完工的工序不在出现在列表
        process_list = []
        for wo in work_orders:
            if wo.state == "未完工":
                process_list.append({
                    "id": wo.process_id.id,
                    "name": wo.process_id.name,
                })
        wo_list = []

        operation_after_completion = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param(
            'operation.after.completion', default="无操作"
        )
        print_code = False
        if operation_after_completion == "打印批次条码":
            print_code = True
        if process and isinstance(process, list):
            work_orders = work_orders.filtered(lambda wo: wo.process_id.id in process)
        for wo in work_orders:
            if wo.state == "未完工":
                allow_qty, default_qty = wo._get_wo_allow_qty()
                if wo.main_wo_id:
                    same_sequence_wo = wo.task_id.work_order_ids.filtered(lambda work_order: work_order.sequence == wo.sequence and work_order.main_wo_id == wo.main_wo_id)
                else:
                    same_sequence_wo = wo.task_id.work_order_ids.filtered(lambda work_order: work_order.sequence == wo.sequence and not work_order.main_wo_id)
                if len(same_sequence_wo) > 1:
                    default_qty = 0
                wo_list.append(self.pt_get_wo_vals(wo, default_qty, print_code))
        quick = False if http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('app.task.quick.report', default="不启用") == "不启用" else True
        return {
            "state": "success",
            "msgs": "获取成功",
            "task_id": task.id,
            "task_vals": self._get_production_task_vals(task),
            "quick": quick,
            "product": {"id": task.product_id.id, "name": task.product_id.name},
            "wo_list": wo_list,
            "process_list": process_list,
            "standard_list": standard_list,
            "file_url": task.get_instruction_file_url(file_type=file_type) or [],
        }

    # 获取报工图片，入参工单ID或编号，获取对应任务下所有工序的报工图片
    @http.route('/roke/get_work_image', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_work_image(self):
        work_order_id = http.request.jsonrequest.get("work_order_id", "")
        try:
            work_order = http.request.env["roke.work.order"].browse(int(work_order_id))
            task = work_order.task_id
        except Exception:
            return {
                "state": "error",
                "msgs": "工单ID入参错误，当前入参：%s" % str(work_order_id)
            }
        base_url = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('web.base.url')
        process_list = []
        Attachment = http.request.env(user=SUPERUSER_ID)['ir.attachment']
        for wo in task.work_order_ids:
            work_record_list = []
            for wr in wo.record_ids:
                image_urls = []
                attachments = Attachment.search([('res_model', '=', 'roke.work.record'), ('res_id', '=', wr.id), ("name", "like", "报工：")])
                for attachment in attachments:
                    if not attachment.access_token:
                        attachment.generate_access_token()
                    image_urls.append("%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token))
                work_record_list.append({
                    "work_time": (wr.work_time + datetime.timedelta(hours=8)).strftime('%y/%m/%d %H:%M'),
                    "employees": ','.join(wr.employee_ids.mapped("display_name")),
                    "image_urls": image_urls,
                    "has_image": True if image_urls else False
                })
            process_list.append({
                "process": wo.process_id.display_name,
                "work_record_list": work_record_list
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "process_list": process_list
        }

    # 无工单报工
    @http.route('/roke/without_order_multi_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def without_order_multi_submit(self):
        """
        无工单批量报工
        {
            "employee_list": [{"employee_id": 1, "weighted": 1}, {"employee_id": 2, "weighted": 1}],
            "team_id": 1,
            "work_center_id": 1,
            "classes_id": 1,
            "work_time": "2023-01-01 12:12",
            "detail_list": [{
                "product_id": 1,
                "process_id": 1,
                "finish_qty": 10,
                "unqualified_qty": 5,
                "scrap_qty": 2,
                "repair_qty": 3,
                "scrap_list": [{"reason_id": 1, "qty": 1, "note": "报废备注"}, {"reason_id": 2, "qty": 1}],
                "repair_list": [{"reason_id": 1, "qty": 1, "note": "返修备注"}, {"reason_id": 2, "qty": 2}],
                "collection_list": [{"item_id": 1, "result": 12}],
                "note": "第一条备注"
            },{
                "product_id": 1,
                "process_id": 1,
                "finish_qty": 10,
                "note": "第二条备注"
            }]
        }
        :return:
        """
        values = http.request.jsonrequest
        # employee_list = values.get('employee_list', [])  # 人员分配：[{'employee_id':1, 'weighted': 1}]
        detail_list = values.get('detail_list', [])  # 报工详情
        if values.get("work_time"):  # 接口文档这个参数名写错了，暂时从后台调整一下
            values["report_time"] = values.pop("work_time")
        if not detail_list:
            return {"state": "error", "msgs": "报工内容不可为空"}
        WorkOrderObj = http.request.env['roke.work.order']
        ProductionOrderObj = http.request.env['roke.production.order']
        i = 0
        try:
            for detail in detail_list:
                employee_list = detail.get("employee_list", [])
                if not employee_list:
                    return {"state": "error", "msgs": "报工人员不可为空"}
                for employee_val in employee_list:
                    if not employee_val.get("employee_id"):
                        return {"state": "error", "msgs": "人员不可为空"}
                product_id = detail.get("product_id")
                process_id = detail.get("process_id")
                customer_id = detail.get("customer_id", False)
                production_order_id = detail.get("production_order_id", False)
                finish_qty = detail.get("finish_qty")
                if not process_id:
                    return {"state": "error", "msgs": "工序不可为空"}
                if not product_id:
                    return {"state": "error", "msgs": "产品不可为空"}
                collection_list = detail.get('collection_list', [])  # 采集列表
                detail.update(values)
                # 创建工单
                work_order = WorkOrderObj.create(
                    self._without_order_get_order_value(product_id, process_id, finish_qty, collection_list, detail)
                )
                # 无工单报工后修改客户等内容，因为是related字段，直接创建写不进值
                wo_write_dict = {}
                if customer_id:
                    wo_write_dict["customer_id"] = int(customer_id)
                if production_order_id:
                    production_order = ProductionOrderObj.browse(int(production_order_id))
                    wo_write_dict["project_code"] = production_order.project_code or production_order.display_name or ""
                if wo_write_dict:
                    work_order.write(wo_write_dict)
                # 报工
                wizard = http.request.env['roke.create.work.record.wizard'].create(
                    self._get_work_record_values(work_order, detail)
                )
                wizard.confirm()  # 确认执行报工, 创建报工记录
                i += 1
            return {"state": "success", "msgs": "报工成功，本次报工%s条记录。" % i}
        except Exception as e:
            return {"state": "error", "msgs": e}

    def _without_order_team_multi_submit_collection(self, collection_list):
        """
        无工单班组批量报工获取采集项内容
        :param collection_list:
        :return:
        """
        return []

    # 无工单班组批量报工
    @http.route('/roke/without_order_team_multi_submit', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def without_order_team_multi_submit(self):
        """
        无工单班组批量报工
        {
            "employee_list": [1,2,3],
            "team_id": 1,
            "work_center_id": 1,
            "classes_id": 1,
            "work_time": "2023-01-01 12:12",
            "product_id": 1,
            "process_id": 1,
            "finish_qty": 10,
            "unqualified_qty": 5,
            "scrap_qty": 2,
            "repair_qty": 3,
            "scrap_list": [{"reason_id": 1, "qty": 1, "note": "报废备注"}, {"reason_id": 2, "qty": 1}],
            "repair_list": [{"reason_id": 1, "qty": 1, "note": "返修备注"}, {"reason_id": 2, "qty": 2}],
            "collection_list": [{"item_id": 1, "result": 12}],
            "note": "第一条备注"
        }
        :return:
        """
        values = http.request.jsonrequest
        team_id = values.get("team_id", False)
        employee_list = values.get("employee_list", [])
        if not employee_list:
            return {"state": "error", "msgs": "报工人员employee_list不可为空"}

        id = values.get("id")
        confirm = values.get("confirm")
        work_hours = values.get("work_hours") or 0
        work_time = values.get("work_time")
        product_id = values.get("product_id") or False
        process_id = values.get("process_id") or False
        finish_qty = values.get("finish_qty") or 0
        unqualified_qty = values.get("unqualified_qty") or 0
        work_center_id = values.get("work_center_id") or False
        classes_id = values.get("classes_id") or False
        note = values.get("note") or ""
        collection_list = values.get("collection_list") or []

        team_submit = http.request.env["roke.team.submit.wr"]
        if id:
            team_submit = team_submit.browse(id)
            if team_submit.state == "已报工":
                return {'state': 'error', 'msgs': '当前单据已报工禁止编辑', 'new_id': team_submit.id}
        EmployeeObj = http.request.env["roke.employee"]
        employee_vals = []
        for employee_id in employee_list:
            employee = EmployeeObj.browse(int(employee_id))
            if not team_id:
                team_id = employee_id.team_id.id
            # 报工内容
            employee_vals.append((0, 0, {
                "employee_id": employee.id,
                "sum_work_hours": work_hours,
                "wr_line_ids": [(0, 0, {
                    "product_id": product_id,
                    "process_id": process_id,
                    "finish_qty": finish_qty,
                    "unqualified_qty": unqualified_qty,
                    "work_hours": work_hours,
                    "work_center_id": work_center_id,
                    "note": note,
                    "collection_result_ids": self._without_order_team_multi_submit_collection(collection_list)
                })]
            }))
        # 创建班组报工记录
        if work_time:
            work_time = datetime.datetime.strptime(work_time, "%Y-%m-%d %H:%M:%S") - datetime.timedelta(hours=8)
        else:
            work_time = fields.Datetime.now()
        if team_submit:
            for employee_line in team_submit.employee_line_ids:
                employee_vals.append((2, employee_line.id))
            team_submit.write({
                "team_id": int(team_id),
                "classes_id": int(classes_id) if classes_id else False,
                "submit_date": work_time,
                "note": note,
                "employee_line_ids": employee_vals
            })
        else:
            team_submit = http.request.env["roke.team.submit.wr"].create({
                "team_id": int(team_id),
                "classes_id": classes_id,
                "submit_date": work_time,
                "note": note,
                "employee_line_ids": employee_vals
            })
        msgs = "保存成功"
        if confirm:
            try:
                team_submit.confirm()
                msgs = "报工成功"
            except Exception as e:
                _logger.error("无工单班组批量报工错误")
                _logger.error(e)
                team_submit.withdraw()
                # team_submit.unlink()
                return {'state': 'error', 'msgs': e}
        return {'state': 'success', 'msgs': msgs, "team_submit_id": team_submit.id}

    def _create_get_routing_line_vals(self, process_data):
        """
        获取工艺明细的值
        TODO 采集项等工艺明细上的内容暂不处理
        :param process:
        :return:
        """
        process_id = process_data.get("process_id")
        if not process_id:
            raise Exception("必须入参工序ID")
        return {
            "process_id": process_id,
            "sequence": int(process_data.get("sequence")),
            "work_qty": int(process_data.get("qty", 1)),
            "rated_working_hours": process_data.get("rated_working_hours", 0),
            "note": process_data.get("note", "")
        }

    # 创建工艺路线
    @http.route('/roke/create_routing', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_routing(self):
        """
        创建工艺路线
        :return:
        """
        values = http.request.jsonrequest
        name = values.get('name', "")  # 名称
        internal_code = values.get('internal_code', "")  # 内部标识
        template_id = values.get('template_id') or False  # 工艺路线模板（工艺路线规则）
        product_id = values.get('product_id') or False  # 可用产品id
        note = values.get('note', "")  # 备注
        process_list = values.get('process_list', [])  # 工序列表
        process_vals = []
        try:
            for process_data in process_list:
                process_vals.append((0, 0, self._create_get_routing_line_vals(process_data)))
            new_routing_data = {
                "name": name or (fields.Datetime.now() + datetime.timedelta(hours=8)).strftime('%y%m%d %H:%M'),
                "internal_code": internal_code,
                "template_id": template_id,
                "note": note,
                "line_ids": process_vals
            }
            if product_id:
                new_routing_data["product_ids"] = [(6, 0, [product_id])]
            new_routing = http.request.env["roke.routing"].create(new_routing_data)
        except Exception as e:
            return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "创建成功", "new_routing_id": new_routing.id, "new_routing_name": new_routing.display_name}

    def _edit_get_routing_line_vals(self, process_data):
        """
        编辑工艺明细值
        :return:
        """
        process_id = process_data.get("process_id")
        sequence = process_data.get("sequence")
        work_qty = process_data.get("work_qty")
        note = process_data.get("note")
        rated_working_hours = process_data.get("rated_working_hours")
        edit_vals = {}
        if process_id:
            edit_vals["process_id"] = process_id
        if sequence:
            edit_vals["sequence"] = sequence
        if work_qty:
            edit_vals["work_qty"] = work_qty
        if note:
            edit_vals["note"] = note
        if rated_working_hours:
            edit_vals["rated_working_hours"] = rated_working_hours
        return edit_vals

    # 修改工艺路线
    @http.route('/roke/edit_routing', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def edit_routing(self):
        """
        编辑工艺路线
            修改工艺表头
            添加/删除工艺明细、修改序号、工资
        :return:
        """
        values = http.request.jsonrequest
        routing_id = values.get('routing_id', "")
        if not routing_id:
            return {"state": "error", "msgs": "必须入参工艺ID"}
        name = values.get('name', "")
        internal_code = values.get('internal_code', "")
        note = values.get('note', "")
        process_list = values.get("process_list", [])
        template_id = values.get('template_id') or False  # 工艺路线模板（工艺路线规则）
        line_ids = []
        for process_data in process_list:
            line_id = process_data.get("id", False)
            delete = process_data.get("delete", False)
            if delete:
                # 删除
                line_ids.append((2, line_id))
            else:
                line_edit_vals = self._edit_get_routing_line_vals(process_data)
                if not line_edit_vals:
                    continue
                if process_data.get("id", False):
                    # 编辑
                    line_ids.append((1, line_id, line_edit_vals))
                else:
                    # 新建
                    line_ids.append((0, 0, line_edit_vals))
        edit_vals = {}
        if name:
            edit_vals["name"] = name
        if internal_code:
            edit_vals["internal_code"] = internal_code
        if template_id:
            edit_vals["template_id"] = template_id
        if note:
            edit_vals["note"] = note
        if line_ids:
            edit_vals["line_ids"] = line_ids
        if edit_vals:
            http.request.env["roke.routing"].browse(int(routing_id)).write(edit_vals)
        return {"state": "success", "msgs": "编辑成功", "routing_id": int(routing_id)}

    def _get_routing_line_info(self, line):
        """
        获取工艺明细信息
        :return:
        """
        return {
            "id": line.id,
            "sequence": line.sequence,
            "process": {"id": line.process_id.id, "code": line.process_id.code, "name": line.process_id.name},
            "qty": line.work_qty,
            "note": line.note or "",
            "rated_working_hours": line.rated_working_hours
        }

    def _get_routing_info(self, routing, detail=False, product_doc_code=""):
        """
        获取工艺路线信息
        :return:
        """
        value = {
            "id": routing.id,
            "code": routing.code,
            "name": routing.name,
            "internal_code": routing.internal_code or "",
            "note": routing.note or "",
            "template_id": routing.template_id.id or "",
            "product_doc_code": product_doc_code
        }
        if detail:
            process_list = []
            for line in routing.line_ids:
                process_list.append(self._get_routing_line_info(line))
            value["process_list"] = process_list
        return value

    # 工艺路线列表
    @http.route('/roke/get_routing_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_routing_list(self):
        """
        工艺路线列表
        :return:
        """
        params = http.request.jsonrequest
        name = params.get('name', "")  # 名称
        code = params.get('code', "")  # 编号
        internal_code = params.get('internal_code', "")  # 内部编号
        note = params.get('备注', "")  # 内部编号
        domain = []
        if name:
            domain.append(("name", "like", name))
        if code:
            domain.append(("code", "like", code))
        if internal_code:
            domain.append(("internal_code", "like", internal_code))
        if note:
            domain.append(("note", "like", note))
        routing_list = http.request.env["roke.routing"].search(domain)
        # 处理分页
        page_no = params.get('page_no', 1)  # 页码
        page_size = params.get('page_size', 20)  # 每页记录数
        total_number = len(routing_list)
        total_page = math.ceil(len(routing_list) / page_size)  # 总页数
        routing_list = routing_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        result = []
        for routing in routing_list:
            result.append(self._get_routing_info(routing, detail=False))
        return {"state": "success", "msgs": "获取成功", "page_no": page_no, "total_page": total_page, "total_number": total_number, "result": result}

    # 工艺路线详情
    @http.route('/roke/get_routing_detail', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_routing_detail(self):
        """
        工艺路线列表
        :return:
        """
        params = http.request.jsonrequest
        routing_id = params.get('routing_id', "")
        product_id = params.get('product_id', "")
        if not routing_id:
            return {"state": "error", "msgs": "必须入参工艺ID"}
        product_doc_code = ""
        if product_id:
            product = http.request.env["roke.product"].browse(int(product_id))
            if hasattr(product, "document_code"):
                product_doc_code = product.document_code or ""
        routing = http.request.env["roke.routing"].browse(int(routing_id))
        result = self._get_routing_info(routing, detail=True, product_doc_code=product_doc_code)
        return {"state": "success", "msgs": "获取成功", "result": result}

    @http.route('/roke/delete_routing', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def delete_routing(self):
        """
        删除工艺
        :return:
        """
        params = http.request.jsonrequest
        routing_ids = params.get('routing_ids', "")
        if not routing_ids:
            return {"state": "error", "msgs": "必须入参工艺ID列表"}
        routing = http.request.env(user=SUPERUSER_ID)["roke.routing"].browse(routing_ids)
        if http.request.env(user=SUPERUSER_ID)["roke.production.task"].search([
            ("routing_id", "in", routing_ids), ("state", "not in", ["已完工", "强制完工"])
        ], limit=1):
            routing.write({"active": False})
            return {"state": "success", "msgs": "当前工艺正在使用中，已为您自动归档。"}
        try:
            routing.unlink()
        except Exception as e:
            routing.write({"active": False})
            return {"state": "success", "msgs": "当前工艺删除失败，但已为您自动归档。"}
        return {"state": "success", "msgs": "删除成功"}

    def _get_process_vals(self, values):
        """
        获取创建工序值
        :return:
        """
        return {
            "name": values.get("name", "")
        }

    # 创建工序
    @http.route('/roke/create_process', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_process(self):
        """
        创建工序
        :return:
        """
        if not http.request.jsonrequest.get("name"):
            return {"state": "error", "msgs": "必须入参工序名称"}
        new_process = http.request.env["roke.process"].create(self._get_process_vals(http.request.jsonrequest))
        return {"state": "success", "msgs": "创建成功", "new_process_id": new_process.id, "new_process_name": "%s（%s）" % (new_process.name, new_process.code)}

    # 补打条码获取产出物ID
    @http.route('/roke/get_print_lot_code_ids', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_print_lot_code_ids(self):
        """
        补打条码获取产出物ID
        :return:
        """
        work_record_id = http.request.jsonrequest.get("work_record_id")
        if not work_record_id:
            return {"state": "error", "msgs": "必须入参work_record_id"}
        work_record = http.request.env["roke.work.record"].browse(int(work_record_id))
        if work_record.result_ids:
            return {"state": "success", "msgs": "获取成功", "print_lot_code_ids": work_record.get_print_lot_code_ids()}
        else:
            return {"state": "error", "msgs": "当前报工记录没有可打印的内容"}

    # 强制完工
    @http.route('/roke/force_finish', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def force_finish(self):
        """
        强制完工
        :return:
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        production_task_id = http.request.jsonrequest.get("production_task_id")
        if not work_order_id and not production_task_id:
            return {"state": "error", "msgs": "工单ID和生产任务ID必须入参其中一个"}
        try:
            if work_order_id:
                # 工单强制完工 fix bug 4096 工单强制完工仅强制完工当前工单
                wo = http.request.env["roke.work.order"].browse(int(work_order_id))
                wo.force_finish()
                return {"state": "success", "msgs": "操作成功"}
            else:
                # 任务强制完工
                production_task = http.request.env["roke.work.record"].browse(int(production_task_id))
                production_task.force_finish()
        except Exception as e:
            return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "操作成功"}

    # 取消完工
    @http.route('/roke/cancel_force_finish', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def cancel_force_finish(self):
        """
        取消强制完工
        :return:
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        production_task_id = http.request.jsonrequest.get("production_task_id")
        if not work_order_id and not production_task_id:
            return {"state": "error", "msgs": "工单ID和生产任务ID必须入参其中一个"}
        try:
            if work_order_id:
                # 工单强制完工
                production_task = http.request.env["roke.work.order"].browse(int(work_order_id)).task_id
            else:
                # 任务强制完工
                production_task = http.request.env["roke.work.record"].browse(int(production_task_id))
            production_task.cancel_force_finish()
        except Exception as e:
            return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "操作成功"}

    # 单独提交采集结果
    @http.route('/roke/submit_collection_result', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def submti_collection_result(self):
        """
        单独提交采集结果
        :return:
        """
        return {"state": "error", "msgs": "当前系统未开通生产采集功能，请联系管理员开通相关权限。"}

    # 获取生产订单
    @http.route('/roke/get_production_order', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_production_order(self):
        """
        获取生产订单
        :return:
        """
        order_code = http.request.jsonrequest.get("order_code")
        state = http.request.jsonrequest.get("state")
        has_work_order = http.request.jsonrequest.get("has_work_order")
        domain = []
        if order_code:
            domain.append(("code", "ilike", order_code))
        if state:
            domain.append(("state", "=", state))
        production_orders = http.request.env["roke.production.order"].search(domain)
        if has_work_order:
            production_orders = production_orders.filtered(lambda po: len(po.line_ids.task_ids.work_order_ids) > 0)
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(production_orders)
        if page_size:
            total_page = math.ceil(len(production_orders) / page_size)  # 总页数
            production_orders = production_orders[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        result = []
        for production_order in production_orders:
            val = {
                "id": production_order.id,
                "code": production_order.code,
                "state": production_order.state
            }
            if production_order.customer_id:
                val["customer"] = {"id": production_order.customer_id.id, "name": production_order.customer_id.display_name}
                val["customer_name"] = production_order.customer_id.display_name
            result.append(val)
        return {
            "state": "success",
            "msgs": "获取成功。",
            "page_no": page_no,
            "total_page": total_page,
            "total_number": total_number,
            "result": result
        }

    def _get_salary_total(self, values):
        """
        计算工资  实现于工资模块
        """
        return 0

    # 计算工资
    @http.route('/roke/compute_salary', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def compute_salary(self):
        """
        获取工资
        """
        salary = self._get_salary_total(http.request.jsonrequest)
        return {"state": "success", "msgs": "获取成功。", "salary": salary}

    # 创建返修原因
    @http.route('/roke/create_repair_reason', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_repair_reason(self):
        """
        创建返修原因
        """
        return {"state": "error", "msgs": "未启用质量管理，忽略创建。"}

    # 创建报废原因
    @http.route('/roke/create_scrap_reason', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_scrap_reason(self):
        """
        创建报废原因
        """
        return {"state": "error", "msgs": "未启用质量管理，忽略创建。"}

    # -----自定义报工-----
    # 自定义报工
    @http.route('/roke/report_for_work', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def report_for_work(self):
        """
        报工
        """
        data_list = http.request.jsonrequest.get('data_list', "")
        # 创建工单
        http.request.env["roke.work.record.fine"].report_for_work(data_list)
        return {
            "state": "success",
            "msgs": "提交成功",
        }

    @http.route('/roke/get_emp_list', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_emp_list(self):
        employee_condition = http.request.jsonrequest.get('employee', False)  # 工序名称或编号
        team_id = http.request.jsonrequest.get('team_id', False)
        domain = []
        if employee_condition:
            domain = [
                "|",
                ("name", "ilike", employee_condition),
                ("code", "ilike", employee_condition)
            ]
        employee_list = http.request.env(user=SUPERUSER_ID)['roke.employee'].search(
            domain
        )
        if employee_list and team_id:
            employee_list = employee_list.filtered(lambda e: e.team_id.id == int(team_id))
        # 处理分页
        page_no = http.request.jsonrequest.get('page_no', 1)  # 页码
        page_size = http.request.jsonrequest.get('page_size', 0)  # 每页记录数
        total_number = len(employee_list)
        if page_size:
            total_page = math.ceil(len(employee_list) / page_size)  # 总页数
            employee_list = employee_list[(page_no - 1) * page_size: page_no * page_size]  # 当前页记录
        else:
            total_page = 0
        result = []
        for employee in employee_list:
            result.append({
                "emp_id": employee.id,
                "name": employee.name,
                "team_id": employee.team_id.id
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "page_no": page_no,
            "total_page": total_page,
            "total_number": total_number,
            "result": result
        }

    def _get_edit_wr_wizard_val(self, values):
        """
        获取编辑报工记录值,便于其它模块集成
        :return:
        """
        if values.__contains__("params"):
            values.pop("params")  # 移除公司数据
        return values

    @http.route('/roke/edit_work_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def edit_work_record(self):
        """
        编辑报工记录
        {
            "wr_id": 5,
            "finish_qty": 10,
            "unqualified_qty": 0,
            "work_hours": 1,
            "work_time": "2023-01-01 12:22:00"
        }
        """
        values = http.request.jsonrequest
        wr_id = values.get('wr_id') or False
        if not wr_id:
            return {"state": "error", "msgs": "必须入参报工记录ID"}
        wizard = http.request.env["roke.edit.work.record.wizard"].create(self._get_edit_wr_wizard_val(values))
        wizard.confirm()
        return {"state": "success", "msgs": "编辑成功", "wr_id": wr_id}

    @http.route('/roke/get_collection_category', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_collection_category(self):
        """
        获取采集项类别
        """
        return {"state": "error", "msgs": "未安装数据采集模块，该功能不可用。"}

    @http.route('/roke/get_collection_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_collection_list(self):
        """
        获取采集项列表
        """
        return {"state": "error", "msgs": "未安装数据采集模块，该功能不可用。"}

    @http.route('/roke/create_collection_item', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_collection_item(self):
        """
        创建采集项
        """
        return {"state": "error", "msgs": "未安装数据采集模块，该功能不可用。"}

    @http.route('/roke/get_fields_show_hide', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_fields_show_hide(self):
        """
        获取字段显示隐藏
        """
        ConfigParameter = http.request.env(user=SUPERUSER_ID)['ir.config_parameter']
        show_customer = ConfigParameter.get_param('app_show_customer', default="1")
        app_show_note = ConfigParameter.get_param('app_show_note', default="1")
        app_show_production_order = ConfigParameter.get_param('app_show_production_order', default="1")
        app_show_order_code = ConfigParameter.get_param('app_show_order_code', default="1")
        app_show_plan_date = ConfigParameter.get_param('app_show_plan_date', default="1")
        app_show_task_type = ConfigParameter.get_param('app_show_task_type', default="1")
        app_show_work_center = ConfigParameter.get_param('app_show_work_center', default="1")
        app_show_workshop = ConfigParameter.get_param('app_show_workshop', default="1")
        app_show_team = ConfigParameter.get_param('app_show_team', default="1")
        app_required_production_order = ConfigParameter.get_param('app_required_production_order', default="0")
        app_required_note = ConfigParameter.get_param('app_required_note', default="0")
        app_required_work_hours = ConfigParameter.get_param('app_required_work_hours', default="0")
        unqualified_qty_update_finish = True if ConfigParameter.get_param('unqualified_qty_update_finish') == "1" else False
        return {
            "state": "success",
            "msgs": "获取成功",
            "customer": show_customer != "0",
            "note": app_show_note != "0",
            "production_order": app_show_production_order != "0",
            "order_code": app_show_order_code != "0",
            "plan_date": app_show_plan_date != "0",
            "task_type": app_show_task_type != "0",
            "work_center": app_show_work_center != "0",
            "workshop": app_show_workshop != "0",
            "team": app_show_team != "0",
            "required_production_order": app_required_production_order == "1",
            "required_note": app_required_note == "1",
            "required_work_hours": app_required_work_hours == "1",
            "unqualified_qty_update_finish": unqualified_qty_update_finish
        }

    #创建班组
    @http.route('/roke/create_team',type='json',methods=['POST','OPTIONS'],auth='user',csrf='*')
    def roke_create_team(self):
        """
        创建班组
        :return:
        """
        if not http.request.jsonrequest.get("name"):
            return {"state": "error", "msgs": "必须入参班组名称"}
        new_team = http.request.env['roke.work.team'].create({
            'name': http.request.jsonrequest.get("name")
        })
        return {"state": "success", "msgs": "创建成功", "new_team_id": new_team.id, "new_team_name": "%s（%s）" % (new_team.name, new_team.code)}

    # 创建人员
    @http.route('/roke/create_employee', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_employee(self):
        """
        创建人员
        :return:
        """
        if not http.request.jsonrequest.get("name"):
            return {"state": "error", "msgs": "必须入参人员名称"}
        new_employee = http.request.env["roke.employee"].create({
            "name": http.request.jsonrequest.get("name"),
            "job_number": http.request.jsonrequest.get("job_number") or "",
            "team_id": http.request.jsonrequest.get("team_id") or False,
        })
        http.request.env.user.write({"employee_ids": [(4, new_employee.id)]})
        return {"state": "success", "msgs": "创建成功", "new_employee_id": new_employee.id, "new_employee_name": "%s（%s）" % (new_employee.name, new_employee.code)}

    # 创建产品
    @http.route('/roke/create_product', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def create_product(self):
        """
        创建产品
        :return:
        """
        if not http.request.jsonrequest.get("name"):
            return {"state": "error", "msgs": "必须入参产品名称"}
        new_product = http.request.env["roke.product"].create({
            "name": http.request.jsonrequest.get("name"),
            "unit_price": http.request.jsonrequest.get("unit_price")
        })
        return {"state": "success", "msgs": "创建成功", "new_product_id": new_product.id, "new_product_name": new_product.display_name}

    @http.route('/roke/get_process_work_center', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_process_work_center(self):
        """
        获取工序可选工作中心
        """
        return {"state": "error", "msgs": "请联系管理员安装【融科MES 工作中心设置】模块。"}

    @http.route('/roke/get_product_state', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_product_state(self):
        """
        产品状态
        """
        records = http.request.env["roke.product.state"].search([("process", "=", False)])
        return {
            "state": "success",
            "msgs": "获取成功",
            "datas": [{"id": i.id, "name": i.name} for i in records]
        }

    @http.route('/roke/get_workshop_inspect', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_workshop_inspect(self):
        """
        校验车间检查，避免未安装车间检查接口404
        """
        return {
            "state": "success",
            "msgs": "请求成功",
            "need_inspect": False,
            "inspect_list": []
        }

    @http.route('/roke/submit_workshop_inspect', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def submit_workshop_inspect(self):
        """
        提交车间检查，避免未安装车间检查接口404
        """
        return {
            "state": "error",
            "msgs": "未安装车间检查模块，不需要执行此操作"
        }

    @http.route('/roke/wo_dispatch', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def wo_dispatch(self):
        """
        工单指派
        """
        wo_ids = http.request.jsonrequest.get("wo_ids")
        employee_ids = http.request.jsonrequest.get("employee_ids") or []
        team_id = http.request.jsonrequest.get("team_id") or False
        work_center_id = http.request.jsonrequest.get("work_center_id") or False
        plan_date = http.request.jsonrequest.get("plan_date", False)
        if not wo_ids:
            return {"state": "error", "msgs": "未选择工单"}
        wos = http.request.env["roke.work.order"].browse(wo_ids).filtered(lambda wo: wo.state != "已完工")
        if not wos:
            return {"state": "error", "msgs": "所选工单已全部完工，禁止派工"}
        write_dict = {
            "employee_ids": [(6, 0, employee_ids)] if employee_ids else [(6, 0, [])],
            "team_id": team_id if team_id else False,
            "work_center_id": work_center_id if work_center_id else False,
            "dispatch_time": fields.Datetime.now() if any([employee_ids, team_id, work_center_id, plan_date]) else False
        }
        if plan_date:
            write_dict["plan_date"] = plan_date
        wos.write(write_dict)
        return {
            "state": "success",
            "msgs": "完成派工"
        }

    @http.route('/roke/get_wo_assigned_record', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False, cors='*')
    def get_wo_assigned_record(self):
        """
        获取工单指派列表
        """
        wo_id = http.request.jsonrequest.get("wo_id")
        assigned_records = http.request.env["roke.assigned.wo.record"].search([("work_order_id", "=", wo_id)])
        wo_id = http.request.env["roke.work.order"].search([("id", "=", wo_id)])
        assigned_list = []
        for assigned_record in assigned_records:
            assigned_list.append({
                "assigned_time": (assigned_record.create_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M'),
                "team": assigned_record.team_id.name or "",
                "work_center": assigned_record.work_center_id.name or "",
                "employees": "、".join(assigned_record.employee_ids.mapped("name")),
                "plan_date": (wo_id.plan_date + datetime.timedelta(hours=8)).strftime('%Y-%m-%d %H:%M')
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "assigned_list": assigned_list
        }

    @http.route('/roke/team_member_registration', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def team_member_registration(self):
        """
		班组人员注册
		"""
        team_id = http.request.jsonrequest.get("team_id")
        employee_name = http.request.jsonrequest.get("employee_name")
        phone = http.request.jsonrequest.get("phone", False)
        image = http.request.jsonrequest.get("image", False)
        if not team_id:
            return {
                "state": "error",
                "mags": "未获取到班组信息"
            }
        if not employee_name:
            return {
                "state": "error",
                "mage": "未获取到姓名"
            }
        roke_employee_obj = http.request.env["roke.employee"]
        employee_id = roke_employee_obj.sudo().create({
            'name': employee_name,
            'phone': phone,
            'team_id': team_id,
            'image_1920': image
        })
        return {
            "state": "success",
            "msgs": "创建成功"
        }

    @http.route('/roke/get_team_employee_info', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def get_team_employee_info(self):
        """
		班组人员注册
		"""
        phone = http.request.jsonrequest.get("phone", False)
        if not phone:
            return {
                "state": "error",
                "mage": "未获取到手机号,请输入手机号!"
            }
        else:
            roke_employee_obj = http.request.env["roke.employee"]
            employee_record = roke_employee_obj.sudo().search(
                [('phone', '=', phone), ('active', '=', True), ('team_id', '!=', False)], limit=1)
            if not employee_record:
                return {
                    "state": "error",
                    "mage": "无法识别该用户!禁止访问!"
                }
            employee_info = {
                'employee_name': employee_record.name,
                'employee_id': employee_record.id
            }
        return {
            "state": "success",
            "msgs": "获取成功",
            "info": employee_info
        }

    @http.route('/roke/transfer_work_order', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def transfer_work_order(self):
        """
        工单转移
        """
        transfer_qty = http.request.jsonrequest.get("transfer_qty")
        team_id = http.request.jsonrequest.get("team_id")
        employee_ids = http.request.jsonrequest.get("employee_ids")
        work_center_id = http.request.jsonrequest.get("work_center_id")
        origin_wo_id = http.request.jsonrequest.get("origin_wo_id")
        # 确保 employee_ids 是一个列表
        if isinstance(employee_ids, int):
            employee_ids = [employee_ids]
        origin_wo = http.request.env['roke.work.order'].browse(int(origin_wo_id))
        allow_transfer_qty = origin_wo.plan_qty - origin_wo.finish_qty
        if origin_wo.planned_start_time > origin_wo.plan_date:
            return {"state": "error", "msgs": "工单计划开始时间不得超过计划完成时间。"}
        if allow_transfer_qty <= 0:
            return {"state": "error", "msgs": "无可转移数量。"}
        if transfer_qty > allow_transfer_qty:
            return {"state": "error", "msgs": "转移工单数量大于允许转移数量，请调整转移工单数量。"}
        # 创建转移工单向导
        try:
            transfer_wizard = http.request.env['roke.transfer.work.order.wizard'].create({
                "origin_wo_id": origin_wo_id,
                "allow_transfer_qty": allow_transfer_qty,
                "transfer_qty": transfer_qty,
                "team_id": team_id,
                "employee_ids": [(6, 0, employee_ids)],
                "work_center_id": work_center_id,
            })
        except Exception as e:
            if 'roke_work_order_start_finish_constraint' in str(e):
                return {"state": "error", "msgs": "工单计划开始时间不得超过计划完成时间。"}
            else:
                return {"state": "error", "msgs": str(e)}
        transfer_wizard.confirm()
        return {"state": "success", "msg": "工单转移成功"}

    # 以下是工单的开工完工操作，因为这几个接口后面可能会有更多的操作，所以拆成了单个接口
    @http.route('/roke/work_order_start', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def work_order_start_api(self):
        """
        工单开工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        work_center_ids = http.request.jsonrequest.get("work_center_ids", [])
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        work_order = http.request.env['roke.work.order'].browse(int(work_order_id))
        if work_center_ids:
            work_centers = http.request.env['roke.work.center'].search([('id', 'in', work_center_ids)])
            work_order_ids = http.request.env['roke.work.order'].search([("id", "!=", work_order_id),
                                                                         ('work_center_ids', 'in', work_center_ids)])
            for v in work_centers:
                work_order_ids.write({"work_center_ids": [(3, v.id)]})
            work_order.write({"work_center_ids": [(6, 0, work_order.work_center_ids.ids + work_centers.ids)]})
        if work_order.state != "未完工":
            return {"state": "error", "msgs": "当前工单状态：{}，不可进行开工操作。".format(work_order.state)}
        elif not work_order.wo_manual_start:
            return {"state": "error", "msgs": "当前工单不需要手工开工。"}
        elif work_order.wo_start_state and work_order.wo_start_state != "未开工":
            return {"state": "error", "msgs": "当前工单开工状态：{}，不可进行开工操作。".format(work_order.wo_start_state)}
        work_order.work_order_start()
        return {"state": "success", "msg": "工单开工成功"}

    @http.route('/roke/work_order_finish', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def work_order_finish_api(self):
        """
        工单完工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        work_order = http.request.env['roke.work.order'].browse(int(work_order_id))
        if work_order.state != "未完工":
            return {"state": "error", "msgs": "当前工单状态：{}，不可进行完工操作。".format(work_order.state)}
        elif not work_order.wo_manual_finish:
            return {"state": "error", "msgs": "当前工单不需要手工完工。"}
        elif work_order.wo_start_state and work_order.wo_start_state != "已开工":
            return {"state": "error", "msgs": "当前工单开工状态：{}，不可进行完工操作。".format(work_order.wo_start_state)}
        work_order.work_order_start()
        return {"state": "success", "msg": "工单完工成功"}

    @http.route('/roke/cancel_work_order_start', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def cancel_work_order_start_api(self):
        """
        工单取消开工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        work_order = http.request.env['roke.work.order'].browse(int(work_order_id))
        if work_order.record_ids:
            return {"state": "error", "msgs": "当前工单已报工，不可取消开工。"}
        elif work_order.state != "未完工":
            return {"state": "error", "msgs": "当前工单状态：{}，不可取消开工。".format(work_order.state)}
        elif not work_order.wo_manual_start:
            return {"state": "error", "msgs": "当前工单不可手工取消开工。"}
        elif work_order.wo_start_state and work_order.wo_start_state != "已开工":
            return {"state": "error", "msgs": "当前工单开工状态：{}，不可进行取消开工。".format(work_order.wo_start_state)}
        work_order.cancel_work_order_start()
        return {"state": "success", "msg": "工单开工成功"}

    @http.route('/roke/cancel_work_order_finish', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def cancel_work_order_finish_api(self):
        """
        工单取消完工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        work_order = http.request.env['roke.work.order'].browse(int(work_order_id))
        if work_order.state != "已完工":
            return {"state": "error", "msgs": "当前工单状态：{}，不可取消完工。".format(work_order.state)}
        elif not work_order.wo_manual_finish:
            return {"state": "error", "msgs": "当前工单不可手工取消完工。"}
        work_order.cancel_work_order_finish()
        return {"state": "success", "msg": "工单完工成功"}

    @http.route('/roke/make_suspend', type='json', methods=['POST'], auth="user", csrf=False, cors='*')
    def make_suspend(self):
        """
        工单取消完工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        http.request.env['roke.work.order'].browse(int(work_order_id)).make_suspend()
        return {"code": 0, "state": "success", "msg": "工单暂停成功"}

    @http.route('/roke/cancel_make_suspend', type='json', methods=['POST'], auth="user", csrf=False, cors='*')
    def cancel_make_suspend(self):
        """
        工单取消完工
        """
        work_order_id = http.request.jsonrequest.get("work_order_id")
        if not work_order_id:
            return {"state": "error", "msgs": "必须选择工单。"}
        http.request.env['roke.work.order'].browse(int(work_order_id)).cancel_make_suspend()
        return {"code": 0, "state": "success", "msg": "工单取消暂停成功"}
