# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
import math
from urllib import parse
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import timed<PERSON>ta


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class RokeProductDosplayNameSettings(models.Model):
    _name = "roke.pt.display.name.settings"
    _description = "任务显示名称配置项"
    _rec_name = "name"

    name = fields.Char(string="名称", required=True)
    code = fields.Char(string="编码", required=True)


class RokeProductionTask(models.Model):
    _name = "roke.production.task"
    _inherit = ['mail.thread', 'mail.activity.mixin', 'roke.order.print.mixin']
    _description = "生产任务"
    _order = "sequence desc"
    _rec_name = "code"

    code = fields.Char(string="编号", required=True, index=True, tracking=True, copy=False,
                       default=lambda self: self.env['ir.sequence'].next_by_code('roke.production.task.code'))
    state = fields.Selection([("未完工", "未完工"), ("已完工", "已完工"), ("强制完工", "强制完工"), ("暂停", "暂停")],
                             required=True, default="未完工", tracking=True, string="状态")
    priority = fields.Selection([("1级", "1级"), ("2级", "2级"), ("3级", "3级"), ("4级", "4级"), ("5级", "5级")],
                                string="优先级", required=True, default='1级', tracking=True)
    type = fields.Selection([("生产", "生产")], required=True, default="生产", string="类型")
    product_id = fields.Many2one("roke.product", string="成品", required=True, index=True)
    uom_id = fields.Many2one("roke.uom", string="单位", related="product_id.uom_id")
    plan_qty = fields.Float(string="计划数量", digits='SCSL', tracking=True, default=1)
    finish_qty = fields.Float(string="完工数量", digits='SCSL', tracking=True)
    force_finish_qty = fields.Float(string="强制完工数量", digits='SCSL')
    routing_id = fields.Many2one("roke.routing", string="工艺路线", index=True, tracking=True)
    plan_start_date = fields.Date(string="计划开始日期", default=fields.Date.context_today)
    start_date = fields.Date(string="开始日期")
    plan_date = fields.Date(string="计划完成日期", default=fields.Date.context_today)
    finish_date = fields.Date(string="实际完成日期")
    note = fields.Text(string="备注")
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    work_center_id = fields.Many2one("roke.work.center", string="指派工作中心", tracking=True)
    team_id = fields.Many2one("roke.work.team", string="指派班组", tracking=True)
    workshop_id = fields.Many2one("roke.workshop", string="指派车间产线", tracking=True)

    # 订单内容
    order_line_id = fields.Many2one("roke.production.order.line", string="订单明细", ondelete="cascade")
    order_id = fields.Many2one("roke.production.order", string="生产订单", related="order_line_id.order_id", store=True)
    project_code = fields.Char(string="项目号", related="order_id.project_code", store=True)
    customer_id = fields.Many2one("roke.partner", string="客户")

    work_order_ids = fields.One2many("roke.work.order", "task_id", string="工单信息")
    record_ids = fields.One2many("roke.work.record", "pt_id", string="报工记录")
    # 产出物
    result_ids = fields.One2many("roke.production.result", "pt_id", string="产出物")

    allow_create_wo = fields.Boolean(string="允许创建工单", compute="_compute_allow_create_wo", store=True, default=True)

    wo_qty = fields.Integer(string="工单数", compute='_compute_child_wo_qty')
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    department_id = fields.Many2one('roke.department', string='部门')
    # 作业指导图片
    instruction_file_data = fields.Image('作业指导图片', store=True)
    image_128 = fields.Image('作业指导图片', related="instruction_file_data", max_width=256, max_height=256)
    standard_item_ids = fields.One2many("roke.work.standard.item", "task_id", string="作业规范")
    is_create_work = fields.Boolean(string="是否生成工单", default=False)
    is_not_work = fields.Boolean(string="是否无工单", compute="_compute_allow_create_wo", store=True, default=True)
    classes_id = fields.Many2one("roke.classes", related="order_line_id.classes_id", string="预设班次", store=True)
    print_user_id = fields.Many2one('res.users', string="打印人")
    print_time = fields.Datetime(string="打印时间")
    print_count = fields.Integer(string="打印次数", default=0)
    file_ids = fields.Many2many("ir.attachment", "task_attachments_rel", string="附件")
    deadline = fields.Date(string="承诺交期", related="order_id.deadline", store=True, index=True)
    has_child_process = fields.Boolean(string="主子工艺", compute="_compute_has_child_process")
    pass_rate = fields.Float(string="合格率(%)", default=0, digits='SCSL', compute="_compute_qty_and_rate", store=True)
    sum_unqualified_qty = fields.Float(string="不合格数", digits='SCSL', compute="_compute_sum_unqualified_qty", store=True)
    plan_work_hours = fields.Float(string="计划工时", compute="_compute_plan_work_hours", store=True, readonly=False,
                                   help="根据工艺路线的额定工时和任务计划数量计算得出计划工时，支持手动修改。")
    work_hours = fields.Float(string="报工工时", compute="_compute_sum_work_hours", store=True)
    sequence  = fields.Float(string="序号", compute="_compute_sequence", store=True)
    parent_id = fields.Many2one("roke.production.task", string="上级任务")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    @api.depends('parent_id')
    def _compute_sequence(self):
        for item in self:
            if item.parent_id:
                item.sequence = item.parent_id.id - 0.5
            else:
                item.sequence = item.id

    # 生产任务不合格数计算
    @api.depends('record_ids', 'record_ids.unqualified_qty')
    def _compute_sum_unqualified_qty(self):
        for record in self:
            record.sum_unqualified_qty = sum(record.record_ids.mapped("unqualified_qty"))

    @api.depends('routing_id')
    def _compute_plan_work_hours(self):
        """
        根据工艺额定工时计算报工计划工时
        """
        for record in self:
            rated_working_hours = sum(record.routing_id.line_ids.mapped("rated_working_hours"))
            record.plan_work_hours = record.plan_qty * rated_working_hours

    @api.onchange("priority")
    def _onchange_priority_work_order_ids(self):
        for v in self:
            for order in v.work_order_ids:
                order.priority = v.priority

    @api.depends('record_ids', 'record_ids.work_hours')
    def _compute_sum_work_hours(self):
        """
        计算报工工时
        """
        for record in self:
            record.work_hours = sum(record.record_ids.mapped("work_hours"))

    # 生产任务合格率计算
    @api.depends('record_ids', 'record_ids.finish_qty', 'record_ids.unqualified_qty', 'plan_qty', 'work_order_ids',
                 'work_order_ids.finish_qty', 'work_order_ids.unqualified_qty')
    def _compute_qty_and_rate(self):
        for record in self:
            record.ensure_one()
            # 生产任务合格数量
            sum_qualified_qty = record.finish_qty
            pass_rate_type = self.env['ir.config_parameter'].sudo().get_param('pass.rate.type',
                                                                              default="合格数量/(合格数量+不合格数量)")
            if record.state != "未完工":
                if pass_rate_type == "合格数量/(合格数量+不合格数量)":
                    total_qty = sum_qualified_qty + record.sum_unqualified_qty
                    if total_qty > 0:
                        record.pass_rate = (sum_qualified_qty / total_qty) * 100
                    else:
                        record.pass_rate = 0
                elif pass_rate_type == "合格数量/计划数量":
                    if record.plan_qty > 0:
                        record.pass_rate = (sum_qualified_qty / record.plan_qty) * 100
                    else:
                        record.pass_rate = 0
                else:
                    record.pass_rate = 0
            else:
                if pass_rate_type == "报工合格数量/(报工合格数量+报工不合格数量)":
                    total_qty = sum_qualified_qty + record.sum_unqualified_qty
                    if total_qty > 0:
                        record.pass_rate = (sum_qualified_qty / total_qty) * 100
                    else:
                        record.pass_rate = 0
                else:
                    record.pass_rate = 0

    @api.onchange("routing_id")
    def _compute_has_child_process(self):
        for record in self:
            if self.routing_id.line_ids.child_process_ids:
                record.has_child_process = True
            else:
                record.has_child_process = False

    @api.onchange('plan_start_date','plan_date')
    def onchange_start_end_time(self):
        if not self.plan_start_date:
            self.plan_start_date = fields.Datetime.now()
        if not self.plan_date:
            self.plan_date = fields.Datetime.now()

        if self.plan_start_date and self.plan_date and self.plan_start_date > self.plan_date:
            return {
                'warning': {'message': '任务开始时间不能大于任务结束时间'},
                'value': {'plan_date': False}
            }
        if self.work_order_ids and self.plan_start_date and self.plan_date:
            date_num = (self.plan_date - self.plan_start_date).days
            work_order_num = len(self.work_order_ids)
            if date_num == 0:
                for work_order in self.work_order_ids:
                    work_order.planned_start_time = self.plan_start_date
                    work_order.plan_date = self.plan_start_date
            else:
                num = work_order_num // date_num
                if num < 1:
                    num = 1
                day = 0
                circulation_num = 1
                for work_order in self.work_order_ids:
                    date = self.plan_start_date + timedelta(days=day)
                    work_order.planned_start_time = date
                    work_order.plan_date = date
                    if circulation_num == num:
                        circulation_num = 1
                        if day + 1 <= date_num:
                            day += 1
                    else:
                        circulation_num += 1

    @api.onchange("plan_qty")
    def _onchange_plan_qty(self):
        if self.plan_qty <= 0:
            return {"warning": {"title": "计划数量错误", "message": "计划数不能小于或等于0"},
                    "value": {"plan_qty": 1}}

    def _compute_child_wo_qty(self):
        """
        计算工单数
        :return:
        """
        for record in self:
            record.wo_qty = self.sudo().env["roke.work.order"].search_count([('task_id', '=', record.id)])

    @api.depends("work_order_ids")
    def _compute_allow_create_wo(self):
        """
        已生成生产工单禁止重复生成
        :return:
        """
        for record in self:
            if not record.work_order_ids or record.work_order_ids.filtered(lambda l: not l.from_routing):
                # 无系统从工艺路线创建的工单，则允许编辑
                record.allow_create_wo = True
            else:
                record.allow_create_wo = False
            # 是否存在工单
            if record.work_order_ids:
                record.is_not_work = False
            else:
                record.is_not_work = True

    @api.onchange("product_id")
    def _onchange_product_id(self):
        # if self.order_id:
        return {"value": {"routing_id": self.product_id.routing_id}}

    def _repair_process_get_wo_values(self, process):
        """
        工序返修获取工单值
        :param process:
        :return:
        """
        self.ensure_one()
        SequenceObj = self.env['ir.sequence']
        return {
            "task_id": self.id,
            "code": SequenceObj.next_by_code('roke.work.order.code'),
            "priority": self.priority,
            "product_id": self.product_id.id,
            "plan_qty": self.plan_qty,
            "process_id": process.id,
            "plan_date": self.plan_date,
            "note": self.note,
            "repair_line_id": self.repair_line_id.id,
            "customer_id": self.customer_id.id,
            "dispatch_time": fields.Datetime.now(),
            "type": self.type,
            "from_routing": True,
            "employee_ids": process.default_employee_ids,
        }

    def multi_add_process_action(self):
        return {
            'name': '批量添加工序',
            'type': 'ir.actions.act_window',
            'res_model': 'roke.pt.multi.add.process.wizard',
            'view_mode': 'form',
            'target': 'new'
        }

    def force_finish(self, origin='self'):
        """
        强制完工
        :param origin: 调用来源
        :return:
        """
        self.ensure_one()
        po, wo, force_qty = False, False, 0
        if origin == "self":
            po, wo = True, True
            # 强制完工处理
            force_finish_type = self.env['ir.config_parameter'].sudo().get_param('force.finish.type', default="only_finish")
            #  根据强制完工配置执行修改完成数
            min_finish, max_finish = 0, 0
            wo_finish_list = self.work_order_ids.filtered(lambda wo: wo.finish_qty > 0).mapped("finish_qty")
            if wo_finish_list:
                min_finish, max_finish = min(wo_finish_list), max(wo_finish_list)
            if force_finish_type == "produce_min" and min_finish > self.finish_qty:
                force_qty = min_finish - self.finish_qty
            elif force_finish_type == "produce_max" and max_finish > self.finish_qty:
                force_qty = max_finish - self.finish_qty
            elif force_finish_type == "produce_plan" and self.plan_qty > self.finish_qty:
                force_qty = self.plan_qty - self.finish_qty
        if origin == "po":
            po = True
        elif origin == "wo":
            wo = True
        write_dict = {"state": "强制完工", "finish_date": fields.Date.context_today(self)}
        if force_qty:
            self.finish_report_task(force_qty)  # 完工数调整
            write_dict["force_finish_qty"] = force_qty  # 记录强制完工数量
        self.write(write_dict)
        self.env.cr.commit()
        if po:
            # 生产订单发起的完工，要写入工单
            self.work_order_ids.filtered(lambda wo: wo.state == "未完工").force_finish(origin="pt")
        self.env.cr.commit()
        if wo:
            # 工单发起的往，要同步到生产订单
            if not self.order_id.line_ids.task_id.filtered(lambda pt: pt.state == "未完工"):
                self.order_id.force_finish(origin="pt")

    def cancel_force_finish(self, origin='self'):
        """
        取消强制完工
        :param origin: 调用来源
        :return:
        """
        self.ensure_one()
        po, wo = False, False
        if origin == "self":
            po, wo = True, True
        if origin == "po":
            po = True
        elif origin == "wo":
            wo = True
        self.write({"state": "未完工", "finish_date": False})
        if po:
            self.work_order_ids.filtered(lambda wo: wo.state == "强制完工").cancel_force_finish(origin="pt")
        if wo and self.order_id.state == "强制完工":
            self.order_id.cancel_force_finish(origin="pt")
        # 修改生产任务和生产订单完成数
        if self.force_finish_qty:
            force_qty = self.force_finish_qty
            self.write({"force_finish_qty": 0, "finish_qty": self.finish_qty - force_qty})  # 记录强制完工数量
            if self.order_line_id:
                self.order_line_id.write({"finish_qty": self.finish_qty - force_qty})

    def make_suspend(self, origin='self'):
        """
        暂停
        :return:
        """
        po, wo = False, False
        if origin == "self" or origin == "child":
            po, wo = True, True
        if origin == "po":
            po = True
        elif origin == "wo":
            wo = True
        wos = self.work_order_ids.filtered(lambda wo: wo.state == "未完工")
        self.write({"state": "暂停"})
        self.env.cr.commit()
        if po:
            self.work_order_ids.filtered(lambda wo: wo.state == "未完工").make_suspend(origin="pt")
        if wo:
            if not self.order_id.line_ids.task_id.filtered(lambda pt: pt.state == "未完工"):
                self.order_id.make_suspend(origin="pt")

        if origin == "self":
            try:
                childs = self._get_child_pt(self)
            except:
                childs=[]
            create_dict = {'pt': [(6, 0, self.ids+childs)]}
            child_tasks = self.browse(childs)
            child_wos = child_tasks.work_order_ids
            if wos:
                create_dict.update({
                    'wo': [(6, 0, wos.ids+child_wos.ids)]
                })
            if not self.order_id.line_ids.task_id.filtered(lambda pt: pt.state == "未完工"):
                create_dict.update({
                    'po': self.order_id.id
                })
            res = self.env['roke.suspend.note.wizard'].create(create_dict)
            return {
                'name': '暂停原因',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'new',
                'res_id': res.id,
                'res_model': 'roke.suspend.note.wizard'
            }

    def cancel_make_suspend(self, origin='self'):
        """
        取消暂停
        :return:
        """
        po, wo = False, False
        if origin == "self" or origin == "child":
            po, wo = True, True
        if origin == "po":
            po = True
        elif origin == "wo":
            wo = True
        self.write({"state": "未完工"})
        if po:
            self.work_order_ids.filtered(lambda wo: wo.state == "暂停").cancel_make_suspend(origin="pt")
        if wo and self.order_id.state == "暂停":
            self.order_id.cancel_make_suspend(origin="pt")

    def _get_new_work_order_data(self, routing_line, product, plan_qty, task_type):
        """
        onchang获取工单数据
        :return:
        """
        rounding_type = self.env['ir.config_parameter'].sudo().get_param('e_bom.material.demand.rounding', default="精确计算")
        plan_qty = plan_qty * routing_line.multiple
        if rounding_type == "向上取整":
            plan_qty = math.ceil(plan_qty)
        elif rounding_type == "向下取整":
            plan_qty = int(plan_qty)
        return {
            "product_id": product.id,
            "process_id": routing_line.process_id.id,
            "routing_line_id": routing_line.id,
            "sequence": routing_line.sequence,
            "plan_qty": plan_qty,
            "note": self.note,
            "type": task_type,
            "employee_ids": routing_line.process_id.default_employee_ids,
            "workshop_id": self.workshop_id.id,
            "team_id": self.team_id.id,
        }

    def get_create_wo_routing_lines(self, routing):
        """
        获取生成工单的工艺明细
        :return:
        """
        return routing.line_ids

    @api.onchange("routing_id", "product_id", "plan_qty", "type")
    def _onchange_routing_id(self):
        if self.has_child_process:
            return {"value": {
                "work_order_ids": [(6, 0, [])]
            }}
        if self.product_id and self.routing_id and self.plan_qty:
            routing = self.routing_id
            product = self.product_id
            plan_qty = self.plan_qty
            task_type = self.type
            routing_lines = self.get_create_wo_routing_lines(routing)
            work_orders = []
            for wo in self.work_order_ids:
                work_orders.append((2, wo.id))

            # 通过生产任务计划开始、结束时间来平均控制每个生产工单的计划开始、结束时间
            date_num = (self.plan_date - self.plan_start_date).days
            work_order_num = len(routing_lines)
            if date_num == 0:
                for routing_line in routing_lines:
                    date = self.plan_start_date
                    work_order_data = self._get_new_work_order_data(routing_line, product, plan_qty, task_type)
                    work_order_data['planned_start_time'] = date
                    work_order_data['plan_date'] = date
                    work_orders.append(
                        (0, 0, work_order_data))
            else:
                num = work_order_num // date_num
                if num < 1:
                    num = 1
                day = 0
                circulation_num = 1
                for routing_line in routing_lines:
                    date = self.plan_start_date + timedelta(days=day)
                    if circulation_num == num:
                        circulation_num = 1
                        if day + 1 <= date_num:
                            day += 1
                    else:
                        circulation_num += 1
                    work_order_data = self._get_new_work_order_data(routing_line, product, plan_qty, task_type)
                    work_order_data['planned_start_time'] = date
                    work_order_data['plan_date'] = date
                    work_orders.append(
                        (0, 0, work_order_data))

            return {"value": {
                "routing_id": routing.id,
                "product_id": product.id,
                "plan_qty": plan_qty,
                "type": task_type,
                "work_order_ids": work_orders
            }}

    def _create_work_order_get_values(self, task, routing_line):
        """
        任务生成工单获取工单数据
        :return:
        """
        SequenceObj = self.env['ir.sequence']
        rounding_type = self.env['ir.config_parameter'].sudo().get_param('e_bom.material.demand.rounding', default="精确计算")
        plan_qty = task.plan_qty * routing_line.multiple
        if rounding_type == "向上取整":
            plan_qty = math.ceil(plan_qty)
        elif rounding_type == "向下取整":
            plan_qty = int(plan_qty)
        return {
            "task_id": task.id,
            "code": SequenceObj.next_by_code('roke.work.order.code'),
            "priority": task.priority,
            "product_id": task.product_id.id,
            "plan_qty": plan_qty,
            "process_id": routing_line.process_id.id,
            "routing_line_id": routing_line.id,
            "sequence": routing_line.sequence,
            "main_wo_sequence": routing_line.sequence,
            "plan_date": task.plan_date,
            "note": task.note,
            "customer_id": task.customer_id.id,
            "dispatch_time": fields.Datetime.now(),
            "type": task.type,
            "from_routing": True,
            "employee_ids": routing_line.process_id.default_employee_ids,
            "workshop_id": task.workshop_id.id,
            "team_id": task.team_id.id,
        }

    def _create_child_wo_get_values(self, task, child_process, main_wo):
        """
        任务生成子工单获取数据
        :return:
        """
        SequenceObj = self.env['ir.sequence']
        plan_qty = task.plan_qty
        return {
            "wo_child_type": "child",
            "task_id": task.id,
            "code": SequenceObj.next_by_code('roke.work.order.code'),
            "priority": task.priority,
            "product_id": task.product_id.id,
            "plan_qty": plan_qty,
            "process_id": child_process.child_process_id.id,
            "sequence": child_process.sequence,
            "main_wo_id": main_wo.id,
            "main_wo_sequence": main_wo.sequence,
            "plan_date": task.plan_date,
            "note": task.note,
            "customer_id": task.customer_id.id,
            "dispatch_time": fields.Datetime.now(),
            "type": task.type,
            "from_routing": True,
            "employee_ids": child_process.child_process_id.default_employee_ids,
            "workshop_id": task.workshop_id.id,
            "team_id": task.team_id.id,
        }

    @api.model
    def create(self, vals):
        if vals.get("code") in ('/', None, False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.production.task.code')
        if vals.get('work_order_ids'):
            vals["is_create_work"] = True
        res = super(RokeProductionTask, self).create(vals)
        for record in res:
            if record.work_order_ids:
                record.work_order_ids.write({"priority": record.priority})
        return res

    def create_work_order(self):
        """
        生成工单
        :return:
        """
        WorkOrderObj = self.env["roke.work.order"]
        work_order_ids = []
        for task in self:
            work_order_list = []
            routing_lines = task.get_create_wo_routing_lines(task.routing_id)
            if routing_lines:
                for routing_line in routing_lines:
                    # 多组织创建工单 不加sudo无法生成 --jht
                    work_order = WorkOrderObj.sudo().create(task._create_work_order_get_values(task, routing_line))
                    work_order_ids.append(work_order.id)
                    work_order_list.append(work_order)
                    # 创建子工单
                    for child_process in routing_line.child_process_ids:
                        child_wo = WorkOrderObj.sudo().create(task._create_child_wo_get_values(task, child_process, work_order))
                        work_order_ids.append(child_wo.id)
                        work_order_list.append(child_wo)
            else:
                raise ValidationError("该工单还未设置工艺路线，请返回设置")
            # 通过生产任务计划开始、结束时间来平均控制每个生产工单的计划开始、结束时间
            date_num = (task.plan_date - task.plan_start_date).days
            work_order_num = len(work_order_list)
            if date_num == 0:
                for work_order in work_order_list:
                    work_order.planned_start_time = task.plan_start_date
                    work_order.plan_date = task.plan_start_date
            else:
                num = work_order_num // date_num
                if num < 1:
                    num = 1
                day = 0
                circulation_num = 1
                for work_order in work_order_list:
                    date = task.plan_start_date + timedelta(days=day)
                    work_order.planned_start_time = date
                    work_order.plan_date = date
                    if circulation_num == num:
                        circulation_num = 1
                        if day + 1 <= date_num:
                            day += 1
                    else:
                        circulation_num += 1

        self.is_create_work = True
        action = self.env["ir.actions.actions"]._for_xml_id("roke_mes_production.view_roke_work_order_action")
        action["domain"] = [('task_id', 'in', self.ids)]
        action["target"] = 'current'
        action["views"] = [
            (self.env.ref('roke_mes_production.view_roke_work_order_tree').id, 'tree'),
            (self.env.ref('roke_mes_production.view_roke_work_order_form').id, 'form')
        ]
        return action

    def app_create_work_order(self):
        """
        app生成工单
        :return:
        """
        self.create_work_order()

    def process_finish_qty(self):
        """
        计算完工数
        :return:
        """
        finish_qty = self.finish_qty
        # 取最后一道工序,且不为质检工序
        if self.work_order_ids:
            max_sequence = max(self.work_order_ids.filtered(lambda wo: wo.type != '质检').mapped("sequence"))
            finish_qty = sum(self.work_order_ids.filtered(lambda wo: wo.sequence == max_sequence and wo.type != '质检').mapped("finish_qty"))
        return finish_qty

    def _check_pt_finish(self):
        """
        校验任务是否完工
        :return:
        """
        if not self.work_order_ids.filtered(lambda wo: wo.state == "未完工"):
            return True
        else:
            return False

    def finish_report_task(self, qty):
        """
        生产任务完成
        :param qty:
        :return:
        """
        # 修改当前完成数
        # finish_qty = round(min(self.work_order_ids.mapped("finish_qty")), _get_pd(self.env))
        finish_qty = self.process_finish_qty()
        write_dict = {"finish_qty": finish_qty}
        done = False
        if self._check_pt_finish():
            write_dict["state"] = "已完工"
            write_dict["finish_date"] = fields.Date.context_today(self)
            done = True
        self.write(write_dict)
        if self.order_line_id and done:
            # 获取同一生产订单明细下的未完工任务
            if not self.order_id.line_ids.filtered(lambda l: l.state == "未完工"):
                self.order_id.write({"state": "已完工", "finish_date": fields.Date.context_today(self)})

    def withdraw_report_task(self):
        """
        生产任务报工撤回
        :param qty:
        :return:
        """
        # 修改当前完成数
        finish_qty = self.process_finish_qty()
        write_dict = {"finish_qty": finish_qty}
        if finish_qty < self.plan_qty:
            write_dict["state"] = "未完工"
        else:
            write_dict["state"] = "已完工"
        self.write(write_dict)
        if self.order_line_id and write_dict.get("state") == "未完工":
            self.order_id.write({"state": "未完工"})

    def unlink(self):
        if self.record_ids:
            raise ValidationError("当前任务已报工，禁止删除！请确认工单尚未执行后，先撤回报工再删除生产任务。")
        orders = self.order_id
        res = super(RokeProductionTask, self).unlink()
        orders._compute_allow_create_task()  # 更新生产订单状态
        return res

    def write(self, vals):
        work_dict={}
        if vals.get("plan_qty",False):
            # 判断改动的字段
            work_dict["plan_qty"] = vals.get("plan_qty")
        if vals.get("customer_id", False):
            work_dict["customer_id"] = vals.get("customer_id")
        # if vals.get("plan_date", False):
        #     work_dict["plan_date"] = vals.get("plan_date")
        if vals.get("priority", False):
            work_dict["priority"] = vals.get("priority")
        if not self._context.get("add_plan_qty"):
            for record in self:
                if record.work_order_ids.filtered(lambda wo: wo.state not in ["暂停", "未完工"]) and work_dict:
                    raise ValidationError("当前任务所生成工单已报工，禁止编辑！")
                if record.work_order_ids:
                    record.work_order_ids.write(work_dict)
        return super(RokeProductionTask, self).write(vals)

    def pt_action_work_order(self):
        """
        查看当前生产任务下的工单
        :return:
        """
        action = self.env["ir.actions.actions"]._for_xml_id("roke_mes_production.view_roke_work_order_action")
        action["domain"] = [('task_id', 'in', self.ids)]
        action["target"] = 'current'
        action["views"] = [
            (self.env.ref('roke_mes_production.view_roke_work_order_tree').id, 'tree'),
            (self.env.ref('roke_mes_production.view_roke_work_order_form').id, 'form')
        ]
        return action

    def get_instruction_file_url(self, file_type=None):
        """
        获取作业知道图片预览地址
        :param file_type: 入参‘base64’或预览地址
        :return:
        """
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachment = self.sudo().env['ir.attachment'].search([
            ("res_model", "=", "roke.production.task"),
            ("res_id", "=", self.id),
            ("res_field", "=", "instruction_file_data")
        ])  # 必须带sudo，原因见odoo-14.0/odoo/addons/base/models/ir_attachment.py 第441行
        if not attachment:
            return False
        if file_type == "base64":
            return [{
                "id": attachment.id,
                "name": attachment.name,
                "type": "base64",
                "data": attachment.datas,
                "is_picture": False,
            }]
        if not attachment.access_token:
            attachment.generate_access_token()
        if attachment.mimetype == "application/pdf":
            # pdf 预览
            content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
            url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
            is_picture = False
        else:
            # 图片 预览
            url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
            is_picture = True if attachment.index_content == 'image' else False
        return [{
            "id": attachment.id,
            "name": attachment.name,
            "type": "url",
            "data": url,
            "is_picture": is_picture,
        }]

    def _pt_name_get(self, pt, pt_display_name_list):
        """
        获取任务显示名称
        """
        name = ""
        i = 0
        for rec in pt_display_name_list:
            extra = getattr(pt, rec)
            if type(extra) != str:
                extra = extra.name
            if extra:
                if i:
                    name += f"【{extra}】"
                else:
                    name += f"{extra}"
            i += 1
        if name:
            return name
        return "%s【%s】" % (pt.code, pt.product_id.name)

    def name_get(self):
        res = []
        pt_display_names = self.env['ir.config_parameter'].sudo().get_param('production.task.display.name.ids', default=[])
        pt_display_name_ids = [int(rec) for rec in pt_display_names if rec.isdigit()]
        pt_display_name_list = [rec.code for rec in self.env['roke.pt.display.name.settings'].browse(pt_display_name_ids)]
        for record in self:
            res.append((record.id, self._pt_name_get(record, pt_display_name_list)))
        return res

    def _get_task_standard_item_vals(self, task, standard_item):
        """
        获取作业规范值
        :return:
        """
        return {
            "task_id": task.id,
            "sequence": standard_item.sequence,
            "title": standard_item.title,
            "name": standard_item.name,
            "image_1920": standard_item.image_1920
        }

    def action_split_task(self):
        """
        拆分任务
        :return:
        """
        self.ensure_one()
        if self.record_ids:
            raise ValidationError("当前任务已报工，禁止拆分！")
        res = self.env['roke.mes.split.task.wizard'].create({
            "origin_task_id": self.id,
            "can_split_qty": self.plan_qty
        })
        return {
            'name': '拆分任务',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.mes.split.task.wizard'
        }

