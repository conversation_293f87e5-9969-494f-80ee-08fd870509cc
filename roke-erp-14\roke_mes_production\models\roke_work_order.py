# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from urllib import parse
from odoo.osv import expression
from datetime import timedelta,date
from odoo.tools.float_utils import float_round
import datetime
import pytz
import math
import logging
_logger = logging.getLogger(__name__)


def float_to_time(hours):
    """ 将小时数转换为时间对象. """
    if hours == 24.0:
        return datetime.time.max.replace(microsecond=0)
    fractional, integral = math.modf(hours)
    return datetime.time(int(integral), int(float_round(60 * fractional, precision_digits=0)), 0)


def _get_pd(env, index="Production"):
    return env["decimal.precision"].precision_get(index)


class RokeWorkOrder(models.Model):
    _name = "roke.work.order"
    _inherit = ['mail.thread', 'mail.activity.mixin', 'roke.order.print.mixin']
    _description = "生产工单"
    _order = "task_id, main_wo_sequence, sequence, code"
    _rec_name = "code"

    equipment_id = fields.Many2one("roke.mes.equipment", string="指派设备",index=True, tracking=True)
    task_id = fields.Many2one("roke.production.task", string="生产任务", index=True, tracking=True, ondelete="cascade")
    code = fields.Char(string="编号", index=True, tracking=True, copy=False)
    state = fields.Selection([("未派工", "未派工"), ("未开工", "未开工"), ("进行中", "进行中"), ("已完工", "已完工"), ("强制完工", "强制完工"), ("暂停", "暂停")],
                             required=False, default="未派工", tracking=True, string="状态")
    last_state = fields.Char(string="上一次状态", default="未派工")
    priority = fields.Selection([("1级", "1级"), ("2级", "2级"), ("3级", "3级"), ("4级", "4级"), ("5级", "5级")],
                                string="优先级", required=True, default='1级', tracking=True)
    type = fields.Selection([("生产", "生产")], required=True, default="生产", tracking=True, string="类型")
    product_id = fields.Many2one("roke.product", string="成品", index=True, ondelete="restrict")
    uom_id = fields.Many2one("roke.uom", string="单位", related="product_id.uom_id")
    plan_qty = fields.Float(string="计划数量", digits='SCSL', tracking=True)
    finish_qty = fields.Float(string="完工数量", digits='SCSL', tracking=True, copy=False)
    unqualified_qty = fields.Float(string="不合格数", digits='SCSL', tracking=True, copy=False)
    work_hours = fields.Float(string="工时", tracking=True, copy=False)
    pass_rate = fields.Float(string="合格率%", compute="_compute_pass_rate", store=True)
    routing_id = fields.Many2one("roke.routing", string="工艺路线", index=True)
    routing_line_id = fields.Many2one("roke.routing.line", string="工艺明细", index=True)
    sequence = fields.Integer(string="序号", default=1)
    process_id = fields.Many2one("roke.process", string="工序", index=True, ondelete="restrict")
    plan_date = fields.Datetime(string="计划完成日期", default=fields.Datetime.now)
    note = fields.Text(string="备注")
    order_id = fields.Many2one("roke.production.order", string="生产订单", related="task_id.order_id", store=True)
    project_code = fields.Char(string="项目号", related="task_id.project_code", store=True)
    customer_id = fields.Many2one(related="task_id.customer_id", string="客户", store=True)
    team_id = fields.Many2one("roke.work.team", string="指派班组", tracking=True)
    employee_ids = fields.Many2many("roke.employee", string="指派人员")
    work_center_id = fields.Many2one("roke.work.center", string="指派工作中心", tracking=True)
    # workshop_id = fields.Many2one("roke.workshop", string="指派车间产线", tracking=True)
    workshop_id = fields.Many2one("roke.workshop", string="指派车间产线", tracking=True, related="task_id.workshop_id",readonly=True,store=True)
    dispatch_time = fields.Datetime(string="派工时间")
    finish_time = fields.Datetime(string="报工时间", copy=False)
    from_routing = fields.Boolean(string="来源于工艺", default=False)
    record_ids = fields.One2many("roke.work.record", "work_order_id", string="报工记录")
    assigned_record_ids = fields.One2many("roke.assigned.wo.record", "work_order_id", string="报工记录")
    work_employee_ids = fields.Many2many("roke.employee", "work_order_work_employee_rel", string="报工人员", compute="_compute_work_employee_ids")
    allow_edit = fields.Boolean(string="允许编辑", compute="_compute_allow_edit", store=True, default=True)
    # 作业指导图片
    instruction_file_data = fields.Image('作业指导图片', related="process_id.instruction_file_data", store=True)
    image_128 = fields.Image('作业指导图片', related="instruction_file_data", max_width=256, max_height=256)
    standard_item_ids = fields.One2many("roke.work.standard.item", "wo_id", string="作业规范")
    # 工单开/完工
    wo_start_state = fields.Selection([("未开工", "未开工"), ("已开工", "已开工")], string="开工状态")
    wo_start_time = fields.Datetime(string="开工时间")
    wo_finish_time = fields.Datetime(string="完工时间")
    wo_duration = fields.Float(string="工单耗时", compute="_compute_wo_duration", store=True)
    wo_manual_start = fields.Boolean(string="手工开工", compute="_compute_wo_manual_setting")
    wo_manual_finish = fields.Boolean(string="手工完工", compute="_compute_wo_manual_setting")
    # 产出物
    result_ids = fields.One2many("roke.production.result", "wo_id", string="产出物")
    is_entrust = fields.Boolean(string='是否委外', tracking=True)
    active = fields.Boolean(string="有效的", default=True, tracking=True)
    department_id = fields.Many2one('roke.department', string='部门')
    company_id = fields.Many2one('res.company', string='公司', default=lambda self: self.env.company)
    rated_working_hours = fields.Float(string="额定工时", compute="_compute_rated_working_hours")
    temp_content = fields.Text(string="临时报工数据")
    deadline = fields.Date(string="承诺交期", related="order_id.deadline", store=True, index=True)
    # 主子工单
    wo_child_type = fields.Selection([("main", "主工单"), ("child", "子工单")], string="主子工单分类", default="main")
    main_wo_id = fields.Many2one("roke.work.order", string="主工单")
    main_wo_sequence = fields.Integer(string="主工单序号")
    child_wo_ids = fields.One2many("roke.work.order", "main_wo_id",  string="子工单")
    # 末道工序
    last = fields.Boolean(string="末道工序", compute="_compute_last")
    # 生产排产
    # planned_start_time = fields.Datetime(string="计划开始时间", compute="_compute_start_time",store=True, index=True)
    planned_start_time = fields.Datetime(string="计划开始时间", index=True, default=fields.Datetime.now)
    planned_finish_time = fields.Datetime(string="计划完成时间", compute="_compute_finish_time",store=True, index=True)

    work_center_ids = fields.Many2many("roke.work.center", string="工作中心")

    _sql_constraints = [
        ('code_unique', 'UNIQUE(code)', '编号已存在，不可重复。如使用系统默认编号请刷新页面；如使用自定义编号请先删除重复的编号')
    ]

    def action_save(self):
        self.ensure_one()
        if self.state == '未派工':
            if not self.task_id.start_date:
                self.task_id.start_date = fields.Date.context_today(self)
            self.state = '未开工'
            self.dispatch_time = fields.Datetime.now()

    def action_set_employee_ids(self):
        self.ensure_one()
        form_id = self.env.ref("roke_mes_production.view_roke_work_order_set_employee_ids_form").id
        return {
            "name": "派工",
            "type": "ir.actions.act_window",
            "view_type": "form",
            "view_mode": "form",
            "view_id": form_id,
            "views": [(form_id, "form")],
            "res_model": "roke.work.order",
            "target": "new",
            "res_id": self.id,
        }

    @api.onchange("planned_start_time", "plan_date")
    def onchange_start_end_time(self):
        if self.planned_start_time and self.plan_date and self.planned_start_time > self.plan_date:
            return {
                'warning': {'message': '工单计划开始时间不能大于工单计划结束时间'},
                'value': {'plan_date': False}
            }

    def _compute_wo_manual_setting(self):
        """
        判断当前是否允许手工开工或完工
        :return:
        """
        company = self.env.user.company_id
        ConfigParameter = self.sudo().env['ir.config_parameter']
        for record in self:
            record.wo_manual_finish = True if company.complete_basis == "手动完工" else False
            record.wo_manual_start = False if ConfigParameter.get_param(
                'work.order.manual.start', default="0"
            ) == "0" else True

    @api.depends("wo_start_time", "wo_finish_time")
    def _compute_wo_duration(self):
        """
        计算工单工时
        :return:
        """
        for record in self:
            duration = 0
            if record.wo_start_time and record.wo_finish_time:
                duration = (record.wo_finish_time - record.wo_start_time).total_seconds()
            record.wo_duration = round(duration / 3600, 2) if duration > 0 else 0

    def _compute_last(self):
        for record in self:
            last = False
            if record.main_wo_id:
                # 当前为子工单。
                if record.main_wo_id.task_id.work_order_ids.filtered(
                        lambda wo: wo.sequence > record.main_wo_id.sequence
                ) and record.main_wo_id.child_wo_ids.filtered(
                    lambda wo: wo.sequence > record.sequence
                ):
                    last = True
            else:
                # 主工单
                if not record.task_id.work_order_ids.filtered(
                        lambda wo: wo.sequence > record.sequence
                ):
                    last = True
            record.last = last

    @api.depends("finish_qty", "unqualified_qty")
    def _compute_pass_rate(self):
        for record in self:
            if record.finish_qty or record.unqualified_qty:
                record.pass_rate = record.finish_qty / (record.finish_qty + record.unqualified_qty) * 100
            else:
                record.pass_rate = 0

    @api.depends('create_date')
    def _compute_start_time(self):
        for order in self:
            order.planned_start_time = order.create_date

    @api.depends('task_id.plan_date','plan_date')
    def _compute_finish_time(self):
        for order in self:
            if order.task_id.plan_date:
                order.planned_finish_time = order.task_id.plan_date+timedelta(days=1)
            else:
                order.planned_finish_time = date.today() + timedelta(days=1)

    def _compute_rated_working_hours(self):
        for record in self:
            if record.routing_line_id:
                record.rated_working_hours = record.routing_line_id.rated_working_hours
            else:
                record.rated_working_hours = record.process_id.rated_working_hours

    # 修改工序变更作业人员
    @api.onchange("process_id")
    def _onchange_process_id(self):
        self.employee_ids = self.process_id.default_employee_ids

    @api.onchange("workshop_id")
    def _onchange_workshop_id(self):
        if self.workshop_id:
            equipment_id = self.env["roke.mes.equipment"].sudo().search([("workshop_id", "=", self.workshop_id.id)],limit=1)
            return {"value": {"equipment_id": equipment_id and equipment_id.id }}


    def send_work_info(self):
        print('工单指派，消息发送!')

    # 暂时保存，app移动下单工单报工
    def _check_freedom_work(self,finish_qty):
        """
        校验自由报工
        :return:
        """
        allow_qty, default_qty = self._get_wo_allow_qty()
        if round(finish_qty, _get_pd(self.env)) > allow_qty:
            raise ValidationError(
                "工单%s 当前报工数【%s】大于可报数量【%s】，禁止报工。（如想取消该限制，请联系系统管理员将“自由报工”选项设置为“允许”）" % (
                    self.display_name, str(finish_qty), str(allow_qty)
                )
            )

    # 在工单表
    def app_confirm(self,finish_qty,unqualified_qty,work_hours,team_id,employee_id,work_center_id=None,classes_id=None,device_info=None,note=None):
        """
        app生成报工记录（单人报工）
        :return:
        """
        if self.state in ["暂停", "强制完工"]:
            raise ValidationError("工单（{}）状态是{}不允许报工".format(self.code, self.state))
        if not finish_qty and not unqualified_qty and not work_hours:
            raise ValidationError("完成数，不合格数，工时不能同时为空或0")
        employee = self.env['roke.employee'].search([('id','=',int(employee_id))])
        team = self.env['roke.work.team'].search([('id','=',int(team_id))])
        work_center, classes = None, None
        if work_center_id:
            work_center = self.env['roke.work.center'].search([('id','=',int(work_center_id))])
        if classes_id:
            classes = self.env['roke.classes'].search([('id','=',int(classes_id))])
        teams = team or employee.team_id
        restrict_date_team = teams.filtered(lambda team: team.restrict_work_date)
        if restrict_date_team and not self.env.user.has_group("base.group_system"):
            today = fields.Date.context_today(self)
            last_days = min(restrict_date_team.mapped("last_days"))
            after_days = max(restrict_date_team.mapped("after_days"))
            last_time = datetime.datetime.combine(today - timedelta(days=last_days), float_to_time(max(restrict_date_team.mapped("last_time"))))
            after_time = datetime.datetime.combine(today + timedelta(days=after_days), float_to_time(max(restrict_date_team.mapped("after_time"))))
            work_time = self.work_time + timedelta(hours=8)
            if work_time < last_time or work_time > after_time:
                raise ValidationError("报工时间错误，当前人员可报工时间范围：%s 到 %s。" % (str(last_time), str(after_time)))
        # 校验在此之前的工序报工数
        if self.env.user.company_id.freedom_work != "允许":
            self._check_freedom_work(finish_qty)
        # 创建报工记录
        employee_ids, allot_list = employee.ids, [(0, 0, {'employee_id': employee.id,'weighted': 1,'proportion': 100})]
        new_record = self.env["roke.work.record"].create({
            "code": self.env['ir.sequence'].next_by_code('roke.work.record.code'),
            "work_order_id": self.id,
            "product_id": self.product_id.id,
            "uom_id": self.uom_id.id,
            "process_id": self.process_id.id,
            "team_id": team.id,
            "employee_ids": employee_ids,
            "work_center_id": work_center.id if work_center else False,
            "classes_id": classes.id if classes else False,
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "work_hours": work_hours,
            "customer_id": self.customer_id.id,
            "work_time":  datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "device_info": device_info,
            "note": note,
            "allot_ids": allot_list
        })
        # 工单完工
        self.finish_report_work_order(finish_qty, unqualified_qty, work_hours, finish_time=fields.Datetime.now(), wr_id=new_record.id)

    @api.depends("record_ids")
    def _compute_allow_edit(self):
        for record in self:
            record.allow_edit = len(record.record_ids) == 0

    def _compute_work_employee_ids(self):
        for record in self:
            e_ids = record.record_ids.employee_ids.ids
            record.work_employee_ids = [(6, 0, list(set(e_ids)))]

    @api.onchange("product_id")
    def _onchange_product_id(self):
        if self.order_id:
            return {"value": {"routing_id": self.product_id.routing_id}}

    @api.onchange("task_id")
    def _onchange_task_id(self):
        if self.task_id:
            return {"value": {"plan_qty": self.task_id.plan_qty, "product_id": self.task_id.product_id.id}}

    def _get_wo_standard_item_vals(self, wo, standard_item):
        """
        获取作业规范值
        :return:
        """
        return {
            "wo_id": wo.id,
            "sequence": standard_item.sequence,
            "title": standard_item.title,
            "name": standard_item.name,
            "image_1920": standard_item.image_1920
        }

    @api.model
    def create(self, vals):
        if not vals.get("dispatch_time", False):
            vals["dispatch_time"] = fields.Datetime.now()
        if not vals.get("code", False):
            vals["code"] = self.env['ir.sequence'].next_by_code('roke.work.order.code')
        # 判断开工状态
        if self.sudo().env['ir.config_parameter'].get_param(
            'work.order.manual.start', default="0"
        ) == "0":
            # 不是手工开工时，默认开工状态为已开工
            vals["wo_start_state"] = "已开工"
        else:
            vals["wo_start_state"] = "未开工"
        res = super(RokeWorkOrder, self).create(vals)
        # 创建作业规范
        standard_item_dicts = []
        for wo in res:
            if wo.routing_line_id:
                for standard_item in wo.routing_line_id.standard_item_ids:
                    standard_item_dicts.append(self._get_wo_standard_item_vals(wo, standard_item))
        self.env["roke.work.standard.item"].create(standard_item_dicts)
        for v in res:
            assigned_data = {}
            if "employee_ids" in vals.keys():
                assigned_data["employee_ids"] = [(6, 0, v.employee_ids.ids)]
            if "work_center_id" in vals.keys():
                assigned_data["work_center_id"] = v.work_center_id.id
            if "team_id" in vals.keys():
                assigned_data["team_id"] = v.team_id.id
            if assigned_data:
                assigned_data["work_order_id"] = v.id
                self.env["roke.assigned.wo.record"].create(assigned_data)
        return res

    def write(self, vals):
        if vals.get("state", False):
            for v in self:
                v.last_state = v.state
        res = super(RokeWorkOrder, self).write(vals)
        for v in self:
            assigned_data = {}
            if "employee_ids" in vals.keys():
                assigned_data["employee_ids"] = [(6, 0, v.employee_ids.ids if v.employee_ids else [])]
            if "work_center_id" in vals.keys():
                assigned_data["work_center_id"] = v.work_center_id and v.work_center_id.id
            if "team_id" in vals.keys():
                assigned_data["team_id"] = v.team_id and v.team_id.id
            if assigned_data:
                assigned_data["work_order_id"] = v.id
                self.env["roke.assigned.wo.record"].create(assigned_data)
        return res

    @api.model
    def _search(self, args, offset=0, limit=None, order=None, count=False, access_rights_uid=None):
        check_allow_process = self._context.get("check_allow_process")
        if check_allow_process:
            allow_process = self.env["roke.user.default.setting"].search([
                ("user_id", "=", self.env.user.id)
            ]).allow_process_ids
            domain = []
            if allow_process:
                domain = [("process_id", "in", allow_process.ids)]
            args = expression.AND([domain, args])
        return super(RokeWorkOrder, self)._search(args, offset=offset, limit=limit, order=order,
                                                  count=count, access_rights_uid=access_rights_uid)

    def force_finish(self, origin='self'):
        """
        强制完工
        :param origin: 调用来源
        :return:
        """
        force_finish_type = self.env['ir.config_parameter'].sudo().get_param('force.finish.type', default="only_finish")
        if self.task_id and force_finish_type != "only_finish" and origin == "self":
            # 需要修改完成数的强制完工类型，统一到生产任务重处理
            self.task_id.force_finish(origin="self")
            return
        self.write({"state": "强制完工"})
        self.child_wo_ids.write({"state": "强制完工"})
        self.env.cr.commit()
        if self.task_id and origin == "self" and not self.task_id.work_order_ids.filtered(lambda wo: wo.state in ["未派工", "未开工", "进行中"]):
            # 校验任务,任务下工单都完工则任务强制完工
            self.task_id.force_finish(origin="wo")

    def cancel_force_finish(self, origin='self'):
        """
        取消强制完工
        :param origin: 调用来源
        :return:
        """
        force_finish_type = self.env['ir.config_parameter'].sudo().get_param('force.finish.type', default="only_finish")
        if force_finish_type != "only_finish" and origin == "self" and self.task_id.state == "强制完工":
            # 需要修改完成数的强制完工类型，统一到生产任务重处理
            self.task_id.cancel_force_finish(origin="self")
            return
        self.write({"state": self.last_state})
        for item in self.child_wo_ids:
            item.write({"state": item.last_state})
        if origin == "self" and self.task_id.state == "强制完工":
            self.task_id.cancel_force_finish(origin="wo")

    def make_suspend(self, origin='self'):
        """
        暂停
        :return:
        """
        self.write({"state": "暂停"})
        self.child_wo_ids.write({"state": "暂停"})
        self.env.cr.commit()
        if origin == "self":
            create_dict = {'wo': [(6, 0, self.ids + self.child_wo_ids.ids)]}
            if not self.task_id.work_order_ids.filtered(lambda wo: wo.state in ["未派工", "未开工", "进行中"]):
                self.task_id.make_suspend(origin="wo")
                create_dict.update({'pt': [(6, 0, self.task_id.ids)]})
            if not self.order_id.line_ids.filtered(lambda wo: wo.state == "未完工"):
                create_dict.update({'po': self.order_id.id})
            res = self.env['roke.suspend.note.wizard'].create(create_dict)
            return {
                'name': '暂停原因',
                'type': 'ir.actions.act_window',
                'view_mode': 'form',
                'target': 'new',
                'res_id': res.id,
                'res_model': 'roke.suspend.note.wizard'
            }

    def cancel_make_suspend(self, origin='self'):
        """
        取消暂停
        :return:
        """
        self.write({"state": self.last_state})
        for item in self.child_wo_ids:
            item.write({"state": item.last_state})
        if origin == "self" and self.task_id.state == "暂停":
            self.task_id.cancel_make_suspend(origin="wo")

    def create_fo(self):
        """
        创建成品入库单
        :return:
        """

    def _create_work_record_get_values(self, wait_qty, default_qty, multi):
        """
        打开报工窗口时获取数据
        :return:
        """
        classes = self.env["roke.classes"].get_now_classes()
        finish_qty = default_qty if default_qty > 0 else 0
        autofill_rated_wh = True if self.sudo().env['ir.config_parameter'].get_param('autofill.rated.working.hours') == "1" else False
        if autofill_rated_wh:
            work_hours = finish_qty * self.rated_working_hours
        else:
            work_hours = 0
        return {
            "work_order_id": self.id,
            "plan_qty": self.plan_qty,
            "wait_qty": wait_qty if wait_qty > 0 else 0,
            "employee_id": self.employee_ids[:1].id,
            "team_id": self.team_id.id,
            "work_center_id": self.work_center_id.id,
            "classes_id": classes.id,
            "finish_qty": finish_qty,
            "work_hours": work_hours,
            "multi": multi
        }

    def _get_after_wos(self):
        """
        获取后道可执行工序
        :return:
        """
        self.ensure_one()
        if self.wo_child_type == "child":
            main_wo = self.main_wo_id
            same_main_wos = main_wo.child_wo_ids
            if self == same_main_wos[-1]:
                # 末道子工序校验其主工序的后道主工序的完工数量
                after_wos = self.sudo().task_id.work_order_ids.filtered(
                    lambda wo: wo.sequence > main_wo.sequence and wo.wo_child_type == "main" and wo != main_wo
                )
            else:
                after_wos = main_wo.child_wo_ids.filtered(
                    lambda wo: wo.sequence > self.sequence
                )
        else:
            # 因为子工序末道工序完工会回写其主工序的合格数，所以这里只需要筛选主工序即可
            after_wos = self.sudo().task_id.work_order_ids.filtered(
                lambda wo: wo.sequence > self.sequence and wo.wo_child_type == "main"
            )
        return after_wos

    def _get_all_previous(self):
        """
        获取所有前工序
        :return:
        """
        previous = self.sudo().task_id.work_order_ids.filtered(lambda wo: wo.sequence < self.sequence and wo.wo_child_type == "main")
        return previous

    def _get_all_child_previous(self):
        """
        获取子工序的所有前工序
        :return:
        """
        previous = self.sudo().main_wo_id.child_wo_ids.filtered(lambda wo: wo.sequence < self.sequence)
        return previous

    def _get_previous(self):
        """
        获取前道工序对应工单(可能多个)
        :return:
        """
        # 所有之前的工序
        previous = self._get_all_previous()
        if len(previous) <= 1:
            return previous
        # 前道工序序号
        previous_sequence = max(previous.mapped("sequence"))
        # 前道工序
        previous_wos = previous.filtered(lambda wo: wo.sequence == previous_sequence)
        return previous_wos

    def _get_child_previous(self):
        """
        获取自动单的前道工序工单
        :return:
        """
        previous = self._get_all_child_previous()
        if len(previous) <= 1:
            return previous
        # 前道工序序号
        previous_sequence = max(previous.mapped("sequence"))
        # 前道工序
        previous_wos = previous.filtered(lambda wo: wo.sequence == previous_sequence)
        return previous_wos

    def _get_previous_finished_qty(self, previous):
        """
        获取前道工序完工数,同级工序的计划数和完工数
        :return:
        """
        sequence_qty = {}
        for p in previous:
            sequence = p.sequence
            if sequence in sequence_qty:
                sequence_qty[sequence] = sequence_qty.get(sequence) + p.finish_qty
            else:
                sequence_qty[sequence] = p.finish_qty
        return min(sequence_qty.values())

    def _multi_previous_get_min_finished(self, previous):
        """
        前道工序为并行工序时获取完工数最小的工序
        :return:
        """
        return sorted(previous, key=lambda x: x['finish_qty'], reverse=True)[0]

    def _get_production_multiple(self, previous):
        """
        获取当前生产倍数
        :param previous:
        :return:
        """
        if not self.routing_line_id:
            # 无工艺明细直接返回
            return 1
        if len(previous) > 1:
            # 取到最小数对应的前道工单
            rel_previous = self._multi_previous_get_min_finished(previous)
            if not rel_previous.routing_line_id.multiple:  # 无倍数直接返回
                return 1
            return self.routing_line_id.multiple / rel_previous.routing_line_id.multiple
        if not previous.routing_line_id.multiple:  # 无倍数直接返回
            return 1
        return self.routing_line_id.multiple / previous.routing_line_id.multiple

    def _get_previous_min_qty(self):
        """
        获取前道工序最小完工数
        :return:
        """
        previous = self._get_previous()  # 考虑并行工序原因，前道工序可能有多个
        if previous:
            # 获取前道工序最小完成数
            previous_finished_qty = self._get_previous_finished_qty(previous)
            # 生产倍数
            min_qty = previous_finished_qty * self._get_production_multiple(previous)
            return min_qty, previous
        else:
            return self.plan_qty * self._get_production_multiple(previous), previous

    def _get_child_previous_min_qty(self):
        """
        获取子工单的前工序最小完工数量
        :return:
        """
        previous = self._get_child_previous()  # 考虑并行工序原因，前道工序可能有多个
        if previous:
            # 获取前道工序最小完成数
            min_qty = self._get_previous_finished_qty(previous)
        elif self.main_wo_id:
            # 获取主工序的前道工序
            min_qty, previous = self.main_wo_id._get_previous_min_qty()
        else:
            min_qty = self.plan_qty
        return min_qty, previous

    def _get_wo_allow_qty(self):
        """
        获取工单可报数量
            可报数量=当前计划数-当前完成数；若禁止自由报工，可报数量=前道工序完工数
            默认数量=可报数量
        :return:
        """
        company = self.env.user.company_id
        exceed_plan_qty = float(self.sudo().env["ir.config_parameter"].get_param('exceed.plan.qty', default=0))
        default_qty = round(self.plan_qty - self.finish_qty, _get_pd(self.env))
        allow_qty = round(self.plan_qty - self.finish_qty, _get_pd(self.env))
        if company.freedom_work != "允许":  # 禁止自由报工 校验前道工序完成数：当前允许数量等于签到工序最小完工数
            if self.wo_child_type == "child" and self.main_wo_id:
                # 子工序校验可报数量
                child_previous_min_qty, previous = self._get_child_previous_min_qty()
                if child_previous_min_qty > self.plan_qty:  # 如果前工单最小报工数大于当前工单计划数，那么取当前计划数（拆分工单场景）
                    child_previous_min_qty = self.plan_qty
                if type(child_previous_min_qty) in (float, int):
                    allow_qty = child_previous_min_qty - self.finish_qty
                    default_qty = child_previous_min_qty - self.finish_qty
            else:
                previous_min_qty, previous = self._get_previous_min_qty()
                if previous_min_qty > self.plan_qty:  # 如果前工单最小报工数大于当前工单计划数，那么取当前计划数（拆分工单场景）
                    previous_min_qty = self.plan_qty
                if type(previous_min_qty) in (float, int):
                    allow_qty = previous_min_qty - self.finish_qty
                    default_qty = previous_min_qty - self.finish_qty
            if exceed_plan_qty and company.exceed_plan == "允许" and not previous:
                # 禁止自由报工，允许超计划时。当没有前工序时，允许数量增加超计划数量；有前道工序则不可超前道工序
                allow_qty = round(self.plan_qty + exceed_plan_qty - self.finish_qty, _get_pd(self.env))
        else:
            # 允许自由报工，超计划时允许数量增加超计划数量
            if exceed_plan_qty and company.exceed_plan == "允许":
                allow_qty = round(self.plan_qty + exceed_plan_qty - self.finish_qty, _get_pd(self.env))
        if company.complete_basis == "报工数":
            allow_qty = round(allow_qty - self.unqualified_qty, _get_pd(self.env))
            default_qty = round(default_qty - self.unqualified_qty, _get_pd(self.env))
        return round(allow_qty, _get_pd(self.env)) if allow_qty > 0 else 0, round(default_qty, _get_pd(self.env)) if default_qty > 0 else 0

    def create_work_record(self, multi):
        """
        报工
        :return:
        """
        allow_qty, default_qty = self._get_wo_allow_qty()
        if allow_qty <= 0:
            raise ValidationError("当前工单可报数量为0，请先完成前道工序。（或联系管理员设置系统【允许自由报工（生产设置）】并【允许超库存报工（物料设置）】")
        res = self.env["roke.create.work.record.wizard"].create(self._create_work_record_get_values(allow_qty, default_qty, multi))
        return {
            'name': '报工',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.create.work.record.wizard'
        }

    def multi_create_work_record(self):
        """
        多人报工
        :return:
        """
        return self.create_work_record(True)

    def single_create_work_record(self):
        """
        单人报工
        :return:
        """
        return self.create_work_record(False)

    def _get_production_result_vals(self, qty, wr_id):
        """获取产出物信息"""
        work_record = self.env["roke.work.record"].browse(wr_id)
        return {
            "product_id": self.product_id.id,
            "qty": qty,
            "wr_id": wr_id,
            "wo_id": self.id,
            "pt_id": self.task_id.id,
            "po_id": self.order_id.id,
            "product_state_id": work_record.product_state_id.id,
        }

    def _get_finish_check_previous(self):
        """
        获取工单完成时实际校验数的前工序工序
        :return:
        """
        return self.task_id.work_order_ids.filtered(lambda wo: wo.sequence < self.sequence)

    def _get_finish_check_qty(self, self_finish_qty, self_unqualified_qty):
        """
        获取工单完成时实际校验数，可能是合格数，也可能是不合格数
        :return:
        """
        check_qty = self_finish_qty
        if self.env.user.company_id.complete_basis == "报工数":  # 以报工数作为完工标准时，check_qty = 合格数 + 不合格数
            check_qty += self_unqualified_qty
            # 获取前道工序所有不合格数量，即前道工序报不合格后，当前工序应报数量要减掉前道不合格的数量。后面可能再调整
            # previous = self.task_id.work_order_ids.filtered(lambda wo: wo.sequence < self.sequence)
            previous = self._get_finish_check_previous()
            if previous:
                check_qty += sum(previous.mapped("unqualified_qty"))
        return check_qty

    def _finish_check_task(self):
        """
        判断工单完成时是否级联完成任务
            任务下所有工单完工即表示需要级联完成任务
        :return:
        """
        # return self.task_id and not self.task_id.work_order_ids.filtered(lambda wo: wo.state == "未完工")
        return self.task_id and self == self.task_id.work_order_ids[-1]

    def check_create_production_result(self, wr_id):
        """
        校验是否生成产出物
        :return:
        """
        if self.task_id and self == self.task_id.work_order_ids.sorted(
                key=lambda r: (r.sequence, r.id)
        ).filtered(lambda wo: not wo.child_wo_ids)[-1]:
            # 无子工单的最后一个工单
            return True
        elif not self.task_id and self.process_id.without_wo_produce:
            # 无工单是否产生产出物
            return True
        return False

    def finish_report_work_order(self, qty, unqualified_qty, work_hours, finish_time=fields.Datetime.now(), wr_id=False, move=True):
        """
        工单完成
        :return:
        """
        # 修改当前工单完成数
        finish_qty = round(self.finish_qty + qty, _get_pd(self.env))
        unqualified_qty = round(self.unqualified_qty + unqualified_qty, _get_pd(self.env))
        new_work_hours = round(self.work_hours + work_hours, 2)
        write_dict = {
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "work_hours": new_work_hours,
            "finish_time": finish_time,
            "state": self.state
        }
        if not self.wo_manual_start and not self.wo_start_time:
            # 不是手工开工，且无报工记录时，这里写入开工时间
            write_dict["wo_start_time"] = fields.Datetime.now()
        check_qty = self._get_finish_check_qty(finish_qty, unqualified_qty)
        if check_qty >= self.plan_qty:
            # 根据工单是否手工完工处理工单的状态和完工时间
            if self.wo_manual_finish:
                write_dict.pop("state")
            else:
                write_dict["state"] = "已完工"
                write_dict["wo_finish_time"] = fields.Datetime.now()
            # write_dict["state"] = "已完工"
            # if not self.wo_manual_finish:
            #     write_dict["wo_finish_time"] = fields.Datetime.now()
        self.write(write_dict)
        if self.task_id:
            self.task_id.finish_report_task(qty)
        if self.check_create_production_result(wr_id):
            self.env["roke.production.result"].create(self._get_production_result_vals(qty, wr_id))
        # 回写主工单工时 TODO 子工单
        if self.wo_child_type == "child" and self.main_wo_id:
            main_write_dict = {
                "work_hours": self.main_wo_id.work_hours + work_hours
            }
            # 所有子工序都完工，主工单完工
            if self.main_wo_id.child_wo_ids.filtered(lambda child: child.state == "未完工"):
                main_state = "未完工"
            else:
                main_state = self.state
            main_write_dict["state"] = main_state
            if self.main_wo_id.child_wo_ids[-1] == self:
                main_write_dict["finish_time"] = finish_time
                main_write_dict["finish_qty"] = self.main_wo_id.finish_qty + qty
            self.main_wo_id.write(main_write_dict)

    def withdraw_report_work_order(self, qty, unqualified_qty, work_hours, work_record):
        """
        撤回报工
        :param qty:
        :param unqualified_qty:
        :param work_hours:
        :return:
        """
        finish_qty = round(self.finish_qty - qty, _get_pd(self.env))
        unqualified_qty = round(self.unqualified_qty - unqualified_qty, _get_pd(self.env))
        new_work_hours = round(self.work_hours - work_hours, 2)
        write_dict = {
            "finish_qty": finish_qty,
            "unqualified_qty": unqualified_qty,
            "work_hours": new_work_hours,
        }
        if not self.wo_manual_start and not self.record_ids:
            # 不是手工开工，且存在开工时间，这里开工时间置为空
            write_dict["wo_start_time"] = False
        if finish_qty < self.plan_qty:
            # 根据工单是否手工完工处理工单的状态和完工时间
            if not self.wo_manual_finish:
                write_dict["state"] = self.last_state
                write_dict["wo_finish_time"] = False
            # write_dict["state"] = "未完工"
            # if not self.wo_manual_finish:
            #     write_dict["wo_finish_time"] = False
        self.write(write_dict)
        if self.task_id:
            max_sequence = max(self.task_id.work_order_ids.mapped("sequence"))
            if self.sequence == max_sequence:  # 最后一级工序（可能是多个并行工序）撤回时，撤回任务的完工数
                self.task_id.withdraw_report_task()
            elif write_dict.get("state") in ["未派工", "未开工", "进行中"]:
                # 自由报工时，撤回中间工序报工，任务级联编辑为未完工
                self.task_id.write({"state": "未完工"})
                if self.order_id:
                    self.order_id.write({"state": "未完工"})
        # 回写主工单工时 TODO 子工单
        if self.wo_child_type == "child" and self.main_wo_id:
            if self.main_wo_id.child_wo_ids[-1] == self:
                self.main_wo_id.write({
                    "work_hours": self.main_wo_id.work_hours - work_hours,
                    "finish_qty": self.main_wo_id.finish_qty - qty,
                    "state": self.main_wo_id.last_state,
                    "finish_time": False
                })

    @api.onchange("employee_ids")
    def _onchange_employee_ids(self):
        if self.employee_ids:
            return {"value": {
                "team_id": self.employee_ids[0].team_id.id,
            }}

    def unlink(self):
        if sum(self.record_ids.mapped("finish_qty")) > 0:
            raise ValidationError("当前任务已报工禁止删除！如果您一定要删除请先联系管理员删除报工记录。")
        return super(RokeWorkOrder, self).unlink()

    def get_instruction_file_url(self, file_type=None):
        """
        获取作业知道图片预览地址
        :param file_type: 入参‘base64’或预览地址
        :return:
        """
        base_url = self.sudo().env['ir.config_parameter'].get_param('web.base.url')
        attachment = self.sudo().env['ir.attachment'].search([
            ("res_model", "=", "roke.work.order"),
            ("res_id", "=", self.id),
            ("res_field", "=", "instruction_file_data")
        ])  # 必须带sudo，原因见odoo-14.0/odoo/addons/base/models/ir_attachment.py 第441行
        if not attachment:
            return False
        if file_type == "base64":
            return [{
                "id": attachment.id,
                "name": attachment.name,
                "mimetype": attachment.mimetype,
                "type": "base64",
                "data": attachment.datas,
                "is_picture": False,
            }]
        if not attachment.access_token:
            attachment.generate_access_token()
        if attachment.mimetype == "application/pdf":
            # pdf 预览
            content_url = parse.quote("/web/content/%s?access_token=%s" % (str(attachment.id), attachment.sudo().access_token))
            url = "%s/web/static/lib/pdfjs/web/viewer.html?file=%s" % (base_url, content_url)
            is_picture = False
        else:
            # 图片 预览
            url = "%s/web/image/%s?access_token=%s" % (base_url, str(attachment.id), attachment.sudo().access_token)
            is_picture = True if attachment.index_content == 'image' else False
        return [{
            "id": attachment.id,
            "name": attachment.name,
            "mimetype": attachment.mimetype,
            "type": "url",
            "data": url,
            "is_picture": is_picture,
        }]

    def _get_work_order_name(self):
        return "%s【%s-%s】" % (self.code, self.product_id.name, self.process_id.name)

    def name_get(self):
        return [(record.id, record._get_work_order_name()) for record in self]

    def previous_replenish_plan_qty(self, qty, replenish_self=False):
        """
        前工序补件，通用方法，目前仅，质检模块调用
        :param qty: 补件数量
        :param replenish_self: 当前工单是否补件
        :return:
        """
        self.ensure_one()
        previous = self.task_id.work_order_ids.filtered(lambda wo: wo.sequence < self.sequence)
        if replenish_self:
            previous += self
        for previous_wo in previous:
            previous_wo.write({"plan_qty": previous_wo.plan_qty + qty, "state": previous_wo.last_state})

    def _split_wo_get_values(self, allow_split_qty):
        """
        获取拆分工单的值
        :return:
        """
        return {
            "origin_wo_id": self.id,
            "split_wo_count": 1,
            "allow_split_qty": allow_split_qty,
            "split_qty": allow_split_qty,
            "team_id": self.team_id.id,
            "employee_ids": [(6, 0, self.employee_ids.ids)],
            "work_center_id": self.work_center_id.id,
            "plan_date": self.plan_date
        }

    def action_split_wo(self):
        """
        拆分工单
        :return:
        """
        allow_split_qty = self.plan_qty - self.finish_qty
        if allow_split_qty <= 0:
            raise ValidationError("无可拆分数量。")
        res = self.env["roke.split.work.order.wizard"].create(self._split_wo_get_values(allow_split_qty))
        return {
            'name': '拆分工单',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.split.work.order.wizard'
        }

    def _order_transfer_get_values(self, allow_transfer_qty):
        """
        获取工单转移的值
        :return:
        """
        return {
            "origin_wo_id": self.id,
            "allow_transfer_qty": allow_transfer_qty,
            "transfer_qty": allow_transfer_qty,
            "team_id": self.team_id.id,
            "employee_ids": [(6, 0, self.employee_ids.ids)],
            "work_center_id": self.work_center_id.id,
        }

    def work_order_transfer(self):
        """
        工单转移
        :return
        """
        company = self.env.user.company_id
        if company.complete_basis == "合格数":
            allow_transfer_qty = self.plan_qty - self.finish_qty
        elif company.complete_basis == "报工数":
            allow_transfer_qty = self.plan_qty - self.finish_qty - self.unqualified_qty
        else:
            raise ValidationError("无效的完工依据。")
        if allow_transfer_qty <= 0:
            raise ValidationError("无可转移数量。")
        res = self.env["roke.transfer.work.order.wizard"].create(self._order_transfer_get_values(allow_transfer_qty))
        return {
            'name': '转移工单',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.transfer.work.order.wizard'
        }

    def work_order_start(self):
        """
        工单开工
        :return:
        """
        self.filtered(lambda wo: wo.wo_manual_start).write({
            "wo_start_time": fields.Datetime.now(),
            "wo_start_state": "已开工",
            "state": "进行中"
        })

    def work_order_finish(self):
        """
        工单完工
        :return:
        """
        manual_finish_wo = self.filtered(lambda wo: wo.wo_manual_finish)
        manual_finish_wo.write({
            "wo_finish_time": fields.Datetime.now(),
            "state": "已完工"
        })
        # 手工完工时，修改对应生产任务的完工状态
        manual_finish_wo.mapped("task_id").finish_report_task(self.finish_qty)

    def cancel_work_order_start(self):
        """
        取消工单开工
        :return:
        """
        self.filtered(lambda wo: wo.wo_manual_start).write({
            "wo_start_time": False,
            "wo_start_state": "未开工",
            "state": "未开工"
        })

    def cancel_work_order_finish(self):
        """
        取消工单完工
        :return:
        """
        manual_finish_wo = self.filtered(lambda wo: wo.wo_manual_finish)
        manual_finish_wo.write({
            "wo_finish_time": False,
            "state": manual_finish_wo.last_state
        })
        # 手工取消完工时，修改对应生产任务和生产订单的完工状态
        manual_finish_wo.mapped("task_id").write({"state": "未完工"})
        manual_finish_wo.mapped("order_id").write({"state": "未完工"})

