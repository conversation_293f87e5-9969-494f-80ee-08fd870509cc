# -*- coding: utf-8 -*-
"""
Description:

Versions:
    Created by www.rokedata.com<HuChuanwei>
"""
from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo import tools


# class RokeWorkOrderHoursReport(models.Model):
#     _name = "roke.work.order.hours.report"
#     _description = "工单计时"
#     _auto = False
#
#
#     work_order_id = fields.Many2one("roke.work.order", string="工单")
#     product_id = fields.Many2one("roke.product", string="产品名称")
#     process_id = fields.Many2one("roke.process", string="工序")
#     team_id = fields.Many2one("roke.work.team", string="班组")
#     employee_ids = fields.Many2many("roke.employee", string="作业人员", compute="_compute_employee")
#     work_center_id = fields.Many2one("roke.work.center", string="工作中心")
#     finish_qty = fields.Float(string="完工数量", digits='Production')
#     unqualified_qty = fields.Float(string="不合格数", digits='Production')
#     pass_rate = fields.Float(string="合格率(%)", default=0, digits='SCSL')
#     work_hours = fields.Float(string="工时")
#     project_code = fields.Char(string="项目号")
#     customer_id = fields.Many2one("roke.partner", string="客户")
#     note = fields.Text(string="备注")
#     # allot_ids = fields.One2many("roke.work.record.allot", "work_record_id", string="绩效分配")
#     task_id = fields.Many2one("roke.production.task", string="任务单")
#     production_order_id = fields.Many2one("roke.production.order", string="生产订单")
#     dispatch_qty = fields.Float(string="派工数量", digits='Production')
#     dispatch_time = fields.Datetime(string="派工时间")
#     create_date = fields.Datetime(string="报工时间")
#     state = fields.Selection([("未完工", "未完工"), ("已完工", "已完工")], string="状态")
#     priority = fields.Selection([('正常', '正常'), ('紧急', '紧急'), ('非常紧急', '非常紧急')], string="优先级")
#     company_id = fields.Many2one('res.company', string='公司')


