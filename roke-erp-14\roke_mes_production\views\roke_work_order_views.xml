<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--生产工单-->
    <!--可编辑列表-->
    <record id="view_roke_work_order_edit_tree" model="ir.ui.view">
        <field name="name">roke.work.order.edit.tree</field>
        <field name="model">roke.work.order</field>
        <field name="arch" type="xml">
            <tree editable="bottom"
                  decoration-success="state in ['已完工', '强制完工']"
                  decoration-warning="priority=='4级' and state=='未派工'"
                  decoration-danger="priority=='5级' and state=='未派工'">
                <field name="sequence" optional="hide" widget="handle"/>
                <field name="task_id" invisible="1"/>
                <field name="routing_line_id" invisible="1"/>
                <field name="main_wo_sequence" optional="hide"/>
                <field name="code" readonly="1" optional="show"/>
                <field name="process_id" optional="show" required="1" width="90px"/>
                <field name="product_id" optional="hide" invisible="1" width="90px"/>
                <field name="plan_qty" optional="show" required="1" sum="计划数量"/>
                <field name="uom_id" optional="show"/>
                <field name="finish_qty" optional="show" readonly="1" sum="完工数量"/>
                <field name="unqualified_qty" optional="show" readonly="1" sum="不合格数"/>
                <field name="pass_rate" optional="show"/>
                <field name="work_hours" optional="show" readonly="1" sum="工时数"/>
                <field name="plan_date" optional="show"/>
                <field name="planned_start_time" optional="show"/>
                <field name="employee_ids" widget="many2many_tags" optional="show"/>
                <field name="state" optional="show" readonly="1"/>
                <field name="wo_start_state" optional="show" readonly="1"/>
                <field name="dispatch_time" optional="show" readonly="1"/>
                <field name="team_id" optional="hide"/>
                <field name="work_employee_ids" widget="many2many_tags" optional="hide"/>
                <field name="work_center_id" optional="hide"/>
                <field name="order_id" optional="hide"/>
                <field name="customer_id" optional="hide"/>
                <field name="project_code" optional="hide"/>
                <field name="note" optional="hide"/>
                <field name="type" optional="hide"/>
                <field name="wo_child_type" invisible="1"/>
                <field name="child_wo_ids" invisible="1"/>
                <field name="wo_manual_start" invisible="1"/>
                <field name="priority" optional="hide"/>
                <field name="finish_time" optional="hide" readonly="1"/>
                <field name="create_uid" string="创建人" optional="hide"/>
                <field name="create_date" string="创建时间" optional="hide"/>
                <button name="action_set_employee_ids" type="object" string="派工" class="oe_highlight oe_read_only" states="未派工"/>
                <button name="single_create_work_record" type="object" string="单人报工" class="oe_highlight oe_read_only"
                        attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                         '&amp;', ('wo_start_state','=','未开工'), ('wo_manual_start', '=', True)]}"/>
                <button name="multi_create_work_record" type="object" string="多人报工" class="oe_highlight oe_read_only"
                        attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                         '&amp;', ('wo_start_state','=','未开工'), ('wo_manual_start', '=', True)]}"/>
            </tree>
        </field>
    </record>
    <!-- 派工 -->
    <record id="view_roke_work_order_set_employee_ids_form" model="ir.ui.view">
        <field name="name">view_roke_work_order_set_employee_ids_form</field>
        <field name="model">roke.work.order</field>
        <field name="arch" type="xml">
            <form string="派工">
                <sheet>
                    <group id="g1" col="1">
                        <group string="工序信息" col="4">
                            <field name="product_id" readonly="1"/>
                            <field name="process_id" readonly="1"/>
                            <field name="plan_qty" readonly="1"/>
                            <field name="uom_id" readonly="1"/>
                        </group>
                        <group string="执行班组信息" col="4">
                            <field name="team_id" required="1"/>
                            <field name="work_hours" required="1"/>
                            <field name="employee_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                </sheet>
                <footer>
                    <button string="确认" type="object" name="action_save" class="oe_highlight oe_read_only"/>
                    <button special="cancel" string="取消"/>
                </footer>
            </form>
        </field>
    </record>
    <!--search-->
    <record id="view_roke_work_order_search" model="ir.ui.view">
        <field name="name">roke.work.order.search</field>
        <field name="model">roke.work.order</field>
        <field name="priority">1</field>
        <field name="arch" type="xml">
            <search string="生产工单">
                <field name="code"/>
                <field name="task_id"/>
                <field name="order_id"/>
                <field name="product_id"/>
                <field name="process_id"/>
                <field name="customer_id"/>
                <field name="project_code"/>
                <field name="plan_date"/>
                <field name="work_center_id"/>
                <filter string="已归档" name="inactive" domain="[('active', '=', False)]"/>
                <filter string="已开工" name="已开工" domain="[('wo_start_state', '=', '已开工')]"/>
                <filter string="未开工" name="未开工" domain="[('wo_start_state', '=', '未开工')]"/>
                <separator/>
                <filter string="未派工" name="未派工" domain="[('state', '=', '未派工')]"/>
                <filter string="未开工" name="未开工" domain="[('state', '=','未开工')]"/>
                <filter string="进行中" name="进行中" domain="[('state', '=','进行中')]"/>
                <filter string="已完工" name="已完工" domain="[('state', '=','已完工')]"/>
                <separator/>
                <filter string="1级" name="1级" domain="[('priority', '=', '1级')]"/>
                <filter string="2级" name="2级" domain="[('priority', '=', '2级')]"/>
                <filter string="3级" name="3级" domain="[('priority', '=', '3级')]"/>
                <filter string="4级" name="4级" domain="[('priority', '=', '4级')]"/>
                <filter string="5级" name="5级" domain="[('priority', '=', '5级')]"/>
                <separator/>
                <filter string="生产" name="生产" domain="[('type', '=', '生产')]"/>
                <separator/>
                <filter name="filter_date" date="plan_date" default_period="this_month"/>
                <filter name="filter_create_date" date="create_date" default_period="this_month" string="创建时间"/>
                <group expand="0" string="Group By">
                    <filter string="生产任务" name="group_task_id" context="{'group_by': 'task_id'}"/>
                    <filter string="成品" name="group_product_id" context="{'group_by': 'product_id'}"/>
                    <filter string="工序" name="group_process_id" context="{'group_by': 'process_id'}"/>
                    <filter string="订单" name="group_order_id" context="{'group_by': 'order_id'}"/>
                    <filter string="客户" name="group_customer_id" context="{'group_by': 'customer_id'}"/>
                    <filter string="状态" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="开工状态" name="group_wo_start_state" context="{'group_by': 'wo_start_state'}"/>
                    <filter string="计划完成" name="group_plan_date" context="{'group_by': 'plan_date'}"/>
                    <filter string="优先级" name="group_priority" context="{'group_by': 'priority'}"/>
                </group>
            </search>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_work_order_tree" model="ir.ui.view">
        <field name="name">roke.work.order.tree</field>
        <field name="model">roke.work.order</field>
        <field name="arch" type="xml">
            <tree string="生产工单"
                  decoration-success="state in ['已完工', '强制完工']"
                  decoration-warning="priority=='4级' and state=='未派工'"
                  decoration-danger="priority=='5级' and state=='未派工'">
                <field name="code"/>
                <field name="process_id" optional="show"/>
                <field name="product_id" optional="show"/>
                <field name="plan_qty" optional="show" sum="计划数合计"/>
                <field name="uom_id" optional="show"/>
                <field name="finish_qty" optional="show" sum="合格数合计"/>
                <field name="unqualified_qty" optional="show" sum="不合格数合计"/>
                <field name="pass_rate" optional="show"/>
                <field name="work_hours" optional="show" sum="工时合计"/>
                <field name="team_id" optional="show"/>
                <field name="employee_ids" widget="many2many_tags" optional="show"/>
                <field name="work_center_id" optional="show"/>
                <field name="plan_date" optional="show"/>
                <!--承诺交期-->
                <field name="priority" optional="show"/>
                <field name="state" optional="show"/>
                <field name="wo_start_state" optional="show"/>
                <field name="task_id" optional="hide"/>
                <field name="sequence" optional="hide"/>
                <field name="main_wo_sequence" optional="hide"/>
                <field name="work_employee_ids" widget="many2many_tags" optional="hide"/>
                <field name="order_id" optional="hide"/>
                <field name="customer_id" optional="hide"/>
                <field name="project_code" optional="hide"/>
                <field name="dispatch_time" optional="hide"/>
                <field name="finish_time" optional="hide"/>
                <field name="note" optional="hide"/>
                <field name="type" optional="hide"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
                <field name="wo_child_type" invisible="1"/>
                <field name="child_wo_ids" invisible="1"/>
                <field name="wo_manual_start" invisible="1"/>
                <field name="create_uid" string="创建人" optional="hide"/>
                <field name="create_date" string="创建时间" optional="hide"/>
                <button name="single_create_work_record" type="object" string="单人报工" class="oe_highlight oe_read_only"
                        attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                         '&amp;', ('wo_start_state','=','未开工'), ('wo_manual_start', '=', True)]}"/>
                <button name="multi_create_work_record" type="object" string="多人报工" class="oe_highlight oe_read_only"
                        attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                         '&amp;', ('wo_start_state','=','未开工'), ('wo_manual_start', '=', True)]}"/>
            </tree>
        </field>
    </record>
    <!--tree-->
    <record id="view_roke_work_order_simple_tree" model="ir.ui.view">
        <field name="name">roke.work.order.simple.tree</field>
        <field name="model">roke.work.order</field>
        <field name="priority">2000</field>
        <field name="arch" type="xml">
            <tree string="生产工单"
                  decoration-success="state in ['已完工', '强制完工']"
                  decoration-warning="priority=='4级' and state=='未派工'"
                  decoration-danger="priority=='5级' and state=='未派工'">
                <field name="code"/>
                <field name="process_id" optional="show"/>
                <field name="product_id" optional="show"/>
                <field name="plan_qty" optional="show" sum="计划数合计"/>
                <field name="finish_qty" optional="show" sum="合格数合计"/>
                <field name="unqualified_qty" optional="show" sum="不合格数合计"/>
                <field name="pass_rate" optional="show"/>
                <field name="work_hours" optional="show" sum="工时合计"/>
                <field name="team_id" optional="show"/>
                <field name="employee_ids" widget="many2many_tags" optional="show"/>
                <field name="work_center_id" optional="show"/>
                <field name="plan_date" optional="show"/>
                <!--承诺交期-->
                <field name="priority" optional="show"/>
                <field name="state" optional="show"/>
                <field name="task_id" optional="hide"/>
                <field name="sequence" optional="hide"/>
                <field name="work_employee_ids" widget="many2many_tags" optional="hide"/>
                <field name="uom_id" optional="hide"/>
                <field name="order_id" optional="hide"/>
                <field name="customer_id" optional="hide"/>
                <field name="project_code" optional="hide"/>
                <field name="dispatch_time" optional="hide"/>
                <field name="finish_time" optional="hide"/>
                <field name="note" optional="hide"/>
                <field name="type" optional="hide"/>
                <field name="is_printed"/>
                <field name="print_times"/>
                <field name="print_uid"/>
                <field name="print_date"/>
                <field name="create_uid" string="创建人" optional="hide"/>
                <field name="create_date" string="创建时间" optional="hide"/>
            </tree>
        </field>
    </record>
    <!--form-->
    <record id="view_roke_work_order_form" model="ir.ui.view">
        <field name="name">roke.work.order.form</field>
        <field name="model">roke.work.order</field>
        <field name="arch" type="xml">
            <form string="生产工单">
                <header>
                    <button name="action_set_employee_ids" type="object" string="派工" class="oe_highlight oe_read_only" states="未派工"/>
                    <button name="work_order_start" type="object" string="开工" class="oe_highlight"
                            attrs="{'invisible': ['|', '|', ('state', '!=', '未开工'), ('wo_manual_start','!=',True), ('wo_start_state','!=','未开工')]}"
                            confirm="确认开工？"/>
                    <button name="work_order_finish" type="object" string="完工" class="oe_highlight"
                            attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('wo_manual_finish','!=',True), ('wo_start_state','!=','已开工')]}"
                            confirm="确认完工？"/>

                    <button name="single_create_work_record" type="object" string="单人报工" class="oe_highlight oe_read_only"
                            attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                            '&amp;', ('wo_start_state','=','进行中'), ('wo_manual_start', '=', True)]}"/>
                    <button name="multi_create_work_record" type="object" string="多人报工" class="oe_highlight oe_read_only"
                            attrs="{'invisible': ['|', '|', ('state', '!=', '进行中'), ('child_wo_ids','!=',[]),
                            '&amp;', ('wo_start_state','=','进行中'), ('wo_manual_start', '=', True)]}"/>
                    <button name="force_finish" type="object" string="强制完工" class="btn btn-danger"
                            attrs="{'invisible': [('state', '!=', '进行中')]}" confirm="确认强制完工？"/>
                    <button name="cancel_force_finish" type="object" string="取消强制完工" class="btn btn-danger"
                            attrs="{'invisible': [('state', '!=', '强制完工')]}" confirm="确认取消强制完工？"/>
                    <button name="make_suspend" type="object" string="暂停" class="btn btn-danger"
                            attrs="{'invisible': [('state', '!=', '进行中')]}" confirm="确认暂停？"
                            />
                    <button name="cancel_make_suspend" type="object" string="取消暂停" class="btn btn-danger"
                            attrs="{'invisible': [('state', '!=', '暂停')]}" confirm="确认取消暂停？"
                            />
                    <button name="action_split_wo" type="object" string="拆分工单" class="btn btn-danger"
                            attrs="{'invisible': ['|', ('state', 'not in', ['未派工', '未开工']), ('child_wo_ids','!=',[])]}"/>
                    <button name="work_order_transfer" type="object" string="工单转移"  class="oe_highlight"
                            attrs="{'invisible': ['|', ('state', 'not in', ['未派工', '未开工']), ('child_wo_ids','!=',[])]}"/>
                    <button name="cancel_work_order_start" type="object" string="取消开工"
                            attrs="{'invisible': ['|', '|', '|', ('state', '!=', '进行中'), ('wo_manual_start','!=',True), ('wo_start_state','!=','已开工'), ('record_ids','!=',[])]}"
                            confirm="确认取消开工？"/>
                    <button name="cancel_work_order_finish" type="object" string="取消完工"
                            attrs="{'invisible': ['|', ('state', '!=', '已完工'), ('wo_manual_finish','!=',True)]}"
                            confirm="确认取消完工？"/>
                    <field name="state" widget="statusbar" statusbar_visible="未派工,未开工,进行中,已完工"/>
                </header>
                <div name="button_box" class="oe_button_box"/>
                <field name="allow_edit" invisible="1"/>
                <field name="rated_working_hours" invisible="1"/>
                <field name="wo_child_type" invisible="1"/>
                <field name="child_wo_ids" invisible="1"/>
                <field name="wo_manual_start" invisible="1"/>
                <field name="wo_manual_finish" invisible="1"/>
                <group id="g1" col="4">
                    <group>
                        <field name="process_id" required="1" attrs="{'readonly': [('allow_edit', '!=', True)]}"/>
                        <label for="plan_qty"/>
                        <div name="plan_qty" class="o_row">
                            <field name="plan_qty" required="1" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                            <span name="plan_uom">
                                <field name="uom_id"/>
                            </span>
                        </div>
                        <field name="team_id" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <field name="deadline" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <!--计薪规则-->
                    </group>
                    <group>
                        <field name="product_id" required="1" attrs="{'readonly': [('allow_edit', '!=', True)]}"/>
                        <label for="finish_qty"/>
                        <div name="finish_qty" class="o_row">
                            <field name="finish_qty" readonly="1"/>
                            <span name="finish_uom">
                                <field name="uom_id"/>
                            </span>
                        </div>
                        <field name="pass_rate"/>
                        <field name="employee_ids" widget="many2many_tags" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <field name="workshop_id" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <!--班组计薪方式-->
                        <field name="type" readonly="1"/>
                        <field name="dispatch_time" readonly="1" attrs="{'invisible': [('dispatch_time', '=', False)]}"/>
                    </group>
                    <group>
                        <field name="customer_id" options="{'no_create':True,'no_create_edit':True}" readonly="0" force_save="1"/>
                        <label for="unqualified_qty"/>
                        <div name="unqualified_qty" class="o_row">
                            <field name="unqualified_qty" readonly="1"/>
                            <span name="unqualified_uom">
                                <field name="uom_id"/>
                            </span>
                        </div>
                        <field name="planned_start_time"/>
                        <field name="planned_finish_time" force_save="1" invisible="1"/>
                        <field name="plan_date" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <field name="work_center_id" attrs="{'readonly': [('state', '=', '已完工')]}" string="工作中心"/>


                    </group>
                    <group>
                        <field name="priority" attrs="{'readonly': [('state', '=', '已完工')]}"/>
                        <field name="work_hours" readonly="1"/>
                        <field name="rated_working_hours" readonly="1"/>
                        <field name="department_id" options="{'no_create': True}"/>
                        <field name="equipment_id"  options="{'no_create':True,'no_create_edit':True}" />

                    </group>
                </group>
                <notebook>
                    <field name="last" invisible="1"/>
                    <page string="报工记录" name="record_ids">
                        <field name="record_ids" readonly="1" context="{'tree_view_ref': 'roke_mes_production.view_roke_work_record_embed_tree'}"/>
                    </page>
                    <page string="作业规范/指导">
                        <group string="作业规范">
                            <field name="standard_item_ids" nolabel="1" context="{'tree_view_ref': 'roke_mes_base.view_roke_work_standard_item_editable_tree'}"/>
                        </group>
                        <group string="作业指导" name="attachment">
                            <field name="instruction_file_data" nolabel="1" readonly="False" widget='image' options='{"zoom": true, "preview_image":"image_128"}'/>
                        </group>
                    </page>
                    <page string="派工记录">
                        <field name="assigned_record_ids" readonly="1"/>
                    </page>
                </notebook>
                <group id="g2">
                    <field name="note" placeholder="此处可以填写备注或描述" />
                </group>
                <group id="g3" col="8">
                    <field name="finish_time" readonly="1"/>
                    <field name="order_id" readonly="1"/>
                    <field name="project_code"/>
                    <field name="task_id" options="{'no_create':1}"/>
                    <field name="wo_start_state" readonly="1" attrs="{'invisible': [('wo_manual_start', '=', False)]}"/>
                    <field name="wo_start_time" readonly="1" attrs="{'invisible': [('wo_manual_start', '=', False)]}"/>
                    <field name="wo_finish_time" readonly="1" attrs="{'invisible': [('wo_manual_start', '=', False)]}"/>
                    <field name="wo_duration" readonly="1" attrs="{'invisible': [('wo_manual_start', '=', False)]}"/>
                </group>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
    <!--pivot-->
    <record id="view_roke_work_order_pivot" model="ir.ui.view">
        <field name="name">roke.work.order.pivot</field>
        <field name="model">roke.work.order</field>
        <field name="arch" type="xml">
            <pivot string="生产工单">
                 <field name="plan_date" type="col" interval="day" string="计划完成"/>
                 <field name="product_id" type="row"/>
                 <field name="plan_qty" type="measure"/>
                 <field name="finish_qty" type="measure"/>
            </pivot>
        </field>
    </record>
    <!--action-->
    <record id="view_roke_work_order_action" model="ir.actions.act_window">
        <field name="name">生产工单</field>
        <field name="res_model">roke.work.order</field>
        <field name="view_mode">tree,form,pivot</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[('is_entrust','=',False)]</field>
        <field name="context">{}</field>
        <field name="search_view_id" ref="view_roke_work_order_search"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('view_roke_work_order_tree')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('view_roke_work_order_form')})]"/>
        <field name="form_view_id" ref="view_roke_work_order_form"/>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            点击左上角“创建”按钮新增一个生产工单。
          </p><p>
            或者您也可以从生产订单处推送生产工单。
          </p>
        </field>
    </record>
    <!--批量变更工单-->
    <record id="action_order_multi_dispatch" model="ir.actions.act_window">
        <field name="name">工单批量指派</field>
        <field name="res_model">roke.order.multi.dispatch.wizard</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_roke_order_multi_dispatch_wizard_form"/>
        <field name="target">new</field>
        <field name="context">{}</field>
        <field name="binding_model_id" ref="roke_mes_production.model_roke_work_order"/>
        <field name="binding_view_types">list</field>
    </record>

</odoo>
