<odoo>
    <record id="view_roke_workorder_data_param_form" model="ir.ui.view">
        <field name="name">roke.workorder.data.param.form</field>
        <field name="model">roke.common.data.param</field>
        <field name="arch" type="xml">
            <form string="获取工单数据">
                <sheet>
                    <group>
                        <field name="url" placeholder="请输入接口地址" required="1"/>
                        <field name="method" required="1" />
                        <field name="result" readonly="1" colspan="4" attrs="{'invisible': [('result', '=', 'null')]}"/>
                    </group>
                     <notebook>
                    <page string="自定义参数">
                        <field name="param_ids" >
                            <tree editable="bottom">
                                <field name="key" required="1"/>
                                <field name="value_type" required="1"/>
                                <field name="value" />
                            </tree>
                        </field>
                    </page>
                </notebook>
                     <footer>
                    <div style=" text-align: right;margin-right:30px">
                        <button name="fetch_data" type="object" string="调用接口拉取工单数据" class="btn-primary"/>
                    </div>
                </footer>
                </sheet>

            </form>
        </field>
    </record>

    <record id="action_roke_workorder_data_param" model="ir.actions.act_window">
        <field name="name">获取工单数据</field>
        <field name="res_model">roke.common.data.param</field>
        <field name="view_mode">form</field>
        <field name="target">current</field>
        <field name="view_id" ref="view_roke_workorder_data_param_form"/>
    </record>
</odoo>
