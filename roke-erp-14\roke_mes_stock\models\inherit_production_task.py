from odoo import models, fields, api, _
import math
from odoo.exceptions import ValidationError


class InheritProductionTask(models.Model):
    _inherit = "roke.production.task"

    bom_picking_ids = fields.Many2many(
        "roke.mes.stock.picking", string="bom领料单")
    bom_picking_qty = fields.Integer(
        string="BOM领料单数量", compute="_compute_bom_picking_qty")

    red_task_picking_ids = fields.Many2many(
        "roke.mes.stock.picking", "red_task_picking_ids_rel", string="红单")
    red_task_picking_qty = fields.Integer(
        string="红单数量", compute="_compute_red_task_picking_qty")

    code_ids = fields.Many2many("roke.mes.stock.lot", string="条码信息")

    def _compute_bom_picking_qty(self):
        for record in self:
            record.bom_picking_qty = len(record.bom_picking_ids)

    def _compute_red_task_picking_qty(self):
        for record in self:
            record.red_task_picking_qty = len(record.red_task_picking_ids)

    def pt_action_bom_picking(self):
        """
        查看当前生产任务下的BOM领料单
        :return:
        """
        view_tree = self.env.ref(
            'roke_mes_stock.view_roke_mes_stock_general_out_tree', False)
        view_form = self.env.ref(
            'roke_mes_stock.view_roke_mes_production_out_form', False)
        form_view = [(view_tree.id or False, 'tree'),
                     (view_form.id or False, 'form')]
        return {
            'name': '本生产任务下BOM领料单',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', self.bom_picking_ids.ids)],
            'views': form_view,
            'res_model': 'roke.mes.stock.picking'
        }

    def pt_action_red_task_picking(self):
        """
        查看当前生产任务下的红单
        :return:
        """
        view_tree = self.env.ref(
            'roke_mes_stock.view_roke_mes_stock_general_red_out_tree', False)
        view_form = self.env.ref(
            'roke_mes_stock.view_roke_mes_general_red_out_form', False)
        form_view = [(view_tree.id or False, 'tree'),
                     (view_form.id or False, 'form')]
        return {
            'name': '本生产任务下红单',
            'type': 'ir.actions.act_window',
            'view_mode': 'tree,form',
            'target': 'current',
            'domain': [('id', 'in', self.red_task_picking_ids.ids)],
            'views': form_view,
            'res_model': 'roke.mes.stock.picking'
        }

    def _get_default_picking_type(self):
        picking_type = self.env['roke.mes.stock.picking.type'].search([
            ('picking_logotype', '=', 'SCLLD')
        ])
        if picking_type:
            return picking_type[:1]

    def _get_demand_create_picking_line_vals(self, demand):
        """
        获取bom生成调拨单的数据
        """
        picking_type = self._get_default_picking_type()
        if not picking_type:
            raise ValidationError("未找到默认的调拨类型，请创建业务标识为生产领料单的库存作业类型")
        return {
            "pt_demand_id": demand.id,
            "picking_type_id": picking_type.id,
            "material_id": demand.material_id.id,
            "demand_qty": demand.demand_qty,
            "qty": demand.demand_qty - demand.receive_qty
        }

    def _get_can_picking_demand(self, task):
        """
        获取可领料的下级物料
        :param task: 生产任务
        :param first: 是否只领第一级
        :return:
        """
        pt_demands = task.pt_demand_ids.filtered(
            lambda demand: demand.first and demand.material_id.type in (
                "原材料", "其它") and demand.demand_qty > demand.receive_qty
        )
        for child_pt in task.child_pt_ids:
            pt_demands += self._get_can_picking_demand(child_pt)
        return pt_demands

    def create_bom_stock_picking(self):
        """
        根据bom生成调拨单，只领第一级
        :return:
        """
        if not self.e_bom_id:
            return
        line_vals = []
        pt_demands = self.pt_demand_ids.filtered(
            lambda demand: demand.first and demand.demand_qty > demand.receive_qty
        )
        for demand in pt_demands:
            line_vals.append(
                (0, 0, self._get_demand_create_picking_line_vals(demand)))
        res = self.env["roke.bom.create.picking.wizard"].create({
            "line_ids": line_vals
        })
        return {
            'name': 'bom领料',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.bom.create.picking.wizard'
        }

    def create_pt_demand_stock_picking(self):
        """
        批量领下级物料，领下一级和下级任务的下级物料
        :return:
        """
        line_vals = []
        pt_demands = self._get_can_picking_demand(self)
        for demand in pt_demands:
            line_vals.append(
                (0, 0, self._get_demand_create_picking_line_vals(demand)))
        if not line_vals:
            raise ValidationError("所有下级原材料均已完成领料或不需要领料")
        res = self.env["roke.bom.create.picking.wizard"].create({
            "line_ids": line_vals
        })
        return {
            'name': '批量领料',
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'target': 'new',
            'res_id': res.id,
            'res_model': 'roke.bom.create.picking.wizard'
        }

    def refresh_material_demand(self):
        """
        刷新物料需求
        :return:
        """
        if self.pt_demand_ids.receive_move_ids:
            raise ValidationError(
                "当前下级物料已领料，禁止刷新。如过需要刷新下级物料，请先取消现有下级物料的领料单。（联查-BOM领料单-取消-删除）")
        return super(InheritProductionTask, self).refresh_material_demand()

    def action_view_codes(self):
        result = self.env["ir.actions.actions"]._for_xml_id(
            'roke_mes_stock.view_production_roke_mes_stock_lot_action')
        if len(self.code_ids) == 1:
            res = self.env.ref(
                'roke_mes_stock.view_production_roke_mes_stock_lot_form', False)
            form_view = [(res and res.id or False, 'form')]
            if 'views' in result:
                result['views'] = form_view + \
                    [(state, view)
                     for state, view in result['views'] if view != 'form']
            else:
                result['views'] = form_view
            result['res_id'] = self.code_ids[0].id
        else:
            result["context"] = {"search_default_groupby_product_id": False}
            result['domain'] = "[('id','in', %s)]" % (self.code_ids.ids)
        return result

    @api.onchange("plan_qty", "product_id")
    def _onchange_plan_qty(self):
        """
        计算计划数量
        :return:
        """
        if self.product_id.track_type == "unique":
            if int(self.plan_qty) < self.plan_qty:
                raise ValidationError('单件打码，计划数量应为整数。')

    @api.model
    def create(self, values):
        res = super(InheritProductionTask, self).create(values)
        for record in res:
            record.generate_code()
        return res

    def write(self, values):
        res = super(InheritProductionTask, self).write(values)
        if "plan_qty" in values.keys() and self.product_id.track_type == "unique":
            for record in self:
                record.generate_code()
        return res

    def generate_code(self):
        """
        生成条码
        :return:
        """
        code_model = self.env["roke.mes.stock.lot"]
        if self.product_id.track_type == "unique":
            self.code_ids.unlink()
            self.code_ids = [(0, 0, {
                "name": f"{self.code}_{num + 1}",
                "product_id": self.product_id.id,
                "task_id": self.id
            }) for num in range(int(self.plan_qty))]
        elif self.product_id.track_type == "lot":
            if not self.lot_code:
                rule_id = self.product_id.lot_rule_id
                self.lot_code = rule_id.next_code()
            self.code_ids.unlink()
            self.code_ids = [(0, 0, {
                "name": self.lot_code,
                "product_id": self.product_id.id,
                "task_id": self.id
            })]

    def print_task_code(self):
        template_id = self.env.ref(
            "roke_mes_stock.template_stock_lot_code", raise_if_not_found=False)
        docids = []
        for record in self:
            for code_id in record.code_ids:
                docids.append(code_id)
        return self.do_print(template_id.print_data_ids[0], docids)

    def do_print(self, template_id, docids):
        code_ids = []
        task_ids = []

        for code_id in docids:
            code_ids.append(code_id.id)
            task_ids.append(code_id.task_id.id)
            self.env["roke.order.print.record"].sudo().set_print_record(
                "roke.mes.stock.lot", code_id.id, "PC生产任务条码打印"
            )
        for task_id in list(set(task_ids)):
            self.env["roke.order.print.record"].sudo().set_print_record(
                "roke.production.task", task_id, "PC生产任务打印"
            )

        return {
            'id': template_id.action_id.id,
            'name': template_id.action_id.name,
            'type': 'ir.actions.report',
            'binding_type': 'report',
            'report_type': 'qweb-html',
            'report_name': template_id.action_id.report_name,
            'xml_id': template_id.qweb_id.xml_id,
            'help': False,
            'binding_model_id': (template_id.model_id.id, template_id.model_id.name),
            'binding_view_types': 'list,form',
            'display_name': template_id.action_id.name,
            'context': {'active_ids': code_ids}
        }

    def multiple_create_bom_stock_picking(self):
        # 批量生成bom领料
        for rec in self:
            if not rec.e_bom_id:
                return
            line_vals = []
            pt_demands = rec.pt_demand_ids.filtered(
                lambda demand: demand.first and demand.demand_qty > demand.receive_qty
            )
            for demand in pt_demands:
                line_vals.append(
                    (0, 0, self._get_demand_create_picking_line_vals(demand)))
            res = self.env["roke.bom.create.picking.wizard"].create({
                "line_ids": line_vals
            })
            res.confirm(multiple=True)

