from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError
import xlwt
import xlrd
from io import BytesIO
import base64
import logging
import datetime

_logger = logging.getLogger(__name__)
_s_date = datetime.date(1899, 12, 31).toordinal() - 1


class RokeBomCreatePickingWizard(models.TransientModel):
    _name = "roke.bom.create.picking.wizard"
    _description = "bom领料"

    picking_purpose_id = fields.Many2one("roke.picking.purpose", string="领料用途")
    line_ids = fields.One2many("roke.bom.create.picking.line.wizard", "wizard_id", string="bom领料明细")

    def _get_picking_line_vals(self, src_location, dest_location, line):
        """获取调拨单需求"""
        return {
            "src_location_id": src_location.id,
            "dest_location_id": dest_location.id,
            "product_id": line.material_id.id,
            "qty": line.qty,
            "pt_demand_id": line.pt_demand_id.id
        }

    def confirm(self, multiple=False):
        task_id = self.env["roke.production.task"].browse(self._context.get('active_id'))
        picking_type_list = []
        for line in self.line_ids:
            if line.picking_type_id not in picking_type_list:
                picking_type_list.append(line.picking_type_id)
        picking_vals = []
        for picking_type in picking_type_list:
            src_location = picking_type.src_location_id
            dest_location = picking_type.dest_location_id
            picking_line_vals = []
            for line in self.line_ids:
                if line.qty <= 0:
                    continue
                if line.picking_type_id == picking_type:
                    picking_line_vals.append((0, 0, self._get_picking_line_vals(src_location, dest_location, line)))
            picking_vals.append({
                "state": "草稿",
                "picking_type_id": picking_type.id,
                "origin": task_id.code,
                "partner_id": task_id.customer_id.id,
                "src_location_id": src_location.id,
                "dest_location_id": dest_location.id,
                "move_line_ids": picking_line_vals,
                "picking_purpose_id": self.picking_purpose_id.id
            })
        pickings = self.env["roke.mes.stock.picking"].create(picking_vals)
        if multiple:
            # 更新创建时间
            for picking in pickings:
                self.env.cr.execute(f"""UPDATE {picking._name.replace(".", "_")}
                                        SET picking_date='{task_id.create_date.date().strftime("%Y-%m-%d")}', 
                                            create_date='{datetime.datetime.combine(task_id.create_date.date(), datetime.time.min).strftime("%Y-%m-%d %H:%M:%S")}'
                                        WHERE id = {picking.id}""")

        new_picking_ids = task_id.bom_picking_ids.ids + pickings.ids
        task_id.write({"bom_picking_ids": [(6, 0, new_picking_ids)]})
        view_tree = self.env.ref('roke_mes_stock.view_roke_mes_stock_general_out_tree', False)
        view_form = self.env.ref('roke_mes_stock.view_roke_mes_production_out_form', False)
        form_view = [(view_tree.id or False, 'tree'),
                     (view_form.id or False, 'form')]
        if not multiple:
            if len(pickings) == 1:
                return {
                    'name': '本次创建的领料单',
                    'type': 'ir.actions.act_window',
                    'view_mode': 'form',
                    'target': 'current',
                    'res_id': pickings.id,
                    'res_model': 'roke.mes.stock.picking',
                    'views': [(view_form.id or False, 'form')]
                }
            else:
                return {
                    'name': '本次创建的领料单',
                    'type': 'ir.actions.act_window',
                    'view_mode': 'tree,form',
                    'target': 'current',
                    'domain': [("id", "in", pickings.ids)],
                    'views': [(view_tree.id or False, 'tree'),
                              (view_form.id or False, 'form')],
                    'res_model': 'roke.mes.stock.picking'
                }


class RokeBomCreatePickingLineWizard(models.TransientModel):
    _name = "roke.bom.create.picking.line.wizard"
    _description = "bom领料明细"

    wizard_id = fields.Many2one("roke.bom.create.picking.wizard", string="bom领料")
    picking_type_id = fields.Many2one("roke.mes.stock.picking.type", string="业务类型", domain=[
        ('type', '=', '出库'),
        ('src_location_id.location_type', '=', "内部位置"),
        ('dest_location_id.location_type', '=', "生产位置")
    ])
    pt_demand_id = fields.Many2one("roke.pt.material.demand", string="任务物料需求")
    material_id = fields.Many2one("roke.product", string="物料")
    demand_qty = fields.Float(string="需求数", digits='KCSL')
    qty = fields.Float(string="领料数", digits='KCSL')
