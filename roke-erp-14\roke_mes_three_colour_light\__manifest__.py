{
    'name': '物联网灯管理',
    'depends': ['roke_workstation_api', 'roke_mes_equipment'],
    'author': 'www.rokedata.com',
    'website': 'http://www.rokedata.com',
    'description': """

    """,
    'data': [
        'security/ir.model.access.csv',
        'data/ir_cron.xml',
        'views/template.xml',
        'views/roke_stack_light.xml',
        'views/inherit_roke_abnormal_alarm.xml',
        'views/roke_three_color_light_view.xml',
        'views/inherit_res_company.xml',
        'report/work_order_hours_report.xml',
        'views/menus.xml',
    ],
    'demo': [
        # 'data/demo.xml',
    ],
    'qweb':[
        "static/src/xml/roke_three_color_light_iframe.xml"
    ],
    'application': True,
    'installable': True,
    'auto_install': False,
}
