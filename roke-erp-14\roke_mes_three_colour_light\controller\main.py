import os
import datetime
import logging
import requests
from odoo.addons.roke_mes_client.controller import login as mes_login
from odoo import http, tools, SUPERUSER_ID, fields
from jinja2 import FileSystemLoader, Environment
import pytz
import json
from dateutil.relativedelta import relativedelta
_logger = logging.getLogger(__name__)

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static/src/view")
env = Environment(loader=templateloader)


class RokeMesThreeColourLight(http.Controller):
    @http.route("/roke/three_color_light/device_state_list", type="http", auth='none', cors='*', csrf=False)
    def device_state_list(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('equipment_status.html')
        return template.render(data)
    
    @http.route("/roke/three_color_light/device_state_list_1", type="http", auth='none', cors='*', csrf=False)
    def device_state_list_1(self, **kwargs):
        _self = http.request
        factory_code = "66c6bd8c-fd58-11ef-9692-00163e04c506"  # 天合堂
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('equipment_status_1.html')
        return template.render(data)
    
    @http.route("/roke/three_color_light/device_state_list_2", type="http", auth='none', cors='*', csrf=False)
    def device_state_list_2(self, **kwargs):
        _self = http.request
        factory_code = "8d8dec6e-0d44-11f0-9692-00163e04c506"  # 荏原
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('equipment_status_2.html')
        return template.render(data)

    @http.route("/roke/three_color_light/device_analysis", type="http", auth='none', cors='*', csrf=False)
    def device_analysis(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('oee_analysis.html')
        return template.render(data)

    @http.route("/roke/three_color_light/oee_time_sequence_table", type="http", auth='none', cors='*', csrf=False)
    def oee_time_sequence_table(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('oee_time_sequence_table.html')
        return template.render(data)

    @http.route("/roke/three_color_light/oee_summary_table", type="http", auth='none', cors='*', csrf=False)
    def oee_summary_table(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('oee_summary_table.html')
        return template.render(data)

    @http.route("/roke/three_color_light/oee_monthly_summary_table", type="http", auth='none', cors='*', csrf=False)
    def oee_monthly_summary_table(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('oee_monthly_summary_table.html')
        return template.render(data)

    @http.route("/roke/three_color_light/device_table", type="http", auth='none', cors='*', csrf=False)
    def device_table(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('device_table.html')
        return template.render(data)
    
    @http.route("/roke/three_color_light/oee_device_working_rate", type="http", auth='none', cors='*', csrf=False)
    def oee_device_working_rate(self, **kwargs):
        _self = http.request
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        data = {"code": 1, "message": "请求通过", "data": {"factory_code": factory_code}}
        template = env.get_template('oee_device_working_rate.html')
        return template.render(data)
    
    def get_notify_users(self, stack_light_config):
        # 获取通知用户
        notify_users = []
        if stack_light_config.notify_user_ids:
            for user in stack_light_config.notify_user_ids:
                notify_users.append({
                    "id": user.id,
                    "name": user.name,
                    "employee_id": user.employee_id.id,
                    "employee_name": user.employee_id.name,
                })
        # 如果没有通知用户，则获取通知用户组的用户
        elif stack_light_config.notify_group_id:
            for user in stack_light_config.notify_group_id.users:
                notify_users.append({
                    "id": user.id,
                    "name": user.name,
                    "employee_id": user.employee_id.id,
                    "employee_name": user.employee_id.name,
                })
        return notify_users
    
    @http.route('/get/workstation/today/abnormal', type='json', auth="user", csrf=False, cors='*')
    def get_today_abnormal_order(self):
        """获取今日异常数据"""
        user_tz = pytz.timezone(http.request.env.context.get('tz', "Asia/Shanghai"))
        today = datetime.datetime.now(user_tz)
        start_time = datetime.datetime.combine(today, datetime.time(0, 0, 0)) - relativedelta(hours=8)
        end_time = datetime.datetime.combine(today, datetime.time(23, 59, 59)) - relativedelta(hours=8)
        abnormal_alarm_ids = http.request.env["roke.abnormal.alarm"].search([('create_date', '>=', start_time), ('create_date', '<=', end_time)])
        hour_data = []
        for i in range(6):
            hour_time = today - relativedelta(hours=8+i)
            if hour_time.day != today.day:
                break
            start_hour_time = datetime.datetime.combine(hour_time, datetime.time(hour_time.hour, 0, 0))
            end_hour_time = datetime.datetime.combine(hour_time, datetime.time(hour_time.hour, 59, 59))
            hour_data.append({
                (today - relativedelta(hours=i)).hour: len(abnormal_alarm_ids.filtered(lambda x: x.create_date >= start_hour_time and x.create_date <= end_hour_time)),
            })
        general_list = []
        res = self.get_abnormal_type_list()
        for item in res['abnormal_type_list']:
            general_list.append({
                "name": item.get('name'),
                "count": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.id == item.get('id')))
            })
        return {"code": 0, "message": "获取成功", "data": hour_data, "general_list": general_list}
    
    @http.route('/roke/get/abnormal_type_list', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_type_list(self):
        """
        获取异常类型列表
        :return: 返回异常类型的id和name字段，以列表形式返回
        """
        abnormal_type_ids = http.request.env['roke.abnormal.alarm.type'].search([])
        abnormal_type_list = []
        for item in abnormal_type_ids:
            stack_light_config = http.request.env['roke.stack.light.config'].search([
                ('name', '=', item.id)
            ], limit=1)
            notify_users = self.get_notify_users(stack_light_config)
            abnormal_type_list.append({
                "id": item.id,
                "name": item.name,
                "handle_employee_ids": [{"id": i.get('employee_id'), "name": i.get('employee_name')} for i in notify_users],
                "is_show": item.is_show
            })

        return {
            "state": "success",
            "msgs": "获取成功",
            "abnormal_type_list": abnormal_type_list
        }
    
    @http.route('/roke/get/stack_light_config', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_stack_light_config(self):
        """
        获取安灯配置数据
        :param abnormal_alarm_id: 异常表单ID
        :return: 安灯配置数据，包括通知用户、通知用户组、关闭人和发起人
        """
        try:
            # 获取请求参数
            jsonrequest = http.request.jsonrequest
            abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)

            if not abnormal_alarm_id:
                return {
                    "state": "error",
                    "msgs": "缺少必要参数: abnormal_alarm_id"
                }

            # 查询异常表单
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))

            if not abnormal_alarm.exists():
                return {
                    "state": "error",
                    "msgs": f"未找到ID为{abnormal_alarm_id}的异常表单"
                }

            # 查询与异常表单的异常类型一致的安灯配置
            stack_light_config = http.request.env['roke.stack.light.config'].search([
                ('name', '=', abnormal_alarm.abnormal_id.id)
            ], limit=1)

            if not stack_light_config:
                return {
                    "state": "error",
                    "msgs": f"未找到与异常类型'{abnormal_alarm.abnormal_id.name}'对应的安灯配置"
                }

            # 获取通知用户
            notify_users = self.get_notify_users(stack_light_config)
            # 获取关闭人
            close_user = None
            if stack_light_config.close_user_id:
                close_user = {
                    "id": stack_light_config.close_user_id.id,
                    "name": stack_light_config.close_user_id.name
                }
            else:
                close_user = {
                    "id": abnormal_alarm.sponsor.id,
                    "name": abnormal_alarm.sponsor.name
                }

            return {
                "state": "success",
                "msgs": "获取成功",
                "config_id": stack_light_config.id,
                "notify_users": notify_users,
                "close_user": close_user,
            }
        except Exception as e:
            return {
                "state": "error",
                "msgs": f"获取安灯配置数据失败: {str(e)}"
            }
        
    @http.route('/roke/dws_platform/message', type='json', auth="none", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def handle_dws_platform_message(self):
        """
        处理安灯信号
        :param plant_type: 车间类型，可选值：'停线'或'非停线'
        :param abnormal_name: 异常类型
        :param note: 备注
        :return: 创建结果
        """
        jsonrequest = http.request.jsonrequest
        _logger.info(f"处理安灯信号: {jsonrequest}")
        device_code = jsonrequest.get('device_code', '')
        color = jsonrequest.get('color', '')
        # 参数验证
        if not device_code:
            return {"state": "error", "msgs": "缺少必传参数: device_code"}
        if not color:
            return {"state": "error", "msgs": "缺少必传参数: color"}
        stack_light_id = http.request.env['roke.stack.light'].sudo().search([("code", "=", device_code)], limit=1)
        if not stack_light_id:
            return {"state": "error", "msgs": "未找到安灯盒子"}
        config_id = http.request.env['roke.stack.light.config'].sudo().search([("color", "=", color)], limit=1)
        if config_id:
            notify_users = self.get_notify_users(config_id)
            # 创建对应颜色的异常表单
            self.create_abnormal_alarm_by_system(config_id.name, stack_light_id, notify_users)
        elif color == "green":
            # 关闭异常表单
            alarm_ids = stack_light_id.alarm_ids.filtered(lambda x: x.abnormal_status in ["待确认", "待到场"])
            for alarm_id in alarm_ids:
                config_id = http.request.env['roke.stack.light.config'].sudo().search([("name", "=", alarm_id.abnormal_id.id)], limit=1)
                notify_users = self.get_notify_users(config_id)
                alarm_id.write({
                    'recipient': notify_users[0].get("id") if notify_users else alarm_id.recipient.id,
                    'confirm_time': fields.Datetime.now(),
                    'arrive_user_id': notify_users[0].get("id") if notify_users else alarm_id.recipient.id,
                    'arrive_time': fields.Datetime.now()
                })
        else:
            pass

    def create_abnormal_alarm_by_system(self, alarm_type_id, stack_light_id, notify_users):
        """
        创建异常表单
        :param alarm_type_id: 异常类型
        :param stack_light_id: 安灯盒子
        :return: 创建结果
        """
        if stack_light_id.alarm_ids.filtered(lambda x: x.abnormal_status != "处理完成" and x.abnormal_id == alarm_type_id):
            return {"state": "error", "msgs": "已存在未处理的异常表单"}
        # 创建异常表单
        abnormal_alarm = http.request.env['roke.abnormal.alarm'].sudo().create({
            "plant_type": "停线",
            "abnormal_id": alarm_type_id.id,
            "note": "自动创建",
            "sponsor": http.request.env.user.id,
            "originating_time": fields.Datetime.now(),
            "plant_id": stack_light_id.plant_id.id,
            "light_id": stack_light_id.id,
            "handle_employee_ids": [(6, 0, [i.get('employee_id') for i in notify_users])],
        })
        return abnormal_alarm
    
    @http.route('/roke/get/equipment_list', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_equipment_list(self):
        """
        获取设备列表
        :return:
        """
        domain = []
        jsonrequest = http.request.jsonrequest
        name = jsonrequest.get("name")
        if name:
            domain.append(("name", "ilike", name))
        plant_id = jsonrequest.get("plant_id")
        if plant_id:
            domain.append(("plant_id", "=", plant_id))
        category_id = jsonrequest.get("category_id")
        if category_id:
            domain.append(("category_id", "=", category_id))
        equipment_ids = http.request.env['roke.mes.equipment'].search(domain)
        equipment_list = []
        for item in equipment_ids:
            equipment_list.append({
                "id": item.id,
                "code": item.code,
                "name": item.name,
                "category_id": item.category_id.id,
                "category_name": item.category_id.name,
                "specification": item.specification,
                "manufacture_date": item.manufacture_date,
                "e_state": item.e_state,
            })
        return {
            "state": "success",
            "msgs": "获取成功",
            "equipment_list": equipment_list
        }
    
    @http.route('/roke/edit/abnormal_alarm', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def edit_abnormal_alarm(self):
        """
        编辑异常表单
        :return:
        """
        jsonrequest = http.request.jsonrequest
        _logger.info(f"edit_abnormal_alarm: {jsonrequest}")
        equipment_id = jsonrequest.get("equipment_id")
        alarm_id = jsonrequest.get("alarm_id")
        if not alarm_id:
            return {"state": "error", "msgs": "缺少必传参数：alarm_id"}
        alarm_id = http.request.env['roke.abnormal.alarm'].browse(alarm_id)
        val = {}
        _logger.info(f"edit_abnormal_alarm-equipment_id: {equipment_id}")
        if equipment_id:
            equipment_id = http.request.env['roke.mes.equipment'].browse(equipment_id)
            val["equipment_id"] = equipment_id.id
            val["work_center"] = equipment_id.work_center_id.id
            val["workshop_id"] = equipment_id.workshop_id.id
        try:
            alarm_id.write(val)
        except Exception as e:
            return {"state": "error", "msgs": e}
        return {"state": "success", "msgs": "更新成功"}
    
    def get_stack_light_state(self):
        """
        获取设备的安灯状态
        :return: 安灯状态
        """
        factory_code = http.request.env(user=SUPERUSER_ID)['ir.config_parameter'].get_param('database.uuid', default="")
        try:
            res = requests.get(f"https://dws-platform.xbg.rokeris.com/dev-api/public/device/count/{factory_code}")
            res_json = json.loads(res.text)
            equipment_state_list = [{"code": item.get("code"), "state": item.get("state")} for item in res_json.get("data", [])]
        except Exception as e:
            equipment_state_list = []
        return equipment_state_list

    
    @http.route('/roke/get/stack_light/state', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_stack_light_state_and_maintenance_info(self):
        """
        获取安灯状态和设备维保信息
        :return:
        """
        jsonrequest = http.request.jsonrequest
        equipment_code_list = jsonrequest.env["roke.mes.equipment"].sudo().search([("code", "!=", False)]).mapped("code")
        check_count = jsonrequest.env["roke.mes.eqpt.spot.check.record"].sudo().search_count([("state", "=", "in_progress")])
        repair_count = jsonrequest.env["roke.mes.maintenance.order"].sudo().search_count([("state", "=", "assign"), ("type", "=", "repair")])
        repair_count = jsonrequest.env["roke.mes.maintenance.order"].sudo().search_count([("state", "=", "assign"), ("type", "=", "repair")])
        data = {
            "total": len(equipment_code_list),
            "green": 0,
            "yellow": 0,
            "red": 0,
            "gray": 0,
            "check_count": check_count,
            "repair_count": repair_count,
            ""
        }
        equipment_state_list = self.get_stack_light_state()
        for item in equipment_state_list:
            if item.get("code") in equipment_code_list:
                data[item.get("state")] += 1
        return {
            "state": "success",
            "msgs": "获取成功",
            "data": data
        }


class Login(mes_login.Login):

    def get_openid(self, js_code):
        # 从系统参数获取 appid 和 secret
        appid = http.request.env['ir.config_parameter'].sudo().get_param('wechat.appid')
        secret = http.request.env['ir.config_parameter'].sudo().get_param('wechat.secret')

        # 微信接口换取 openid
        wx_url = (
            f"https://api.weixin.qq.com/sns/jscode2session?"
            f"appid={appid}&secret={secret}&js_code={js_code}&grant_type=authorization_code"
        )

        try:
            res = requests.get(wx_url)
            data = res.json()
            openid = data.get('openid', '')
        except Exception as e:
            _logger.error(f"get_openid: {e}")
            openid = ''
        return openid

    @http.route('/roke/mes/client_login', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False, cors='*')
    def roke_mes_login(self):
        res = super(Login, self).roke_mes_login()
        js_code = http.request.jsonrequest.get("js_code")
        user_id = http.request.env["res.users"].sudo().browse(res.get("user_id"))
        if js_code:
            if not user_id.openid:
                openid = self.get_openid(js_code)
                user_id.openid = openid
        res["app_version"] = user_id.company_id.app_version
        return res

