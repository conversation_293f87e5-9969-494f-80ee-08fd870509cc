import os
import datetime
import logging
import requests
from odoo.addons.roke_mes_client.controller import login as mes_login
from odoo import http, tools, SUPERUSER_ID, fields
from jinja2 import FileSystemLoader, Environment
import pytz
from dateutil.relativedelta import relativedelta

_logger = logging.getLogger(__name__)


def convert_utc_to_local(dt, tz_name='Asia/Shanghai'):
    if not dt:
        return ""
    # 假设传入的 dt 是字符串或 Odoo 的 datetime 字段
    if isinstance(dt, str):
        dt_utc = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
    else:
        dt_utc = dt

    # 设置 UTC 时间为 aware datetime
    dt_utc = pytz.utc.localize(dt_utc)

    # 转换为目标时区时间
    target_tz = pytz.timezone(tz_name)
    dt_local = dt_utc.astimezone(target_tz)

    return dt_local.strftime("%Y-%m-%d %H:%M:%S")


class RokeMesThreeColourLightScanCard(http.Controller):

    @http.route('/roke/three_color_light/scan_query', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def scan_query(self):
        """
        三色灯扫码查询接口
        :param post: {
            "barcode": "三色灯编码",
            "business_type": "check/maintain/repair/workorder"  # 业务类型
        }
        :return: 对应业务类型的列表数据
        """

        barcode = http.request.jsonrequest.get('barcode')
        business_type = http.request.jsonrequest.get('business_type')
        page_size = int(http.request.jsonrequest.get('page_size', 1))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        if not barcode or not business_type:
            return {"code": "100", "state": "error", "msg": "缺少必要参数", "data": []}

        equipment = http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([('code', '=', barcode)], limit=1)
        if not equipment:
            return {"code": "100", "state": "error", "msg": "未找到对应设备", "data": []}

        try:
            if business_type == 'check':
                # 设备点检
                return self._get_check_records(equipment, state="not_started", limit=page_size, offset=offset)
            elif business_type == 'maintain':
                # 设备保养
                return self._get_maintain_records(equipment, page_size, offset)
            elif business_type == 'repair':
                # 设备维修
                return self._get_repair_records(equipment, page_size, offset)
            elif business_type == 'workorder':
                # 工单
                return self._get_work_orders(equipment, page_size, offset)
            else:
                return {"code": "100", "state": "error", "msg": "不支持的业务类型", "data": []}
        except Exception as e:
            _logger.error(f"查询失败: {str(e)}")
            return {"code": "100", "state": "error", "msg": "查询失败", "data": []}

    @http.route('/roke/three_color_light/check_record/edit_line', type='json', methods=['POST', 'OPTIONS'], auth="none",
                csrf=False, cors='*')
    def check_record_edit_line(self):
        """
        点检记录行编辑接口：
        - 支持手动输入检查值和结果判定（normal/anomaly/fault）
        - 自动判断 normal_state
        - 点击确定调用此接口保存数据
        {
          "record_id": 123,  // 点检记录 ID
          "check_items": [
            {
              "line_id": 456,  // 检查项明细 ID
              "check_value": "100",  // 手动输入的检查值
              "result": "normal"  // 结果判定：normal/anomaly/fault
            },
            ...
          ]
        }

        """
        try:
            data = http.request.jsonrequest
            record_id = data.get("record_id")
            check_items = data.get("check_items", [])

            if not record_id or not check_items:
                return {"state": "error", "msg": "缺少必要参数"}

            # 获取当前点检记录
            spot_check_record = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.record'].browse(record_id)
            if not spot_check_record.exists():
                return {"state": "error", "msg": "点检记录不存在"}

            # 更新每一行检查项的数据
            for item in check_items:
                line_id = item.get("line_id")
                check_value = item.get("check_value")
                result = item.get("result")

                line = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.line'].browse(line_id)
                if line.exists() and line.record_id.id == record_id:
                    line.write({
                        "check_value": check_value,
                        "result": result
                    })

            # 判断是否全部完成
            all_lines = spot_check_record.item_record_ids
            is_check_finished = all(line.result not in ["wait", '', False] for line in all_lines)
            if not is_check_finished:
                http.request.env.cr.rollback()
                return {"state": "error", "msg": "结果判定未完成", "data": {
                    "normal_state": '',
                    "normal_state_name": ''
                }}
            else:
                normal_state = spot_check_record.normal_state
                normal_state_name = spot_check_record.get_selection_field_values("normal_state", normal_state)
                spot_check_record.make_finish()
                return {
                    "state": "success",
                    "msg": "",
                    "data": {
                        "normal_state": normal_state,
                        "normal_state_name": normal_state_name
                    }
                }

        except Exception as e:
            _logger.error(f"更新点检记录失败: {str(e)}")
            http.request.env.cr.rollback()
            return {"state": "error", "msg": f"更新失败: {str(e)}"}

    @http.route('/roke/three_color_light/check_record/create', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False,
                cors='*')
    def light_in_create_check_record(self):
        """创建设备点检单 前置/roke/get/check_plan_list"""
        equipment_id = http.request.jsonrequest.get('equipment_id', False)
        check_plan_id = http.request.jsonrequest.get('check_plan_id', False)
        finish_user_id = http.request.jsonrequest.get('finish_user_id', False) or http.request.env.user.id
        item_list = http.request.jsonrequest.get('item_list', [])
        if not all([equipment_id, check_plan_id, item_list]):
            return {"state": "error", "msgs": "缺少必传参数"}
        try:
            check_id = http.request.env['roke.mes.eqpt.spot.check.record'].create({
                "equipment_id": int(equipment_id),
                "check_plan_id": check_plan_id,
                "finish_user_id": finish_user_id,
                "assign_user_ids": [(4, http.request.env.user.id or SUPERUSER_ID)],
                "is_api": True,
                "description": "app 接口生成",
                "item_record_ids": [(0, 0, {
                    "check_item_id": item.get("check_item_id"),
                    "check_value": item.get("check_value"),
                    "result": item.get("result"),

                }) for item in item_list]
            })

            is_check_finished = all(line.result not in ["wait", '', False] for line in check_id.item_record_ids)
            if not is_check_finished:
                http.request.env.cr.rollback()
                return {"state": "error", "msg": "结果判定未完成", "data": {
                    "normal_state": None
                }}
            else:
                check_id.make_finish()
                return {"state": "success", "msgs": "创建成功", "record_id": check_id.id}
        except Exception as e:
            _logger.error(f"创建点检记录失败: {str(e)}")
            http.request.env.cr.rollback()
            return {"state": "error", "msg": f"创建失败: {str(e)}"}

    @http.route('/roke/three_color_light/maintain_record/edit_line', type='json', methods=['POST', 'OPTIONS'],
                auth="none", csrf=False, cors='*')
    def maintain_record_edit_line(self):
        """
        保养记录行编辑接口：
        - 支持手动输入操作描述和结果判定（normal/anomaly/fault）
        - 自动判断 normal_state
        - 点击确定调用此接口保存数据
        {
          "record_id": 123,
          "maintain_items": [
            {
              "line_id": 456,
              "description": "设备润滑完成",
              "result": "normal"
            },
            ...
          ]
        }
        """
        try:
            data = http.request.jsonrequest
            record_id = data.get("record_id")
            maintain_items = data.get("maintain_items", [])

            if not record_id or not maintain_items:
                return {"state": "error", "msg": "缺少必要参数"}

            # 获取当前保养记录
            maintain_record = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].browse(record_id)
            if not maintain_record.exists():
                return {"state": "error", "msg": "保养记录不存在"}

            # 更新每一行保养项的数据
            for item in maintain_items:
                line_id = item.get("line_id")
                description = item.get("description")
                result = item.get("result")
                line = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order.item'].browse(int(line_id))
                line.write({
                        "description": description,
                        "result": result,
                        "state": 'finish'
                    })

            # # 判断是否全部完成
            # is_check_finished = all(line.result not in ["wait", '', False] for line in all_lines)
            #
            # if not is_check_finished:
            #     http.request.env.cr.rollback()
            #     return {"state": "error", "msg": "结果判定未完成", "data": {
            #         "normal_state": '',
            #         "normal_state_name": ''
            #     }}
            normal_state = maintain_record.normal_state
            normal_state_name = maintain_record.get_selection_field_values("normal_state", normal_state)

            # 更新状态为完成
            maintain_record.write({
                "state": "finish",
                "finish_time": fields.Datetime.now(),

            })

            return {
                "state": "success",
                "msg": "",
                "data": {
                    "normal_state": normal_state,
                    "normal_state_name": normal_state_name
                }
            }

        except Exception as e:
            _logger.error(f"更新保养记录失败: {str(e)}")
            http.request.env.cr.rollback()
            return {"state": "error", "msg": f"更新失败: {str(e)}"}

    @http.route('/roke/three_color_light/maintain_record/create', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def light_in_create_maintenance(self):
        """
        创建保养任务 qianzhi - > /maintenance/scheme/items
        :return:
        """
        try:
            equipment_id = http.request.jsonrequest.get('equipment_id', False)
            maintenance_scheme = http.request.jsonrequest.get('maintenance_scheme', False)
            priority = http.request.jsonrequest.get('priority', False)
            repair_user_id = http.request.jsonrequest.get('repair_user_id', False)
            items_list = http.request.jsonrequest.get('items_list', False)
            user_id = http.request.env.user
            request = http.request.env['roke.mes.maintenance.order'].sudo()
            vals = {
                "code": http.request.env['ir.sequence'].sudo().search([('code', '=', 'roke.mes.repair.code')],
                                                                      limit=1)._next(),
                "type": 'maintain',
                "user_id": (user_id and user_id.id) or 2,
                "equipment_id": int(equipment_id),
                "maintenance_scheme_id": int(maintenance_scheme),
                "priority": priority,
                "repair_user_id": int(repair_user_id),
                "item_ids": []
            }

            for item in items_list:
                vals['item_ids'].append((0, 0, {
                    "item_id": int(item.get('item_id')),
                    "description": item.get('description'),
                    "maintenance_description": item.get('maintenance_description'),
                    "execute_user_id": (user_id and user_id.id) or 2,
                    "result": item.get('result'),
                    "state": 'finish',
                }))
            maintain_record = request.create(vals)
            # 判断是否全部完成
            all_lines = maintain_record.item_ids
            is_check_finished = all(line.result not in ["wait", '', False] for line in all_lines)

            if not is_check_finished:
                http.request.env.cr.rollback()
                return {"state": "error", "msg": "结果判定未完成", "data": {
                    "normal_state": '',
                    "normal_state_name": ''
                }}
            # 更新状态为完成
            maintain_record.write({
                "state": "finish",
                "finished":True,
                "finish_time": fields.Datetime.now(),

            })
            return {"state": "success", "msgs": "创建成功", 'task_id': maintain_record.id}
        except Exception as e:
            _logger.error(f"创建记录失败: {str(e)}")
            http.request.env.cr.rollback()
            return {"state": "error", "msg": f"创建失败: {str(e)}"}

    @http.route('/roke/three_color_light/repair_record/edit', type='json', methods=['POST', 'OPTIONS'], auth="none",
                csrf=False, cors='*')
    def repair_record_edit(self):
        """
        维修记录编辑接口：
        {
          "record_id": 123,  // 维修记录 ID
          "fault_description": "故障描述",
          "start_date": "2023-01-01 10:00:00",  // 本地时间 (UTC+8)
          "finish_time": "2023-01-01 12:00:00", // 本地时间 (UTC+8)
          "repair_description": "维修描述"
        }
        """
        try:
            data = http.request.jsonrequest
            record_id = data.get("record_id")
            if not record_id:
                return {"state": "error", "msg": "缺少维修记录ID"}

            repair_record = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].browse(record_id)
            if not repair_record.exists():
                return {"state": "error", "msg": "维修记录不存"}

            vals = {}
            if 'fault_description' in data:
                vals['fault_description'] = data.get('fault_description')
            if 'start_date' in data:
                start_date = datetime.datetime.strptime(data.get('start_date'), "%Y-%m-%d %H:%M:%S") - datetime.timedelta(
                    hours=8)
                vals['start_date'] = start_date.strftime("%Y-%m-%d %H:%M:%S")
            if 'finish_time' in data:
                finish_time = datetime.datetime.strptime(data.get('finish_time'), "%Y-%m-%d %H:%M:%S") - datetime.timedelta(
                    hours=8)
                vals['finish_time'] = finish_time.strftime("%Y-%m-%d %H:%M:%S")
            if 'repair_description' in data:
                vals['repair_description'] = data.get('repair_description')

            if vals:
                vals['state'] = 'finish'  # 直接添加 state 到 vals
                vals['finished'] = True
                repair_record.write(vals)  # 然后直接写入

                return {
                    "state": "success",
                    "msg": "更新成功",
                    "data": {
                        "id": repair_record.id,
                    }
                }
            else:
                return {"state": "error", "msg": "无更新的内容"}

        except Exception as e:
            _logger.error(f"更新维修记录失败: {str(e)}")
            return {"state": "error", "msg": f"更新失败: {str(e)}"}

    @http.route('/roke/three_color_light/work_order/create', type='json', methods=['POST', 'OPTIONS'], auth="user",
                csrf=False, cors='*')
    def create_work_order(self):
        """
        创建工单接口
        入参:

        出参:

        """
        try:
            data = http.request.jsonrequest
            equipment_id = data.get('equipment_id', False)

            if not all(key in data for key in ['process_id', 'product_id', 'plan_qty', 'salary_type', 'priority']):
                return {"state": "error", "msg": "缺少必填参数"}

            # Create work order values
            vals = {
                'code': http.request.env(user=SUPERUSER_ID)['ir.sequence'].next_by_code('roke.work.order.code'),
                'process_id': data['process_id'],
                'product_id': data['product_id'],
                'plan_qty': data['plan_qty'],
                'priority': data.get('priority', '1级'),
                'salary_type': data['salary_type'],
                'employee_ids': [(6, 0, data.get('employee_ids', []))],
                'state': '未开工',
                'dispatch_time': fields.Datetime.now(),
                "type": "生产",
                "equipment_id": int(equipment_id),
            }

            work_order = http.request.env(user=SUPERUSER_ID)['roke.work.order'].create(vals)

            return {
                "state": "success",
                "msg": "工单创建成功",
                "data": {
                    "work_order_id": work_order.id,
                    "code": work_order.code
                }
            }

        except Exception as e:
            _logger.error(f"创建工单失败: {str(e)}")
            return {"state": "error", "msg": f"创建工单失败: {str(e)}"}

    def get_equipment_repair_list(self, equipment_id, type="repair", limit=1, offset=0):
        """获取单个设备报修记录"""
        record_list = []
        if type == "repair":
            repair_ids = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(
                [("equipment_id", "=", equipment_id), ("type", "=", type), ("state", '=','assign')],
                limit=limit, offset=offset, order="report_time desc")
            for item in repair_ids:
                record_list.append({
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "deadline": item.deadline.strftime(
                        '%Y-%m-%d') if item.deadline else "",
                    "repair_description": item.repair_description,
                    "state": item.get_selection_field_values("state", item.state),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d %H:%M:%S') if item.finish_time else "",
                    "equipment_name": item.equipment_id.name,
                    "equipment_id": item.equipment_id.id,
                    "priority": item.priority,
                    "priority_name": item.get_selection_field_values("priority", item.priority),
                    "last_maintenance_date": item.last_maintenance_date.strftime(
                        '%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                    "item_list": []
                })

        elif type == "maintain":
            repair_ids = http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(
                [("equipment_id", "=", equipment_id), ("type", "=", type), ('maintain_state', '=', 'not_started')],
                limit=limit, offset=offset, order="create_date desc")
            for item in repair_ids:
                vals = {
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "state": item.get_selection_field_values("state", item.state),
                    "maintain_state": item.get_selection_field_values("maintain_state", item.maintain_state),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.create_date + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.create_date else "",
                    "equipment_name": item.equipment_id.name,
                    "equipment_id": item.equipment_id.id,
                    "priority": item.priority,
                    "priority_name": item.get_selection_field_values("priority", item.priority),
                    "last_maintenance_date": (item.last_maintenance_date + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                    "user_name": item.user_id.name,
                    "deadline": item.deadline.strftime(
                        '%Y-%m-%d') if item.deadline else "",
                }
                item_list = []
                for item_line in item.item_ids:
                    item_list.append({
                        "line_id": item_line.id,
                        "item_id": item_line.item_id.id,
                        "item_name": item_line.item_id.name,
                        "item_code": item_line.item_id.code,
                        "execute_user_id": item_line.execute_user_id.id,
                        "execute_user_name": item_line.execute_user_id.name,
                        "execute_time": (item_line.execute_time + datetime.timedelta(hours=8)).strftime(
                            '%Y-%m-%d') if item_line.execute_time else "",
                        "execute_description": item_line.description,
                        "maintenance_description": item_line.maintenance_description,
                    })
                vals["item_list"] = item_list
                record_list.append(vals)

        return record_list

    def _get_check_records(self, equipment_id, state='not_started', limit=1, offset=0):
        record_list = []
        record_ids = http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.record'].search(
            [("equipment_id", "=", equipment_id.id),
             ("state", '=', state)],
            limit=limit, offset=offset, order="create_date desc")
        for item in record_ids:
            record_list.append({
                "id": item.id,
                "code": item.code,
                "check_plan_id": item.check_plan_id.id,
                "check_plan_name": item.check_plan_id.name if item.check_plan_id else "",
                "assign_user_name": item.assign_user_ids[0].name if item.assign_user_ids else "",
                "equipment_id": item.equipment_id.id,
                "equipment_name": item.equipment_id.name if item.equipment_id else "",
                "start_date": (item.start_date + datetime.timedelta(hours=8)).strftime(
                    "%Y-%m-%d %H:%M:%S") if item.start_date else "",
                "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime(
                    '%Y-%m-%d') if item.finish_time else "",
                "state": item.get_selection_field_values("state", item.state),
                "description": item.description,
                "finish_user_id": item.finish_user_id.id,
                "finish_user_name": item.finish_user_id.name if item.finish_user_id else "",
                "item_record_names": ",".join(
                    [item_id.check_item_id.name for item_id in item.item_record_ids]) if item.item_record_ids else "",
                "item_record_list": [
                    {
                        "id": item_id.id,
                        "check_item_id": item_id.check_item_id.id,
                        "check_item_name": item_id.check_item_id.name,
                        "check_value": item_id.check_value,
                        "result": item_id.result,
                        "result_name": item_id.get_selection_field_values("result", item_id.result),
                        "description": item_id.description,
                    } for item_id in item.item_record_ids
                ]
            })

        return {"state": "success", "msgs": "", "records": record_list, "equipments": {
            "equipment_id": equipment_id.id,
            "equipment_name": equipment_id.name,
            "equipment_code": equipment_id.code,
        }}

    def _get_maintain_records(self, equipment_id, limit=1, offset=0):
        """获取设备保养记录"""
        result = self.get_equipment_repair_list(equipment_id.id, type="maintain", limit=limit, offset=offset)
        return {"state": "success", "msgs": "", "records": result, "equipments": {
            "equipment_id": equipment_id.id,
            "equipment_name": equipment_id.name,
            "equipment_code": equipment_id.code,
        }

                }

    def _get_repair_records(self, equipment_id, limit=1, offset=0):
        """获取设备维修记录"""
        result = self.get_equipment_repair_list(equipment_id.id, type="repair", limit=limit, offset=offset)
        return {"state": "success", "msgs": "获取成功", "records": result,
                "equipments": {
                    "equipment_id": equipment_id.id,
                    "equipment_name": equipment_id.name,
                    "equipment_code": equipment_id.code,
                }
                }

    def _get_work_order_details(self, equipment_id, product_id=False, state="", zuoye_id=False, limit=False,
                                page=False):
        domain = [('equipment_id', '=', equipment_id)]
        if product_id:
            domain.append(("product_id", "=", product_id))
        if state:
            domain.append(("state", "=", state))  # # 未开工/未完成/强制完工/完工
        if zuoye_id:
            domain.append(('record_ids.employee_ids', '=', zuoye_id))
        if limit and page:
            offset = (page - 1) * limit
            all_roke_work_orders = http.request.env(user=SUPERUSER_ID)["roke.work.order"].sudo().search(domain,
                                                                                                        limit=limit,
                                                                                                        offset=offset,
                                                                                                        order="create_date desc")
        else:
            all_roke_work_orders = http.request.env(user=SUPERUSER_ID)["roke.work.order"].sudo().search(domain)

        if not all_roke_work_orders:
            return {"code": "200", "state": "success", "msg": "该设备对应的产品或者状态或者作业人,没有工单", "data": []}

        work_order_list = list()
        for work_order in all_roke_work_orders:
            zuoye_names = work_order.record_ids.mapped("employee_ids").mapped('name')
            if zuoye_names:
                zuoye_names = ",".join(work_order.record_ids.mapped("employee_ids").mapped('name'))
            else:
                zuoye_names = ""
            allow_qty, default_qty = work_order._get_wo_allow_qty()
            vals = {
                "work_order_id": work_order.id,
                "code": work_order.code,
                "state": work_order.state,
                'product_id': work_order.product_id.id,
                "product_name": work_order.product_id.name,
                "plan_qty": work_order.plan_qty,
                "process_name": work_order.process_id.name or '',
                "finish_qty": work_order.finish_qty,
                "no_finish_qty": work_order.plan_qty - work_order.finish_qty,
                "employee_names": ",".join(work_order.employee_ids.mapped("name")),  # 指派人
                "zuoye_names": zuoye_names,  # 作业任务 报工记录的人员
                "uom_id": work_order.uom_id.id,
                'uom_name': work_order.uom_id.name,
                "wo_start_time": convert_utc_to_local(work_order.wo_start_time)[5:] if work_order.wo_start_time else "",
                # 开工时间
                "task_id": work_order.task_id.id or None,
                "task_code": work_order.task_id.code or '',
                # "work_center_id": work_order.work_center_id.id,
                # "work_center_name": work_order.work_center_id.name,
                "workshop_id": work_order.workshop_id.id or None,
                "workshop_name": work_order.workshop_id.name or '',  # 工作中心
                "dispatch_time": convert_utc_to_local(work_order.dispatch_time),  # 下单日期
                'wait_qty': allow_qty,
            }
            work_order_list.append(vals)
        result = {
            "code": "200",
            "state": "success",
            "msg": "",
            "data": work_order_list,
        }
        return result

    def _get_work_orders(self, equipment_id, limit=False, page=False):
        """获取工单记录"""
        result = self._get_work_order_details(equipment_id.id, limit=limit, page=page)
        result.update({
            "equipments": {
            "equipment_id": equipment_id.id,
            "equipment_name": equipment_id.name,
            "equipment_code": equipment_id.code,
        }})
        return result
