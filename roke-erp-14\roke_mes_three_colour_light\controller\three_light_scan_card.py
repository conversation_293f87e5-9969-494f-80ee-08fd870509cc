import os
import datetime
import logging
import requests
from odoo.addons.roke_mes_client.controller import login as mes_login
from odoo import http, tools, SUPERUSER_ID, fields
from jinja2 import FileSystemLoader, Environment
import pytz
from dateutil.relativedelta import relativedelta
_logger = logging.getLogger(__name__)

def convert_utc_to_local(dt, tz_name='Asia/Shanghai'):
    if not dt:
        return ""
    # 假设传入的 dt 是字符串或 Odoo 的 datetime 字段
    if isinstance(dt, str):
        dt_utc = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
    else:
        dt_utc = dt

    # 设置 UTC 时间为 aware datetime
    dt_utc = pytz.utc.localize(dt_utc)

    # 转换为目标时区时间
    target_tz = pytz.timezone(tz_name)
    dt_local = dt_utc.astimezone(target_tz)

    return dt_local.strftime("%Y-%m-%d %H:%M:%S")

class RokeMesThreeColourLightScanCard(http.Controller):

    @http.route('/roke/three_color_light/scan_query', type='json', methods=['POST', 'OPTIONS'], auth="none", csrf=False,
                cors='*')
    def scan_query(self):
        """
        三色灯扫码查询接口
        :param post: {
            "barcode": "三色灯编码",
            "business_type": "check/maintain/repair/workorder"  # 业务类型
        }
        :return: 对应业务类型的列表数据
        """

        barcode =  http.request.jsonrequest.get('barcode')
        business_type =  http.request.jsonrequest.get('business_type')
        page_size = int(http.request.jsonrequest.get('page_size', 1))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        offset = (page_no - 1) * page_size
        if not barcode or not business_type:
            return {"code": "100", "state": "error", "msg": "缺少必要参数", "data": []}

        equipment =http.request.env(user=SUPERUSER_ID)['roke.mes.equipment'].search([('code', '=', barcode)], limit=1)
        if not equipment:
            return {"code": "100", "state": "error", "msg": "未找到对应设备", "data": []}

        try:
            if business_type == 'check':
                # 设备点检
                return self._get_check_records(equipment.id,  page_size, offset)
            elif business_type == 'maintain':
                # 设备保养
                return self._get_maintain_records(equipment.id,  page_size, offset)
            elif business_type == 'repair':
                # 设备维修
                return self._get_repair_records(equipment.id,  page_size, offset)
            elif business_type == 'workorder':
                # 工单
                return self._get_work_orders(equipment.id,  page_size, offset)
            else:
                return {"code": "100", "state": "error", "msg": "不支持的业务类型", "data": []}
        except Exception as e:
            _logger.error(f"查询失败: {str(e)}")
            return {"code": "100", "state": "error", "msg": "查询失败", "data": []}

    def get_equipment_repair_list(self, equipment_id, type="repair",state='wait', limit=1, offset=0):
        """获取单个设备报修记录"""
        record_list = []
        if type == "repair":
            repair_ids =http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(
                [("equipment_id", "=", equipment_id), ("type", "=", type)],
                limit=limit, offset=offset, order="report_time desc")
            for item in repair_ids:
                record_list.append({
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "state": item.get_selection_field_values("state",item.state),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d %H:%M:%S') if item.finish_time else "",
                    "equipment_name": item.equipment_id.name,
                    "priority": item.priority,
                    "last_maintenance_date": item.last_maintenance_date.strftime(
                        '%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                    "item_list": []
                })

        elif type == "maintain":
            repair_ids =http.request.env(user=SUPERUSER_ID)['roke.mes.maintenance.order'].search(
                [("equipment_id", "=", equipment_id), ("type", "=", type),('state','=',state)],
                limit=limit, offset=offset, order="create_date desc")
            for item in repair_ids:
                vals = {
                    "id": item.id,
                    "report_user_id": item.report_user_id.id,
                    "report_user_name": item.report_user_id.name,
                    "repair_user_id": item.repair_user_id.id,
                    "repair_user_name": item.repair_user_id.name,
                    "report_time": (item.report_time + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.report_time else "",
                    "fault_description": item.fault_description,
                    "state": item.get_selection_field_values("state",item.state),
                    "maintenance_scheme": item.maintenance_scheme_id.name if item.maintenance_scheme_id else "",
                    "finish_time": (item.create_date + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.create_date else "",
                    "equipment_name": item.equipment_id.name,
                    "priority": item.priority,
                    "last_maintenance_date": (item.last_maintenance_date + datetime.timedelta(hours=8)).strftime(
                        '%Y-%m-%d') if item.last_maintenance_date else "",
                    "use_time": item.use_time,
                }
                item_list = []
                for item_line in item.item_ids:
                    item_list.append({
                        "item_id": item_line.item_id.id,
                        "item_name": item_line.item_id.name,
                        "item_code": item_line.item_id.code,
                        "execute_user_id": item_line.execute_user_id.id,
                        "execute_user_name": item_line.execute_user_id.name,
                        "execute_time": (item_line.execute_time + datetime.timedelta(hours=8)).strftime(
                            '%Y-%m-%d') if item_line.execute_time else "",
                        "execute_description": item_line.description,
                    })
                vals["item_list"] = item_list
                record_list.append(vals)

        return record_list

    def _get_check_records(self,equipment_id,state='wait', limit=1, offset=0):
        record_list = []
        record_ids =http.request.env(user=SUPERUSER_ID)['roke.mes.eqpt.spot.check.record'].search([("equipment_id", "=", equipment_id),
                                                                                                   ("state",'=',state)],
                                                                                limit=limit, offset=offset, order="create_date desc")
        for item in record_ids:
            record_list.append({
                "id": item.id,
                "code": item.code,
                "check_plan_id": item.check_plan_id.id,
                "check_plan_name": item.check_plan_id.name if item.check_plan_id else "",
                "assign_user_name": item.assign_user_ids[0].name if item.assign_user_ids else "",
                "finish_time": (item.finish_time + datetime.timedelta(hours=8)).strftime('%Y-%m-%d') if item.finish_time else "",
                "state": item.get_selection_field_values("state", item.state),
                "description": item.description,
                "finish_user_id": item.finish_user_id.id,
                "finish_user_name": item.finish_user_id.name if item.finish_user_id else "",
                "item_record_names": ",".join([item_id.check_item_id.name for item_id in item.item_record_ids]) if item.item_record_ids else "",
                "item_record_list": [
                    {
                        "id": item_id.id,
                        "check_item_id": item_id.check_item_id.id,
                        "check_item_name": item_id.check_item_id.name,
                        "check_value": item_id.check_value,
                        "result": item_id.result,
                        "result_name":item_id.get_selection_field_values("result",item_id.result),
                        "description": item_id.description,
                    } for item_id in item.item_record_ids
                ]
            })

        return {"state": "success", "msgs": "", "records": record_list}

    def _get_maintain_records(self, equipment_id,  limit=1, offset=0):
        """获取设备保养记录"""
        result = self.get_equipment_repair_list(equipment_id,  type="maintain", limit=limit, offset=offset)
        return {"state": "success", "msgs": "", "records": result}


    def _get_repair_records(self, equipment_id,  limit=1, offset=0):
        """获取设备维修记录"""
        result = self.get_equipment_repair_list(equipment_id,  type="repair", limit=limit, offset=offset)
        return {"state": "success", "msgs": "获取成功", "records": result}

    def _get_work_order_details(self, equipment_id, product_id=False, state="", zuoye_id=False, limit=False,
                                page=False):
        domain = [('equipment_id', '=', equipment_id)]
        if product_id:
            domain.append(("product_id", "=", product_id))
        if state:
            domain.append(("state", "=", state))  # # 未开工/未完成/强制完工/完工
        if zuoye_id:
            domain.append(('record_ids.employee_ids', '=', zuoye_id))
        if limit and page:
            offset = (page - 1) * limit
            all_roke_work_orders =http.request.env(user=SUPERUSER_ID)["roke.work.order"].sudo().search(domain, limit=limit, offset=offset)
        else:
            all_roke_work_orders =http.request.env(user=SUPERUSER_ID)["roke.work.order"].sudo().search(domain)

        if not all_roke_work_orders:
            return {"code": "200", "state": "success", "msg": "该设备对应的产品或者状态或者作业人,没有工单", "data": []}

        work_order_list = list()
        for work_order in all_roke_work_orders:
            zuoye_names = work_order.record_ids.mapped("employee_ids").mapped('name')
            if zuoye_names:
                zuoye_names = ",".join(work_order.record_ids.mapped("employee_ids").mapped('name'))
            else:
                zuoye_names = ""
            allow_qty, default_qty = work_order._get_wo_allow_qty()
            vals = {
                "work_order_id": work_order.id,
                "code": work_order.code,
                "state": work_order.state,
                'product_id': work_order.product_id.id,
                "product_name": work_order.product_id.name,
                "plan_qty": work_order.plan_qty,
                "process_name": work_order.process_id.name or '',
                "finish_qty": work_order.finish_qty,
                "no_finish_qty": work_order.plan_qty - work_order.finish_qty,
                "employee_names": ",".join(work_order.employee_ids.mapped("name")),  # 指派人
                "zuoye_names": zuoye_names,  # 作业任务 报工记录的人员
                "uom_id": work_order.uom_id.id,
                'uom_name': work_order.uom_id.name,
                "wo_start_time": convert_utc_to_local(work_order.wo_start_time)[5:] if work_order.wo_start_time else "",
                # 开工时间
                "task_id": work_order.task_id.id or None,
                "task_code": work_order.task_id.code or '',
                # "work_center_id": work_order.work_center_id.id,
                # "work_center_name": work_order.work_center_id.name,
                "workshop_id": work_order.workshop_id.id or None,
                "workshop_name": work_order.workshop_id.name or '',  # 工作中心
                "dispatch_time": convert_utc_to_local(work_order.dispatch_time),  # 下单日期
                'wait_qty': allow_qty,
            }
            work_order_list.append(vals)
        result = {
            "code": "200",
            "state": "success",
            "msg": "",
            "data": work_order_list,
        }
        return result
    def _get_work_orders(self, equipment_id,limit=False, page=False):
        """获取工单记录"""
        result = self._get_work_order_details(equipment_id,limit=limit, page=page)
        return result