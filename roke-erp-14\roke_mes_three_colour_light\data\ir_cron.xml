<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 每分钟执行一次的定时任务，调用get_stack_light_state方法 -->
        <record id="ir_cron_get_stack_light_state" model="ir.cron">
            <field name="name">获取安灯状态</field>
            <field name="model_id" ref="model_roke_stack_light_config"/>
            <field name="state">code</field>
            <field name="code">model.get_stack_light_state()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="False"/>
            <field name="user_id" ref="base.user_root"/>
        </record>
    </data>
</odoo>
