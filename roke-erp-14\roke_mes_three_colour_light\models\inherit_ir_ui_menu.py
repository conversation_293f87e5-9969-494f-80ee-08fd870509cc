from odoo import api, fields, models, modules, tools, _


class InheritIrUiMenu(models.Model):
    _inherit = 'ir.ui.menu'

    @api.model
    def get_index_page(self):
        # 重写：获取用户设置的数据看板菜单
        if self.env.user.dashboard_id.ks_dashboard_menu_id:
            board_index = self.env.user.dashboard_id.ks_dashboard_menu_id
        else:
            board_index = self.env.ref("roke_mes_three_colour_light.roke_three_color_light_iframe_device_state_list_menu", raise_if_not_found=False)

        data = None
        if board_index:
            data = {
                "parent_id": board_index.parent_id.id, "action": board_index.action.id, "name": board_index.name,
                "is_sub_reports": board_index.is_sub_reports, "is_divider": board_index.is_divider,
                "sequence": board_index.sequence, 
                "web_icon": board_index.web_icon, "web_icon_data": board_index.web_icon_data,
                "x_menu_id": board_index.id, "action_data": {
                    "type": board_index.action.type, "tag": board_index.action.tag,
                }
            }
        return data