from odoo import api, models, fields


class InheritRokeAbnormalAlarmRecord(models.Model):
    _inherit = "roke.abnormal.alarm"

    light_id = fields.Many2one("roke.stack.light", string="安灯")

    @api.model
    def create(self, vals):
        res = super(InheritRokeAbnormalAlarmRecord, self).create(vals)
        config_id = self.env["roke.stack.light.config"].search([("name", "=", res.abnormal_id.id)], limit=1)
        if config_id.is_notify == "是":
            if config_id.notify_user_ids:
                users = config_id.notify_user_ids
            else:
                users = config_id.notify_group_id.users
            if config_id.mini_program:
                val_list = []
                for item in users:
                    message_data = {
                        "thing4": {
                            "value": item.name
                        },
                        "thing3": {
                            "value": res.abnormal_id.name
                        },
                        "thing2": {
                            "value": f"{res.plant_id.name}-{res.workshop_id.name}-{res.work_center.name}"
                        },
                    }
                    val_list.append({
                        "user_id": item.id,
                        "message_data": message_data
                    })
                wechat_notify_ids = self.env["wechat.notify"].sudo().create(val_list)
                wechat_notify_ids.send_wechat_notification(res.id)
            if config_id.email:
                for item in users:
                    # 创建或获取邮件消息
                    msg_data = {
                        'subject': f'异常表单通知: {res.code}',
                        'model': 'roke.abnormal.alarm',
                        'res_id': res.id,
                        'record_name': res.code,
                        'body': f'<p>异常表单 {res.code} 需要您的处理</p>',
                        'message_type': 'notification',
                        'author_id': item.id,
                    }
                    msg = self.env['mail.message'].create(msg_data)
                    # 发送非审批流消息
                    self.env['mail.send'].send_mail(
                        model=res._name,
                        res_id=res.id,
                        user=item,
                        msg=msg,
                        not_approval=True,
                        message="异常表单通知"
                    )
            if config_id.roke_app:
                code = "-".join([res._description or res._name, str(res.id)])
                if res.equipment_id and res.abnormal_id.name == "设备异常":
                    body = f"【{res.equipment_id.name}】出现了故障，请及时处理。"
                else:
                    body = f"工位【{res.work_center.name}】发起了【{res.abnormal_id.name}】安灯请求，请及时处理。"
                user_list = []
                for item in users:
                    user_list.append((0, 0, {
                        "notification_type": 'inbox',
                        "res_partner_id": item.partner_id.id
                    }))
                data = {
                    "subject": code,
                    "date": fields.datetime.now(),
                    "email_from": '',
                    "author_id": 2,
                    "message_type": "notification",
                    "model": res._name,
                    "res_id": res.id,
                    "record_name": code,
                    "body": body,
                    "notification_ids": user_list
                }
                self.env['mail.message'].sudo().create(data)

        return res