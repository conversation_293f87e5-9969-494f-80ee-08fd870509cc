from odoo import models, fields, api
import requests
import json
import logging

_logger = logging.getLogger(__name__)

class WechatNotify(models.Model):
    _name = 'wechat.notify'
    _description = '小程序通知'

    user_id = fields.Many2one('res.users', string='用户')
    openid = fields.Char(string='微信openid', related="user_id.openid")
    message_data = fields.Text(string='消息内容')
    state = fields.Selection([('成功', '成功'), ('失败', '失败')], string="发送状态")
    note = fields.Text(string="返回结果")

    # 获取 access_token（有效期2小时，建议缓存）
    def get_access_token(self, appid, secret):
        url = f'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}'
        response = requests.get(url)
        result = response.json()
        return result.get('access_token')

    # 发送订阅消息
    def send_wechat_message(self, template_id, page, access_token):
        url = f'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={access_token}'
        payload = {
            "touser": self.openid,
            "template_id": template_id,
            "page": page,  # 小程序内页面路径
            "data": eval(self.message_data),
            "miniprogram_state": "formal",
            "lang": "zh_CN"
        }
        headers = {"Content-Type": "application/json"}
        _logger.info(f"payload：{payload}")
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        self.note = response.json()
        _logger.info(f"小程序通知结果：{self.note}, openid: {self.openid}, user: {self.user_id.name}")
        if response.json() == 0:
            self.state = "成功"
        else:
            self.state = "失败"
        self.env["roke.pub.notice.record"].create({
            # "msgs_id": self.id,
            "send_type": "小程序",
            "send_text": self.message_data,
            "state": self.state,
            "user_ids": [(6, 0, self.user_id.ids)],
            "last_send": fields.Datetime.now(),
            "note": self.note
        })

    def send_wechat_notification(self, id):
        appid = self.env['ir.config_parameter'].sudo().get_param('wechat.appid')
        secret = self.env['ir.config_parameter'].sudo().get_param('wechat.secret')
        template_id = self.env['ir.config_parameter'].sudo().get_param('wechat.template_id')
        page = f'pages/andon/andonDetails?abnormalAlarmId={id}'
        access_token = self.get_access_token(appid, secret)
        for item in self:
            item.send_wechat_message(template_id, page, access_token)
