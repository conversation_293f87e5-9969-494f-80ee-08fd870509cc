# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo import tools


class RokeWorkOrderHoursReport(models.Model):
    _name = "roke.work.order.hours.report"
    _description = "工单计时报表"
    _auto = False
    work_order_id = fields.Many2one("roke.work.order", string="工单")
    work_order_code = fields.Char(string="工单编号",readonly=True)
    product_id = fields.Many2one("roke.product", string="产品名称")
    product_name = fields.Char(string="产品名称",readonly=True)
    process_id = fields.Many2one("roke.process", string="工序")
    process_name = fields.Char(string="工序名称",readonly=True)
    plan_qty = fields.Float(string="计划数量", digits='Production')
    work_hours = fields.Float(string="标准工时(h)")
    actual_hours  = fields.Float(string="实际工时(h)")
    work_rate = fields.Float(string="工作效率")

    @api.model
    def init(self):
        """ 初始化视图 """
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
                CREATE OR REPLACE VIEW %s AS (
                     SELECT 
                        wo.id AS id,
                        wo.id AS work_order_id,
                        wo.code as work_order_code,
                        wo.product_id AS product_id,
                        product.name as product_name ,
                        wo.process_id AS process_id,
                        process.name as process_name,
                        wo.plan_qty AS plan_qty,
                        process.rated_working_hours AS work_hours,
                        COALESCE(SUM(wr.work_hours), 0) AS actual_hours,
                        CASE 
                            WHEN process.rated_working_hours > 0 AND COALESCE(SUM(wr.work_hours), 0) > 0 THEN
                                ROUND(( process.rated_working_hours  / SUM(wr.work_hours)) * 100) 
                            ELSE 0
                        END AS work_rate
                    FROM 
                        roke_work_order wo
                    LEFT JOIN roke_work_record wr ON wr.work_order_id = wo.id
                    LEFT JOIN roke_process process ON wo.process_id = process.id
                    LEFT JOIN roke_product product ON wo.product_id = product.id
										
										
                    GROUP BY 
                        wo.id,wo.code, wo.product_id,product_name, wo.process_id,process.name, wo.plan_qty, process.rated_working_hours
                )
            """ % self._table)


