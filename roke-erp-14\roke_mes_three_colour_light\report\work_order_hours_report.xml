<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_work_order_hours_report_tree" model="ir.ui.view">
            <field name="name">roke.work.order.hours.report.tree</field>
            <field name="model">roke.work.order.hours.report</field>
            <field name="arch" type="xml">
                <tree string="工单计时报表" create="0" edit="0" delete="0">
                    <field name="product_name" string="产品名称" optional="show"/>
                    <field name="work_order_code" string="工单编号" optional="show"/>
                    <field name="process_name" string="工序" optional="show"/>
                    <field name="plan_qty" string="生产数量" optional="show"/>
                    <field name="work_hours" optional="show" />
                    <field name="actual_hours" optional="show"/>
                    <field name="work_rate" string="工作效率(%)" widget="progressbar" optional="show" />
                </tree>
            </field>
        </record>

        <!-- Search View -->
        <record id="view_work_order_hours_report_search" model="ir.ui.view">
            <field name="name">roke.work.order.hours.report.search</field>
            <field name="model">roke.work.order.hours.report</field>
            <field name="arch" type="xml">
                <search string="工单计时搜索">
                    <field name="work_order_id" string="工单"/>
                    <field name="product_id" string="产品名称"/>
                    <field name="process_id" string="工序"/>

                    <group expand="0" string="分组">
                        <filter string="按产品分组" name="group_by_product" context="{'group_by':'product_id'}"/>
                        <filter string="按工序分组" name="group_by_process" context="{'group_by':'process_id'}"/>
                        <filter string="按工单分组" name="group_by_work_order" context="{'group_by':'work_order_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="action_work_order_hours_report" model="ir.actions.act_window">
            <field name="name">工单计时报表</field>
            <field name="res_model">roke.work.order.hours.report</field>
            <field name="view_mode">tree</field>
            <field name="search_view_id" ref="view_work_order_hours_report_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">暂无工时数据</p>
            </field>
        </record>


        <menuitem id="menu_roke_work_hours"
                  name="工单计时"
                  parent="roke_mes_equipment.roke_mes_equipment_report_menu"
                  action="action_work_order_hours_report"
                  sequence="30"/>
    </data>

</odoo>