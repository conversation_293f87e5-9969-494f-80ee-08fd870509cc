// roke_mes_three_colour_light/static/src/js/page_label_color.js
odoo.define('roke_mes_three_colour_light.field_color', function (require) {
    "use strict";

    const FormRenderer = require('web.FormRenderer');

    FormRenderer.include({
        _updateView: function () {
            this._super.apply(this, arguments);
            this._setFieldColor();
        },

        _setFieldColor: function () {
            const record = this.state && this.state.model === "roke.stack.light.config" && this.state.data;
            if (!record) return;

            const color = record.color;

            // 找到 tab 标签
            const $span = this.el.querySelectorAll('span[name="color"]');
            $span.forEach(span => {
                span.style.color = color;
            });
        }
    });
});
