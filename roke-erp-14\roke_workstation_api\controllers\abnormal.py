# -*- coding: utf-8 -*-
import datetime
import logging
import pytz
from dateutil.relativedelta import relativedelta

from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_workstation_api.controllers.center import RokeWorkstationCenter
from jinja2 import FileSystemLoader, Environment
import os

# 设置查找html文件的路径
BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static")
env = Environment(loader=templateloader)


_logger = logging.getLogger(__name__)


def get_time_difference(user_tz):
    return user_tz.utcoffset(
        datetime.datetime.utcnow()
    ).total_seconds() / 3600


class RokeWorkstationAbnormal(http.Controller):

    @http.route('/roke/abnormal_alarm/census', type='http', auth='public', csrf=False, cors="*")
    def roke_index_demo_module(self, **kwargs):
        template = env.get_template('html/abnormal_alarm/view/index.html')
        html = template.render({})
        return html

    @http.route('/roke/workstation/abnormal/get_list', type='json', auth="user", csrf=False, cors='*')
    def abnormal_get_list(self):
        """
        获取异常表单列表
        limit: int 条数
        page: int 页数
        """
        _self = http.request
        limit = _self.jsonrequest.get("limit", 20)
        page = _self.jsonrequest.get("page", 1)

        records = _self.env["roke.abnormal.alarm"].search([("alarm_state", "=", '开始'), ("work_center", "!=", False)],
                                                          limit=limit, offset=limit *
                                                          (page - 1),
                                                          order="create_date desc")
        data = []
        for v in records:
            abnormal_item = v.abnormal_item_ids and v.abnormal_item_ids[0]
            data.append({
                "id": v.id,
                "code": v.code,
                "abnormal_item": {"id": abnormal_item.id, "name": abnormal_item.name or ""},
                "color": abnormal_item.color,
                "work_center": {"id": v.work_center.id, "name": v.work_center.name or "",
                                "center_icon": RokeWorkstationCenter()._get_center_icon_url(v.work_center.id)
                                if v.work_center.center_icon else ""}
            })
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/roke/workstation/abnormal/solve', type='json', auth="user", csrf=False, cors='*')
    def abnormal_solve(self):
        """
        异常完成响应接口
        limit: int 条数
        page: int 页数
        """
        _self = http.request
        abnormal_alarm = _self.jsonrequest.get("abnormal_alarm_id", False)
        uid = http.request.uid or http.request.session.uid
        if not abnormal_alarm:
            return {"code": 1, "message": "修改失败！ 异常ID未必传参数。"}

        abnormal_alarm = _self.env["roke.abnormal.alarm"].search(
            [("id", "=", abnormal_alarm)])
        if not abnormal_alarm:
            return {"code": 1, "message": "修改失败！ 未找到该数据。"}
        employee_id = _self.env["roke.employee"].search(
            [("user_id", "=", uid)])
        abnormal_alarm.write(
            {"alarm_state": '结束', "handle_employee_ids": [(4, employee_id.id)]})
        return {"code": 0, "message": "修改成功！"}

    @http.route('/get/workstation/everyday/abnormal', type='json', auth="user", csrf=False, cors='*')
    def get_everyday_abnormal_order(self):
        today = fields.Date.today()
        start_time = datetime.datetime.combine(today, datetime.time(0, 0, 0)).astimezone(pytz.utc) - relativedelta(hours=8)
        end_time = datetime.datetime.combine(today, datetime.time(23, 59, 59)).astimezone(pytz.utc) - relativedelta(hours=8)
        abnormal_alarm_ids = http.request.env["roke.abnormal.alarm"].search([('create_date', '>=', start_time), ('create_date', '<=', end_time)])
        data = []
        for abnormal_alarm_id in abnormal_alarm_ids:
            data.append({
                "id": abnormal_alarm_id.id,
                "workshop_name": abnormal_alarm_id.workshop_id.name or "",
                "work_center_name": abnormal_alarm_id.work_center.name or "",
                "abnormal_item": abnormal_alarm_id.abnormal_id.name if abnormal_alarm_id.abnormal_id else "",
                "originating_time": abnormal_alarm_id.originating_time + datetime.timedelta(hours=8) if abnormal_alarm_id.originating_time else '',
                "arrive_time": abnormal_alarm_id.arrive_time + datetime.timedelta(hours=8) if abnormal_alarm_id.arrive_time else '',
            })
        general = {
            "draft": len(abnormal_alarm_ids.filtered(lambda x: x.originating_time + datetime.timedelta(hours=8.5) >= datetime.datetime.now() and not x.arrive_time)),
            "start": len(abnormal_alarm_ids.filtered(lambda x: x.arrive_time and not x.finished_time)),
            "overtime": len(abnormal_alarm_ids.filtered(lambda x: x.originating_time + datetime.timedelta(hours=8.5) < datetime.datetime.now() and not x.arrive_time)),
            "stop": len(abnormal_alarm_ids.filtered(lambda x: x.plant_type == "停线")),
            "running": len(abnormal_alarm_ids.filtered(lambda x: x.plant_type == "非停线")),
            "quality": len(abnormal_alarm_ids.filtered(lambda x: "质量异常" in x.abnormal_id.name)),
            "routing": len(abnormal_alarm_ids.filtered(lambda x: "工艺异常" in x.abnormal_id.name)),
            "equipment": len(abnormal_alarm_ids.filtered(lambda x: "设备异常" in x.abnormal_id.name)),
        }
        return {"code": 0, "message": "获取成功", "data": data, "general": general}

    @http.route('/get/workstation/week/abnormal', type='json', auth="user", csrf=False, cors='*')
    def get_week_abnormal_order(self):
        _self = http.request
        workshop_id = _self.jsonrequest.get("workshop_id", False)  # 产线
        if workshop_id:
            workshop_ids = [workshop_id]
        else:
            workshop_ids = http.request.env["roke.workshop"].search([]).mapped('id')
        equipment_id = _self.jsonrequest.get("equipment_id", False)  # 设备
        if equipment_id:
            equipment_ids = [equipment_id]
        else:
            equipment_ids = http.request.env["roke.mes.equipment"].search([]).mapped('id')
        tz = pytz.timezone("Asia/Shanghai")
        now = datetime.datetime.now(tz)
        week_day = now - relativedelta(days=6)
        week_time = datetime.datetime.combine(now, datetime.time(0, 0, 0)) - relativedelta(hours=8) - relativedelta(days=6)
        abnormal_alarm_ids = http.request.env["roke.abnormal.alarm"].search([('create_date', '>=', week_time)])
        week = []
        for num in range(7):
            one_day_abnormal_alarm_ids = abnormal_alarm_ids.filtered(lambda x: x.create_date >= week_time + relativedelta(days=num) and x.create_date < week_time + relativedelta(days=num+1))
            week.append({
                "day": (week_day + relativedelta(days=num)).day,
                "count": sum(one_day_abnormal_alarm_ids.filtered(lambda x: x.workshop_id.id in workshop_ids).mapped("wait_duration")),
                "equipment_count": len(one_day_abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "设备异常" and x.equipment_id.id in equipment_ids))
            })
        data = {
            "week": week,
            "routing": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "工艺异常")), # 工艺
            "quality": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "质量异常")), # 质量
            "starving": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "缺料断料")), # 缺料
            "equipment": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "设备异常")), # 设备
            # "shape": len(abnormal_alarm_ids.filtered(lambda x: x.abnormal_id.name == "外形问题")), # 外观
        }
        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/roke/workstation/abnormal/create', type='json', auth="user", csrf=False, cors='*')
    def create_abnormal_order(self):
        kwargs = http.request.jsonrequest

        plant_type = kwargs.get("plant_type", False)

        if not plant_type:
            return {"code": 1, "message": "缺少参数 plant_type", "data": None}

        center_id = kwargs.get("work_center_id", False)
        item_ids = kwargs.get("item_ids") or []
        work_order_id = kwargs.get("wo_id")
        process_id = kwargs.get("process_id", False)
        product_id = kwargs.get("product_id", False)
        note = kwargs.get("reason") or ""
        files = kwargs.get("files") or []

        if not center_id:
            return {"code": 1, "message": "缺少参数 work_center_id", "data": None}

        center_obj = http.request.env["roke.work.center"].search([
            ("id", "=", int(center_id))
        ])

        if not center_obj:
            return {"code": 1, "message": "工位信息不存在或已删除", "data": None}

        workshop_id = center_obj.workshop_id.id
        plant_id = center_obj.workshop_id.plant_id.id

        if work_order_id:
            work_order_obj = http.request.env["roke.work.order"].search([
                ("id", "=", int(work_order_id))
            ])
            if not process_id:
                process_id = work_order_obj.process_id.id
            if not product_id:
                product_id = work_order_obj.product_id.id

        items = http.request.env["roke.abnormal.alarm.item"].browse(item_ids)
        departments = items.mapped("department_id")

        order = http.request.env["roke.abnormal.alarm"].create({
            "plant_id": plant_id,
            "workshop_id": workshop_id,
            "plant_type": plant_type,

            "abnormal_item_ids": [(6, 0, item_ids)],
            "work_center": center_id,
            "process": process_id,
            "product": product_id,
            "handle_department_id": departments[0].id if departments else False,
            "handle_employee_ids": [(6, 0, items.employee_ids.ids)],
            "note": note
        })

        attachment_ids = []
        Attachment = http.request.env['ir.attachment']
        i = 1
        for file in files:
            file_data = file.get("data")
            file_type = file.get("type") or ""
            attachment = Attachment.create({
                'name': "异常%s-%s.%s" % (order.display_name, str(i), file_type),
                'datas': file_data,
                'res_model': "roke.abnormal.alarm",
                'res_id': order.id
            })
            attachment_ids.append(attachment.id)
            i += 1
        order.write({"image_ids": [(6, 0, attachment_ids)]})

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai"))
        originating_time = pytz.utc.localize(order.originating_time).astimezone(
            user_tz) if order.originating_time else None

        count = http.request.env["roke.abnormal.alarm"].search_count([
            ("plant_id", "=", int(plant_id)), ("workshop_id", "=", int(workshop_id)),
            ("abnormal_item_ids", "=", item_ids[0]),
            ("sponsor", "=", http.request.env.user.id), ("alarm_state", "=", "开始")
        ])

        return {
            "code": 0, "message": "提交成功",
            "data": {
                "id": order.id,
                "plant_type": order.plant_type,
                "count": count,
                "sponser": {"id": order.sponsor.id, "name": order.sponsor.name},
                "originating_time": originating_time,
                "plant": {"id": order.plant_id.id, "name": order.plant_id.name},
                "workshop": {"id": order.workshop_id.id, "name": order.workshop_id.name},
                "center": {"id": order.work_center.id, "name": order.work_center.name},
                "abnormal": {"id": order.abnormal_item_ids[0].id, "name": order.abnormal_item_ids[0].name},
            }
        }

    @http.route('/roke/workstation/abnormal/read', type='json', auth="user", csrf=False, cors='*')
    def read_abnormal_order(self):
        kwargs = http.request.jsonrequest

        center_id = kwargs.get("center_id", False)
        item_ids = kwargs.get("item_ids") or []

        if not center_id:
            return {"code": 1, "message": "缺少参数 work_center_id", "data": None}

        center_obj = http.request.env["roke.work.center"].search([
            ("id", "=", int(center_id))
        ])

        if not center_obj:
            return {"code": 1, "message": "工位信息不存在或已删除", "data": None}

        workshop_id = center_obj.workshop_id.id
        plant_id = center_obj.workshop_id.plant_id.id

        orders = http.request.env["roke.abnormal.alarm"].search([
            ("plant_id", "=", plant_id), ("workshop_id", "=", workshop_id),
            ("abnormal_item_ids", "=", item_ids[0]),
            ("sponsor", "=", http.request.env.user.id), ("alarm_state", "=", "开始")
        ])

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        data = [
            {
                "id": order.id,
                "plant_type": order.plant_type,
                "plant": {"id": order.plant_id.id, "name": order.plant_id.name},
                "workshop": {"id": order.workshop_id.id, "name": order.workshop_id.name},
                "center": {"id": order.work_center.id, "name": order.work_center.name},
                "abnormal": {"id": order.abnormal_item_ids[0].id, "name": order.abnormal_item_ids[0].name},
                "sponser": {"id": order.sponsor.id, "name": order.sponsor.name},
                "originating_time": pytz.utc.localize(order.originating_time).astimezone(user_tz) if order.originating_time else None
            }
            for order in orders
        ]
        return {"code": 0, "message": "获取报警信息列表成功", "data": data}

    @http.route('/roke/workstation/abnormal/get', type='json', auth="user", csrf=False, cors='*')
    def get_abnormal_info(self):
        kwargs = http.request.jsonrequest
        abnormal_id = kwargs.get("abnormal_id", False)

        if not abnormal_id:
            return {"code": 1, "message": "缺少必要的参数 abnormal_id", "data": None}

        abnormal_obj = http.request.env["roke.abnormal.alarm"].sudo().search([
            ("id", "=", abnormal_id)
        ])

        if not abnormal_obj:
            return {"code": 1, "message": "未找到异常信息", "data": None}

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        data = {
            "id": abnormal_obj.id,
            "plant_type": abnormal_obj.plant_type,
            "plant": {"id": abnormal_obj.plant_id.id, "name": abnormal_obj.plant_id.name},
            "workshop": {"id": abnormal_obj.workshop_id.id, "name": abnormal_obj.workshop_id.name},
            "center": {"id": abnormal_obj.work_center.id, "name": abnormal_obj.work_center.name},
            "sponser": {"id": abnormal_obj.sponsor.id, "name": abnormal_obj.sponsor.name},
            "abnormal": abnormal_obj.abnormal_item_ids.mapped("name"),
            "originating_time": pytz.utc.localize(abnormal_obj.originating_time).astimezone(user_tz) if abnormal_obj.originating_time else None,
            "arrive_time": pytz.utc.localize(abnormal_obj.arrive_time).astimezone(user_tz) if abnormal_obj.arrive_time else None,
            "finished_time": pytz.utc.localize(abnormal_obj.finished_time).astimezone(user_tz) if abnormal_obj.finished_time else None
        }

        return {"code": 0, "message": "获取成功", "data": data}

    @http.route('/roke/workstation/abnormal/update', type='json', auth="user", csrf=False, cors='*')
    def update_abnormal_order(self):
        kwargs = http.request.jsonrequest

        abnormal_id = kwargs.get("abnormal_id", False)
        update_type = kwargs.get("update_type", False)

        if not abnormal_id:
            return {"code": 1, "message": "缺少参数 abnormal_id", "data": None}

        if not update_type:
            return {"code": 1, "message": "缺少参数 update_type", "data": None}

        if not update_type in ["到场", "完成"]:
            return {"code": 1, "message": "参数 update_type 错误", "data": None}

        abnormal_obj = http.request.env["roke.abnormal.alarm"].sudo().search([
            ("id", "=", abnormal_id), ("alarm_state", "=", "开始")
        ])

        if not abnormal_obj:
            return {"code": 1, "message": "异常报警信息不存在或已处理", "data": None}

        if update_type == "到场":
            message = "已更新到场信息"
            abnormal_obj.write({
                "arrive_user_id": http.request.env.user.id, "arrive_time": fields.Datetime.now()
            })
        else:
            message = "已更新完成信息"
            abnormal_obj.write({
                "alarm_state": "结束", "finished_time": fields.Datetime.now()
            })
        return {"code": 0, "message": message, "data": None}

    @http.route('/roke/workstation/abnormal/list', type='json', auth="user", csrf=False, cors='*')
    def list_abnormal_order(self):
        kwargs = http.request.jsonrequest

        client = kwargs.get("client", "")

        state = kwargs.get("state", False)

        plant_id = kwargs.get("plant_id", False)
        workshop_id = kwargs.get("workshop_id", False)
        center_id = kwargs.get("center_id", False)
        plant_type = kwargs.get("plant_type", False)

        datetime_start = kwargs.get("datetime_start")
        datetime_stop = kwargs.get("datetime_stop")

        page_size = kwargs.get("page_size", 0)
        page_no = kwargs.get("page_no", 0)
        offset = max((int(page_no) - 1) * int(page_size), 0)

        is_asc = kwargs.get("sort", True)
        sort = "asc" if is_asc else "desc"

        domain = []

        order = f"originating_time {sort}, arrive_time {sort}"

        if client == "操作设备":
            domain.append(("sponsor.id", "=", http.request.env.user.id))

        if state:
            if state == "未完成":
                domain.append(("arrive_time", "!=", False))
                domain.append(("finished_time", "=", False))
                order = f"arrive_time {sort}"
            elif state == "已完成":
                domain.append(("finished_time", "!=", False))
                order = f"finished_time {sort}"  # 默认需要传递 desc
            elif state == "未到场":
                domain.append(("arrive_time", "=", False))
                order = f"originating_time {sort}"
            elif state == "全部":
                order = f"originating_time {sort}"
            else:
                domain.append(("alarm_state", "=", state))

        if plant_id:
            domain.append(("plant_id", "=", plant_id))

        if workshop_id:
            domain.append(("workshop_id", "=", workshop_id))

        if center_id:
            domain.append(("work_center", "=", center_id))

        if plant_type:
            domain.append(("plant_type", "=", plant_type))

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        time_difference = get_time_difference(user_tz)
        formatter = "%Y-%m-%d %H:%M:%S"

        if datetime_start:
            time_obj = datetime.datetime.strptime(datetime_start, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", ">=", fixed.strftime(formatter))
            )

        if datetime_stop:
            time_obj = datetime.datetime.strptime(datetime_stop, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", "<=", fixed.strftime(formatter))
            )

        if page_size and page_no:
            orders = http.request.env["roke.abnormal.alarm"].search(
                domain, limit=int(page_size), offset=offset, order=order
            )
        else:
            orders = http.request.env["roke.abnormal.alarm"].search(
                domain, order=order
            )

        data = [
            {
                "id": order.id,
                "plant_type": order.plant_type,
                "plant": {"id": order.plant_id.id, "name": order.plant_id.name},
                "workshop": {"id": order.workshop_id.id, "name": order.workshop_id.name},
                "center": {"id": order.work_center.id, "name": order.work_center.name},
                "sponser": {"id": order.sponsor.id, "name": order.sponsor.name},
                "arriver": {"id": order.arrive_user_id.id, "name": order.arrive_user_id.name},
                "abnormal": order.abnormal_item_ids.mapped("name"),
                "originating_time": pytz.utc.localize(order.originating_time).astimezone(user_tz) if order.originating_time else None,
                "arrive_time": pytz.utc.localize(order.arrive_time).astimezone(user_tz) if order.arrive_time else None,
                "finished_time": pytz.utc.localize(order.finished_time).astimezone(user_tz) if order.finished_time else None,
                "wait_duration": order.wait_duration,
                "finished_duration": order.finished_duration
            }
            for order in orders
        ]
        return {"code": 0, "message": "获取报警信息列表成功", "data": data}

    @http.route('/roke/mobile/abnormal/list', type='json', auth="user", csrf=False, cors='*')
    def list_roke_mobile_abnormal(self):
        kwargs = http.request.jsonrequest

        page_size = kwargs.get("page_size", 0)
        page_no = kwargs.get("page_no", 0)
        offset = max((int(page_no) - 1) * int(page_size), 0)

        datetime_start = kwargs.get("datetime_start")
        datetime_stop = kwargs.get("datetime_stop")

        domain = []

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        time_difference = get_time_difference(user_tz)
        formatter = "%Y-%m-%d %H:%M:%S"

        if datetime_start:
            time_obj = datetime.datetime.strptime(datetime_start, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", ">=", fixed.strftime(formatter))
            )

        if datetime_stop:
            time_obj = datetime.datetime.strptime(datetime_stop, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", "<=", fixed.strftime(formatter))
            )

        orders = http.request.env["roke.abnormal.alarm"].search(
            domain, limit=int(page_size), offset=offset, order="id desc"
        )

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        data = [
            {
                "id": order.id,
                "plant_type": order.plant_type,
                "plant_name": order.plant_id.name or "",
                "workshop_name": order.workshop_id.name or "",
                "center_name": order.work_center.name or "",
                "product_name": order.product.name or "",
                "sponser": order.sponsor.name or "",
                "arriver": order.arrive_user_id.name or "",
                "abnormal": order.abnormal_item_ids.mapped("name"),
                "originating_time": pytz.utc.localize(order.originating_time).astimezone(user_tz) if order.originating_time else None,
                "arrive_time": pytz.utc.localize(order.arrive_time).astimezone(user_tz) if order.arrive_time else None,
                "finished_time": pytz.utc.localize(order.finished_time).astimezone(user_tz) if order.finished_time else None,
                "wait_duration": order.wait_duration,
                "finished_duration": order.finished_duration
            }
            for order in orders
        ]
        return {"code": 0, "message": "获取报警信息列表成功", "data": data}

    @http.route('/roke/mobile/abnormal/pie1', type='json', auth="user", csrf=False, cors='*')
    def pie1_roke_mobile_abnormal(self):

        kwargs = http.request.jsonrequest

        datetime_start = kwargs.get("datetime_start")
        datetime_stop = kwargs.get("datetime_stop")

        item_id = kwargs.get("item_id", False)

        domain = []

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        time_difference = get_time_difference(user_tz)
        formatter = "%Y-%m-%d %H:%M:%S"

        if datetime_start:
            time_obj = datetime.datetime.strptime(datetime_start, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append((
                "originating_time", ">=", fixed.strftime(formatter))
            )

        if datetime_stop:
            time_obj = datetime.datetime.strptime(datetime_stop, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", "<=", fixed.strftime(formatter))
            )

        if item_id:
            domain.append(("abnormal_item_ids", "=", item_id))

        waiting = http.request.env["roke.abnormal.alarm"].search_count(
            domain + [("arrive_time", "=", False)]
        )
        processing = http.request.env["roke.abnormal.alarm"].search_count(
            domain + [("arrive_time", "!=", False),
                      ("finished_time", "=", False)]
        )
        finished = http.request.env["roke.abnormal.alarm"].search_count(
            domain + [("finished_time", "!=", False)]
        )

        item_objs = http.request.env["roke.abnormal.alarm.item"].sudo().search([])

        items = [
            {"id": item_obj.id, "name": item_obj.name}
            for item_obj in item_objs
        ]

        datas = {
            "waiting": waiting, "processing": processing, "finished": finished
        }

        data = {
            "items": items,
            "datas": datas
        }
        return {"code": 0, "message": "获取次数信息成功", "data": data}

    @http.route('/roke/mobile/abnormal/pie2', type='json', auth="user", csrf=False, cors='*')
    def pie2_roke_mobile_abnormal(self):

        kwargs = http.request.jsonrequest

        datetime_start = kwargs.get("datetime_start")
        datetime_stop = kwargs.get("datetime_stop")

        domain = [
            "|",
            ("finished_duration", "!=",  0),
            ("finished_duration", "!=", False)
        ]

        user_tz = pytz.timezone(
            http.request.env.context.get('tz', "Asia/Shanghai")
        )

        time_difference = get_time_difference(user_tz)
        formatter = "%Y-%m-%d %H:%M:%S"

        if datetime_start:
            time_obj = datetime.datetime.strptime(datetime_start, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", ">=", fixed.strftime(formatter))
            )

        if datetime_stop:
            time_obj = datetime.datetime.strptime(datetime_stop, formatter)
            fixed = time_obj + datetime.timedelta(hours=-time_difference)
            domain.append(
                ("originating_time", "<=", fixed.strftime(formatter))
            )

        abnormal_model = http.request.env['roke.abnormal.alarm']
        item_objs = http.request.env["roke.abnormal.alarm.item"].search([])

        data = []
        for item_obj in item_objs:
            records = abnormal_model.search(
                domain + [("abnormal_item_ids", "=", item_obj.id)]
            )
            data.append({
                "name": item_obj.name,
                "color": item_obj.color or "#000000",
                "value": round(
                    sum(records.mapped("finished_duration")) / 60, 2
                )
            })

        return {"code": 0, "message": "获取时间信息成功", "data": data}

    @http.route('/roke/get/abnormal_alarm_list', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_alarm_list(self):
        """
        获取异常表单列表
        :return:
        """
        jsonrequest = http.request.jsonrequest
        domain = []
        abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)
        if abnormal_alarm_id:
            domain.append(('id', '=', abnormal_alarm_id))
        abnormal_id = jsonrequest.get('abnormal_id', False)
        if abnormal_id:
            domain.append(('abnormal_id', '=', abnormal_id))
        page_size = int(http.request.jsonrequest.get('page_size', 2))
        page_no = int(http.request.jsonrequest.get('page_no', 1))
        abnormal_alarm_ids = http.request.env['roke.abnormal.alarm'].search(domain, limit=page_size, offset=(page_no - 1) * page_size, order="originating_time desc")
        abnormal_alarm_list = []
        for item in abnormal_alarm_ids:
            abnormal_alarm_list.append({
                "id": item.id,
                "abnormal_name": item.abnormal_id.name,
                "sponsor": item.sponsor.name,
                "finished_duration": item.finished_duration if item.finished_duration else round((fields.Datetime.now() - item.originating_time).total_seconds() / 60, 2),
                "originating_time": item.originating_time + relativedelta(hours=8) if item.originating_time else '',
                "do_duration": item.finished_duration - item.wait_duration if item.finished_duration else 0,  # 处理耗时
                "alarm_state": item.alarm_state,
                "plant_type": item.plant_type,
                "plant_name": item.plant_id.name,
                "workshop_name": item.workshop_id.name,
                "center_name": item.work_center.name,
                "recipient": item.recipient.name if item.recipient else '',
                "confirm_time": item.confirm_time + relativedelta(hours=8) if item.confirm_time else '',
                "arrive_user": item.arrive_user_id.name if item.arrive_user_id else '',
                "arrive_time": item.arrive_time + relativedelta(hours=8) if item.arrive_time else '',
                "finished_time": item.finished_time + relativedelta(hours=8) if item.finished_time else '',
                "processing_results": item.processing_results,
                "equipment_name": item.equipment_id.name,
                "abnormal_status": item.abnormal_status
            })
        total = http.request.env['roke.abnormal.alarm'].search_count(domain)
        return {
            "state": "success",
            "msgs": "获取成功",
            "total": total,
            "abnormal_alarm_list": abnormal_alarm_list
        }
    
    @http.route('/roke/get/abnormal_alarm/info', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_abnormal_alarm_info(self):
        """
        获取异常表单详情
        :return:
        """
        jsonrequest = http.request.jsonrequest
        domain = []
        abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)
        if not abnormal_alarm_id:
            return {"state": "error", "msgs": "缺少必传参数"}
        _logger.info(f"get_abnormal_alarm_info, abnormal_alarm_id: {abnormal_alarm_id}")
        abnormal_alarm_id = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))
        abnormal_alarm = {
            "id": abnormal_alarm_id.id,
            "abnormal_name": abnormal_alarm_id.abnormal_id.name,
            "sponsor": abnormal_alarm_id.sponsor.name,
            "finished_duration": abnormal_alarm_id.finished_duration if abnormal_alarm_id.finished_duration else round((fields.Datetime.now() - abnormal_alarm_id.originating_time).total_seconds() / 60, 2),
            "originating_time": abnormal_alarm_id.originating_time + relativedelta(hours=8) if abnormal_alarm_id.originating_time else '',
            "do_duration": abnormal_alarm_id.finished_duration - abnormal_alarm_id.wait_duration if abnormal_alarm_id.finished_duration else 0,  # 处理耗时
            "alarm_state": abnormal_alarm_id.alarm_state,
            "plant_type": abnormal_alarm_id.plant_type,
            "plant_id": abnormal_alarm_id.plant_id.id,
            "plant_name": abnormal_alarm_id.plant_id.name,
            "workshop_name": abnormal_alarm_id.workshop_id.name,
            "center_name": abnormal_alarm_id.work_center.name,
            "recipient": abnormal_alarm_id.recipient.name if abnormal_alarm_id.recipient else '',
            "confirm_time": abnormal_alarm_id.confirm_time + relativedelta(hours=8) if abnormal_alarm_id.confirm_time else '',
            "arrive_user": abnormal_alarm_id.arrive_user_id.name if abnormal_alarm_id.arrive_user_id else '',
            "arrive_time": abnormal_alarm_id.arrive_time + relativedelta(hours=8) if abnormal_alarm_id.arrive_time else '',
            "finished_time": abnormal_alarm_id.finished_time + relativedelta(hours=8) if abnormal_alarm_id.finished_time else '',
            "processing_results": abnormal_alarm_id.processing_results,
            "equipment_name": abnormal_alarm_id.equipment_id.name,
            "abnormal_status": abnormal_alarm_id.abnormal_status
        }
        total = http.request.env['roke.abnormal.alarm'].search_count(domain)
        return {
            "state": "success",
            "msgs": "获取成功",
            "total": total,
            "abnormal_alarm": abnormal_alarm
        }

    @http.route('/roke/abnormal_alarm/close', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def close_abnormal_alarm(self):
        """
        关闭异常数据，将alarm_state字段设置为关闭
        :param abnormal_alarm_id: 异常数据ID
        :return: 关闭结果
        """
        try:
            # 获取请求参数
            jsonrequest = http.request.jsonrequest
            abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)

            if not abnormal_alarm_id:
                return {
                    "state": "error",
                    "msgs": "缺少必要参数: abnormal_alarm_id"
                }

            # 查询指定的异常数据
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))

            if not abnormal_alarm.exists():
                return {
                    "state": "error",
                    "msgs": f"未找到ID为{abnormal_alarm_id}的异常数据"
                }

            # 检查是否已经关闭
            if abnormal_alarm.alarm_state == '关闭':
                return {
                    "state": "error",
                    "msgs": "该异常数据已经关闭"
                }

            # 更新异常数据
            abnormal_alarm.write({
                'alarm_state': '关闭'
            })

            return {
                "state": "success",
                "msgs": "成功关闭异常数据",
                "abnormal_alarm_id": abnormal_alarm.id,
                "code": abnormal_alarm.code
            }
        except Exception as e:
            return {
                "state": "error",
                "msgs": f"关闭异常数据失败: {str(e)}"
            }

    @http.route('/roke/abnormal_alarm/finish', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def finish_abnormal_alarm(self):
        """
        确认异常数据处理完成，支持文字和图片传入
        :param abnormal_alarm_id: 异常数据ID
        :param processing_results: 处理结果文字描述
        :param images: 处理结果图片列表，格式为[{"name": "图片名称", "data": "base64编码的图片数据", "type": "图片类型，如jpg,png等"}]
        :return: 确认结果
        """
        try:
            # 获取请求参数
            jsonrequest = http.request.jsonrequest
            abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)
            processing_results = jsonrequest.get('processing_results', '')
            images = jsonrequest.get('images', [])

            if not abnormal_alarm_id:
                return {
                    "state": "error",
                    "msgs": "缺少必要参数: abnormal_alarm_id"
                }

            # 获取当前时间
            current_time = fields.Datetime.now()

            # 查询指定的异常数据
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))

            if not abnormal_alarm.exists():
                return {
                    "state": "error",
                    "msgs": f"未找到ID为{abnormal_alarm_id}的异常数据"
                }

            # 检查是否已经处理完成
            if abnormal_alarm.alarm_state == '结束':
                return {
                    "state": "error",
                    "msgs": f"该异常数据已经处理完成，完成时间: {abnormal_alarm.finished_time}"
                }

            # 处理图片
            attachment_ids = []
            if images:
                Attachment = http.request.env['ir.attachment']
                for i, image in enumerate(images):
                    image_name = image.get("name", f"处理图片{i+1}")
                    image_data = image.get("data", "")
                    image_type = image.get("type", "")

                    if not image_data:
                        continue

                    attachment = Attachment.create({
                        'name': f"{image_name}.{image_type}" if image_type else image_name,
                        'datas': image_data,
                        'res_model': "roke.abnormal.alarm",
                        'res_id': abnormal_alarm.id
                    })
                    attachment_ids.append(attachment.id)

            # 更新异常数据
            update_vals = {
                'processing_results': processing_results,
                'alarm_state': '结束',
                'finished_time': current_time
            }

            # 如果有图片，更新image_ids字段
            if attachment_ids:
                # 获取现有的图片附件
                existing_image_ids = abnormal_alarm.image_ids.ids
                # 合并现有图片和新上传的图片
                update_vals['image_ids'] = [(6, 0, existing_image_ids + attachment_ids)]

            abnormal_alarm.write(update_vals)

            return {
                "state": "success",
                "msgs": "成功确认处理完成",
                "abnormal_alarm_id": abnormal_alarm.id,
                "code": abnormal_alarm.code,
                "finished_time": current_time,
                "attachment_count": len(attachment_ids)
            }
        except Exception as e:
            return {
                "state": "error",
                "msgs": f"确认处理完成失败: {str(e)}"
            }

    @http.route('/roke/abnormal_alarm/arrive', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def arrive_abnormal_alarm(self):
        """
        确认异常数据到场，将arrive_time设置为当前时间，arrive_user_id设置为当前人员
        :param abnormal_alarm_id: 异常数据ID
        :return: 确认结果
        """
        try:
            # 获取请求参数
            jsonrequest = http.request.jsonrequest
            abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)

            if not abnormal_alarm_id:
                return {
                    "state": "error",
                    "msgs": "缺少必要参数: abnormal_alarm_id"
                }

            # 获取当前用户ID
            current_user_id = http.request.env.user.id

            # 获取当前时间
            current_time = fields.Datetime.now()

            # 查询指定的异常数据
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))

            if not abnormal_alarm.exists():
                return {
                    "state": "error",
                    "msgs": f"未找到ID为{abnormal_alarm_id}的异常数据"
                }

            # 检查是否已经确认到场
            if abnormal_alarm.arrive_time:
                return {
                    "state": "error",
                    "msgs": f"该异常数据已经确认到场，到场时间: {abnormal_alarm.arrive_time}"
                }

            # 更新异常数据
            abnormal_alarm.write({
                'arrive_user_id': current_user_id,
                'arrive_time': current_time
            })

            return {
                "state": "success",
                "msgs": f"成功确认到场",
                "abnormal_alarm_id": abnormal_alarm.id,
                "code": abnormal_alarm.code,
                "arrive_time": current_time
            }
        except Exception as e:
            return {
                "state": "error",
                "msgs": f"确认到场失败: {str(e)}"
            }

    @http.route('/roke/abnormal_alarm/confirm', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def confirm_abnormal_alarm(self):
        """
        确认异常数据，将recipient设置为当前人员，confirm_time设置为当前时间
        :param abnormal_alarm_id: 异常数据ID
        :return: 确认结果
        """
        try:
            # 获取请求参数
            jsonrequest = http.request.jsonrequest
            abnormal_alarm_id = jsonrequest.get('abnormal_alarm_id', False)

            if not abnormal_alarm_id:
                return {
                    "state": "error",
                    "msgs": "缺少必要参数: abnormal_alarm_id"
                }

            # 获取当前用户ID
            current_user_id = http.request.env.user.id

            # 获取当前时间
            current_time = fields.Datetime.now()

            # 查询指定的异常数据
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].browse(int(abnormal_alarm_id))

            if not abnormal_alarm.exists():
                return {
                    "state": "error",
                    "msgs": f"未找到ID为{abnormal_alarm_id}的异常数据"
                }

            # 更新异常数据
            abnormal_alarm.write({
                'recipient': current_user_id,
                'confirm_time': current_time
            })

            return {
                "state": "success",
                "msgs": f"成功确认异常数据",
                "abnormal_alarm_id": abnormal_alarm.id,
                "code": abnormal_alarm.code
            }
        except Exception as e:
            return {
                "state": "error",
                "msgs": f"确认异常数据失败: {str(e)}"
            }

    @http.route('/roke/get/employee_simple_list', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_employee_simple_list(self):
        """
        获取员工简单列表，仅返回id和name，支持根据name模糊查询和分页
        :param name: 员工姓名，用于模糊查询
        :param page_size: 每页记录数
        :param page_no: 页码，从1开始
        :param active: 是否查询有效员工，默认为True
        :return: 员工列表和总记录数
        """
        jsonrequest = http.request.jsonrequest
        name = jsonrequest.get('name', '')
        page_size = int(jsonrequest.get('page_size', 10))
        page_no = int(jsonrequest.get('page_no', 1))
        active = jsonrequest.get('active', True)

        # 构建查询条件
        domain = [('active', '=', active)]
        if name:
            domain.append(('name', 'ilike', name))

        # 计算总记录数
        total = http.request.env['roke.employee'].search_count(domain)

        # 查询员工数据
        offset = (page_no - 1) * page_size
        employee_ids = http.request.env['roke.employee'].search(domain, limit=page_size, offset=offset, order="name asc")

        # 构建返回数据
        employee_list = []
        for employee in employee_ids:
            employee_list.append({
                "id": employee.id,
                "name": employee.name
            })

        return {
            "state": "success",
            "msgs": "获取成功",
            "total": total,
            "employee_list": employee_list
        }

    @http.route('/roke/create/abnormal_alarm', type='json', auth="user", methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def create_abnormal_alarm(self):
        """
        创建异常表单
        :param plant_type: 车间类型，可选值：'停线'或'非停线'
        :param abnormal_id: 异常类型ID
        :param handle_employee_ids: 处理人ID列表
        :param note: 备注
        :return: 创建结果
        """
        jsonrequest = http.request.jsonrequest
        plant_type = jsonrequest.get('plant_type', False)
        abnormal_id = jsonrequest.get('abnormal_id', False)
        handle_employee_ids = jsonrequest.get('handle_employee_ids', [])
        note = jsonrequest.get('note', '')

        # 参数验证
        if not plant_type:
            return {"state": "error", "msgs": "缺少必传参数: plant_type"}
        if not abnormal_id:
            return {"state": "error", "msgs": "缺少必传参数: abnormal_id"}
        if not handle_employee_ids:
            return {"state": "error", "msgs": "缺少必传参数: handle_employee_ids"}

        # 创建异常表单
        try:
            abnormal_alarm = http.request.env['roke.abnormal.alarm'].create({
                "plant_type": plant_type,
                "abnormal_id": abnormal_id,
                "handle_employee_ids": [(6, 0, handle_employee_ids)],
                "note": note,
                "sponsor": http.request.env.user.id,
                "originating_time": fields.Datetime.now()
            })

            return {
                "state": "success",
                "msgs": "创建成功",
                "id": abnormal_alarm.id,
                "code": abnormal_alarm.code
            }
        except Exception as e:
            return {"state": "error", "msgs": f"创建失败: {str(e)}"}

    @http.route('/roke/abnormal/equipment_failure_list', type='json', auth="user", csrf=False, cors='*')
    def get_abnormal_equipment_failure_list(self):
        """
        获取设备故障异常数据列表
        """
        # 获取设备故障类型的ID
        # limit: int
        # 条数
        # page: int
        # 页数
        _self = http.request
        limit = _self.jsonrequest.get("limit", False)
        page = _self.jsonrequest.get("page", 1)
        equipment_id = _self.jsonrequest.get('equipment_id', False)
        today = _self.jsonrequest.get('today', False)
        if not today:
            today = fields.Date.today()
        else:
            today = fields.Date.from_string(today)

        # 获取当前日期的开始和结束时间 数据库 比界面少8小时
        start_of_day = fields.Datetime.to_string(datetime.datetime.combine(today, datetime.datetime.min.time()) - datetime.timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.datetime.combine(today, datetime.datetime.max.time()) - datetime.timedelta(hours=8))
        if not equipment_id:
            return {"code": "100", "msg": "未传设备id", "data": [],'state':  'error'}
        equipment_failure_type = http.request.env['roke.abnormal.alarm.type'].search([('name', '=', '设备异常')],
                                                                                     limit=1)
        if not equipment_failure_type:
            return {"code": "100", "msg": "未找到设备故障类型", "data": [],'state':  'error'}

        # 查询设备故障类型的异常记录
        if limit and page:
            abnormal_records = http.request.env['roke.abnormal.alarm'].search([
                ('abnormal_id', '=', equipment_failure_type.id),('equipment_id','=',equipment_id),
                ('originating_time', '>=', start_of_day),
                ('originating_time', '<=', end_of_day)
            ], limit=limit, offset=(page - 1) * limit)
        else:
            abnormal_records = http.request.env['roke.abnormal.alarm'].search([
                ('abnormal_id', '=', equipment_failure_type.id),('equipment_id','=',equipment_id),
                ('originating_time', '>=', start_of_day),
                ('originating_time', '<=', end_of_day)
            ])
        if not abnormal_records:
           return {"code": "100", "msg": "未找到设备故障类型的异常记录", "data": [],'state':  'error'}

        data = []
        for record in abnormal_records:
            data.append({
                "originating_time": fields.Datetime.to_string(record.originating_time + datetime.timedelta(hours=8)),  # 故障时间
                "sponsor_name": record.sponsor.name,  # 发起人
                "sponsor_id":record.sponsor.id,
                "handle_employee_names": ', '.join([employee.name for employee in record.handle_employee_ids]),  # 处理人
                "do_duration": record.finished_duration - record.wait_duration if record.finished_duration else 0,  # 处理耗时
                "status": record.abnormal_status  # 状态
            })

        return {"code": "200", "msg": "", "data": data, 'state': 'success'}
