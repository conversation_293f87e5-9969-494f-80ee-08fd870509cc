# -*- coding: utf-8 -*-
import os
from collections import Counter
from datetime import datetime, timedelta
import json
import logging
from itertools import groupby
from operator import attrgetter

import requests



from jinja2 import Environment, FileSystemLoader
from odoo import models, fields, api, http, SUPERUSER_ID, _

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static")
env = Environment(loader=templateloader)

_logger = logging.getLogger(__name__)


class EquipmentTj(http.Controller):

    @http.route('/roke/workstation/equipment_staticfy', type='http', auth='public',
                csrf=False, cors="*")
    def roke_equipment_staticfy_module(self, **kwargs):
        template = env.get_template('html/abnormal_alarm/view/equipment_staticfy.html')
        html = template.render({})
        return html

    @http.route('/roke/workstation/equipment/get_equipment_count', type='json', auth='user', csrf=False, cors="*")
    def get_equipment_count(self, **kwargs):
        factory_code = http.request.env['ir.config_parameter'].sudo().get_param('database.uuid', default="")
        try:
            res = requests.post(
                url="https://dws-platform.xbg.rokeris.com/dev-api/public/device_efficiency/device_state_list",
                data=json.dumps({'factory_code': factory_code}),
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            res_json = res.json()
        except Exception as e:
            _logger.error("获取设备状态失败：%s", str(e))
            return {
                'code': 500,
                'state': 'error',
                'msg': '请求外部接口失败',
                'data': []
            }

        if res_json.get("code") != 200:
            return {
                'code': 400,
                'state': 'error',
                'msg': '获取设备状态失败',
                'data': []
            }

        dws_equipment_data = res_json.get("data", [])

        # 统计各状态数量
        state_counter = Counter(item['state'] for item in dws_equipment_data)

        aggregated_data = {
            'green': state_counter.get('green', 0),
            'yellow': state_counter.get('yellow', 0),
            'red': state_counter.get('red', 0),
            'gray': state_counter.get('gray', 0),
            'total': len(dws_equipment_data)

        }
        return {
            'code': 200,
            'state': 'success',
            'msg': '获取设备状态成功',
            'data': aggregated_data
        }

    @http.route('/roke/workstation/equipment/today_check_repair', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def today_check_repair(self, **kwargs):
        # 获取请求参数
        post_data = http.request.jsonrequest or {}
        today = post_data.get('today')  # 格式如 "2024-10-05"

        try:
            if today:
                today = datetime.strptime(today, "%Y-%m-%d")
            else:
                today = datetime.combine(datetime.today(), datetime.min.time())
        except ValueError:
            return {
                "msg": "日期格式错误，请使用 YYYY-MM-DD",
                "code": 400,
                "data": {},
                "state": "error"

            }
            # 时间范围
        date_start = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        date_end = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))

        env = http.request.env(user=SUPERUSER_ID)

        #今日待检设备数量：今天创建、未完成的点检记录
        pending_check = env['roke.mes.eqpt.spot.check.record'].search([
            ('create_date', '>=', date_start),
            ('create_date', '<=', date_end),
            ('state', '=', 'wait'),
        ])

        # ✅ 今日待修设备数量：今天产生的、来源于点检的维修单
        pending_repair = env['roke.mes.maintenance.order'].search([
            ('report_time', '>=', date_start),
            ('report_time', '<=', date_end),
            ('type', '=', 'repair'),
            # ('repair_origin', '=', 'check'),
            ('state', 'in', ['wait', 'assign']),
        ])

        return {
            'code': 200,
            'state': 'success',
            'msg': '',
            'data': {
                'pending_check_count': len(set(pending_check.mapped('equipment_id'))),
                'pending_repair_count': len(set(pending_repair.mapped('equipment_id'))),

            }
        }

    @http.route('/roke/workstation/equipment/weekly_check_anomalies', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def weekly_check_anomalies(self):

        today = fields.Date.today()
        # 计算过去7天的日期范围
        dates = [(today - timedelta(days=i)) for i in range(6, -1, -1)]  # 最近7天，从早到晚排序

        result = []
        env = http.request.env(user=SUPERUSER_ID)
        for day in dates:
            # 转换为 datetime 时间段

            start_of_day = fields.Datetime.to_string(datetime.combine(day, datetime.min.time()) - timedelta(hours=8))
            end_of_day = fields.Datetime.to_string(datetime.combine(day, datetime.max.time()) - timedelta(hours=8))

            # 查询当天异常点检项数量("anomaly", "异常"), ("fault", "故障")
            anomaly_count = env['roke.mes.eqpt.spot.check.record'].search_count([
                ('create_date', '>=', start_of_day),
                ('create_date', '<=', end_of_day),
                ('normal_state', '=', 'abnormal')
            ])

            result.append({
                "date": day.strftime('%Y-%m-%d'),
                "anomaly_count": anomaly_count
            })

        return {
            'code': 200,
            'state': 'success',
            'msg': '',
            'data': result

        }

    @http.route('/roke/workstation/equipment/weekly_repair_stats', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def weekly_repair_stats(self):

        today = fields.Date.today()
        dates = [(today - timedelta(days=i)) for i in range(6, -1, -1)]  # Last 7 days

        result = []
        env = http.request.env(user=SUPERUSER_ID)

        for day in dates:
            start = datetime.combine(day, datetime.min.time()) - timedelta(hours=8)
            end = datetime.combine(day, datetime.max.time()) - timedelta(hours=8)

            repair_count = env['roke.mes.maintenance.order'].search_count([
                ('report_time', '>=', fields.Datetime.to_string(start)),
                ('report_time', '<=', fields.Datetime.to_string(end)),
                ('type', '=', 'repair'),
                # ('state', 'in', ['finish'])
                # ('repair_origin', '=', 'check')
            ])

            result.append({
                "date": day.strftime('%Y-%m-%d'),
                "repair_count": repair_count
            })

        return {
            'code': 200,
            'state': 'success',
            'msg':'',
            'data': result
        }

    @http.route('/roke/workstation/equipment/weekly_maintenance_stats', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def weekly_maintenance_stats(self):
        try:
            params = http.request.jsonrequest or {}
            page = int(params.get('page', 1))
            page_size = int(params.get('page_size', 5))

            if page < 1 or page_size < 1:
                return {'code': 400, 'state': 'error', 'msg': '分页参数必须大于0'}

            today = fields.Date.today()
            seven_days_ago = today - timedelta(days=6)
            date_start = fields.Datetime.to_string(
                datetime.combine(seven_days_ago, datetime.min.time()) - timedelta(hours=8)
            )

            env = http.request.env(user=SUPERUSER_ID)
            order_model = env['roke.mes.maintenance.order']

            # 查询并分组
            maintenance_data = order_model.read_group(
                domain=[
                    ('type', '=', 'maintain'),
                    ('create_date', '>=', date_start),

                ],
                fields=['equipment_id', 'id:count'],
                groupby=['equipment_id'],
                offset=(page - 1) * page_size,
                limit=page_size,
            )

            # 打印数据结构用于调试（上线前可注释）
            # _logger.info("maintenance_data structure: %s", maintenance_data)

            result = []
            for item in maintenance_data:
                equipment_id = item.get('equipment_id')
                result.append({
                    'equipment_id': equipment_id[0],
                    'equipment_name': equipment_id[1],
                    'maintenance_count': item.get('equipment_id_count'),
                })

            return {
                'code': 200,
                'state': 'success',
                'msg': '',
                'data': result
            }
        except Exception as e:
            _logger.error("weekly_maintenance_stats error: %s", str(e), exc_info=True)
            return {
                'code': 500,
                'state': 'error',
                'msg': '服务器内部错误',
                'data':[],
            }

    @http.route('/roke/workstation/equipment/change_list', type='json', methods=['POST', 'OPTIONS'], auth="user", csrf=False,
                cors='*')
    def get_equipment_change_record(self):
        page_size = int(http.request.jsonrequest.get('page_size', 5))
        page = int(http.request.jsonrequest.get('page', 1))
        env = http.request.env(user=SUPERUSER_ID)
        result = []
        """获取单个设备更换件记录"""
        change_ids = env['roke.mes.equipment.change.record'].read_group(
            domain=[
            ],
            fields=['e_id', 'id:count'],
            groupby=['e_id'],
            offset=(page - 1) * page_size,
            limit=page_size,
        )
        for item in change_ids:
            equipment_id = item.get('e_id')
            result.append({
                'equipment_id': equipment_id[0],
                'equipment_name': equipment_id[1],
                'change_count': item.get('e_id_count'),
            })

        return {
            'code': 200,
            'state': 'success',
            'msg': '',
            'data': result
        }


