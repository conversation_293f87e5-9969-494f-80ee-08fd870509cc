# -*- coding: utf-8 -*-
import os
from datetime import datetime, timedelta
import json
import logging
from itertools import groupby
from operator import attrgetter

import requests
from dateutil.relativedelta import relativedelta

import pytz
from jinja2 import Environment, FileSystemLoader
from odoo import models, fields, api, http, SUPERUSER_ID, _
from odoo.addons.roke_workstation_api.controllers.data_analysis import reduce_pytz_conversion, pytz_conversion

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
templateloader = FileSystemLoader(searchpath=BASE_DIR + "/static")
env = Environment(loader=templateloader)


_logger = logging.getLogger(__name__)



class WorkOrderTj(http.Controller):

    @http.route('/roke/workstation/work_order_staticfy', type='http', auth='public', csrf=False, cors="*")
    def roke_work_order_staticfy_module(self, **kwargs):
        template = env.get_template('html/abnormal_alarm/view/work_order_staticfy.html')
        html = template.render({})
        return html

    @http.route('/roke/workstation/work_order_statistics', methods=['POST', 'OPTIONS'], type='json',
                auth='user', csrf=False, cors="*")
    def get_work_order_statistics(self):
        work_order_obj = http.request.env(user=SUPERUSER_ID)['roke.work.order'].sudo()

        # 工单总数
        total_count = work_order_obj.search_count([])

        # 已完成工单数（状态为“已完工”或“强制完工”）
        completed_count = work_order_obj.search_count([
            ('state', 'in', ['已完工', '强制完工'])
        ])

        # 处理中工单数（状态为“未完工”，并且有报工记录）
        in_progress_count = work_order_obj.search_count([
            ('state', '=', '未完工'),
            ('record_ids', '!=', False)
        ])

        now = fields.Datetime.now() - relativedelta(hours=8)

       #  # 超时工单数（计划完成时间早于当前时间，并且状态不是“已完工”或“强制完工”）
       #  overdue_count = work_order_obj.search_count([
       #     ('plan_date', '<', now),
       #     ('state', '=', '未完工'),'|',('record_ids', '=', False)
       #
       # ])
       #
       #  pause_count =  work_order_obj.search_count([
       #      ('plan_date', '<', now),
       #      ('state', '=', '暂停')
       #
       #  ])


        return {
            'state': 'success',
            'message': '',
            'code': 200,
            'data': {
                'total': total_count,
                'completed': completed_count,
                'in_progress': in_progress_count,
                'overdue':total_count - completed_count - in_progress_count,
            },

        }

    @http.route('/roke/workstation/work_order/progress_ratio_statistics', methods=['POST', 'OPTIONS'], type='json',
                auth='user', csrf=False, cors="*")
    def get_work_order_progress_ratio_statistics(self):
        work_order_obj = http.request.env(user=SUPERUSER_ID)['roke.work.order']

        # 获取总工单数
        total_count = work_order_obj.search_count([])

        if total_count == 0:
            return {
                'state': 'success',
                'message': '',
                'code': 200,
                'data': {
                    'pending_ratio': '0.00%',
                    'in_progress_ratio': '0.00%',
                    'completion_rate': '0.00%'
                }
            }

        # 待开工：无报工记录 + 状态为“未完工”
        pending_count = work_order_obj.search_count([
            ('record_ids', '=', False),
            ('state', '=', '未完工')
        ])

        # 已开工：有报工记录 + 状态为“未完工”
        in_progress_count = work_order_obj.search_count([
            ('record_ids', '!=', False),
            ('state', '=', '未完工')
        ])

        # 已完成：状态为“已完工”或“强制完工”
        completed_count = work_order_obj.search_count([
            ('state', 'in', ['已完工', '强制完工'])
        ])
        pause_count = work_order_obj.search_count([
            ('state', 'not in', ['已完工', '强制完工','未完工'])
        ])


        # 计算比值（保留两位小数）
        pending_ratio = (pending_count / total_count) * 100
        in_progress_ratio = (in_progress_count / total_count) * 100
        completion_rate = (completed_count / total_count) * 100
        pause_ratio = (pause_count / total_count) * 100

        return {
            'state': 'success',
            'message': '',
            'code': 200,
            'data': {
                'pending_ratio': f"{pending_ratio:.2f}%",
                'in_progress_ratio': f"{in_progress_ratio:.2f}%",
                'completion_ratio': f"{completion_rate:.2f}%",
                'pause_ratio': f"{pause_ratio:.2f}%",
            }
        }


    #● 处理中工单数量
    @http.route('/roke/workstation/work_order/process_progress_paginated', methods=['POST', 'OPTIONS'], type='json',
                auth='user', csrf=False, cors="*")
    def get_process_progress_paginated(self):

        work_order_obj = http.request.env(user=SUPERUSER_ID)['roke.work.order']

        page = http.request.jsonrequest.get('page', 1)
        page_size = http.request.jsonrequest.get('page_size', 5)
        offset = (page - 1) * page_size

        processes = work_order_obj.read_group(
            domain=[('state', '=', '未完工'), ('record_ids', '!=', False)],
            fields=['process_id'],
            groupby=['process_id'],
            orderby='process_id',
            limit=page_size,
            offset=offset
        )

        result = []
        for process in processes:
            process_id = process['process_id'][0] if process['process_id'] else False
            process_name = process['process_id'][1] if process['process_id'] else "未指定工序"

            if process_id:
                wo_data = work_order_obj.read_group(
                    domain=[('process_id', '=', process_id), ('state', '=', '未完工'), ('record_ids', '!=', False)],
                    fields=['plan_qty:sum', 'finish_qty:sum'],
                    groupby=['process_id']
                )[0]

                result.append({
                    'process_id': process_id,
                    'process_name': process_name,
                    'plan_qty': round(wo_data.get('plan_qty', 0),2),
                    'finish_qty': round(wo_data.get('finish_qty', 0),2),
                    # 'progress': (wo_data.get('finish_qty', 0) / wo_data.get('plan_qty', 1)) * 100 if wo_data.get(
                    #     'plan_qty', 0) > 0 else 0
                })

        return {
            'state': 'success',
            'message': '',
            'code': 200,
            'data': result,

        }

    @http.route('/roke/workstation/work_order/today_material_input', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_today_material_input(self):
        params = http.request.jsonrequest
        today = params.get('today')
        # company_id = params.get('company_id')
        page = int(params.get('page', 1))
        page_size = int(params.get('page_size', 5))

        if not today:
            today = fields.Date.today()
        else:
            today = datetime.strptime(today, '%Y-%m-%d')

        domain = []
        # if company_id:
        #     domain.append(('company_id', 'in', company_id))
        # else:
        #     domain.append(('company_id', 'in', [http.request.env.company.id or 1]))

        # 时间范围
        start_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        end_of_day = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))
        domain += [
            ('create_date', '>=', start_of_day),
            ('create_date', '<=', end_of_day)
        ]

        record_model = http.request.env(user=SUPERUSER_ID)['roke.wo.material.record']

        # 数据库分组 + 分页查询（按 material_id 分组）
        groups = record_model.read_group(
            domain=domain,
            fields=['material_id', 'qty:sum'],
            groupby=['material_id'],
            orderby='material_id',
            limit=page_size,
            offset=(page - 1) * page_size,
            lazy=False
        )

        result = []
        for group in groups:
            material_id = group['material_id'][0] if group['material_id'] else False
            if not material_id:
                continue
            product = http.request.env['roke.product'].sudo().browse(material_id).exists()
            result.append({
                'product_name': product.name if product else "未知物料",
                'product_id': material_id,
                'qty': round(group['qty'], 2),
                'unit': product.uom_id.name if product and product.uom_id else ''
            })

        return {
            'code': 200,
            'msg': '',
            'state': "success",
            'data': result,

        }

    @http.route('/roke/workstation/work_record/employee_progress', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_employee_progress(self):
        values = http.request.jsonrequest

        team = values.get('team', False)
        today = values.get('today', False)
        page = int(values.get('page', 1))
        page_size = int(values.get('page_size', 5))
        if not today:
            today = fields.Date.today()
        else:
            today = datetime.strptime(today, '%Y-%m-%d')
        # # 时间范围
        # date_start = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        # date_end = fields.Datetime.to_string(datetime.combine(today, datetime.max.time()) - timedelta(hours=8))
        date_start, date_end = today,today
        domain = []
        if date_start:
            domain.append(("capacity_date", ">=", date_start))
        if date_end:
            domain.append(("capacity_date", "<=", date_end))

        if team:
            domain.append(("team_id.name", "ilike", team))

        report_data = http.request.env(user=SUPERUSER_ID)['roke.capacity.analytic.report'].read_group(
            domain,
            ['employee_id', 'finish_qty:sum'],
            ['employee_id'],
            offset=(page - 1) * page_size,
            limit=page_size,
            orderby='finish_qty desc'
        )

        result = [{
            'employee_name': item['employee_id'][1],
            'employee_id': item['employee_id'][0],
            'finish_qty': item['finish_qty']
        } for item in report_data]

        return {
            'code': 200,
            'state': 'success',
            'data': result,
        }

    @http.route('/roke/workstation/work_record/process_progress', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_process_progress(self):
        values = http.request.jsonrequest

        today = values.get('today', False)
        end_date = values.get('end_date', False)
        page = int(values.get('page', 1))
        page_size = int(values.get('page_size', 5))
        if not today  or  not  end_date:
            return {
                'code': 100,
                'state': 'error',
                'msg':"开始或结束日期必传",
                'data': [],
            }

        # # 时间范围
        # date_start = fields.Datetime.to_string(datetime.combine(today, datetime.min.time()) - timedelta(hours=8))
        # date_end = fields.Datetime.to_string(datetime.combine(end_date, datetime.max.time()) - timedelta(hours=8))
        date_start = today
        date_end = end_date
        domain = []
        if date_start:
            domain.append(("capacity_date", ">=", date_start))
        if date_end:
            domain.append(("capacity_date", "<=", date_end))

        report_data = http.request.env(user=SUPERUSER_ID)['roke.capacity.analytic.report'].read_group(
            domain,
            ['process_id', 'unqualified_qty:sum'],
            ['process_id'],
            offset=(page - 1) * page_size,
            limit=page_size,
            orderby='finish_qty desc'
        )

        result = [{
            'process_name': item['process_id'][1],
            'process_id': item['process_id'][0],
            'unqualified_qty': item['unqualified_qty']
        } for item in report_data]

        return {
            'code': 200,
            'state': 'success',
            'msg':'',
            'data': result,
        }

    @http.route('/roke/workstation/work_record/stack_light_state', type='json', auth='user',
                methods=['POST', 'OPTIONS'], csrf=False, cors='*')
    def get_stack_light_state(self):
        values = http.request.jsonrequest
        category_name = values.get('category_name', False)
        page = int(values.get('page', 1))
        page_size = int(values.get('page_size', 5))
        offset = (page - 1) * page_size
        env = http.request.env(user=SUPERUSER_ID)


        domain = []
        if category_name:
            domain.append(("category_id.name", "ilike", category_name))


        all_equipment_ids = env["roke.mes.equipment"].search(domain, order='id asc')


        equipment_dict = {eq.code: eq.name for eq in all_equipment_ids}


        factory_code = env['ir.config_parameter'].sudo().get_param('database.uuid', default="")

        try:
            res = requests.post(
                url="https://dws-platform.xbg.rokeris.com/dev-api/public/device_efficiency/device_state_list",
                data=json.dumps({'factory_code': factory_code}),
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            res_json = res.json()
        except Exception as e:
            _logger.error("获取设备状态失败：%s", str(e))
            return {
                'code': 500,
                'state': 'error',
                'msg': '请求外部接口失败',
                'data': []
            }

        if res_json.get("code") != 200:
            return {
                'code': 400,
                'state': 'error',
                'msg': '获取设备状态失败',
                'data': []
            }

        dws_equipment_data = res_json.get("data", [])


        matched_result = []
        for eq_data in dws_equipment_data:
            code = eq_data.get("code")
            if code in equipment_dict:
                matched_result.append({
                    "name": equipment_dict[code],
                    "code": code,
                    "utilization_rate": eq_data.get("utilization_rate", 0),
                    "state": eq_data.get("state", "未知状态")
                })


        paginated_result = matched_result[offset: offset + page_size]

        return {
            'code': 200,
            'state': 'success',
            'msg': '',
            'data': paginated_result,
        }





