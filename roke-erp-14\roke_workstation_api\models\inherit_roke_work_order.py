from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class WorkstationInheritWorkOrderModel(models.Model):
    _inherit = "roke.work.order"

    repair_task_id = fields.Many2one('roke.production.task', string="上级任务")
    repair_wr_id = fields.Many2one('roke.work.record', string="原始返修报工单")

    def write(self, vals):
        id_dict = {}
        for v in self:
            id_dict[str(v.id)] = {"state": v.state, "old_finish_qty": v.finish_qty}
        res = super(WorkstationInheritWorkOrderModel, self).write(vals)
        for v in self:
            old_dict = id_dict.get(str(v.id), "")
            old_state = old_dict.get("state", "")
            old_finish_qty = old_dict.get("old_finish_qty", 0)
            if "state" not in vals.keys():
                return res
            state = vals.get("state", "")
            if state in ["已完工", "强制完成"]:
                repair_order_id = self.env["roke.repair.order.line"].sudo().search([
                    ("repair_work_order_id", "=", v.id)], limit=1)
                if repair_order_id:
                    repair_order_id.write({
                        "state": "返修完成",
                        "execute_qty": repair_order_id.execute_qty + v.finish_qty
                    })
                    repair_order_id.order_id.write({"state": "返修完成"})
            if state == "进行中" and old_state in ["暂停", "强制完工", "已完工"]:
                repair_order_id = self.env["roke.repair.order.line"].sudo().search([
                    ("repair_work_order_id", "=", v.id)], limit=1)
                if repair_order_id:
                    repair_order_id.write({
                        "state": "返修中",
                        "execute_qty": repair_order_id.execute_qty + v.finish_qty - old_finish_qty
                    })
                    repair_order_id.order_id.write({"state": "返修中"})
        return res


class InheritRokeWorkRecordModel(models.Model):
    _inherit = "roke.work.record"

    def withdraw_work_order(self, work_order):
        if not work_order.task_id and not work_order.record_ids and work_order.type == "生产":
            # 无生产任务且当前工单下无其它报工记录时时，撤回直接删除工单
            work_order.unlink()

    def withdraw(self):
        """
        撤回报工
        :return:
        """
        for v in self:
            for repair in v.repair_line_ids:
                if repair.repair_work_order_id.record_ids:
                    raise ValidationError("返修明细中的返修工单已报工，无法撤销报工！")
                repair.repair_work_order_id.unlink()
            for scrap in v.scrap_line_ids:
                if scrap.scrap_work_order_ids.record_ids:
                    raise ValidationError("报废明细中的补件工单已报工，无法撤销报工！")
                scrap.scrap_work_order_ids.unlink()
        super(InheritRokeWorkRecordModel, self).withdraw()
