/* 选择输入框 */
.el-select {
  display: flex;
  align-items: center;
}

/* 选择输入框 */
.el-select .el-input__inner {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff !important;
}

/* 选择输入框icon */
.el-select .el-input__icon {
  color: #fff !important;
}

/* 选择输入框下拉框 */
.el-select-dropdown {
  background-color: rgba(6, 32, 65, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* 选择输入框下拉框每一行 */
.el-select-dropdown__item {
  color: #fff !important;
}

/* 选择输入框下拉框每一行hover效果 */
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: rgba(64, 158, 255, 0.2) !important;
}

/* 选择输入框下拉框每一行选中效果 */
.el-select-dropdown__item.selected {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

/* 选择输入框聚焦效果 */
.el-input.is-focus .el-input__inner,
.el-input.is-focus .el-input__inner:focus {
  color: #fff !important;
}

/* 选择输入框禁用效果 */
.el-input.is-disabled .el-input__inner {
  background-color: rgba(6, 32, 65, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}
