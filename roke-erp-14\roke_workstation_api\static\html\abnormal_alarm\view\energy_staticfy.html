<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>能耗统计</title>
  <meta content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=0" name="viewport" />
  <!-- /roke_workstation_api/static/html/routing -->
  <link rel="stylesheet" href="/roke_workstation_api/static/html/routing/element-ui/index.css" />
  <link rel="stylesheet" href="/roke_workstation_api/static/html/abnormal_alarm/css/dark_element_ui.css" />
  <script src="/roke_workstation_api/static/html/routing/js/echarts.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/moment.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/vue.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/axios.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/element-ui/index.js"></script>
</head>

<body id="bodyId" style="display: none">
  <div id="app" v-loading.body.fullscreen.lock="loading">
    <div class="filtrateArea">
      <div class="filtrateItemBox">
        <label>车间：</label>
        <el-select v-model="plantValue" filterable size="small" placeholder="请选择车间" clearable
          @change="selChange($event,'plant')">
          <el-option v-for="item in plantList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <div class="filtrateItemBox">
        <label>设备：</label>
        <el-select v-model="deviceValue" filterable size="small" :disabled="!plantValue" placeholder="请选择设备" clearable
          @change="selChange($event,'device')">
          <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
    </div>
    <!-- 统计卡片 -->
    <div class="card-row">
      <div class="stat-card">
        <div class="stat-value">[[ cardData.today || 0 ]][[cardData.uom]]</div>
        <div class="stat-label">今日用电量</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.yesterday || 0 ]][[cardData.uom]]</div>
        <div class="stat-label">昨天用电量</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.monthly || 0 ]][[cardData.uom]]</div>
        <div class="stat-label">月用电量</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.yearly || 0 ]][[cardData.uom]]</div>
        <div class="stat-label">年用电量</div>
      </div>
    </div>
    <!-- 图表区域：两行三列 -->
    <div class="chart-row">
      <div class="chart-box" style="width: 100%;">
        <div class="chart-title">今日分时段用电量</div>
        <div id="dayPartingElectricityConsumptionChart" class="chart-content" v-show="todayEnergyData.length">
        </div>
        <div class="emptyBox" v-show="!todayEnergyData.length">
          <el-empty image="/roke_workstation_api/static/html/abnormal_alarm/images/empty.png" description="暂无数据"
            :image-size="100"></el-empty>
        </div>
      </div>
      <div class="chart-box">
        <div class="chart-title">本周高峰时段</div>
        <div id="peakHoursOfThisWeekChart" class="chart-content" v-show="weeklyEnergyData.length"></div>
        <div class="emptyBox" v-show="!weeklyEnergyData.length">
          <el-empty image="/roke_workstation_api/static/html/abnormal_alarm/images/empty.png" description="暂无数据"
            :image-size="100"></el-empty>
        </div>
      </div>
      <div class="chart-box">
        <div class="chart-title">今日设备能耗top5</div>
        <div id="TOP5EquipmentEnergyConsumptionChart" class="chart-content" v-show="topEnergyData.length"></div>
        <div class="emptyBox" v-show="!topEnergyData.length">
          <el-empty image="/roke_workstation_api/static/html/abnormal_alarm/images/empty.png" description="暂无数据"
            :image-size="100"></el-empty>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  // 发送消息给父页面(关闭odoo的菜单弹窗)
  document.addEventListener("click", () => {
    window.parent.postMessage("hidePopover", "*")
  })
  let vue = new Vue({
    el: "#app",
    delimiters: ["[[", "]]"], // 替换原本vue的[[ key ]]取值方式(与odoo使用的jinja2冲突问题)
    data() {
      return {
        windowHeight: window.innerHeight, // 窗口高度
        baseURL: "", // 基地址 https://yanshi-workstation.xbg.rokeris.com
        dwsBaseUrl: "https://dws-platform.xbg.rokeris.com/dev-api", // dws系统基地址
        loading: false, // 全局加载效果
        cardData: {},
        charts: {},
        todayEnergyData: [], // 今日分时段用电量
        weeklyEnergyData: [], // 本周高峰时段
        topEnergyData: [], // 设备能耗TOP5
        plantList: [], // 车间列表
        plantValue: null, // 选中车间值
        deviceList: [], // 设备列表
        deviceValue: null, // 选中设备值
      }
    },
    async created() {
      // 添加resize事件监听
      window.addEventListener("resize", this.handleResize)
      // 获取车间列表
      this.getPlantListFn().then(result => {
        this.plantList = result
      })
      // 初始化图标
      this.initChartFn()
    },
    mounted() {
      this.$nextTick(() => {
        document.getElementById("bodyId").style.display = "block"
      })
    },
    methods: {
      // 处理窗口大小变化修改图表大小
      handleResize() {
        for (let key in this.charts) {
          this.charts[key] && this.charts[key].resize()
        }
      },
      // 接口请求方法封装
      requestApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.baseURL + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (
              result?.data?.result?.code == 0 ||
              result?.data?.result?.state == "success" ||
              result?.data?.code == 0
            ) {
              resolve(result.data)
            } else if (result?.data?.result?.code == 1) {
              reject(result.data.result.message)
            } else if (result?.data?.result?.state == "error") {
              reject(result.data.result.megs)
            } else if (result?.data?.code == 0) {
              reject(result.data.message)
            } else if (result?.data?.error) {
              reject(result.data.error.message)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 接口请求Dws系统方法封装
      requestDwsApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.dwsBaseUrl + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (result?.data?.success) {
              resolve(result.data)
            } else if (!result.data.success) {
              reject(result.data.msg)
            } else {
              reject(errorMessage)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 获取车间列表
      getPlantListFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/search_read", {
            model: "roke.plant",
            fields: ["id", "name"],
            domain: []
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取设备列表
      getEquipmentListFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/search_read", {
            model: "roke.mes.equipment",
            fields: ["id", "name"],
            domain: [["plant_id", "=", this.plantValue]]
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取数据统计
      getEnergyConsumptionFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/energy/energy_consumption", {
            equipment_id: this.deviceValue, // 设备id
            plant_id: this.plantValue, // 车间id
          }).then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取今日分时段用电量
      getTodayEnergyDataFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/energy/today_energy_data", {
            equipment_id: this.deviceValue, // 设备id
            plant_id: this.plantValue, // 车间id
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取本周高峰时段
      getWeeklyEnergyDataFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/energy/weekly_energy_data", {
            equipment_id: this.deviceValue, // 设备id
            plant_id: this.plantValue, // 车间id
          }).then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取设备能耗TOP5
      getTopEnergyDataFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/energy/top_energy_data", {
            equipment_id: this.deviceValue, // 设备id
            plant_id: this.plantValue, // 车间id
          }).then((res) => {
            resolve({
              data: res.result.data,
              uom: res.result.uom
            } || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 初始化图标
      async initChartFn() {
        this.loading = true
        const apiList = [
          this.getEnergyConsumptionFn(),
          this.getTodayEnergyDataFn(),
          this.getWeeklyEnergyDataFn(),
          this.getTopEnergyDataFn()
        ]
        // 使用Promise.all等待所有请求完成
        await Promise.all(apiList).then(responses => {
          // responses是一个数组，按请求顺序排列
          // 数据统计
          this.cardData = responses[0] || {}
          // 今日分时段用电量
          this.todayEnergyData = responses[1] || {}
          // 本周高峰时段
          this.weeklyEnergyData = responses[2] || {}
          // 设备能耗TOP5
          this.topEnergyData = responses[3] || {}
          this.$nextTick(() => {
            // 今日分时段用电量（柱状图）
            this.initDayPartingElectricityConsumptionChart()
            // 本周高峰时段（饼图）
            this.initPeakHoursOfThisWeekChart()
            // 设备能耗TOP5（柱状图）
            this.initTOP5EquipmentEnergyConsumptionChart()
          })
        }).catch(error => {
          console.error('至少一个请求失败:', error)
        })
        this.loading = false
      },
      // 今日分时段用电量（柱状图）
      initDayPartingElectricityConsumptionChart() {
        const chart = echarts.init(document.getElementById('dayPartingElectricityConsumptionChart'))
        this.charts.dayPartingElectricityConsumption = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        this.todayEnergyData.forEach((item) => {
          xAxisData.push(item.time || '')
          seriesData.push(item.consumption || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['用电量'], textStyle: { color: "#fff", fontSize: 10 } },
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '用电量',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#01c4f9' },
                  { offset: 1, color: '#0a73ff' }
                ])
              },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 本周高峰时段（饼图）
      initPeakHoursOfThisWeekChart() {
        const chart = echarts.init(document.getElementById('peakHoursOfThisWeekChart'))
        this.charts.peakHoursOfThisWeek = chart
        // 单位
        const unit = "℃"
        // 数量
        const seriesData = []
        this.weeklyEnergyData.forEach(item => {
          seriesData.push({
            value: item.consumption || 0,
            name: item.time || ''
          })
        })
        setTimeout(() => {
          chart.setOption({
            tooltip: { trigger: 'item', valueFormatter: (value) => value + unit },
            legend: { left: 'center', textStyle: { color: "#fff", fontSize: 10 } },
            series: [{
              type: 'pie',
              radius: '65%',
              center: ['50%', '55%'],
              itemStyle: { borderRadius: 3, borderColor: '#131d58', borderWidth: 2 },
              label: { show: true, formatter: '{b}: {c}' + unit, color: '#fff', fontSize: 12 },
              data: seriesData
            }]
          })
        }, 1)
      },
      // 设备能耗TOP5（柱状图）
      initTOP5EquipmentEnergyConsumptionChart() {
        const chart = echarts.init(document.getElementById('TOP5EquipmentEnergyConsumptionChart'))
        this.charts.TOP5EquipmentEnergyConsumption = chart
        // 单位
        const unit = this.topEnergyData?.uom || ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        this.topEnergyData?.data.forEach((item) => {
          xAxisData.push(item.equipment_name || '')
          seriesData.push(item.total_consumption || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['设备能耗'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'feedingSituationChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            yAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            series: [{
              name: '设备能耗',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: { color: "#bdb8fc" },
              label: { show: true, position: 'right', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 下拉选择变化事件
      async selChange(el, type) {
        this.loading = true
        if (type == 'plant') {
          this.deviceValue = null
          this.deviceList = []
          this.plantValue = el || null
          // 获取设备列表
          await this.getEquipmentListFn().then(result => {
            this.deviceList = result
          })
        } else if (type == 'device') {
          this.deviceValue = el || null
        }
        this.initChartFn()
        this.loading = false
      },
    },
    beforeDestroy() {
      // 清除resize事件监听
      window.removeEventListener("resize", this.handleResize)
    }
  })
</script>

<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: #f5f7fa;
  }

  #app {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    color: #fff;
    font-family: "Microsoft YaHei", sans-serif;
    background-color: #06114f;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .filtrateArea {
    display: flex;
    align-items: center;
    gap: 10px;

    .filtrateItemBox {
      display: flex;
      align-items: center;
    }
  }

  .card-row {
    display: flex;
    gap: 10px;

    .stat-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      border: 2px solid rgba(255, 255, 255, 0.2);

      .stat-value {
        font-size: 32px;
        font-weight: bold;
      }

      .stat-label {
        font-size: 16px;
      }
    }
  }

  .chart-row {
    flex: auto;
    height: 1px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .chart-box {
    width: calc((100% - 10px) / 2);
    height: calc((100% - 10px) / 2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .chart-title {
      font-size: 16px;
      font-weight: bold;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .chart-content {
      width: 100%;
      height: 100%;
    }

    .emptyBox {
      width: 100%;
      height: calc(100% - 28px);
      display: flex;
      align-items: center;
      justify-content: center;

      .el-empty {
        padding: 0
      }
    }
  }
</style>

</html>