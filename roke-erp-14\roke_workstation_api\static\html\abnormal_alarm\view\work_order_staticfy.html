<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>工单统计</title>
  <meta content="width=device-width,initial-scale=1.0, maximum-scale=1.0,user-scalable=0" name="viewport" />
  <!-- /roke_workstation_api/static/html/routing -->
  <link rel="stylesheet" href="/roke_workstation_api/static/html/routing/element-ui/index.css" />
  <link rel="stylesheet" href="/roke_workstation_api/static/html/abnormal_alarm/css/dark_element_ui.css" />
  <script src="/roke_workstation_api/static/html/routing/js/echarts.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/moment.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/vue.js"></script>
  <script src="/roke_workstation_api/static/html/routing/js/axios.min.js"></script>
  <script src="/roke_workstation_api/static/html/routing/element-ui/index.js"></script>
</head>

<body id="bodyId" style="display: none">
  <div id="app" v-loading.body.fullscreen.lock="loading">
    <!-- 顶部统计卡片 -->
    <div class="card-row">
      <div class="stat-card">
        <div class="stat-value">[[ cardData.total || 0 ]]</div>
        <div class="stat-label">工单总数</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.completed || 0 ]]</div>
        <div class="stat-label">已完成工单数</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.in_progress || 0 ]]</div>
        <div class="stat-label">处理中工单数</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">[[ cardData.overdue || 0 ]]</div>
        <div class="stat-label">超时工单数</div>
      </div>
    </div>
    <!-- 图表区域：两行三列 -->
    <div class="chart-row">
      <div class="chart-box">
        <div class="chart-title">
          <span>生产进度</span>
        </div>
        <div id="productionScheduleChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">
          <span>处理中工单情况</span>
        </div>
        <div id="processingWorkOrderStatusChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">
          <span>今日投料情况</span>
        </div>
        <div id="feedingSituationChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">
          <span>今日设备利用率</span>
          <el-select v-model="deviceValue" filterable size="small" placeholder="请选择设备类型" clearable
            @change="selChange($event, 'deviceType')">
            <el-option v-for="item in deviceTypeList" :key="item.id" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </div>
        <div id="deviceUtilizationChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">
          <span>今日人员绩效</span>
          <el-select v-model="teamValue" filterable size="small" placeholder="请选择班组" clearable
            @change="selChange($event, 'team')">
            <el-option v-for="item in teamList" :key="item.id" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </div>
        <div id="staffPerformanceChart" class="chart-content"></div>
      </div>
      <div class="chart-box">
        <div class="chart-title">
          <span>质量分析</span>
          <el-select v-model="dateValue" filterable size="small" placeholder="请选择" @change="selChange($event, 'date')">
            <el-option v-for="item in dateList" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div id="qualityAnalysisChart" class="chart-content"></div>
      </div>
    </div>
  </div>
</body>

<script>
  // 发送消息给父页面(关闭odoo的菜单弹窗)
  document.addEventListener("click", () => {
    window.parent.postMessage("hidePopover", "*")
  })
  let vue = new Vue({
    el: "#app",
    delimiters: ["[[", "]]"], // 替换原本vue的[[ key ]]取值方式(与odoo使用的jinja2冲突问题)
    data() {
      return {
        windowHeight: window.innerHeight, // 窗口高度
        baseURL: "", // 基地址 https://yanshi-workstation.xbg.rokeris.com
        dwsBaseUrl: "https://dws-platform.xbg.rokeris.com/dev-api", // dws系统基地址
        loading: false, // 全局加载效果
        cardData: {},
        charts: {}, // 图表实例
        deviceTypeList: [], // 设备类型
        deviceValue: null, // 选中设备值
        teamList: [], // 班组列表
        teamValue: null, // 选中班组值
        dateList: [{ id: "今天", name: '今天' }, { id: "昨天", name: '昨天' }, { id: "本周", name: '本周' }], // 日期列表
        dateValue: "今天", // 选中日期值
      }
    },
    async created() {
      this.loading = true
      // 添加resize事件监听
      window.addEventListener("resize", this.handleResize)
      const apiList = [
        this.getWorkOrderStatisticsFn(),
        this.getProgressRatioStatisticsFn(),
        this.getProcessProgressPaginatedFn(),
        this.getTodayMaterialInputFn(),
        this.getStackLightStateFn(),
        this.getEmployeeProgressFn(),
        this.getProcessProgressFn(),
        this.getEquipmentListFn(),
        this.getTeamListFn()
      ]
      // 使用Promise.all等待所有请求完成
      await Promise.all(apiList).then(responses => {
        // responses是一个数组，按请求顺序排列
        const workOrderStatisticsResponse = responses[0]
        const progressRatioStatisticsResponse = responses[1]
        const processProgressPaginatedResponse = responses[2]
        const todayMaterialInputResponse = responses[3]
        const stackLightStateResponse = responses[4]
        const employeeProgressResponse = responses[5]
        const processProgressResponse = responses[6]
        const equipmentListResponse = responses[7]
        const teamListResponse = responses[8]
        // 工单统计数据
        this.cardData = workOrderStatisticsResponse || {}
        // 设备类型列表
        this.deviceTypeList = equipmentListResponse || []
        // 设备类型列表
        this.teamList = teamListResponse || []
        this.$nextTick(() => {
          // 生产进度（饼图）
          this.initProductionScheduleChart(progressRatioStatisticsResponse)
          // 处理中工单情况（柱状图）
          this.initProcessingWorkOrderStatusChart(processProgressPaginatedResponse)
          // 今日投料情况（柱状图）
          this.initFeedingSituationChart(todayMaterialInputResponse)
          // 今日设备利用率（柱状图）
          this.initDeviceUtilizationChart(stackLightStateResponse)
          // 今日人员绩效（条形图）
          this.initStaffPerformanceChart(employeeProgressResponse)
          // 质量分析（柱状图）
          this.initQualityAnalysisChart(processProgressResponse)
        })
      }).catch(error => {
        console.error('至少一个请求失败:', error)
      })
      this.loading = false
    },
    mounted() {
      this.$nextTick(() => {
        document.getElementById("bodyId").style.display = "block"
      })
    },
    methods: {
      // 处理窗口大小变化修改图表大小
      handleResize() {
        for (let key in this.charts) {
          this.charts[key] && this.charts[key].resize()
        }
      },
      // 接口请求方法封装
      requestApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.baseURL + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (
              result?.data?.result?.code == 0 ||
              result?.data?.result?.state == "success" ||
              result?.data?.code == 0
            ) {
              resolve(result.data)
            } else if (result?.data?.result?.code == 1) {
              reject(result.data.result.message)
            } else if (result?.data?.result?.state == "error") {
              reject(result.data.result.megs)
            } else if (result?.data?.code == 0) {
              reject(result.data.message)
            } else if (result?.data?.error) {
              reject(result.data.error.message)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 接口请求Dws系统方法封装
      requestDwsApi(
        url,
        config = {},
        errorMessage = "操作失败，请稍后重试",
        contentType = "application/json"
      ) {
        return new Promise((resolve, reject) => {
          if (!url) reject(null)
          axios({
            method: "POST",
            url: this.dwsBaseUrl + url,
            data: config,
            headers: { "Content-Type": contentType },
          }).then((result) => {
            if (result?.data?.success) {
              resolve(result.data)
            } else if (!result.data.success) {
              reject(result.data.msg)
            } else {
              reject(errorMessage)
            }
          }).catch((error) => {
            reject(errorMessage)
          })
        })
      },
      // 获取设备类型列表
      getEquipmentListFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/search_read", {
            model: "roke.mes.equipment.category",
            fields: ["id", "name"],
            domain: []
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取班组列表
      getTeamListFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/search_read", {
            model: "roke.work.team",
            fields: ["id", "name"],
            domain: []
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取工单统计
      getWorkOrderStatisticsFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_order_statistics").then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取生产进度
      getProgressRatioStatisticsFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_order/progress_ratio_statistics").then((res) => {
            resolve(res.result.data || {})
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取处理中工单数量
      getProcessProgressPaginatedFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_order/process_progress_paginated", {
            page: 1,
            page_size: 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取今日投料情况
      getTodayMaterialInputFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_order/today_material_input", {
            page: 1,
            page_size: 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取今日设备利用率
      getStackLightStateFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_record/stack_light_state", {
            category_name: this.deviceValue,
            page: 1,
            page_size: 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取今日人员绩效
      getEmployeeProgressFn() {
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_record/employee_progress", {
            team: this.teamValue,
            page: 1,
            page_size: 9999
          }).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 获取质量分析
      getProcessProgressFn() {
        const config = { page: 1, page_size: 9999 }
        if (this.dateValue == "今天") {
          config["today"] = moment().format('YYYY-MM-DD')
          config["end_date"] = moment().format('YYYY-MM-DD')
        } else if (this.dateValue == "昨天") {
          config["today"] = moment().subtract(1, 'days').format('YYYY-MM-DD')
          config["end_date"] = moment().subtract(1, 'days').format('YYYY-MM-DD')
        } else if (this.dateValue == "本周") {
          config["today"] = moment().startOf('week').format('YYYY-MM-DD')
          config["end_date"] = moment().format('YYYY-MM-DD')
        }
        return new Promise(async (resolve, reject) => {
          await this.requestApi("/roke/workstation/work_record/process_progress", config).then((res) => {
            resolve(res.result.data || [])
          }).catch((error) => {
            reject(error)
          })
        })
      },
      // 生产进度（饼图）
      initProductionScheduleChart(data) {
        const obj = JSON.parse(JSON.stringify(data))
        for (const key in obj) {
          // 移除%并转换为数字
          obj[key] = parseFloat(obj[key].replace('%', ''))
        }
        const chart = echarts.init(document.getElementById('productionScheduleChart'))
        this.charts.productionSchedule = chart
        // 单位
        const unit = "%"
        setTimeout(() => {
          chart.setOption({
            tooltip: { trigger: 'item', valueFormatter: (value) => value + unit },
            legend: { left: 'center', textStyle: { color: "#fff", fontSize: 10 } },
            series: [{
              type: 'pie',
              radius: '65%',
              center: ['50%', '55%'],
              itemStyle: { borderRadius: 3, borderColor: '#131d58', borderWidth: 2 },
              label: { show: true, formatter: '{b}: {d}' + unit, color: '#fff', fontSize: 12 },
              data: [
                { value: obj?.pending_ratio || 0, name: '待开工', itemStyle: { color: '#e24594' } },
                { value: obj?.in_progress_ratio || 0, name: '已开工', itemStyle: { color: '#1cb0d7' } },
                { value: obj?.completion_ratio || 0, name: '达成率', itemStyle: { color: '#99e484' } },
                { value: obj?.pause_ratio || 0, name: '暂停', itemStyle: { color: '#efeded' } },
              ]
            }]
          })
        }, 1)
      },
      // 处理中工单情况（柱状图）
      initProcessingWorkOrderStatusChart(data) {
        const chart = echarts.init(document.getElementById('processingWorkOrderStatusChart'))
        this.charts.processingWorkOrderStatus = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 计划数量
        const planQuantity = []
        // 完成数量
        const finishQuantity = []
        data.forEach((item) => {
          xAxisData.push(item.process_name || '')
          planQuantity.push(item.plan_qty || 0)
          finishQuantity.push(item.finish_qty || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['计划数量', '完成数量'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'deviceSynthesisEffChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '计划数量',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: planQuantity,
              itemStyle: { color: "#99e484" },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }, {
              name: '完成数量',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: finishQuantity,
              itemStyle: { color: "#6abac3" },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 今日投料情况（柱状图）
      initFeedingSituationChart(data) {
        const chart = echarts.init(document.getElementById('feedingSituationChart'))
        this.charts.feedingSituation = chart
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.product_name || '')
          seriesData.push({
            value: item.qty || 0,
            unit: item.unit || ''
          })
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: {
              trigger: 'axis', axisPointer: { type: 'shadow' },
              formatter: (params) => {
                let dom = ''
                params.forEach((item) => {
                  dom += `<div style="display: flex; align-items: center;">
                          ${item.marker} ${item.seriesName}
                          <strong style="flex:1; margin-left: 20px;text-align: right;">${item.data.value}${item.data.unit}</strong>
                        </div>`
                })
                return `${params[0].name}${dom}`
              }
            },
            legend: { left: 'center', data: ['投料数量'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'feedingSituationChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' }
            },
            series: [{
              name: '投料数量',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: { color: "#99e484" },
              label: {
                show: true, position: 'top', color: '#FFF', fontSize: 10,
                formatter: (params) => params.data.value + params.data.unit
              }
            }]
          })
        }, 1)
      },
      // 今日设备利用率（柱状图）
      initDeviceUtilizationChart(data) {
        const chart = echarts.init(document.getElementById('deviceUtilizationChart'))
        this.charts.deviceUtilization = chart
        // 单位
        const unit = '%'
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.name || item.code || '')
          seriesData.push(item.utilization_rate || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['利用率'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'feedingSituationChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '利用率',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: { color: "#5f9cf7" },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 今日人员绩效（条形图）
      initStaffPerformanceChart(data) {
        const chart = echarts.init(document.getElementById('staffPerformanceChart'))
        this.charts.staffPerformance = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.employee_name || '')
          seriesData.push(item.finish_qty || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['完成情况'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'feedingSituationChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            yAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            series: [{
              name: '完成情况',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: { color: "#bdb8fc" },
              label: { show: true, position: 'right', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 质量分析（柱状图）
      initQualityAnalysisChart(data) {
        const chart = echarts.init(document.getElementById('qualityAnalysisChart'))
        this.charts.qualityAnalysis = chart
        // 单位
        const unit = ''
        // x轴数据
        const xAxisData = []
        // 数量
        const seriesData = []
        data.forEach((item) => {
          xAxisData.push(item.process_name || '')
          seriesData.push(item.unqualified_qty || 0)
        })
        setTimeout(() => {
          chart.setOption({
            grid: { left: '5%', right: '5%', top: '20%', bottom: '2%', containLabel: true },
            tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' }, valueFormatter: (value) => value + unit },
            legend: { left: 'center', data: ['不合格数量'], textStyle: { color: "#fff", fontSize: 10 } },
            dataZoom: [{ id: 'feedingSituationChart', type: 'inside', end: (5 / xAxisData.length) * 100 }],
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: { lineStyle: { color: '#0a73ff' } },
              axisLabel: { color: '#fff', fontSize: 10 }
            },
            yAxis: {
              type: 'value',
              min: 0,
              axisLine: { show: true, lineStyle: { color: '#0a73ff' } },
              splitLine: { lineStyle: { color: '#0a73ff', width: 0.5, opacity: 0.3 } },
              axisLabel: { color: '#fff', fontSize: 10, formatter: '{value}' + unit }
            },
            series: [{
              name: '不合格数量',
              type: 'bar',
              barWidth: '40%',
              barGap: '0%',
              data: seriesData,
              itemStyle: { color: "#5f9cf7" },
              label: { show: true, position: 'top', color: '#FFF', fontSize: 10, formatter: (params) => params.value + unit }
            }]
          })
        }, 1)
      },
      // 下拉选择变化事件
      async selChange(el, type) {
        this.loading = true
        if (type == 'deviceType') {
          this.deviceValue = el
          await this.getStackLightStateFn().then((res) => {
            // 重新加载设备利用率图表
            this.initDeviceUtilizationChart(res)
          }).catch((error) => {
            this.$message.error(error)
          })
        } else if (type == 'team') {
          this.teamValue = el
          await this.getEmployeeProgressFn().then((res) => {
            // 重新加载人员绩效图表
            this.initStaffPerformanceChart(res)
          }).catch((error) => {
            this.$message.error(error)
          })
        } else if (type == 'date') {
          this.dateValue = el
          await this.getProcessProgressFn().then((res) => {
            // 重新加载质量分析图表
            this.initQualityAnalysisChart(res)
          }).catch((error) => {
            this.$message.error(error)
          })
        }
        this.loading = false
      },
    },
    beforeDestroy() {
      // 清除resize事件监听
      window.removeEventListener("resize", this.handleResize)
    }
  })
</script>

<style>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-track {
    background: #f5f7fa;
  }

  #app {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    color: #fff;
    font-family: "Microsoft YaHei", sans-serif;
    background-color: #06114f;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .card-row {
    display: flex;
    gap: 10px;

    .stat-card {
      flex: 1;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      border: 2px solid rgba(255, 255, 255, 0.2);

      .stat-value {
        font-size: 32px;
        font-weight: bold;
      }

      .stat-label {
        font-size: 16px;
      }
    }
  }

  .chart-row {
    flex: auto;
    height: 1px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .chart-box {
    width: calc((100% - 20px) / 3);
    height: calc((100% - 10px) / 2);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .chart-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        flex: 1;
        font-size: 16px;
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .chart-content {
      width: 100%;
      height: 100%;
    }
  }
</style>

</html>