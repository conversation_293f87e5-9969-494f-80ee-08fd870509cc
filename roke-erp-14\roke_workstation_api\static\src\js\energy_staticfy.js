odoo.define('roke_workstation_api.energy_staticfy', function (require) {
    "use strict";
    const AbstractAction = require('web.AbstractAction');
    const core = require('web.core');
    const QWeb = core.qweb;
    const session = require('web.session');
    const Dialog = require("web.Dialog");

    const EnergyStaticfyTemplate = AbstractAction.extend({
        template: 'EnergyStaticfyTemplate',

        start: async function () {
            await this._super(...arguments);

            let self = this;

            window.addEventListener("message", function (event) {
                
            });
        },
    });
    core.action_registry.add('roke_workstation_api.energy_staticfy', EnergyStaticfyTemplate);

    return EnergyStaticfyTemplate;
});
