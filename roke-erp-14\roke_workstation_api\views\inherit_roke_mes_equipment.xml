<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--search-->
    <record id="dws_inherit_view_roke_mes_equipment_search" model="ir.ui.view">
        <field name="name">dws.inherit.roke.mes.equipment.search</field>
        <field name="model">roke.mes.equipment</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_equipment_search"/>
        <field name="arch" type="xml">
            <xpath expr="//search" position="replace">
                <search string="设备">
                    <field string="设备" name="name" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <field name="work_center_id" string="绑定工位"/>
                    <field name="workshop_id"/>
                    <field name="plant_id"/>
                    <group expand="1" string="Group By">
                        <filter string="绑定车间" name="plant_id" context="{'group_by':'plant_id'}"/>
                        <filter string="绑定产线" name="workshop_id" context="{'group_by':'workshop_id'}"/>
                    </group>
                    <searchpanel>
                        <field name="plant_id" icon="fa-wrench" enable_counters="1" expand="1"/>
                        <field name="workshop_id" icon="fa-wrench" enable_counters="1" expand="1"/>
                    </searchpanel>
                </search>
            </xpath>
        </field>
    </record>

    <record id="view_dws_inherit_roke_mes_equipment_form" model="ir.ui.view">
        <field name="name">view_dws_inherit_roke_mes_equipment_form</field>
        <field name="model">roke.mes.equipment</field>
        <field name="inherit_id" ref="roke_mes_equipment.view_roke_mes_equipment_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="replace">
                  <form string="设备">
                    <header>
                        <button name="btn_cr_qr_image" type="object" string="生成二维码" class="oe_highlight"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box"/>
                        <widget name="web_ribbon" text="正常" bg_color="bg-success"
                                attrs="{'invisible': ['|', ('active', '=', False), ('in_repair', '=', True)]}"/>
                        <widget name="web_ribbon" text="归档" bg_color="bg-danger"
                                attrs="{'invisible': [('active', '=', True)]}"/>
                        <widget name="web_ribbon" text="报修" bg_color="bg-warning"
                                attrs="{'invisible': ['|', ('active', '=', False), ('in_repair', '=', False)]}"/>
                        <field name="active" invisible="1"/>
                        <field name="in_repair" invisible="1"/>
                        <group string="设备基础信息" col="3">
                            <group>
                                <field name="name" required="1"/>
                                <field name="specification" required="1"/>
                                <field name="e_state" required="1"/>
                            </group>
                            <group>
                                <field name="code"/>
                                <field name="manufacture_date"/>
                            </group>
                            <group>
                                <field name="category_id" required="1" options="{'no_open': True}"/>
                                <field name="active_date" required="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="其他信息">
                                <group col="4">
                                    <group>
                                        <field name="index_code"/>
                                        <field name="manufacturer"/>
                                        <field name="manufacturer_code"/>
                                    </group>
                                    <group>
                                        <field name="archives_code"/>
                                        <field name="create_archives_user_id"/>
                                        <field name="archives_date"/>

                                    </group>
                                    <group>
                                        <field name="register_code"/>
                                        <field name="test_org_id"/>
                                        <field name="warranty_date"/>
                                    </group>
                                    <group>
                                        <field name="use_permit_code"/>
                                        <field name="last_test_date"/>
                                        <field name="next_test_date"/>
                                    </group>
                                    <group>
                                        <field name="qr_image" widget="image" readonly="1" img_width="128" img_height="128"
                                               height="128"/>
                                    </group>
                                </group>
                                <field name="note" placeholder="此处录入设备描述或内部备注"/>
                            </page>
                            <page string="附属设备信息">
                                <field name="auxiliary_equipment_lines"
                                    context="{'tree_view_ref': 'roke_mes_equipment.view_roke_auxiliary_equipment_line_tree'}"/>
                            </page>
                            <page string="备件信息">
                                <field name="spare_part_ids"/>
                            </page>
                            <page string="外包装信息">
                                <group col="4">
                                    <group>
                                        <field name="is_outer_packaging_intact" widget="radio" options="{'horizontal': True}"/>
                                    </group>
                                    <group></group>
                                    <group></group>
                                    <group></group>
                                </group>
                                <group>
                                    <field name="handling_suggestion" placeholder="此处可以填写处理意见"/>
                                </group>
                            </page>
                            <page string="维修记录">
                                <field name="repair_ids" readonly="1" context="{'tree_view_ref':
                                    'roke_mes_equipment.view_roke_mes_repair_order_tree',
                                    'form_view_ref':'roke_mes_equipment.view_roke_mes_repair_order_form'}">
                                    <tree>
                                        <field name="code"/>
                                        <field name="report_user_id"/>
                                        <field name="report_time"/>
                                        <field name="fault_description"/>
                                        <field name="user_id"/>
                                        <field name="repair_user_id"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="点检记录">
                                <field name="check_ids" readonly="1">
                                    <tree string="点检记录">
                                        <field name="check_plan_id"/>
                                        <field name="assign_user_ids" widget="many2many_tags"/>
                                        <field name="start_date" />
                                        <field name="estimated_completion_time"/>
                                        <field name="finish_user_id"/>
                                        <field name="finish_time"/>
                                        <field name="description"/>
                                        <field name="normal_state"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="保养记录">
                                <field name="maintenance_ids" readonly="1"
                                 context="{'tree_view_ref':
                                    'roke_mes_equipment.view_roke_mes_maintenance_order_tree',
                                    'form_view_ref':'roke_mes_equipment.view_roke_mes_maintenance_order_form'}"
                                >
                                    <tree>
                                        <field name="code"/>
                                        <field name="maintenance_scheme_id"/>
                                        <field name="last_maintenance_date"/>
                                        <field name="user_id"/>
                                        <field name="state"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="更换件记录">
                                <field name="change_record_ids">
                                    <tree editable="bottom">
                                        <field name="spare_part_id"/>
                                        <field name="removed_part_id"/>
                                        <field name="replacement_time"/>
                                        <field name="expiry_time"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </xpath>
            <xpath expr="//field[@name='specification']" position="after">
                <field name="plant_id"/>
            </xpath>
            <xpath expr="//field[@name='manufacture_date']" position="after">
                <field name="workshop_id" attrs="{'readonly': [('plant_id', '=', False)]}"/>
            </xpath>
            <xpath expr="//field[@name='active_date']" position="after">
                <field name="work_center_id" string="工位" attrs="{'readonly': [('workshop_id', '=', False)]}"/>
            </xpath>
        </field>
    </record>

    <record id="dws_inherit_view_roke_mes_equipment_kanban" model="ir.ui.view">
        <field name="name">roke.mes.equipment.kanban</field>
        <field name="model">roke.mes.equipment</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_view o_kanban_mobile" js_class="WSKanbanView">
                <field name="work_center_id"/>
                <field name="id"/>
                <field name="code"/>
                <field name="name"/>
                <field name="e_state"/>
                <field name="light_state"/>
                <templates>
                    <t t-name="kanban-box">
                        <div name="roke_equipment_box" style="display: flex; flex-direction: row; flex-wrap: nowrap;">
                            <div style="flex: 1;">
                                <div style="border-left: 5px solid #3a5268; padding-left: 10px; font-size: 15px; margin-bottom: 10px;">
                                    <t t-if="record.work_center_id.raw_value">
                                        <field name="work_center_id"/>
                                    </t>
                                    <t t-else="">
                                        <span>未绑定工位</span>
                                    </t>
                                </div>
                                <div style="font-size: 15px;">
                                    <span>设备名称</span>
                                    <field name="name"/>
                                </div>
                            </div>

                            <div style="flex: 1;" t-attf-class="text-right light_{{record.code.value}}_card">
                                <field name="light_state"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <record id="roke_mes_equipment.view_roke_mes_equipment_action" model="ir.actions.act_window">
        <field name="view_mode">tree,form,kanban,pivot</field>
    </record>

    <record id="view_roke_equipment_light_config_form" model="ir.ui.view">
        <field name="name">roke.equipment.light.config.form</field>
        <field name="model">roke.equipment.light.config</field>
        <field name="arch" type="xml">
            <form string="三色灯配置" create="0" delete="0" duplicate='0' import="0" form_import="0">
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: green; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_green"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_green_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: yellow; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_yellow"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_yellow_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: red; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_red"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_red_notify" widget="boolean_toggle"/>
                    </div>
                </div>
                <div class="mb16" style="display: flex; flex-direction: row; align-items: center;">
                    <span style="background: gray; border: 1px solid #afafaf; width: 16px; height: 16px; border-radius: 50%"></span>
                    <span class="ml16">代表</span>
                    <div class="ml16" style="width: 250px">
                        <field name="label_gray"/>
                    </div>
                    <span class="ml16">是否需要通知</span>
                    <div class="ml16" style="width: 250px">
                        <field name="is_gray_notify" widget="boolean_toggle"/>
                    </div>
                </div>
            </form>
        </field>
    </record>

    <record id="view_roke_equipment_light_config_tree" model="ir.ui.view">
        <field name="name">roke.equipment.light.config.tree</field>
        <field name="model">roke.equipment.light.config</field>
        <field name="arch" type="xml">
            <tree string="三色灯配置" create="0" delete="0" duplicate='0' import="0">
                <field name="label_green"/>
                <field name="label_yellow"/>
                <field name="label_red"/>
                <field name="label_gray"/>
            </tree>
        </field>
    </record>

    <record id="action_roke_equipment_light_config" model="ir.actions.act_window">
        <field name="name">三色灯配置</field>
        <field name="res_model">roke.equipment.light.config</field>
        <field name="view_mode">tree,form</field>
        <field name="type">ir.actions.act_window</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
    </record>
<!--    <record id="action_roke_equipment_status" model="ir.actions.client">-->
<!--        <field name="name">异常统计</field>-->
<!--        <field name="tag">roke_workstation_api.roke_equipment_status</field>-->
<!--        <field name="target">current</field>-->
<!--    </record>-->
    <record id="action_roke_equipment_status_url" model="ir.actions.act_url">
        <field name="name">异常统计</field>
        <field name="url">/roke/equipment/status</field>
        <field name="target">new</field>
    </record>

    <record id="action_abnormal_alarm_census" model="ir.actions.client">
        <field name="name">安灯数据</field>
        <field name="tag">roke_workstation_api.abnormal_alarm</field>
        <field name="target">current</field>
    </record>

    <record id="action_work_order_staticfy" model="ir.actions.client">
        <field name="name">工单统计看板</field>
        <field name="tag">roke_workstation_api.work_order_staticfy</field>
        <field name="target">current</field>
    </record>

    <record id="action_equipment_staticfy" model="ir.actions.client">
        <field name="name">设备统计看板</field>
        <field name="tag">roke_workstation_api.equipment_staticfy</field>
        <field name="target">current</field>
    </record>

   <record id="action_energy_staticfy" model="ir.actions.client">
        <field name="name">能耗统计看板</field>
        <field name="tag">roke_workstation_api.energy_staticfy</field>
        <field name="target">current</field>
    </record>

</odoo>