<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_roke_abnormal_alarm_views_tree_inherit" model="ir.ui.view">
            <field name="name">view_roke_abnormal_alarm_views_tree_inherit</field>
            <field name="model">roke.abnormal.alarm</field>
            <field name="inherit_id" ref="roke_abnormal_alarm.roke_abnormal_alarm_views_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state_id']" position="replace">
                    <field name="abnormal_status"/>
                </xpath>
            </field>
        </record>
        <!-- 继承 roke_abnormal.alarm 的表单视图 -->
        <record id="view_roke_abnormal_alarm_form_inherit" model="ir.ui.view">
            <field name="name">roke.abnormal.alarm.form.inherit</field>
            <field name="model">roke.abnormal.alarm</field>
            <field name="inherit_id" ref="roke_abnormal_alarm.roke_abnormal_alarm_views_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='group_top']" position="after">
                    <group col="4"  name='time'>
                        <group>
                            <field name="confirm_time" />
                            <field name="abnormal_status" readonly="1"/>
                        </group>
                        <group>
                            <field name="arrive_time" readonly="1" />
                            <field name="arrive_user_id" options="{'no_create': True}" />
                        </group>
                       <group>
                            <field name="finished_time" readonly="1"/>
                            <field name="alarm_state" />
                        </group>
                       <group>
                            <field name="equipment_id" force_save="1"/>
                            <field name="wait_duration" />
                       </group>
                    </group>
                </xpath>
            </field>
        </record>


    </data>
</odoo>