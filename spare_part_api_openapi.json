{"openapi": "3.0.0", "info": {"title": "备件管理接口", "description": "备件创建、使用记录查询和单位列表查询接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/spare_part/create": {"post": {"summary": "创建备件", "description": "创建新的备件记录", "tags": ["备件管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSparePartRequest"}, "examples": {"example1": {"summary": "创建轴承备件", "value": {"name": "轴承", "theoretical_life": 12, "life_unit": "month", "manufacturer": "SKF", "model": "6205", "uom_id": 1, "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."}}}}}}, "responses": {"200": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSparePartResponse"}, "examples": {"success": {"summary": "创建成功", "value": {"state": "success", "msgs": "备件创建成功", "data": {"id": 1, "name": "轴承", "code": "SP001", "model": "6205", "manufacturer": "SKF", "theoretical_life": 12, "life_unit": "month", "uom_name": "件"}}}}}}}, "400": {"description": "参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"nameRequired": {"summary": "备件名称不能为空", "value": {"state": "error", "msgs": "备件名称不能为空"}}}}}}}}}, "/roke/spare_part/uom_list": {"post": {"summary": "获取计量单位列表", "description": "获取所有可用的计量单位列表", "tags": ["备件管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}, "examples": {"empty": {"summary": "空请求体", "value": {}}}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UomListResponse"}, "examples": {"success": {"summary": "获取成功", "value": {"state": "success", "msgs": "获取成功", "data": [{"id": 1, "name": "件", "note": ""}, {"id": 2, "name": "千克", "note": ""}]}}}}}}}}}, "/roke/spare_part/usage_records": {"post": {"summary": "获取备件使用记录", "description": "根据备件ID查询备件使用记录列表", "tags": ["备件管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageRecordsRequest"}, "examples": {"example1": {"summary": "查询备件使用记录", "value": {"spare_part_id": 1, "page": 1, "page_size": 10}}}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsageRecordsResponse"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"CreateSparePartRequest": {"type": "object", "required": ["name", "theoretical_life", "life_unit", "uom_id"], "properties": {"name": {"type": "string", "maxLength": 20, "description": "备件名称，必填，最大20字符", "example": "轴承"}, "theoretical_life": {"type": "integer", "minimum": 1, "description": "理论寿命，必填，正整数", "example": 12}, "life_unit": {"type": "string", "enum": ["month", "year"], "description": "寿命单位，必填，只能是month或year", "example": "month"}, "manufacturer": {"type": "string", "maxLength": 20, "description": "厂家名称，可选，最大20字符", "example": "SKF"}, "model": {"type": "string", "maxLength": 20, "description": "型号，可选，最大20字符", "example": "6205"}, "uom_id": {"type": "integer", "description": "计量单位ID，必填", "example": 1}, "image": {"type": "string", "description": "备件图片，可选，base64编码", "example": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."}}}, "CreateSparePartResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success"], "description": "响应状态"}, "msgs": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "备件ID"}, "name": {"type": "string", "description": "备件名称"}, "code": {"type": "string", "description": "备件编号（自动生成）"}, "model": {"type": "string", "description": "型号"}, "manufacturer": {"type": "string", "description": "厂家"}, "theoretical_life": {"type": "integer", "description": "理论寿命"}, "life_unit": {"type": "string", "description": "寿命单位"}, "uom_name": {"type": "string", "description": "计量单位名称"}}}}}, "UomListResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success"], "description": "响应状态"}, "msgs": {"type": "string", "description": "响应消息"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "单位ID"}, "name": {"type": "string", "description": "单位名称"}, "note": {"type": "string", "description": "备注信息"}}}}}}, "UsageRecordsRequest": {"type": "object", "required": ["spare_part_id"], "properties": {"spare_part_id": {"type": "integer", "description": "备件ID，必填", "example": 1}, "page": {"type": "integer", "minimum": 1, "default": 1, "description": "页码，可选，默认1", "example": 1}, "page_size": {"type": "integer", "minimum": 1, "default": 10, "description": "每页数量，可选，默认10", "example": 10}}}, "UsageRecordsResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success"], "description": "响应状态"}, "msgs": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "properties": {"usage_records": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "使用记录ID"}, "equipment_id": {"type": "integer", "nullable": true, "description": "设备ID"}, "equipment_name": {"type": "string", "description": "设备名称"}, "removed_part_id": {"type": "integer", "nullable": true, "description": "被拆下备件ID"}, "removed_part_name": {"type": "string", "description": "被拆下备件名称"}, "replacement_time": {"type": "string", "format": "date-time", "description": "更换时间"}, "expiry_time": {"type": "string", "format": "date-time", "description": "到期时间"}, "remaining_days": {"type": "integer", "description": "剩余天数"}, "usage_days": {"type": "integer", "description": "已使用天数"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_count": {"type": "integer", "description": "总记录数"}, "total_pages": {"type": "integer", "description": "总页数"}, "has_next": {"type": "boolean", "description": "是否有下一页"}, "has_prev": {"type": "boolean", "description": "是否有上一页"}}}}}}}, "ErrorResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["error"], "description": "响应状态"}, "msgs": {"type": "string", "description": "错误消息"}}}}}}