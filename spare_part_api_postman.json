{"info": {"name": "备件管理接口", "description": "备件创建、使用记录查询和单位列表查询接口", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "创建备件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"轴承\",\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"manufacturer\": \"SKF\",\n  \"model\": \"6205\",\n  \"uom_id\": 1,\n  \"image\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}, "description": "创建新的备件记录\n\n## 请求参数说明\n\n| 参数名 | 类型 | 必填 | 长度限制 | 说明 |\n|--------|------|------|----------|---------|\n| name | string | 是 | 最大20字符 | 备件名称 |\n| theoretical_life | integer | 是 | - | 理论寿命，必须为正整数 |\n| life_unit | string | 是 | - | 寿命单位，只能是\"month\"或\"year\" |\n| manufacturer | string | 否 | 最大20字符 | 厂家名称 |\n| model | string | 否 | 最大20字符 | 型号 |\n| uom_id | integer | 是 | - | 计量单位ID |\n| image | string | 否 | - | 备件图片，base64编码 |\n\n## 验证规则\n\n1. 备件名称：必填，不能为空，最大20个字符\n2. 理论寿命：必填，必须为正整数\n3. 寿命单位：必填，只能选择\"month\"（月）或\"year\"（年）\n4. 计量单位：必填，必须是系统中存在的单位ID\n5. 厂家：可选，最大20个字符\n6. 型号：可选，最大20个字符\n7. 图片：可选，支持base64编码的图片数据"}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"轴承\",\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"manufacturer\": \"SKF\",\n  \"model\": \"6205\",\n  \"uom_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"备件创建成功\",\n  \"data\": {\n    \"id\": 1,\n    \"name\": \"轴承\",\n    \"code\": \"SP001\",\n    \"model\": \"6205\",\n    \"manufacturer\": \"SKF\",\n    \"theoretical_life\": 12,\n    \"life_unit\": \"month\",\n    \"uom_name\": \"件\"\n  }\n}"}, {"name": "参数错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"uom_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"备件名称不能为空\"\n}"}]}, {"name": "获取计量单位列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/spare_part/uom_list", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "uom_list"]}, "description": "获取所有可用的计量单位列表，供创建备件时选择\n\n## 请求参数\n\n无需传入参数，发送空的JSON对象即可。\n\n## 响应说明\n\n返回系统中所有可用的计量单位列表，包含单位ID、名称和备注信息。"}, "response": [{"name": "获取成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/uom_list", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "uom_list"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"name\": \"件\",\n      \"note\": \"\"\n    },\n    {\n      \"id\": 2,\n      \"name\": \"千克\",\n      \"note\": \"\"\n    },\n    {\n      \"id\": 3,\n      \"name\": \"米\",\n      \"note\": \"\"\n    }\n  ]\n}"}]}, {"name": "获取备件使用记录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"page\": 1,\n  \"page_size\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "description": "根据备件ID查询该备件的使用记录列表，支持分页查询\n\n## 请求参数说明\n\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|---------|\n| spare_part_id | integer | 是 | 备件ID |\n| page | integer | 否 | 页码，默认为1 |\n| page_size | integer | 否 | 每页数量，默认为10 |\n\n## 业务逻辑\n\n- 根据备件的理论寿命和寿命单位自动计算到期时间\n- 剩余天数 = 到期时间 - 当前时间\n- 已使用天数 = 当前时间 - 更换时间\n- 支持年和月两种寿命单位的计算\n- 使用记录按更换时间倒序排列（最新的在前）\n- 时间字段已经进行了时区转换（+8小时），返回的是本地时间"}, "response": [{"name": "获取成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"page\": 1,\n  \"page_size\": 10\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": {\n    \"usage_records\": [\n      {\n        \"id\": 1,\n        \"equipment_id\": 5,\n        \"equipment_name\": \"生产线设备A\",\n        \"removed_part_id\": 2,\n        \"removed_part_name\": \"旧轴承\",\n        \"replacement_time\": \"2024-01-15 14:30:00\",\n        \"expiry_time\": \"2025-01-15 14:30:00\",\n        \"remaining_days\": 180,\n        \"usage_days\": 185\n      }\n    ],\n    \"pagination\": {\n      \"page\": 1,\n      \"page_size\": 10,\n      \"total_count\": 1,\n      \"total_pages\": 1,\n      \"has_next\": false,\n      \"has_prev\": false\n    }\n  }\n}"}, {"name": "参数错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"page_size\": 10\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"缺少必传参数: spare_part_id\"\n}"}]}, {"name": "编辑备件信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"name\": \"新轴承\",\n  \"theoretical_life\": 18,\n  \"life_unit\": \"month\",\n  \"manufacturer\": \"NSK\",\n  \"model\": \"6206\",\n  \"uom_id\": 2,\n  \"image\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/spare_part/update", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "update"]}, "description": "更新现有备件的信息\n\n## 请求参数说明\n\n| 参数名 | 类型 | 必填 | 长度限制 | 说明 |\n|--------|------|------|----------|---------|\n| spare_part_id | integer | 是 | - | 备件ID |\n| name | string | 否 | 最大20字符 | 备件名称 |\n| theoretical_life | integer | 否 | - | 理论寿命，必须为正整数 |\n| life_unit | string | 否 | - | 寿命单位，只能是\"month\"或\"year\" |\n| manufacturer | string | 否 | 最大20字符 | 厂家名称 |\n| model | string | 否 | 最大20字符 | 型号 |\n| uom_id | integer | 否 | - | 计量单位ID |\n| image | string | 否 | - | 备件图片，base64编码 |\n\n## 更新规则\n\n1. **备件ID**：必填，用于定位要更新的备件记录\n2. **其他字段**：均为可选，只传入需要更新的字段即可\n3. **验证规则**：与创建接口相同的验证规则\n4. **部分更新**：支持部分字段更新，不传入的字段保持原值不变\n5. **空值处理**：传入空字符串会清空对应字段（可选字段）\n\n## 注意事项\n\n- 如果备件已有使用记录，修改理论寿命和寿命单位会影响现有记录的到期时间计算\n- 备件编号（code）不可修改，系统自动生成\n- 至少需要传入一个要更新的字段，否则返回错误"}, "response": [{"name": "更新成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"name\": \"新轴承\",\n  \"theoretical_life\": 18,\n  \"manufacturer\": \"NSK\"\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/update", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "update"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"备件信息更新成功\",\n  \"data\": {\n    \"id\": 1,\n    \"name\": \"新轴承\",\n    \"code\": \"SP001\",\n    \"model\": \"6205\",\n    \"manufacturer\": \"NSK\",\n    \"theoretical_life\": 18,\n    \"life_unit\": \"month\",\n    \"uom_name\": \"件\"\n  }\n}"}, {"name": "备件不存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 999,\n  \"name\": \"新轴承\"\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/update", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "update"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"备件记录不存在\"\n}"}, {"name": "没有更新字段", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1\n}"}, "url": {"raw": "{{baseUrl}}/roke/spare_part/update", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "update"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"没有要更新的字段\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8069", "type": "string", "description": "API服务器地址"}, {"key": "token", "value": "", "type": "string", "description": "用户认证token"}]}