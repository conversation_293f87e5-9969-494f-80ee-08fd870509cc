{"info": {"name": "备件管理接口", "description": "备件创建和单位列表查询接口", "version": "1.0.0"}, "items": [{"name": "创建备件", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}, "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"轴承\",\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"manufacturer\": \"SKF\",\n  \"model\": \"6205\",\n  \"uom_id\": 1,\n  \"image\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...\"\n}", "options": {"raw": {"language": "json"}}}, "description": "创建新的备件记录"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"轴承\",\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"manufacturer\": \"SKF\",\n  \"model\": \"6205\",\n  \"uom_id\": 1\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"备件创建成功\",\n  \"data\": {\n    \"id\": 1,\n    \"name\": \"轴承\",\n    \"code\": \"SP001\",\n    \"model\": \"6205\",\n    \"manufacturer\": \"SKF\",\n    \"theoretical_life\": 12,\n    \"life_unit\": \"month\",\n    \"uom_name\": \"件\"\n  }\n}"}, {"name": "参数错误响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/create", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "create"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"theoretical_life\": 12,\n  \"life_unit\": \"month\",\n  \"uom_id\": 1\n}"}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"备件名称不能为空\"\n}"}]}, {"name": "获取计量单位列表", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/uom_list", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "uom_list"]}, "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "description": "获取所有可用的计量单位列表"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/uom_list", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "uom_list"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": [\n    {\n      \"id\": 1,\n      \"name\": \"件\",\n      \"note\": \"\"\n    },\n    {\n      \"id\": 2,\n      \"name\": \"千克\",\n      \"note\": \"\"\n    },\n    {\n      \"id\": 3,\n      \"name\": \"米\",\n      \"note\": \"\"\n    }\n  ]\n}"}]}], "documentation": {"description": "## 备件管理接口说明\n\n### 1. 创建备件接口 (/roke/spare_part/create)\n\n该接口用于创建新的备件记录。\n\n#### 请求参数\n\n| 参数名 | 类型 | 必填 | 长度限制 | 说明 | 示例值 |\n|--------|------|------|----------|------|--------|\n| name | string | 是 | 最大20字符 | 备件名称 | \"轴承\" |\n| theoretical_life | integer | 是 | - | 理论寿命，必须为正整数 | 12 |\n| life_unit | string | 是 | - | 寿命单位，只能是\"month\"或\"year\" | \"month\" |\n| manufacturer | string | 否 | 最大20字符 | 厂家名称 | \"SKF\" |\n| model | string | 否 | 最大20字符 | 型号 | \"6205\" |\n| uom_id | integer | 是 | - | 计量单位ID | 1 |\n| image | string | 否 | - | 备件图片，base64编码 | \"data:image/jpeg;base64,...\" |\n\n#### 响应参数\n\n**成功响应 (state: \"success\")**\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| state | string | 响应状态 |\n| msgs | string | 响应消息 |\n| data | object | 创建的备件数据 |\n| data.id | integer | 备件ID |\n| data.name | string | 备件名称 |\n| data.code | string | 备件编号（自动生成） |\n| data.model | string | 型号 |\n| data.manufacturer | string | 厂家 |\n| data.theoretical_life | integer | 理论寿命 |\n| data.life_unit | string | 寿命单位 |\n| data.uom_name | string | 计量单位名称 |\n\n#### 验证规则\n\n1. **备件名称**：必填，不能为空，最大20个字符\n2. **理论寿命**：必填，必须为正整数\n3. **寿命单位**：必填，只能选择\"month\"（月）或\"year\"（年）\n4. **计量单位**：必填，必须是系统中存在的单位ID\n5. **厂家**：可选，最大20个字符\n6. **型号**：可选，最大20个字符\n7. **图片**：可选，支持base64编码的图片数据\n\n### 2. 获取计量单位列表接口 (/roke/spare_part/uom_list)\n\n该接口用于获取系统中所有可用的计量单位列表，供创建备件时选择。\n\n#### 请求参数\n\n无需传入参数，发送空的JSON对象即可。\n\n#### 响应参数\n\n**成功响应 (state: \"success\")**\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| state | string | 响应状态 |\n| msgs | string | 响应消息 |\n| data | array | 计量单位列表 |\n| data[].id | integer | 单位ID |\n| data[].name | string | 单位名称 |\n| data[].note | string | 备注信息 |\n\n## 注意事项\n\n1. 两个接口都需要用户认证，请在请求头中包含有效的Authorization token\n2. 备件编号会在创建时自动生成，无需手动指定\n3. 图片上传支持常见的图片格式，建议使用base64编码\n4. 建议先调用单位列表接口获取可用单位，再创建备件\n5. 理论寿命和寿命单位将用于计算备件的到期时间\n\n## 错误码说明\n\n- **参数验证错误**：返回具体的验证失败信息\n- **数据不存在错误**：如选择的计量单位不存在\n- **系统错误**：服务器内部错误，返回错误详情"}}