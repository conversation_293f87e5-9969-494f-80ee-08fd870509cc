{"info": {"name": "备件使用记录查询接口", "description": "根据备件ID查询该备件的使用记录列表，支持分页查询", "version": "1.0.0"}, "item": {"name": "获取备件使用记录", "request": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"page\": 1,\n  \"page_size\": 10\n}", "options": {"raw": {"language": "json"}}}, "description": "根据备件ID查询备件使用记录列表"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"page\": 1,\n  \"page_size\": 10\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": {\n    \"usage_records\": [\n      {\n        \"id\": 1,\n        \"equipment_id\": 5,\n        \"equipment_name\": \"生产线设备A\",\n        \"removed_part_id\": 2,\n        \"removed_part_name\": \"旧轴承\",\n        \"replacement_time\": \"2024-01-15 14:30:00\",\n        \"expiry_time\": \"2025-01-15 14:30:00\",\n        \"remaining_days\": 180,\n        \"usage_days\": 185\n      }\n    ],\n    \"pagination\": {\n      \"page\": 1,\n      \"page_size\": 10,\n      \"total_count\": 1,\n      \"total_pages\": 1,\n      \"has_next\": false,\n      \"has_prev\": false\n    }\n  }\n}"}, {"name": "参数错误响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"page_size\": 10\n}"}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"缺少必传参数: spare_part_id\"\n}"}, {"name": "参数格式错误响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": \"invalid\",\n  \"page\": 1,\n  \"page_size\": 10\n}"}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"参数格式错误，spare_part_id必须为整数\"\n}"}, {"name": "服务器错误响应", "originalRequest": {"method": "POST", "url": {"raw": "{{baseUrl}}/roke/spare_part/usage_records", "host": ["{{baseUrl}}"], "path": ["roke", "spare_part", "usage_records"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"spare_part_id\": 1,\n  \"page\": 1,\n  \"page_size\": 10\n}"}}, "status": "Internal Server Error", "code": 500, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"获取备件使用记录失败: 数据库连接错误\"\n}"}]}, "documentation": {"description": "## 接口说明\n\n该接口用于根据备件ID查询该备件的使用记录列表，支持分页查询。返回的使用记录按更换时间倒序排列。\n\n## 请求参数\n\n| 参数名 | 类型 | 必填 | 说明 | 示例值 |\n|--------|------|------|------|--------|\n| spare_part_id | integer | 是 | 备件ID | 1 |\n| page | integer | 否 | 页码，默认为1 | 1 |\n| page_size | integer | 否 | 每页数量，默认为10 | 10 |\n\n## 响应参数\n\n### 成功响应 (state: \"success\")\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| state | string | 响应状态，成功时为\"success\" |\n| msgs | string | 响应消息 |\n| data | object | 响应数据 |\n| data.usage_records | array | 使用记录列表 |\n| data.pagination | object | 分页信息 |\n\n### 使用记录对象 (usage_records数组元素)\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| id | integer | 使用记录ID |\n| equipment_id | integer | 设备ID，可能为null |\n| equipment_name | string | 设备名称 |\n| removed_part_id | integer | 被拆下备件ID，可能为null |\n| removed_part_name | string | 被拆下备件名称 |\n| replacement_time | string | 更换时间，格式：YYYY-MM-DD HH:mm:ss |\n| expiry_time | string | 到期时间，格式：YYYY-MM-DD HH:mm:ss |\n| remaining_days | integer | 剩余天数 |\n| usage_days | integer | 已使用天数 |\n\n### 分页信息对象 (pagination)\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| page | integer | 当前页码 |\n| page_size | integer | 每页数量 |\n| total_count | integer | 总记录数 |\n| total_pages | integer | 总页数 |\n| has_next | boolean | 是否有下一页 |\n| has_prev | boolean | 是否有上一页 |\n\n### 错误响应 (state: \"error\")\n\n| 参数名 | 类型 | 说明 |\n|--------|------|------|\n| state | string | 响应状态，错误时为\"error\" |\n| msgs | string | 错误消息 |\n\n## 注意事项\n\n1. 时间字段已经进行了时区转换（+8小时），返回的是本地时间\n2. 使用记录按更换时间倒序排列（最新的在前）\n3. spare_part_id参数必须为有效的整数\n4. 分页参数page和page_size必须为正整数\n5. 需要用户认证，请在请求头中包含有效的Authorization token\n\n## 业务逻辑\n\n- 根据备件的理论寿命和寿命单位自动计算到期时间\n- 剩余天数 = 到期时间 - 当前时间\n- 已使用天数 = 当前时间 - 更换时间\n- 支持年和月两种寿命单位的计算"}}