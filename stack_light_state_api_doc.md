# 安灯状态查询接口文档

## 接口概述

**接口路径**: `/roke/get/stack_light/state`  
**请求方法**: `POST`  
**认证方式**: Bearer Token  
**内容类型**: `application/json`

该接口用于获取工厂所有设备的安灯状态统计信息，以及当前的点检、维修、保养任务数量。主要用于生产管理看板和设备监控大屏。

## 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | application/json |
| Authorization | string | 是 | Bearer {token} |

### Body Parameters
无需传入参数，发送空的JSON对象即可。

### 请求示例
```json
{}
```

## 响应格式

### 成功响应
```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "total": 50,
    "green": 35,
    "yellow": 8,
    "red": 5,
    "gray": 2,
    "check_count": 3,
    "repair_count": 2,
    "maintain_count": 1
  }
}
```

## 响应参数说明

### 基本响应字段
| 参数名 | 类型 | 说明 |
|--------|------|------|
| state | string | 响应状态，固定为"success" |
| msgs | string | 响应消息 |
| data | object | 统计数据 |

### 统计数据字段 (data)
| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| total | integer | 设备总数量 | 50 |
| green | integer | 绿灯状态设备数量（正常运行） | 35 |
| yellow | integer | 黄灯状态设备数量（警告状态） | 8 |
| red | integer | 红灯状态设备数量（故障/停机） | 5 |
| gray | integer | 灰灯状态设备数量（离线/未知） | 2 |
| check_count | integer | 进行中的点检任务数量 | 3 |
| repair_count | integer | 待处理的维修任务数量 | 2 |
| maintain_count | integer | 待处理的保养任务数量 | 1 |

## 业务逻辑详解

### 1. 设备总数统计
- 统计系统中所有有编号（code字段不为空）的设备
- 查询条件：`roke.mes.equipment` 表中 `code != False`

### 2. 安灯状态统计
接口通过以下步骤获取安灯状态：

#### 2.1 获取工厂代码
```python
factory_code = env['ir.config_parameter'].get_param('database.uuid', default="")
```

#### 2.2 调用外部DWS平台API
```python
url = f"https://dws-platform.xbg.rokeris.com/dev-api/public/device/count/{factory_code}"
response = requests.get(url)
```

#### 2.3 状态映射
- **green**: 设备正常运行，无异常
- **yellow**: 设备警告状态，可能需要关注
- **red**: 设备故障或停机，需要立即处理
- **gray**: 设备离线或状态未知

### 3. 维保任务统计

#### 3.1 点检任务统计
```python
check_count = env["roke.mes.eqpt.spot.check.record"].search_count([
    ("state", "=", "in_progress")
])
```

#### 3.2 维修任务统计
```python
repair_count = env["roke.mes.maintenance.order"].search_count([
    ("state", "=", "assign"), 
    ("type", "=", "repair")
])
```

#### 3.3 保养任务统计
```python
maintain_count = env["roke.mes.maintenance.order"].search_count([
    ("state", "=", "assign"), 
    ("type", "=", "maintain")
])
```

## 外部依赖

### DWS平台集成
- **平台名称**: DWS设备监控平台
- **基础URL**: https://dws-platform.xbg.rokeris.com
- **API端点**: `/dev-api/public/device/count/{factory_code}`
- **请求方式**: GET
- **认证方式**: 无需认证（公开接口）

### 工厂代码配置
工厂代码从系统参数中获取：
- **参数名**: `database.uuid`
- **配置路径**: 设置 → 技术 → 参数 → 系统参数

## 应用场景

### 1. 生产管理看板
```javascript
// 前端调用示例
fetch('/roke/get/stack_light/state', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({})
})
.then(response => response.json())
.then(data => {
  // 更新看板显示
  updateDashboard(data.data);
});
```

### 2. 设备监控大屏
- 实时显示设备状态分布
- 展示设备健康度指标
- 维保任务提醒

### 3. 移动端应用
- 快速了解工厂设备状态
- 维保任务数量提醒
- 异常设备快速定位

## 错误处理

### 外部平台异常
当DWS平台不可用时：
- 安灯状态统计全部为0
- 维保任务统计正常返回
- 接口仍返回成功状态

```json
{
  "state": "success",
  "msgs": "获取成功",
  "data": {
    "total": 50,
    "green": 0,
    "yellow": 0,
    "red": 0,
    "gray": 0,
    "check_count": 3,
    "repair_count": 2,
    "maintain_count": 1
  }
}
```

## 性能考虑

### 1. 缓存策略
建议前端实现缓存：
- 缓存时间：30秒-1分钟
- 避免频繁调用外部API

### 2. 超时处理
外部API调用建议设置超时：
- 连接超时：5秒
- 读取超时：10秒

### 3. 降级方案
当外部平台不可用时：
- 仍返回维保任务统计
- 安灯状态显示为"数据获取中"

## 监控指标

### 1. 接口性能
- 响应时间：通常 < 2秒
- 成功率：> 95%

### 2. 外部依赖
- DWS平台可用性
- 网络连接状态

### 3. 数据准确性
- 设备总数与实际设备数量一致
- 维保任务数量与系统记录一致

## 注意事项

1. **权限要求**: 需要用户认证，确保有设备查看权限
2. **网络依赖**: 安灯状态依赖外部平台，可能存在网络延迟
3. **数据实时性**: 维保任务数据实时性较高，安灯状态可能有延迟
4. **异常处理**: 外部平台异常时不影响接口正常返回
5. **配置依赖**: 需要正确配置工厂代码参数

## 相关接口

- **异常类型列表**: `/roke/get/abnormal_type_list`
- **设备列表**: `/roke/get/equipment_list`
- **安灯配置**: `/roke/get/stack_light_config`

## 版本历史

- **v1.0.0**: 初始版本，支持基本的安灯状态和维保任务统计
