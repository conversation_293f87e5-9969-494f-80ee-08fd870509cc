{"openapi": "3.0.0", "info": {"title": "安灯状态查询接口", "description": "获取安灯状态和设备维保信息接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/get/stack_light/state": {"post": {"summary": "获取安灯状态和设备维保信息", "description": "获取工厂所有设备的安灯状态统计信息，以及当前的点检、维修、保养任务数量", "tags": ["安灯管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {}, "example": {}}, "examples": {"empty": {"summary": "空请求体", "value": {}}}}}}, "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StackLightStateResponse"}, "examples": {"success": {"summary": "获取成功", "value": {"state": "success", "msgs": "获取成功", "data": {"total": 50, "green": 35, "yellow": 8, "red": 5, "gray": 2, "check_count": 3, "repair_count": 2, "maintain_count": 1}}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"StackLightStateResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success"], "description": "响应状态"}, "msgs": {"type": "string", "description": "响应消息"}, "data": {"type": "object", "description": "安灯状态和维保信息", "properties": {"total": {"type": "integer", "description": "设备总数量", "example": 50}, "green": {"type": "integer", "description": "绿灯状态设备数量（正常运行）", "example": 35}, "yellow": {"type": "integer", "description": "黄灯状态设备数量（警告状态）", "example": 8}, "red": {"type": "integer", "description": "红灯状态设备数量（故障/停机）", "example": 5}, "gray": {"type": "integer", "description": "灰灯状态设备数量（离线/未知）", "example": 2}, "check_count": {"type": "integer", "description": "进行中的点检任务数量", "example": 3}, "repair_count": {"type": "integer", "description": "待处理的维修任务数量", "example": 2}, "maintain_count": {"type": "integer", "description": "待处理的保养任务数量", "example": 1}}}}}}}}