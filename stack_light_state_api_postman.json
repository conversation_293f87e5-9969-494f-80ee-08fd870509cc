{"info": {"name": "安灯状态查询接口", "description": "获取安灯状态和设备维保信息接口文档", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "获取安灯状态和设备维保信息", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/get/stack_light/state", "host": ["{{baseUrl}}"], "path": ["roke", "get", "stack_light", "state"]}, "description": "获取工厂所有设备的安灯状态统计信息，以及当前的点检、维修、保养任务数量\n\n## 接口说明\n\n该接口用于获取工厂设备的安灯状态概览，包括各种状态的设备数量统计和维保任务统计。主要用于生产管理看板和设备监控大屏。\n\n## 请求参数\n\n无需传入参数，发送空的JSON对象即可。\n\n## 业务逻辑\n\n### 安灯状态统计\n接口会统计所有设备的安灯状态：\n- **绿灯（green）**: 设备正常运行状态\n- **黄灯（yellow）**: 设备警告状态，可能需要注意\n- **红灯（red）**: 设备故障或停机状态，需要立即处理\n- **灰灯（gray）**: 设备离线或状态未知\n\n### 维保任务统计\n同时统计当前的维保任务情况：\n- **点检任务（check_count）**: 状态为\"进行中\"的点检记录数量\n- **维修任务（repair_count）**: 状态为\"已分配\"的维修任务数量\n- **保养任务（maintain_count）**: 状态为\"已分配\"的保养任务数量\n\n### 数据来源\n1. **设备总数**: 从系统中所有有编号的设备统计\n2. **安灯状态**: 通过调用外部DWS平台API获取实时状态\n3. **维保任务**: 从系统内部的点检记录和维保任务表查询\n\n## 外部依赖\n\n接口依赖外部DWS平台获取设备状态：\n- **平台地址**: https://dws-platform.xbg.rokeris.com\n- **API路径**: /dev-api/public/device/count/{factory_code}\n- **工厂代码**: 从系统参数 `database.uuid` 获取\n\n## 应用场景\n\n1. **生产管理看板**: 实时显示设备运行状态概览\n2. **设备监控大屏**: 展示工厂设备健康度\n3. **维保管理**: 显示待处理的维保任务数量\n4. **移动端应用**: 快速了解工厂设备状态\n\n## 注意事项\n\n1. 接口需要用户认证，确保有相应的查看权限\n2. 安灯状态数据来自外部平台，可能存在网络延迟\n3. 如果外部平台不可用，安灯状态统计可能为0\n4. 维保任务统计基于当前数据库状态，实时性较高\n5. 设备总数只统计有编号的设备"}, "response": [{"name": "获取成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/get/stack_light/state", "host": ["{{baseUrl}}"], "path": ["roke", "get", "stack_light", "state"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": {\n    \"total\": 50,\n    \"green\": 35,\n    \"yellow\": 8,\n    \"red\": 5,\n    \"gray\": 2,\n    \"check_count\": 3,\n    \"repair_count\": 2,\n    \"maintain_count\": 1\n  }\n}"}, {"name": "外部平台异常时的响应", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/get/stack_light/state", "host": ["{{baseUrl}}"], "path": ["roke", "get", "stack_light", "state"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msgs\": \"获取成功\",\n  \"data\": {\n    \"total\": 50,\n    \"green\": 0,\n    \"yellow\": 0,\n    \"red\": 0,\n    \"gray\": 0,\n    \"check_count\": 3,\n    \"repair_count\": 2,\n    \"maintain_count\": 1\n  }\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8069", "type": "string", "description": "API服务器地址"}, {"key": "token", "value": "", "type": "string", "description": "用户认证token"}]}