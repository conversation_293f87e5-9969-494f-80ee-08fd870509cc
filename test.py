import time

nums = list(range(1_000_000))

# filter + lambda
start = time.time()
res1 = list(filter(lambda x: x % 2 == 0, nums))
print("filter:", time.time() - start)

# 列表推导式
start = time.time()
res2 = [x for x in nums if x % 2 == 0]
print("列表推导式:", time.time() - start)

# for 循环
start = time.time()
res3 = []
for x in nums:
    if x % 2 == 0:
        res3.append(x)
print("for循环:", time.time() - start)
