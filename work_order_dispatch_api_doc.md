# 工单派工接口文档

## 接口概述

**接口路径**: `/roke/work_order_dispatch`  
**请求方法**: `POST`  
**认证方式**: Bearer Token  
**内容类型**: `application/json`

该接口用于对生产工单执行派工操作，将工单从"未派工"状态变更为"未开工"状态，并记录派工时间。同时可以分配班组、人员和工作中心等生产资源。

## 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | application/json |
| Authorization | string | 是 | Bearer {token} |

### Body Parameters
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| work_order_id | integer | 是 | 工单ID | 123 |
| team_id | integer | 否 | 班组ID | 5 |
| employee_ids | array | 否 | 人员ID列表 | [10, 11, 12] |
| work_center_id | integer | 否 | 工作中心ID | 3 |

### 请求示例

#### 基本派工
```json
{
  "work_order_id": 123
}
```

#### 派工并分配资源
```json
{
  "work_order_id": 123,
  "team_id": 5,
  "employee_ids": [10, 11, 12],
  "work_center_id": 3
}
```

## 响应格式

### 成功响应
```json
{
  "state": "success",
  "msg": "工单派工成功",
  "data": {
    "id": 123,
    "code": "WO202401001",
    "state": "未开工",
    "dispatch_time": "2024-01-15 14:30:00",
    "team_name": "生产一班",
    "work_center_name": "加工中心01",
    "employee_names": ["张三", "李四"]
  }
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "错误描述信息"
}
```

## 业务逻辑详解

### 1. 派工条件检查

系统会按顺序检查以下条件：

#### 1.1 工单存在性检查
- 验证 `work_order_id` 是否存在
- 如果不存在，返回错误："必须选择工单。"

#### 1.2 工单状态检查
- 工单状态必须为 "未派工"
- 如果状态不符合，返回错误："当前工单状态：{状态}，不可进行派工操作。"

#### 1.3 资源验证（可选）
- **班组验证**：如果传入 `team_id`，验证班组是否存在
- **人员验证**：如果传入 `employee_ids`，验证所有人员是否存在
- **工作中心验证**：如果传入 `work_center_id`，验证工作中心是否存在

### 2. 派工操作

通过所有检查后，系统会执行以下操作：

1. **调用 action_save 方法**
   - 设置工单状态为 "未开工"
   - 设置派工时间为当前时间
   - 如果生产任务未设置开始日期，设置为今天

2. **分配生产资源**（如果提供）
   - 分配班组 (`team_id`)
   - 分配人员 (`employee_ids`)
   - 分配工作中心 (`work_center_id`)

## 工单状态流转

```
未派工 → 未开工 → 进行中 → 已完工
  ↑        ↑        ↑        ↑
创建工单  派工接口  开工接口  完工接口
```

### 状态说明

1. **未派工**: 工单创建后的初始状态，等待派工
2. **未开工**: 派工后的状态，等待开工
3. **进行中**: 开工后或有报工记录后的状态
4. **已完工**: 调用完工接口后的最终状态

## 生产资源管理

### 班组分配
- **作用**: 指定负责该工单的班组
- **影响**: 影响报工记录的班组归属
- **可选**: 可以在派工时分配，也可以后续分配

### 人员分配
- **作用**: 指定具体的操作人员
- **影响**: 影响报工记录的人员归属
- **可选**: 支持多人分配，可以动态调整

### 工作中心分配
- **作用**: 指定使用的工作中心/设备
- **影响**: 影响产能计算和设备利用率统计
- **可选**: 可以根据工艺要求分配

## 错误码说明

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 必须选择工单。 | 未传入work_order_id参数 | 检查请求参数 |
| 当前工单状态：{状态}，不可进行派工操作。 | 工单状态不是"未派工" | 检查工单当前状态 |
| 选择的班组不存在。 | 传入的team_id无效 | 检查班组ID是否正确 |
| 部分选择的人员不存在。 | 传入的employee_ids中有无效ID | 检查人员ID列表 |
| 选择的工作中心不存在。 | 传入的work_center_id无效 | 检查工作中心ID是否正确 |

## 使用场景

### 1. 简单派工（无资源分配）
```json
{
  "work_order_id": 123
}
```
适用于：快速派工，后续再分配资源

### 2. 完整派工（分配所有资源）
```json
{
  "work_order_id": 123,
  "team_id": 5,
  "employee_ids": [10, 11, 12],
  "work_center_id": 3
}
```
适用于：计划性生产，一次性分配所有资源

### 3. 部分资源派工
```json
{
  "work_order_id": 123,
  "team_id": 5
}
```
适用于：先分配班组，人员和设备后续安排

## 注意事项

1. **状态前置条件**: 只有"未派工"状态的工单才能进行派工
2. **任务关联**: 派工会自动设置关联生产任务的开始日期
3. **资源可选**: 所有资源分配都是可选的，可以灵活组合
4. **后续操作**: 派工后需要调用开工接口才能开始生产
5. **时间记录**: 派工时间会自动记录，用于生产周期统计

## 相关接口

- **工单开工**: `/roke/work_order_start`
- **工单完工**: `/roke/work_order_finish`
- **工单报工**: `/roke/work_record_create`
- **工单查询**: `/roke/work_order_list`

## 业务流程示例

### 标准派工流程
1. 创建生产工单（状态：未派工）
2. 调用派工接口（状态：未开工）
3. 分配生产资源（班组、人员、工作中心）
4. 调用开工接口（状态：进行中）
5. 进行生产报工（状态：进行中）
6. 调用完工接口（状态：已完工）

### 灵活派工流程
1. 创建生产工单（状态：未派工）
2. 基本派工，不分配资源（状态：未开工）
3. 根据实际情况分配班组
4. 根据人员安排分配操作人员
5. 根据设备情况分配工作中心
6. 开工生产

## 版本历史

- **v1.0.0**: 初始版本，支持基本派工和资源分配功能
