{"openapi": "3.0.0", "info": {"title": "工单派工接口", "description": "生产工单派工操作接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/work_order_dispatch": {"post": {"summary": "工单派工", "description": "对指定的生产工单执行派工操作，将工单状态从\"未派工\"变更为\"未开工\"，并可分配班组、人员和工作中心", "tags": ["生产管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderDispatchRequest"}, "examples": {"basic": {"summary": "基本派工", "value": {"work_order_id": 123}}, "withAssignment": {"summary": "派工并分配资源", "value": {"work_order_id": 123, "team_id": 5, "employee_ids": [10, 11, 12], "work_center_id": 3}}}}}}, "responses": {"200": {"description": "操作结果", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderDispatchResponse"}, "examples": {"success": {"summary": "派工成功", "value": {"state": "success", "msg": "工单派工成功", "data": {"id": 123, "code": "WO202401001", "state": "未开工", "dispatch_time": "2024-01-15 14:30:00", "team_name": "生产一班", "work_center_name": "加工中心01", "employee_names": ["张三", "李四"]}}}, "errorNoWorkOrder": {"summary": "未选择工单", "value": {"state": "error", "msgs": "必须选择工单。"}}, "errorWrongState": {"summary": "工单状态错误", "value": {"state": "error", "msgs": "当前工单状态：未开工，不可进行派工操作。"}}, "errorTeamNotFound": {"summary": "班组不存在", "value": {"state": "error", "msgs": "选择的班组不存在。"}}, "errorEmployeeNotFound": {"summary": "人员不存在", "value": {"state": "error", "msgs": "部分选择的人员不存在。"}}, "errorWorkCenterNotFound": {"summary": "工作中心不存在", "value": {"state": "error", "msgs": "选择的工作中心不存在。"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"WorkOrderDispatchRequest": {"type": "object", "required": ["work_order_id"], "properties": {"work_order_id": {"type": "integer", "description": "工单ID，必填", "example": 123}, "team_id": {"type": "integer", "description": "班组ID，可选", "example": 5}, "employee_ids": {"type": "array", "items": {"type": "integer"}, "description": "人员ID列表，可选", "example": [10, 11, 12]}, "work_center_id": {"type": "integer", "description": "工作中心ID，可选", "example": 3}}}, "WorkOrderDispatchResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success", "error"], "description": "操作状态"}, "msg": {"type": "string", "description": "成功消息，仅在state为success时存在"}, "msgs": {"type": "string", "description": "错误消息，仅在state为error时存在"}, "data": {"type": "object", "description": "派工成功后返回的工单信息", "properties": {"id": {"type": "integer", "description": "工单ID"}, "code": {"type": "string", "description": "工单编号"}, "state": {"type": "string", "description": "工单状态"}, "dispatch_time": {"type": "string", "format": "date-time", "description": "派工时间"}, "team_name": {"type": "string", "description": "班组名称"}, "work_center_name": {"type": "string", "description": "工作中心名称"}, "employee_names": {"type": "array", "items": {"type": "string"}, "description": "人员名称列表"}}}}}}}}