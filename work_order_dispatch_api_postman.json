{"info": {"name": "工单派工接口", "description": "生产工单派工操作接口文档", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "工单派工", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123,\n  \"team_id\": 5,\n  \"employee_ids\": [10, 11, 12],\n  \"work_center_id\": 3\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}, "description": "对指定的生产工单执行派工操作\n\n## 接口说明\n\n该接口用于对生产工单执行派工操作，将工单状态从\"未派工\"变更为\"未开工\"，并记录派工时间。同时可以分配班组、人员和工作中心等生产资源。\n\n## 请求参数\n\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|---------|\n| work_order_id | integer | 是 | 工单ID |\n| team_id | integer | 否 | 班组ID |\n| employee_ids | array | 否 | 人员ID列表 |\n| work_center_id | integer | 否 | 工作中心ID |\n\n## 业务逻辑\n\n### 派工条件检查\n1. **工单状态检查**：工单状态必须为\"未派工\"\n2. **资源验证**：验证班组、人员、工作中心是否存在\n\n### 派工操作\n成功派工后会更新以下字段：\n- `state`: 工单状态（\"未开工\"）\n- `dispatch_time`: 派工时间（当前时间）\n- `task_id.start_date`: 生产任务开始日期（如果未设置）\n- 可选分配：班组、人员、工作中心\n\n### 资源分配逻辑\n- **班组分配**：指定负责该工单的班组\n- **人员分配**：指定具体的操作人员\n- **工作中心分配**：指定使用的工作中心/设备\n- 所有资源分配都是可选的，可以在派工后再分配\n\n## 派工流程\n\n1. **创建工单**（状态：未派工）\n2. **调用派工接口**（状态：未开工）\n3. **分配生产资源**（班组、人员、工作中心）\n4. **工单开工**（状态：进行中）\n5. **生产报工**（状态：进行中）\n6. **工单完工**（状态：已完工）\n\n## 注意事项\n\n1. 只有\"未派工\"状态的工单才能进行派工操作\n2. 派工操作会自动设置生产任务的开始日期\n3. 资源分配是可选的，可以在派工时分配，也可以后续分配\n4. 派工后工单进入\"未开工\"状态，需要调用开工接口才能开始生产\n5. 派工时间会自动记录为当前时间"}, "response": [{"name": "派工成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123,\n  \"team_id\": 5,\n  \"employee_ids\": [10, 11],\n  \"work_center_id\": 3\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msg\": \"工单派工成功\",\n  \"data\": {\n    \"id\": 123,\n    \"code\": \"WO202401001\",\n    \"state\": \"未开工\",\n    \"dispatch_time\": \"2024-01-15 14:30:00\",\n    \"team_name\": \"生产一班\",\n    \"work_center_name\": \"加工中心01\",\n    \"employee_names\": [\"张三\", \"李四\"]\n  }\n}"}, {"name": "基本派工（无资源分配）", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msg\": \"工单派工成功\",\n  \"data\": {\n    \"id\": 123,\n    \"code\": \"WO202401001\",\n    \"state\": \"未开工\",\n    \"dispatch_time\": \"2024-01-15 14:30:00\",\n    \"team_name\": \"\",\n    \"work_center_name\": \"\",\n    \"employee_names\": []\n  }\n}"}, {"name": "未选择工单", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"必须选择工单。\"\n}"}, {"name": "工单状态错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单状态：未开工，不可进行派工操作。\"\n}"}, {"name": "班组不存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123,\n  \"team_id\": 999\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_dispatch", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_dispatch"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"选择的班组不存在。\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8069", "type": "string", "description": "API服务器地址"}, {"key": "token", "value": "", "type": "string", "description": "用户认证token"}]}