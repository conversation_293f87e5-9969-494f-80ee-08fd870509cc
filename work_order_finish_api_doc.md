# 工单完工接口文档

## 接口概述

**接口路径**: `/roke/work_order_finish`  
**请求方法**: `POST`  
**认证方式**: Bearer Token  
**内容类型**: `application/json`

该接口用于对生产工单执行完工操作，将工单从"未完工"状态变更为"已完工"状态，并记录完工时间。

## 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | application/json |
| Authorization | string | 是 | Bearer {token} |

### Body Parameters
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| work_order_id | integer | 是 | 工单ID | 123 |

### 请求示例

```json
{
  "work_order_id": 123
}
```

## 响应格式

### 成功响应
```json
{
  "state": "success",
  "msg": "工单完工成功"
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "错误描述信息"
}
```

## 业务逻辑详解

### 1. 完工条件检查

系统会按顺序检查以下条件：

#### 1.1 工单存在性检查
- 验证 `work_order_id` 是否存在
- 如果不存在，返回错误："必须选择工单。"

#### 1.2 工单状态检查
- 工单状态必须为 "未完工"
- 如果状态不符合，返回错误："当前工单状态：{状态}，不可进行完工操作。"

#### 1.3 手工完工配置检查
- 工单必须配置为需要手工完工 (`wo_manual_finish = True`)
- 如果不需要手工完工，返回错误："当前工单不需要手工完工。"

#### 1.4 开工状态检查
- 工单开工状态必须为 "已开工"
- 如果未开工，返回错误："当前工单开工状态：{状态}，不可进行完工操作。"

### 2. 完工操作

通过所有检查后，系统会执行以下操作：

1. **更新工单字段**
   - `wo_finish_time`: 设置为当前时间
   - `state`: 设置为 "已完工"

2. **更新关联任务**
   - 调用 `work_order.work_order_finish()` 方法
   - 更新关联的生产任务完工状态

## 完工模式对比

### 自动完工模式 (`wo_manual_finish = False`)

**特点**:
- 当报工数量达到计划数量时自动完工
- 不需要调用此接口
- 完工时间为最后一次报工时间

**适用场景**:
- 标准化生产流程
- 按数量驱动的生产
- 减少人工干预

### 手工完工模式 (`wo_manual_finish = True`)

**特点**:
- 即使报工数量达到计划数量也不会自动完工
- 必须调用此接口手动完工
- 完工时间为调用接口的时间

**适用场景**:
- 需要质检确认的工序
- 复杂工艺流程
- 需要人工验收的生产

## 工单状态流转

```
未开工 → 进行中 → 已完工
  ↑        ↑        ↑
开工接口  报工操作  完工接口
```

### 状态说明

1. **未开工**: 工单创建后的初始状态
2. **进行中**: 开工后或有报工记录后的状态
3. **已完工**: 调用完工接口后的最终状态

## 错误码说明

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 必须选择工单。 | 未传入work_order_id参数 | 检查请求参数 |
| 当前工单状态：{状态}，不可进行完工操作。 | 工单状态不是"未完工" | 检查工单当前状态 |
| 当前工单不需要手工完工。 | 工单配置为自动完工 | 检查工单完工配置 |
| 当前工单开工状态：{状态}，不可进行完工操作。 | 工单未开工 | 先调用开工接口 |

## 使用场景

### 1. 质检确认后完工
```json
{
  "work_order_id": 123
}
```

### 2. 批量生产完工
```json
{
  "work_order_id": 456
}
```

### 3. 特殊工艺完工
```json
{
  "work_order_id": 789
}
```

## 注意事项

1. **前置条件**: 工单必须已经开工才能完工
2. **操作不可逆**: 工单完工后无法直接撤销，需要使用专门的取消完工接口
3. **权限要求**: 需要用户认证，确保操作人员有相应权限
4. **状态依赖**: 完工后工单不能再进行报工操作
5. **任务影响**: 完工会影响关联的生产任务和生产订单状态

## 相关接口

- **工单开工**: `/roke/work_order_start`
- **取消完工**: `/roke/cancel_work_order_finish`
- **工单报工**: `/roke/work_record_create`
- **工单查询**: `/roke/work_order_list`

## 业务流程示例

### 标准完工流程
1. 创建工单（状态：未开工）
2. 调用开工接口（状态：进行中）
3. 进行生产报工（状态：进行中）
4. 调用完工接口（状态：已完工）

### 手工完工流程
1. 工单配置 `wo_manual_finish = True`
2. 即使报工数量达到计划数量，状态仍为"进行中"
3. 质检或验收通过后，调用完工接口
4. 工单状态变更为"已完工"

## 版本历史

- **v1.0.0**: 初始版本，支持基本完工功能
- **v1.0.1**: 修复了接口实现中的bug（调用错误的方法）
