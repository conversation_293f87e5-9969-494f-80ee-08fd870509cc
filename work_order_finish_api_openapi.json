{"openapi": "3.0.0", "info": {"title": "工单完工接口", "description": "生产工单完工操作接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/work_order_finish": {"post": {"summary": "工单完工", "description": "对指定的生产工单执行完工操作，将工单状态从\"未完工\"变更为\"已完工\"", "tags": ["生产管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderFinishRequest"}, "examples": {"basic": {"summary": "工单完工", "value": {"work_order_id": 123}}}}}}, "responses": {"200": {"description": "操作结果", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderFinishResponse"}, "examples": {"success": {"summary": "完工成功", "value": {"state": "success", "msg": "工单完工成功"}}, "errorNoWorkOrder": {"summary": "未选择工单", "value": {"state": "error", "msgs": "必须选择工单。"}}, "errorWrongState": {"summary": "工单状态错误", "value": {"state": "error", "msgs": "当前工单状态：已完工，不可进行完工操作。"}}, "errorNoManualFinish": {"summary": "不需要手工完工", "value": {"state": "error", "msgs": "当前工单不需要手工完工。"}}, "errorNotStarted": {"summary": "未开工", "value": {"state": "error", "msgs": "当前工单开工状态：未开工，不可进行完工操作。"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"WorkOrderFinishRequest": {"type": "object", "required": ["work_order_id"], "properties": {"work_order_id": {"type": "integer", "description": "工单ID，必填", "example": 123}}}, "WorkOrderFinishResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success", "error"], "description": "操作状态"}, "msg": {"type": "string", "description": "成功消息，仅在state为success时存在"}, "msgs": {"type": "string", "description": "错误消息，仅在state为error时存在"}}}}}}