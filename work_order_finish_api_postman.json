{"info": {"name": "工单完工接口", "description": "生产工单完工操作接口文档", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "工单完工", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}, "description": "对指定的生产工单执行完工操作\n\n## 接口说明\n\n该接口用于对生产工单执行完工操作，将工单状态从\"未完工\"变更为\"已完工\"，并记录完工时间。\n\n## 请求参数\n\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|---------|\n| work_order_id | integer | 是 | 工单ID |\n\n## 业务逻辑\n\n### 完工条件检查\n1. **工单状态检查**：工单状态必须为\"未完工\"\n2. **手工完工检查**：工单必须配置为需要手工完工\n3. **开工状态检查**：工单必须已经开工（开工状态为\"已开工\"）\n\n### 完工操作\n成功完工后会更新以下字段：\n- `wo_finish_time`: 完工时间（当前时间）\n- `state`: 工单状态（\"已完工\"）\n- 同时会更新关联的生产任务完工状态\n\n## 系统配置\n\n接口行为受工单配置影响：\n- `wo_manual_finish`: 是否需要手工完工\n  - `True`: 需要手工完工（需要调用此接口）\n  - `False`: 自动完工（根据报工数量自动完工）\n\n## 自动完工 vs 手工完工\n\n### 自动完工模式\n- 当报工数量达到计划数量时自动完工\n- 不需要调用此接口\n- 完工时间为最后一次报工时间\n\n### 手工完工模式\n- 即使报工数量达到计划数量也不会自动完工\n- 必须调用此接口手动完工\n- 完工时间为调用接口的时间\n\n## 注意事项\n\n1. 只有配置为手工完工的工单才能使用此接口\n2. 工单必须处于\"未完工\"状态且已经开工\n3. 完工操作不可逆，请确认后再执行\n4. 完工后工单不能再进行报工操作\n5. 完工会影响生产任务和生产订单的状态"}, "response": [{"name": "完工成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msg\": \"工单完工成功\"\n}"}, {"name": "未选择工单", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"必须选择工单。\"\n}"}, {"name": "工单状态错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单状态：已完工，不可进行完工操作。\"\n}"}, {"name": "不需要手工完工", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单不需要手工完工。\"\n}"}, {"name": "未开工", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_finish", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_finish"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单开工状态：未开工，不可进行完工操作。\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8069", "type": "string", "description": "API服务器地址"}, {"key": "token", "value": "", "type": "string", "description": "用户认证token"}]}