# 工单开工接口文档

## 接口概述

**接口路径**: `/roke/work_order_start`  
**请求方法**: `POST`  
**认证方式**: Bearer Token  
**内容类型**: `application/json`

该接口用于对生产工单执行开工操作，将工单从"未开工"状态变更为"已开工"状态，并记录开工时间。

## 请求参数

### Headers
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Content-Type | string | 是 | application/json |
| Authorization | string | 是 | Bearer {token} |

### Body Parameters
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| work_order_id | integer | 是 | 工单ID | 123 |
| work_center_ids | array | 否 | 工作中心ID列表 | [1, 2, 3] |

### 请求示例

#### 基本开工
```json
{
  "work_order_id": 123
}
```

#### 开工并分配工作中心
```json
{
  "work_order_id": 123,
  "work_center_ids": [1, 2, 3]
}
```

## 响应格式

### 成功响应
```json
{
  "state": "success",
  "msg": "工单开工成功"
}
```

### 错误响应
```json
{
  "state": "error",
  "msgs": "错误描述信息"
}
```

## 业务逻辑详解

### 1. 工作中心分配逻辑

当传入 `work_center_ids` 参数时，系统会执行以下操作：

1. **移除其他工单的工作中心分配**
   - 查找所有分配了指定工作中心的其他工单
   - 从这些工单中移除指定的工作中心分配

2. **分配工作中心给当前工单**
   - 将指定的工作中心分配给当前工单
   - 与工单现有的工作中心合并（不会覆盖）

### 2. 开工条件检查

系统会按顺序检查以下条件：

#### 2.1 工单存在性检查
- 验证 `work_order_id` 是否存在
- 如果不存在，返回错误："必须选择工单。"

#### 2.2 工单状态检查
- 工单状态必须为 "未完工"
- 如果状态不符合，返回错误："当前工单状态：{状态}，不可进行开工操作。"

#### 2.3 手工开工配置检查
- 工单必须配置为需要手工开工 (`wo_manual_start = True`)
- 如果不需要手工开工，返回错误："当前工单不需要手工开工。"

#### 2.4 开工状态检查
- 工单开工状态必须为 "未开工"
- 如果已经开工，返回错误："当前工单开工状态：{状态}，不可进行开工操作。"

### 3. 开工操作

通过所有检查后，系统会执行以下操作：

1. **更新工单字段**
   - `wo_start_time`: 设置为当前时间
   - `wo_start_state`: 设置为 "已开工"
   - `state`: 设置为 "进行中"

2. **调用工单开工方法**
   - 执行 `work_order.work_order_start()` 方法
   - 该方法会处理所有需要手工开工的工单

## 系统配置

### 手工开工配置

接口行为受系统参数 `work.order.manual.start` 影响：

- **值为 "0"**: 自动开工模式
  - 工单创建时自动设置为"已开工"状态
  - 不需要调用此接口
  
- **值为 "1"**: 手工开工模式
  - 工单创建时设置为"未开工"状态
  - 需要调用此接口手动开工

### 完工配置

工单完工行为受公司配置 `complete_basis` 影响：

- **"手动完工"**: 需要手动调用完工接口
- **其他值**: 根据报工数量自动完工

## 错误码说明

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 必须选择工单。 | 未传入work_order_id参数 | 检查请求参数 |
| 当前工单状态：{状态}，不可进行开工操作。 | 工单状态不是"未完工" | 检查工单当前状态 |
| 当前工单不需要手工开工。 | 系统配置为自动开工 | 检查系统配置或工单配置 |
| 当前工单开工状态：{状态}，不可进行开工操作。 | 工单已经开工 | 检查工单开工状态 |

## 使用场景

### 1. 生产车间开工
```json
{
  "work_order_id": 123
}
```

### 2. 指定工作中心开工
```json
{
  "work_order_id": 123,
  "work_center_ids": [5, 6]
}
```

### 3. 批量工作中心调整
```json
{
  "work_order_id": 123,
  "work_center_ids": [1, 2, 3, 4, 5]
}
```

## 注意事项

1. **操作不可逆**: 工单开工后无法直接撤销，需要使用专门的取消开工接口
2. **权限要求**: 需要用户认证，确保操作人员有相应权限
3. **并发控制**: 多人同时操作同一工单时，以最后一次操作为准
4. **工作中心冲突**: 工作中心分配会影响其他工单，请谨慎操作
5. **状态依赖**: 后续的报工、完工等操作都依赖于开工状态

## 相关接口

- **工单完工**: `/roke/work_order_finish`
- **取消开工**: `/roke/cancel_work_order_start`
- **工单报工**: `/roke/work_record_create`
- **工单查询**: `/roke/work_order_list`

## 版本历史

- **v1.0.0**: 初始版本，支持基本开工和工作中心分配功能
