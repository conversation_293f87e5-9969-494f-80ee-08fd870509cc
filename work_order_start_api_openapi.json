{"openapi": "3.0.0", "info": {"title": "工单开工接口", "description": "生产工单开工操作接口", "version": "1.0.0"}, "servers": [{"url": "{baseUrl}", "description": "API服务器", "variables": {"baseUrl": {"default": "http://localhost:8069", "description": "服务器地址"}}}], "paths": {"/roke/work_order_start": {"post": {"summary": "工单开工", "description": "对指定的生产工单执行开工操作，并可选择性地分配工作中心", "tags": ["生产管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderStartRequest"}, "examples": {"basic": {"summary": "基本开工", "value": {"work_order_id": 123}}, "withWorkCenters": {"summary": "开工并分配工作中心", "value": {"work_order_id": 123, "work_center_ids": [1, 2, 3]}}}}}}, "responses": {"200": {"description": "操作结果", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkOrderStartResponse"}, "examples": {"success": {"summary": "开工成功", "value": {"state": "success", "msg": "工单开工成功"}}, "errorNoWorkOrder": {"summary": "未选择工单", "value": {"state": "error", "msgs": "必须选择工单。"}}, "errorWrongState": {"summary": "工单状态错误", "value": {"state": "error", "msgs": "当前工单状态：已完工，不可进行开工操作。"}}, "errorNoManualStart": {"summary": "不需要手工开工", "value": {"state": "error", "msgs": "当前工单不需要手工开工。"}}, "errorAlreadyStarted": {"summary": "已经开工", "value": {"state": "error", "msgs": "当前工单开工状态：已开工，不可进行开工操作。"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "用户认证token"}}, "schemas": {"WorkOrderStartRequest": {"type": "object", "required": ["work_order_id"], "properties": {"work_order_id": {"type": "integer", "description": "工单ID，必填", "example": 123}, "work_center_ids": {"type": "array", "items": {"type": "integer"}, "description": "工作中心ID列表，可选。指定后会将这些工作中心分配给当前工单，并从其他工单中移除", "example": [1, 2, 3]}}}, "WorkOrderStartResponse": {"type": "object", "properties": {"state": {"type": "string", "enum": ["success", "error"], "description": "操作状态"}, "msg": {"type": "string", "description": "成功消息，仅在state为success时存在"}, "msgs": {"type": "string", "description": "错误消息，仅在state为error时存在"}}}}}}