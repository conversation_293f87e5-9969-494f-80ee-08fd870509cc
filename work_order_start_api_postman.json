{"info": {"name": "工单开工接口", "description": "生产工单开工操作接口文档", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "工单开工", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text", "description": "用户认证token"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123,\n  \"work_center_ids\": [1, 2, 3]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}, "description": "对指定的生产工单执行开工操作\n\n## 接口说明\n\n该接口用于对生产工单执行开工操作，将工单状态从\"未开工\"变更为\"已开工\"，并记录开工时间。\n\n## 请求参数\n\n| 参数名 | 类型 | 必填 | 说明 |\n|--------|------|------|---------|\n| work_order_id | integer | 是 | 工单ID |\n| work_center_ids | array | 否 | 工作中心ID列表 |\n\n## 业务逻辑\n\n### 工作中心分配逻辑\n如果传入了 `work_center_ids` 参数：\n1. 从其他工单中移除这些工作中心的分配\n2. 将这些工作中心分配给当前工单\n3. 与当前工单已有的工作中心合并\n\n### 开工条件检查\n1. **工单状态检查**：工单状态必须为\"未完工\"\n2. **手工开工检查**：工单必须配置为需要手工开工\n3. **开工状态检查**：工单开工状态必须为\"未开工\"\n\n### 开工操作\n成功开工后会更新以下字段：\n- `wo_start_time`: 开工时间（当前时间）\n- `wo_start_state`: 开工状态（\"已开工\"）\n- `state`: 工单状态（\"进行中\"）\n\n## 系统配置\n\n接口行为受以下系统参数影响：\n- `work.order.manual.start`: 是否启用手工开工模式\n  - \"0\": 自动开工（工单创建时自动开工）\n  - \"1\": 手工开工（需要调用此接口开工）\n\n## 注意事项\n\n1. 只有配置为手工开工的工单才能使用此接口\n2. 工单必须处于\"未完工\"状态且开工状态为\"未开工\"\n3. 工作中心分配是可选的，不影响开工操作本身\n4. 开工操作不可逆，请确认后再执行"}, "response": [{"name": "开工成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"success\",\n  \"msg\": \"工单开工成功\"\n}"}, {"name": "未选择工单", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"必须选择工单。\"\n}"}, {"name": "工单状态错误", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单状态：已完工，不可进行开工操作。\"\n}"}, {"name": "不需要手工开工", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单不需要手工开工。\"\n}"}, {"name": "已经开工", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"work_order_id\": 123\n}"}, "url": {"raw": "{{baseUrl}}/roke/work_order_start", "host": ["{{baseUrl}}"], "path": ["roke", "work_order_start"]}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"state\": \"error\",\n  \"msgs\": \"当前工单开工状态：已开工，不可进行开工操作。\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8069", "type": "string", "description": "API服务器地址"}, {"key": "token", "value": "", "type": "string", "description": "用户认证token"}]}